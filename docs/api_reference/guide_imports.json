{"ChatPromptTemplate": {"🦜️🏓 LangServe": "https://python.langchain.com/docs/langserve/", "Conceptual guide": "https://python.langchain.com/docs/concepts/", "# Example": "https://python.langchain.com/docs/versions/migrating_chains/map_rerank_docs_chain/", "# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_router_chain/", "Load docs": "https://python.langchain.com/docs/versions/migrating_chains/conversation_retrieval_chain/", "# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/", "How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to handle long text when doing extraction": "https://python.langchain.com/docs/how_to/extraction_long_text/", "How to add values to a chain's state": "https://python.langchain.com/docs/how_to/assign/", "How to do per-user retrieval": "https://python.langchain.com/docs/how_to/qa_per_user/", "How to track token usage in ChatModels": "https://python.langchain.com/docs/how_to/chat_token_usage_tracking/", "How to create a custom LLM class": "https://python.langchain.com/docs/how_to/custom_llm/", "How to inspect runnables": "https://python.langchain.com/docs/how_to/inspect/", "How to handle cases where no queries are generated": "https://python.langchain.com/docs/how_to/query_no_queries/", "How to use few shot examples in chat models": "https://python.langchain.com/docs/how_to/few_shot_examples_chat/", "How to summarize text through iterative refinement": "https://python.langchain.com/docs/how_to/summarize_refine/", "How to do tool/function calling": "https://python.langchain.com/docs/how_to/function_calling/", "How to create tools": "https://python.langchain.com/docs/how_to/custom_tools/", "How to use prompting alone (no tool calling) to do extraction": "https://python.langchain.com/docs/how_to/extraction_parse/", "How to deal with large databases when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_large_db/", "How to use reference examples when doing extraction": "https://python.langchain.com/docs/how_to/extraction_examples/", "How to handle multiple queries when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_queries/", "How to add fallbacks to a runnable": "https://python.langchain.com/docs/how_to/fallbacks/", "How to propagate callbacks  constructor": "https://python.langchain.com/docs/how_to/callbacks_constructor/", "How to map values to a graph database": "https://python.langchain.com/docs/how_to/graph_mapping/", "How to save and load LangChain objects": "https://python.langchain.com/docs/how_to/serialization/", "How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/", "How to stream results from your RAG application": "https://python.langchain.com/docs/how_to/qa_streaming/", "How to get your RAG application to return sources": "https://python.langchain.com/docs/how_to/qa_sources/", "How to summarize text through parallelization": "https://python.langchain.com/docs/how_to/summarize_map_reduce/", "How to attach callbacks to a runnable": "https://python.langchain.com/docs/how_to/callbacks_attach/", "How to handle tool errors": "https://python.langchain.com/docs/how_to/tools_error/", "How to add tools to chatbots": "https://python.langchain.com/docs/how_to/chatbots_tools/", "How to add default invocation args to a Runnable": "https://python.langchain.com/docs/how_to/binding/", "How to convert Runnables as Tools": "https://python.langchain.com/docs/how_to/convert_runnable_to_tool/", "How to stream events from a tool": "https://python.langchain.com/docs/how_to/tool_stream_events/", "How to create a dynamic (self-constructing) chain": "https://python.langchain.com/docs/how_to/dynamic_chain/", "How to create custom callback handlers": "https://python.langchain.com/docs/how_to/custom_callbacks/", "How to stream runnables": "https://python.langchain.com/docs/how_to/streaming/", "How to invoke runnables in parallel": "https://python.langchain.com/docs/how_to/parallel/", "How to pass through arguments from one step to the next": "https://python.langchain.com/docs/how_to/passthrough/", "How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add message history": "https://python.langchain.com/docs/how_to/message_history/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to handle multiple retrievers when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_retrievers/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/", "How to run custom functions": "https://python.langchain.com/docs/how_to/functions/", "How to add memory to chatbots": "https://python.langchain.com/docs/how_to/chatbots_memory/", "How deal with high cardinality categoricals when doing query analysis": "https://python.langchain.com/docs/how_to/query_high_cardinality/", "How to return structured data from a model": "https://python.langchain.com/docs/how_to/structured_output/", "How to add ad-hoc tool calling capability to LLMs and Chat Models": "https://python.langchain.com/docs/how_to/tools_prompting/", "LangChain Expression Language Cheatsheet": "https://python.langchain.com/docs/how_to/lcel_cheatsheet/", "How to debug your LLM apps": "https://python.langchain.com/docs/how_to/debugging/", "How to chain runnables": "https://python.langchain.com/docs/how_to/sequence/", "Hybrid Search": "https://python.langchain.com/docs/how_to/hybrid/", "How to migrate from legacy LangChain agents to LangGraph": "https://python.langchain.com/docs/how_to/migrate_agent/", "How to do query validation as part of SQL question-answering": "https://python.langchain.com/docs/how_to/sql_query_checking/", "How to summarize text in a single LLM call": "https://python.langchain.com/docs/how_to/summarize_stuff/", "How to use multimodal prompts": "https://python.langchain.com/docs/how_to/multimodal_prompts/", "How to use few-shot prompting with tool calling": "https://python.langchain.com/docs/how_to/tools_few_shot/", "How to pass callbacks in at runtime": "https://python.langchain.com/docs/how_to/callbacks_runtime/", "How to add examples to the prompt for query analysis": "https://python.langchain.com/docs/how_to/query_few_shot/", "Facebook Messenger": "https://python.langchain.com/docs/integrations/chat_loaders/facebook/", "LangSmith LLM Runs": "https://python.langchain.com/docs/integrations/chat_loaders/langsmith_llm_runs/", "iMessage": "https://python.langchain.com/docs/integrations/chat_loaders/imessage/", "NVIDIA NIMs ": "https://python.langchain.com/docs/integrations/text_embedding/nvidia_ai_endpoints/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "You.com": "https://python.langchain.com/docs/integrations/retrievers/you-retriever/", "Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/", "AskNews": "https://python.langchain.com/docs/integrations/retrievers/asknews/", "WikipediaRetriever": "https://python.langchain.com/docs/integrations/retrievers/wikipedia/", "TavilySearchAPIRetriever": "https://python.langchain.com/docs/integrations/retrievers/tavily/", "Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "RAGatouille": "https://python.langchain.com/docs/integrations/retrievers/ragatouille/", "ArxivRetriever": "https://python.langchain.com/docs/integrations/retrievers/arxiv/", "ElasticsearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/elasticsearch_retriever/", "Google Vertex AI Search": "https://python.langchain.com/docs/integrations/retrievers/google_vertex_ai_search/", "Tavily Search": "https://python.langchain.com/docs/integrations/tools/tavily_search/", "FinancialDatasets Toolkit": "https://python.langchain.com/docs/integrations/tools/financial_datasets/", "Databricks Unity Catalog (UC)": "https://python.langchain.com/docs/integrations/tools/databricks/", "Riza Code Interpreter": "https://python.langchain.com/docs/integrations/tools/riza/", "Redis": "https://python.langchain.com/docs/integrations/memory/redis_chat_message_history/", "Google SQL for MySQL": "https://python.langchain.com/docs/integrations/memory/google_sql_mysql/", "Google AlloyDB for PostgreSQL": "https://python.langchain.com/docs/integrations/memory/google_alloydb/", "ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "AWS DynamoDB": "https://python.langchain.com/docs/integrations/memory/aws_dynamodb/", "Couchbase": "https://python.langchain.com/docs/integrations/memory/couchbase_chat_message_history/", "MongoDB": "https://python.langchain.com/docs/integrations/memory/mongodb_chat_message_history/", "SQL (SQLAlchemy)": "https://python.langchain.com/docs/integrations/memory/sql_chat_message_history/", "Streamlit": "https://python.langchain.com/docs/integrations/memory/streamlit_chat_message_history/", "Google El Carro Oracle": "https://python.langchain.com/docs/integrations/memory/google_el_carro/", "SQLite": "https://python.langchain.com/docs/integrations/memory/sqlite/", "Google SQL for PostgreSQL": "https://python.langchain.com/docs/integrations/memory/google_sql_pg/", "Google SQL for SQL Server": "https://python.langchain.com/docs/integrations/memory/google_sql_mssql/", "TiDB": "https://python.langchain.com/docs/integrations/memory/tidb_chat_message_history/", "Kinetica Language To SQL Chat Model": "https://python.langchain.com/docs/integrations/chat/kinetica/", "ChatFireworks": "https://python.langchain.com/docs/integrations/chat/fireworks/", "ChatYI": "https://python.langchain.com/docs/integrations/chat/yi/", "ChatAnthropic": "https://python.langchain.com/docs/integrations/chat/anthropic/", "ChatGroq": "https://python.langchain.com/docs/integrations/chat/groq/", "ChatGoogleGenerativeAI": "https://python.langchain.com/docs/integrations/chat/google_generative_ai/", "OllamaFunctions": "https://python.langchain.com/docs/integrations/chat/ollama_functions/", "ChatOpenAI": "https://python.langchain.com/docs/integrations/chat/openai/", "ChatVertexAI": "https://python.langchain.com/docs/integrations/chat/google_vertex_ai_palm/", "ChatBedrock": "https://python.langchain.com/docs/integrations/chat/bedrock/", "JinaChat": "https://python.langchain.com/docs/integrations/chat/jinachat/", "ChatOllama": "https://python.langchain.com/docs/integrations/chat/ollama/", "ChatOCIGenAI": "https://python.langchain.com/docs/integrations/chat/oci_generative_ai/", "AzureChatOpenAI": "https://python.langchain.com/docs/integrations/chat/azure_chat_openai/", "Llama.cpp": "https://python.langchain.com/docs/integrations/chat/llamacpp/", "ChatMistralAI": "https://python.langchain.com/docs/integrations/chat/mistralai/", "ChatAI21": "https://python.langchain.com/docs/integrations/chat/ai21/", "ChatDatabricks": "https://python.langchain.com/docs/integrations/chat/databricks/", "ChatTogether": "https://python.langchain.com/docs/integrations/chat/together/", "Llama2Chat": "https://python.langchain.com/docs/integrations/chat/llama2_chat/", "Cohere": "https://python.langchain.com/docs/integrations/providers/cohere/", "Eden AI": "https://python.langchain.com/docs/integrations/chat/edenai/", "ChatWatsonx": "https://python.langchain.com/docs/integrations/chat/ibm_watsonx/", "vLLM Chat": "https://python.langchain.com/docs/integrations/chat/vllm/", "Yuan2.0": "https://python.langchain.com/docs/integrations/chat/yuan2/", "Maritalk": "https://python.langchain.com/docs/integrations/chat/maritalk/", "ChatPerplexity": "https://python.langchain.com/docs/integrations/chat/perplexity/", "ChatUpstage": "https://python.langchain.com/docs/integrations/chat/upstage/", "ChatNVIDIA": "https://python.langchain.com/docs/integrations/chat/nvidia_ai_endpoints/", "Context": "https://python.langchain.com/docs/integrations/callbacks/context/", "Fiddler": "https://python.langchain.com/docs/integrations/callbacks/fiddler/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "MLflow": "https://python.langchain.com/docs/integrations/providers/mlflow_tracking/", "Weaviate": "https://python.langchain.com/docs/integrations/vectorstores/weaviate/", "Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/", "ApertureDB": "https://python.langchain.com/docs/integrations/vectorstores/aperturedb/", "Apache Cassandra": "https://python.langchain.com/docs/integrations/vectorstores/cassandra/", "OpenAI metadata tagger": "https://python.langchain.com/docs/integrations/document_transformers/openai_metadata_tagger/", "Image captions": "https://python.langchain.com/docs/integrations/document_loaders/image_captions/", "Figma": "https://python.langchain.com/docs/integrations/document_loaders/figma/", "OllamaLLM": "https://python.langchain.com/docs/integrations/llms/ollama/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Build a Local RAG Application": "https://python.langchain.com/docs/tutorials/local_rag/", "Summarize Text": "https://python.langchain.com/docs/tutorials/summarization/", "Build an Extraction Chain": "https://python.langchain.com/docs/tutorials/extraction/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/", "Classify Text into Labels": "https://python.langchain.com/docs/tutorials/classification/", "Build a Query Analysis System": "https://python.langchain.com/docs/tutorials/query_analysis/", "Build a Simple LLM Application with LCEL": "https://python.langchain.com/docs/tutorials/llm_chain/", "Build a PDF ingestion and Question/Answering system": "https://python.langchain.com/docs/tutorials/pdf_qa/", "Vector stores and retrievers": "https://python.langchain.com/docs/tutorials/retrievers/"}, "ChatAnthropic": {"🦜️🏓 LangServe": "https://python.langchain.com/docs/langserve/", "Conceptual guide": "https://python.langchain.com/docs/concepts/", "How to use callbacks in async environments": "https://python.langchain.com/docs/how_to/callbacks_async/", "How to route between sub-chains": "https://python.langchain.com/docs/how_to/routing/", "How to track token usage in ChatModels": "https://python.langchain.com/docs/how_to/chat_token_usage_tracking/", "How to merge consecutive messages of the same type": "https://python.langchain.com/docs/how_to/merge_message_runs/", "How to parse XML output": "https://python.langchain.com/docs/how_to/output_parser_xml/", "How to use prompting alone (no tool calling) to do extraction": "https://python.langchain.com/docs/how_to/extraction_parse/", "How to handle rate limits": "https://python.langchain.com/docs/how_to/chat_model_rate_limiting/", "How to add fallbacks to a runnable": "https://python.langchain.com/docs/how_to/fallbacks/", "How to propagate callbacks  constructor": "https://python.langchain.com/docs/how_to/callbacks_constructor/", "How to stream chat model responses": "https://python.langchain.com/docs/how_to/chat_streaming/", "How to attach callbacks to a runnable": "https://python.langchain.com/docs/how_to/callbacks_attach/", "How to filter messages": "https://python.langchain.com/docs/how_to/filter_messages/", "How to create a dynamic (self-constructing) chain": "https://python.langchain.com/docs/how_to/dynamic_chain/", "How to create custom callback handlers": "https://python.langchain.com/docs/how_to/custom_callbacks/", "How to configure runtime chain internals": "https://python.langchain.com/docs/how_to/configure/", "How to create a custom Output Parser": "https://python.langchain.com/docs/how_to/output_parser_custom/", "Response metadata": "https://python.langchain.com/docs/how_to/response_metadata/", "How to pass callbacks in at runtime": "https://python.langchain.com/docs/how_to/callbacks_runtime/", "Anthropic": "https://python.langchain.com/docs/integrations/providers/anthropic/", "PlayWright Browser Toolkit": "https://python.langchain.com/docs/integrations/tools/playwright/", "Riza Code Interpreter": "https://python.langchain.com/docs/integrations/tools/riza/", "ChatAnthropic": "https://python.langchain.com/docs/integrations/chat/anthropic/", "Log10": "https://python.langchain.com/docs/integrations/providers/log10/", "Build an Agent": "https://python.langchain.com/docs/tutorials/agents/"}, "ChatOpenAI": {"🦜️🏓 LangServe": "https://python.langchain.com/docs/langserve/", "Conceptual guide": "https://python.langchain.com/docs/concepts/", "# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_router_chain/", "Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "# Example": "https://python.langchain.com/docs/versions/migrating_chains/map_rerank_docs_chain/", "How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to disable parallel tool calling": "https://python.langchain.com/docs/how_to/tool_calling_parallel/", "How to add values to a chain's state": "https://python.langchain.com/docs/how_to/assign/", "How to track token usage in ChatModels": "https://python.langchain.com/docs/how_to/chat_token_usage_tracking/", "How to inspect runnables": "https://python.langchain.com/docs/how_to/inspect/", "How to bind model-specific tools": "https://python.langchain.com/docs/how_to/tools_model_specific/", "How to handle cases where no queries are generated": "https://python.langchain.com/docs/how_to/query_no_queries/", "How to trim messages": "https://python.langchain.com/docs/how_to/trim_messages/", "How to use few shot examples in chat models": "https://python.langchain.com/docs/how_to/few_shot_examples_chat/", "How to use LangChain with different Pydantic versions": "https://python.langchain.com/docs/how_to/pydantic_compatibility/", "How to use reference examples when doing extraction": "https://python.langchain.com/docs/how_to/extraction_examples/", "How to handle multiple queries when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_queries/", "How to add fallbacks to a runnable": "https://python.langchain.com/docs/how_to/fallbacks/", "How to map values to a graph database": "https://python.langchain.com/docs/how_to/graph_mapping/", "How to pass multimodal data directly to models": "https://python.langchain.com/docs/how_to/multimodal_inputs/", "How to save and load LangChain objects": "https://python.langchain.com/docs/how_to/serialization/", "How to use the output-fixing parser": "https://python.langchain.com/docs/how_to/output_parser_fixing/", "How to convert tools to OpenAI Functions": "https://python.langchain.com/docs/how_to/tools_as_openai_functions/", "How to add tools to chatbots": "https://python.langchain.com/docs/how_to/chatbots_tools/", "How to add default invocation args to a Runnable": "https://python.langchain.com/docs/how_to/binding/", "How to invoke runnables in parallel": "https://python.langchain.com/docs/how_to/parallel/", "How to pass through arguments from one step to the next": "https://python.langchain.com/docs/how_to/passthrough/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to configure runtime chain internals": "https://python.langchain.com/docs/how_to/configure/", "How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/", "How to handle multiple retrievers when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_retrievers/", "How to construct knowledge graphs": "https://python.langchain.com/docs/how_to/graph_constructing/", "How to retry when a parsing error occurs": "https://python.langchain.com/docs/how_to/output_parser_retry/", "How to use the MultiQueryRetriever": "https://python.langchain.com/docs/how_to/MultiQueryRetriever/", "How to run custom functions": "https://python.langchain.com/docs/how_to/functions/", "How to best prompt for Graph-RAG": "https://python.langchain.com/docs/how_to/graph_prompting/", "How to add memory to chatbots": "https://python.langchain.com/docs/how_to/chatbots_memory/", "How deal with high cardinality categoricals when doing query analysis": "https://python.langchain.com/docs/how_to/query_high_cardinality/", "How to get log probabilities": "https://python.langchain.com/docs/how_to/logprobs/", "How to parse YAML output": "https://python.langchain.com/docs/how_to/output_parser_yaml/", "Response metadata": "https://python.langchain.com/docs/how_to/response_metadata/", "How to parse JSON output": "https://python.langchain.com/docs/how_to/output_parser_json/", "How to do \"self-querying\" retrieval": "https://python.langchain.com/docs/how_to/self_query/", "Hybrid Search": "https://python.langchain.com/docs/how_to/hybrid/", "How to migrate from legacy LangChain agents to LangGraph": "https://python.langchain.com/docs/how_to/migrate_agent/", "How to stream tool calls": "https://python.langchain.com/docs/how_to/tool_streaming/", "How to use multimodal prompts": "https://python.langchain.com/docs/how_to/multimodal_prompts/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/", "How to use few-shot prompting with tool calling": "https://python.langchain.com/docs/how_to/tools_few_shot/", "How to add examples to the prompt for query analysis": "https://python.langchain.com/docs/how_to/query_few_shot/", "Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "WeChat": "https://python.langchain.com/docs/integrations/chat_loaders/wechat/", "Facebook Messenger": "https://python.langchain.com/docs/integrations/chat_loaders/facebook/", "LangSmith LLM Runs": "https://python.langchain.com/docs/integrations/chat_loaders/langsmith_llm_runs/", "Slack": "https://python.langchain.com/docs/integrations/chat_loaders/slack/", "WhatsApp": "https://python.langchain.com/docs/integrations/chat_loaders/whatsapp/", "LangSmith Chat Datasets": "https://python.langchain.com/docs/integrations/chat_loaders/langsmith_dataset/", "iMessage": "https://python.langchain.com/docs/integrations/chat_loaders/imessage/", "Telegram": "https://python.langchain.com/docs/integrations/chat_loaders/telegram/", "Discord": "https://python.langchain.com/docs/integrations/chat_loaders/discord/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "RePhraseQuery": "https://python.langchain.com/docs/integrations/retrievers/re_phrase/", "You.com": "https://python.langchain.com/docs/integrations/retrievers/you-retriever/", "Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/", "AskNews": "https://python.langchain.com/docs/integrations/tools/asknews/", "LLMLingua Document Compressor": "https://python.langchain.com/docs/integrations/retrievers/llmlingua/", "Outline": "https://python.langchain.com/docs/integrations/retrievers/outline/", "SEC filing": "https://python.langchain.com/docs/integrations/retrievers/sec_filings/", "TavilySearchAPIRetriever": "https://python.langchain.com/docs/integrations/retrievers/tavily/", "Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "RAGatouille": "https://python.langchain.com/docs/integrations/retrievers/ragatouille/", "ElasticsearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/elasticsearch_retriever/", "Rememberizer": "https://python.langchain.com/docs/integrations/retrievers/rememberizer/", "Milvus Hybrid Search Retriever": "https://python.langchain.com/docs/integrations/retrievers/milvus_hybrid_search/", "Kay.ai": "https://python.langchain.com/docs/integrations/retrievers/kay/", "FlashRank reranker": "https://python.langchain.com/docs/integrations/retrievers/flashrank-reranker/", "Vectara self-querying ": "https://python.langchain.com/docs/integrations/retrievers/self_query/vectara_self_query/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/", "Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/retrievers/self_query/tencentvectordb/", "MyScale": "https://python.langchain.com/docs/integrations/retrievers/self_query/myscale_self_query/", "OpenAI": "https://python.langchain.com/docs/integrations/providers/openai/", "ChatGPT Plugins": "https://python.langchain.com/docs/integrations/tools/chatgpt_plugins/", "Connery Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/connery/", "Infobip": "https://python.langchain.com/docs/integrations/tools/infobip/", "PowerBI Toolkit": "https://python.langchain.com/docs/integrations/tools/powerbi/", "E2B Data Analysis": "https://python.langchain.com/docs/integrations/tools/e2b_data_analysis/", "Human as a tool": "https://python.langchain.com/docs/integrations/tools/human_tools/", "Azure Container Apps dynamic sessions": "https://python.langchain.com/docs/integrations/tools/azure_dynamic_sessions/", "FinancialDatasets Toolkit": "https://python.langchain.com/docs/integrations/tools/financial_datasets/", "Slack Toolkit": "https://python.langchain.com/docs/integrations/tools/slack/", "Cassandra Database Toolkit": "https://python.langchain.com/docs/integrations/tools/cassandra_database/", "Yahoo Finance News": "https://python.langchain.com/docs/integrations/tools/yahoo_finance_news/", "Polygon IO Toolkit": "https://python.langchain.com/docs/integrations/tools/polygon_toolkit/", "Semantic Scholar API Tool": "https://python.langchain.com/docs/integrations/tools/semanticscholar/", "Spark SQL Toolkit": "https://python.langchain.com/docs/integrations/tools/spark_sql/", "Requests Toolkit": "https://python.langchain.com/docs/integrations/tools/requests/", "AINetwork Toolkit": "https://python.langchain.com/docs/integrations/tools/ainetwork/", "Passio NutritionAI": "https://python.langchain.com/docs/integrations/tools/passio_nutrition_ai/", "Cogniswitch Toolkit": "https://python.langchain.com/docs/integrations/tools/cogniswitch/", "Bearly Code Interpreter": "https://python.langchain.com/docs/integrations/tools/bearly/", "Pandas Dataframe": "https://python.langchain.com/docs/integrations/tools/pandas/", "ArXiv": "https://python.langchain.com/docs/integrations/tools/arxiv/", "Robocorp Toolkit": "https://python.langchain.com/docs/integrations/tools/robocorp/", "Connery Toolkit": "https://python.langchain.com/docs/integrations/tools/connery_toolkit/", "MultiOn Toolkit": "https://python.langchain.com/docs/integrations/tools/multion/", "Exa Search": "https://python.langchain.com/docs/integrations/tools/exa_search/", "Amadeus Toolkit": "https://python.langchain.com/docs/integrations/tools/amadeus/", "Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/", "You.com Search": "https://python.langchain.com/docs/integrations/tools/you/", "OpenAPI Toolkit": "https://python.langchain.com/docs/integrations/tools/openapi/", "Shell (bash)": "https://python.langchain.com/docs/integrations/tools/bash/", "Polygon IO Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/polygon/", "Redis": "https://python.langchain.com/docs/integrations/memory/redis_chat_message_history/", "ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "AWS DynamoDB": "https://python.langchain.com/docs/integrations/memory/aws_dynamodb/", "Couchbase": "https://python.langchain.com/docs/integrations/memory/couchbase_chat_message_history/", "MongoDB": "https://python.langchain.com/docs/integrations/memory/mongodb_chat_message_history/", "Xata": "https://python.langchain.com/docs/integrations/memory/xata_chat_message_history/", "Remembrall": "https://python.langchain.com/docs/integrations/memory/remembrall/", "SQL (SQLAlchemy)": "https://python.langchain.com/docs/integrations/memory/sql_chat_message_history/", "Streamlit": "https://python.langchain.com/docs/integrations/memory/streamlit_chat_message_history/", "SQLite": "https://python.langchain.com/docs/integrations/memory/sqlite/", "TiDB": "https://python.langchain.com/docs/integrations/memory/tidb_chat_message_history/", "ChatOpenAI": "https://python.langchain.com/docs/integrations/chat/openai/", "vLLM Chat": "https://python.langchain.com/docs/integrations/chat/vllm/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/", "Context": "https://python.langchain.com/docs/integrations/callbacks/context/", "Label Studio": "https://python.langchain.com/docs/integrations/callbacks/labelstudio/", "PromptLayer": "https://python.langchain.com/docs/integrations/callbacks/promptlayer/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "Trubrics": "https://python.langchain.com/docs/integrations/callbacks/trubrics/", "Infino": "https://python.langchain.com/docs/integrations/callbacks/infino/", "Upstash Ratelimit Callback": "https://python.langchain.com/docs/integrations/callbacks/upstash_ratelimit/", "CnosDB": "https://python.langchain.com/docs/integrations/providers/cnosdb/", "Log10": "https://python.langchain.com/docs/integrations/providers/log10/", "MLflow": "https://python.langchain.com/docs/integrations/providers/mlflow_tracking/", "Flyte": "https://python.langchain.com/docs/integrations/providers/flyte/", "Arthur": "https://python.langchain.com/docs/integrations/providers/arthur_tracking/", "Dataherald": "https://python.langchain.com/docs/integrations/providers/dataherald/", "Log, Trace, and Monitor": "https://python.langchain.com/docs/integrations/providers/portkey/logging_tracing_portkey/", "Portkey": "https://python.langchain.com/docs/integrations/providers/portkey/index/", "Weaviate": "https://python.langchain.com/docs/integrations/vectorstores/weaviate/", "Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/", "Hippo": "https://python.langchain.com/docs/integrations/vectorstores/hippo/", "Vectara": "https://python.langchain.com/docs/integrations/vectorstores/vectara/", "Momento Vector Index (MVI)": "https://python.langchain.com/docs/integrations/vectorstores/momento_vector_index/", "Neo4j Vector Index": "https://python.langchain.com/docs/integrations/vectorstores/neo4jvector/", "Apache Cassandra": "https://python.langchain.com/docs/integrations/vectorstores/cassandra/", "KDB.AI": "https://python.langchain.com/docs/integrations/vectorstores/kdbai/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "RankLLM Reranker": "https://python.langchain.com/docs/integrations/document_transformers/rankllm-reranker/", "OpenAI metadata tagger": "https://python.langchain.com/docs/integrations/document_transformers/openai_metadata_tagger/", "Jina Reranker": "https://python.langchain.com/docs/integrations/document_transformers/jina_rerank/", "YouTube audio": "https://python.langchain.com/docs/integrations/document_loaders/youtube_audio/", "Image captions": "https://python.langchain.com/docs/integrations/document_loaders/image_captions/", "Figma": "https://python.langchain.com/docs/integrations/document_loaders/figma/", "Browserbase": "https://python.langchain.com/docs/integrations/document_loaders/browserbase/", "Memgraph": "https://python.langchain.com/docs/integrations/graphs/memgraph/", "RDFLib": "https://python.langchain.com/docs/integrations/graphs/rdflib_sparql/", "NebulaGraph": "https://python.langchain.com/docs/integrations/graphs/nebula_graph/", "HugeGraph": "https://python.langchain.com/docs/integrations/graphs/hugegraph/", "Diffbot": "https://python.langchain.com/docs/integrations/graphs/diffbot/", "Ontotext GraphDB": "https://python.langchain.com/docs/integrations/graphs/ontotext/", "Apache AGE": "https://python.langchain.com/docs/integrations/graphs/apache_age/", "Neo4j": "https://python.langchain.com/docs/integrations/graphs/neo4j_cypher/", "ArangoDB": "https://python.langchain.com/docs/integrations/graphs/arangodb/", "Amazon Neptune with Cypher": "https://python.langchain.com/docs/integrations/graphs/amazon_neptune_open_cypher/", "Kuzu": "https://python.langchain.com/docs/integrations/graphs/kuzu_db/", "FalkorDB": "https://python.langchain.com/docs/integrations/graphs/falkordb/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/", "Classify Text into Labels": "https://python.langchain.com/docs/tutorials/classification/", "Build a Query Analysis System": "https://python.langchain.com/docs/tutorials/query_analysis/", "Build a Simple LLM Application with LCEL": "https://python.langchain.com/docs/tutorials/llm_chain/", "Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/", "Build a Question Answering application over a Graph Database": "https://python.langchain.com/docs/tutorials/graph/"}, "SystemMessage": {"🦜️🏓 LangServe": "https://python.langchain.com/docs/langserve/", "How to merge consecutive messages of the same type": "https://python.langchain.com/docs/how_to/merge_message_runs/", "How to trim messages": "https://python.langchain.com/docs/how_to/trim_messages/", "How to use reference examples when doing extraction": "https://python.langchain.com/docs/how_to/extraction_examples/", "How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/", "How to filter messages": "https://python.langchain.com/docs/how_to/filter_messages/", "How to compose prompts together": "https://python.langchain.com/docs/how_to/prompts_composition/", "How to migrate from legacy LangChain agents to LangGraph": "https://python.langchain.com/docs/how_to/migrate_agent/", "How to add examples to the prompt for query analysis": "https://python.langchain.com/docs/how_to/query_few_shot/", "Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "Robocorp Toolkit": "https://python.langchain.com/docs/integrations/tools/robocorp/", "Exa Search": "https://python.langchain.com/docs/integrations/tools/exa_search/", "Snowflake Cortex": "https://python.langchain.com/docs/integrations/chat/snowflake/", "# Related": "https://python.langchain.com/docs/integrations/chat/solar/", "ChatHuggingFace": "https://python.langchain.com/docs/integrations/chat/huggingface/", "ChatOctoAI": "https://python.langchain.com/docs/integrations/chat/octoai/", "ChatYI": "https://python.langchain.com/docs/integrations/chat/yi/", "LlamaEdge": "https://python.langchain.com/docs/integrations/chat/llama_edge/", "ChatKonko": "https://python.langchain.com/docs/integrations/chat/konko/", "GigaChat": "https://python.langchain.com/docs/integrations/chat/gigachat/", "JinaChat": "https://python.langchain.com/docs/integrations/chat/jinachat/", "ChatOCIGenAI": "https://python.langchain.com/docs/integrations/chat/oci_generative_ai/", "ChatEverlyAI": "https://python.langchain.com/docs/integrations/chat/everlyai/", "ChatFriendli": "https://python.langchain.com/docs/integrations/chat/friendli/", "ZHIPU AI": "https://python.langchain.com/docs/integrations/chat/zhipuai/", "Llama2Chat": "https://python.langchain.com/docs/integrations/chat/llama2_chat/", "ChatWatsonx": "https://python.langchain.com/docs/integrations/chat/ibm_watsonx/", "vLLM Chat": "https://python.langchain.com/docs/integrations/chat/vllm/", "Yuan2.0": "https://python.langchain.com/docs/integrations/chat/yuan2/", "ChatTongyi": "https://python.langchain.com/docs/integrations/chat/tongyi/", "MoonshotChat": "https://python.langchain.com/docs/integrations/chat/moonshot/", "ChatPremAI": "https://python.langchain.com/docs/integrations/chat/premai/", "ChatAnyscale": "https://python.langchain.com/docs/integrations/chat/anyscale/", "ChatYandexGPT": "https://python.langchain.com/docs/integrations/chat/yandex/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/", "Context": "https://python.langchain.com/docs/integrations/callbacks/context/", "Label Studio": "https://python.langchain.com/docs/integrations/callbacks/labelstudio/", "Trubrics": "https://python.langchain.com/docs/integrations/callbacks/trubrics/", "MLflow Deployments for LLMs": "https://python.langchain.com/docs/integrations/providers/mlflow/", "MLflow AI Gateway": "https://python.langchain.com/docs/integrations/providers/mlflow_ai_gateway/", "PremAI": "https://python.langchain.com/docs/integrations/providers/premai/", "Javelin AI Gateway": "https://python.langchain.com/docs/integrations/providers/javelin_ai_gateway/", "Google Cloud Vertex AI": "https://python.langchain.com/docs/integrations/llms/google_vertex_ai_palm/", "Javelin AI Gateway Tutorial": "https://python.langchain.com/docs/integrations/llms/javelin/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/", "Build a Simple LLM Application with LCEL": "https://python.langchain.com/docs/tutorials/llm_chain/"}, "HumanMessage": {"🦜️🏓 LangServe": "https://python.langchain.com/docs/langserve/", "Conceptual guide": "https://python.langchain.com/docs/concepts/", "Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to use callbacks in async environments": "https://python.langchain.com/docs/how_to/callbacks_async/", "How to merge consecutive messages of the same type": "https://python.langchain.com/docs/how_to/merge_message_runs/", "How to trim messages": "https://python.langchain.com/docs/how_to/trim_messages/", "How to do tool/function calling": "https://python.langchain.com/docs/how_to/function_calling/", "How to use reference examples when doing extraction": "https://python.langchain.com/docs/how_to/extraction_examples/", "How to pass multimodal data directly to models": "https://python.langchain.com/docs/how_to/multimodal_inputs/", "How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/", "How to convert tools to OpenAI Functions": "https://python.langchain.com/docs/how_to/tools_as_openai_functions/", "How to filter messages": "https://python.langchain.com/docs/how_to/filter_messages/", "How to handle tool errors": "https://python.langchain.com/docs/how_to/tools_error/", "How to add tools to chatbots": "https://python.langchain.com/docs/how_to/chatbots_tools/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add message history": "https://python.langchain.com/docs/how_to/message_history/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to pass tool outputs to chat models": "https://python.langchain.com/docs/how_to/tool_results_pass_to_model/", "How to return structured data from a model": "https://python.langchain.com/docs/how_to/structured_output/", "How to compose prompts together": "https://python.langchain.com/docs/how_to/prompts_composition/", "How to use few-shot prompting with tool calling": "https://python.langchain.com/docs/how_to/tools_few_shot/", "How to add examples to the prompt for query analysis": "https://python.langchain.com/docs/how_to/query_few_shot/", "WeChat": "https://python.langchain.com/docs/integrations/chat_loaders/wechat/", "Discord": "https://python.langchain.com/docs/integrations/chat_loaders/discord/", "Zep Open Source": "https://python.langchain.com/docs/integrations/retrievers/zep_memorystore/", "Zep Cloud": "https://python.langchain.com/docs/integrations/retrievers/zep_cloud_memorystore/", "Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Imagen": "https://python.langchain.com/docs/integrations/tools/google_imagen/", "Zep Open Source Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory/", "ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "Zep Cloud Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory_cloud/", "Snowflake Cortex": "https://python.langchain.com/docs/integrations/chat/snowflake/", "# Related": "https://python.langchain.com/docs/integrations/chat/solar/", "ChatHuggingFace": "https://python.langchain.com/docs/integrations/chat/huggingface/", "AzureMLChatOnlineEndpoint": "https://python.langchain.com/docs/integrations/chat/azureml_chat_endpoint/", "Alibaba Cloud PAI EAS": "https://python.langchain.com/docs/integrations/chat/alibaba_cloud_pai_eas/", "Chat with Coze Bot": "https://python.langchain.com/docs/integrations/chat/coze/", "ChatOctoAI": "https://python.langchain.com/docs/integrations/chat/octoai/", "ChatYI": "https://python.langchain.com/docs/integrations/chat/yi/", "DeepInfra": "https://python.langchain.com/docs/integrations/chat/deepinfra/", "ChatLiteLLM": "https://python.langchain.com/docs/integrations/chat/litellm/", "LlamaEdge": "https://python.langchain.com/docs/integrations/chat/llama_edge/", "VolcEngineMaasChat": "https://python.langchain.com/docs/integrations/chat/volcengine_maas/", "ChatKonko": "https://python.langchain.com/docs/integrations/chat/konko/", "MLX": "https://python.langchain.com/docs/integrations/chat/mlx/", "GigaChat": "https://python.langchain.com/docs/integrations/chat/gigachat/", "JinaChat": "https://python.langchain.com/docs/integrations/chat/jinachat/", "ChatOllama": "https://python.langchain.com/docs/integrations/chat/ollama/", "ChatOCIGenAI": "https://python.langchain.com/docs/integrations/chat/oci_generative_ai/", "ChatEverlyAI": "https://python.langchain.com/docs/integrations/chat/everlyai/", "GPTRouter": "https://python.langchain.com/docs/integrations/chat/gpt_router/", "ChatLiteLLMRouter": "https://python.langchain.com/docs/integrations/chat/litellm_router/", "ChatFriendli": "https://python.langchain.com/docs/integrations/chat/friendli/", "ZHIPU AI": "https://python.langchain.com/docs/integrations/chat/zhipuai/", "Chat with Baichuan-192K": "https://python.langchain.com/docs/integrations/chat/baichuan/", "QianfanChatEndpoint": "https://python.langchain.com/docs/integrations/chat/baidu_qianfan_endpoint/", "Cohere": "https://python.langchain.com/docs/integrations/llms/cohere/", "Eden AI": "https://python.langchain.com/docs/integrations/chat/edenai/", "ErnieBotChat": "https://python.langchain.com/docs/integrations/chat/ernie/", "ChatWatsonx": "https://python.langchain.com/docs/integrations/chat/ibm_watsonx/", "vLLM Chat": "https://python.langchain.com/docs/integrations/chat/vllm/", "Tencent Hunyuan": "https://python.langchain.com/docs/integrations/chat/tencent_hunyuan/", "MiniMaxChat": "https://python.langchain.com/docs/integrations/chat/minimax/", "Yuan2.0": "https://python.langchain.com/docs/integrations/chat/yuan2/", "ChatTongyi": "https://python.langchain.com/docs/integrations/chat/tongyi/", "PromptLayerChatOpenAI": "https://python.langchain.com/docs/integrations/chat/promptlayer_chatopenai/", "SparkLLM Chat": "https://python.langchain.com/docs/integrations/chat/sparkllm/", "MoonshotChat": "https://python.langchain.com/docs/integrations/chat/moonshot/", "Dappier AI": "https://python.langchain.com/docs/integrations/chat/dappier/", "Maritalk": "https://python.langchain.com/docs/integrations/chat/maritalk/", "ChatPremAI": "https://python.langchain.com/docs/integrations/chat/premai/", "ChatAnyscale": "https://python.langchain.com/docs/integrations/chat/anyscale/", "ChatYandexGPT": "https://python.langchain.com/docs/integrations/chat/yandex/", "ChatNVIDIA": "https://python.langchain.com/docs/integrations/chat/nvidia_ai_endpoints/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/", "Context": "https://python.langchain.com/docs/integrations/callbacks/context/", "Label Studio": "https://python.langchain.com/docs/integrations/callbacks/labelstudio/", "PromptLayer": "https://python.langchain.com/docs/integrations/callbacks/promptlayer/", "Trubrics": "https://python.langchain.com/docs/integrations/callbacks/trubrics/", "Log10": "https://python.langchain.com/docs/integrations/providers/log10/", "MLflow Deployments for LLMs": "https://python.langchain.com/docs/integrations/providers/mlflow/", "MLflow AI Gateway": "https://python.langchain.com/docs/integrations/providers/mlflow_ai_gateway/", "Flyte": "https://python.langchain.com/docs/integrations/providers/flyte/", "PremAI": "https://python.langchain.com/docs/integrations/providers/premai/", "Arthur": "https://python.langchain.com/docs/integrations/providers/arthur_tracking/", "Javelin AI Gateway": "https://python.langchain.com/docs/integrations/providers/javelin_ai_gateway/", "Browserbase": "https://python.langchain.com/docs/integrations/document_loaders/browserbase/", "Google Cloud Vertex AI": "https://python.langchain.com/docs/integrations/llms/google_vertex_ai_palm/", "Javelin AI Gateway Tutorial": "https://python.langchain.com/docs/integrations/llms/javelin/", "Azure ML": "https://python.langchain.com/docs/integrations/llms/azure_ml/", "Chat Bot Feedback Template": "https://python.langchain.com/docs/templates/chat-bot-feedback/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/", "Build an Agent": "https://python.langchain.com/docs/tutorials/agents/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/", "Build a Simple LLM Application with LCEL": "https://python.langchain.com/docs/tutorials/llm_chain/"}, "RunnableMap": {"🦜️🏓 LangServe": "https://python.langchain.com/docs/langserve/"}, "RunnableLambda": {"🦜️🏓 LangServe": "https://python.langchain.com/docs/langserve/", "How to handle long text when doing extraction": "https://python.langchain.com/docs/how_to/extraction_long_text/", "How to route between sub-chains": "https://python.langchain.com/docs/how_to/routing/", "How to convert Runnables as Tools": "https://python.langchain.com/docs/how_to/convert_runnable_to_tool/", "How to stream runnables": "https://python.langchain.com/docs/how_to/streaming/", "How to retry when a parsing error occurs": "https://python.langchain.com/docs/how_to/output_parser_retry/", "How to run custom functions": "https://python.langchain.com/docs/how_to/functions/", "LangChain Expression Language Cheatsheet": "https://python.langchain.com/docs/how_to/lcel_cheatsheet/", "How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/", "How to dispatch custom callback events": "https://python.langchain.com/docs/how_to/callbacks_custom_events/", "Upstash Ratelimit Callback": "https://python.langchain.com/docs/integrations/callbacks/upstash_ratelimit/", "Vector stores and retrievers": "https://python.langchain.com/docs/tutorials/retrievers/"}, "PromptTemplate": {"Conceptual guide": "https://python.langchain.com/docs/concepts/", "# Example": "https://python.langchain.com/docs/versions/migrating_chains/map_rerank_docs_chain/", "# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_router_chain/", "How to better prompt when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_prompting/", "How to use output parsers to parse an LLM response into structured format": "https://python.langchain.com/docs/how_to/output_parser_structured/", "How to route between sub-chains": "https://python.langchain.com/docs/how_to/routing/", "How to select examples by n-gram overlap": "https://python.langchain.com/docs/how_to/example_selectors_ngram/", "How to select examples by length": "https://python.langchain.com/docs/how_to/example_selectors_length_based/", "How to use example selectors": "https://python.langchain.com/docs/how_to/example_selectors/", "How to use few shot examples": "https://python.langchain.com/docs/how_to/few_shot_examples/", "How to select examples by similarity": "https://python.langchain.com/docs/how_to/example_selectors_similarity/", "How to parse XML output": "https://python.langchain.com/docs/how_to/output_parser_xml/", "How to reorder retrieved results to mitigate the \"lost in the middle\" effect": "https://python.langchain.com/docs/how_to/long_context_reorder/", "How to add fallbacks to a runnable": "https://python.langchain.com/docs/how_to/fallbacks/", "Run models locally": "https://python.langchain.com/docs/how_to/local_llms/", "How to configure runtime chain internals": "https://python.langchain.com/docs/how_to/configure/", "How to retry when a parsing error occurs": "https://python.langchain.com/docs/how_to/output_parser_retry/", "How to use the MultiQueryRetriever": "https://python.langchain.com/docs/how_to/MultiQueryRetriever/", "How to best prompt for Graph-RAG": "https://python.langchain.com/docs/how_to/graph_prompting/", "How to parse YAML output": "https://python.langchain.com/docs/how_to/output_parser_yaml/", "How to compose prompts together": "https://python.langchain.com/docs/how_to/prompts_composition/", "How to partially format prompt templates": "https://python.langchain.com/docs/how_to/prompts_partial/", "How to parse JSON output": "https://python.langchain.com/docs/how_to/output_parser_json/", "How to select examples by maximal marginal relevance (MMR)": "https://python.langchain.com/docs/how_to/example_selectors_mmr/", "How to track token usage for LLMs": "https://python.langchain.com/docs/how_to/llm_token_usage_tracking/", "Clarifai": "https://python.langchain.com/docs/integrations/llms/clarifai/", "RePhraseQuery": "https://python.langchain.com/docs/integrations/retrievers/re_phrase/", "Google Drive": "https://python.langchain.com/docs/integrations/document_loaders/google_drive/", "Milvus Hybrid Search Retriever": "https://python.langchain.com/docs/integrations/retrievers/milvus_hybrid_search/", "Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/", "NVIDIA Riva: ASR and TTS": "https://python.langchain.com/docs/integrations/tools/nvidia_riva/", "Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/", "Dall-E Image Generator": "https://python.langchain.com/docs/integrations/tools/dalle_image_generator/", "Motörhead": "https://python.langchain.com/docs/integrations/memory/motorhead_memory/", "Context": "https://python.langchain.com/docs/integrations/callbacks/context/", "SageMaker Tracking": "https://python.langchain.com/docs/integrations/callbacks/sagemaker_tracking/", "Argilla": "https://python.langchain.com/docs/integrations/callbacks/argilla/", "DSPy": "https://python.langchain.com/docs/integrations/providers/dspy/", "Comet": "https://python.langchain.com/docs/integrations/providers/comet_tracking/", "Aim": "https://python.langchain.com/docs/integrations/providers/aim_tracking/", "Weights & Biases": "https://python.langchain.com/docs/integrations/providers/wandb_tracking/", "MLflow AI Gateway": "https://python.langchain.com/docs/integrations/providers/mlflow_ai_gateway/", "Rebuff": "https://python.langchain.com/docs/integrations/providers/rebuff/", "Prediction Guard": "https://python.langchain.com/docs/integrations/llms/predictionguard/", "Shale Protocol": "https://python.langchain.com/docs/integrations/providers/shaleprotocol/", "Flyte": "https://python.langchain.com/docs/integrations/providers/flyte/", "Ray Serve": "https://python.langchain.com/docs/integrations/providers/ray_serve/", "Javelin AI Gateway": "https://python.langchain.com/docs/integrations/providers/javelin_ai_gateway/", "Identity-enabled RAG using PebbloRetrievalQA": "https://python.langchain.com/docs/integrations/providers/pebblo/pebblo_retrieval_qa/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/", "Amazon Document DB": "https://python.langchain.com/docs/integrations/vectorstores/documentdb/", "Google Cloud Vertex AI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/google_cloud_vertexai_rerank/", "AirbyteLoader": "https://python.langchain.com/docs/integrations/document_loaders/airbyte/", "Memgraph": "https://python.langchain.com/docs/integrations/graphs/memgraph/", "Apache AGE": "https://python.langchain.com/docs/integrations/graphs/apache_age/", "Neo4j": "https://python.langchain.com/docs/integrations/graphs/neo4j_cypher/", "Baseten": "https://python.langchain.com/docs/integrations/llms/baseten/", "StochasticAI": "https://python.langchain.com/docs/integrations/llms/stochasticai/", "Solar": "https://python.langchain.com/docs/integrations/llms/solar/", "Bittensor": "https://python.langchain.com/docs/integrations/llms/bittensor/", "IPEX-LLM": "https://python.langchain.com/docs/integrations/llms/ipex_llm/", "Banana": "https://python.langchain.com/docs/integrations/llms/banana/", "Alibaba Cloud PAI EAS": "https://python.langchain.com/docs/integrations/llms/alibabacloud_pai_eas_endpoint/", "OpenLLM": "https://python.langchain.com/docs/integrations/llms/openllm/", "SageMakerEndpoint": "https://python.langchain.com/docs/integrations/llms/sagemaker/", "Fireworks": "https://python.langchain.com/docs/integrations/llms/fireworks/", "OctoAI": "https://python.langchain.com/docs/integrations/llms/octoai/", "Writer": "https://python.langchain.com/docs/integrations/llms/writer/", "Modal": "https://python.langchain.com/docs/integrations/llms/modal/", "TextGen": "https://python.langchain.com/docs/integrations/llms/textgen/", "Xorbits Inference (Xinference)": "https://python.langchain.com/docs/integrations/llms/xinference/", "Nebula (Symbl.ai)": "https://python.langchain.com/docs/integrations/llms/symblai_nebula/", "DeepInfra": "https://python.langchain.com/docs/integrations/llms/deepinfra/", "AnthropicLLM": "https://python.langchain.com/docs/integrations/llms/anthropic/", "NLP Cloud": "https://python.langchain.com/docs/integrations/llms/nlpcloud/", "GPT4All": "https://python.langchain.com/docs/integrations/llms/gpt4all/", "ForefrontAI": "https://python.langchain.com/docs/integrations/llms/forefrontai/", "MosaicML": "https://python.langchain.com/docs/integrations/llms/mosaicml/", "Volc Engine Maas": "https://python.langchain.com/docs/integrations/llms/volcengine_maas/", "CerebriumAI": "https://python.langchain.com/docs/integrations/llms/cerebriumai/", "OpenAI": "https://python.langchain.com/docs/integrations/llms/openai/", "Google Cloud Vertex AI": "https://python.langchain.com/docs/integrations/llms/google_vertex_ai_palm/", "Predibase": "https://python.langchain.com/docs/integrations/llms/predibase/", "GigaChat": "https://python.langchain.com/docs/integrations/llms/gigachat/", "# Oracle Cloud Infrastructure Generative AI": "https://python.langchain.com/docs/integrations/llms/oci_generative_ai/", "Llama.cpp": "https://python.langchain.com/docs/integrations/llms/llamacpp/", "Hugging Face Local Pipelines": "https://python.langchain.com/docs/integrations/llms/huggingface_pipelines/", "OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/", "Titan Takeoff": "https://python.langchain.com/docs/integrations/llms/titan_takeoff/", "Aphrodite Engine": "https://python.langchain.com/docs/integrations/llms/aphrodite/", "AI21LLM": "https://python.langchain.com/docs/integrations/llms/ai21/", "Cohere": "https://python.langchain.com/docs/integrations/llms/cohere/", "Eden AI": "https://python.langchain.com/docs/integrations/llms/edenai/", "Javelin AI Gateway Tutorial": "https://python.langchain.com/docs/integrations/llms/javelin/", "IBM watsonx.ai": "https://python.langchain.com/docs/integrations/llms/ibm_watsonx/", "C Transformers": "https://python.langchain.com/docs/integrations/llms/ctransformers/", "vLLM": "https://python.langchain.com/docs/integrations/llms/vllm/", "Azure ML": "https://python.langchain.com/docs/integrations/llms/azure_ml/", "Manifest": "https://python.langchain.com/docs/integrations/llms/manifest/", "ExLlamaV2": "https://python.langchain.com/docs/integrations/llms/exllamav2/", "Minimax": "https://python.langchain.com/docs/integrations/llms/minimax/", "Tongyi Qwen": "https://python.langchain.com/docs/integrations/llms/tongyi/", "Huggingface Endpoints": "https://python.langchain.com/docs/integrations/llms/huggingface_endpoint/", "MLX Local Pipelines": "https://python.langchain.com/docs/integrations/llms/mlx_pipelines/", "Runhouse": "https://python.langchain.com/docs/integrations/llms/runhouse/", "Anyscale": "https://python.langchain.com/docs/integrations/llms/anyscale/", "YandexGPT": "https://python.langchain.com/docs/integrations/llms/yandex/", "GooseAI": "https://python.langchain.com/docs/integrations/llms/gooseai/", "OpenLM": "https://python.langchain.com/docs/integrations/llms/openlm/", "Aleph Alpha": "https://python.langchain.com/docs/integrations/llms/aleph_alpha/", "Cloudflare Workers AI": "https://python.langchain.com/docs/integrations/llms/cloudflare_workersai/", "CTranslate2": "https://python.langchain.com/docs/integrations/llms/ctranslate2/", "Google AI": "https://python.langchain.com/docs/integrations/llms/google_ai/", "PipelineAI": "https://python.langchain.com/docs/integrations/llms/pipelineai/", "ChatGLM": "https://python.langchain.com/docs/integrations/llms/chatglm/", "Gradient": "https://python.langchain.com/docs/integrations/llms/gradient/", "Petals": "https://python.langchain.com/docs/integrations/llms/petals/", "OpenVINO": "https://python.langchain.com/docs/integrations/llms/openvino/", "Intel Weight-Only Quantization": "https://python.langchain.com/docs/integrations/llms/weight_only_quantization/", "Replicate": "https://python.langchain.com/docs/integrations/llms/replicate/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/", "Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/"}, "MessagesPlaceholder": {"Conceptual guide": "https://python.langchain.com/docs/concepts/", "How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to handle long text when doing extraction": "https://python.langchain.com/docs/how_to/extraction_long_text/", "How to use reference examples when doing extraction": "https://python.langchain.com/docs/how_to/extraction_examples/", "How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/", "How to stream results from your RAG application": "https://python.langchain.com/docs/how_to/qa_streaming/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add message history": "https://python.langchain.com/docs/how_to/message_history/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/", "How to add examples to the prompt for query analysis": "https://python.langchain.com/docs/how_to/query_few_shot/", "Redis": "https://python.langchain.com/docs/integrations/memory/redis_chat_message_history/", "Google SQL for MySQL": "https://python.langchain.com/docs/integrations/memory/google_sql_mysql/", "Google AlloyDB for PostgreSQL": "https://python.langchain.com/docs/integrations/memory/google_alloydb/", "ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "AWS DynamoDB": "https://python.langchain.com/docs/integrations/memory/aws_dynamodb/", "Couchbase": "https://python.langchain.com/docs/integrations/memory/couchbase_chat_message_history/", "MongoDB": "https://python.langchain.com/docs/integrations/memory/mongodb_chat_message_history/", "SQL (SQLAlchemy)": "https://python.langchain.com/docs/integrations/memory/sql_chat_message_history/", "Streamlit": "https://python.langchain.com/docs/integrations/memory/streamlit_chat_message_history/", "Google El Carro Oracle": "https://python.langchain.com/docs/integrations/memory/google_el_carro/", "SQLite": "https://python.langchain.com/docs/integrations/memory/sqlite/", "Google SQL for PostgreSQL": "https://python.langchain.com/docs/integrations/memory/google_sql_pg/", "Google SQL for SQL Server": "https://python.langchain.com/docs/integrations/memory/google_sql_mssql/", "TiDB": "https://python.langchain.com/docs/integrations/memory/tidb_chat_message_history/", "Llama2Chat": "https://python.langchain.com/docs/integrations/chat/llama2_chat/", "Build an Extraction Chain": "https://python.langchain.com/docs/tutorials/extraction/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/"}, "CSVLoader": {"Conceptual guide": "https://python.langchain.com/docs/concepts/", "How to load CSVs": "https://python.langchain.com/docs/how_to/document_loader_csv/", "ChatGPT plugin": "https://python.langchain.com/docs/integrations/retrievers/chatgpt-plugin/", "Aerospike": "https://python.langchain.com/docs/integrations/vectorstores/aerospike/", "CSV": "https://python.langchain.com/docs/integrations/document_loaders/csv/", "Document loaders": "https://python.langchain.com/docs/integrations/document_loaders/index/", "Pebblo Safe DocumentLoader": "https://python.langchain.com/docs/integrations/document_loaders/pebblo/"}, "StrOutputParser": {"Conceptual guide": "https://python.langchain.com/docs/concepts/", "# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_chain/", "# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/", "# Example": "https://python.langchain.com/docs/versions/migrating_chains/refine_docs_chain/", "Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "How to add values to a chain's state": "https://python.langchain.com/docs/how_to/assign/", "How to route between sub-chains": "https://python.langchain.com/docs/how_to/routing/", "How to do per-user retrieval": "https://python.langchain.com/docs/how_to/qa_per_user/", "How to inspect runnables": "https://python.langchain.com/docs/how_to/inspect/", "How to summarize text through iterative refinement": "https://python.langchain.com/docs/how_to/summarize_refine/", "How to create tools": "https://python.langchain.com/docs/how_to/custom_tools/", "How to add fallbacks to a runnable": "https://python.langchain.com/docs/how_to/fallbacks/", "How to map values to a graph database": "https://python.langchain.com/docs/how_to/graph_mapping/", "How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/", "How to get your RAG application to return sources": "https://python.langchain.com/docs/how_to/qa_sources/", "How to summarize text through parallelization": "https://python.langchain.com/docs/how_to/summarize_map_reduce/", "How to add default invocation args to a Runnable": "https://python.langchain.com/docs/how_to/binding/", "How to convert Runnables as Tools": "https://python.langchain.com/docs/how_to/convert_runnable_to_tool/", "How to stream events from a tool": "https://python.langchain.com/docs/how_to/tool_stream_events/", "How to create a dynamic (self-constructing) chain": "https://python.langchain.com/docs/how_to/dynamic_chain/", "How to stream runnables": "https://python.langchain.com/docs/how_to/streaming/", "How to invoke runnables in parallel": "https://python.langchain.com/docs/how_to/parallel/", "How to pass through arguments from one step to the next": "https://python.langchain.com/docs/how_to/passthrough/", "How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/", "How to run custom functions": "https://python.langchain.com/docs/how_to/functions/", "How to chain runnables": "https://python.langchain.com/docs/how_to/sequence/", "Hybrid Search": "https://python.langchain.com/docs/how_to/hybrid/", "How to do query validation as part of SQL question-answering": "https://python.langchain.com/docs/how_to/sql_query_checking/", "Facebook Messenger": "https://python.langchain.com/docs/integrations/chat_loaders/facebook/", "iMessage": "https://python.langchain.com/docs/integrations/chat_loaders/imessage/", "NVIDIA NIMs ": "https://python.langchain.com/docs/integrations/text_embedding/nvidia_ai_endpoints/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "You.com": "https://python.langchain.com/docs/integrations/retrievers/you-retriever/", "Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/", "AskNews": "https://python.langchain.com/docs/integrations/retrievers/asknews/", "WikipediaRetriever": "https://python.langchain.com/docs/integrations/retrievers/wikipedia/", "TavilySearchAPIRetriever": "https://python.langchain.com/docs/integrations/retrievers/tavily/", "ArxivRetriever": "https://python.langchain.com/docs/integrations/retrievers/arxiv/", "ElasticsearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/elasticsearch_retriever/", "Milvus Hybrid Search Retriever": "https://python.langchain.com/docs/integrations/retrievers/milvus_hybrid_search/", "Google Vertex AI Search": "https://python.langchain.com/docs/integrations/retrievers/google_vertex_ai_search/", "ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "ChatOllama": "https://python.langchain.com/docs/integrations/chat/ollama/", "Maritalk": "https://python.langchain.com/docs/integrations/chat/maritalk/", "ChatNVIDIA": "https://python.langchain.com/docs/integrations/chat/nvidia_ai_endpoints/", "Fiddler": "https://python.langchain.com/docs/integrations/callbacks/fiddler/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "DSPy": "https://python.langchain.com/docs/integrations/providers/dspy/", "MLflow": "https://python.langchain.com/docs/integrations/providers/mlflow_tracking/", "Shale Protocol": "https://python.langchain.com/docs/integrations/providers/shaleprotocol/", "Weaviate": "https://python.langchain.com/docs/integrations/vectorstores/weaviate/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/", "Apache Cassandra": "https://python.langchain.com/docs/integrations/vectorstores/cassandra/", "Volc Engine Maas": "https://python.langchain.com/docs/integrations/llms/volcengine_maas/", "OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/", "AI21LLM": "https://python.langchain.com/docs/integrations/llms/ai21/", "PipelineAI": "https://python.langchain.com/docs/integrations/llms/pipelineai/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Build a Local RAG Application": "https://python.langchain.com/docs/tutorials/local_rag/", "Summarize Text": "https://python.langchain.com/docs/tutorials/summarization/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/", "Build a Simple LLM Application with LCEL": "https://python.langchain.com/docs/tutorials/llm_chain/"}, "SimpleJsonOutputParser": {"Conceptual guide": "https://python.langchain.com/docs/concepts/", "How to use output parsers to parse an LLM response into structured format": "https://python.langchain.com/docs/how_to/output_parser_structured/"}, "BaseChatModel": {"Contribute Integrations": "https://python.langchain.com/docs/contributing/how_to/integrations/", "How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "deprecated": {"Contribute Integrations": "https://python.langchain.com/docs/contributing/how_to/integrations/"}, "UnstructuredMarkdownLoader": {"langchain": "https://python.langchain.com/docs/changes/changelog/langchain/", "How to load Markdown": "https://python.langchain.com/docs/how_to/document_loader_markdown/", "Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "Apache Doris": "https://python.langchain.com/docs/integrations/vectorstores/apache_doris/", "StarRocks": "https://python.langchain.com/docs/integrations/vectorstores/starrocks/", "UnstructuredMarkdownLoader": "https://python.langchain.com/docs/integrations/document_loaders/unstructured_markdown/"}, "Document": {"# Example": "https://python.langchain.com/docs/versions/migrating_chains/map_rerank_docs_chain/", "# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/", "How to handle long text when doing extraction": "https://python.langchain.com/docs/how_to/extraction_long_text/", "How to create a custom Document Loader": "https://python.langchain.com/docs/how_to/document_loader_custom/", "How to summarize text through iterative refinement": "https://python.langchain.com/docs/how_to/summarize_refine/", "How to summarize text through parallelization": "https://python.langchain.com/docs/how_to/summarize_map_reduce/", "How to use the LangChain indexing API": "https://python.langchain.com/docs/how_to/indexing/", "How to convert Runnables as Tools": "https://python.langchain.com/docs/how_to/convert_runnable_to_tool/", "How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "How to create a custom Retriever": "https://python.langchain.com/docs/how_to/custom_retriever/", "How to construct knowledge graphs": "https://python.langchain.com/docs/how_to/graph_constructing/", "How to use a time-weighted vector store retriever": "https://python.langchain.com/docs/how_to/time_weighted_vectorstore/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/", "How to load Markdown": "https://python.langchain.com/docs/how_to/document_loader_markdown/", "How to do \"self-querying\" retrieval": "https://python.langchain.com/docs/how_to/self_query/", "How to summarize text in a single LLM call": "https://python.langchain.com/docs/how_to/summarize_stuff/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/", "Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Oracle AI Vector Search: Generate Embeddings": "https://python.langchain.com/docs/integrations/text_embedding/oracleai/", "Kinetica Vectorstore based Retriever": "https://python.langchain.com/docs/integrations/retrievers/kinetica/", "Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/", "ChatGPT plugin": "https://python.langchain.com/docs/integrations/retrievers/chatgpt-plugin/", "Cohere RAG": "https://python.langchain.com/docs/integrations/retrievers/cohere/", "Weaviate Hybrid Search": "https://python.langchain.com/docs/integrations/retrievers/weaviate-hybrid/", "BM25": "https://python.langchain.com/docs/integrations/retrievers/bm25/", "Qdrant Sparse Vector": "https://python.langchain.com/docs/integrations/retrievers/qdrant-sparse/", "ElasticsearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/elasticsearch_retriever/", "TF-IDF": "https://python.langchain.com/docs/integrations/retrievers/tf_idf/", "Milvus": "https://python.langchain.com/docs/integrations/vectorstores/milvus/", "PGVector (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/pgvector_self_query/", "Weaviate": "https://python.langchain.com/docs/integrations/retrievers/self_query/weaviate_self_query/", "Vectara self-querying ": "https://python.langchain.com/docs/integrations/retrievers/self_query/vectara_self_query/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/", "DashVector": "https://python.langchain.com/docs/integrations/retrievers/self_query/dashvector/", "Databricks Vector Search": "https://python.langchain.com/docs/integrations/retrievers/self_query/databricks_vector_search/", "DingoDB": "https://python.langchain.com/docs/integrations/retrievers/self_query/dingo/", "OpenSearch": "https://python.langchain.com/docs/integrations/retrievers/self_query/opensearch_self_query/", "Elasticsearch": "https://python.langchain.com/docs/integrations/vectorstores/elasticsearch/", "Chroma": "https://python.langchain.com/docs/integrations/vectorstores/chroma/", "Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/tencentvectordb/", "Timescale Vector (Postgres) ": "https://python.langchain.com/docs/integrations/retrievers/self_query/timescalevector_self_query/", "Astra DB (Cassandra)": "https://python.langchain.com/docs/integrations/retrievers/self_query/astradb/", "Pinecone": "https://python.langchain.com/docs/integrations/vectorstores/pinecone/", "Supabase (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/supabase_self_query/", "Redis": "https://python.langchain.com/docs/integrations/vectorstores/redis/", "MyScale": "https://python.langchain.com/docs/integrations/retrievers/self_query/myscale_self_query/", "Deep Lake": "https://python.langchain.com/docs/integrations/retrievers/self_query/activeloop_deeplake_self_query/", "MongoDB Atlas": "https://python.langchain.com/docs/integrations/vectorstores/mongodb_atlas/", "Qdrant": "https://python.langchain.com/docs/integrations/vectorstores/qdrant/", "Oracle AI Vector Search: Generate Summary": "https://python.langchain.com/docs/integrations/tools/oracleai/", "Cohere": "https://python.langchain.com/docs/integrations/providers/cohere/", "Identity-enabled RAG using PebbloRetrievalQA": "https://python.langchain.com/docs/integrations/providers/pebblo/pebblo_retrieval_qa/", "Kinetica Vectorstore API": "https://python.langchain.com/docs/integrations/vectorstores/kinetica/", "Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/", "PGVector": "https://python.langchain.com/docs/integrations/vectorstores/pgvector/", "SingleStoreDB": "https://python.langchain.com/docs/integrations/vectorstores/singlestoredb/", "Annoy": "https://python.langchain.com/docs/integrations/vectorstores/annoy/", "Couchbase ": "https://python.langchain.com/docs/integrations/vectorstores/couchbase/", "Oracle AI Vector Search: Vector Store": "https://python.langchain.com/docs/integrations/vectorstores/oracle/", "Neo4j Vector Index": "https://python.langchain.com/docs/integrations/vectorstores/neo4jvector/", "Lantern": "https://python.langchain.com/docs/integrations/vectorstores/lantern/", "Google Firestore (Native Mode)": "https://python.langchain.com/docs/integrations/document_loaders/google_firestore/", "ClickHouse": "https://python.langchain.com/docs/integrations/vectorstores/clickhouse/", "Astra DB Vector Store": "https://python.langchain.com/docs/integrations/vectorstores/astradb/", "Faiss (Async)": "https://python.langchain.com/docs/integrations/vectorstores/faiss_async/", "Apache Cassandra": "https://python.langchain.com/docs/integrations/vectorstores/cassandra/", "PGVecto.rs": "https://python.langchain.com/docs/integrations/vectorstores/pgvecto_rs/", "Postgres Embedding": "https://python.langchain.com/docs/integrations/vectorstores/pgembedding/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "Faiss": "https://python.langchain.com/docs/integrations/vectorstores/faiss/", "Nuclia": "https://python.langchain.com/docs/integrations/document_transformers/nuclia_transformer/", "AI21SemanticTextSplitter": "https://python.langchain.com/docs/integrations/document_transformers/ai21_semantic_text_splitter/", "Google Cloud Vertex AI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/google_cloud_vertexai_rerank/", "OpenAI metadata tagger": "https://python.langchain.com/docs/integrations/document_transformers/openai_metadata_tagger/", "Doctran: extract properties": "https://python.langchain.com/docs/integrations/document_transformers/doctran_extract_properties/", "Google Translate": "https://python.langchain.com/docs/integrations/document_transformers/google_translate/", "Doctran: interrogate documents": "https://python.langchain.com/docs/integrations/document_transformers/doctran_interrogate_document/", "Doctran: language translation": "https://python.langchain.com/docs/integrations/document_transformers/doctran_translate_document/", "TensorFlow Datasets": "https://python.langchain.com/docs/integrations/document_loaders/tensorflow_datasets/", "Google Cloud SQL for MySQL": "https://python.langchain.com/docs/integrations/document_loaders/google_cloud_sql_mysql/", "Airbyte Salesforce (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_salesforce/", "Airbyte CDK (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_cdk/", "Airbyte Stripe (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_stripe/", "Copy Paste": "https://python.langchain.com/docs/integrations/document_loaders/copypaste/", "Airbyte Typeform (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_typeform/", "Apify Dataset": "https://python.langchain.com/docs/integrations/document_loaders/apify_dataset/", "Google Firestore in Datastore Mode": "https://python.langchain.com/docs/integrations/document_loaders/google_datastore/", "Oracle AI Vector Search: Document Processing": "https://python.langchain.com/docs/integrations/document_loaders/oracleai/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/", "Airbyte Hubspot (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_hubspot/", "Airbyte Gong (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_gong/", "Google Memorystore for Redis": "https://python.langchain.com/docs/integrations/document_loaders/google_memorystore_redis/", "Google Bigtable": "https://python.langchain.com/docs/integrations/document_loaders/google_bigtable/", "Google Cloud SQL for SQL server": "https://python.langchain.com/docs/integrations/document_loaders/google_cloud_sql_mssql/", "Google El Carro for Oracle Workloads": "https://python.langchain.com/docs/integrations/document_loaders/google_el_carro/", "Airbyte Shopify (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_shopify/", "Airbyte Zendesk Support (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_zendesk_support/", "Google Spanner": "https://python.langchain.com/docs/integrations/document_loaders/google_spanner/", "PDFMiner": "https://python.langchain.com/docs/integrations/document_loaders/pdfminer/", "Azure Cosmos DB for Apache Gremlin": "https://python.langchain.com/docs/integrations/graphs/azure_cosmosdb_gremlin/", "SageMakerEndpoint": "https://python.langchain.com/docs/integrations/llms/sagemaker/", "self-query-qdrant": "https://python.langchain.com/docs/templates/self-query-qdrant/", "Summarize Text": "https://python.langchain.com/docs/tutorials/summarization/", "Build a Query Analysis System": "https://python.langchain.com/docs/tutorials/query_analysis/", "Vector stores and retrievers": "https://python.langchain.com/docs/tutorials/retrievers/"}, "LLMChain": {"# Example": "https://python.langchain.com/docs/versions/migrating_chains/map_rerank_docs_chain/", "# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_chain/", "# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/", "Clarifai": "https://python.langchain.com/docs/integrations/llms/clarifai/", "RePhraseQuery": "https://python.langchain.com/docs/integrations/retrievers/re_phrase/", "Memorize": "https://python.langchain.com/docs/integrations/tools/memorize/", "Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/", "Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/", "Dall-E Image Generator": "https://python.langchain.com/docs/integrations/tools/dalle_image_generator/", "Motörhead": "https://python.langchain.com/docs/integrations/memory/motorhead_memory/", "Llama2Chat": "https://python.langchain.com/docs/integrations/chat/llama2_chat/", "Context": "https://python.langchain.com/docs/integrations/callbacks/context/", "SageMaker Tracking": "https://python.langchain.com/docs/integrations/callbacks/sagemaker_tracking/", "Argilla": "https://python.langchain.com/docs/integrations/callbacks/argilla/", "Comet": "https://python.langchain.com/docs/integrations/providers/comet_tracking/", "Aim": "https://python.langchain.com/docs/integrations/providers/aim_tracking/", "Weights & Biases": "https://python.langchain.com/docs/integrations/providers/wandb_tracking/", "MLflow Deployments for LLMs": "https://python.langchain.com/docs/integrations/providers/mlflow/", "MLflow AI Gateway": "https://python.langchain.com/docs/integrations/providers/mlflow_ai_gateway/", "Rebuff": "https://python.langchain.com/docs/integrations/providers/rebuff/", "Prediction Guard": "https://python.langchain.com/docs/integrations/llms/predictionguard/", "Flyte": "https://python.langchain.com/docs/integrations/providers/flyte/", "Ray Serve": "https://python.langchain.com/docs/integrations/providers/ray_serve/", "Javelin AI Gateway": "https://python.langchain.com/docs/integrations/providers/javelin_ai_gateway/", "Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/", "Google Cloud Vertex AI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/google_cloud_vertexai_rerank/", "Baseten": "https://python.langchain.com/docs/integrations/llms/baseten/", "StochasticAI": "https://python.langchain.com/docs/integrations/llms/stochasticai/", "Solar": "https://python.langchain.com/docs/integrations/llms/solar/", "Bittensor": "https://python.langchain.com/docs/integrations/llms/bittensor/", "IPEX-LLM": "https://python.langchain.com/docs/integrations/llms/ipex_llm/", "Banana": "https://python.langchain.com/docs/integrations/llms/banana/", "Alibaba Cloud PAI EAS": "https://python.langchain.com/docs/integrations/llms/alibabacloud_pai_eas_endpoint/", "OpenLLM": "https://python.langchain.com/docs/integrations/llms/openllm/", "OctoAI": "https://python.langchain.com/docs/integrations/llms/octoai/", "Writer": "https://python.langchain.com/docs/integrations/llms/writer/", "Modal": "https://python.langchain.com/docs/integrations/llms/modal/", "TextGen": "https://python.langchain.com/docs/integrations/llms/textgen/", "Xorbits Inference (Xinference)": "https://python.langchain.com/docs/integrations/llms/xinference/", "Nebula (Symbl.ai)": "https://python.langchain.com/docs/integrations/llms/symblai_nebula/", "DeepInfra": "https://python.langchain.com/docs/integrations/llms/deepinfra/", "NLP Cloud": "https://python.langchain.com/docs/integrations/llms/nlpcloud/", "ForefrontAI": "https://python.langchain.com/docs/integrations/llms/forefrontai/", "MosaicML": "https://python.langchain.com/docs/integrations/llms/mosaicml/", "CerebriumAI": "https://python.langchain.com/docs/integrations/llms/cerebriumai/", "Predibase": "https://python.langchain.com/docs/integrations/llms/predibase/", "GigaChat": "https://python.langchain.com/docs/integrations/llms/gigachat/", "OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/", "Aphrodite Engine": "https://python.langchain.com/docs/integrations/llms/aphrodite/", "Eden AI": "https://python.langchain.com/docs/integrations/llms/edenai/", "Javelin AI Gateway Tutorial": "https://python.langchain.com/docs/integrations/llms/javelin/", "C Transformers": "https://python.langchain.com/docs/integrations/llms/ctransformers/", "vLLM": "https://python.langchain.com/docs/integrations/llms/vllm/", "Azure ML": "https://python.langchain.com/docs/integrations/llms/azure_ml/", "Minimax": "https://python.langchain.com/docs/integrations/llms/minimax/", "Yuan2.0": "https://python.langchain.com/docs/integrations/llms/yuan2/", "Huggingface Endpoints": "https://python.langchain.com/docs/integrations/llms/huggingface_endpoint/", "Runhouse": "https://python.langchain.com/docs/integrations/llms/runhouse/", "Anyscale": "https://python.langchain.com/docs/integrations/llms/anyscale/", "YandexGPT": "https://python.langchain.com/docs/integrations/llms/yandex/", "GooseAI": "https://python.langchain.com/docs/integrations/llms/gooseai/", "OpenLM": "https://python.langchain.com/docs/integrations/llms/openlm/", "Cloudflare Workers AI": "https://python.langchain.com/docs/integrations/llms/cloudflare_workersai/", "CTranslate2": "https://python.langchain.com/docs/integrations/llms/ctranslate2/", "ChatGLM": "https://python.langchain.com/docs/integrations/llms/chatglm/", "Gradient": "https://python.langchain.com/docs/integrations/llms/gradient/", "Petals": "https://python.langchain.com/docs/integrations/llms/petals/", "Replicate": "https://python.langchain.com/docs/integrations/llms/replicate/", "Summarize Text": "https://python.langchain.com/docs/tutorials/summarization/"}, "StuffDocumentsChain": {"# Example": "https://python.langchain.com/docs/versions/migrating_chains/stuff_docs_chain/", "# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/"}, "create_stuff_documents_chain": {"# Example": "https://python.langchain.com/docs/versions/migrating_chains/stuff_docs_chain/", "Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "How to reorder retrieved results to mitigate the \"lost in the middle\" effect": "https://python.langchain.com/docs/how_to/long_context_reorder/", "How to stream results from your RAG application": "https://python.langchain.com/docs/how_to/qa_streaming/", "How to get your RAG application to return sources": "https://python.langchain.com/docs/how_to/qa_sources/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to summarize text in a single LLM call": "https://python.langchain.com/docs/how_to/summarize_stuff/", "RAGatouille": "https://python.langchain.com/docs/integrations/retrievers/ragatouille/", "ApertureDB": "https://python.langchain.com/docs/integrations/vectorstores/aperturedb/", "Jina Reranker": "https://python.langchain.com/docs/integrations/document_transformers/jina_rerank/", "Image captions": "https://python.langchain.com/docs/integrations/document_loaders/image_captions/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Summarize Text": "https://python.langchain.com/docs/tutorials/summarization/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/", "Build a PDF ingestion and Question/Answering system": "https://python.langchain.com/docs/tutorials/pdf_qa/"}, "LLMMathChain": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_math_chain/"}, "BaseMessage": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_math_chain/", "How to trim messages": "https://python.langchain.com/docs/how_to/trim_messages/", "How to use reference examples when doing extraction": "https://python.langchain.com/docs/how_to/extraction_examples/", "How to propagate callbacks  constructor": "https://python.langchain.com/docs/how_to/callbacks_constructor/", "How to attach callbacks to a runnable": "https://python.langchain.com/docs/how_to/callbacks_attach/", "How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/", "How to pass callbacks in at runtime": "https://python.langchain.com/docs/how_to/callbacks_runtime/", "How to add examples to the prompt for query analysis": "https://python.langchain.com/docs/how_to/query_few_shot/", "WeChat": "https://python.langchain.com/docs/integrations/chat_loaders/wechat/", "Discord": "https://python.langchain.com/docs/integrations/chat_loaders/discord/", "Chat Bot Feedback Template": "https://python.langchain.com/docs/templates/chat-bot-feedback/"}, "RunnableConfig": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/multi_prompt_chain/", "# Example": "https://python.langchain.com/docs/versions/migrating_chains/refine_docs_chain/", "How to access the RunnableConfig from a tool": "https://python.langchain.com/docs/how_to/tool_configure/", "How to summarize text through iterative refinement": "https://python.langchain.com/docs/how_to/summarize_refine/", "How to handle tool errors": "https://python.langchain.com/docs/how_to/tools_error/", "How to stream events from a tool": "https://python.langchain.com/docs/how_to/tool_stream_events/", "How to run custom functions": "https://python.langchain.com/docs/how_to/functions/", "How to add ad-hoc tool calling capability to LLMs and Chat Models": "https://python.langchain.com/docs/how_to/tools_prompting/", "LangChain Expression Language Cheatsheet": "https://python.langchain.com/docs/how_to/lcel_cheatsheet/", "How to dispatch custom callback events": "https://python.langchain.com/docs/how_to/callbacks_custom_events/", "How to pass runtime secrets to runnables": "https://python.langchain.com/docs/how_to/runnable_runtime_secrets/", "Tavily Search": "https://python.langchain.com/docs/integrations/tools/tavily_search/"}, "tool": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_math_chain/", "How to disable parallel tool calling": "https://python.langchain.com/docs/how_to/tool_calling_parallel/", "How to use tools in a chain": "https://python.langchain.com/docs/how_to/tools_chain/", "How to access the RunnableConfig from a tool": "https://python.langchain.com/docs/how_to/tool_configure/", "How to do tool/function calling": "https://python.langchain.com/docs/how_to/function_calling/", "How to pass run time values to tools": "https://python.langchain.com/docs/how_to/tool_runtime/", "How to add a human-in-the-loop for tools": "https://python.langchain.com/docs/how_to/tools_human/", "How to create tools": "https://python.langchain.com/docs/how_to/custom_tools/", "How to pass multimodal data directly to models": "https://python.langchain.com/docs/how_to/multimodal_inputs/", "How to force models to call a tool": "https://python.langchain.com/docs/how_to/tool_choice/", "How to handle tool errors": "https://python.langchain.com/docs/how_to/tools_error/", "How to stream events from a tool": "https://python.langchain.com/docs/how_to/tool_stream_events/", "How to stream runnables": "https://python.langchain.com/docs/how_to/streaming/", "How to pass tool outputs to chat models": "https://python.langchain.com/docs/how_to/tool_results_pass_to_model/", "How to add ad-hoc tool calling capability to LLMs and Chat Models": "https://python.langchain.com/docs/how_to/tools_prompting/", "How to return artifacts from a tool": "https://python.langchain.com/docs/how_to/tool_artifacts/", "How to migrate from legacy LangChain agents to LangGraph": "https://python.langchain.com/docs/how_to/migrate_agent/", "How to stream tool calls": "https://python.langchain.com/docs/how_to/tool_streaming/", "How to pass runtime secrets to runnables": "https://python.langchain.com/docs/how_to/runnable_runtime_secrets/", "How to use few-shot prompting with tool calling": "https://python.langchain.com/docs/how_to/tools_few_shot/", "FinancialDatasets Toolkit": "https://python.langchain.com/docs/integrations/tools/financial_datasets/", "Exa Search": "https://python.langchain.com/docs/integrations/tools/exa_search/", "DeepInfra": "https://python.langchain.com/docs/integrations/chat/deepinfra/", "ChatOllama": "https://python.langchain.com/docs/integrations/chat/ollama/", "Llama.cpp": "https://python.langchain.com/docs/integrations/chat/llamacpp/", "Cohere": "https://python.langchain.com/docs/integrations/providers/cohere/", "Eden AI": "https://python.langchain.com/docs/integrations/chat/edenai/", "ChatTongyi": "https://python.langchain.com/docs/integrations/chat/tongyi/", "ChatPremAI": "https://python.langchain.com/docs/integrations/chat/premai/", "ChatNVIDIA": "https://python.langchain.com/docs/integrations/chat/nvidia_ai_endpoints/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/", "PremAI": "https://python.langchain.com/docs/integrations/providers/premai/", "Log, Trace, and Monitor": "https://python.langchain.com/docs/integrations/providers/portkey/logging_tracing_portkey/", "Portkey": "https://python.langchain.com/docs/integrations/providers/portkey/index/", "JSONFormer": "https://python.langchain.com/docs/integrations/llms/jsonformer_experimental/"}, "MultiPromptChain": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/multi_prompt_chain/"}, "ConversationChain": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/conversation_chain/"}, "ConversationBufferMemory": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/conversation_chain/", "Memorize": "https://python.langchain.com/docs/integrations/tools/memorize/", "Gradio": "https://python.langchain.com/docs/integrations/tools/gradio_tools/", "SceneXplain": "https://python.langchain.com/docs/integrations/tools/sceneXplain/", "Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/", "Xata": "https://python.langchain.com/docs/integrations/memory/xata_chat_message_history/", "Llama2Chat": "https://python.langchain.com/docs/integrations/chat/llama2_chat/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/", "Bittensor": "https://python.langchain.com/docs/integrations/llms/bittensor/"}, "InMemoryChatMessageHistory": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/conversation_chain/", "How to trim messages": "https://python.langchain.com/docs/how_to/trim_messages/", "How to migrate from legacy LangChain agents to LangGraph": "https://python.langchain.com/docs/how_to/migrate_agent/", "ChatNVIDIA": "https://python.langchain.com/docs/integrations/chat/nvidia_ai_endpoints/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/"}, "RunnableWithMessageHistory": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/conversation_chain/", "Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to trim messages": "https://python.langchain.com/docs/how_to/trim_messages/", "How to add tools to chatbots": "https://python.langchain.com/docs/how_to/chatbots_tools/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add message history": "https://python.langchain.com/docs/how_to/message_history/", "How to add memory to chatbots": "https://python.langchain.com/docs/how_to/chatbots_memory/", "How to migrate from legacy LangChain agents to LangGraph": "https://python.langchain.com/docs/how_to/migrate_agent/", "Redis": "https://python.langchain.com/docs/integrations/memory/redis_chat_message_history/", "Google SQL for MySQL": "https://python.langchain.com/docs/integrations/memory/google_sql_mysql/", "Google AlloyDB for PostgreSQL": "https://python.langchain.com/docs/integrations/memory/google_alloydb/", "ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "AWS DynamoDB": "https://python.langchain.com/docs/integrations/memory/aws_dynamodb/", "Couchbase": "https://python.langchain.com/docs/integrations/memory/couchbase_chat_message_history/", "MongoDB": "https://python.langchain.com/docs/integrations/memory/mongodb_chat_message_history/", "SQL (SQLAlchemy)": "https://python.langchain.com/docs/integrations/memory/sql_chat_message_history/", "Streamlit": "https://python.langchain.com/docs/integrations/memory/streamlit_chat_message_history/", "Google El Carro Oracle": "https://python.langchain.com/docs/integrations/memory/google_el_carro/", "SQLite": "https://python.langchain.com/docs/integrations/memory/sqlite/", "Google SQL for PostgreSQL": "https://python.langchain.com/docs/integrations/memory/google_sql_pg/", "Google SQL for SQL Server": "https://python.langchain.com/docs/integrations/memory/google_sql_mssql/", "TiDB": "https://python.langchain.com/docs/integrations/memory/tidb_chat_message_history/", "ChatNVIDIA": "https://python.langchain.com/docs/integrations/chat/nvidia_ai_endpoints/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/"}, "BaseChatMessageHistory": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/conversation_chain/", "Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/"}, "ConstitutionalChain": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/constitutional_chain/"}, "ConstitutionalPrinciple": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/constitutional_chain/"}, "OpenAI": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/constitutional_chain/", "# Example": "https://python.langchain.com/docs/versions/migrating_chains/map_rerank_docs_chain/", "How to use output parsers to parse an LLM response into structured format": "https://python.langchain.com/docs/how_to/output_parser_structured/", "How to reorder retrieved results to mitigate the \"lost in the middle\" effect": "https://python.langchain.com/docs/how_to/long_context_reorder/", "How to add fallbacks to a runnable": "https://python.langchain.com/docs/how_to/fallbacks/", "How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/", "How to retry when a parsing error occurs": "https://python.langchain.com/docs/how_to/output_parser_retry/", "How to stream responses from an LLM": "https://python.langchain.com/docs/how_to/streaming_llm/", "How to cache LLM responses": "https://python.langchain.com/docs/how_to/llm_caching/", "How to track token usage for LLMs": "https://python.langchain.com/docs/how_to/llm_token_usage_tracking/", "Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Milvus": "https://python.langchain.com/docs/integrations/retrievers/self_query/milvus_self_query/", "PGVector (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/pgvector_self_query/", "Weaviate": "https://python.langchain.com/docs/integrations/vectorstores/weaviate/", "Databricks Vector Search": "https://python.langchain.com/docs/integrations/retrievers/self_query/databricks_vector_search/", "DingoDB": "https://python.langchain.com/docs/integrations/retrievers/self_query/dingo/", "OpenSearch": "https://python.langchain.com/docs/integrations/retrievers/self_query/opensearch_self_query/", "Elasticsearch": "https://python.langchain.com/docs/integrations/retrievers/self_query/elasticsearch_self_query/", "Chroma": "https://python.langchain.com/docs/integrations/retrievers/self_query/chroma_self_query/", "Timescale Vector (Postgres) ": "https://python.langchain.com/docs/integrations/retrievers/self_query/timescalevector_self_query/", "Astra DB (Cassandra)": "https://python.langchain.com/docs/integrations/retrievers/self_query/astradb/", "Pinecone": "https://python.langchain.com/docs/integrations/retrievers/self_query/pinecone/", "Supabase (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/supabase_self_query/", "Redis": "https://python.langchain.com/docs/integrations/retrievers/self_query/redis_self_query/", "Deep Lake": "https://python.langchain.com/docs/integrations/retrievers/self_query/activeloop_deeplake_self_query/", "MongoDB Atlas": "https://python.langchain.com/docs/integrations/retrievers/self_query/mongodb_atlas/", "Qdrant": "https://python.langchain.com/docs/integrations/retrievers/self_query/qdrant_self_query/", "OpenAI": "https://python.langchain.com/docs/integrations/llms/openai/", "Jira Toolkit": "https://python.langchain.com/docs/integrations/tools/jira/", "Google Jobs": "https://python.langchain.com/docs/integrations/tools/google_jobs/", "Google Serper": "https://python.langchain.com/docs/integrations/tools/google_serper/", "Azure Cognitive Services Toolkit": "https://python.langchain.com/docs/integrations/tools/azure_cognitive_services/", "Human as a tool": "https://python.langchain.com/docs/integrations/tools/human_tools/", "Natural Language API Toolkits": "https://python.langchain.com/docs/integrations/tools/openapi_nla/", "Steam Toolkit": "https://python.langchain.com/docs/integrations/tools/steam/", "JSON Toolkit": "https://python.langchain.com/docs/integrations/tools/json/", "Google Finance": "https://python.langchain.com/docs/integrations/tools/google_finance/", "ClickUp Toolkit": "https://python.langchain.com/docs/integrations/tools/clickup/", "AWS Lambda": "https://python.langchain.com/docs/integrations/tools/awslambda/", "Google Drive": "https://python.langchain.com/docs/integrations/tools/google_drive/", "OpenWeatherMap": "https://python.langchain.com/docs/integrations/tools/openweathermap/", "Eleven Labs Text2Speech": "https://python.langchain.com/docs/integrations/tools/eleven_labs_tts/", "Office365 Toolkit": "https://python.langchain.com/docs/integrations/tools/office365/", "Pandas Dataframe": "https://python.langchain.com/docs/integrations/tools/pandas/", "Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/", "Lemon Agent": "https://python.langchain.com/docs/integrations/tools/lemonai/", "NASA Toolkit": "https://python.langchain.com/docs/integrations/tools/nasa/", "GraphQL": "https://python.langchain.com/docs/integrations/tools/graphql/", "SearchApi": "https://python.langchain.com/docs/integrations/providers/searchapi/", "Gradio": "https://python.langchain.com/docs/integrations/tools/gradio_tools/", "SceneXplain": "https://python.langchain.com/docs/integrations/tools/sceneXplain/", "Azure AI Services Toolkit": "https://python.langchain.com/docs/integrations/tools/azure_ai_services/", "OpenAPI Toolkit": "https://python.langchain.com/docs/integrations/tools/openapi/", "Dall-E Image Generator": "https://python.langchain.com/docs/integrations/tools/dalle_image_generator/", "Gitlab Toolkit": "https://python.langchain.com/docs/integrations/tools/gitlab/", "Ionic Shopping Tool": "https://python.langchain.com/docs/integrations/tools/ionic_shopping/", "Zep Open Source Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory/", "Motörhead": "https://python.langchain.com/docs/integrations/memory/motorhead_memory/", "Zep Cloud Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory_cloud/", "Confident": "https://python.langchain.com/docs/integrations/callbacks/confident/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/", "Fiddler": "https://python.langchain.com/docs/integrations/callbacks/fiddler/", "SageMaker Tracking": "https://python.langchain.com/docs/integrations/callbacks/sagemaker_tracking/", "Label Studio": "https://python.langchain.com/docs/integrations/callbacks/labelstudio/", "Comet Tracing": "https://python.langchain.com/docs/integrations/callbacks/comet_tracing/", "Argilla": "https://python.langchain.com/docs/integrations/callbacks/argilla/", "PromptLayer": "https://python.langchain.com/docs/integrations/callbacks/promptlayer/", "Streamlit": "https://python.langchain.com/docs/integrations/callbacks/streamlit/", "Trubrics": "https://python.langchain.com/docs/integrations/callbacks/trubrics/", "Infino": "https://python.langchain.com/docs/integrations/callbacks/infino/", "DSPy": "https://python.langchain.com/docs/integrations/providers/dspy/", "Comet": "https://python.langchain.com/docs/integrations/providers/comet_tracking/", "Aim": "https://python.langchain.com/docs/integrations/providers/aim_tracking/", "Weights & Biases": "https://python.langchain.com/docs/integrations/providers/wandb_tracking/", "Log10": "https://python.langchain.com/docs/integrations/providers/log10/", "LangChain Decorators ✨": "https://python.langchain.com/docs/integrations/providers/langchain_decorators/", "Rebuff": "https://python.langchain.com/docs/integrations/providers/rebuff/", "Serper - Google Search API": "https://python.langchain.com/docs/integrations/providers/google_serper/", "Helicone": "https://python.langchain.com/docs/integrations/providers/helicone/", "Shale Protocol": "https://python.langchain.com/docs/integrations/providers/shaleprotocol/", "WhyLabs": "https://python.langchain.com/docs/integrations/providers/whylabs_profiling/", "WandB Tracing": "https://python.langchain.com/docs/integrations/providers/wandb_tracing/", "ClearML": "https://python.langchain.com/docs/integrations/providers/clearml_tracking/", "Ray Serve": "https://python.langchain.com/docs/integrations/providers/ray_serve/", "Identity-enabled RAG using PebbloRetrievalQA": "https://python.langchain.com/docs/integrations/providers/pebblo/pebblo_retrieval_qa/", "Apache Doris": "https://python.langchain.com/docs/integrations/vectorstores/apache_doris/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/", "StarRocks": "https://python.langchain.com/docs/integrations/vectorstores/starrocks/", "Marqo": "https://python.langchain.com/docs/integrations/vectorstores/marqo/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "Amazon Document DB": "https://python.langchain.com/docs/integrations/vectorstores/documentdb/", "VoyageAI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/voyageai-reranker/", "Apify Dataset": "https://python.langchain.com/docs/integrations/document_loaders/apify_dataset/", "Psychic": "https://python.langchain.com/docs/integrations/document_loaders/psychic/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/", "Amazon Textract ": "https://python.langchain.com/docs/integrations/document_loaders/amazon_textract/", "NetworkX": "https://python.langchain.com/docs/integrations/graphs/networkx/", "OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/", "Layerup Security": "https://python.langchain.com/docs/integrations/llms/layerup_security/", "Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/"}, "CRITIQUE_PROMPT": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/constitutional_chain/"}, "REVISION_PROMPT": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/constitutional_chain/"}, "WebBaseLoader": {"Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/", "Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to stream results from your RAG application": "https://python.langchain.com/docs/how_to/qa_streaming/", "How to get your RAG application to return sources": "https://python.langchain.com/docs/how_to/qa_sources/", "How to summarize text through parallelization": "https://python.langchain.com/docs/how_to/summarize_map_reduce/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to use the MultiQueryRetriever": "https://python.langchain.com/docs/how_to/MultiQueryRetriever/", "RePhraseQuery": "https://python.langchain.com/docs/integrations/retrievers/re_phrase/", "Infino": "https://python.langchain.com/docs/integrations/callbacks/infino/", "ApertureDB": "https://python.langchain.com/docs/integrations/vectorstores/aperturedb/", "Zep Cloud": "https://python.langchain.com/docs/integrations/vectorstores/zep_cloud/", "Zep": "https://python.langchain.com/docs/integrations/vectorstores/zep/", "Google Cloud Vertex AI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/google_cloud_vertexai_rerank/", "Merge Documents Loader": "https://python.langchain.com/docs/integrations/document_loaders/merge_doc/", "WebBaseLoader": "https://python.langchain.com/docs/integrations/document_loaders/web_base/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Build a Local RAG Application": "https://python.langchain.com/docs/tutorials/local_rag/", "Summarize Text": "https://python.langchain.com/docs/tutorials/summarization/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/"}, "FAISS": {"Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to handle long text when doing extraction": "https://python.langchain.com/docs/how_to/extraction_long_text/", "How to load PDFs": "https://python.langchain.com/docs/how_to/document_loader_pdf/", "How to better prompt when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_prompting/", "How to add values to a chain's state": "https://python.langchain.com/docs/how_to/assign/", "How to inspect runnables": "https://python.langchain.com/docs/how_to/inspect/", "How to deal with large databases when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_large_db/", "How to stream runnables": "https://python.langchain.com/docs/how_to/streaming/", "How to invoke runnables in parallel": "https://python.langchain.com/docs/how_to/parallel/", "How to pass through arguments from one step to the next": "https://python.langchain.com/docs/how_to/passthrough/", "How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/", "How to use a time-weighted vector store retriever": "https://python.langchain.com/docs/how_to/time_weighted_vectorstore/", "How to create and query vector stores": "https://python.langchain.com/docs/how_to/vectorstores/", "How to use a vectorstore as a retriever": "https://python.langchain.com/docs/how_to/vectorstore_retriever/", "Caching": "https://python.langchain.com/docs/how_to/caching_embeddings/", "How to combine results from multiple retrievers": "https://python.langchain.com/docs/how_to/ensemble_retriever/", "How to select examples by maximal marginal relevance (MMR)": "https://python.langchain.com/docs/how_to/example_selectors_mmr/", "NVIDIA NIMs ": "https://python.langchain.com/docs/integrations/text_embedding/nvidia_ai_endpoints/", "Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/", "LLMLingua Document Compressor": "https://python.langchain.com/docs/integrations/retrievers/llmlingua/", "Cohere reranker": "https://python.langchain.com/docs/integrations/retrievers/cohere-reranker/", "FlashRank reranker": "https://python.langchain.com/docs/integrations/retrievers/flashrank-reranker/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "RAGatouille": "https://python.langchain.com/docs/integrations/providers/ragatouille/", "Facebook - Meta": "https://python.langchain.com/docs/integrations/providers/facebook/", "Faiss (Async)": "https://python.langchain.com/docs/integrations/vectorstores/faiss_async/", "Faiss": "https://python.langchain.com/docs/integrations/vectorstores/faiss/", "VoyageAI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/voyageai-reranker/", "RankLLM Reranker": "https://python.langchain.com/docs/integrations/document_transformers/rankllm-reranker/", "Volcengine Reranker": "https://python.langchain.com/docs/integrations/document_transformers/volcengine_rerank/", "OpenVINO Reranker": "https://python.langchain.com/docs/integrations/document_transformers/openvino_rerank/", "Cross Encoder Reranker": "https://python.langchain.com/docs/integrations/document_transformers/cross_encoder_reranker/", "Jina Reranker": "https://python.langchain.com/docs/integrations/document_transformers/jina_rerank/", "DashScope Reranker": "https://python.langchain.com/docs/integrations/document_transformers/dashscope_rerank/", "YouTube audio": "https://python.langchain.com/docs/integrations/document_loaders/youtube_audio/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/"}, "OpenAIEmbeddings": {"Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to handle long text when doing extraction": "https://python.langchain.com/docs/how_to/extraction_long_text/", "How to load PDFs": "https://python.langchain.com/docs/how_to/document_loader_pdf/", "How to better prompt when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_prompting/", "How to add values to a chain's state": "https://python.langchain.com/docs/how_to/assign/", "How to route between sub-chains": "https://python.langchain.com/docs/how_to/routing/", "How to do per-user retrieval": "https://python.langchain.com/docs/how_to/qa_per_user/", "How to use few shot examples": "https://python.langchain.com/docs/how_to/few_shot_examples/", "How to inspect runnables": "https://python.langchain.com/docs/how_to/inspect/", "How to handle cases where no queries are generated": "https://python.langchain.com/docs/how_to/query_no_queries/", "How to use few shot examples in chat models": "https://python.langchain.com/docs/how_to/few_shot_examples_chat/", "How to select examples by similarity": "https://python.langchain.com/docs/how_to/example_selectors_similarity/", "Text embedding models": "https://python.langchain.com/docs/how_to/embed_text/", "How to deal with large databases when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_large_db/", "How to handle multiple queries when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_queries/", "How to stream results from your RAG application": "https://python.langchain.com/docs/how_to/qa_streaming/", "How to get your RAG application to return sources": "https://python.langchain.com/docs/how_to/qa_sources/", "How to use the LangChain indexing API": "https://python.langchain.com/docs/how_to/indexing/", "How to split text based on semantic similarity": "https://python.langchain.com/docs/how_to/semantic-chunker/", "How to convert Runnables as Tools": "https://python.langchain.com/docs/how_to/convert_runnable_to_tool/", "How to stream runnables": "https://python.langchain.com/docs/how_to/streaming/", "How to invoke runnables in parallel": "https://python.langchain.com/docs/how_to/parallel/", "How to pass through arguments from one step to the next": "https://python.langchain.com/docs/how_to/passthrough/", "How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/", "How to handle multiple retrievers when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_retrievers/", "How to use a time-weighted vector store retriever": "https://python.langchain.com/docs/how_to/time_weighted_vectorstore/", "How to create and query vector stores": "https://python.langchain.com/docs/how_to/vectorstores/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/", "How to use the MultiQueryRetriever": "https://python.langchain.com/docs/how_to/MultiQueryRetriever/", "How to best prompt for Graph-RAG": "https://python.langchain.com/docs/how_to/graph_prompting/", "How to use the Parent Document Retriever": "https://python.langchain.com/docs/how_to/parent_document_retriever/", "How deal with high cardinality categoricals when doing query analysis": "https://python.langchain.com/docs/how_to/query_high_cardinality/", "How to use a vectorstore as a retriever": "https://python.langchain.com/docs/how_to/vectorstore_retriever/", "Caching": "https://python.langchain.com/docs/how_to/caching_embeddings/", "How to combine results from multiple retrievers": "https://python.langchain.com/docs/how_to/ensemble_retriever/", "How to select examples by maximal marginal relevance (MMR)": "https://python.langchain.com/docs/how_to/example_selectors_mmr/", "How to do \"self-querying\" retrieval": "https://python.langchain.com/docs/how_to/self_query/", "Hybrid Search": "https://python.langchain.com/docs/how_to/hybrid/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/", "Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "OpenAIEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/openai/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "RePhraseQuery": "https://python.langchain.com/docs/integrations/retrievers/re_phrase/", "Kinetica Vectorstore based Retriever": "https://python.langchain.com/docs/integrations/retrievers/kinetica/", "JaguarDB Vector Database": "https://python.langchain.com/docs/integrations/retrievers/jaguar/", "Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/", "LLMLingua Document Compressor": "https://python.langchain.com/docs/integrations/retrievers/llmlingua/", "SingleStoreDB": "https://python.langchain.com/docs/integrations/vectorstores/singlestoredb/", "kNN": "https://python.langchain.com/docs/integrations/retrievers/knn/", "DocArray": "https://python.langchain.com/docs/integrations/retrievers/docarray_retriever/", "SVM": "https://python.langchain.com/docs/integrations/retrievers/svm/", "Pinecone Hybrid Search": "https://python.langchain.com/docs/integrations/retrievers/pinecone_hybrid_search/", "Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "Milvus Hybrid Search Retriever": "https://python.langchain.com/docs/integrations/retrievers/milvus_hybrid_search/", "FlashRank reranker": "https://python.langchain.com/docs/integrations/retrievers/flashrank-reranker/", "LOTR (Merger Retriever)": "https://python.langchain.com/docs/integrations/retrievers/merger_retriever/", "Milvus": "https://python.langchain.com/docs/integrations/retrievers/self_query/milvus_self_query/", "PGVector (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/pgvector_self_query/", "Weaviate": "https://python.langchain.com/docs/integrations/vectorstores/weaviate/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/", "Databricks Vector Search": "https://python.langchain.com/docs/integrations/vectorstores/databricks_vector_search/", "DingoDB": "https://python.langchain.com/docs/integrations/vectorstores/dingo/", "OpenSearch": "https://python.langchain.com/docs/integrations/vectorstores/opensearch/", "Elasticsearch": "https://python.langchain.com/docs/integrations/retrievers/self_query/elasticsearch_self_query/", "Chroma": "https://python.langchain.com/docs/integrations/retrievers/self_query/chroma_self_query/", "Timescale Vector (Postgres) ": "https://python.langchain.com/docs/integrations/retrievers/self_query/timescalevector_self_query/", "Astra DB (Cassandra)": "https://python.langchain.com/docs/integrations/retrievers/self_query/astradb/", "Pinecone": "https://python.langchain.com/docs/integrations/retrievers/self_query/pinecone/", "Supabase (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/supabase/", "Redis": "https://python.langchain.com/docs/integrations/retrievers/self_query/redis_self_query/", "MyScale": "https://python.langchain.com/docs/integrations/vectorstores/myscale/", "Deep Lake": "https://python.langchain.com/docs/integrations/retrievers/self_query/activeloop_deeplake_self_query/", "MongoDB Atlas": "https://python.langchain.com/docs/integrations/retrievers/self_query/mongodb_atlas/", "Qdrant": "https://python.langchain.com/docs/integrations/retrievers/self_query/qdrant_self_query/", "OpenAI": "https://python.langchain.com/docs/integrations/providers/openai/", "Xata": "https://python.langchain.com/docs/integrations/vectorstores/xata/", "Confident": "https://python.langchain.com/docs/integrations/callbacks/confident/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "RAGatouille": "https://python.langchain.com/docs/integrations/providers/ragatouille/", "Upstash Vector": "https://python.langchain.com/docs/integrations/vectorstores/upstash/", "Javelin AI Gateway": "https://python.langchain.com/docs/integrations/providers/javelin_ai_gateway/", "Identity-enabled RAG using PebbloRetrievalQA": "https://python.langchain.com/docs/integrations/providers/pebblo/pebblo_retrieval_qa/", "LanceDB": "https://python.langchain.com/docs/integrations/vectorstores/lancedb/", "Apache Doris": "https://python.langchain.com/docs/integrations/vectorstores/apache_doris/", "Kinetica Vectorstore API": "https://python.langchain.com/docs/integrations/vectorstores/kinetica/", "Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/", "Hippo": "https://python.langchain.com/docs/integrations/vectorstores/hippo/", "Rockset": "https://python.langchain.com/docs/integrations/vectorstores/rockset/", "Zilliz": "https://python.langchain.com/docs/integrations/vectorstores/zilliz/", "Azure Cosmos DB Mongo vCore": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db/", "viking DB": "https://python.langchain.com/docs/integrations/vectorstores/vikingdb/", "Typesense": "https://python.langchain.com/docs/integrations/vectorstores/typesense/", "Momento Vector Index (MVI)": "https://python.langchain.com/docs/integrations/vectorstores/momento_vector_index/", "TiDB Vector": "https://python.langchain.com/docs/integrations/vectorstores/tidb_vector/", "Activeloop Deep Lake": "https://python.langchain.com/docs/integrations/vectorstores/activeloop_deeplake/", "Neo4j Vector Index": "https://python.langchain.com/docs/integrations/vectorstores/neo4jvector/", "Lantern": "https://python.langchain.com/docs/integrations/vectorstores/lantern/", "DuckDB": "https://python.langchain.com/docs/integrations/vectorstores/duckdb/", "Alibaba Cloud OpenSearch": "https://python.langchain.com/docs/integrations/vectorstores/alibabacloud_opensearch/", "StarRocks": "https://python.langchain.com/docs/integrations/vectorstores/starrocks/", "scikit-learn": "https://python.langchain.com/docs/integrations/vectorstores/sklearn/", "Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/tencentvectordb/", "DocArray HnswSearch": "https://python.langchain.com/docs/integrations/vectorstores/docarray_hnsw/", "Tigris": "https://python.langchain.com/docs/integrations/vectorstores/tigris/", "China Mobile ECloud ElasticSearch VectorSearch": "https://python.langchain.com/docs/integrations/vectorstores/ecloud_vector_search/", "Faiss (Async)": "https://python.langchain.com/docs/integrations/vectorstores/faiss_async/", "Azure AI Search": "https://python.langchain.com/docs/integrations/vectorstores/azuresearch/", "Apache Cassandra": "https://python.langchain.com/docs/integrations/vectorstores/cassandra/", "USearch": "https://python.langchain.com/docs/integrations/vectorstores/usearch/", "KDB.AI": "https://python.langchain.com/docs/integrations/vectorstores/kdbai/", "DocArray InMemorySearch": "https://python.langchain.com/docs/integrations/vectorstores/docarray_in_memory/", "Postgres Embedding": "https://python.langchain.com/docs/integrations/vectorstores/pgembedding/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "Epsilla": "https://python.langchain.com/docs/integrations/vectorstores/epsilla/", "Amazon Document DB": "https://python.langchain.com/docs/integrations/vectorstores/documentdb/", "AnalyticDB": "https://python.langchain.com/docs/integrations/vectorstores/analyticdb/", "Hologres": "https://python.langchain.com/docs/integrations/vectorstores/hologres/", "Meilisearch": "https://python.langchain.com/docs/integrations/vectorstores/meilisearch/", "RankLLM Reranker": "https://python.langchain.com/docs/integrations/document_transformers/rankllm-reranker/", "YouTube audio": "https://python.langchain.com/docs/integrations/document_loaders/youtube_audio/", "Image captions": "https://python.langchain.com/docs/integrations/document_loaders/image_captions/", "Apify Dataset": "https://python.langchain.com/docs/integrations/document_loaders/apify_dataset/", "Psychic": "https://python.langchain.com/docs/integrations/document_loaders/psychic/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/", "Build a Query Analysis System": "https://python.langchain.com/docs/tutorials/query_analysis/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/", "Build a PDF ingestion and Question/Answering system": "https://python.langchain.com/docs/tutorials/pdf_qa/", "Vector stores and retrievers": "https://python.langchain.com/docs/tutorials/retrievers/"}, "RecursiveCharacterTextSplitter": {"Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to split code": "https://python.langchain.com/docs/how_to/code_splitter/", "How to handle cases where no queries are generated": "https://python.langchain.com/docs/how_to/query_no_queries/", "How to recursively split text by characters": "https://python.langchain.com/docs/how_to/recursive_text_splitter/", "How to handle multiple queries when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_queries/", "How to stream results from your RAG application": "https://python.langchain.com/docs/how_to/qa_streaming/", "How to get your RAG application to return sources": "https://python.langchain.com/docs/how_to/qa_sources/", "How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to handle multiple retrievers when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_retrievers/", "How to split text by tokens ": "https://python.langchain.com/docs/how_to/split_by_token/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/", "How to use the MultiQueryRetriever": "https://python.langchain.com/docs/how_to/MultiQueryRetriever/", "How to use the Parent Document Retriever": "https://python.langchain.com/docs/how_to/parent_document_retriever/", "How to split Markdown by Headers": "https://python.langchain.com/docs/how_to/markdown_header_metadata_splitter/", "How to split by HTML header ": "https://python.langchain.com/docs/how_to/HTML_header_metadata_splitter/", "How to split by HTML sections": "https://python.langchain.com/docs/how_to/HTML_section_aware_splitter/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/", "RePhraseQuery": "https://python.langchain.com/docs/integrations/retrievers/re_phrase/", "LLMLingua Document Compressor": "https://python.langchain.com/docs/integrations/retrievers/llmlingua/", "Cohere reranker": "https://python.langchain.com/docs/integrations/retrievers/cohere-reranker/", "Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "FlashRank reranker": "https://python.langchain.com/docs/integrations/retrievers/flashrank-reranker/", "Maritalk": "https://python.langchain.com/docs/integrations/chat/maritalk/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "RAGatouille": "https://python.langchain.com/docs/integrations/providers/ragatouille/", "Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/", "Google Vertex AI Vector Search": "https://python.langchain.com/docs/integrations/vectorstores/google_vertex_ai_vector_search/", "viking DB": "https://python.langchain.com/docs/integrations/vectorstores/vikingdb/", "ApertureDB": "https://python.langchain.com/docs/integrations/vectorstores/aperturedb/", "Azure Cosmos DB No SQL": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db_no_sql/", "Zep Cloud": "https://python.langchain.com/docs/integrations/vectorstores/zep_cloud/", "Apache Cassandra": "https://python.langchain.com/docs/integrations/vectorstores/cassandra/", "Zep": "https://python.langchain.com/docs/integrations/vectorstores/zep/", "Vearch": "https://python.langchain.com/docs/integrations/vectorstores/vearch/", "VoyageAI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/voyageai-reranker/", "RankLLM Reranker": "https://python.langchain.com/docs/integrations/document_transformers/rankllm-reranker/", "Volcengine Reranker": "https://python.langchain.com/docs/integrations/document_transformers/volcengine_rerank/", "OpenVINO Reranker": "https://python.langchain.com/docs/integrations/document_transformers/openvino_rerank/", "Google Cloud Vertex AI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/google_cloud_vertexai_rerank/", "Cross Encoder Reranker": "https://python.langchain.com/docs/integrations/document_transformers/cross_encoder_reranker/", "Jina Reranker": "https://python.langchain.com/docs/integrations/document_transformers/jina_rerank/", "DashScope Reranker": "https://python.langchain.com/docs/integrations/document_transformers/dashscope_rerank/", "YouTube audio": "https://python.langchain.com/docs/integrations/document_loaders/youtube_audio/", "Image captions": "https://python.langchain.com/docs/integrations/document_loaders/image_captions/", "Source Code": "https://python.langchain.com/docs/integrations/document_loaders/source_code/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Build a Local RAG Application": "https://python.langchain.com/docs/tutorials/local_rag/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/", "Build a Query Analysis System": "https://python.langchain.com/docs/tutorials/query_analysis/", "Build a PDF ingestion and Question/Answering system": "https://python.langchain.com/docs/tutorials/pdf_qa/"}, "ConversationalRetrievalChain": {"Load docs": "https://python.langchain.com/docs/versions/migrating_chains/conversation_retrieval_chain/", "Outline": "https://python.langchain.com/docs/integrations/retrievers/outline/", "SEC filing": "https://python.langchain.com/docs/integrations/retrievers/sec_filings/", "Rememberizer": "https://python.langchain.com/docs/integrations/retrievers/rememberizer/", "Kay.ai": "https://python.langchain.com/docs/integrations/retrievers/kay/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/"}, "create_history_aware_retriever": {"Load docs": "https://python.langchain.com/docs/versions/migrating_chains/conversation_retrieval_chain/", "How to stream results from your RAG application": "https://python.langchain.com/docs/how_to/qa_streaming/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/"}, "create_retrieval_chain": {"Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "How to stream results from your RAG application": "https://python.langchain.com/docs/how_to/qa_streaming/", "How to get your RAG application to return sources": "https://python.langchain.com/docs/how_to/qa_sources/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "RAGatouille": "https://python.langchain.com/docs/integrations/retrievers/ragatouille/", "ApertureDB": "https://python.langchain.com/docs/integrations/vectorstores/aperturedb/", "Jina Reranker": "https://python.langchain.com/docs/integrations/document_transformers/jina_rerank/", "Image captions": "https://python.langchain.com/docs/integrations/document_loaders/image_captions/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/", "Build a PDF ingestion and Question/Answering system": "https://python.langchain.com/docs/tutorials/pdf_qa/"}, "MapReduceDocumentsChain": {"# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/"}, "ReduceDocumentsChain": {"# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/"}, "CharacterTextSplitter": {"# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/", "How to handle long text when doing extraction": "https://python.langchain.com/docs/how_to/extraction_long_text/", "How to split by character": "https://python.langchain.com/docs/how_to/character_text_splitter/", "How to summarize text through parallelization": "https://python.langchain.com/docs/how_to/summarize_map_reduce/", "How to use the LangChain indexing API": "https://python.langchain.com/docs/how_to/indexing/", "How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/", "How to create and query vector stores": "https://python.langchain.com/docs/how_to/vectorstores/", "How to split text by tokens ": "https://python.langchain.com/docs/how_to/split_by_token/", "How to use a vectorstore as a retriever": "https://python.langchain.com/docs/how_to/vectorstore_retriever/", "Caching": "https://python.langchain.com/docs/how_to/caching_embeddings/", "Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "Kinetica Vectorstore based Retriever": "https://python.langchain.com/docs/integrations/retrievers/kinetica/", "JaguarDB Vector Database": "https://python.langchain.com/docs/integrations/retrievers/jaguar/", "SingleStoreDB": "https://python.langchain.com/docs/integrations/retrievers/singlestoredb/", "OpenAI": "https://python.langchain.com/docs/integrations/providers/openai/", "Confident": "https://python.langchain.com/docs/integrations/callbacks/confident/", "Upstash Vector": "https://python.langchain.com/docs/integrations/vectorstores/upstash/", "VDMS": "https://python.langchain.com/docs/integrations/providers/vdms/", "LanceDB": "https://python.langchain.com/docs/integrations/vectorstores/lancedb/", "Kinetica Vectorstore API": "https://python.langchain.com/docs/integrations/vectorstores/kinetica/", "SQLite-VSS": "https://python.langchain.com/docs/integrations/vectorstores/sqlitevss/", "Vald": "https://python.langchain.com/docs/integrations/vectorstores/vald/", "Weaviate": "https://python.langchain.com/docs/integrations/vectorstores/weaviate/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/", "DashVector": "https://python.langchain.com/docs/integrations/vectorstores/dashvector/", "Databricks Vector Search": "https://python.langchain.com/docs/integrations/vectorstores/databricks_vector_search/", "ScaNN": "https://python.langchain.com/docs/integrations/vectorstores/scann/", "Xata": "https://python.langchain.com/docs/integrations/vectorstores/xata/", "Hippo": "https://python.langchain.com/docs/integrations/vectorstores/hippo/", "Vespa": "https://python.langchain.com/docs/integrations/vectorstores/vespa/", "Rockset": "https://python.langchain.com/docs/integrations/vectorstores/rockset/", "DingoDB": "https://python.langchain.com/docs/integrations/vectorstores/dingo/", "Zilliz": "https://python.langchain.com/docs/integrations/vectorstores/zilliz/", "Azure Cosmos DB Mongo vCore": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db/", "Annoy": "https://python.langchain.com/docs/integrations/vectorstores/annoy/", "Couchbase ": "https://python.langchain.com/docs/integrations/vectorstores/couchbase/", "Typesense": "https://python.langchain.com/docs/integrations/vectorstores/typesense/", "Momento Vector Index (MVI)": "https://python.langchain.com/docs/integrations/vectorstores/momento_vector_index/", "TiDB Vector": "https://python.langchain.com/docs/integrations/vectorstores/tidb_vector/", "Relyt": "https://python.langchain.com/docs/integrations/vectorstores/relyt/", "Activeloop Deep Lake": "https://python.langchain.com/docs/integrations/vectorstores/activeloop_deeplake/", "vlite": "https://python.langchain.com/docs/integrations/vectorstores/vlite/", "Neo4j Vector Index": "https://python.langchain.com/docs/integrations/vectorstores/neo4jvector/", "Lantern": "https://python.langchain.com/docs/integrations/vectorstores/lantern/", "Tair": "https://python.langchain.com/docs/integrations/vectorstores/tair/", "DuckDB": "https://python.langchain.com/docs/integrations/vectorstores/duckdb/", "Alibaba Cloud OpenSearch": "https://python.langchain.com/docs/integrations/vectorstores/alibabacloud_opensearch/", "Clarifai": "https://python.langchain.com/docs/integrations/vectorstores/clarifai/", "scikit-learn": "https://python.langchain.com/docs/integrations/vectorstores/sklearn/", "Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/tencentvectordb/", "DocArray HnswSearch": "https://python.langchain.com/docs/integrations/vectorstores/docarray_hnsw/", "MyScale": "https://python.langchain.com/docs/integrations/vectorstores/myscale/", "TileDB": "https://python.langchain.com/docs/integrations/vectorstores/tiledb/", "Google Memorystore for Redis": "https://python.langchain.com/docs/integrations/vectorstores/google_memorystore_redis/", "Tigris": "https://python.langchain.com/docs/integrations/vectorstores/tigris/", "China Mobile ECloud ElasticSearch VectorSearch": "https://python.langchain.com/docs/integrations/vectorstores/ecloud_vector_search/", "Bagel": "https://python.langchain.com/docs/integrations/vectorstores/bagel/", "Baidu Cloud ElasticSearch VectorSearch": "https://python.langchain.com/docs/integrations/vectorstores/baiducloud_vector_search/", "AwaDB": "https://python.langchain.com/docs/integrations/vectorstores/awadb/", "Supabase (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/supabase/", "SurrealDB": "https://python.langchain.com/docs/integrations/vectorstores/surrealdb/", "OpenSearch": "https://python.langchain.com/docs/integrations/vectorstores/opensearch/", "Faiss (Async)": "https://python.langchain.com/docs/integrations/vectorstores/faiss_async/", "BagelDB": "https://python.langchain.com/docs/integrations/vectorstores/bageldb/", "ManticoreSearch VectorStore": "https://python.langchain.com/docs/integrations/vectorstores/manticore_search/", "Azure AI Search": "https://python.langchain.com/docs/integrations/vectorstores/azuresearch/", "USearch": "https://python.langchain.com/docs/integrations/vectorstores/usearch/", "PGVecto.rs": "https://python.langchain.com/docs/integrations/vectorstores/pgvecto_rs/", "Marqo": "https://python.langchain.com/docs/integrations/vectorstores/marqo/", "DocArray InMemorySearch": "https://python.langchain.com/docs/integrations/vectorstores/docarray_in_memory/", "Postgres Embedding": "https://python.langchain.com/docs/integrations/vectorstores/pgembedding/", "Intel's Visual Data Management System (VDMS)": "https://python.langchain.com/docs/integrations/vectorstores/vdms/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "Epsilla": "https://python.langchain.com/docs/integrations/vectorstores/epsilla/", "Amazon Document DB": "https://python.langchain.com/docs/integrations/vectorstores/documentdb/", "SemaDB": "https://python.langchain.com/docs/integrations/vectorstores/semadb/", "AnalyticDB": "https://python.langchain.com/docs/integrations/vectorstores/analyticdb/", "Hologres": "https://python.langchain.com/docs/integrations/vectorstores/hologres/", "Baidu VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/baiduvectordb/", "Meilisearch": "https://python.langchain.com/docs/integrations/vectorstores/meilisearch/", "Psychic": "https://python.langchain.com/docs/integrations/document_loaders/psychic/", "Manifest": "https://python.langchain.com/docs/integrations/llms/manifest/", "Summarize Text": "https://python.langchain.com/docs/tutorials/summarization/"}, "acollapse_docs": {"# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/", "How to summarize text through parallelization": "https://python.langchain.com/docs/how_to/summarize_map_reduce/", "Summarize Text": "https://python.langchain.com/docs/tutorials/summarization/"}, "split_list_of_docs": {"# Basic example (short documents)": "https://python.langchain.com/docs/versions/migrating_chains/map_reduce_chain/", "How to summarize text through parallelization": "https://python.langchain.com/docs/how_to/summarize_map_reduce/", "Summarize Text": "https://python.langchain.com/docs/tutorials/summarization/"}, "RefineDocumentsChain": {"# Example": "https://python.langchain.com/docs/versions/migrating_chains/refine_docs_chain/"}, "RetrievalQA": {"Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "LLMLingua Document Compressor": "https://python.langchain.com/docs/integrations/retrievers/llmlingua/", "Bedrock (Knowledge Bases) Retriever": "https://python.langchain.com/docs/integrations/retrievers/bedrock/", "Cohere reranker": "https://python.langchain.com/docs/integrations/retrievers/cohere-reranker/", "Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "FlashRank reranker": "https://python.langchain.com/docs/integrations/retrievers/flashrank-reranker/", "Confident": "https://python.langchain.com/docs/integrations/callbacks/confident/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "Apache Doris": "https://python.langchain.com/docs/integrations/vectorstores/apache_doris/", "ScaNN": "https://python.langchain.com/docs/integrations/vectorstores/scann/", "Google Vertex AI Vector Search": "https://python.langchain.com/docs/integrations/vectorstores/google_vertex_ai_vector_search/", "Momento Vector Index (MVI)": "https://python.langchain.com/docs/integrations/vectorstores/momento_vector_index/", "Activeloop Deep Lake": "https://python.langchain.com/docs/integrations/vectorstores/activeloop_deeplake/", "StarRocks": "https://python.langchain.com/docs/integrations/vectorstores/starrocks/", "KDB.AI": "https://python.langchain.com/docs/integrations/vectorstores/kdbai/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "Amazon Document DB": "https://python.langchain.com/docs/integrations/vectorstores/documentdb/", "VoyageAI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/voyageai-reranker/", "RankLLM Reranker": "https://python.langchain.com/docs/integrations/document_transformers/rankllm-reranker/", "YouTube audio": "https://python.langchain.com/docs/integrations/document_loaders/youtube_audio/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/"}, "RunnablePassthrough": {"Load docs": "https://python.langchain.com/docs/versions/migrating_chains/retrieval_qa/", "# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_router_chain/", "How to add values to a chain's state": "https://python.langchain.com/docs/how_to/assign/", "How to route between sub-chains": "https://python.langchain.com/docs/how_to/routing/", "How to do per-user retrieval": "https://python.langchain.com/docs/how_to/qa_per_user/", "How to inspect runnables": "https://python.langchain.com/docs/how_to/inspect/", "How to handle cases where no queries are generated": "https://python.langchain.com/docs/how_to/query_no_queries/", "How to do tool/function calling": "https://python.langchain.com/docs/how_to/function_calling/", "How to add a human-in-the-loop for tools": "https://python.langchain.com/docs/how_to/tools_human/", "How to deal with large databases when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_large_db/", "How to handle multiple queries when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_queries/", "How to map values to a graph database": "https://python.langchain.com/docs/how_to/graph_mapping/", "How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/", "How to get your RAG application to return sources": "https://python.langchain.com/docs/how_to/qa_sources/", "How to add default invocation args to a Runnable": "https://python.langchain.com/docs/how_to/binding/", "How to convert Runnables as Tools": "https://python.langchain.com/docs/how_to/convert_runnable_to_tool/", "How to create a dynamic (self-constructing) chain": "https://python.langchain.com/docs/how_to/dynamic_chain/", "How to stream runnables": "https://python.langchain.com/docs/how_to/streaming/", "How to invoke runnables in parallel": "https://python.langchain.com/docs/how_to/parallel/", "How to pass through arguments from one step to the next": "https://python.langchain.com/docs/how_to/passthrough/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to handle multiple retrievers when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_retrievers/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/", "How to add memory to chatbots": "https://python.langchain.com/docs/how_to/chatbots_memory/", "How deal with high cardinality categoricals when doing query analysis": "https://python.langchain.com/docs/how_to/query_high_cardinality/", "How to add ad-hoc tool calling capability to LLMs and Chat Models": "https://python.langchain.com/docs/how_to/tools_prompting/", "LangChain Expression Language Cheatsheet": "https://python.langchain.com/docs/how_to/lcel_cheatsheet/", "Hybrid Search": "https://python.langchain.com/docs/how_to/hybrid/", "How to use few-shot prompting with tool calling": "https://python.langchain.com/docs/how_to/tools_few_shot/", "How to add examples to the prompt for query analysis": "https://python.langchain.com/docs/how_to/query_few_shot/", "NVIDIA NIMs ": "https://python.langchain.com/docs/integrations/text_embedding/nvidia_ai_endpoints/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "You.com": "https://python.langchain.com/docs/integrations/retrievers/you-retriever/", "Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/", "AskNews": "https://python.langchain.com/docs/integrations/retrievers/asknews/", "WikipediaRetriever": "https://python.langchain.com/docs/integrations/retrievers/wikipedia/", "TavilySearchAPIRetriever": "https://python.langchain.com/docs/integrations/retrievers/tavily/", "ArxivRetriever": "https://python.langchain.com/docs/integrations/retrievers/arxiv/", "ElasticsearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/elasticsearch_retriever/", "Milvus Hybrid Search Retriever": "https://python.langchain.com/docs/integrations/retrievers/milvus_hybrid_search/", "Google Vertex AI Search": "https://python.langchain.com/docs/integrations/retrievers/google_vertex_ai_search/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "DSPy": "https://python.langchain.com/docs/integrations/providers/dspy/", "Weaviate": "https://python.langchain.com/docs/integrations/vectorstores/weaviate/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/", "Apache Cassandra": "https://python.langchain.com/docs/integrations/vectorstores/cassandra/", "Google Cloud Vertex AI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/google_cloud_vertexai_rerank/", "OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Build a Local RAG Application": "https://python.langchain.com/docs/tutorials/local_rag/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/", "Build a Query Analysis System": "https://python.langchain.com/docs/tutorials/query_analysis/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/", "Vector stores and retrievers": "https://python.langchain.com/docs/tutorials/retrievers/"}, "LLMRouterChain": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_router_chain/"}, "RouterOutputParser": {"# Legacy": "https://python.langchain.com/docs/versions/migrating_chains/llm_router_chain/"}, "MapRerankDocumentsChain": {"# Example": "https://python.langchain.com/docs/versions/migrating_chains/map_rerank_docs_chain/"}, "RegexParser": {"# Example": "https://python.langchain.com/docs/versions/migrating_chains/map_rerank_docs_chain/"}, "TavilySearchResults": {"Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to add tools to chatbots": "https://python.langchain.com/docs/how_to/chatbots_tools/", "How to debug your LLM apps": "https://python.langchain.com/docs/how_to/debugging/", "Tavily Search": "https://python.langchain.com/docs/integrations/tools/tavily_search/", "ZHIPU AI": "https://python.langchain.com/docs/integrations/chat/zhipuai/", "Cohere": "https://python.langchain.com/docs/integrations/providers/cohere/", "Build an Agent": "https://python.langchain.com/docs/tutorials/agents/"}, "create_retriever_tool": {"Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "Xata": "https://python.langchain.com/docs/integrations/memory/xata_chat_message_history/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/"}, "create_tool_calling_agent": {"Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to use tools in a chain": "https://python.langchain.com/docs/how_to/tools_chain/", "How to track token usage in ChatModels": "https://python.langchain.com/docs/how_to/chat_token_usage_tracking/", "How to add tools to chatbots": "https://python.langchain.com/docs/how_to/chatbots_tools/", "How to debug your LLM apps": "https://python.langchain.com/docs/how_to/debugging/", "How to migrate from legacy LangChain agents to LangGraph": "https://python.langchain.com/docs/how_to/migrate_agent/", "Azure Container Apps dynamic sessions": "https://python.langchain.com/docs/integrations/tools/azure_dynamic_sessions/", "FinancialDatasets Toolkit": "https://python.langchain.com/docs/integrations/tools/financial_datasets/", "Databricks Unity Catalog (UC)": "https://python.langchain.com/docs/integrations/tools/databricks/", "Riza Code Interpreter": "https://python.langchain.com/docs/integrations/tools/riza/", "Bing Search": "https://python.langchain.com/docs/integrations/tools/bing_search/"}, "AgentExecutor": {"Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to use tools in a chain": "https://python.langchain.com/docs/how_to/tools_chain/", "How to track token usage in ChatModels": "https://python.langchain.com/docs/how_to/chat_token_usage_tracking/", "How to add tools to chatbots": "https://python.langchain.com/docs/how_to/chatbots_tools/", "How to debug your LLM apps": "https://python.langchain.com/docs/how_to/debugging/", "How to migrate from legacy LangChain agents to LangGraph": "https://python.langchain.com/docs/how_to/migrate_agent/", "Infobip": "https://python.langchain.com/docs/integrations/tools/infobip/", "AskNews": "https://python.langchain.com/docs/integrations/tools/asknews/", "Azure Container Apps dynamic sessions": "https://python.langchain.com/docs/integrations/tools/azure_dynamic_sessions/", "FinancialDatasets Toolkit": "https://python.langchain.com/docs/integrations/tools/financial_datasets/", "Cassandra Database Toolkit": "https://python.langchain.com/docs/integrations/tools/cassandra_database/", "Polygon IO Toolkit": "https://python.langchain.com/docs/integrations/tools/polygon_toolkit/", "Semantic Scholar API Tool": "https://python.langchain.com/docs/integrations/tools/semanticscholar/", "Databricks Unity Catalog (UC)": "https://python.langchain.com/docs/integrations/tools/databricks/", "Passio NutritionAI": "https://python.langchain.com/docs/integrations/tools/passio_nutrition_ai/", "Memorize": "https://python.langchain.com/docs/integrations/tools/memorize/", "Riza Code Interpreter": "https://python.langchain.com/docs/integrations/tools/riza/", "ArXiv": "https://python.langchain.com/docs/integrations/tools/arxiv/", "Robocorp Toolkit": "https://python.langchain.com/docs/integrations/tools/robocorp/", "MultiOn Toolkit": "https://python.langchain.com/docs/integrations/tools/multion/", "Exa Search": "https://python.langchain.com/docs/integrations/tools/exa_search/", "Amadeus Toolkit": "https://python.langchain.com/docs/integrations/tools/amadeus/", "Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/", "You.com Search": "https://python.langchain.com/docs/integrations/tools/you/", "Bing Search": "https://python.langchain.com/docs/integrations/tools/bing_search/", "Azure AI Services Toolkit": "https://python.langchain.com/docs/integrations/tools/azure_ai_services/", "Polygon IO Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/polygon/", "Ionic Shopping Tool": "https://python.langchain.com/docs/integrations/tools/ionic_shopping/", "MLX": "https://python.langchain.com/docs/integrations/chat/mlx/", "ZHIPU AI": "https://python.langchain.com/docs/integrations/chat/zhipuai/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/", "Streamlit": "https://python.langchain.com/docs/integrations/callbacks/streamlit/", "Cohere": "https://python.langchain.com/docs/integrations/providers/cohere/", "Dataherald": "https://python.langchain.com/docs/integrations/providers/dataherald/", "Log, Trace, and Monitor": "https://python.langchain.com/docs/integrations/providers/portkey/logging_tracing_portkey/", "Portkey": "https://python.langchain.com/docs/integrations/providers/portkey/index/", "Bittensor": "https://python.langchain.com/docs/integrations/llms/bittensor/"}, "AIMessage": {"Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to merge consecutive messages of the same type": "https://python.langchain.com/docs/how_to/merge_message_runs/", "How to trim messages": "https://python.langchain.com/docs/how_to/trim_messages/", "How to do tool/function calling": "https://python.langchain.com/docs/how_to/function_calling/", "How to add a human-in-the-loop for tools": "https://python.langchain.com/docs/how_to/tools_human/", "How to use prompting alone (no tool calling) to do extraction": "https://python.langchain.com/docs/how_to/extraction_parse/", "How to use reference examples when doing extraction": "https://python.langchain.com/docs/how_to/extraction_examples/", "How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/", "How to filter messages": "https://python.langchain.com/docs/how_to/filter_messages/", "How to handle tool errors": "https://python.langchain.com/docs/how_to/tools_error/", "How to add tools to chatbots": "https://python.langchain.com/docs/how_to/chatbots_tools/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to create a custom Output Parser": "https://python.langchain.com/docs/how_to/output_parser_custom/", "How to return structured data from a model": "https://python.langchain.com/docs/how_to/structured_output/", "How to compose prompts together": "https://python.langchain.com/docs/how_to/prompts_composition/", "How to use few-shot prompting with tool calling": "https://python.langchain.com/docs/how_to/tools_few_shot/", "How to add examples to the prompt for query analysis": "https://python.langchain.com/docs/how_to/query_few_shot/", "Twitter (via Apify)": "https://python.langchain.com/docs/integrations/chat_loaders/twitter/", "Zep Open Source": "https://python.langchain.com/docs/integrations/retrievers/zep_memorystore/", "Zep Cloud": "https://python.langchain.com/docs/integrations/retrievers/zep_cloud_memorystore/", "Google Imagen": "https://python.langchain.com/docs/integrations/tools/google_imagen/", "Zep Open Source Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory/", "ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "Zep Cloud Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory_cloud/", "ChatOllama": "https://python.langchain.com/docs/integrations/chat/ollama/", "ChatOCIGenAI": "https://python.langchain.com/docs/integrations/chat/oci_generative_ai/", "ZHIPU AI": "https://python.langchain.com/docs/integrations/chat/zhipuai/", "Yuan2.0": "https://python.langchain.com/docs/integrations/chat/yuan2/", "Google Cloud Vertex AI": "https://python.langchain.com/docs/integrations/llms/google_vertex_ai_palm/", "ChatGLM": "https://python.langchain.com/docs/integrations/llms/chatglm/", "Chat Bot Feedback Template": "https://python.langchain.com/docs/templates/chat-bot-feedback/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/"}, "ChatMessageHistory": {"Build an Agent with AgentExecutor (Legacy)": "https://python.langchain.com/docs/how_to/agent_executor/", "How to add tools to chatbots": "https://python.langchain.com/docs/how_to/chatbots_tools/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add memory to chatbots": "https://python.langchain.com/docs/how_to/chatbots_memory/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/"}, "Neo4jGraph": {"How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to map values to a graph database": "https://python.langchain.com/docs/how_to/graph_mapping/", "How to construct knowledge graphs": "https://python.langchain.com/docs/how_to/graph_constructing/", "How to best prompt for Graph-RAG": "https://python.langchain.com/docs/how_to/graph_prompting/", "Neo4j": "https://python.langchain.com/docs/integrations/graphs/neo4j_cypher/", "Diffbot": "https://python.langchain.com/docs/integrations/graphs/diffbot/", "Build a Question Answering application over a Graph Database": "https://python.langchain.com/docs/tutorials/graph/"}, "AsyncCallbackManagerForToolRun": {"How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to create tools": "https://python.langchain.com/docs/how_to/custom_tools/"}, "CallbackManagerForToolRun": {"How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to create tools": "https://python.langchain.com/docs/how_to/custom_tools/"}, "BaseTool": {"How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to use LangChain with different Pydantic versions": "https://python.langchain.com/docs/how_to/pydantic_compatibility/", "How to pass run time values to tools": "https://python.langchain.com/docs/how_to/tool_runtime/", "How to create tools": "https://python.langchain.com/docs/how_to/custom_tools/", "How to return artifacts from a tool": "https://python.langchain.com/docs/how_to/tool_artifacts/"}, "format_to_openai_function_messages": {"How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/"}, "OpenAIFunctionsAgentOutputParser": {"How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/"}, "convert_to_openai_function": {"How to add a semantic layer over graph database": "https://python.langchain.com/docs/how_to/graph_semantic/", "How to convert tools to OpenAI Functions": "https://python.langchain.com/docs/how_to/tools_as_openai_functions/"}, "BSHTMLLoader": {"How to handle long text when doing extraction": "https://python.langchain.com/docs/how_to/extraction_long_text/", "How to load HTML": "https://python.langchain.com/docs/how_to/document_loader_html/", "BSHTMLLoader": "https://python.langchain.com/docs/integrations/document_loaders/bshtml/"}, "TokenTextSplitter": {"How to handle long text when doing extraction": "https://python.langchain.com/docs/how_to/extraction_long_text/", "How to split text by tokens ": "https://python.langchain.com/docs/how_to/split_by_token/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "Apache Doris": "https://python.langchain.com/docs/integrations/vectorstores/apache_doris/", "StarRocks": "https://python.langchain.com/docs/integrations/vectorstores/starrocks/"}, "PyPDFLoader": {"How to load PDFs": "https://python.langchain.com/docs/how_to/document_loader_pdf/", "Google Vertex AI Vector Search": "https://python.langchain.com/docs/integrations/vectorstores/google_vertex_ai_vector_search/", "Azure Cosmos DB No SQL": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db_no_sql/", "Apache Cassandra": "https://python.langchain.com/docs/integrations/vectorstores/cassandra/", "KDB.AI": "https://python.langchain.com/docs/integrations/vectorstores/kdbai/", "PyPDFLoader": "https://python.langchain.com/docs/integrations/document_loaders/pypdfloader/", "Merge Documents Loader": "https://python.langchain.com/docs/integrations/document_loaders/merge_doc/", "Google Cloud Storage File": "https://python.langchain.com/docs/integrations/document_loaders/google_cloud_storage_file/", "Build a PDF ingestion and Question/Answering system": "https://python.langchain.com/docs/tutorials/pdf_qa/"}, "SQLDatabase": {"How to better prompt when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_prompting/", "How to deal with large databases when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_large_db/", "How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/", "How to do query validation as part of SQL question-answering": "https://python.langchain.com/docs/how_to/sql_query_checking/", "SQLDatabase Toolkit": "https://python.langchain.com/docs/integrations/tools/sql_database/", "CnosDB": "https://python.langchain.com/docs/integrations/providers/cnosdb/", "Rebuff": "https://python.langchain.com/docs/integrations/providers/rebuff/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/"}, "create_sql_query_chain": {"How to better prompt when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_prompting/", "How to deal with large databases when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_large_db/", "How to do query validation as part of SQL question-answering": "https://python.langchain.com/docs/how_to/sql_query_checking/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/"}, "FewShotPromptTemplate": {"How to better prompt when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_prompting/", "How to select examples by n-gram overlap": "https://python.langchain.com/docs/how_to/example_selectors_ngram/", "How to select examples by length": "https://python.langchain.com/docs/how_to/example_selectors_length_based/", "How to use example selectors": "https://python.langchain.com/docs/how_to/example_selectors/", "How to use few shot examples": "https://python.langchain.com/docs/how_to/few_shot_examples/", "How to select examples by similarity": "https://python.langchain.com/docs/how_to/example_selectors_similarity/", "How to best prompt for Graph-RAG": "https://python.langchain.com/docs/how_to/graph_prompting/", "How to select examples by maximal marginal relevance (MMR)": "https://python.langchain.com/docs/how_to/example_selectors_mmr/", "Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/"}, "SemanticSimilarityExampleSelector": {"How to better prompt when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_prompting/", "How to use few shot examples": "https://python.langchain.com/docs/how_to/few_shot_examples/", "How to use few shot examples in chat models": "https://python.langchain.com/docs/how_to/few_shot_examples_chat/", "How to select examples by similarity": "https://python.langchain.com/docs/how_to/example_selectors_similarity/", "How to best prompt for Graph-RAG": "https://python.langchain.com/docs/how_to/graph_prompting/", "How to select examples by maximal marginal relevance (MMR)": "https://python.langchain.com/docs/how_to/example_selectors_mmr/"}, "PydanticOutputParser": {"How to use output parsers to parse an LLM response into structured format": "https://python.langchain.com/docs/how_to/output_parser_structured/", "How to use prompting alone (no tool calling) to do extraction": "https://python.langchain.com/docs/how_to/extraction_parse/", "How to use the output-fixing parser": "https://python.langchain.com/docs/how_to/output_parser_fixing/", "How to retry when a parsing error occurs": "https://python.langchain.com/docs/how_to/output_parser_retry/", "How to return structured data from a model": "https://python.langchain.com/docs/how_to/structured_output/", "Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/"}, "AsyncCallbackHandler": {"How to use callbacks in async environments": "https://python.langchain.com/docs/how_to/callbacks_async/", "How to dispatch custom callback events": "https://python.langchain.com/docs/how_to/callbacks_custom_events/", "Bedrock": "https://python.langchain.com/docs/integrations/llms/bedrock/"}, "BaseCallbackHandler": {"How to use callbacks in async environments": "https://python.langchain.com/docs/how_to/callbacks_async/", "How to propagate callbacks  constructor": "https://python.langchain.com/docs/how_to/callbacks_constructor/", "How to attach callbacks to a runnable": "https://python.langchain.com/docs/how_to/callbacks_attach/", "How to create custom callback handlers": "https://python.langchain.com/docs/how_to/custom_callbacks/", "How to dispatch custom callback events": "https://python.langchain.com/docs/how_to/callbacks_custom_events/", "How to pass callbacks in at runtime": "https://python.langchain.com/docs/how_to/callbacks_runtime/", "GPT4All": "https://python.langchain.com/docs/integrations/llms/gpt4all/"}, "LLMResult": {"How to use callbacks in async environments": "https://python.langchain.com/docs/how_to/callbacks_async/", "How to propagate callbacks  constructor": "https://python.langchain.com/docs/how_to/callbacks_constructor/", "How to attach callbacks to a runnable": "https://python.langchain.com/docs/how_to/callbacks_attach/", "How to pass callbacks in at runtime": "https://python.langchain.com/docs/how_to/callbacks_runtime/", "Google Cloud Vertex AI": "https://python.langchain.com/docs/integrations/llms/google_vertex_ai_palm/"}, "RunnableParallel": {"How to add values to a chain's state": "https://python.langchain.com/docs/how_to/assign/", "How to invoke runnables in parallel": "https://python.langchain.com/docs/how_to/parallel/", "How to pass through arguments from one step to the next": "https://python.langchain.com/docs/how_to/passthrough/", "How to add message history": "https://python.langchain.com/docs/how_to/message_history/", "How to retry when a parsing error occurs": "https://python.langchain.com/docs/how_to/output_parser_retry/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/", "LangChain Expression Language Cheatsheet": "https://python.langchain.com/docs/how_to/lcel_cheatsheet/", "How to chain runnables": "https://python.langchain.com/docs/how_to/sequence/", "ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "Google Cloud Vertex AI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/google_cloud_vertexai_rerank/"}, "RunnableBranch": {"How to route between sub-chains": "https://python.langchain.com/docs/how_to/routing/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/"}, "cosine_similarity": {"How to route between sub-chains": "https://python.langchain.com/docs/how_to/routing/"}, "ConfigurableField": {"How to do per-user retrieval": "https://python.langchain.com/docs/how_to/qa_per_user/", "How to configure runtime chain internals": "https://python.langchain.com/docs/how_to/configure/", "LangChain Expression Language Cheatsheet": "https://python.langchain.com/docs/how_to/lcel_cheatsheet/", "How to combine results from multiple retrievers": "https://python.langchain.com/docs/how_to/ensemble_retriever/", "Hybrid Search": "https://python.langchain.com/docs/how_to/hybrid/"}, "NGramOverlapExampleSelector": {"How to select examples by n-gram overlap": "https://python.langchain.com/docs/how_to/example_selectors_ngram/"}, "get_openai_callback": {"How to track token usage in ChatModels": "https://python.langchain.com/docs/how_to/chat_token_usage_tracking/", "How to run custom functions": "https://python.langchain.com/docs/how_to/functions/", "How to track token usage for LLMs": "https://python.langchain.com/docs/how_to/llm_token_usage_tracking/", "AzureChatOpenAI": "https://python.langchain.com/docs/integrations/chat/azure_chat_openai/"}, "load_tools": {"How to track token usage in ChatModels": "https://python.langchain.com/docs/how_to/chat_token_usage_tracking/", "Google": "https://python.langchain.com/docs/integrations/providers/google/", "ChatGPT Plugins": "https://python.langchain.com/docs/integrations/tools/chatgpt_plugins/", "Google Jobs": "https://python.langchain.com/docs/integrations/tools/google_jobs/", "Human as a tool": "https://python.langchain.com/docs/integrations/tools/human_tools/", "Google Finance": "https://python.langchain.com/docs/integrations/tools/google_finance/", "AWS Lambda": "https://python.langchain.com/docs/integrations/tools/awslambda/", "Google Drive": "https://python.langchain.com/docs/integrations/tools/google_drive/", "OpenWeatherMap": "https://python.langchain.com/docs/integrations/providers/openweathermap/", "Memorize": "https://python.langchain.com/docs/integrations/tools/memorize/", "Eleven Labs Text2Speech": "https://python.langchain.com/docs/integrations/tools/eleven_labs_tts/", "ArXiv": "https://python.langchain.com/docs/integrations/tools/arxiv/", "GraphQL": "https://python.langchain.com/docs/integrations/tools/graphql/", "SceneXplain": "https://python.langchain.com/docs/integrations/tools/sceneXplain/", "Dall-E Image Generator": "https://python.langchain.com/docs/integrations/tools/dalle_image_generator/", "MLX": "https://python.langchain.com/docs/integrations/chat/mlx/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/", "SageMaker Tracking": "https://python.langchain.com/docs/integrations/callbacks/sagemaker_tracking/", "Comet Tracing": "https://python.langchain.com/docs/integrations/callbacks/comet_tracing/", "Argilla": "https://python.langchain.com/docs/integrations/callbacks/argilla/", "Streamlit": "https://python.langchain.com/docs/integrations/callbacks/streamlit/", "SerpAPI": "https://python.langchain.com/docs/integrations/providers/serpapi/", "SearchApi": "https://python.langchain.com/docs/integrations/providers/searchapi/", "Comet": "https://python.langchain.com/docs/integrations/providers/comet_tracking/", "Aim": "https://python.langchain.com/docs/integrations/providers/aim_tracking/", "Golden": "https://python.langchain.com/docs/integrations/providers/golden/", "Weights & Biases": "https://python.langchain.com/docs/integrations/providers/wandb_tracking/", "Wolfram Alpha": "https://python.langchain.com/docs/integrations/providers/wolfram_alpha/", "MLflow": "https://python.langchain.com/docs/integrations/providers/mlflow_tracking/", "DataForSEO": "https://python.langchain.com/docs/integrations/providers/dataforseo/", "SearxNG Search API": "https://python.langchain.com/docs/integrations/providers/searx/", "Serper - Google Search API": "https://python.langchain.com/docs/integrations/providers/google_serper/", "Stack Exchange": "https://python.langchain.com/docs/integrations/providers/stackexchange/", "Flyte": "https://python.langchain.com/docs/integrations/providers/flyte/", "WandB Tracing": "https://python.langchain.com/docs/integrations/providers/wandb_tracing/", "ClearML": "https://python.langchain.com/docs/integrations/providers/clearml_tracking/", "Dataherald": "https://python.langchain.com/docs/integrations/providers/dataherald/", "Amazon API Gateway": "https://python.langchain.com/docs/integrations/llms/amazon_api_gateway/"}, "ChatBedrock": {"How to track token usage in ChatModels": "https://python.langchain.com/docs/how_to/chat_token_usage_tracking/", "Response metadata": "https://python.langchain.com/docs/how_to/response_metadata/", "AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "ChatBedrock": "https://python.langchain.com/docs/integrations/chat/bedrock/", "Amazon Neptune with SPARQL": "https://python.langchain.com/docs/integrations/graphs/amazon_neptune_sparql/"}, "get_bedrock_anthropic_callback": {"How to track token usage in ChatModels": "https://python.langchain.com/docs/how_to/chat_token_usage_tracking/"}, "CallbackManagerForLLMRun": {"How to create a custom LLM class": "https://python.langchain.com/docs/how_to/custom_llm/", "How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "LLM": {"How to create a custom LLM class": "https://python.langchain.com/docs/how_to/custom_llm/"}, "GenerationChunk": {"How to create a custom LLM class": "https://python.langchain.com/docs/how_to/custom_llm/"}, "BaseLoader": {"How to create a custom Document Loader": "https://python.langchain.com/docs/how_to/document_loader_custom/", "How to use the LangChain indexing API": "https://python.langchain.com/docs/how_to/indexing/"}, "BaseBlobParser": {"How to create a custom Document Loader": "https://python.langchain.com/docs/how_to/document_loader_custom/"}, "Blob": {"How to create a custom Document Loader": "https://python.langchain.com/docs/how_to/document_loader_custom/", "Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Cloud Document AI": "https://python.langchain.com/docs/integrations/document_transformers/google_docai/"}, "FileSystemBlobLoader": {"How to create a custom Document Loader": "https://python.langchain.com/docs/how_to/document_loader_custom/"}, "GenericLoader": {"How to create a custom Document Loader": "https://python.langchain.com/docs/how_to/document_loader_custom/", "Grobid": "https://python.langchain.com/docs/integrations/document_loaders/grobid/", "YouTube audio": "https://python.langchain.com/docs/integrations/document_loaders/youtube_audio/", "Source Code": "https://python.langchain.com/docs/integrations/document_loaders/source_code/"}, "LengthBasedExampleSelector": {"How to select examples by length": "https://python.langchain.com/docs/how_to/example_selectors_length_based/"}, "BaseExampleSelector": {"How to use example selectors": "https://python.langchain.com/docs/how_to/example_selectors/"}, "Language": {"How to split code": "https://python.langchain.com/docs/how_to/code_splitter/", "Source Code": "https://python.langchain.com/docs/integrations/document_loaders/source_code/"}, "Chroma": {"How to use few shot examples": "https://python.langchain.com/docs/how_to/few_shot_examples/", "How to handle cases where no queries are generated": "https://python.langchain.com/docs/how_to/query_no_queries/", "How to use few shot examples in chat models": "https://python.langchain.com/docs/how_to/few_shot_examples_chat/", "How to select examples by similarity": "https://python.langchain.com/docs/how_to/example_selectors_similarity/", "How to reorder retrieved results to mitigate the \"lost in the middle\" effect": "https://python.langchain.com/docs/how_to/long_context_reorder/", "How to handle multiple queries when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_queries/", "How to stream results from your RAG application": "https://python.langchain.com/docs/how_to/qa_streaming/", "How to get your RAG application to return sources": "https://python.langchain.com/docs/how_to/qa_sources/", "How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "How to add chat history": "https://python.langchain.com/docs/how_to/qa_chat_history_how_to/", "How to add retrieval to chatbots": "https://python.langchain.com/docs/how_to/chatbots_retrieval/", "How to handle multiple retrievers when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_retrievers/", "How to create and query vector stores": "https://python.langchain.com/docs/how_to/vectorstores/", "How to use the MultiQueryRetriever": "https://python.langchain.com/docs/how_to/MultiQueryRetriever/", "How to use the Parent Document Retriever": "https://python.langchain.com/docs/how_to/parent_document_retriever/", "How deal with high cardinality categoricals when doing query analysis": "https://python.langchain.com/docs/how_to/query_high_cardinality/", "How to do \"self-querying\" retrieval": "https://python.langchain.com/docs/how_to/self_query/", "RePhraseQuery": "https://python.langchain.com/docs/integrations/retrievers/re_phrase/", "LOTR (Merger Retriever)": "https://python.langchain.com/docs/integrations/retrievers/merger_retriever/", "Chroma": "https://python.langchain.com/docs/integrations/vectorstores/chroma/", "Confident": "https://python.langchain.com/docs/integrations/callbacks/confident/", "Google Cloud Vertex AI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/google_cloud_vertexai_rerank/", "Image captions": "https://python.langchain.com/docs/integrations/document_loaders/image_captions/", "Psychic": "https://python.langchain.com/docs/integrations/document_loaders/psychic/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/", "Build a Retrieval Augmented Generation (RAG) App": "https://python.langchain.com/docs/tutorials/rag/", "Build a Local RAG Application": "https://python.langchain.com/docs/tutorials/local_rag/", "Conversational RAG": "https://python.langchain.com/docs/tutorials/qa_chat_history/", "Build a Query Analysis System": "https://python.langchain.com/docs/tutorials/query_analysis/", "Build a PDF ingestion and Question/Answering system": "https://python.langchain.com/docs/tutorials/pdf_qa/", "Vector stores and retrievers": "https://python.langchain.com/docs/tutorials/retrievers/"}, "merge_message_runs": {"How to merge consecutive messages of the same type": "https://python.langchain.com/docs/how_to/merge_message_runs/"}, "PydanticToolsParser": {"How to handle cases where no queries are generated": "https://python.langchain.com/docs/how_to/query_no_queries/", "How to do tool/function calling": "https://python.langchain.com/docs/how_to/function_calling/", "How to deal with large databases when doing SQL question-answering": "https://python.langchain.com/docs/how_to/sql_large_db/", "How to handle multiple queries when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_queries/", "How to use chat models to call tools": "https://python.langchain.com/docs/how_to/tool_calling/", "How to handle multiple retrievers when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_retrievers/", "ChatPremAI": "https://python.langchain.com/docs/integrations/chat/premai/", "PremAI": "https://python.langchain.com/docs/integrations/providers/premai/"}, "chain": {"How to handle cases where no queries are generated": "https://python.langchain.com/docs/how_to/query_no_queries/", "How to pass run time values to tools": "https://python.langchain.com/docs/how_to/tool_runtime/", "How to handle multiple queries when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_queries/", "How to create a dynamic (self-constructing) chain": "https://python.langchain.com/docs/how_to/dynamic_chain/", "How to stream runnables": "https://python.langchain.com/docs/how_to/streaming/", "How to handle multiple retrievers when doing query analysis": "https://python.langchain.com/docs/how_to/query_multiple_retrievers/", "How to run custom functions": "https://python.langchain.com/docs/how_to/functions/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/", "Tavily Search": "https://python.langchain.com/docs/integrations/tools/tavily_search/"}, "trim_messages": {"How to trim messages": "https://python.langchain.com/docs/how_to/trim_messages/", "How to add memory to chatbots": "https://python.langchain.com/docs/how_to/chatbots_memory/", "Build a Chatbot": "https://python.langchain.com/docs/tutorials/chatbot/"}, "ToolMessage": {"How to trim messages": "https://python.langchain.com/docs/how_to/trim_messages/", "How to do tool/function calling": "https://python.langchain.com/docs/how_to/function_calling/", "How to use reference examples when doing extraction": "https://python.langchain.com/docs/how_to/extraction_examples/", "How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/", "How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/", "How to handle tool errors": "https://python.langchain.com/docs/how_to/tools_error/", "How to return structured data from a model": "https://python.langchain.com/docs/how_to/structured_output/", "How to use few-shot prompting with tool calling": "https://python.langchain.com/docs/how_to/tools_few_shot/", "How to add examples to the prompt for query analysis": "https://python.langchain.com/docs/how_to/query_few_shot/", "Cohere": "https://python.langchain.com/docs/integrations/providers/cohere/", "Eden AI": "https://python.langchain.com/docs/integrations/chat/edenai/", "ChatPremAI": "https://python.langchain.com/docs/integrations/chat/premai/", "PremAI": "https://python.langchain.com/docs/integrations/providers/premai/"}, "RecursiveJsonSplitter": {"How to split JSON data": "https://python.langchain.com/docs/how_to/recursive_json_splitter/"}, "FewShotChatMessagePromptTemplate": {"How to use few shot examples in chat models": "https://python.langchain.com/docs/how_to/few_shot_examples_chat/", "Fiddler": "https://python.langchain.com/docs/integrations/callbacks/fiddler/"}, "XMLOutputParser": {"How to parse XML output": "https://python.langchain.com/docs/how_to/output_parser_xml/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/"}, "InjectedToolArg": {"How to pass run time values to tools": "https://python.langchain.com/docs/how_to/tool_runtime/"}, "Runnable": {"How to add a human-in-the-loop for tools": "https://python.langchain.com/docs/how_to/tools_human/", "How to handle tool errors": "https://python.langchain.com/docs/how_to/tools_error/", "How to create a dynamic (self-constructing) chain": "https://python.langchain.com/docs/how_to/dynamic_chain/"}, "StructuredTool": {"How to create tools": "https://python.langchain.com/docs/how_to/custom_tools/", "Infobip": "https://python.langchain.com/docs/integrations/tools/infobip/"}, "GenericFakeChatModel": {"How to create tools": "https://python.langchain.com/docs/how_to/custom_tools/"}, "ToolException": {"How to create tools": "https://python.langchain.com/docs/how_to/custom_tools/"}, "AzureAIDocumentIntelligenceLoader": {"How to load Microsoft Office files": "https://python.langchain.com/docs/how_to/document_loader_office_file/", "Microsoft Word": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_word/", "Microsoft Excel": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_excel/", "Microsoft PowerPoint": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_powerpoint/", "Azure AI Document Intelligence": "https://python.langchain.com/docs/integrations/document_loaders/azure_document_intelligence/"}, "InMemoryRateLimiter": {"How to handle rate limits": "https://python.langchain.com/docs/how_to/chat_model_rate_limiting/"}, "LongContextReorder": {"How to reorder retrieved results to mitigate the \"lost in the middle\" effect": "https://python.langchain.com/docs/how_to/long_context_reorder/", "LOTR (Merger Retriever)": "https://python.langchain.com/docs/integrations/retrievers/merger_retriever/"}, "DatetimeOutputParser": {"How to add fallbacks to a runnable": "https://python.langchain.com/docs/how_to/fallbacks/"}, "CypherQueryCorrector": {"How to map values to a graph database": "https://python.langchain.com/docs/how_to/graph_mapping/"}, "Schema": {"How to map values to a graph database": "https://python.langchain.com/docs/how_to/graph_mapping/"}, "dumpd": {"How to save and load LangChain objects": "https://python.langchain.com/docs/how_to/serialization/"}, "dumps": {"How to save and load LangChain objects": "https://python.langchain.com/docs/how_to/serialization/"}, "load": {"How to save and load LangChain objects": "https://python.langchain.com/docs/how_to/serialization/"}, "loads": {"How to save and load LangChain objects": "https://python.langchain.com/docs/how_to/serialization/"}, "set_llm_cache": {"How to cache chat model responses": "https://python.langchain.com/docs/how_to/chat_model_caching/", "How to cache LLM responses": "https://python.langchain.com/docs/how_to/llm_caching/", "Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "DSPy": "https://python.langchain.com/docs/integrations/providers/dspy/", "MongoDB Atlas": "https://python.langchain.com/docs/integrations/providers/mongodb_atlas/", "Astra DB": "https://python.langchain.com/docs/integrations/providers/astradb/", "Couchbase": "https://python.langchain.com/docs/integrations/providers/couchbase/", "Redis": "https://python.langchain.com/docs/integrations/providers/redis/", "Cassandra": "https://python.langchain.com/docs/integrations/providers/cassandra/", "Momento": "https://python.langchain.com/docs/integrations/providers/momento/"}, "InMemoryCache": {"How to cache chat model responses": "https://python.langchain.com/docs/how_to/chat_model_caching/", "How to cache LLM responses": "https://python.langchain.com/docs/how_to/llm_caching/", "Model caches": "https://python.langchain.com/docs/integrations/llm_caching/"}, "SQLiteCache": {"How to cache chat model responses": "https://python.langchain.com/docs/how_to/chat_model_caching/", "How to cache LLM responses": "https://python.langchain.com/docs/how_to/llm_caching/", "Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "DSPy": "https://python.langchain.com/docs/integrations/providers/dspy/"}, "create_sql_agent": {"How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/", "CnosDB": "https://python.langchain.com/docs/integrations/providers/cnosdb/"}, "PythonAstREPLTool": {"How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/"}, "JsonOutputKeyToolsParser": {"How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/"}, "create_pandas_dataframe_agent": {"How to do question answering over CSVs": "https://python.langchain.com/docs/how_to/sql_csv/", "Pandas Dataframe": "https://python.langchain.com/docs/integrations/tools/pandas/"}, "OutputFixingParser": {"How to use the output-fixing parser": "https://python.langchain.com/docs/how_to/output_parser_fixing/", "How to retry when a parsing error occurs": "https://python.langchain.com/docs/how_to/output_parser_retry/"}, "FunctionMessage": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "AIMessageChunk": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/", "How to create a custom Output Parser": "https://python.langchain.com/docs/how_to/output_parser_custom/", "Google Cloud Vertex AI": "https://python.langchain.com/docs/integrations/llms/google_vertex_ai_palm/"}, "FunctionMessageChunk": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "HumanMessageChunk": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "SystemMessageChunk": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "ToolMessageChunk": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "AsyncCallbackManagerForLLMRun": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "SimpleChatModel": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "ChatGeneration": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/", "How to create a custom Output Parser": "https://python.langchain.com/docs/how_to/output_parser_custom/"}, "ChatGenerationChunk": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "ChatResult": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "run_in_executor": {"How to create a custom chat model class": "https://python.langchain.com/docs/how_to/custom_chat_model/"}, "MoveFileTool": {"How to convert tools to OpenAI Functions": "https://python.langchain.com/docs/how_to/tools_as_openai_functions/"}, "filter_messages": {"How to filter messages": "https://python.langchain.com/docs/how_to/filter_messages/"}, "ToolCall": {"How to handle tool errors": "https://python.langchain.com/docs/how_to/tools_error/"}, "SQLRecordManager": {"How to use the LangChain indexing API": "https://python.langchain.com/docs/how_to/indexing/"}, "index": {"How to use the LangChain indexing API": "https://python.langchain.com/docs/how_to/indexing/"}, "SemanticChunker": {"How to split text based on semantic similarity": "https://python.langchain.com/docs/how_to/semantic-chunker/"}, "InMemoryVectorStore": {"How to convert Runnables as Tools": "https://python.langchain.com/docs/how_to/convert_runnable_to_tool/", "FireworksEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/fireworks/", "OpenAIEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/openai/", "OllamaEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/ollama/", "MistralAIEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/mistralai/", "AI21Embeddings": "https://python.langchain.com/docs/integrations/text_embedding/ai21/", "TogetherEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/together/", "CohereEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/cohere/", "AzureOpenAIEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/azureopenai/", "NomicEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/nomic/", "AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Amazon MemoryDB": "https://python.langchain.com/docs/integrations/vectorstores/memorydb/"}, "JsonOutputParser": {"How to stream runnables": "https://python.langchain.com/docs/how_to/streaming/", "How to add ad-hoc tool calling capability to LLMs and Chat Models": "https://python.langchain.com/docs/how_to/tools_prompting/", "How to parse JSON output": "https://python.langchain.com/docs/how_to/output_parser_json/"}, "InMemoryByteStore": {"How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "Caching": "https://python.langchain.com/docs/how_to/caching_embeddings/", "InMemoryByteStore": "https://python.langchain.com/docs/integrations/stores/in_memory/"}, "TextLoader": {"How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/", "How to load documents from a directory": "https://python.langchain.com/docs/how_to/document_loader_directory/", "How to create and query vector stores": "https://python.langchain.com/docs/how_to/vectorstores/", "How to use the Parent Document Retriever": "https://python.langchain.com/docs/how_to/parent_document_retriever/", "How to use a vectorstore as a retriever": "https://python.langchain.com/docs/how_to/vectorstore_retriever/", "Caching": "https://python.langchain.com/docs/how_to/caching_embeddings/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "Kinetica Vectorstore based Retriever": "https://python.langchain.com/docs/integrations/retrievers/kinetica/", "JaguarDB Vector Database": "https://python.langchain.com/docs/integrations/retrievers/jaguar/", "LLMLingua Document Compressor": "https://python.langchain.com/docs/integrations/retrievers/llmlingua/", "Cohere reranker": "https://python.langchain.com/docs/integrations/retrievers/cohere-reranker/", "SingleStoreDB": "https://python.langchain.com/docs/integrations/retrievers/singlestoredb/", "FlashRank reranker": "https://python.langchain.com/docs/integrations/retrievers/flashrank-reranker/", "Confident": "https://python.langchain.com/docs/integrations/callbacks/confident/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "Upstash Vector": "https://python.langchain.com/docs/integrations/vectorstores/upstash/", "VDMS": "https://python.langchain.com/docs/integrations/providers/vdms/", "Vectara Chat": "https://python.langchain.com/docs/integrations/providers/vectara/vectara_chat/", "LanceDB": "https://python.langchain.com/docs/integrations/vectorstores/lancedb/", "Kinetica Vectorstore API": "https://python.langchain.com/docs/integrations/vectorstores/kinetica/", "SQLite-VSS": "https://python.langchain.com/docs/integrations/vectorstores/sqlitevss/", "Vald": "https://python.langchain.com/docs/integrations/vectorstores/vald/", "Weaviate": "https://python.langchain.com/docs/integrations/vectorstores/weaviate/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/", "DashVector": "https://python.langchain.com/docs/integrations/vectorstores/dashvector/", "Databricks Vector Search": "https://python.langchain.com/docs/integrations/vectorstores/databricks_vector_search/", "ScaNN": "https://python.langchain.com/docs/integrations/vectorstores/scann/", "Xata": "https://python.langchain.com/docs/integrations/vectorstores/xata/", "Hippo": "https://python.langchain.com/docs/integrations/vectorstores/hippo/", "Vespa": "https://python.langchain.com/docs/integrations/vectorstores/vespa/", "Rockset": "https://python.langchain.com/docs/integrations/vectorstores/rockset/", "DingoDB": "https://python.langchain.com/docs/integrations/vectorstores/dingo/", "Zilliz": "https://python.langchain.com/docs/integrations/vectorstores/zilliz/", "Azure Cosmos DB Mongo vCore": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db/", "viking DB": "https://python.langchain.com/docs/integrations/vectorstores/vikingdb/", "Annoy": "https://python.langchain.com/docs/integrations/vectorstores/annoy/", "Couchbase ": "https://python.langchain.com/docs/integrations/vectorstores/couchbase/", "Typesense": "https://python.langchain.com/docs/integrations/vectorstores/typesense/", "Momento Vector Index (MVI)": "https://python.langchain.com/docs/integrations/vectorstores/momento_vector_index/", "TiDB Vector": "https://python.langchain.com/docs/integrations/vectorstores/tidb_vector/", "Relyt": "https://python.langchain.com/docs/integrations/vectorstores/relyt/", "Atlas": "https://python.langchain.com/docs/integrations/vectorstores/atlas/", "Activeloop Deep Lake": "https://python.langchain.com/docs/integrations/vectorstores/activeloop_deeplake/", "vlite": "https://python.langchain.com/docs/integrations/vectorstores/vlite/", "Neo4j Vector Index": "https://python.langchain.com/docs/integrations/vectorstores/neo4jvector/", "Lantern": "https://python.langchain.com/docs/integrations/vectorstores/lantern/", "Tair": "https://python.langchain.com/docs/integrations/vectorstores/tair/", "DuckDB": "https://python.langchain.com/docs/integrations/vectorstores/duckdb/", "Alibaba Cloud OpenSearch": "https://python.langchain.com/docs/integrations/vectorstores/alibabacloud_opensearch/", "Clarifai": "https://python.langchain.com/docs/integrations/vectorstores/clarifai/", "scikit-learn": "https://python.langchain.com/docs/integrations/vectorstores/sklearn/", "Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/tencentvectordb/", "DocArray HnswSearch": "https://python.langchain.com/docs/integrations/vectorstores/docarray_hnsw/", "MyScale": "https://python.langchain.com/docs/integrations/vectorstores/myscale/", "TileDB": "https://python.langchain.com/docs/integrations/vectorstores/tiledb/", "Google Memorystore for Redis": "https://python.langchain.com/docs/integrations/vectorstores/google_memorystore_redis/", "Tigris": "https://python.langchain.com/docs/integrations/vectorstores/tigris/", "China Mobile ECloud ElasticSearch VectorSearch": "https://python.langchain.com/docs/integrations/vectorstores/ecloud_vector_search/", "Bagel": "https://python.langchain.com/docs/integrations/vectorstores/bagel/", "Baidu Cloud ElasticSearch VectorSearch": "https://python.langchain.com/docs/integrations/vectorstores/baiducloud_vector_search/", "AwaDB": "https://python.langchain.com/docs/integrations/vectorstores/awadb/", "Supabase (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/supabase/", "SurrealDB": "https://python.langchain.com/docs/integrations/vectorstores/surrealdb/", "OpenSearch": "https://python.langchain.com/docs/integrations/vectorstores/opensearch/", "Faiss (Async)": "https://python.langchain.com/docs/integrations/vectorstores/faiss_async/", "BagelDB": "https://python.langchain.com/docs/integrations/vectorstores/bageldb/", "ManticoreSearch VectorStore": "https://python.langchain.com/docs/integrations/vectorstores/manticore_search/", "Azure AI Search": "https://python.langchain.com/docs/integrations/vectorstores/azuresearch/", "USearch": "https://python.langchain.com/docs/integrations/vectorstores/usearch/", "PGVecto.rs": "https://python.langchain.com/docs/integrations/vectorstores/pgvecto_rs/", "Marqo": "https://python.langchain.com/docs/integrations/vectorstores/marqo/", "DocArray InMemorySearch": "https://python.langchain.com/docs/integrations/vectorstores/docarray_in_memory/", "Postgres Embedding": "https://python.langchain.com/docs/integrations/vectorstores/pgembedding/", "Intel's Visual Data Management System (VDMS)": "https://python.langchain.com/docs/integrations/vectorstores/vdms/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "Epsilla": "https://python.langchain.com/docs/integrations/vectorstores/epsilla/", "Amazon Document DB": "https://python.langchain.com/docs/integrations/vectorstores/documentdb/", "SemaDB": "https://python.langchain.com/docs/integrations/vectorstores/semadb/", "AnalyticDB": "https://python.langchain.com/docs/integrations/vectorstores/analyticdb/", "Hologres": "https://python.langchain.com/docs/integrations/vectorstores/hologres/", "Baidu VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/baiduvectordb/", "Vearch": "https://python.langchain.com/docs/integrations/vectorstores/vearch/", "Meilisearch": "https://python.langchain.com/docs/integrations/vectorstores/meilisearch/", "VoyageAI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/voyageai-reranker/", "RankLLM Reranker": "https://python.langchain.com/docs/integrations/document_transformers/rankllm-reranker/", "Volcengine Reranker": "https://python.langchain.com/docs/integrations/document_transformers/volcengine_rerank/", "OpenVINO Reranker": "https://python.langchain.com/docs/integrations/document_transformers/openvino_rerank/", "Cross Encoder Reranker": "https://python.langchain.com/docs/integrations/document_transformers/cross_encoder_reranker/", "Jina Reranker": "https://python.langchain.com/docs/integrations/document_transformers/jina_rerank/", "DashScope Reranker": "https://python.langchain.com/docs/integrations/document_transformers/dashscope_rerank/"}, "MultiVectorRetriever": {"How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/", "Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/"}, "SearchType": {"How to retrieve using multiple vectors per document": "https://python.langchain.com/docs/how_to/multi_vector/", "Zep Open Source": "https://python.langchain.com/docs/integrations/retrievers/zep_memorystore/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/"}, "init_chat_model": {"How to init any model in one line": "https://python.langchain.com/docs/how_to/chat_models_universal_init/", "How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "SQLChatMessageHistory": {"How to add message history": "https://python.langchain.com/docs/how_to/message_history/", "SQL (SQLAlchemy)": "https://python.langchain.com/docs/integrations/memory/sql_chat_message_history/", "SQLite": "https://python.langchain.com/docs/integrations/providers/sqlite/"}, "ConfigurableFieldSpec": {"How to add message history": "https://python.langchain.com/docs/how_to/message_history/"}, "LlamaCpp": {"Run models locally": "https://python.langchain.com/docs/how_to/local_llms/", "Llama2Chat": "https://python.langchain.com/docs/integrations/chat/llama2_chat/", "Llama.cpp": "https://python.langchain.com/docs/integrations/llms/llamacpp/"}, "CallbackManager": {"Run models locally": "https://python.langchain.com/docs/how_to/local_llms/", "ChatLiteLLM": "https://python.langchain.com/docs/integrations/chat/litellm/", "GPTRouter": "https://python.langchain.com/docs/integrations/chat/gpt_router/", "ChatLiteLLMRouter": "https://python.langchain.com/docs/integrations/chat/litellm_router/", "ZHIPU AI": "https://python.langchain.com/docs/integrations/chat/zhipuai/", "Llama.cpp": "https://python.langchain.com/docs/integrations/llms/llamacpp/", "Titan Takeoff": "https://python.langchain.com/docs/integrations/llms/titan_takeoff/"}, "StreamingStdOutCallbackHandler": {"Run models locally": "https://python.langchain.com/docs/how_to/local_llms/", "DeepInfra": "https://python.langchain.com/docs/integrations/chat/deepinfra/", "ChatLiteLLM": "https://python.langchain.com/docs/integrations/chat/litellm/", "ChatEverlyAI": "https://python.langchain.com/docs/integrations/chat/everlyai/", "GPTRouter": "https://python.langchain.com/docs/integrations/chat/gpt_router/", "ChatLiteLLMRouter": "https://python.langchain.com/docs/integrations/chat/litellm_router/", "ZHIPU AI": "https://python.langchain.com/docs/integrations/chat/zhipuai/", "Yuan2.0": "https://python.langchain.com/docs/integrations/chat/yuan2/", "GPT4All": "https://python.langchain.com/docs/integrations/providers/gpt4all/", "Arthur": "https://python.langchain.com/docs/integrations/providers/arthur_tracking/", "TextGen": "https://python.langchain.com/docs/integrations/llms/textgen/", "Llama.cpp": "https://python.langchain.com/docs/integrations/llms/llamacpp/", "Titan Takeoff": "https://python.langchain.com/docs/integrations/llms/titan_takeoff/", "Eden AI": "https://python.langchain.com/docs/integrations/llms/edenai/", "C Transformers": "https://python.langchain.com/docs/integrations/llms/ctransformers/", "ExLlamaV2": "https://python.langchain.com/docs/integrations/llms/exllamav2/", "Huggingface Endpoints": "https://python.langchain.com/docs/integrations/llms/huggingface_endpoint/", "Replicate": "https://python.langchain.com/docs/integrations/llms/replicate/"}, "GPT4All": {"Run models locally": "https://python.langchain.com/docs/how_to/local_llms/", "PromptLayer": "https://python.langchain.com/docs/integrations/callbacks/promptlayer/", "GPT4All": "https://python.langchain.com/docs/integrations/llms/gpt4all/"}, "Llamafile": {"Run models locally": "https://python.langchain.com/docs/how_to/local_llms/", "Llamafile": "https://python.langchain.com/docs/integrations/llms/llamafile/"}, "ConditionalPromptSelector": {"Run models locally": "https://python.langchain.com/docs/how_to/local_llms/"}, "HubRunnable": {"How to configure runtime chain internals": "https://python.langchain.com/docs/how_to/configure/"}, "ContextualCompressionRetriever": {"How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/", "LLMLingua Document Compressor": "https://python.langchain.com/docs/integrations/retrievers/llmlingua/", "Cohere reranker": "https://python.langchain.com/docs/integrations/retrievers/cohere-reranker/", "FlashRank reranker": "https://python.langchain.com/docs/integrations/retrievers/flashrank-reranker/", "LOTR (Merger Retriever)": "https://python.langchain.com/docs/integrations/retrievers/merger_retriever/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "RAGatouille": "https://python.langchain.com/docs/integrations/providers/ragatouille/", "VoyageAI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/voyageai-reranker/", "RankLLM Reranker": "https://python.langchain.com/docs/integrations/document_transformers/rankllm-reranker/", "Volcengine Reranker": "https://python.langchain.com/docs/integrations/document_transformers/volcengine_rerank/", "OpenVINO Reranker": "https://python.langchain.com/docs/integrations/document_transformers/openvino_rerank/", "Google Cloud Vertex AI Reranker": "https://python.langchain.com/docs/integrations/document_transformers/google_cloud_vertexai_rerank/", "Cross Encoder Reranker": "https://python.langchain.com/docs/integrations/document_transformers/cross_encoder_reranker/", "Jina Reranker": "https://python.langchain.com/docs/integrations/document_transformers/jina_rerank/", "DashScope Reranker": "https://python.langchain.com/docs/integrations/document_transformers/dashscope_rerank/"}, "LLMChainExtractor": {"How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/"}, "LLMChainFilter": {"How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/"}, "LLMListwiseRerank": {"How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/"}, "EmbeddingsFilter": {"How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/", "How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/"}, "DocumentCompressorPipeline": {"How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/"}, "EmbeddingsRedundantFilter": {"How to do retrieval with contextual compression": "https://python.langchain.com/docs/how_to/contextual_compression/", "LOTR (Merger Retriever)": "https://python.langchain.com/docs/integrations/retrievers/merger_retriever/"}, "Comparator": {"How to construct filters for query analysis": "https://python.langchain.com/docs/how_to/query_constructing_filters/"}, "Comparison": {"How to construct filters for query analysis": "https://python.langchain.com/docs/how_to/query_constructing_filters/"}, "Operation": {"How to construct filters for query analysis": "https://python.langchain.com/docs/how_to/query_constructing_filters/"}, "Operator": {"How to construct filters for query analysis": "https://python.langchain.com/docs/how_to/query_constructing_filters/"}, "StructuredQuery": {"How to construct filters for query analysis": "https://python.langchain.com/docs/how_to/query_constructing_filters/"}, "ChromaTranslator": {"How to construct filters for query analysis": "https://python.langchain.com/docs/how_to/query_constructing_filters/", "How to do \"self-querying\" retrieval": "https://python.langchain.com/docs/how_to/self_query/"}, "ElasticsearchTranslator": {"How to construct filters for query analysis": "https://python.langchain.com/docs/how_to/query_constructing_filters/"}, "WikipediaQueryRun": {"How to use built-in tools and toolkits": "https://python.langchain.com/docs/how_to/tools_builtin/", "Wikipedia": "https://python.langchain.com/docs/integrations/tools/wikipedia/"}, "WikipediaAPIWrapper": {"How to use built-in tools and toolkits": "https://python.langchain.com/docs/how_to/tools_builtin/", "Wikipedia": "https://python.langchain.com/docs/integrations/tools/wikipedia/", "Zep Open Source Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory/", "Zep Cloud Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory_cloud/"}, "CallbackManagerForRetrieverRun": {"How to create a custom Retriever": "https://python.langchain.com/docs/how_to/custom_retriever/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/"}, "BaseRetriever": {"How to create a custom Retriever": "https://python.langchain.com/docs/how_to/custom_retriever/"}, "LLMGraphTransformer": {"How to construct knowledge graphs": "https://python.langchain.com/docs/how_to/graph_constructing/"}, "RetryOutputParser": {"How to retry when a parsing error occurs": "https://python.langchain.com/docs/how_to/output_parser_retry/"}, "TimeWeightedVectorStoreRetriever": {"How to use a time-weighted vector store retriever": "https://python.langchain.com/docs/how_to/time_weighted_vectorstore/"}, "InMemoryDocstore": {"How to use a time-weighted vector store retriever": "https://python.langchain.com/docs/how_to/time_weighted_vectorstore/", "Annoy": "https://python.langchain.com/docs/integrations/vectorstores/annoy/", "Faiss": "https://python.langchain.com/docs/integrations/vectorstores/faiss/"}, "mock_now": {"How to use a time-weighted vector store retriever": "https://python.langchain.com/docs/how_to/time_weighted_vectorstore/"}, "RunnableGenerator": {"How to create a custom Output Parser": "https://python.langchain.com/docs/how_to/output_parser_custom/"}, "OutputParserException": {"How to create a custom Output Parser": "https://python.langchain.com/docs/how_to/output_parser_custom/"}, "BaseOutputParser": {"How to create a custom Output Parser": "https://python.langchain.com/docs/how_to/output_parser_custom/", "How to use the MultiQueryRetriever": "https://python.langchain.com/docs/how_to/MultiQueryRetriever/"}, "BaseGenerationOutputParser": {"How to create a custom Output Parser": "https://python.langchain.com/docs/how_to/output_parser_custom/"}, "Generation": {"How to create a custom Output Parser": "https://python.langchain.com/docs/how_to/output_parser_custom/"}, "DirectoryLoader": {"How to load documents from a directory": "https://python.langchain.com/docs/how_to/document_loader_directory/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "Apache Doris": "https://python.langchain.com/docs/integrations/vectorstores/apache_doris/", "StarRocks": "https://python.langchain.com/docs/integrations/vectorstores/starrocks/"}, "PythonLoader": {"How to load documents from a directory": "https://python.langchain.com/docs/how_to/document_loader_directory/"}, "LanceDB": {"How to create and query vector stores": "https://python.langchain.com/docs/how_to/vectorstores/", "LanceDB": "https://python.langchain.com/docs/integrations/vectorstores/lancedb/"}, "SpacyTextSplitter": {"How to split text by tokens ": "https://python.langchain.com/docs/how_to/split_by_token/", "spaCy": "https://python.langchain.com/docs/integrations/providers/spacy/", "Atlas": "https://python.langchain.com/docs/integrations/vectorstores/atlas/"}, "SentenceTransformersTokenTextSplitter": {"How to split text by tokens ": "https://python.langchain.com/docs/how_to/split_by_token/"}, "NLTKTextSplitter": {"How to split text by tokens ": "https://python.langchain.com/docs/how_to/split_by_token/"}, "KonlpyTextSplitter": {"How to split text by tokens ": "https://python.langchain.com/docs/how_to/split_by_token/"}, "WikipediaRetriever": {"How to get a RAG application to add citations": "https://python.langchain.com/docs/how_to/qa_citations/", "WikipediaRetriever": "https://python.langchain.com/docs/integrations/retrievers/wikipedia/", "Wikipedia": "https://python.langchain.com/docs/integrations/providers/wikipedia/"}, "UnstructuredHTMLLoader": {"How to load HTML": "https://python.langchain.com/docs/how_to/document_loader_html/", "Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/"}, "MultiQueryRetriever": {"How to use the MultiQueryRetriever": "https://python.langchain.com/docs/how_to/MultiQueryRetriever/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/", "Vectara": "https://python.langchain.com/docs/integrations/vectorstores/vectara/"}, "GraphCypherQAChain": {"How to best prompt for Graph-RAG": "https://python.langchain.com/docs/how_to/graph_prompting/", "Neo4j": "https://python.langchain.com/docs/integrations/graphs/neo4j_cypher/", "Memgraph": "https://python.langchain.com/docs/integrations/graphs/memgraph/", "Diffbot": "https://python.langchain.com/docs/integrations/graphs/diffbot/", "Apache AGE": "https://python.langchain.com/docs/integrations/graphs/apache_age/", "Build a Question Answering application over a Graph Database": "https://python.langchain.com/docs/tutorials/graph/"}, "Neo4jVector": {"How to best prompt for Graph-RAG": "https://python.langchain.com/docs/how_to/graph_prompting/", "Neo4j": "https://python.langchain.com/docs/integrations/providers/neo4j/", "Neo4j Vector Index": "https://python.langchain.com/docs/integrations/vectorstores/neo4jvector/"}, "ParentDocumentRetriever": {"How to use the Parent Document Retriever": "https://python.langchain.com/docs/how_to/parent_document_retriever/"}, "InMemoryStore": {"How to use the Parent Document Retriever": "https://python.langchain.com/docs/how_to/parent_document_retriever/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/", "Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/"}, "YamlOutputParser": {"How to parse YAML output": "https://python.langchain.com/docs/how_to/output_parser_yaml/"}, "PipelinePromptTemplate": {"How to compose prompts together": "https://python.langchain.com/docs/how_to/prompts_composition/"}, "CacheBackedEmbeddings": {"Caching": "https://python.langchain.com/docs/how_to/caching_embeddings/"}, "LocalFileStore": {"Caching": "https://python.langchain.com/docs/how_to/caching_embeddings/", "LocalFileStore": "https://python.langchain.com/docs/integrations/stores/file_system/"}, "Ollama": {"How to add ad-hoc tool calling capability to LLMs and Chat Models": "https://python.langchain.com/docs/how_to/tools_prompting/", "ApertureDB": "https://python.langchain.com/docs/integrations/vectorstores/aperturedb/"}, "render_text_description": {"How to add ad-hoc tool calling capability to LLMs and Chat Models": "https://python.langchain.com/docs/how_to/tools_prompting/", "MLX": "https://python.langchain.com/docs/integrations/chat/mlx/"}, "RunnableSerializable": {"LangChain Expression Language Cheatsheet": "https://python.langchain.com/docs/how_to/lcel_cheatsheet/"}, "Run": {"LangChain Expression Language Cheatsheet": "https://python.langchain.com/docs/how_to/lcel_cheatsheet/"}, "MarkdownHeaderTextSplitter": {"How to split Markdown by Headers": "https://python.langchain.com/docs/how_to/markdown_header_metadata_splitter/"}, "HTMLHeaderTextSplitter": {"How to split by HTML header ": "https://python.langchain.com/docs/how_to/HTML_header_metadata_splitter/"}, "EnsembleRetriever": {"How to combine results from multiple retrievers": "https://python.langchain.com/docs/how_to/ensemble_retriever/"}, "BM25Retriever": {"How to combine results from multiple retrievers": "https://python.langchain.com/docs/how_to/ensemble_retriever/", "BM25": "https://python.langchain.com/docs/integrations/retrievers/bm25/", "Maritalk": "https://python.langchain.com/docs/integrations/chat/maritalk/"}, "ChatMistralAI": {"Response metadata": "https://python.langchain.com/docs/how_to/response_metadata/", "ChatMistralAI": "https://python.langchain.com/docs/integrations/chat/mistralai/", "MistralAI": "https://python.langchain.com/docs/integrations/providers/mistralai/", "Build an Extraction Chain": "https://python.langchain.com/docs/tutorials/extraction/"}, "ChatGroq": {"Response metadata": "https://python.langchain.com/docs/how_to/response_metadata/", "ChatGroq": "https://python.langchain.com/docs/integrations/chat/groq/"}, "set_verbose": {"How to debug your LLM apps": "https://python.langchain.com/docs/how_to/debugging/", "OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/"}, "set_debug": {"How to debug your LLM apps": "https://python.langchain.com/docs/how_to/debugging/", "Bittensor": "https://python.langchain.com/docs/integrations/llms/bittensor/", "TextGen": "https://python.langchain.com/docs/integrations/llms/textgen/", "OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/"}, "MaxMarginalRelevanceExampleSelector": {"How to select examples by maximal marginal relevance (MMR)": "https://python.langchain.com/docs/how_to/example_selectors_mmr/"}, "AttributeInfo": {"How to do \"self-querying\" retrieval": "https://python.langchain.com/docs/how_to/self_query/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/", "Milvus": "https://python.langchain.com/docs/integrations/retrievers/self_query/milvus_self_query/", "PGVector (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/pgvector_self_query/", "Weaviate": "https://python.langchain.com/docs/integrations/retrievers/self_query/weaviate_self_query/", "Vectara self-querying ": "https://python.langchain.com/docs/integrations/retrievers/self_query/vectara_self_query/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/retrievers/self_query/hanavector_self_query/", "DashVector": "https://python.langchain.com/docs/integrations/retrievers/self_query/dashvector/", "Databricks Vector Search": "https://python.langchain.com/docs/integrations/retrievers/self_query/databricks_vector_search/", "DingoDB": "https://python.langchain.com/docs/integrations/retrievers/self_query/dingo/", "OpenSearch": "https://python.langchain.com/docs/integrations/retrievers/self_query/opensearch_self_query/", "Elasticsearch": "https://python.langchain.com/docs/integrations/retrievers/self_query/elasticsearch_self_query/", "Chroma": "https://python.langchain.com/docs/integrations/retrievers/self_query/chroma_self_query/", "Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/retrievers/self_query/tencentvectordb/", "Timescale Vector (Postgres) ": "https://python.langchain.com/docs/integrations/retrievers/self_query/timescalevector_self_query/", "Astra DB (Cassandra)": "https://python.langchain.com/docs/integrations/retrievers/self_query/astradb/", "Pinecone": "https://python.langchain.com/docs/integrations/retrievers/self_query/pinecone/", "Supabase (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/supabase_self_query/", "Redis": "https://python.langchain.com/docs/integrations/retrievers/self_query/redis_self_query/", "MyScale": "https://python.langchain.com/docs/integrations/retrievers/self_query/myscale_self_query/", "Deep Lake": "https://python.langchain.com/docs/integrations/retrievers/self_query/activeloop_deeplake_self_query/", "MongoDB Atlas": "https://python.langchain.com/docs/integrations/retrievers/self_query/mongodb_atlas/", "Qdrant": "https://python.langchain.com/docs/integrations/retrievers/self_query/qdrant_self_query/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/", "self-query-qdrant": "https://python.langchain.com/docs/templates/self-query-qdrant/"}, "SelfQueryRetriever": {"How to do \"self-querying\" retrieval": "https://python.langchain.com/docs/how_to/self_query/", "How to add scores to retriever results": "https://python.langchain.com/docs/how_to/add_scores_retriever/", "Milvus": "https://python.langchain.com/docs/integrations/retrievers/self_query/milvus_self_query/", "PGVector (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/pgvector_self_query/", "Weaviate": "https://python.langchain.com/docs/integrations/retrievers/self_query/weaviate_self_query/", "Vectara self-querying ": "https://python.langchain.com/docs/integrations/retrievers/self_query/vectara_self_query/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/retrievers/self_query/hanavector_self_query/", "DashVector": "https://python.langchain.com/docs/integrations/retrievers/self_query/dashvector/", "Databricks Vector Search": "https://python.langchain.com/docs/integrations/retrievers/self_query/databricks_vector_search/", "DingoDB": "https://python.langchain.com/docs/integrations/retrievers/self_query/dingo/", "OpenSearch": "https://python.langchain.com/docs/integrations/retrievers/self_query/opensearch_self_query/", "Elasticsearch": "https://python.langchain.com/docs/integrations/retrievers/self_query/elasticsearch_self_query/", "Chroma": "https://python.langchain.com/docs/integrations/providers/chroma/", "Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/retrievers/self_query/tencentvectordb/", "Timescale Vector (Postgres) ": "https://python.langchain.com/docs/integrations/retrievers/self_query/timescalevector_self_query/", "Astra DB (Cassandra)": "https://python.langchain.com/docs/integrations/retrievers/self_query/astradb/", "Pinecone": "https://python.langchain.com/docs/integrations/retrievers/self_query/pinecone/", "Supabase (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/supabase_self_query/", "Redis": "https://python.langchain.com/docs/integrations/retrievers/self_query/redis_self_query/", "MyScale": "https://python.langchain.com/docs/integrations/retrievers/self_query/myscale_self_query/", "Deep Lake": "https://python.langchain.com/docs/integrations/retrievers/self_query/activeloop_deeplake_self_query/", "MongoDB Atlas": "https://python.langchain.com/docs/integrations/retrievers/self_query/mongodb_atlas/", "Qdrant": "https://python.langchain.com/docs/integrations/retrievers/self_query/qdrant_self_query/", "Astra DB": "https://python.langchain.com/docs/integrations/providers/astradb/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "Docugami": "https://python.langchain.com/docs/integrations/document_loaders/docugami/"}, "StructuredQueryOutputParser": {"How to do \"self-querying\" retrieval": "https://python.langchain.com/docs/how_to/self_query/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/retrievers/self_query/hanavector_self_query/"}, "get_query_constructor_prompt": {"How to do \"self-querying\" retrieval": "https://python.langchain.com/docs/how_to/self_query/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/retrievers/self_query/hanavector_self_query/"}, "add": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "cos": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "divide": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "log": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "multiply": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "negate": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "pi": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "power": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "sin": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "subtract": {"How to select examples from a LangSmith dataset": "https://python.langchain.com/docs/how_to/example_selectors_langsmith/"}, "adispatch_custom_event": {"How to dispatch custom callback events": "https://python.langchain.com/docs/how_to/callbacks_custom_events/"}, "dispatch_custom_event": {"How to dispatch custom callback events": "https://python.langchain.com/docs/how_to/callbacks_custom_events/"}, "Cassandra": {"Hybrid Search": "https://python.langchain.com/docs/how_to/hybrid/", "Cassandra": "https://python.langchain.com/docs/integrations/providers/cassandra/", "Apache Cassandra": "https://python.langchain.com/docs/integrations/vectorstores/cassandra/"}, "HTMLSectionSplitter": {"How to split by HTML sections": "https://python.langchain.com/docs/how_to/HTML_section_aware_splitter/"}, "JSONLoader": {"How to load JSON": "https://python.langchain.com/docs/how_to/document_loader_json/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/", "JSONLoader": "https://python.langchain.com/docs/integrations/document_loaders/json/"}, "UpstashRedisCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Upstash Vector": "https://python.langchain.com/docs/integrations/providers/upstash/"}, "RedisCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Redis": "https://python.langchain.com/docs/integrations/providers/redis/"}, "RedisSemanticCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Redis": "https://python.langchain.com/docs/integrations/providers/redis/"}, "GPTCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/"}, "MomentoCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Momento": "https://python.langchain.com/docs/integrations/providers/momento/"}, "SQLAlchemyCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/"}, "CassandraCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Cassandra": "https://python.langchain.com/docs/integrations/providers/cassandra/"}, "CassandraSemanticCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Cassandra": "https://python.langchain.com/docs/integrations/providers/cassandra/"}, "AzureCosmosDBSemanticCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/"}, "CosmosDBSimilarityType": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Azure Cosmos DB Mongo vCore": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db/"}, "CosmosDBVectorSearchType": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Azure Cosmos DB Mongo vCore": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db/"}, "load_summarize_chain": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/", "Infino": "https://python.langchain.com/docs/integrations/callbacks/infino/", "LarkSuite (FeiShu)": "https://python.langchain.com/docs/integrations/document_loaders/larksuite/"}, "OpenSearchSemanticCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/"}, "SingleStoreDBSemanticCache": {"Model caches": "https://python.langchain.com/docs/integrations/llm_caching/"}, "map_ai_messages": {"WeChat": "https://python.langchain.com/docs/integrations/chat_loaders/wechat/", "Facebook Messenger": "https://python.langchain.com/docs/integrations/chat_loaders/facebook/", "GMail": "https://python.langchain.com/docs/integrations/chat_loaders/gmail/", "Slack": "https://python.langchain.com/docs/integrations/chat_loaders/slack/", "WhatsApp": "https://python.langchain.com/docs/integrations/chat_loaders/whatsapp/", "iMessage": "https://python.langchain.com/docs/integrations/chat_loaders/imessage/", "Telegram": "https://python.langchain.com/docs/integrations/chat_loaders/telegram/", "Discord": "https://python.langchain.com/docs/integrations/chat_loaders/discord/"}, "merge_chat_runs": {"WeChat": "https://python.langchain.com/docs/integrations/chat_loaders/wechat/", "Facebook Messenger": "https://python.langchain.com/docs/integrations/chat_loaders/facebook/", "Slack": "https://python.langchain.com/docs/integrations/chat_loaders/slack/", "WhatsApp": "https://python.langchain.com/docs/integrations/chat_loaders/whatsapp/", "iMessage": "https://python.langchain.com/docs/integrations/chat_loaders/imessage/", "Telegram": "https://python.langchain.com/docs/integrations/chat_loaders/telegram/", "Discord": "https://python.langchain.com/docs/integrations/chat_loaders/discord/"}, "ChatSession": {"WeChat": "https://python.langchain.com/docs/integrations/chat_loaders/wechat/", "Slack": "https://python.langchain.com/docs/integrations/chat_loaders/slack/", "WhatsApp": "https://python.langchain.com/docs/integrations/chat_loaders/whatsapp/", "iMessage": "https://python.langchain.com/docs/integrations/chat_loaders/imessage/", "Telegram": "https://python.langchain.com/docs/integrations/chat_loaders/telegram/", "Discord": "https://python.langchain.com/docs/integrations/chat_loaders/discord/"}, "FolderFacebookMessengerChatLoader": {"Facebook Messenger": "https://python.langchain.com/docs/integrations/chat_loaders/facebook/", "Facebook - Meta": "https://python.langchain.com/docs/integrations/providers/facebook/"}, "SingleFileFacebookMessengerChatLoader": {"Facebook Messenger": "https://python.langchain.com/docs/integrations/chat_loaders/facebook/", "Facebook - Meta": "https://python.langchain.com/docs/integrations/providers/facebook/"}, "convert_messages_for_finetuning": {"Facebook Messenger": "https://python.langchain.com/docs/integrations/chat_loaders/facebook/", "LangSmith LLM Runs": "https://python.langchain.com/docs/integrations/chat_loaders/langsmith_llm_runs/", "LangSmith Chat Datasets": "https://python.langchain.com/docs/integrations/chat_loaders/langsmith_dataset/", "iMessage": "https://python.langchain.com/docs/integrations/chat_loaders/imessage/"}, "convert_message_to_dict": {"Twitter (via Apify)": "https://python.langchain.com/docs/integrations/chat_loaders/twitter/"}, "convert_pydantic_to_openai_function": {"LangSmith LLM Runs": "https://python.langchain.com/docs/integrations/chat_loaders/langsmith_llm_runs/"}, "PydanticOutputFunctionsParser": {"LangSmith LLM Runs": "https://python.langchain.com/docs/integrations/chat_loaders/langsmith_llm_runs/"}, "LangSmithRunChatLoader": {"LangSmith LLM Runs": "https://python.langchain.com/docs/integrations/chat_loaders/langsmith_llm_runs/"}, "GMailLoader": {"GMail": "https://python.langchain.com/docs/integrations/chat_loaders/gmail/"}, "SlackChatLoader": {"Slack": "https://python.langchain.com/docs/integrations/providers/slack/"}, "WhatsAppChatLoader": {"WhatsApp": "https://python.langchain.com/docs/integrations/providers/whatsapp/", "Facebook - Meta": "https://python.langchain.com/docs/integrations/providers/facebook/", "WhatsApp Chat": "https://python.langchain.com/docs/integrations/document_loaders/whatsapp_chat/"}, "LangSmithDatasetChatLoader": {"LangSmith Chat Datasets": "https://python.langchain.com/docs/integrations/chat_loaders/langsmith_dataset/"}, "IMessageChatLoader": {"iMessage": "https://python.langchain.com/docs/integrations/chat_loaders/imessage/"}, "TelegramChatLoader": {"Telegram": "https://python.langchain.com/docs/integrations/providers/telegram/"}, "BookendEmbeddings": {"Bookend AI": "https://python.langchain.com/docs/integrations/text_embedding/bookend/"}, "SolarEmbeddings": {"Solar": "https://python.langchain.com/docs/integrations/text_embedding/solar/"}, "HuggingFaceBgeEmbeddings": {"BGE on Hugging Face": "https://python.langchain.com/docs/integrations/text_embedding/bge_huggingface/", "Hugging Face": "https://python.langchain.com/docs/integrations/providers/huggingface/"}, "IpexLLMBgeEmbeddings": {"Local BGE Embeddings with IPEX-LLM on Intel CPU": "https://python.langchain.com/docs/integrations/text_embedding/ipex_llm/", "Local BGE Embeddings with IPEX-LLM on Intel GPU": "https://python.langchain.com/docs/integrations/text_embedding/ipex_llm_gpu/"}, "QuantizedBiEncoderEmbeddings": {"Embedding Documents using Optimized and Quantized Embedders": "https://python.langchain.com/docs/integrations/text_embedding/optimum_intel/", "Intel": "https://python.langchain.com/docs/integrations/providers/intel/"}, "XinferenceEmbeddings": {"Xorbits inference (Xinference)": "https://python.langchain.com/docs/integrations/text_embedding/xinference/"}, "LLMRailsEmbeddings": {"LLMRails": "https://python.langchain.com/docs/integrations/text_embedding/llm_rails/"}, "AscendEmbeddings": {"# Related": "https://python.langchain.com/docs/integrations/text_embedding/ascend/", "Ascend": "https://python.langchain.com/docs/integrations/providers/ascend/"}, "DeepInfraEmbeddings": {"DeepInfra": "https://python.langchain.com/docs/integrations/providers/deepinfra/"}, "HuggingFaceInferenceAPIEmbeddings": {"Hugging Face": "https://python.langchain.com/docs/integrations/text_embedding/huggingfacehub/"}, "GPT4AllEmbeddings": {"GPT4All": "https://python.langchain.com/docs/integrations/text_embedding/gpt4all/", "ManticoreSearch VectorStore": "https://python.langchain.com/docs/integrations/vectorstores/manticore_search/"}, "MosaicMLInstructorEmbeddings": {"MosaicML": "https://python.langchain.com/docs/integrations/text_embedding/mosaicml/"}, "QuantizedBgeEmbeddings": {"Intel® Extension for Transformers Quantized Text Embeddings": "https://python.langchain.com/docs/integrations/text_embedding/itrex/", "Intel": "https://python.langchain.com/docs/integrations/providers/intel/"}, "BedrockEmbeddings": {"Bedrock": "https://python.langchain.com/docs/integrations/text_embedding/bedrock/", "AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Amazon MemoryDB": "https://python.langchain.com/docs/integrations/vectorstores/memorydb/"}, "GigaChatEmbeddings": {"GigaChat": "https://python.langchain.com/docs/integrations/text_embedding/gigachat/", "Salute Devices": "https://python.langchain.com/docs/integrations/providers/salute_devices/"}, "OCIGenAIEmbeddings": {"Oracle Cloud Infrastructure Generative AI": "https://python.langchain.com/docs/integrations/text_embedding/oci_generative_ai/", "Oracle Cloud Infrastructure (OCI)": "https://python.langchain.com/docs/integrations/providers/oci/"}, "OVHCloudEmbeddings": {"OVHcloud": "https://python.langchain.com/docs/integrations/text_embedding/ovhcloud/"}, "FastEmbedEmbeddings": {"FastEmbed by Qdrant": "https://python.langchain.com/docs/integrations/text_embedding/fastembed/"}, "LlamaCppEmbeddings": {"Llama.cpp": "https://python.langchain.com/docs/integrations/providers/llamacpp/"}, "NLPCloudEmbeddings": {"NLP Cloud": "https://python.langchain.com/docs/integrations/text_embedding/nlp_cloud/", "NLPCloud": "https://python.langchain.com/docs/integrations/providers/nlpcloud/"}, "TextEmbedEmbeddings": {"TextEmbed - Embedding Inference Server": "https://python.langchain.com/docs/integrations/text_embedding/textembed/"}, "LaserEmbeddings": {"LASER Language-Agnostic SEntence Representations Embeddings by Meta AI": "https://python.langchain.com/docs/integrations/text_embedding/laser/", "Facebook - Meta": "https://python.langchain.com/docs/integrations/providers/facebook/"}, "OpenCLIPEmbeddings": {"OpenClip": "https://python.langchain.com/docs/integrations/text_embedding/open_clip/", "LanceDB": "https://python.langchain.com/docs/integrations/vectorstores/lancedb/", "SingleStoreDB": "https://python.langchain.com/docs/integrations/vectorstores/singlestoredb/"}, "TitanTakeoffEmbed": {"Titan Takeoff": "https://python.langchain.com/docs/integrations/text_embedding/titan_takeoff/"}, "MistralAIEmbeddings": {"MistralAIEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/mistralai/", "MistralAI": "https://python.langchain.com/docs/integrations/providers/mistralai/"}, "SpacyEmbeddings": {"SpaCy": "https://python.langchain.com/docs/integrations/text_embedding/spacy_embedding/", "NanoPQ (Product Quantization)": "https://python.langchain.com/docs/integrations/retrievers/nanopq/", "spaCy": "https://python.langchain.com/docs/integrations/providers/spacy/"}, "DatabricksEmbeddings": {"Databricks": "https://python.langchain.com/docs/integrations/text_embedding/databricks/"}, "BaichuanTextEmbeddings": {"Baichuan Text Embeddings": "https://python.langchain.com/docs/integrations/text_embedding/baichuan/", "Baichuan": "https://python.langchain.com/docs/integrations/providers/baichuan/"}, "TogetherEmbeddings": {"TogetherEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/together/"}, "HuggingFaceInstructEmbeddings": {"Instruct Embeddings on Hugging Face": "https://python.langchain.com/docs/integrations/text_embedding/instruct_embeddings/", "Hugging Face": "https://python.langchain.com/docs/integrations/providers/huggingface/"}, "OracleEmbeddings": {"Oracle AI Vector Search: Generate Embeddings": "https://python.langchain.com/docs/integrations/text_embedding/oracleai/", "OracleAI Vector Search": "https://python.langchain.com/docs/integrations/providers/oracleai/"}, "QianfanEmbeddingsEndpoint": {"Baidu Qianfan": "https://python.langchain.com/docs/integrations/text_embedding/baidu_qianfan_endpoint/", "ERNIE": "https://python.langchain.com/docs/integrations/text_embedding/ernie/", "Baidu": "https://python.langchain.com/docs/integrations/providers/baidu/", "Baidu Cloud ElasticSearch VectorSearch": "https://python.langchain.com/docs/integrations/vectorstores/baiducloud_vector_search/"}, "EdenAiEmbeddings": {"EDEN AI": "https://python.langchain.com/docs/integrations/text_embedding/edenai/", "Eden AI": "https://python.langchain.com/docs/integrations/providers/edenai/"}, "JohnSnowLabsEmbeddings": {"John Snow Labs": "https://python.langchain.com/docs/integrations/text_embedding/johnsnowlabs_embedding/"}, "ErnieEmbeddings": {"ERNIE": "https://python.langchain.com/docs/integrations/text_embedding/ernie/"}, "ClarifaiEmbeddings": {"Clarifai": "https://python.langchain.com/docs/integrations/providers/clarifai/"}, "AzureOpenAIEmbeddings": {"AzureOpenAIEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/azureopenai/", "AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure Cosmos DB No SQL": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db_no_sql/", "Azure AI Search": "https://python.langchain.com/docs/integrations/vectorstores/azuresearch/"}, "InfinityEmbeddings": {"Infinity": "https://python.langchain.com/docs/integrations/providers/infinity/"}, "InfinityEmbeddingsLocal": {"Infinity": "https://python.langchain.com/docs/integrations/text_embedding/infinity/"}, "AwaEmbeddings": {"AwaDB": "https://python.langchain.com/docs/integrations/providers/awadb/"}, "VolcanoEmbeddings": {"Volc Engine": "https://python.langchain.com/docs/integrations/text_embedding/volcengine/"}, "MiniMaxEmbeddings": {"MiniMax": "https://python.langchain.com/docs/integrations/text_embedding/minimax/", "Minimax": "https://python.langchain.com/docs/integrations/providers/minimax/"}, "FakeEmbeddings": {"Fake Embeddings": "https://python.langchain.com/docs/integrations/text_embedding/fake/", "DocArray": "https://python.langchain.com/docs/integrations/retrievers/docarray_retriever/", "Relyt": "https://python.langchain.com/docs/integrations/vectorstores/relyt/", "Tair": "https://python.langchain.com/docs/integrations/vectorstores/tair/", "Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/tencentvectordb/", "Google Memorystore for Redis": "https://python.langchain.com/docs/integrations/vectorstores/google_memorystore_redis/", "PGVecto.rs": "https://python.langchain.com/docs/integrations/vectorstores/pgvecto_rs/", "Baidu VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/baiduvectordb/"}, "ClovaEmbeddings": {"Clova Embeddings": "https://python.langchain.com/docs/integrations/text_embedding/clova/"}, "NeMoEmbeddings": {"NVIDIA NeMo embeddings": "https://python.langchain.com/docs/integrations/text_embedding/nemo/"}, "SparkLLMTextEmbeddings": {"SparkLLM Text Embeddings": "https://python.langchain.com/docs/integrations/text_embedding/sparkllm/", "iFlytek": "https://python.langchain.com/docs/integrations/providers/iflytek/"}, "PremAIEmbeddings": {"PremAI": "https://python.langchain.com/docs/integrations/text_embedding/premai/"}, "KNNRetriever": {"Voyage AI": "https://python.langchain.com/docs/integrations/text_embedding/voyageai/", "kNN": "https://python.langchain.com/docs/integrations/retrievers/knn/"}, "SelfHostedEmbeddings": {"Self Hosted": "https://python.langchain.com/docs/integrations/text_embedding/self-hosted/"}, "SelfHostedHuggingFaceEmbeddings": {"Self Hosted": "https://python.langchain.com/docs/integrations/text_embedding/self-hosted/"}, "SelfHostedHuggingFaceInstructEmbeddings": {"Self Hosted": "https://python.langchain.com/docs/integrations/text_embedding/self-hosted/"}, "AnyscaleEmbeddings": {"Anyscale": "https://python.langchain.com/docs/integrations/providers/anyscale/"}, "EmbaasEmbeddings": {"Embaas": "https://python.langchain.com/docs/integrations/text_embedding/embaas/"}, "YandexGPTEmbeddings": {"YandexGPT": "https://python.langchain.com/docs/integrations/text_embedding/yandex/"}, "JinaEmbeddings": {"Jina": "https://python.langchain.com/docs/integrations/providers/jina/", "Jina Reranker": "https://python.langchain.com/docs/integrations/document_transformers/jina_rerank/"}, "AlephAlphaAsymmetricSemanticEmbedding": {"Aleph Alpha": "https://python.langchain.com/docs/integrations/providers/aleph_alpha/"}, "AlephAlphaSymmetricSemanticEmbedding": {"Aleph Alpha": "https://python.langchain.com/docs/integrations/providers/aleph_alpha/"}, "CloudflareWorkersAIEmbeddings": {"Cloudflare Workers AI": "https://python.langchain.com/docs/integrations/text_embedding/cloudflare_workersai/", "Cloudflare": "https://python.langchain.com/docs/integrations/providers/cloudflare/"}, "DashScopeEmbeddings": {"DashScope": "https://python.langchain.com/docs/integrations/text_embedding/dashscope/", "DashVector": "https://python.langchain.com/docs/integrations/vectorstores/dashvector/", "DashScope Reranker": "https://python.langchain.com/docs/integrations/document_transformers/dashscope_rerank/"}, "TensorflowHubEmbeddings": {"TensorFlow Hub": "https://python.langchain.com/docs/integrations/text_embedding/tensorflowhub/"}, "LlamafileEmbeddings": {"llamafile": "https://python.langchain.com/docs/integrations/text_embedding/llamafile/"}, "GradientEmbeddings": {"Gradient": "https://python.langchain.com/docs/integrations/providers/gradient/"}, "ModelScopeEmbeddings": {"ModelScope": "https://python.langchain.com/docs/integrations/providers/modelscope/"}, "SagemakerEndpointEmbeddings": {"SageMaker": "https://python.langchain.com/docs/integrations/text_embedding/sagemaker-endpoint/", "AWS": "https://python.langchain.com/docs/integrations/providers/aws/"}, "EmbeddingsContentHandler": {"SageMaker": "https://python.langchain.com/docs/integrations/text_embedding/sagemaker-endpoint/"}, "DocArrayInMemorySearch": {"UpstageEmbeddings": "https://python.langchain.com/docs/integrations/text_embedding/upstage/", "DocArray InMemorySearch": "https://python.langchain.com/docs/integrations/vectorstores/docarray_in_memory/"}, "SambaStudioEmbeddings": {"SambaNova": "https://python.langchain.com/docs/integrations/text_embedding/sambanova/"}, "OpenVINOEmbeddings": {"OpenVINO": "https://python.langchain.com/docs/integrations/text_embedding/openvino/", "OpenVINO Reranker": "https://python.langchain.com/docs/integrations/document_transformers/openvino_rerank/"}, "OpenVINOBgeEmbeddings": {"OpenVINO": "https://python.langchain.com/docs/integrations/text_embedding/openvino/"}, "LocalAIEmbeddings": {"LocalAI": "https://python.langchain.com/docs/integrations/text_embedding/localai/"}, "AzureAISearchRetriever": {"AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/"}, "AzureSearch": {"AzureAISearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/azure_ai_search/", "Azure AI Search": "https://python.langchain.com/docs/integrations/vectorstores/azuresearch/"}, "RePhraseQueryRetriever": {"RePhraseQuery": "https://python.langchain.com/docs/integrations/retrievers/re_phrase/"}, "YouSearchAPIWrapper": {"You.com": "https://python.langchain.com/docs/integrations/retrievers/you-retriever/", "You.com Search": "https://python.langchain.com/docs/integrations/tools/you/"}, "YouRetriever": {"You.com": "https://python.langchain.com/docs/integrations/retrievers/you-retriever/"}, "Kinetica": {"Kinetica Vectorstore based Retriever": "https://python.langchain.com/docs/integrations/retrievers/kinetica/", "Kinetica": "https://python.langchain.com/docs/integrations/providers/kinetica/", "Kinetica Vectorstore API": "https://python.langchain.com/docs/integrations/vectorstores/kinetica/"}, "KineticaSettings": {"Kinetica Vectorstore based Retriever": "https://python.langchain.com/docs/integrations/retrievers/kinetica/", "Kinetica Vectorstore API": "https://python.langchain.com/docs/integrations/vectorstores/kinetica/", "Kinetica": "https://python.langchain.com/docs/integrations/document_loaders/kinetica/"}, "Jaguar": {"JaguarDB Vector Database": "https://python.langchain.com/docs/integrations/retrievers/jaguar/", "Jaguar": "https://python.langchain.com/docs/integrations/providers/jaguar/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/"}, "BaseStore": {"Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/"}, "VectorStore": {"Fleet AI Context": "https://python.langchain.com/docs/integrations/retrievers/fleet_context/"}, "AskNewsRetriever": {"AskNews": "https://python.langchain.com/docs/integrations/retrievers/asknews/"}, "LLMLinguaCompressor": {"LLMLingua Document Compressor": "https://python.langchain.com/docs/integrations/retrievers/llmlingua/"}, "ElasticSearchBM25Retriever": {"ElasticSearch BM25": "https://python.langchain.com/docs/integrations/retrievers/elastic_search_bm25/"}, "OutlineRetriever": {"Outline": "https://python.langchain.com/docs/integrations/providers/outline/"}, "ZepMemory": {"Zep Open Source": "https://python.langchain.com/docs/integrations/retrievers/zep_memorystore/", "Zep Open Source Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory/"}, "SearchScope": {"Zep Open Source": "https://python.langchain.com/docs/integrations/retrievers/zep_memorystore/"}, "ZepRetriever": {"Zep Open Source": "https://python.langchain.com/docs/integrations/retrievers/zep_memorystore/", "Zep Open Source Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory/"}, "VespaRetriever": {"Vespa": "https://python.langchain.com/docs/integrations/providers/vespa/"}, "AmazonKendraRetriever": {"Amazon Kendra": "https://python.langchain.com/docs/integrations/retrievers/amazon_kendra_retriever/", "AWS": "https://python.langchain.com/docs/integrations/providers/aws/"}, "AmazonKnowledgeBasesRetriever": {"Bedrock (Knowledge Bases) Retriever": "https://python.langchain.com/docs/integrations/retrievers/bedrock/", "AWS": "https://python.langchain.com/docs/integrations/providers/aws/"}, "Bedrock": {"Bedrock (Knowledge Bases) Retriever": "https://python.langchain.com/docs/integrations/retrievers/bedrock/"}, "CohereEmbeddings": {"Cohere reranker": "https://python.langchain.com/docs/integrations/retrievers/cohere-reranker/"}, "Cohere": {"Cohere reranker": "https://python.langchain.com/docs/integrations/retrievers/cohere-reranker/", "self-query-qdrant": "https://python.langchain.com/docs/templates/self-query-qdrant/"}, "ZepCloudMemory": {"Zep Cloud": "https://python.langchain.com/docs/integrations/retrievers/zep_cloud_memorystore/", "ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "Zep Cloud Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory_cloud/"}, "ZepCloudRetriever": {"Zep Cloud": "https://python.langchain.com/docs/integrations/retrievers/zep_cloud_memorystore/", "Zep Cloud Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory_cloud/", "Zep": "https://python.langchain.com/docs/integrations/providers/zep/"}, "NeuralDBRetriever": {"**NeuralDB**": "https://python.langchain.com/docs/integrations/retrievers/thirdai_neuraldb/"}, "SingleStoreDB": {"SingleStoreDB": "https://python.langchain.com/docs/integrations/vectorstores/singlestoredb/"}, "MetalRetriever": {"Metal": "https://python.langchain.com/docs/integrations/providers/metal/"}, "BreebsRetriever": {"BREEBS (Open Knowledge)": "https://python.langchain.com/docs/integrations/retrievers/breebs/"}, "NanoPQRetriever": {"NanoPQ (Product Quantization)": "https://python.langchain.com/docs/integrations/retrievers/nanopq/"}, "ChatGPTPluginRetriever": {"ChatGPT plugin": "https://python.langchain.com/docs/integrations/retrievers/chatgpt-plugin/", "OpenAI": "https://python.langchain.com/docs/integrations/providers/openai/"}, "KayAiRetriever": {"SEC filing": "https://python.langchain.com/docs/integrations/retrievers/sec_filings/", "Kay.ai": "https://python.langchain.com/docs/integrations/retrievers/kay/"}, "DriaRetriever": {"Dria": "https://python.langchain.com/docs/integrations/retrievers/dria_index/"}, "DocArrayRetriever": {"DocArray": "https://python.langchain.com/docs/integrations/retrievers/docarray_retriever/"}, "SVMRetriever": {"SVM": "https://python.langchain.com/docs/integrations/retrievers/svm/", "scikit-learn": "https://python.langchain.com/docs/integrations/providers/sklearn/"}, "TavilySearchAPIRetriever": {"TavilySearchAPIRetriever": "https://python.langchain.com/docs/integrations/retrievers/tavily/"}, "PineconeHybridSearchRetriever": {"Pinecone Hybrid Search": "https://python.langchain.com/docs/integrations/retrievers/pinecone_hybrid_search/", "Pinecone": "https://python.langchain.com/docs/integrations/providers/pinecone/"}, "DeepLake": {"Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "Deep Lake": "https://python.langchain.com/docs/integrations/retrievers/self_query/activeloop_deeplake_self_query/", "Activeloop Deep Lake": "https://python.langchain.com/docs/integrations/vectorstores/activeloop_deeplake/"}, "AsyncHtmlLoader": {"Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "HTML to text": "https://python.langchain.com/docs/integrations/document_transformers/html2text/", "Markdownify": "https://python.langchain.com/docs/integrations/document_transformers/markdownify/", "AsyncHtml": "https://python.langchain.com/docs/integrations/document_loaders/async_html/"}, "Html2TextTransformer": {"Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "HTML to text": "https://python.langchain.com/docs/integrations/document_transformers/html2text/", "Async Chromium": "https://python.langchain.com/docs/integrations/document_loaders/async_chromium/"}, "create_structured_output_chain": {"Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/"}, "HumanMessagePromptTemplate": {"Activeloop Deep Memory": "https://python.langchain.com/docs/integrations/retrievers/activeloop/", "JinaChat": "https://python.langchain.com/docs/integrations/chat/jinachat/", "Llama2Chat": "https://python.langchain.com/docs/integrations/chat/llama2_chat/", "vLLM Chat": "https://python.langchain.com/docs/integrations/chat/vllm/", "Context": "https://python.langchain.com/docs/integrations/callbacks/context/", "Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/", "Figma": "https://python.langchain.com/docs/integrations/document_loaders/figma/"}, "PubMedRetriever": {"PubMed": "https://python.langchain.com/docs/integrations/providers/pubmed/"}, "WeaviateHybridSearchRetriever": {"Weaviate Hybrid Search": "https://python.langchain.com/docs/integrations/retrievers/weaviate-hybrid/"}, "EmbedchainRetriever": {"Embedchain": "https://python.langchain.com/docs/integrations/retrievers/embedchain/"}, "ArxivRetriever": {"ArxivRetriever": "https://python.langchain.com/docs/integrations/retrievers/arxiv/", "Arxiv": "https://python.langchain.com/docs/integrations/providers/arxiv/"}, "QdrantSparseVectorRetriever": {"Qdrant Sparse Vector": "https://python.langchain.com/docs/integrations/retrievers/qdrant-sparse/"}, "DeterministicFakeEmbedding": {"ElasticsearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/elasticsearch_retriever/"}, "Embeddings": {"ElasticsearchRetriever": "https://python.langchain.com/docs/integrations/retrievers/elasticsearch_retriever/", "Infinispan": "https://python.langchain.com/docs/integrations/vectorstores/infinispanvs/"}, "RememberizerRetriever": {"Rememberizer": "https://python.langchain.com/docs/integrations/retrievers/rememberizer/"}, "ArceeRetriever": {"Arcee": "https://python.langchain.com/docs/integrations/providers/arcee/"}, "FlashrankRerank": {"FlashRank reranker": "https://python.langchain.com/docs/integrations/retrievers/flashrank-reranker/", "UpTrain": "https://python.langchain.com/docs/integrations/callbacks/uptrain/"}, "ChaindeskRetriever": {"Chaindesk": "https://python.langchain.com/docs/integrations/providers/chaindesk/"}, "MergerRetriever": {"LOTR (Merger Retriever)": "https://python.langchain.com/docs/integrations/retrievers/merger_retriever/"}, "EmbeddingsClusteringFilter": {"LOTR (Merger Retriever)": "https://python.langchain.com/docs/integrations/retrievers/merger_retriever/"}, "TFIDFRetriever": {"TF-IDF": "https://python.langchain.com/docs/integrations/retrievers/tf_idf/"}, "PGVector": {"PGVector (Postgres)": "https://python.langchain.com/docs/integrations/retrievers/self_query/pgvector_self_query/", "PGVector": "https://python.langchain.com/docs/integrations/providers/pgvector/"}, "Weaviate": {"Weaviate": "https://python.langchain.com/docs/integrations/retrievers/self_query/weaviate_self_query/"}, "Vectara": {"Vectara self-querying ": "https://python.langchain.com/docs/integrations/retrievers/self_query/vectara_self_query/", "Vectara": "https://python.langchain.com/docs/integrations/vectorstores/vectara/", "Vectara Chat": "https://python.langchain.com/docs/integrations/providers/vectara/vectara_chat/"}, "HanaDB": {"SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/", "SAP": "https://python.langchain.com/docs/integrations/providers/sap/"}, "HanaTranslator": {"SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/retrievers/self_query/hanavector_self_query/"}, "DashVector": {"DashVector": "https://python.langchain.com/docs/integrations/vectorstores/dashvector/"}, "Tongyi": {"DashVector": "https://python.langchain.com/docs/integrations/retrievers/self_query/dashvector/", "Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/", "Tongyi Qwen": "https://python.langchain.com/docs/integrations/llms/tongyi/"}, "DatabricksVectorSearch": {"Databricks Vector Search": "https://python.langchain.com/docs/integrations/vectorstores/databricks_vector_search/"}, "Dingo": {"DingoDB": "https://python.langchain.com/docs/integrations/vectorstores/dingo/"}, "OpenSearchVectorSearch": {"OpenSearch": "https://python.langchain.com/docs/integrations/vectorstores/opensearch/", "AWS": "https://python.langchain.com/docs/integrations/providers/aws/"}, "ConnectionParams": {"Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/tencentvectordb/", "Baidu VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/baiduvectordb/"}, "MetaField": {"Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/tencentvectordb/"}, "TencentVectorDB": {"Tencent Cloud VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/tencentvectordb/", "Tencent": "https://python.langchain.com/docs/integrations/providers/tencent/"}, "TimescaleVector": {"Timescale Vector (Postgres) ": "https://python.langchain.com/docs/integrations/retrievers/self_query/timescalevector_self_query/", "Timescale Vector (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/timescalevector/"}, "AstraDB": {"Astra DB (Cassandra)": "https://python.langchain.com/docs/integrations/retrievers/self_query/astradb/"}, "SupabaseVectorStore": {"Supabase (Postgres)": "https://python.langchain.com/docs/integrations/vectorstores/supabase/"}, "Redis": {"Redis": "https://python.langchain.com/docs/integrations/vectorstores/redis/"}, "MyScale": {"MyScale": "https://python.langchain.com/docs/integrations/vectorstores/myscale/"}, "MongoDBAtlasVectorSearch": {"MongoDB Atlas": "https://python.langchain.com/docs/integrations/retrievers/self_query/mongodb_atlas/"}, "Qdrant": {"Qdrant": "https://python.langchain.com/docs/integrations/retrievers/self_query/qdrant_self_query/", "Identity-enabled RAG using PebbloRetrievalQA": "https://python.langchain.com/docs/integrations/providers/pebblo/pebblo_retrieval_qa/"}, "AzureChatOpenAI": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "OpenAI": "https://python.langchain.com/docs/integrations/providers/openai/", "Bing Search": "https://python.langchain.com/docs/integrations/tools/bing_search/", "AzureChatOpenAI": "https://python.langchain.com/docs/integrations/chat/azure_chat_openai/", "Azure Cosmos DB for Apache Gremlin": "https://python.langchain.com/docs/integrations/graphs/azure_cosmosdb_gremlin/"}, "AzureMLOnlineEndpoint": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure ML": "https://python.langchain.com/docs/integrations/llms/azure_ml/"}, "AzureOpenAI": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "OpenAI": "https://python.langchain.com/docs/integrations/providers/openai/", "Azure OpenAI": "https://python.langchain.com/docs/integrations/llms/azure_openai/"}, "AzureAIDataLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure AI Data": "https://python.langchain.com/docs/integrations/document_loaders/azure_ai_data/"}, "AzureBlobStorageContainerLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure Blob Storage Container": "https://python.langchain.com/docs/integrations/document_loaders/azure_blob_storage_container/"}, "AzureBlobStorageFileLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure Blob Storage File": "https://python.langchain.com/docs/integrations/document_loaders/azure_blob_storage_file/"}, "OneDriveLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Microsoft OneDrive": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_onedrive/"}, "OneDriveFileLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/"}, "UnstructuredWordDocumentLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "Microsoft Word": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_word/"}, "UnstructuredExcelLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "Microsoft Excel": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_excel/"}, "SharePointLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Microsoft SharePoint": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_sharepoint/"}, "UnstructuredPowerPointLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "Microsoft PowerPoint": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_powerpoint/"}, "OneNoteLoader": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Microsoft OneNote": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_onenote/"}, "AzureCosmosDBVectorSearch": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure Cosmos DB Mongo vCore": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db/"}, "BingSearchResults": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Bing Search": "https://python.langchain.com/docs/integrations/tools/bing_search/"}, "BingSearchAPIWrapper": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Bing Search": "https://python.langchain.com/docs/integrations/tools/bing_search/"}, "O365Toolkit": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Office365 Toolkit": "https://python.langchain.com/docs/integrations/tools/office365/"}, "PowerBIToolkit": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "PowerBI Toolkit": "https://python.langchain.com/docs/integrations/tools/powerbi/"}, "PowerBIDataset": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "PowerBI Toolkit": "https://python.langchain.com/docs/integrations/tools/powerbi/"}, "PlayWrightBrowserToolkit": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "PlayWright Browser Toolkit": "https://python.langchain.com/docs/integrations/tools/playwright/"}, "GremlinGraph": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure Cosmos DB for Apache Gremlin": "https://python.langchain.com/docs/integrations/graphs/azure_cosmosdb_gremlin/"}, "GraphDocument": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure Cosmos DB for Apache Gremlin": "https://python.langchain.com/docs/integrations/graphs/azure_cosmosdb_gremlin/"}, "Node": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure Cosmos DB for Apache Gremlin": "https://python.langchain.com/docs/integrations/graphs/azure_cosmosdb_gremlin/"}, "Relationship": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/", "Azure Cosmos DB for Apache Gremlin": "https://python.langchain.com/docs/integrations/graphs/azure_cosmosdb_gremlin/"}, "PresidioAnonymizer": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/"}, "PresidioReversibleAnonymizer": {"Microsoft": "https://python.langchain.com/docs/integrations/providers/microsoft/"}, "BedrockLLM": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Bedrock": "https://python.langchain.com/docs/integrations/llms/bedrock/"}, "AmazonAPIGateway": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Amazon API Gateway": "https://python.langchain.com/docs/integrations/llms/amazon_api_gateway/"}, "SagemakerEndpoint": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "SageMakerEndpoint": "https://python.langchain.com/docs/integrations/llms/sagemaker/"}, "ContentHandlerBase": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/"}, "S3DirectoryLoader": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "AWS S3 Directory": "https://python.langchain.com/docs/integrations/document_loaders/aws_s3_directory/"}, "S3FileLoader": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "AWS S3 File": "https://python.langchain.com/docs/integrations/document_loaders/aws_s3_file/"}, "AmazonTextractPDFLoader": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Amazon Textract ": "https://python.langchain.com/docs/integrations/document_loaders/amazon_textract/"}, "AthenaLoader": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Athena": "https://python.langchain.com/docs/integrations/document_loaders/athena/"}, "GlueCatalogLoader": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Glue Catalog": "https://python.langchain.com/docs/integrations/document_loaders/glue_catalog/"}, "DynamoDBChatMessageHistory": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "AWS DynamoDB": "https://python.langchain.com/docs/integrations/memory/aws_dynamodb/"}, "NeptuneGraph": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Amazon Neptune with Cypher": "https://python.langchain.com/docs/integrations/graphs/amazon_neptune_open_cypher/"}, "NeptuneAnalyticsGraph": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Amazon Neptune with Cypher": "https://python.langchain.com/docs/integrations/graphs/amazon_neptune_open_cypher/"}, "NeptuneOpenCypherQAChain": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Amazon Neptune with Cypher": "https://python.langchain.com/docs/integrations/graphs/amazon_neptune_open_cypher/"}, "NeptuneRdfGraph": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Amazon Neptune with SPARQL": "https://python.langchain.com/docs/integrations/graphs/amazon_neptune_sparql/"}, "NeptuneSparqlQAChain": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "Amazon Neptune with SPARQL": "https://python.langchain.com/docs/integrations/graphs/amazon_neptune_sparql/"}, "SageMakerCallbackHandler": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/", "SageMaker Tracking": "https://python.langchain.com/docs/integrations/callbacks/sagemaker_tracking/"}, "AmazonComprehendModerationChain": {"AWS": "https://python.langchain.com/docs/integrations/providers/aws/"}, "HuggingFaceHubEmbeddings": {"Hugging Face": "https://python.langchain.com/docs/integrations/providers/huggingface/"}, "HuggingFaceDatasetLoader": {"Hugging Face": "https://python.langchain.com/docs/integrations/providers/huggingface/", "HuggingFace dataset": "https://python.langchain.com/docs/integrations/document_loaders/hugging_face_dataset/"}, "load_huggingface_tool": {"Hugging Face": "https://python.langchain.com/docs/integrations/providers/huggingface/", "HuggingFace Hub Tools": "https://python.langchain.com/docs/integrations/tools/huggingface_tools/"}, "ChatGPTLoader": {"OpenAI": "https://python.langchain.com/docs/integrations/providers/openai/", "ChatGPT Data": "https://python.langchain.com/docs/integrations/document_loaders/chatgpt_loader/"}, "DallEAPIWrapper": {"OpenAI": "https://python.langchain.com/docs/integrations/providers/openai/", "Dall-E Image Generator": "https://python.langchain.com/docs/integrations/tools/dalle_image_generator/"}, "OpenAIModerationChain": {"OpenAI": "https://python.langchain.com/docs/integrations/providers/openai/"}, "GooglePalmEmbeddings": {"Google": "https://python.langchain.com/docs/integrations/providers/google/"}, "ScaNN": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "ScaNN": "https://python.langchain.com/docs/integrations/vectorstores/scann/"}, "GoogleVertexAISearchRetriever": {"Google": "https://python.langchain.com/docs/integrations/providers/google/"}, "GoogleDocumentAIWarehouseRetriever": {"Google": "https://python.langchain.com/docs/integrations/providers/google/"}, "GoogleFinanceQueryRun": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Finance": "https://python.langchain.com/docs/integrations/tools/google_finance/"}, "GoogleFinanceAPIWrapper": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Finance": "https://python.langchain.com/docs/integrations/tools/google_finance/"}, "GoogleJobsQueryRun": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Jobs": "https://python.langchain.com/docs/integrations/tools/google_jobs/"}, "GoogleLensQueryRun": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Lens": "https://python.langchain.com/docs/integrations/tools/google_lens/"}, "GoogleLensAPIWrapper": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Lens": "https://python.langchain.com/docs/integrations/tools/google_lens/"}, "GooglePlacesTool": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Places": "https://python.langchain.com/docs/integrations/tools/google_places/"}, "GoogleScholarQueryRun": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Scholar": "https://python.langchain.com/docs/integrations/tools/google_scholar/"}, "GoogleScholarAPIWrapper": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Scholar": "https://python.langchain.com/docs/integrations/tools/google_scholar/"}, "GoogleTrendsQueryRun": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Trends": "https://python.langchain.com/docs/integrations/tools/google_trends/"}, "GoogleTrendsAPIWrapper": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Trends": "https://python.langchain.com/docs/integrations/tools/google_trends/"}, "SearchApiAPIWrapper": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "SearchApi": "https://python.langchain.com/docs/integrations/providers/searchapi/"}, "SerpAPIWrapper": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "SerpAPI": "https://python.langchain.com/docs/integrations/providers/serpapi/", "MLX": "https://python.langchain.com/docs/integrations/chat/mlx/"}, "GoogleSerperAPIWrapper": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "Google Serper": "https://python.langchain.com/docs/integrations/tools/google_serper/", "Serper - Google Search API": "https://python.langchain.com/docs/integrations/providers/google_serper/"}, "YouTubeSearchTool": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "YouTube": "https://python.langchain.com/docs/integrations/tools/youtube/"}, "YoutubeAudioLoader": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "YouTube audio": "https://python.langchain.com/docs/integrations/document_loaders/youtube_audio/"}, "OpenAIWhisperParser": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "YouTube audio": "https://python.langchain.com/docs/integrations/document_loaders/youtube_audio/"}, "YoutubeLoader": {"Google": "https://python.langchain.com/docs/integrations/providers/google/", "YouTube": "https://python.langchain.com/docs/integrations/providers/youtube/", "YouTube transcripts": "https://python.langchain.com/docs/integrations/document_loaders/youtube_transcript/", "Build a Query Analysis System": "https://python.langchain.com/docs/tutorials/query_analysis/"}, "AnthropicLLM": {"Anthropic": "https://python.langchain.com/docs/integrations/providers/anthropic/", "AnthropicLLM": "https://python.langchain.com/docs/integrations/llms/anthropic/"}, "AIPluginTool": {"ChatGPT Plugins": "https://python.langchain.com/docs/integrations/tools/chatgpt_plugins/"}, "AgentType": {"ChatGPT Plugins": "https://python.langchain.com/docs/integrations/tools/chatgpt_plugins/", "Connery Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/connery/", "Jira Toolkit": "https://python.langchain.com/docs/integrations/tools/jira/", "Google Jobs": "https://python.langchain.com/docs/integrations/tools/google_jobs/", "Google Serper": "https://python.langchain.com/docs/integrations/tools/google_serper/", "Azure Cognitive Services Toolkit": "https://python.langchain.com/docs/integrations/tools/azure_cognitive_services/", "E2B Data Analysis": "https://python.langchain.com/docs/integrations/tools/e2b_data_analysis/", "Human as a tool": "https://python.langchain.com/docs/integrations/tools/human_tools/", "Natural Language API Toolkits": "https://python.langchain.com/docs/integrations/tools/openapi_nla/", "Steam Toolkit": "https://python.langchain.com/docs/integrations/tools/steam/", "Yahoo Finance News": "https://python.langchain.com/docs/integrations/tools/yahoo_finance_news/", "Google Finance": "https://python.langchain.com/docs/integrations/tools/google_finance/", "ClickUp Toolkit": "https://python.langchain.com/docs/integrations/tools/clickup/", "AWS Lambda": "https://python.langchain.com/docs/integrations/tools/awslambda/", "Google Drive": "https://python.langchain.com/docs/integrations/tools/google_drive/", "OpenWeatherMap": "https://python.langchain.com/docs/integrations/tools/openweathermap/", "AINetwork Toolkit": "https://python.langchain.com/docs/integrations/tools/ainetwork/", "Memorize": "https://python.langchain.com/docs/integrations/tools/memorize/", "PlayWright Browser Toolkit": "https://python.langchain.com/docs/integrations/tools/playwright/", "Eleven Labs Text2Speech": "https://python.langchain.com/docs/integrations/tools/eleven_labs_tts/", "Office365 Toolkit": "https://python.langchain.com/docs/integrations/tools/office365/", "Bearly Code Interpreter": "https://python.langchain.com/docs/integrations/tools/bearly/", "Pandas Dataframe": "https://python.langchain.com/docs/integrations/tools/pandas/", "Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/", "NASA Toolkit": "https://python.langchain.com/docs/integrations/tools/nasa/", "Connery Toolkit": "https://python.langchain.com/docs/integrations/tools/connery_toolkit/", "GraphQL": "https://python.langchain.com/docs/integrations/tools/graphql/", "SearchApi": "https://python.langchain.com/docs/integrations/providers/searchapi/", "Eden AI": "https://python.langchain.com/docs/integrations/tools/edenai_tools/", "Gitlab Toolkit": "https://python.langchain.com/docs/integrations/tools/gitlab/", "Shell (bash)": "https://python.langchain.com/docs/integrations/tools/bash/", "Zep Open Source Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory/", "Xata": "https://python.langchain.com/docs/integrations/memory/xata_chat_message_history/", "Zep Cloud Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory_cloud/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/", "Comet Tracing": "https://python.langchain.com/docs/integrations/callbacks/comet_tracing/", "Argilla": "https://python.langchain.com/docs/integrations/callbacks/argilla/", "Aim": "https://python.langchain.com/docs/integrations/providers/aim_tracking/", "Weights & Biases": "https://python.langchain.com/docs/integrations/providers/wandb_tracking/", "MLflow": "https://python.langchain.com/docs/integrations/providers/mlflow_tracking/", "Serper - Google Search API": "https://python.langchain.com/docs/integrations/providers/google_serper/", "Flyte": "https://python.langchain.com/docs/integrations/providers/flyte/", "WandB Tracing": "https://python.langchain.com/docs/integrations/providers/wandb_tracing/", "ClearML": "https://python.langchain.com/docs/integrations/providers/clearml_tracking/", "Amazon API Gateway": "https://python.langchain.com/docs/integrations/llms/amazon_api_gateway/"}, "initialize_agent": {"ChatGPT Plugins": "https://python.langchain.com/docs/integrations/tools/chatgpt_plugins/", "Connery Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/connery/", "Jira Toolkit": "https://python.langchain.com/docs/integrations/tools/jira/", "Google Jobs": "https://python.langchain.com/docs/integrations/tools/google_jobs/", "Google Serper": "https://python.langchain.com/docs/integrations/tools/google_serper/", "Azure Cognitive Services Toolkit": "https://python.langchain.com/docs/integrations/tools/azure_cognitive_services/", "E2B Data Analysis": "https://python.langchain.com/docs/integrations/tools/e2b_data_analysis/", "Human as a tool": "https://python.langchain.com/docs/integrations/tools/human_tools/", "Natural Language API Toolkits": "https://python.langchain.com/docs/integrations/tools/openapi_nla/", "Steam Toolkit": "https://python.langchain.com/docs/integrations/tools/steam/", "Yahoo Finance News": "https://python.langchain.com/docs/integrations/tools/yahoo_finance_news/", "Google Finance": "https://python.langchain.com/docs/integrations/tools/google_finance/", "ClickUp Toolkit": "https://python.langchain.com/docs/integrations/tools/clickup/", "AWS Lambda": "https://python.langchain.com/docs/integrations/tools/awslambda/", "Google Drive": "https://python.langchain.com/docs/integrations/tools/google_drive/", "OpenWeatherMap": "https://python.langchain.com/docs/integrations/tools/openweathermap/", "AINetwork Toolkit": "https://python.langchain.com/docs/integrations/tools/ainetwork/", "Memorize": "https://python.langchain.com/docs/integrations/tools/memorize/", "PlayWright Browser Toolkit": "https://python.langchain.com/docs/integrations/tools/playwright/", "Eleven Labs Text2Speech": "https://python.langchain.com/docs/integrations/tools/eleven_labs_tts/", "Office365 Toolkit": "https://python.langchain.com/docs/integrations/tools/office365/", "Bearly Code Interpreter": "https://python.langchain.com/docs/integrations/tools/bearly/", "Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/", "NASA Toolkit": "https://python.langchain.com/docs/integrations/tools/nasa/", "Connery Toolkit": "https://python.langchain.com/docs/integrations/tools/connery_toolkit/", "GraphQL": "https://python.langchain.com/docs/integrations/tools/graphql/", "SearchApi": "https://python.langchain.com/docs/integrations/providers/searchapi/", "Gradio": "https://python.langchain.com/docs/integrations/tools/gradio_tools/", "SceneXplain": "https://python.langchain.com/docs/integrations/tools/sceneXplain/", "Eden AI": "https://python.langchain.com/docs/integrations/tools/edenai_tools/", "Dall-E Image Generator": "https://python.langchain.com/docs/integrations/tools/dalle_image_generator/", "Gitlab Toolkit": "https://python.langchain.com/docs/integrations/tools/gitlab/", "Shell (bash)": "https://python.langchain.com/docs/integrations/tools/bash/", "Zep Open Source Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory/", "Xata": "https://python.langchain.com/docs/integrations/memory/xata_chat_message_history/", "Zep Cloud Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory_cloud/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/", "SageMaker Tracking": "https://python.langchain.com/docs/integrations/callbacks/sagemaker_tracking/", "Comet Tracing": "https://python.langchain.com/docs/integrations/callbacks/comet_tracing/", "Argilla": "https://python.langchain.com/docs/integrations/callbacks/argilla/", "Comet": "https://python.langchain.com/docs/integrations/providers/comet_tracking/", "Aim": "https://python.langchain.com/docs/integrations/providers/aim_tracking/", "Weights & Biases": "https://python.langchain.com/docs/integrations/providers/wandb_tracking/", "MLflow": "https://python.langchain.com/docs/integrations/providers/mlflow_tracking/", "Serper - Google Search API": "https://python.langchain.com/docs/integrations/providers/google_serper/", "Flyte": "https://python.langchain.com/docs/integrations/providers/flyte/", "WandB Tracing": "https://python.langchain.com/docs/integrations/providers/wandb_tracing/", "ClearML": "https://python.langchain.com/docs/integrations/providers/clearml_tracking/", "Amazon API Gateway": "https://python.langchain.com/docs/integrations/llms/amazon_api_gateway/"}, "DataForSeoAPIWrapper": {"DataForSEO": "https://python.langchain.com/docs/integrations/providers/dataforseo/"}, "Tool": {"DataForSEO": "https://python.langchain.com/docs/integrations/tools/dataforseo/", "Python REPL": "https://python.langchain.com/docs/integrations/tools/python/", "Google Serper": "https://python.langchain.com/docs/integrations/tools/google_serper/", "SerpAPI": "https://python.langchain.com/docs/integrations/tools/serpapi/", "SearchApi": "https://python.langchain.com/docs/integrations/providers/searchapi/", "Google Search": "https://python.langchain.com/docs/integrations/tools/google_search/", "Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/", "Ionic Shopping Tool": "https://python.langchain.com/docs/integrations/tools/ionic_shopping/", "Zep Open Source Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory/", "Zep Cloud Memory": "https://python.langchain.com/docs/integrations/memory/zep_memory_cloud/", "Serper - Google Search API": "https://python.langchain.com/docs/integrations/providers/google_serper/", "Bittensor": "https://python.langchain.com/docs/integrations/llms/bittensor/"}, "ConneryToolkit": {"Connery Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/connery/", "Connery Toolkit": "https://python.langchain.com/docs/integrations/tools/connery_toolkit/"}, "ConneryService": {"Connery Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/connery/", "Connery Toolkit": "https://python.langchain.com/docs/integrations/tools/connery_toolkit/"}, "DataheraldAPIWrapper": {"Dataherald": "https://python.langchain.com/docs/integrations/providers/dataherald/"}, "SearxSearchWrapper": {"SearxNG Search": "https://python.langchain.com/docs/integrations/tools/searx_search/", "SearxNG Search API": "https://python.langchain.com/docs/integrations/providers/searx/"}, "JiraToolkit": {"Jira Toolkit": "https://python.langchain.com/docs/integrations/tools/jira/"}, "JiraAPIWrapper": {"Jira Toolkit": "https://python.langchain.com/docs/integrations/tools/jira/"}, "PythonREPL": {"Python REPL": "https://python.langchain.com/docs/integrations/tools/python/"}, "GoogleJobsAPIWrapper": {"Google Jobs": "https://python.langchain.com/docs/integrations/tools/google_jobs/"}, "InfobipAPIWrapper": {"Infobip": "https://python.langchain.com/docs/integrations/tools/infobip/"}, "create_openai_functions_agent": {"Infobip": "https://python.langchain.com/docs/integrations/tools/infobip/", "AskNews": "https://python.langchain.com/docs/integrations/tools/asknews/", "Polygon IO Toolkit": "https://python.langchain.com/docs/integrations/tools/polygon_toolkit/", "Semantic Scholar API Tool": "https://python.langchain.com/docs/integrations/tools/semanticscholar/", "Passio NutritionAI": "https://python.langchain.com/docs/integrations/tools/passio_nutrition_ai/", "MultiOn Toolkit": "https://python.langchain.com/docs/integrations/tools/multion/", "You.com Search": "https://python.langchain.com/docs/integrations/tools/you/", "Polygon IO Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/polygon/"}, "AskNewsSearch": {"AskNews": "https://python.langchain.com/docs/integrations/tools/asknews/"}, "create_pbi_agent": {"PowerBI Toolkit": "https://python.langchain.com/docs/integrations/tools/powerbi/"}, "AzureCognitiveServicesToolkit": {"Azure Cognitive Services Toolkit": "https://python.langchain.com/docs/integrations/tools/azure_cognitive_services/"}, "E2BDataAnalysisTool": {"E2B Data Analysis": "https://python.langchain.com/docs/integrations/tools/e2b_data_analysis/"}, "SQLDatabaseToolkit": {"SQLDatabase Toolkit": "https://python.langchain.com/docs/integrations/tools/sql_database/", "CnosDB": "https://python.langchain.com/docs/integrations/providers/cnosdb/", "Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/"}, "HumanInputRun": {"Human as a tool": "https://python.langchain.com/docs/integrations/tools/human_tools/"}, "FinancialDatasetsToolkit": {"FinancialDatasets Toolkit": "https://python.langchain.com/docs/integrations/tools/financial_datasets/"}, "FinancialDatasetsAPIWrapper": {"FinancialDatasets Toolkit": "https://python.langchain.com/docs/integrations/tools/financial_datasets/"}, "NLAToolkit": {"Natural Language API Toolkits": "https://python.langchain.com/docs/integrations/tools/openapi_nla/"}, "Requests": {"Natural Language API Toolkits": "https://python.langchain.com/docs/integrations/tools/openapi_nla/"}, "ZenGuardTool": {"ZenGuard AI": "https://python.langchain.com/docs/integrations/tools/zenguard/"}, "Detector": {"ZenGuard AI": "https://python.langchain.com/docs/integrations/tools/zenguard/"}, "SlackToolkit": {"Slack Toolkit": "https://python.langchain.com/docs/integrations/tools/slack/", "Slack": "https://python.langchain.com/docs/integrations/providers/slack/"}, "SteamToolkit": {"Steam Toolkit": "https://python.langchain.com/docs/integrations/tools/steam/"}, "SteamWebAPIWrapper": {"Steam Toolkit": "https://python.langchain.com/docs/integrations/tools/steam/"}, "create_openai_tools_agent": {"Cassandra Database Toolkit": "https://python.langchain.com/docs/integrations/tools/cassandra_database/", "Log, Trace, and Monitor": "https://python.langchain.com/docs/integrations/providers/portkey/logging_tracing_portkey/", "Portkey": "https://python.langchain.com/docs/integrations/providers/portkey/index/"}, "CassandraDatabaseToolkit": {"Cassandra Database Toolkit": "https://python.langchain.com/docs/integrations/tools/cassandra_database/", "Cassandra": "https://python.langchain.com/docs/integrations/providers/cassandra/"}, "CassandraDatabase": {"Cassandra Database Toolkit": "https://python.langchain.com/docs/integrations/tools/cassandra_database/"}, "NucliaUnderstandingAPI": {"Nuclia Understanding": "https://python.langchain.com/docs/integrations/tools/nuclia/", "Nuclia": "https://python.langchain.com/docs/integrations/document_loaders/nuclia/"}, "YahooFinanceNewsTool": {"Yahoo Finance News": "https://python.langchain.com/docs/integrations/tools/yahoo_finance_news/"}, "JsonToolkit": {"JSON Toolkit": "https://python.langchain.com/docs/integrations/tools/json/"}, "create_json_agent": {"JSON Toolkit": "https://python.langchain.com/docs/integrations/tools/json/"}, "JsonSpec": {"JSON Toolkit": "https://python.langchain.com/docs/integrations/tools/json/", "OpenAPI Toolkit": "https://python.langchain.com/docs/integrations/tools/openapi/"}, "PolygonToolkit": {"Polygon IO Toolkit": "https://python.langchain.com/docs/integrations/tools/polygon_toolkit/", "Polygon IO Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/polygon/"}, "PolygonAPIWrapper": {"Polygon IO Toolkit": "https://python.langchain.com/docs/integrations/tools/polygon_toolkit/", "Polygon IO Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/polygon/"}, "WikidataAPIWrapper": {"Wikidata": "https://python.langchain.com/docs/integrations/tools/wikidata/"}, "WikidataQueryRun": {"Wikidata": "https://python.langchain.com/docs/integrations/tools/wikidata/"}, "TwilioAPIWrapper": {"Twilio": "https://python.langchain.com/docs/integrations/tools/twilio/"}, "IFTTTWebhook": {"IFTTT WebHooks": "https://python.langchain.com/docs/integrations/tools/ifttt/"}, "SemanticScholarQueryRun": {"Semantic Scholar API Tool": "https://python.langchain.com/docs/integrations/tools/semanticscholar/"}, "AlphaVantageAPIWrapper": {"Alpha Vantage": "https://python.langchain.com/docs/integrations/tools/alpha_vantage/"}, "GitHubToolkit": {"Github Toolkit": "https://python.langchain.com/docs/integrations/tools/github/"}, "GitHubAPIWrapper": {"Github Toolkit": "https://python.langchain.com/docs/integrations/tools/github/"}, "ChatDatabricks": {"Databricks Unity Catalog (UC)": "https://python.langchain.com/docs/integrations/tools/databricks/", "ChatDatabricks": "https://python.langchain.com/docs/integrations/chat/databricks/"}, "UCFunctionToolkit": {"Databricks Unity Catalog (UC)": "https://python.langchain.com/docs/integrations/tools/databricks/"}, "GoogleCloudTextToSpeechTool": {"Google Cloud Text-to-Speech": "https://python.langchain.com/docs/integrations/tools/google_cloud_texttospeech/"}, "ClickupToolkit": {"ClickUp Toolkit": "https://python.langchain.com/docs/integrations/tools/clickup/"}, "ClickupAPIWrapper": {"ClickUp Toolkit": "https://python.langchain.com/docs/integrations/tools/clickup/"}, "SparkSQLToolkit": {"Spark SQL Toolkit": "https://python.langchain.com/docs/integrations/tools/spark_sql/"}, "create_spark_sql_agent": {"Spark SQL Toolkit": "https://python.langchain.com/docs/integrations/tools/spark_sql/"}, "SparkSQL": {"Spark SQL Toolkit": "https://python.langchain.com/docs/integrations/tools/spark_sql/"}, "OracleSummary": {"Oracle AI Vector Search: Generate Summary": "https://python.langchain.com/docs/integrations/tools/oracleai/", "OracleAI Vector Search": "https://python.langchain.com/docs/integrations/providers/oracleai/"}, "StackExchangeAPIWrapper": {"StackExchange": "https://python.langchain.com/docs/integrations/tools/stackexchange/", "Stack Exchange": "https://python.langchain.com/docs/integrations/providers/stackexchange/"}, "RequestsToolkit": {"Requests Toolkit": "https://python.langchain.com/docs/integrations/tools/requests/"}, "TextRequestsWrapper": {"Requests Toolkit": "https://python.langchain.com/docs/integrations/tools/requests/"}, "OpenWeatherMapAPIWrapper": {"OpenWeatherMap": "https://python.langchain.com/docs/integrations/providers/openweathermap/"}, "AINetworkToolkit": {"AINetwork Toolkit": "https://python.langchain.com/docs/integrations/tools/ainetwork/", "AINetwork": "https://python.langchain.com/docs/integrations/providers/ainetwork/"}, "get_from_env": {"Passio NutritionAI": "https://python.langchain.com/docs/integrations/tools/passio_nutrition_ai/"}, "NutritionAI": {"Passio NutritionAI": "https://python.langchain.com/docs/integrations/tools/passio_nutrition_ai/"}, "NutritionAIAPI": {"Passio NutritionAI": "https://python.langchain.com/docs/integrations/tools/passio_nutrition_ai/"}, "PubmedQueryRun": {"PubMed": "https://python.langchain.com/docs/integrations/tools/pubmed/"}, "GradientLLM": {"Memorize": "https://python.langchain.com/docs/integrations/tools/memorize/", "Gradient": "https://python.langchain.com/docs/integrations/llms/gradient/"}, "create_async_playwright_browser": {"PlayWright Browser Toolkit": "https://python.langchain.com/docs/integrations/tools/playwright/"}, "ElevenLabsText2SpeechTool": {"Eleven Labs Text2Speech": "https://python.langchain.com/docs/integrations/tools/eleven_labs_tts/", "ElevenLabs": "https://python.langchain.com/docs/integrations/providers/elevenlabs/"}, "create_conversational_retrieval_agent": {"Cogniswitch Toolkit": "https://python.langchain.com/docs/integrations/tools/cogniswitch/"}, "CogniswitchToolkit": {"Cogniswitch Toolkit": "https://python.langchain.com/docs/integrations/tools/cogniswitch/"}, "BearlyInterpreterTool": {"Bearly Code Interpreter": "https://python.langchain.com/docs/integrations/tools/bearly/"}, "ExecPython": {"Riza Code Interpreter": "https://python.langchain.com/docs/integrations/tools/riza/"}, "ZapierToolkit": {"Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/"}, "ZapierNLAWrapper": {"Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/"}, "SimpleSequentialChain": {"Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/", "SageMaker Tracking": "https://python.langchain.com/docs/integrations/callbacks/sagemaker_tracking/", "Rebuff": "https://python.langchain.com/docs/integrations/providers/rebuff/", "Predibase": "https://python.langchain.com/docs/integrations/llms/predibase/", "Eden AI": "https://python.langchain.com/docs/integrations/llms/edenai/", "Replicate": "https://python.langchain.com/docs/integrations/llms/replicate/"}, "TransformChain": {"Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/", "Rebuff": "https://python.langchain.com/docs/integrations/providers/rebuff/"}, "ZapierNLARunAction": {"Zapier Natural Language Actions": "https://python.langchain.com/docs/integrations/tools/zapier/"}, "RivaASR": {"NVIDIA Riva: ASR and TTS": "https://python.langchain.com/docs/integrations/tools/nvidia_riva/"}, "RivaTTS": {"NVIDIA Riva: ASR and TTS": "https://python.langchain.com/docs/integrations/tools/nvidia_riva/"}, "RivaAudioEncoding": {"NVIDIA Riva: ASR and TTS": "https://python.langchain.com/docs/integrations/tools/nvidia_riva/"}, "AudioStream": {"NVIDIA Riva: ASR and TTS": "https://python.langchain.com/docs/integrations/tools/nvidia_riva/"}, "GoldenQueryAPIWrapper": {"Golden Query": "https://python.langchain.com/docs/integrations/tools/golden_query/", "Golden": "https://python.langchain.com/docs/integrations/providers/golden/"}, "create_react_agent": {"ArXiv": "https://python.langchain.com/docs/integrations/tools/arxiv/", "Amadeus Toolkit": "https://python.langchain.com/docs/integrations/tools/amadeus/", "Ionic Shopping Tool": "https://python.langchain.com/docs/integrations/tools/ionic_shopping/", "Streamlit": "https://python.langchain.com/docs/integrations/callbacks/streamlit/", "Dataherald": "https://python.langchain.com/docs/integrations/providers/dataherald/", "Bittensor": "https://python.langchain.com/docs/integrations/llms/bittensor/"}, "ArxivAPIWrapper": {"ArXiv": "https://python.langchain.com/docs/integrations/tools/arxiv/"}, "OpenAIFunctionsAgent": {"Robocorp Toolkit": "https://python.langchain.com/docs/integrations/tools/robocorp/", "Exa Search": "https://python.langchain.com/docs/integrations/tools/exa_search/", "LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/"}, "NasaToolkit": {"NASA Toolkit": "https://python.langchain.com/docs/integrations/tools/nasa/"}, "NasaAPIWrapper": {"NASA Toolkit": "https://python.langchain.com/docs/integrations/tools/nasa/"}, "MultionToolkit": {"MultiOn Toolkit": "https://python.langchain.com/docs/integrations/tools/multion/"}, "DuckDuckGoSearchRun": {"DuckDuckGo Search": "https://python.langchain.com/docs/integrations/tools/ddg/"}, "DuckDuckGoSearchResults": {"DuckDuckGo Search": "https://python.langchain.com/docs/integrations/tools/ddg/"}, "DuckDuckGoSearchAPIWrapper": {"DuckDuckGo Search": "https://python.langchain.com/docs/integrations/tools/ddg/"}, "SceneXplainTool": {"SceneXplain": "https://python.langchain.com/docs/integrations/tools/sceneXplain/"}, "WolframAlphaAPIWrapper": {"Wolfram Alpha": "https://python.langchain.com/docs/integrations/providers/wolfram_alpha/"}, "AmadeusToolkit": {"Amadeus Toolkit": "https://python.langchain.com/docs/integrations/tools/amadeus/"}, "HuggingFaceHub": {"Amadeus Toolkit": "https://python.langchain.com/docs/integrations/tools/amadeus/"}, "ReActJsonSingleInputOutputParser": {"Amadeus Toolkit": "https://python.langchain.com/docs/integrations/tools/amadeus/", "MLX": "https://python.langchain.com/docs/integrations/chat/mlx/"}, "render_text_description_and_args": {"Amadeus Toolkit": "https://python.langchain.com/docs/integrations/tools/amadeus/"}, "EdenAiExplicitImageTool": {"Eden AI": "https://python.langchain.com/docs/integrations/providers/edenai/"}, "EdenAiObjectDetectionTool": {"Eden AI": "https://python.langchain.com/docs/integrations/providers/edenai/"}, "EdenAiParsingIDTool": {"Eden AI": "https://python.langchain.com/docs/integrations/providers/edenai/"}, "EdenAiParsingInvoiceTool": {"Eden AI": "https://python.langchain.com/docs/integrations/providers/edenai/"}, "EdenAiSpeechToTextTool": {"Eden AI": "https://python.langchain.com/docs/integrations/providers/edenai/"}, "EdenAiTextModerationTool": {"Eden AI": "https://python.langchain.com/docs/integrations/providers/edenai/"}, "EdenAiTextToSpeechTool": {"Eden AI": "https://python.langchain.com/docs/integrations/providers/edenai/"}, "EdenAI": {"Eden AI": "https://python.langchain.com/docs/integrations/llms/edenai/"}, "MojeekSearch": {"Mojeek Search": "https://python.langchain.com/docs/integrations/tools/mojeek_search/"}, "RedditSearchRun": {"Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/"}, "RedditSearchAPIWrapper": {"Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/"}, "RedditSearchSchema": {"Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/"}, "StructuredChatAgent": {"Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/"}, "ReadOnlySharedMemory": {"Reddit Search ": "https://python.langchain.com/docs/integrations/tools/reddit_search/"}, "YouSearchTool": {"You.com Search": "https://python.langchain.com/docs/integrations/tools/you/"}, "AzureAiServicesToolkit": {"Azure AI Services Toolkit": "https://python.langchain.com/docs/integrations/tools/azure_ai_services/"}, "create_structured_chat_agent": {"Azure AI Services Toolkit": "https://python.langchain.com/docs/integrations/tools/azure_ai_services/"}, "reduce_openapi_spec": {"OpenAPI Toolkit": "https://python.langchain.com/docs/integrations/tools/openapi/"}, "RequestsWrapper": {"OpenAPI Toolkit": "https://python.langchain.com/docs/integrations/tools/openapi/"}, "OpenAPIToolkit": {"OpenAPI Toolkit": "https://python.langchain.com/docs/integrations/tools/openapi/"}, "create_openapi_agent": {"OpenAPI Toolkit": "https://python.langchain.com/docs/integrations/tools/openapi/"}, "GitLabToolkit": {"Gitlab Toolkit": "https://python.langchain.com/docs/integrations/tools/gitlab/"}, "GitLabAPIWrapper": {"Gitlab Toolkit": "https://python.langchain.com/docs/integrations/tools/gitlab/"}, "ShellTool": {"Shell (bash)": "https://python.langchain.com/docs/integrations/tools/bash/"}, "PolygonAggregates": {"Polygon IO Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/polygon/"}, "PolygonFinancials": {"Polygon IO Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/polygon/"}, "PolygonLastQuote": {"Polygon IO Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/polygon/"}, "PolygonTickerNews": {"Polygon IO Toolkit and Tools": "https://python.langchain.com/docs/integrations/tools/polygon/"}, "FileManagementToolkit": {"File System": "https://python.langchain.com/docs/integrations/tools/filesystem/"}, "BraveSearch": {"Brave Search": "https://python.langchain.com/docs/integrations/providers/brave_search/"}, "RedisChatMessageHistory": {"Redis": "https://python.langchain.com/docs/integrations/memory/redis_chat_message_history/"}, "KafkaChatMessageHistory": {"Kafka": "https://python.langchain.com/docs/integrations/memory/kafka_chat_message_history/"}, "ElasticsearchChatMessageHistory": {"Elasticsearch": "https://python.langchain.com/docs/integrations/memory/elasticsearch_chat_message_history/"}, "UpstashRedisChatMessageHistory": {"Upstash Redis": "https://python.langchain.com/docs/integrations/memory/upstash_redis_chat_message_history/", "Upstash Vector": "https://python.langchain.com/docs/integrations/providers/upstash/"}, "ZepCloudChatMessageHistory": {"ZepCloudChatMessageHistory": "https://python.langchain.com/docs/integrations/memory/zep_cloud_chat_message_history/", "Zep": "https://python.langchain.com/docs/integrations/providers/zep/"}, "SingleStoreDBChatMessageHistory": {"SingleStoreDB": "https://python.langchain.com/docs/integrations/providers/singlestoredb/"}, "PostgresChatMessageHistory": {"Postgres": "https://python.langchain.com/docs/integrations/memory/postgres_chat_message_history/"}, "MomentoChatMessageHistory": {"Momento Cache": "https://python.langchain.com/docs/integrations/memory/momento_chat_message_history/", "Momento": "https://python.langchain.com/docs/integrations/providers/momento/"}, "XataChatMessageHistory": {"Xata": "https://python.langchain.com/docs/integrations/providers/xata/"}, "XataVectorStore": {"Xata": "https://python.langchain.com/docs/integrations/vectorstores/xata/"}, "CassandraChatMessageHistory": {"Cassandra ": "https://python.langchain.com/docs/integrations/memory/cassandra_chat_message_history/", "Cassandra": "https://python.langchain.com/docs/integrations/providers/cassandra/"}, "MotorheadMemory": {"Motörhead": "https://python.langchain.com/docs/integrations/memory/motorhead_memory/"}, "AstraDBChatMessageHistory": {"Astra DB ": "https://python.langchain.com/docs/integrations/memory/astradb_chat_message_history/"}, "StreamlitChatMessageHistory": {"Streamlit": "https://python.langchain.com/docs/integrations/providers/streamlit/"}, "Neo4jChatMessageHistory": {"Neo4j": "https://python.langchain.com/docs/integrations/memory/neo4j_chat_message_history/"}, "TiDBChatMessageHistory": {"TiDB": "https://python.langchain.com/docs/integrations/providers/tidb/"}, "RocksetChatMessageHistory": {"Rockset": "https://python.langchain.com/docs/integrations/providers/rockset/"}, "ChatSnowflakeCortex": {"Snowflake Cortex": "https://python.langchain.com/docs/integrations/chat/snowflake/"}, "SolarChat": {"# Related": "https://python.langchain.com/docs/integrations/chat/solar/"}, "AzureMLChatOnlineEndpoint": {"AzureMLChatOnlineEndpoint": "https://python.langchain.com/docs/integrations/chat/azureml_chat_endpoint/"}, "AzureMLEndpointApiType": {"AzureMLChatOnlineEndpoint": "https://python.langchain.com/docs/integrations/chat/azureml_chat_endpoint/", "Azure ML": "https://python.langchain.com/docs/integrations/llms/azure_ml/"}, "CustomOpenAIChatContentFormatter": {"AzureMLChatOnlineEndpoint": "https://python.langchain.com/docs/integrations/chat/azureml_chat_endpoint/"}, "ChatKinetica": {"Kinetica Language To SQL Chat Model": "https://python.langchain.com/docs/integrations/chat/kinetica/", "Kinetica": "https://python.langchain.com/docs/integrations/providers/kinetica/"}, "KineticaSqlOutputParser": {"Kinetica Language To SQL Chat Model": "https://python.langchain.com/docs/integrations/chat/kinetica/"}, "KineticaSqlResponse": {"Kinetica Language To SQL Chat Model": "https://python.langchain.com/docs/integrations/chat/kinetica/"}, "PaiEasChatEndpoint": {"Alibaba Cloud PAI EAS": "https://python.langchain.com/docs/integrations/chat/alibaba_cloud_pai_eas/", "Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/"}, "ChatCoze": {"Chat with Coze Bot": "https://python.langchain.com/docs/integrations/chat/coze/"}, "ChatOctoAI": {"ChatOctoAI": "https://python.langchain.com/docs/integrations/chat/octoai/", "OctoAI": "https://python.langchain.com/docs/integrations/providers/octoai/"}, "ChatYi": {"ChatYI": "https://python.langchain.com/docs/integrations/chat/yi/", "01.AI": "https://python.langchain.com/docs/integrations/providers/yi/"}, "ChatDeepInfra": {"DeepInfra": "https://python.langchain.com/docs/integrations/providers/deepinfra/"}, "ChatLiteLLM": {"ChatLiteLLM": "https://python.langchain.com/docs/integrations/chat/litellm/"}, "LlamaEdgeChatService": {"LlamaEdge": "https://python.langchain.com/docs/integrations/chat/llama_edge/"}, "OllamaFunctions": {"OllamaFunctions": "https://python.langchain.com/docs/integrations/chat/ollama_functions/"}, "VolcEngineMaasChat": {"VolcEngineMaasChat": "https://python.langchain.com/docs/integrations/chat/volcengine_maas/"}, "ChatLlamaAPI": {"ChatLlamaAPI": "https://python.langchain.com/docs/integrations/chat/llama_api/"}, "create_tagging_chain": {"ChatLlamaAPI": "https://python.langchain.com/docs/integrations/chat/llama_api/"}, "ChatKonko": {"ChatKonko": "https://python.langchain.com/docs/integrations/chat/konko/"}, "ChatBedrockConverse": {"ChatBedrock": "https://python.langchain.com/docs/integrations/chat/bedrock/"}, "MLXPipeline": {"MLX": "https://python.langchain.com/docs/integrations/providers/mlx/", "MLX Local Pipelines": "https://python.langchain.com/docs/integrations/llms/mlx_pipelines/"}, "ChatMLX": {"MLX": "https://python.langchain.com/docs/integrations/providers/mlx/"}, "format_log_to_str": {"MLX": "https://python.langchain.com/docs/integrations/chat/mlx/"}, "GigaChat": {"GigaChat": "https://python.langchain.com/docs/integrations/llms/gigachat/", "Salute Devices": "https://python.langchain.com/docs/integrations/providers/salute_devices/"}, "JinaChat": {"JinaChat": "https://python.langchain.com/docs/integrations/chat/jinachat/"}, "SystemMessagePromptTemplate": {"JinaChat": "https://python.langchain.com/docs/integrations/chat/jinachat/", "vLLM Chat": "https://python.langchain.com/docs/integrations/chat/vllm/", "Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/", "Figma": "https://python.langchain.com/docs/integrations/document_loaders/figma/"}, "ChatOCIGenAI": {"ChatOCIGenAI": "https://python.langchain.com/docs/integrations/chat/oci_generative_ai/", "Oracle Cloud Infrastructure (OCI)": "https://python.langchain.com/docs/integrations/providers/oci/"}, "ChatLlamaCpp": {"Llama.cpp": "https://python.langchain.com/docs/integrations/providers/llamacpp/"}, "convert_to_openai_tool": {"Llama.cpp": "https://python.langchain.com/docs/integrations/chat/llamacpp/"}, "ChatEverlyAI": {"ChatEverlyAI": "https://python.langchain.com/docs/integrations/chat/everlyai/"}, "GPTRouter": {"GPTRouter": "https://python.langchain.com/docs/integrations/chat/gpt_router/"}, "GPTRouterModel": {"GPTRouter": "https://python.langchain.com/docs/integrations/chat/gpt_router/"}, "ChatLiteLLMRouter": {"ChatLiteLLMRouter": "https://python.langchain.com/docs/integrations/chat/litellm_router/"}, "ChatFriendli": {"ChatFriendli": "https://python.langchain.com/docs/integrations/chat/friendli/"}, "ChatZhipuAI": {"ZHIPU AI": "https://python.langchain.com/docs/integrations/chat/zhipuai/", "Zhipu AI": "https://python.langchain.com/docs/integrations/providers/zhipuai/"}, "create_json_chat_agent": {"ZHIPU AI": "https://python.langchain.com/docs/integrations/chat/zhipuai/"}, "ChatBaichuan": {"Chat with Baichuan-192K": "https://python.langchain.com/docs/integrations/chat/baichuan/", "Baichuan": "https://python.langchain.com/docs/integrations/providers/baichuan/"}, "ChatTogether": {"ChatTogether": "https://python.langchain.com/docs/integrations/chat/together/", "Together AI": "https://python.langchain.com/docs/integrations/llms/together/"}, "Llama2Chat": {"Llama2Chat": "https://python.langchain.com/docs/integrations/chat/llama2_chat/"}, "HuggingFaceTextGenInference": {"Llama2Chat": "https://python.langchain.com/docs/integrations/chat/llama2_chat/"}, "QianfanChatEndpoint": {"QianfanChatEndpoint": "https://python.langchain.com/docs/integrations/chat/baidu_qianfan_endpoint/", "ErnieBotChat": "https://python.langchain.com/docs/integrations/chat/ernie/", "Baidu": "https://python.langchain.com/docs/integrations/providers/baidu/"}, "ChatEdenAI": {"Eden AI": "https://python.langchain.com/docs/integrations/providers/edenai/"}, "ErnieBotChat": {"ErnieBotChat": "https://python.langchain.com/docs/integrations/chat/ernie/"}, "ChatHunyuan": {"Tencent Hunyuan": "https://python.langchain.com/docs/integrations/chat/tencent_hunyuan/", "Tencent": "https://python.langchain.com/docs/integrations/providers/tencent/"}, "MiniMaxChat": {"MiniMaxChat": "https://python.langchain.com/docs/integrations/chat/minimax/", "Minimax": "https://python.langchain.com/docs/integrations/providers/minimax/"}, "ChatYuan2": {"Yuan2.0": "https://python.langchain.com/docs/integrations/chat/yuan2/", "IEIT Systems": "https://python.langchain.com/docs/integrations/providers/ieit_systems/"}, "ChatTongyi": {"ChatTongyi": "https://python.langchain.com/docs/integrations/chat/tongyi/", "Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/"}, "PromptLayerChatOpenAI": {"PromptLayerChatOpenAI": "https://python.langchain.com/docs/integrations/chat/promptlayer_chatopenai/", "PromptLayer": "https://python.langchain.com/docs/integrations/providers/promptlayer/"}, "ChatSparkLLM": {"SparkLLM Chat": "https://python.langchain.com/docs/integrations/chat/sparkllm/", "iFlytek": "https://python.langchain.com/docs/integrations/providers/iflytek/"}, "MoonshotChat": {"MoonshotChat": "https://python.langchain.com/docs/integrations/chat/moonshot/"}, "ChatDappierAI": {"Dappier AI": "https://python.langchain.com/docs/integrations/chat/dappier/"}, "ChatMaritalk": {"Maritalk": "https://python.langchain.com/docs/integrations/chat/maritalk/", "MariTalk": "https://python.langchain.com/docs/integrations/providers/maritalk/"}, "OnlinePDFLoader": {"Maritalk": "https://python.langchain.com/docs/integrations/chat/maritalk/", "UnstructuredPDFLoader": "https://python.langchain.com/docs/integrations/document_loaders/unstructured_pdfloader/"}, "load_qa_chain": {"Maritalk": "https://python.langchain.com/docs/integrations/chat/maritalk/", "Amazon Textract ": "https://python.langchain.com/docs/integrations/document_loaders/amazon_textract/", "SageMakerEndpoint": "https://python.langchain.com/docs/integrations/llms/sagemaker/"}, "ChatPremAI": {"ChatPremAI": "https://python.langchain.com/docs/integrations/chat/premai/", "PremAI": "https://python.langchain.com/docs/integrations/providers/premai/"}, "ChatAnyscale": {"ChatAnyscale": "https://python.langchain.com/docs/integrations/chat/anyscale/", "Anyscale": "https://python.langchain.com/docs/integrations/providers/anyscale/"}, "ChatYandexGPT": {"ChatYandexGPT": "https://python.langchain.com/docs/integrations/chat/yandex/", "Yandex": "https://python.langchain.com/docs/integrations/providers/yandex/"}, "ChatPerplexity": {"ChatPerplexity": "https://python.langchain.com/docs/integrations/chat/perplexity/", "Perplexity": "https://python.langchain.com/docs/integrations/providers/perplexity/"}, "ChatAnthropicTools": {"[Deprecated] Experimental Anthropic Tools Wrapper": "https://python.langchain.com/docs/integrations/chat/anthropic_functions/"}, "DeepEvalCallbackHandler": {"Confident": "https://python.langchain.com/docs/integrations/callbacks/confident/", "Confident AI": "https://python.langchain.com/docs/integrations/providers/confident/"}, "LLMonitorCallbackHandler": {"LLMonitor": "https://python.langchain.com/docs/integrations/providers/llmonitor/"}, "identify": {"LLMonitor": "https://python.langchain.com/docs/integrations/callbacks/llmonitor/"}, "ContextCallbackHandler": {"Context": "https://python.langchain.com/docs/integrations/providers/context/"}, "FiddlerCallbackHandler": {"Fiddler": "https://python.langchain.com/docs/integrations/providers/fiddler/"}, "LabelStudioCallbackHandler": {"Label Studio": "https://python.langchain.com/docs/integrations/providers/labelstudio/"}, "CometTracer": {"Comet Tracing": "https://python.langchain.com/docs/integrations/callbacks/comet_tracing/", "Comet": "https://python.langchain.com/docs/integrations/providers/comet_tracking/"}, "ArgillaCallbackHandler": {"Argilla": "https://python.langchain.com/docs/integrations/providers/argilla/"}, "StdOutCallbackHandler": {"Argilla": "https://python.langchain.com/docs/integrations/callbacks/argilla/", "Comet": "https://python.langchain.com/docs/integrations/providers/comet_tracking/", "Aim": "https://python.langchain.com/docs/integrations/providers/aim_tracking/", "Weights & Biases": "https://python.langchain.com/docs/integrations/providers/wandb_tracking/", "ClearML": "https://python.langchain.com/docs/integrations/providers/clearml_tracking/", "OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/"}, "PromptLayerCallbackHandler": {"PromptLayer": "https://python.langchain.com/docs/integrations/providers/promptlayer/"}, "StreamlitCallbackHandler": {"Streamlit": "https://python.langchain.com/docs/integrations/providers/streamlit/", "GPT4All": "https://python.langchain.com/docs/integrations/providers/gpt4all/"}, "UpTrainCallbackHandler": {"UpTrain": "https://python.langchain.com/docs/integrations/providers/uptrain/"}, "TrubricsCallbackHandler": {"Trubrics": "https://python.langchain.com/docs/integrations/providers/trubrics/"}, "InfinoCallbackHandler": {"Infino": "https://python.langchain.com/docs/integrations/providers/infino/"}, "UpstashRatelimitError": {"Upstash Ratelimit Callback": "https://python.langchain.com/docs/integrations/callbacks/upstash_ratelimit/"}, "UpstashRatelimitHandler": {"Upstash Ratelimit Callback": "https://python.langchain.com/docs/integrations/callbacks/upstash_ratelimit/"}, "FigmaFileLoader": {"Figma": "https://python.langchain.com/docs/integrations/document_loaders/figma/"}, "Baseten": {"Baseten": "https://python.langchain.com/docs/integrations/llms/baseten/"}, "WeatherDataLoader": {"Weather": "https://python.langchain.com/docs/integrations/document_loaders/weather/"}, "Tair": {"Tair": "https://python.langchain.com/docs/integrations/vectorstores/tair/", "Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/"}, "CollegeConfidentialLoader": {"College Confidential": "https://python.langchain.com/docs/integrations/document_loaders/college_confidential/"}, "RWKV": {"RWKV-4": "https://python.langchain.com/docs/integrations/providers/rwkv/"}, "LakeFSLoader": {"lakeFS": "https://python.langchain.com/docs/integrations/document_loaders/lakefs/"}, "FaunaLoader": {"Fauna": "https://python.langchain.com/docs/integrations/document_loaders/fauna/"}, "OCIGenAI": {"Oracle Cloud Infrastructure (OCI)": "https://python.langchain.com/docs/integrations/providers/oci/", "# Oracle Cloud Infrastructure Generative AI": "https://python.langchain.com/docs/integrations/llms/oci_generative_ai/"}, "OCIModelDeploymentVLLM": {"Oracle Cloud Infrastructure (OCI)": "https://python.langchain.com/docs/integrations/providers/oci/", "OCI Data Science Model Deployment Endpoint": "https://python.langchain.com/docs/integrations/llms/oci_model_deployment_endpoint/"}, "OCIModelDeploymentTGI": {"Oracle Cloud Infrastructure (OCI)": "https://python.langchain.com/docs/integrations/providers/oci/", "OCI Data Science Model Deployment Endpoint": "https://python.langchain.com/docs/integrations/llms/oci_model_deployment_endpoint/"}, "OracleDocLoader": {"OracleAI Vector Search": "https://python.langchain.com/docs/integrations/providers/oracleai/", "Oracle AI Vector Search: Document Processing": "https://python.langchain.com/docs/integrations/document_loaders/oracleai/"}, "OracleTextSplitter": {"OracleAI Vector Search": "https://python.langchain.com/docs/integrations/providers/oracleai/", "Oracle AI Vector Search: Document Processing": "https://python.langchain.com/docs/integrations/document_loaders/oracleai/"}, "OracleVS": {"OracleAI Vector Search": "https://python.langchain.com/docs/integrations/providers/oracleai/", "Oracle AI Vector Search: Vector Store": "https://python.langchain.com/docs/integrations/vectorstores/oracle/"}, "Lantern": {"Lantern": "https://python.langchain.com/docs/integrations/vectorstores/lantern/"}, "DropboxLoader": {"Dropbox": "https://python.langchain.com/docs/integrations/document_loaders/dropbox/"}, "ForefrontAI": {"ForefrontAI": "https://python.langchain.com/docs/integrations/llms/forefrontai/"}, "CometCallbackHandler": {"Comet": "https://python.langchain.com/docs/integrations/providers/comet_tracking/"}, "CTransformers": {"C Transformers": "https://python.langchain.com/docs/integrations/llms/ctransformers/"}, "BiliBiliLoader": {"BiliBili": "https://python.langchain.com/docs/integrations/document_loaders/bilibili/"}, "TencentCOSDirectoryLoader": {"Tencent": "https://python.langchain.com/docs/integrations/providers/tencent/", "Tencent COS Directory": "https://python.langchain.com/docs/integrations/document_loaders/tencent_cos_directory/"}, "TencentCOSFileLoader": {"Tencent": "https://python.langchain.com/docs/integrations/providers/tencent/", "Tencent COS File": "https://python.langchain.com/docs/integrations/document_loaders/tencent_cos_file/"}, "OBSDirectoryLoader": {"Huawei": "https://python.langchain.com/docs/integrations/providers/huawei/", "Huawei OBS Directory": "https://python.langchain.com/docs/integrations/document_loaders/huawei_obs_directory/"}, "OBSFileLoader": {"Huawei": "https://python.langchain.com/docs/integrations/providers/huawei/", "Huawei OBS File": "https://python.langchain.com/docs/integrations/document_loaders/huawei_obs_file/"}, "DiffbotLoader": {"Diffbot": "https://python.langchain.com/docs/integrations/document_loaders/diffbot/"}, "DiffbotGraphTransformer": {"Diffbot": "https://python.langchain.com/docs/integrations/graphs/diffbot/", "Neo4j": "https://python.langchain.com/docs/integrations/providers/neo4j/"}, "DeepSparse": {"DeepSparse": "https://python.langchain.com/docs/integrations/llms/deepsparse/"}, "AimCallbackHandler": {"Aim": "https://python.langchain.com/docs/integrations/providers/aim_tracking/"}, "ModernTreasuryLoader": {"Modern Treasury": "https://python.langchain.com/docs/integrations/document_loaders/modern_treasury/"}, "GitHubIssuesLoader": {"GitHub": "https://python.langchain.com/docs/integrations/document_loaders/github/"}, "GithubFileLoader": {"GitHub": "https://python.langchain.com/docs/integrations/document_loaders/github/"}, "Banana": {"Banana": "https://python.langchain.com/docs/integrations/llms/banana/"}, "InfinispanVS": {"Infinispan VS": "https://python.langchain.com/docs/integrations/providers/infinispanvs/", "Infinispan": "https://python.langchain.com/docs/integrations/vectorstores/infinispanvs/"}, "CerebriumAI": {"CerebriumAI": "https://python.langchain.com/docs/integrations/llms/cerebriumai/"}, "GutenbergLoader": {"Gutenberg": "https://python.langchain.com/docs/integrations/document_loaders/gutenberg/"}, "WikipediaLoader": {"Wikipedia": "https://python.langchain.com/docs/integrations/document_loaders/wikipedia/", "Diffbot": "https://python.langchain.com/docs/integrations/graphs/diffbot/"}, "ConfluenceLoader": {"Confluence": "https://python.langchain.com/docs/integrations/document_loaders/confluence/"}, "Predibase": {"Predibase": "https://python.langchain.com/docs/integrations/llms/predibase/"}, "Beam": {"Beam": "https://python.langchain.com/docs/integrations/llms/beam/"}, "GrobidParser": {"Grobid": "https://python.langchain.com/docs/integrations/document_loaders/grobid/"}, "Typesense": {"Typesense": "https://python.langchain.com/docs/integrations/vectorstores/typesense/"}, "Hologres": {"Hologres": "https://python.langchain.com/docs/integrations/vectorstores/hologres/", "Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/"}, "ArangoGraph": {"ArangoDB": "https://python.langchain.com/docs/integrations/graphs/arangodb/"}, "ArangoGraphQAChain": {"ArangoDB": "https://python.langchain.com/docs/integrations/graphs/arangodb/"}, "ArcGISLoader": {"ArcGIS": "https://python.langchain.com/docs/integrations/document_loaders/arcgis/"}, "WandbCallbackHandler": {"Weights & Biases": "https://python.langchain.com/docs/integrations/providers/wandb_tracking/"}, "ObsidianLoader": {"Obsidian": "https://python.langchain.com/docs/integrations/document_loaders/obsidian/"}, "BrowserbaseLoader": {"Browserbase": "https://python.langchain.com/docs/integrations/document_loaders/browserbase/"}, "OctoAIEndpoint": {"OctoAI": "https://python.langchain.com/docs/integrations/llms/octoai/"}, "OctoAIEmbeddings": {"OctoAI": "https://python.langchain.com/docs/integrations/providers/octoai/"}, "Nebula": {"Nebula": "https://python.langchain.com/docs/integrations/providers/symblai_nebula/", "Nebula (Symbl.ai)": "https://python.langchain.com/docs/integrations/llms/symblai_nebula/"}, "Writer": {"Writer": "https://python.langchain.com/docs/integrations/llms/writer/"}, "BaichuanLLM": {"Baichuan": "https://python.langchain.com/docs/integrations/providers/baichuan/", "Baichuan LLM": "https://python.langchain.com/docs/integrations/llms/baichuan/"}, "ApacheDoris": {"Apache Doris": "https://python.langchain.com/docs/integrations/vectorstores/apache_doris/"}, "ZepCloudVectorStore": {"Zep": "https://python.langchain.com/docs/integrations/providers/zep/", "Zep Cloud": "https://python.langchain.com/docs/integrations/vectorstores/zep_cloud/"}, "BrowserlessLoader": {"Browserless": "https://python.langchain.com/docs/integrations/document_loaders/browserless/"}, "AZLyricsLoader": {"AZLyrics": "https://python.langchain.com/docs/integrations/document_loaders/azlyrics/"}, "ToMarkdownLoader": {"2Markdown": "https://python.langchain.com/docs/integrations/document_loaders/tomarkdown/"}, "SparkLLM": {"iFlytek": "https://python.langchain.com/docs/integrations/providers/iflytek/", "SparkLLM": "https://python.langchain.com/docs/integrations/llms/sparkllm/"}, "Mlflow": {"MLflow Deployments for LLMs": "https://python.langchain.com/docs/integrations/providers/mlflow/"}, "MlflowEmbeddings": {"MLflow Deployments for LLMs": "https://python.langchain.com/docs/integrations/providers/mlflow/"}, "ChatMlflow": {"MLflow Deployments for LLMs": "https://python.langchain.com/docs/integrations/providers/mlflow/"}, "GitLoader": {"Git": "https://python.langchain.com/docs/integrations/document_loaders/git/"}, "MlflowAIGateway": {"MLflow AI Gateway": "https://python.langchain.com/docs/integrations/providers/mlflow_ai_gateway/"}, "MlflowAIGatewayEmbeddings": {"MLflow AI Gateway": "https://python.langchain.com/docs/integrations/providers/mlflow_ai_gateway/"}, "ChatMLflowAIGateway": {"MLflow AI Gateway": "https://python.langchain.com/docs/integrations/providers/mlflow_ai_gateway/"}, "Tigris": {"Tigris": "https://python.langchain.com/docs/integrations/vectorstores/tigris/"}, "Meilisearch": {"Meilisearch": "https://python.langchain.com/docs/integrations/vectorstores/meilisearch/"}, "SQLDatabaseChain": {"Rebuff": "https://python.langchain.com/docs/integrations/providers/rebuff/"}, "SnowflakeLoader": {"Snowflake": "https://python.langchain.com/docs/integrations/document_loaders/snowflake/"}, "CubeSemanticLoader": {"Cube": "https://python.langchain.com/docs/integrations/providers/cube/", "Cube Semantic Layer": "https://python.langchain.com/docs/integrations/document_loaders/cube_semantic/"}, "Clickhouse": {"ClickHouse": "https://python.langchain.com/docs/integrations/vectorstores/clickhouse/"}, "ClickhouseSettings": {"ClickHouse": "https://python.langchain.com/docs/integrations/vectorstores/clickhouse/"}, "TelegramChatFileLoader": {"Telegram": "https://python.langchain.com/docs/integrations/document_loaders/telegram/"}, "TelegramChatApiLoader": {"Telegram": "https://python.langchain.com/docs/integrations/document_loaders/telegram/"}, "PredictionGuard": {"Prediction Guard": "https://python.langchain.com/docs/integrations/llms/predictionguard/"}, "Together": {"Together AI": "https://python.langchain.com/docs/integrations/llms/together/"}, "NotionDirectoryLoader": {"Notion DB": "https://python.langchain.com/docs/integrations/providers/notion/", "Notion DB 2/2": "https://python.langchain.com/docs/integrations/document_loaders/notion/"}, "NotionDBLoader": {"Notion DB": "https://python.langchain.com/docs/integrations/providers/notion/", "Notion DB 2/2": "https://python.langchain.com/docs/integrations/document_loaders/notiondb/"}, "MWDumpLoader": {"MediaWikiDump": "https://python.langchain.com/docs/integrations/providers/mediawikidump/", "MediaWiki Dump": "https://python.langchain.com/docs/integrations/document_loaders/mediawikidump/"}, "BraveSearchLoader": {"Brave Search": "https://python.langchain.com/docs/integrations/document_loaders/brave_search/"}, "StarRocks": {"StarRocks": "https://python.langchain.com/docs/integrations/vectorstores/starrocks/"}, "GooseAI": {"GooseAI": "https://python.langchain.com/docs/integrations/llms/gooseai/"}, "DatadogLogsLoader": {"Datadog Logs": "https://python.langchain.com/docs/integrations/document_loaders/datadog_logs/"}, "ApifyWrapper": {"Apify": "https://python.langchain.com/docs/integrations/providers/apify/", "Apify Dataset": "https://python.langchain.com/docs/integrations/document_loaders/apify_dataset/"}, "ApifyDatasetLoader": {"Apify": "https://python.langchain.com/docs/integrations/providers/apify/", "Apify Dataset": "https://python.langchain.com/docs/integrations/document_loaders/apify_dataset/"}, "NLPCloud": {"NLPCloud": "https://python.langchain.com/docs/integrations/providers/nlpcloud/", "NLP Cloud": "https://python.langchain.com/docs/integrations/llms/nlpcloud/"}, "Milvus": {"Milvus": "https://python.langchain.com/docs/integrations/providers/milvus/", "Zilliz": "https://python.langchain.com/docs/integrations/vectorstores/zilliz/"}, "SemaDB": {"SemaDB": "https://python.langchain.com/docs/integrations/vectorstores/semadb/"}, "GitbookLoader": {"GitBook": "https://python.langchain.com/docs/integrations/document_loaders/gitbook/"}, "Rockset": {"Rockset": "https://python.langchain.com/docs/integrations/vectorstores/rockset/"}, "RocksetLoader": {"Rockset": "https://python.langchain.com/docs/integrations/document_loaders/rockset/"}, "Minimax": {"Minimax": "https://python.langchain.com/docs/integrations/llms/minimax/"}, "UnstructuredCHMLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/"}, "UnstructuredCSVLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "CSV": "https://python.langchain.com/docs/integrations/document_loaders/csv/"}, "UnstructuredEmailLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "Email": "https://python.langchain.com/docs/integrations/document_loaders/email/"}, "UnstructuredEPubLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "EPub ": "https://python.langchain.com/docs/integrations/document_loaders/epub/"}, "UnstructuredFileIOLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "Google Drive": "https://python.langchain.com/docs/integrations/document_loaders/google_drive/"}, "UnstructuredImageLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "Images": "https://python.langchain.com/docs/integrations/document_loaders/image/"}, "UnstructuredODTLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "Open Document Format (ODT)": "https://python.langchain.com/docs/integrations/document_loaders/odt/"}, "UnstructuredOrgModeLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "Org-mode": "https://python.langchain.com/docs/integrations/document_loaders/org_mode/"}, "UnstructuredPDFLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "UnstructuredPDFLoader": "https://python.langchain.com/docs/integrations/document_loaders/unstructured_pdfloader/"}, "UnstructuredRSTLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "RST": "https://python.langchain.com/docs/integrations/document_loaders/rst/"}, "UnstructuredRTFLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/"}, "UnstructuredTSVLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "TSV": "https://python.langchain.com/docs/integrations/document_loaders/tsv/"}, "UnstructuredURLLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "URL": "https://python.langchain.com/docs/integrations/document_loaders/url/"}, "UnstructuredXMLLoader": {"Unstructured": "https://python.langchain.com/docs/integrations/providers/unstructured/", "UnstructuredXMLLoader": "https://python.langchain.com/docs/integrations/document_loaders/xml/"}, "SelfHostedPipeline": {"Runhouse": "https://python.langchain.com/docs/integrations/llms/runhouse/"}, "SelfHostedHuggingFaceLLM": {"Runhouse": "https://python.langchain.com/docs/integrations/llms/runhouse/"}, "MlflowCallbackHandler": {"MLflow": "https://python.langchain.com/docs/integrations/providers/mlflow_tracking/"}, "SpreedlyLoader": {"Spreedly": "https://python.langchain.com/docs/integrations/document_loaders/spreedly/"}, "OpenLLM": {"OpenLLM": "https://python.langchain.com/docs/integrations/llms/openllm/"}, "PubMedLoader": {"PubMed": "https://python.langchain.com/docs/integrations/document_loaders/pubmed/"}, "SearxSearchResults": {"SearxNG Search API": "https://python.langchain.com/docs/integrations/providers/searx/"}, "Modal": {"Modal": "https://python.langchain.com/docs/integrations/llms/modal/"}, "OpenCityDataLoader": {"Geopandas": "https://python.langchain.com/docs/integrations/document_loaders/geopandas/", "Open City Data": "https://python.langchain.com/docs/integrations/document_loaders/open_city_data/"}, "PGEmbedding": {"Postgres Embedding": "https://python.langchain.com/docs/integrations/vectorstores/pgembedding/"}, "SQLiteVSS": {"SQLite": "https://python.langchain.com/docs/integrations/providers/sqlite/", "SQLite-VSS": "https://python.langchain.com/docs/integrations/vectorstores/sqlitevss/"}, "Xinference": {"Xorbits Inference (Xinference)": "https://python.langchain.com/docs/integrations/llms/xinference/"}, "IFixitLoader": {"iFixit": "https://python.langchain.com/docs/integrations/document_loaders/ifixit/"}, "AlephAlpha": {"Aleph Alpha": "https://python.langchain.com/docs/integrations/llms/aleph_alpha/"}, "PipelineAI": {"PipelineAI": "https://python.langchain.com/docs/integrations/llms/pipelineai/"}, "FacebookChatLoader": {"Facebook - Meta": "https://python.langchain.com/docs/integrations/providers/facebook/", "Facebook Chat": "https://python.langchain.com/docs/integrations/document_loaders/facebook_chat/"}, "Epsilla": {"Epsilla": "https://python.langchain.com/docs/integrations/vectorstores/epsilla/"}, "AwaDB": {"AwaDB": "https://python.langchain.com/docs/integrations/vectorstores/awadb/"}, "ArxivLoader": {"Arxiv": "https://python.langchain.com/docs/integrations/providers/arxiv/", "ArxivLoader": "https://python.langchain.com/docs/integrations/document_loaders/arxiv/"}, "BlockchainDocumentLoader": {"Alchemy": "https://python.langchain.com/docs/integrations/providers/alchemy/", "Blockchain": "https://python.langchain.com/docs/integrations/document_loaders/blockchain/"}, "BlockchainType": {"Alchemy": "https://python.langchain.com/docs/integrations/providers/alchemy/", "Blockchain": "https://python.langchain.com/docs/integrations/document_loaders/blockchain/"}, "Anyscale": {"Anyscale": "https://python.langchain.com/docs/integrations/llms/anyscale/"}, "StripeLoader": {"Stripe": "https://python.langchain.com/docs/integrations/document_loaders/stripe/"}, "StochasticAI": {"StochasticAI": "https://python.langchain.com/docs/integrations/llms/stochasticai/"}, "Bagel": {"BagelDB": "https://python.langchain.com/docs/integrations/vectorstores/bageldb/", "Bagel": "https://python.langchain.com/docs/integrations/vectorstores/bagel/"}, "TigerGraph": {"TigerGraph": "https://python.langchain.com/docs/integrations/graphs/tigergraph/"}, "BlackboardLoader": {"Blackboard": "https://python.langchain.com/docs/integrations/document_loaders/blackboard/"}, "YandexGPT": {"Yandex": "https://python.langchain.com/docs/integrations/providers/yandex/", "YandexGPT": "https://python.langchain.com/docs/integrations/llms/yandex/"}, "UpstashVectorStore": {"Upstash Vector": "https://python.langchain.com/docs/integrations/vectorstores/upstash/"}, "NucliaTextTransformer": {"Nuclia": "https://python.langchain.com/docs/integrations/document_transformers/nuclia_transformer/"}, "NucliaLoader": {"Nuclia": "https://python.langchain.com/docs/integrations/document_loaders/nuclia/"}, "NucliaDB": {"Nuclia": "https://python.langchain.com/docs/integrations/providers/nuclia/", "NucliaDB": "https://python.langchain.com/docs/integrations/vectorstores/nucliadb/"}, "AnalyticDB": {"AnalyticDB": "https://python.langchain.com/docs/integrations/vectorstores/analyticdb/", "Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/"}, "GoogleApiYoutubeLoader": {"YouTube": "https://python.langchain.com/docs/integrations/providers/youtube/", "YouTube transcripts": "https://python.langchain.com/docs/integrations/document_loaders/youtube_transcript/"}, "PromptLayerOpenAI": {"PromptLayer": "https://python.langchain.com/docs/integrations/providers/promptlayer/", "PromptLayer OpenAI": "https://python.langchain.com/docs/integrations/llms/promptlayer_openai/"}, "USearch": {"USearch": "https://python.langchain.com/docs/integrations/vectorstores/usearch/"}, "EtherscanLoader": {"Etherscan": "https://python.langchain.com/docs/integrations/document_loaders/etherscan/"}, "Arcee": {"Arcee": "https://python.langchain.com/docs/integrations/llms/arcee/"}, "WhyLabsCallbackHandler": {"WhyLabs": "https://python.langchain.com/docs/integrations/providers/whylabs_profiling/"}, "YiLLM": {"01.AI": "https://python.langchain.com/docs/integrations/providers/yi/", "Yi": "https://python.langchain.com/docs/integrations/llms/yi/"}, "IuguLoader": {"Iugu": "https://python.langchain.com/docs/integrations/document_loaders/iugu/"}, "CouchbaseLoader": {"Couchbase": "https://python.langchain.com/docs/integrations/document_loaders/couchbase/"}, "FlyteCallbackHandler": {"Flyte": "https://python.langchain.com/docs/integrations/providers/flyte/"}, "wandb_tracing_enabled": {"WandB Tracing": "https://python.langchain.com/docs/integrations/providers/wandb_tracing/"}, "ManifestWrapper": {"Hazy Research": "https://python.langchain.com/docs/integrations/providers/hazy_research/", "Manifest": "https://python.langchain.com/docs/integrations/llms/manifest/"}, "OntotextGraphDBGraph": {"Ontotext GraphDB": "https://python.langchain.com/docs/integrations/graphs/ontotext/"}, "OntotextGraphDBQAChain": {"Ontotext GraphDB": "https://python.langchain.com/docs/integrations/graphs/ontotext/"}, "Marqo": {"Marqo": "https://python.langchain.com/docs/integrations/vectorstores/marqo/"}, "IMSDbLoader": {"IMSDb": "https://python.langchain.com/docs/integrations/document_loaders/imsdb/"}, "TiDBLoader": {"TiDB": "https://python.langchain.com/docs/integrations/document_loaders/tidb/"}, "TiDBVectorStore": {"TiDB": "https://python.langchain.com/docs/integrations/providers/tidb/", "TiDB Vector": "https://python.langchain.com/docs/integrations/vectorstores/tidb_vector/"}, "DeepInfra": {"DeepInfra": "https://python.langchain.com/docs/integrations/llms/deepinfra/"}, "RedditPostsLoader": {"Reddit": "https://python.langchain.com/docs/integrations/document_loaders/reddit/"}, "TrelloLoader": {"Trello": "https://python.langchain.com/docs/integrations/document_loaders/trello/"}, "AtlasDB": {"Atlas": "https://python.langchain.com/docs/integrations/vectorstores/atlas/"}, "SKLearnVectorStore": {"scikit-learn": "https://python.langchain.com/docs/integrations/vectorstores/sklearn/"}, "EverNoteLoader": {"EverNote": "https://python.langchain.com/docs/integrations/document_loaders/evernote/"}, "VDMS": {"VDMS": "https://python.langchain.com/docs/integrations/providers/vdms/", "Intel's Visual Data Management System (VDMS)": "https://python.langchain.com/docs/integrations/vectorstores/vdms/"}, "VDMS_Client": {"VDMS": "https://python.langchain.com/docs/integrations/providers/vdms/", "Intel's Visual Data Management System (VDMS)": "https://python.langchain.com/docs/integrations/vectorstores/vdms/"}, "TwitterTweetLoader": {"Twitter": "https://python.langchain.com/docs/integrations/document_loaders/twitter/"}, "DiscordChatLoader": {"Discord": "https://python.langchain.com/docs/integrations/document_loaders/discord/"}, "AssemblyAIAudioTranscriptLoader": {"AssemblyAI": "https://python.langchain.com/docs/integrations/providers/assemblyai/", "AssemblyAI Audio Transcripts": "https://python.langchain.com/docs/integrations/document_loaders/assemblyai/"}, "KineticaLoader": {"Kinetica": "https://python.langchain.com/docs/integrations/document_loaders/kinetica/"}, "ClearMLCallbackHandler": {"ClearML": "https://python.langchain.com/docs/integrations/providers/clearml_tracking/"}, "CohereRagRetriever": {"Cohere": "https://python.langchain.com/docs/integrations/providers/cohere/"}, "SlackDirectoryLoader": {"Slack": "https://python.langchain.com/docs/integrations/document_loaders/slack/"}, "OllamaEmbeddings": {"Ollama": "https://python.langchain.com/docs/integrations/providers/ollama/", "ApertureDB": "https://python.langchain.com/docs/integrations/vectorstores/aperturedb/"}, "HNLoader": {"Hacker News": "https://python.langchain.com/docs/integrations/document_loaders/hacker_news/", "Google Spanner": "https://python.langchain.com/docs/integrations/vectorstores/google_spanner/"}, "CTranslate2": {"CTranslate2": "https://python.langchain.com/docs/integrations/llms/ctranslate2/"}, "QianfanLLMEndpoint": {"Baidu": "https://python.langchain.com/docs/integrations/providers/baidu/", "Baidu Qianfan": "https://python.langchain.com/docs/integrations/llms/baidu_qianfan_endpoint/"}, "BESVectorStore": {"Baidu": "https://python.langchain.com/docs/integrations/providers/baidu/", "Baidu Cloud ElasticSearch VectorSearch": "https://python.langchain.com/docs/integrations/vectorstores/baiducloud_vector_search/"}, "BaiduVectorDB": {"Baidu": "https://python.langchain.com/docs/integrations/providers/baidu/", "Baidu VectorDB": "https://python.langchain.com/docs/integrations/vectorstores/baiduvectordb/"}, "Aphrodite": {"PygmalionAI": "https://python.langchain.com/docs/integrations/providers/pygmalionai/", "Aphrodite Engine": "https://python.langchain.com/docs/integrations/llms/aphrodite/"}, "PaiEasEndpoint": {"Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/", "Alibaba Cloud PAI EAS": "https://python.langchain.com/docs/integrations/llms/alibabacloud_pai_eas_endpoint/"}, "MaxComputeLoader": {"Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/", "Alibaba Cloud MaxCompute": "https://python.langchain.com/docs/integrations/document_loaders/alibaba_cloud_maxcompute/"}, "AlibabaCloudOpenSearch": {"Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/", "Alibaba Cloud OpenSearch": "https://python.langchain.com/docs/integrations/vectorstores/alibabacloud_opensearch/"}, "AlibabaCloudOpenSearchSettings": {"Alibaba Cloud": "https://python.langchain.com/docs/integrations/providers/alibaba_cloud/", "Alibaba Cloud OpenSearch": "https://python.langchain.com/docs/integrations/vectorstores/alibabacloud_opensearch/"}, "DocusaurusLoader": {"Docusaurus": "https://python.langchain.com/docs/integrations/document_loaders/docusaurus/"}, "Annoy": {"Annoy": "https://python.langchain.com/docs/integrations/vectorstores/annoy/"}, "BibtexLoader": {"BibTeX": "https://python.langchain.com/docs/integrations/document_loaders/bibtex/"}, "Yuan2": {"IEIT Systems": "https://python.langchain.com/docs/integrations/providers/ieit_systems/", "Yuan2.0": "https://python.langchain.com/docs/integrations/llms/yuan2/"}, "CassandraLoader": {"Cassandra": "https://python.langchain.com/docs/integrations/document_loaders/cassandra/"}, "Vearch": {"Vearch": "https://python.langchain.com/docs/integrations/vectorstores/vearch/"}, "JoplinLoader": {"Joplin": "https://python.langchain.com/docs/integrations/document_loaders/joplin/"}, "ArthurCallbackHandler": {"Arthur": "https://python.langchain.com/docs/integrations/providers/arthur_tracking/"}, "AcreomLoader": {"Acreom": "https://python.langchain.com/docs/integrations/providers/acreom/", "acreom": "https://python.langchain.com/docs/integrations/document_loaders/acreom/"}, "KDBAI": {"KDB.AI": "https://python.langchain.com/docs/integrations/vectorstores/kdbai/"}, "DuckDBLoader": {"DuckDB": "https://python.langchain.com/docs/integrations/document_loaders/duckdb/"}, "Petals": {"Petals": "https://python.langchain.com/docs/integrations/llms/petals/"}, "MomentoVectorIndex": {"Momento": "https://python.langchain.com/docs/integrations/providers/momento/", "Momento Vector Index (MVI)": "https://python.langchain.com/docs/integrations/vectorstores/momento_vector_index/"}, "NIBittensorLLM": {"Bittensor": "https://python.langchain.com/docs/integrations/llms/bittensor/"}, "AirtableLoader": {"Airtable": "https://python.langchain.com/docs/integrations/document_loaders/airtable/"}, "LarkSuiteDocLoader": {"ByteDance": "https://python.langchain.com/docs/integrations/providers/byte_dance/", "LarkSuite (FeiShu)": "https://python.langchain.com/docs/integrations/document_loaders/larksuite/"}, "JavelinAIGateway": {"Javelin AI Gateway": "https://python.langchain.com/docs/integrations/providers/javelin_ai_gateway/", "Javelin AI Gateway Tutorial": "https://python.langchain.com/docs/integrations/llms/javelin/"}, "JavelinAIGatewayEmbeddings": {"Javelin AI Gateway": "https://python.langchain.com/docs/integrations/providers/javelin_ai_gateway/", "Javelin AI Gateway Tutorial": "https://python.langchain.com/docs/integrations/llms/javelin/"}, "ChatJavelinAIGateway": {"Javelin AI Gateway": "https://python.langchain.com/docs/integrations/providers/javelin_ai_gateway/", "Javelin AI Gateway Tutorial": "https://python.langchain.com/docs/integrations/llms/javelin/"}, "TensorflowDatasetLoader": {"TensorFlow Datasets": "https://python.langchain.com/docs/integrations/document_loaders/tensorflow_datasets/"}, "Clarifai": {"Clarifai": "https://python.langchain.com/docs/integrations/llms/clarifai/"}, "DataheraldTextToSQL": {"Dataherald": "https://python.langchain.com/docs/integrations/providers/dataherald/"}, "RoamLoader": {"Roam": "https://python.langchain.com/docs/integrations/document_loaders/roam/"}, "RerankConfig": {"Vectara Chat": "https://python.langchain.com/docs/integrations/providers/vectara/vectara_chat/", "Vectara": "https://python.langchain.com/docs/integrations/vectorstores/vectara/"}, "SummaryConfig": {"Vectara Chat": "https://python.langchain.com/docs/integrations/providers/vectara/vectara_chat/", "Vectara": "https://python.langchain.com/docs/integrations/vectorstores/vectara/"}, "VectaraQueryConfig": {"Vectara Chat": "https://python.langchain.com/docs/integrations/providers/vectara/vectara_chat/", "Vectara": "https://python.langchain.com/docs/integrations/vectorstores/vectara/"}, "PebbloRetrievalQA": {"Identity-enabled RAG using PebbloRetrievalQA": "https://python.langchain.com/docs/integrations/providers/pebblo/pebblo_retrieval_qa/"}, "AuthContext": {"Identity-enabled RAG using PebbloRetrievalQA": "https://python.langchain.com/docs/integrations/providers/pebblo/pebblo_retrieval_qa/"}, "ChainInput": {"Identity-enabled RAG using PebbloRetrievalQA": "https://python.langchain.com/docs/integrations/providers/pebblo/pebblo_retrieval_qa/"}, "SemanticContext": {"Identity-enabled RAG using PebbloRetrievalQA": "https://python.langchain.com/docs/integrations/providers/pebblo/pebblo_retrieval_qa/"}, "RedisStore": {"RedisStore": "https://python.langchain.com/docs/integrations/stores/redis/"}, "CassandraByteStore": {"CassandraByteStore": "https://python.langchain.com/docs/integrations/stores/cassandra/"}, "UpstashRedisByteStore": {"UpstashRedisByteStore": "https://python.langchain.com/docs/integrations/stores/upstash_redis/"}, "ApacheDorisSettings": {"Apache Doris": "https://python.langchain.com/docs/integrations/vectorstores/apache_doris/"}, "DistanceStrategy": {"Kinetica Vectorstore API": "https://python.langchain.com/docs/integrations/vectorstores/kinetica/", "SAP HANA Cloud Vector Engine": "https://python.langchain.com/docs/integrations/vectorstores/sap_hanavector/", "SingleStoreDB": "https://python.langchain.com/docs/integrations/vectorstores/singlestoredb/", "Oracle AI Vector Search: Vector Store": "https://python.langchain.com/docs/integrations/vectorstores/oracle/", "SemaDB": "https://python.langchain.com/docs/integrations/vectorstores/semadb/"}, "SentenceTransformerEmbeddings": {"SQLite-VSS": "https://python.langchain.com/docs/integrations/vectorstores/sqlitevss/", "Vespa": "https://python.langchain.com/docs/integrations/vectorstores/vespa/"}, "Vald": {"Vald": "https://python.langchain.com/docs/integrations/vectorstores/vald/"}, "RetrievalQAWithSourcesChain": {"Weaviate": "https://python.langchain.com/docs/integrations/vectorstores/weaviate/", "Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/", "Jaguar Vector Database": "https://python.langchain.com/docs/integrations/vectorstores/jaguar/", "Neo4j Vector Index": "https://python.langchain.com/docs/integrations/vectorstores/neo4jvector/", "Marqo": "https://python.langchain.com/docs/integrations/vectorstores/marqo/", "Psychic": "https://python.langchain.com/docs/integrations/document_loaders/psychic/"}, "Yellowbrick": {"Yellowbrick": "https://python.langchain.com/docs/integrations/vectorstores/yellowbrick/"}, "LLMRails": {"LLMRails": "https://python.langchain.com/docs/integrations/vectorstores/llm_rails/"}, "ChatGooglePalm": {"ScaNN": "https://python.langchain.com/docs/integrations/vectorstores/scann/"}, "Hippo": {"Hippo": "https://python.langchain.com/docs/integrations/vectorstores/hippo/"}, "RedisText": {"Redis": "https://python.langchain.com/docs/integrations/vectorstores/redis/"}, "RedisNum": {"Redis": "https://python.langchain.com/docs/integrations/vectorstores/redis/"}, "RedisTag": {"Redis": "https://python.langchain.com/docs/integrations/vectorstores/redis/"}, "RedisFilter": {"Redis": "https://python.langchain.com/docs/integrations/vectorstores/redis/"}, "VespaStore": {"Vespa": "https://python.langchain.com/docs/integrations/vectorstores/vespa/"}, "NeuralDBVectorStore": {"ThirdAI NeuralDB": "https://python.langchain.com/docs/integrations/vectorstores/thirdai_neuraldb/"}, "VikingDB": {"viking DB": "https://python.langchain.com/docs/integrations/vectorstores/vikingdb/"}, "VikingDBConfig": {"viking DB": "https://python.langchain.com/docs/integrations/vectorstores/vikingdb/"}, "ApertureDB": {"ApertureDB": "https://python.langchain.com/docs/integrations/vectorstores/aperturedb/"}, "Relyt": {"Relyt": "https://python.langchain.com/docs/integrations/vectorstores/relyt/"}, "oraclevs": {"Oracle AI Vector Search: Vector Store": "https://python.langchain.com/docs/integrations/vectorstores/oracle/"}, "VLite": {"vlite": "https://python.langchain.com/docs/integrations/vectorstores/vlite/"}, "AzureCosmosDBNoSqlVectorSearch": {"Azure Cosmos DB No SQL": "https://python.langchain.com/docs/integrations/vectorstores/azure_cosmos_db_no_sql/"}, "DuckDB": {"DuckDB": "https://python.langchain.com/docs/integrations/vectorstores/duckdb/"}, "StarRocksSettings": {"StarRocks": "https://python.langchain.com/docs/integrations/vectorstores/starrocks/"}, "PathwayVectorClient": {"Pathway": "https://python.langchain.com/docs/integrations/vectorstores/pathway/"}, "DocArrayHnswSearch": {"DocArray HnswSearch": "https://python.langchain.com/docs/integrations/vectorstores/docarray_hnsw/"}, "TileDB": {"TileDB": "https://python.langchain.com/docs/integrations/vectorstores/tiledb/"}, "EcloudESVectorStore": {"China Mobile ECloud ElasticSearch VectorSearch": "https://python.langchain.com/docs/integrations/vectorstores/ecloud_vector_search/"}, "SurrealDBStore": {"SurrealDB": "https://python.langchain.com/docs/integrations/vectorstores/surrealdb/"}, "ManticoreSearch": {"ManticoreSearch VectorStore": "https://python.langchain.com/docs/integrations/vectorstores/manticore_search/"}, "ManticoreSearchSettings": {"ManticoreSearch VectorStore": "https://python.langchain.com/docs/integrations/vectorstores/manticore_search/"}, "HuggingFaceEmbeddings": {"Aerospike": "https://python.langchain.com/docs/integrations/vectorstores/aerospike/", "self-query-qdrant": "https://python.langchain.com/docs/templates/self-query-qdrant/"}, "Aerospike": {"Aerospike": "https://python.langchain.com/docs/integrations/vectorstores/aerospike/"}, "ElasticVectorSearch": {"Elasticsearch": "https://python.langchain.com/docs/integrations/vectorstores/elasticsearch/"}, "PGVecto_rs": {"PGVecto.rs": "https://python.langchain.com/docs/integrations/vectorstores/pgvecto_rs/"}, "ZepVectorStore": {"Zep": "https://python.langchain.com/docs/integrations/vectorstores/zep/"}, "CollectionConfig": {"Zep": "https://python.langchain.com/docs/integrations/vectorstores/zep/"}, "openai": {"OpenAI Adapter(Old)": "https://python.langchain.com/docs/integrations/adapters/openai-old/", "OpenAI Adapter": "https://python.langchain.com/docs/integrations/adapters/openai/"}, "RankLLMRerank": {"RankLLM Reranker": "https://python.langchain.com/docs/integrations/document_transformers/rankllm-reranker/"}, "AsyncChromiumLoader": {"Beautiful Soup": "https://python.langchain.com/docs/integrations/document_transformers/beautiful_soup/", "Async Chromium": "https://python.langchain.com/docs/integrations/document_loaders/async_chromium/"}, "BeautifulSoupTransformer": {"Beautiful Soup": "https://python.langchain.com/docs/integrations/document_transformers/beautiful_soup/"}, "VolcengineRerank": {"Volcengine Reranker": "https://python.langchain.com/docs/integrations/document_transformers/volcengine_rerank/"}, "OpenVINOReranker": {"OpenVINO Reranker": "https://python.langchain.com/docs/integrations/document_transformers/openvino_rerank/"}, "create_metadata_tagger": {"OpenAI metadata tagger": "https://python.langchain.com/docs/integrations/document_transformers/openai_metadata_tagger/"}, "DoctranPropertyExtractor": {"Doctran: extract properties": "https://python.langchain.com/docs/integrations/document_transformers/doctran_extract_properties/"}, "DoctranQATransformer": {"Doctran: interrogate documents": "https://python.langchain.com/docs/integrations/document_transformers/doctran_interrogate_document/"}, "CrossEncoderReranker": {"Cross Encoder Reranker": "https://python.langchain.com/docs/integrations/document_transformers/cross_encoder_reranker/"}, "HuggingFaceCrossEncoder": {"Cross Encoder Reranker": "https://python.langchain.com/docs/integrations/document_transformers/cross_encoder_reranker/"}, "JinaRerank": {"Jina Reranker": "https://python.langchain.com/docs/integrations/document_transformers/jina_rerank/"}, "DoctranTextTranslator": {"Doctran: language translation": "https://python.langchain.com/docs/integrations/document_transformers/doctran_translate_document/"}, "MarkdownifyTransformer": {"Markdownify": "https://python.langchain.com/docs/integrations/document_transformers/markdownify/"}, "DashScopeRerank": {"DashScope Reranker": "https://python.langchain.com/docs/integrations/document_transformers/dashscope_rerank/"}, "XorbitsLoader": {"Xorbits Pandas DataFrame": "https://python.langchain.com/docs/integrations/document_loaders/xorbits/"}, "OutlookMessageLoader": {"Email": "https://python.langchain.com/docs/integrations/document_loaders/email/"}, "TranscriptFormat": {"AssemblyAI Audio Transcripts": "https://python.langchain.com/docs/integrations/document_loaders/assemblyai/", "YouTube transcripts": "https://python.langchain.com/docs/integrations/document_loaders/youtube_transcript/"}, "ScrapingAntLoader": {"ScrapingAnt": "https://python.langchain.com/docs/integrations/document_loaders/scrapingant/"}, "AirbyteSalesforceLoader": {"Airbyte Salesforce (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_salesforce/"}, "AirbyteCDKLoader": {"Airbyte CDK (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_cdk/"}, "Docx2txtLoader": {"Microsoft Word": "https://python.langchain.com/docs/integrations/document_loaders/microsoft_word/"}, "RSpaceLoader": {"# replace these ids with some from your own research notes.": "https://python.langchain.com/docs/integrations/document_loaders/rspace/"}, "SeleniumURLLoader": {"URL": "https://python.langchain.com/docs/integrations/document_loaders/url/"}, "PlaywrightURLLoader": {"URL": "https://python.langchain.com/docs/integrations/document_loaders/url/"}, "AirbyteJSONLoader": {"Airbyte JSON (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_json/"}, "AirbyteStripeLoader": {"Airbyte Stripe (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_stripe/"}, "GeoDataFrameLoader": {"Geopandas": "https://python.langchain.com/docs/integrations/document_loaders/geopandas/"}, "VectorstoreIndexCreator": {"HuggingFace dataset": "https://python.langchain.com/docs/integrations/document_loaders/hugging_face_dataset/", "Spreedly": "https://python.langchain.com/docs/integrations/document_loaders/spreedly/", "Figma": "https://python.langchain.com/docs/integrations/document_loaders/figma/", "Apify Dataset": "https://python.langchain.com/docs/integrations/document_loaders/apify_dataset/", "Iugu": "https://python.langchain.com/docs/integrations/document_loaders/iugu/", "Stripe": "https://python.langchain.com/docs/integrations/document_loaders/stripe/", "Modern Treasury": "https://python.langchain.com/docs/integrations/document_loaders/modern_treasury/"}, "AirbyteTypeformLoader": {"Airbyte Typeform (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_typeform/"}, "MHTMLLoader": {"mhtml": "https://python.langchain.com/docs/integrations/document_loaders/mhtml/"}, "SpiderLoader": {"Spider": "https://python.langchain.com/docs/integrations/document_loaders/spider/"}, "NewsURLLoader": {"News URL": "https://python.langchain.com/docs/integrations/document_loaders/news/"}, "ImageCaptionLoader": {"Image captions": "https://python.langchain.com/docs/integrations/document_loaders/image_captions/"}, "LLMSherpaFileLoader": {"LLM Sherpa": "https://python.langchain.com/docs/integrations/document_loaders/llmsherpa/"}, "PyMuPDFLoader": {"PyMuPDF": "https://python.langchain.com/docs/integrations/document_loaders/pymupdf/"}, "ScrapflyLoader": {"# ScrapFly": "https://python.langchain.com/docs/integrations/document_loaders/scrapfly/"}, "TomlLoader": {"TOML": "https://python.langchain.com/docs/integrations/document_loaders/toml/"}, "PsychicLoader": {"Psychic": "https://python.langchain.com/docs/integrations/document_loaders/psychic/"}, "FireCrawlLoader": {"FireCrawl": "https://python.langchain.com/docs/integrations/document_loaders/firecrawl/"}, "LarkSuiteWikiLoader": {"LarkSuite (FeiShu)": "https://python.langchain.com/docs/integrations/document_loaders/larksuite/"}, "FakeListLLM": {"LarkSuite (FeiShu)": "https://python.langchain.com/docs/integrations/document_loaders/larksuite/"}, "MergedDataLoader": {"Merge Documents Loader": "https://python.langchain.com/docs/integrations/document_loaders/merge_doc/"}, "RecursiveUrlLoader": {"Recursive URL": "https://python.langchain.com/docs/integrations/document_loaders/recursive_url/"}, "PDFPlumberLoader": {"PDFPlumber": "https://python.langchain.com/docs/integrations/document_loaders/pdfplumber/"}, "PyPDFium2Loader": {"PyPDFium2Loader": "https://python.langchain.com/docs/integrations/document_loaders/pypdfium2/"}, "AirbyteHubspotLoader": {"Airbyte Hubspot (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_hubspot/"}, "AirbyteGongLoader": {"Airbyte Gong (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_gong/"}, "AstraDBLoader": {"AstraDB": "https://python.langchain.com/docs/integrations/document_loaders/astradb/"}, "ReadTheDocsLoader": {"ReadTheDocs Documentation": "https://python.langchain.com/docs/integrations/document_loaders/readthedocs_documentation/"}, "MathpixPDFLoader": {"MathPixPDFLoader": "https://python.langchain.com/docs/integrations/document_loaders/mathpix/"}, "PolarsDataFrameLoader": {"Polars DataFrame": "https://python.langchain.com/docs/integrations/document_loaders/polars_dataframe/"}, "DataFrameLoader": {"Pandas DataFrame": "https://python.langchain.com/docs/integrations/document_loaders/pandas_dataframe/"}, "SurrealDBLoader": {"SurrealDB": "https://python.langchain.com/docs/integrations/document_loaders/surrealdb/"}, "DedocFileLoader": {"Dedoc": "https://python.langchain.com/docs/integrations/document_loaders/dedoc/"}, "DedocPDFLoader": {"Dedoc": "https://python.langchain.com/docs/integrations/document_loaders/dedoc/"}, "DedocAPIFileLoader": {"Dedoc": "https://python.langchain.com/docs/integrations/document_loaders/dedoc/"}, "GoogleApiClient": {"YouTube transcripts": "https://python.langchain.com/docs/integrations/document_loaders/youtube_transcript/"}, "ConcurrentLoader": {"Concurrent Loader": "https://python.langchain.com/docs/integrations/document_loaders/concurrent/"}, "RSSFeedLoader": {"RSS Feeds": "https://python.langchain.com/docs/integrations/document_loaders/rss/"}, "PebbloSafeLoader": {"Pebblo Safe DocumentLoader": "https://python.langchain.com/docs/integrations/document_loaders/pebblo/"}, "VsdxLoader": {"Vsdx": "https://python.langchain.com/docs/integrations/document_loaders/vsdx/"}, "NotebookLoader": {"Jupyter Notebook": "https://python.langchain.com/docs/integrations/document_loaders/jupyter_notebook/"}, "OracleAutonomousDatabaseLoader": {"Oracle Autonomous Database": "https://python.langchain.com/docs/integrations/document_loaders/oracleadb_loader/"}, "LanguageParser": {"Source Code": "https://python.langchain.com/docs/integrations/document_loaders/source_code/"}, "SRTLoader": {"Subtitle": "https://python.langchain.com/docs/integrations/document_loaders/subtitle/"}, "MastodonTootsLoader": {"Mastodon": "https://python.langchain.com/docs/integrations/document_loaders/mastodon/"}, "AirbyteShopifyLoader": {"Airbyte Shopify (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_shopify/"}, "PyPDFDirectoryLoader": {"PyPDFDirectoryLoader": "https://python.langchain.com/docs/integrations/document_loaders/pypdfdirectory/"}, "PySparkDataFrameLoader": {"PySpark": "https://python.langchain.com/docs/integrations/document_loaders/pyspark_dataframe/"}, "AirbyteZendeskSupportLoader": {"Airbyte Zendesk Support (Deprecated)": "https://python.langchain.com/docs/integrations/document_loaders/airbyte_zendesk_support/"}, "CoNLLULoader": {"CoNLL-U": "https://python.langchain.com/docs/integrations/document_loaders/conll-u/"}, "MongodbLoader": {"MongoDB": "https://python.langchain.com/docs/integrations/document_loaders/mongodb/"}, "SitemapLoader": {"Sitemap": "https://python.langchain.com/docs/integrations/document_loaders/sitemap/"}, "YuqueLoader": {"Yuque": "https://python.langchain.com/docs/integrations/document_loaders/yuque/"}, "PDFMinerLoader": {"PDFMiner": "https://python.langchain.com/docs/integrations/document_loaders/pdfminer/"}, "PDFMinerPDFasHTMLLoader": {"PDFMiner": "https://python.langchain.com/docs/integrations/document_loaders/pdfminer/"}, "QuipLoader": {"Quip": "https://python.langchain.com/docs/integrations/document_loaders/quip/"}, "LangSmithLoader": {"LangSmithLoader": "https://python.langchain.com/docs/integrations/document_loaders/langsmith/"}, "MemgraphGraph": {"Memgraph": "https://python.langchain.com/docs/integrations/graphs/memgraph/"}, "GraphSparqlQAChain": {"RDFLib": "https://python.langchain.com/docs/integrations/graphs/rdflib_sparql/"}, "RdfGraph": {"RDFLib": "https://python.langchain.com/docs/integrations/graphs/rdflib_sparql/"}, "NebulaGraphQAChain": {"NebulaGraph": "https://python.langchain.com/docs/integrations/graphs/nebula_graph/"}, "NebulaGraph": {"NebulaGraph": "https://python.langchain.com/docs/integrations/graphs/nebula_graph/"}, "GremlinQAChain": {"Azure Cosmos DB for Apache Gremlin": "https://python.langchain.com/docs/integrations/graphs/azure_cosmosdb_gremlin/"}, "GraphIndexCreator": {"NetworkX": "https://python.langchain.com/docs/integrations/graphs/networkx/"}, "GraphQAChain": {"NetworkX": "https://python.langchain.com/docs/integrations/graphs/networkx/"}, "NetworkxEntityGraph": {"NetworkX": "https://python.langchain.com/docs/integrations/graphs/networkx/"}, "HugeGraphQAChain": {"HugeGraph": "https://python.langchain.com/docs/integrations/graphs/hugegraph/"}, "HugeGraph": {"HugeGraph": "https://python.langchain.com/docs/integrations/graphs/hugegraph/"}, "AGEGraph": {"Apache AGE": "https://python.langchain.com/docs/integrations/graphs/apache_age/"}, "KuzuQAChain": {"Kuzu": "https://python.langchain.com/docs/integrations/graphs/kuzu_db/"}, "KuzuGraph": {"Kuzu": "https://python.langchain.com/docs/integrations/graphs/kuzu_db/"}, "FalkorDBQAChain": {"FalkorDB": "https://python.langchain.com/docs/integrations/graphs/falkordb/"}, "FalkorDBGraph": {"FalkorDB": "https://python.langchain.com/docs/integrations/graphs/falkordb/"}, "ConversationBufferWindowMemory": {"Baseten": "https://python.langchain.com/docs/integrations/llms/baseten/", "OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/"}, "Solar": {"Solar": "https://python.langchain.com/docs/integrations/llms/solar/"}, "GoogleSearchAPIWrapper": {"Bittensor": "https://python.langchain.com/docs/integrations/llms/bittensor/"}, "IpexLLM": {"IPEX-LLM": "https://python.langchain.com/docs/integrations/llms/ipex_llm/"}, "LLMContentHandler": {"SageMakerEndpoint": "https://python.langchain.com/docs/integrations/llms/sagemaker/"}, "TextGen": {"TextGen": "https://python.langchain.com/docs/integrations/llms/textgen/"}, "MosaicML": {"MosaicML": "https://python.langchain.com/docs/integrations/llms/mosaicml/"}, "VolcEngineMaasLLM": {"Volc Engine Maas": "https://python.langchain.com/docs/integrations/llms/volcengine_maas/"}, "KoboldApiLLM": {"KoboldAI API": "https://python.langchain.com/docs/integrations/llms/koboldai/"}, "Konko": {"Konko": "https://python.langchain.com/docs/integrations/llms/konko/"}, "OpaquePrompts": {"OpaquePrompts": "https://python.langchain.com/docs/integrations/llms/opaqueprompts/"}, "TitanTakeoff": {"Titan Takeoff": "https://python.langchain.com/docs/integrations/llms/titan_takeoff/"}, "Friendli": {"Friendli": "https://python.langchain.com/docs/integrations/llms/friendli/"}, "Databricks": {"Databricks": "https://python.langchain.com/docs/integrations/llms/databricks/"}, "LMFormatEnforcer": {"LM Format Enforcer": "https://python.langchain.com/docs/integrations/llms/lmformatenforcer_experimental/"}, "VLLM": {"vLLM": "https://python.langchain.com/docs/integrations/llms/vllm/"}, "VLLMOpenAI": {"vLLM": "https://python.langchain.com/docs/integrations/llms/vllm/"}, "CustomOpenAIContentFormatter": {"Azure ML": "https://python.langchain.com/docs/integrations/llms/azure_ml/"}, "ContentFormatterBase": {"Azure ML": "https://python.langchain.com/docs/integrations/llms/azure_ml/"}, "DollyContentFormatter": {"Azure ML": "https://python.langchain.com/docs/integrations/llms/azure_ml/"}, "load_llm": {"Azure ML": "https://python.langchain.com/docs/integrations/llms/azure_ml/"}, "MapReduceChain": {"Manifest": "https://python.langchain.com/docs/integrations/llms/manifest/"}, "ModelLaboratory": {"Manifest": "https://python.langchain.com/docs/integrations/llms/manifest/"}, "ExLlamaV2": {"ExLlamaV2": "https://python.langchain.com/docs/integrations/llms/exllamav2/"}, "RELLM": {"RELLM": "https://python.langchain.com/docs/integrations/llms/rellm_experimental/"}, "Moonshot": {"MoonshotChat": "https://python.langchain.com/docs/integrations/llms/moonshot/"}, "OpenLM": {"OpenLM": "https://python.langchain.com/docs/integrations/llms/openlm/"}, "CloudflareWorkersAI": {"Cloudflare Workers AI": "https://python.langchain.com/docs/integrations/llms/cloudflare_workersai/"}, "ChatGLM3": {"ChatGLM": "https://python.langchain.com/docs/integrations/llms/chatglm/"}, "ChatGLM": {"ChatGLM": "https://python.langchain.com/docs/integrations/llms/chatglm/"}, "Sambaverse": {"SambaNova": "https://python.langchain.com/docs/integrations/llms/sambanova/"}, "SambaStudio": {"SambaNova": "https://python.langchain.com/docs/integrations/llms/sambanova/"}, "LayerupSecurity": {"Layerup Security": "https://python.langchain.com/docs/integrations/llms/layerup_security/"}, "JsonFormer": {"JSONFormer": "https://python.langchain.com/docs/integrations/llms/jsonformer_experimental/"}, "WeightOnlyQuantPipeline": {"Intel Weight-Only Quantization": "https://python.langchain.com/docs/integrations/llms/weight_only_quantization/"}, "Replicate": {"Replicate": "https://python.langchain.com/docs/integrations/llms/replicate/"}, "tracing_v2_enabled": {"Chat Bot Feedback Template": "https://python.langchain.com/docs/templates/chat-bot-feedback/"}, "QuerySQLDataBaseTool": {"Build a Question/Answering system over SQL data": "https://python.langchain.com/docs/tutorials/sql_qa/"}, "OPENAI_TEMPLATE": {"Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/"}, "create_openai_data_generator": {"Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/"}, "DatasetGenerator": {"Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/"}, "create_data_generation_chain": {"Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/"}, "create_extraction_chain_pydantic": {"Generate Synthetic Data": "https://python.langchain.com/docs/tutorials/data_generation/"}}