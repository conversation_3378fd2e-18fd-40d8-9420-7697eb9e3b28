{"cells": [{"cell_type": "markdown", "id": "66a7777e", "metadata": {}, "source": ["# Yuque\n", "\n", ">[<PERSON><PERSON>](https://www.yuque.com/) is a professional cloud-based knowledge base for team collaboration in documentation.\n", "\n", "This notebook covers how to load documents from `Yuque`.\n", "\n", "You can obtain the personal access token by clicking on your personal avatar in the [Personal Settings](https://www.yuque.com/settings/tokens) page."]}, {"cell_type": "code", "execution_count": 1, "id": "9ec8a3b3", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_community.document_loaders import YuqueLoader"]}, {"cell_type": "code", "execution_count": 2, "id": "2ea958f0327ed6e8", "metadata": {"collapsed": false}, "outputs": [], "source": ["loader = YuqueLoader(access_token=\"<your_personal_access_token>\")"]}, {"cell_type": "code", "execution_count": null, "id": "3470dadf", "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["docs = loader.load()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}