{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# TSV\n", "\n", ">A [tab-separated values (TSV)](https://en.wikipedia.org/wiki/Tab-separated_values) file is a simple, text-based file format for storing tabular data.[3] Records are separated by newlines, and values within a record are separated by tab characters."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `UnstructuredTSVLoader`\n", "\n", "You can also load the table using the `UnstructuredTSVLoader`. One advantage of using `UnstructuredTSVLoader` is that if you use it in `\"elements\"` mode, an HTML representation of the table will be available in the metadata."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<table border=\"1\" class=\"dataframe\">\n", "  <tbody>\n", "    <tr>\n", "      <td>Team, \"Payroll (millions)\", \"Wins\"</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Nationals,     81.34, 98</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Reds,          82.20, 97</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Yankees,      197.96, 95</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Giants,       117.62, 94</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Braves,        83.31, 94</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Athletics,     55.37, 94</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Rangers,      120.51, 93</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Orioles,       81.43, 93</td>\n", "    </tr>\n", "    <tr>\n", "      <td><PERSON><PERSON>,          64.17, 90</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Angels,       154.49, 89</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Tigers,       132.30, 88</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Cardinals,    110.30, 88</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Dodgers,       95.14, 86</td>\n", "    </tr>\n", "    <tr>\n", "      <td>White Sox,     96.92, 85</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Brewers,       97.65, 83</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Phillies,     174.54, 81</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Diamondbacks,  74.28, 81</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Pirates,       63.43, 79</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Padres,        55.24, 76</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Mariners,      81.97, 75</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Mets,          93.35, 74</td>\n", "    </tr>\n", "    <tr>\n", "      <td><PERSON> Jays,     75.48, 73</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Royals,        60.91, 72</td>\n", "    </tr>\n", "    <tr>\n", "      <td><PERSON><PERSON>,      118.07, 69</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Red Sox,      173.18, 69</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Indians,       78.43, 68</td>\n", "    </tr>\n", "    <tr>\n", "      <td><PERSON>,         94.08, 66</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Rockies,       78.06, 64</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Cubs,          88.19, 61</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Astros,        60.65, 55</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"]}], "source": ["from langchain_community.document_loaders.tsv import UnstructuredTSVLoader\n", "\n", "loader = UnstructuredTSVLoader(\n", "    file_path=\"./example_data/mlb_teams_2012.csv\", mode=\"elements\"\n", ")\n", "docs = loader.load()\n", "\n", "print(docs[0].metadata[\"text_as_html\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 4}