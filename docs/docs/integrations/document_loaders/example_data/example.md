# Sample Markdown Document

## Introduction

Welcome to this sample Markdown document. Markdown is a lightweight markup language used for formatting text. It's widely used for documentation, readme files, and more.

## Features

### Headers

Markdown supports multiple levels of headers:

- **Header 1**: `# Header 1`
- **Header 2**: `## Header 2`
- **Header 3**: `### Header 3`

### Lists

#### Unordered List

- Item 1
- Item 2
  - Subitem 2.1
  - Subitem 2.2

#### Ordered List

1. First item
2. Second item
3. Third item

### Links

[OpenAI](https://www.openai.com) is an AI research organization.

### Images

Here's an example image:

![Sample Image](https://via.placeholder.com/150)

### Code

#### Inline Code

Use `code` for inline code snippets.

#### Code Block

```python
def greet(name):
    return f"Hello, {name}!"

print(greet("World"))
```
