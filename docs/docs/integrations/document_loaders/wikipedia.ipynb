{"cells": [{"cell_type": "markdown", "id": "bda1f3f5", "metadata": {}, "source": ["# Wikipedia\n", "\n", ">[Wikipedia](https://wikipedia.org/) is a multilingual free online encyclopedia written and maintained by a community of volunteers, known as Wikipedians, through open collaboration and using a wiki-based editing system called MediaWiki. `Wikipedia` is the largest and most-read reference work in history.\n", "\n", "This notebook shows how to load wiki pages from `wikipedia.org` into the Document format that we use downstream."]}, {"cell_type": "markdown", "id": "1b7a1eef-7bf7-4e7d-8bfc-c4e27c9488cb", "metadata": {}, "source": ["## Installation\n", "\n", "First, you need to install the `langchain_community` and `wikipedia` packages."]}, {"cell_type": "code", "execution_count": null, "id": "b674aaea-ed3a-4541-8414-260a8f67f623", "metadata": {"tags": []}, "outputs": [], "source": ["%pip install -qU langchain_community wikipedia"]}, {"cell_type": "markdown", "id": "98342290", "metadata": {}, "source": ["## Parameters\n", "\n", "`WikipediaLoader` has the following arguments:\n", "- `query`: the free text which used to find documents in Wikipedia\n", "- `lang` (optional): default=\"en\". Use it to search in a specific language part of Wikipedia\n", "- `load_max_docs` (optional): default=100. Use it to limit number of downloaded documents. It takes time to download all 100 documents, so use a small number for experiments. There is a hard limit of 300 for now.\n", "- `load_all_available_meta` (optional): default=False. By default only the most important fields downloaded: `title` and `summary`. If `True` then all available fields will be downloaded.\n", "- `doc_content_chars_max` (optional): default=4000. The maximum number of characters for the document content."]}, {"cell_type": "markdown", "id": "95f05e1c-195e-4e2b-ae8e-8d6637f15be6", "metadata": {}, "source": ["## Example"]}, {"cell_type": "code", "execution_count": 1, "id": "9bfd5e46", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import WikipediaLoader"]}, {"cell_type": "code", "execution_count": 2, "id": "700e4ef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["docs = WikipediaLoader(query=\"HUNTER X HUNTER\", load_max_docs=2).load()\n", "len(docs)"]}, {"cell_type": "code", "execution_count": 3, "id": "8977bac0-0042-4f23-9754-247dbd32439b", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["{'title': '<PERSON> × <PERSON>',\n", " 'summary': '<PERSON> × Hunter (pronounced \"hunter hunter\") is a Japanese manga series written and illustrated by <PERSON><PERSON><PERSON>. It has been serialized in Shueisha\\'s shōnen manga magazine Weekly Shōnen Jump since March 1998, although the manga has frequently gone on extended hiatuses since 2006. Its chapters have been collected in 37 tankōbon volumes as of November 2022. The story focuses on a young boy named <PERSON><PERSON> who discovers that his father, who left him at a young age, is actually a world-renowned Hunter, a licensed professional who specializes in fantastical pursuits such as locating rare or unidentified animal species, treasure hunting, surveying unexplored enclaves, or hunting down lawless individuals. <PERSON><PERSON> departs on a journey to become a Hunter and eventually find his father. Along the way, <PERSON><PERSON> meets various other Hunters and encounters the paranormal.\\nHunter × Hunter was adapted into a 62-episode anime television series by Nippon Animation and directed by <PERSON><PERSON><PERSON>, which ran on Fuji Television from October 1999 to March 2001. Three separate original video animations (OVAs) totaling 30 episodes were subsequently produced by Nippon Animation and released in Japan from 2002 to 2004. A second anime television series by Madhouse aired on Nippon Television from October 2011 to September 2014, totaling 148 episodes, with two animated theatrical films released in 2013. There are also numerous audio albums, video games, musicals, and other media based on Hunter × Hunter.\\nThe manga has been licensed for English release in North America by Viz Media since April 2005. Both television series have been also licensed by Viz Media, with the first series having aired on the Funimation Channel in 2009 and the second series broadcast on Adult Swim\\'s Toonami programming block from April 2016 to June 2019.\\nHunter × Hunter has been a huge critical and financial success and has become one of the best-selling manga series of all time, having over 84 million copies in circulation by July 2022.',\n", " 'source': 'https://en.wikipedia.org/wiki/<PERSON>_%C3%97_Hunter'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0].metadata  # metadata of the first document"]}, {"cell_type": "code", "execution_count": 4, "id": "46969806-45a9-4c4d-a61b-cfb9658fc9de", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["'<PERSON> × Hunter (pronounced \"hunter hunter\") is a Japanese manga series written and illustrated by <PERSON><PERSON><PERSON>. It has been serialized in Shueisha\\'s shōnen manga magazine Weekly Shōnen Jump since March 1998, although the manga has frequently gone on extended hiatuses since 2006. Its chapters have been collected in 37 tankōbon volumes as of November 2022. The story focuses on a young boy name'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0].page_content[:400]  # a part of the page content"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}