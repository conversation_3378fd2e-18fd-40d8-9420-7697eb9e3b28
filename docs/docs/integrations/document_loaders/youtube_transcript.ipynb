{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "df770c72", "metadata": {}, "source": ["# YouTube transcripts\n", "\n", ">[YouTube](https://www.youtube.com/) is an online video sharing and social media platform created by Google.\n", "\n", "This notebook covers how to load documents from `YouTube transcripts`."]}, {"cell_type": "code", "execution_count": null, "id": "427d5745", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import YoutubeLoader"]}, {"cell_type": "code", "execution_count": null, "id": "34a25b57", "metadata": {"scrolled": true}, "outputs": [], "source": ["%pip install --upgrade --quiet  youtube-transcript-api"]}, {"cell_type": "code", "execution_count": null, "id": "bc8b308a", "metadata": {}, "outputs": [], "source": ["loader = YoutubeLoader.from_youtube_url(\n", "    \"https://www.youtube.com/watch?v=QsYGlZkevEg\", add_video_info=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d073dd36", "metadata": {}, "outputs": [], "source": ["loader.load()"]}, {"attachments": {}, "cell_type": "markdown", "id": "6b278a1b", "metadata": {}, "source": ["### Add video info"]}, {"cell_type": "code", "execution_count": null, "id": "ba28af69", "metadata": {}, "outputs": [], "source": ["%pip install --upgrade --quiet  pytube"]}, {"cell_type": "code", "execution_count": null, "id": "9b8ea390", "metadata": {}, "outputs": [], "source": ["loader = YoutubeLoader.from_youtube_url(\n", "    \"https://www.youtube.com/watch?v=QsYGlZkevEg\", add_video_info=True\n", ")\n", "loader.load()"]}, {"attachments": {}, "cell_type": "markdown", "id": "fc417e31", "metadata": {}, "source": ["### Add language preferences\n", "\n", "Language param : It's a list of language codes in a descending priority, `en` by default.\n", "\n", "translation param : It's a translate preference, you can translate available transcript to your preferred language."]}, {"cell_type": "code", "execution_count": null, "id": "08510625", "metadata": {}, "outputs": [], "source": ["loader = YoutubeLoader.from_youtube_url(\n", "    \"https://www.youtube.com/watch?v=QsYGlZkevEg\",\n", "    add_video_info=True,\n", "    language=[\"en\", \"id\"],\n", "    translation=\"en\",\n", ")\n", "loader.load()"]}, {"cell_type": "markdown", "id": "69f4e399a9764d73", "metadata": {}, "source": ["### Get transcripts as timestamped chunks\n", "\n", "Get one or more `Document` objects, each containing a chunk of the video transcript.  The length of the chunks, in seconds, may be specified.  Each chunk's metadata includes a URL of the video on YouTube, which will start the video at the beginning of the specific chunk.\n", "\n", "`transcript_format` param:  One of the `langchain_community.document_loaders.youtube.TranscriptFormat` values.  In this case, `TranscriptFormat.CHUNKS`.\n", "\n", "`chunk_size_seconds` param:  An integer number of video seconds to be represented by each chunk of transcript data.  Default is 120 seconds."]}, {"cell_type": "code", "execution_count": null, "id": "540bbf19182f38bc", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders.youtube import TranscriptFormat\n", "\n", "loader = YoutubeLoader.from_youtube_url(\n", "    \"https://www.youtube.com/watch?v=TKCMw0utiak\",\n", "    add_video_info=True,\n", "    transcript_format=TranscriptFormat.CHUNKS,\n", "    chunk_size_seconds=30,\n", ")\n", "print(\"\\n\\n\".join(map(repr, loader.load())))"]}, {"attachments": {}, "cell_type": "markdown", "id": "65796cc5", "metadata": {}, "source": ["## YouTube loader from Google Cloud\n", "\n", "### Prerequisites\n", "\n", "1. Create a Google Cloud project or use an existing project\n", "1. Enable the [Youtube Api](https://console.cloud.google.com/apis/enableflow?apiid=youtube.googleapis.com&project=sixth-grammar-344520)\n", "1. [Authorize credentials for desktop app](https://developers.google.com/drive/api/quickstart/python#authorize_credentials_for_a_desktop_application)\n", "1. `pip install --upgrade google-api-python-client google-auth-httplib2 google-auth-oauthlib youtube-transcript-api`\n", "\n", "### 🧑 Instructions for ingesting your Google Docs data\n", "By default, the `GoogleDriveLoader` expects the `credentials.json` file to be `~/.credentials/credentials.json`, but this is configurable using the `credentials_file` keyword argument. Same thing with `token.json`. Note that `token.json` will be created automatically the first time you use the loader.\n", "\n", "`GoogleApiYoutubeLoader` can load from a list of Google Docs document ids or a folder id. You can obtain your folder and document id from the URL:\n", "Note depending on your set up, the `service_account_path` needs to be set up. See [here](https://developers.google.com/drive/api/v3/quickstart/python) for more details."]}, {"cell_type": "code", "execution_count": null, "id": "c345bc43", "metadata": {}, "outputs": [], "source": ["# Init the GoogleApiClient\n", "from pathlib import Path\n", "\n", "from langchain_community.document_loaders import GoogleApiClient, GoogleApiYoutubeLoader\n", "\n", "google_api_client = GoogleApiClient(credentials_path=Path(\"your_path_creds.json\"))\n", "\n", "\n", "# Use a Channel\n", "youtube_loader_channel = GoogleApiYoutubeLoader(\n", "    google_api_client=google_api_client,\n", "    channel_name=\"Reducible\",\n", "    captions_language=\"en\",\n", ")\n", "\n", "# Use Youtube Ids\n", "\n", "youtube_loader_ids = GoogleApiYoutubeLoader(\n", "    google_api_client=google_api_client, video_ids=[\"TrdevFK_am4\"], add_video_info=True\n", ")\n", "\n", "# returns a list of Documents\n", "youtube_loader_channel.load()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "vscode": {"interpreter": {"hash": "604c1013f65d31a2eb1fca07aae054bedd5a5a0d272dbb31e502c81f0b254b99"}}}, "nbformat": 4, "nbformat_minor": 5}