{"cells": [{"cell_type": "markdown", "id": "db23d51760310705", "metadata": {}, "source": ["# Writer PDF Parser\n", "\n", "This notebook provides a quick overview for getting started with the Writer `PDFParser` [document loader](/docs/concepts/document_loaders/).\n", "\n", "Writer's [PDF Parser](https://dev.writer.com/api-guides/api-reference/tool-api/pdf-parser#parse-pdf) converts PDF documents into other formats like text or Markdown. This is particularly useful when you need to extract and process text content from PDF files for further analysis or integration into your workflow. In `langchain-writer`, we provide usage of Writer's PDF Parser as a LangChain document parser.\n", "\n", "## Overview\n", "\n", "### Integration details\n", "| Class                                                                                                                              | Package          | Local | Serializable | JS support |                                        Package downloads                                         |                                        Package latest                                         |\n", "|:-----------------------------------------------------------------------------------------------------------------------------------|:-----------------| :---: | :---: |:----------:|:------------------------------------------------------------------------------------------------:|:---------------------------------------------------------------------------------------------:|\n", "| [PDFParser](https://github.com/writer/langchain-writer/blob/main/langchain_writer/pdf_parser.py#L55) | [langchain-writer](https://pypi.org/project/langchain-writer/) |      ❌       |                                       ❌                                       | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-writer?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-writer?style=flat-square&label=%20) |"]}, {"cell_type": "markdown", "id": "c5f08d23df5dc127", "metadata": {}, "source": ["## Setup\n", "\n", "The `PDFParser` is available in the `langchain-writer` package:"]}, {"cell_type": "code", "id": "a8d653f15b7ee32d", "metadata": {}, "source": ["%pip install --quiet -<PERSON> langchain-writer"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "3b9709c26797edf", "metadata": {}, "source": ["### Credentials\n", "\n", "Sign up for [Writer AI Studio](https://app.writer.com/aistudio/signup?utm_campaign=devrel) to generate an API key (you can follow this [Quickstart](https://dev.writer.com/api-guides/quickstart)). Then, set the WRITER_API_KEY environment variable:"]}, {"cell_type": "code", "id": "2983e19c9d555e58", "metadata": {}, "source": ["import getpass\n", "import os\n", "\n", "if not os.getenv(\"WRITER_API_KEY\"):\n", "    os.environ[\"WRITER_API_KEY\"] = getpass.getpass(\"Enter your Writer API key: \")"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "92a22c77f03d43dc", "metadata": {}, "source": ["It's also helpful (but not needed) to set up [LangSmith](https://smith.langchain.com/) for best-in-class observability. If you wish to do so, you can set the `LANGSMITH_TRACING` and `LANGSMITH_API_KEY` environment variables:"]}, {"cell_type": "code", "id": "98d8422ecee77403", "metadata": {}, "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass()"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "67ab78950a3da8ba", "metadata": {}, "source": ["### Instantiation\n", "\n", "Next, instantiate an instance of the Writer PDF Parser with the desired output format:"]}, {"cell_type": "code", "id": "787b3ba8af32533f", "metadata": {}, "source": ["from langchain_writer.pdf_parser import PDFParser\n", "\n", "parser = PDFParser(format=\"markdown\")"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "d91c6f752fd31cee", "metadata": {}, "source": ["## Usage\n", "\n", "There are two ways to use the PDF Parser, either synchronously or asynchronously. In either case, the PDF Parser will return a list of `Document` objects, each containing the parsed content of a page from the PDF file.\n", "\n", "### Synchronous usage\n", "\n", "To invoke the PDF Parser synchronously, pass a `Blob` object to the `parse` method referencing the PDF file you want to parse:"]}, {"cell_type": "code", "id": "d1a24b81a8a96f09", "metadata": {}, "source": ["from langchain_core.documents.base import Blob\n", "\n", "file = Blob.from_path(\"../example_data/layout-parser-paper.pdf\")\n", "\n", "parsed_pages = parser.parse(blob=file)\n", "parsed_pages"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "f89c048c7d23807a", "metadata": {}, "source": ["### Asynchronous usage\n", "\n", "To invoke the PDF Parser asynchronously, pass a `Blob` object to the `aparse` method referencing the PDF file you want to parse:"]}, {"cell_type": "code", "id": "e2f7fd52b7188c6c", "metadata": {}, "source": ["parsed_pages_async = await parser.aparse(blob=file)\n", "parsed_pages_async"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "ab25a3bed8437a05", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `PDFParser` features and configurations, head to the [API reference](https://python.langchain.com/api_reference/writer/pdf_parser/langchain_writer.pdf_parser.PDFParser.html#langchain_writer.pdf_parser.PDFParser).\n", "\n", "## Additional resources\n", "You can find information about Writer's models (including costs, context windows, and supported input types) and tools in the [Writer docs](https://dev.writer.com/home).\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}