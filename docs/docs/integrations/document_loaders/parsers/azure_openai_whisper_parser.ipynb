{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Azure OpenAI Whisper Parser"]}, {"cell_type": "markdown", "metadata": {}, "source": [">[Azure OpenAI Whisper Parser](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/whisper-overview) is a wrapper around the Azure OpenAI Whisper API which utilizes machine learning to transcribe audio files to english text. \n", ">\n", ">The Parser supports `.mp3`, `.mp4`, `.mpeg`, `.mpga`, `.m4a`, `.wav`, and `.webm`.\n", "\n", "The current implementation follows LangChain core principles and can be used with other loaders to handle both audio downloading and parsing. As a result of this the parser will `yield` an `Iterator[Document]`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prerequisites"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The service requires Azure credentials, Azure endpoint and Whisper Model deployment, which can be set up by following the guide [here](https://learn.microsoft.com/en-us/azure/ai-services/openai/whisper-quickstart?tabs=command-line%2Cpython-new%2Cjavascript&pivots=programming-language-python). Furthermore, the required dependencies must be installed.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -Uq  langchain langchain-community openai"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `AzureOpenAIWhisperParser`'s method, `.lazy_parse`, accepts a `Blob` object as a parameter containing the file path of the file to be transcribed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.documents.base import Blob\n", "\n", "audio_path = \"path/to/your/audio/file\"\n", "audio_blob = Blob(path=audio_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders.parsers.audio import AzureOpenAIWhisperParser\n", "\n", "endpoint = \"<your_endpoint>\"\n", "key = \"<your_api_key\"\n", "version = \"<your_api_version>\"\n", "name = \"<your_deployment_name>\"\n", "\n", "parser = AzureOpenAIWhisperParser(\n", "    api_key=key, azure_endpoint=endpoint, api_version=version, deployment_name=name\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents = parser.lazy_parse(blob=audio_blob)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for doc in documents:\n", "    print(doc.page_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `AzureOpenAIWhisperParser` can also be used in conjunction with audio loaders, like the `YoutubeAudioLoader` with a `GenericLoader`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders.blob_loaders.youtube_audio import (\n", "    YoutubeAudioL<PERSON>der,\n", ")\n", "from langchain_community.document_loaders.generic import GenericLoader"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Must be a list\n", "url = [\"www.youtube.url.com\"]\n", "\n", "save_dir = \"save/directory/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["name = \"<your_deployment_name>\"\n", "\n", "loader = GenericLoader(\n", "    YoutubeAudioLoader(url, save_dir), AzureOpenAIWhisperParser(deployment_name=name)\n", ")\n", "\n", "docs = loader.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for doc in documents:\n", "    print(doc.page_content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}