{"cells": [{"cell_type": "markdown", "id": "bf920da0", "metadata": {}, "source": ["# WebBaseLoader\n", "\n", "This covers how to use `WebBaseLoader` to load all text from `HTML` webpages into a document format that we can use downstream. For more custom logic for loading webpages look at some child class examples such as `IMSDbLoader`, `AZLyricsLoader`, and `CollegeConfidentialLoader`. \n", "\n", "If you don't want to worry about website crawling, bypassing JS-blocking sites, and data cleaning, consider using `FireCrawlLoader` or the faster option `SpiderLoader`.\n", "\n", "## Overview\n", "### Integration details\n", "\n", "- TODO: Fill in table features.\n", "- TODO: Remove JS support link if not relevant, otherwise ensure link is correct.\n", "- TODO: Make sure API reference links are correct.\n", "\n", "| Class | Package | Local | Serializable | JS support|\n", "| :--- | :--- | :---: | :---: |  :---: |\n", "| [WebBaseLoader](https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.web_base.WebBaseLoader.html) | [langchain-community](https://python.langchain.com/api_reference/community/index.html) | ✅ | ❌ | ❌ | \n", "### Loader features\n", "| Source | Document Lazy Loading | Native Async Support\n", "| :---: | :---: | :---: | \n", "| WebBaseLoader | ✅ | ✅ | \n", "\n", "## Setup\n", "\n", "### Credentials\n", "\n", "`WebBaseLoader` does not require any credentials.\n", "\n", "### Installation\n", "\n", "To use the `WebBaseLoader` you first need to install the `langchain-community` python package.\n"]}, {"cell_type": "code", "execution_count": null, "id": "50a6c9b6", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-community beautifulsoup4"]}, {"cell_type": "markdown", "id": "25465b06", "metadata": {}, "source": ["## Initialization\n", "\n", "Now we can instantiate our model object and load documents:"]}, {"cell_type": "code", "execution_count": 2, "id": "00b6de21", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import WebBaseLoader\n", "\n", "loader = WebBaseLoader(\"https://www.example.com/\")"]}, {"cell_type": "markdown", "id": "c162b300-5f4b-4e37-bab3-17f590fc07cc", "metadata": {}, "source": ["To bypass SSL verification errors during fetching, you can set the \"verify\" option:\n", "\n", "`loader.requests_kwargs = {'verify':False}`\n", "\n", "### Initialization with multiple pages\n", "\n", "You can also pass in a list of pages to load from."]}, {"cell_type": "code", "execution_count": 3, "id": "e86c5d40", "metadata": {}, "outputs": [], "source": ["loader_multiple_pages = WebBaseLoader(\n", "    [\"https://www.example.com/\", \"https://google.com\"]\n", ")"]}, {"cell_type": "markdown", "id": "1f61081e", "metadata": {}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 4, "id": "f06bdc4e", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': 'https://www.example.com/', 'title': 'Example Domain', 'language': 'No language found.'}, page_content='\\n\\n\\nExample Domain\\n\\n\\n\\n\\n\\n\\n\\nExample Domain\\nThis domain is for use in illustrative examples in documents. You may use this\\n    domain in literature without prior coordination or asking for permission.\\nMore information...\\n\\n\\n\\n')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["docs = loader.load()\n", "\n", "docs[0]"]}, {"cell_type": "code", "execution_count": 5, "id": "a390d79f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'source': 'https://www.example.com/', 'title': 'Example Domain', 'language': 'No language found.'}\n"]}], "source": ["print(docs[0].metadata)"]}, {"cell_type": "markdown", "id": "641be294", "metadata": {}, "source": ["### Load multiple urls concurrently\n", "\n", "You can speed up the scraping process by scraping and parsing multiple urls concurrently.\n", "\n", "There are reasonable limits to concurrent requests, defaulting to 2 per second.  If you aren't concerned about being a good citizen, or you control the server you are scraping and don't care about load, you can change the `requests_per_second` parameter to increase the max concurrent requests.  Note, while this will speed up the scraping process, but may cause the server to block you.  Be careful!"]}, {"cell_type": "code", "execution_count": 6, "id": "9f9cf30f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install -qU  nest_asyncio\n", "\n", "# fixes a bug with async<PERSON> and jupyter\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 8, "id": "49586eac", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Fetching pages: 100%|###########################################################################| 2/2 [00:00<00:00,  8.28it/s]\n"]}, {"data": {"text/plain": ["[Document(metadata={'source': 'https://www.example.com/', 'title': 'Example Domain', 'language': 'No language found.'}, page_content='\\n\\n\\nExample Domain\\n\\n\\n\\n\\n\\n\\n\\nExample Domain\\nThis domain is for use in illustrative examples in documents. You may use this\\n    domain in literature without prior coordination or asking for permission.\\nMore information...\\n\\n\\n\\n'),\n", " Document(metadata={'source': 'https://google.com', 'title': 'Google', 'description': \"Search the world's information, including webpages, images, videos and more. Google has many special features to help you find exactly what you're looking for.\", 'language': 'en'}, page_content='GoogleSearch Images Maps Play YouTube News Gmail Drive More »Web History | Settings | Sign in\\xa0Advanced search5 ways Gemini can help during the HolidaysAdvertisingBusiness SolutionsAbout Google© 2024 - Privacy - Terms  ')]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["loader = WebBaseLoader([\"https://www.example.com/\", \"https://google.com\"])\n", "loader.requests_per_second = 1\n", "docs = loader.aload()\n", "docs"]}, {"cell_type": "markdown", "id": "e337b130", "metadata": {}, "source": ["### Loading a xml file, or using a different BeautifulSoup parser\n", "\n", "You can also look at `SitemapLoader` for an example of how to load a sitemap file, which is an example of using this feature."]}, {"cell_type": "code", "execution_count": 9, "id": "16530c50", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'https://www.govinfo.gov/content/pkg/CFR-2018-title10-vol3/xml/CFR-2018-title10-vol3-sec431-86.xml'}, page_content='\\n\\n10\\nEnergy\\n3\\n2018-01-01\\n2018-01-01\\nfalse\\nUniform test method for the measurement of energy efficiency of commercial packaged boilers.\\nÂ§ 431.86\\nSection Â§ 431.86\\n\\nEnergy\\nDEPARTMENT OF ENERGY\\nENERGY CONSERVATION\\nENERGY EFFICIENCY PROGRAM FOR CERTAIN COMMERCIAL AND INDUSTRIAL EQUIPMENT\\nCommercial Packaged Boilers\\nTest Procedures\\n\\n\\n\\n\\n§\\u2009431.86\\nUniform test method for the measurement of energy efficiency of commercial packaged boilers.\\n(a) Scope. This section provides test procedures, pursuant to the Energy Policy and Conservation Act (EPCA), as amended, which must be followed for measuring the combustion efficiency and/or thermal efficiency of a gas- or oil-fired commercial packaged boiler.\\n(b) Testing and Calculations. Determine the thermal efficiency or combustion efficiency of commercial packaged boilers by conducting the appropriate test procedure(s) indicated in Table 1 of this section.\\n\\nTable 1—Test Requirements for Commercial Packaged Boiler Equipment Classes\\n\\nEquipment category\\nSubcategory\\nCertified rated inputBtu/h\\n\\nStandards efficiency metric(§\\u2009431.87)\\n\\nTest procedure(corresponding to\\nstandards efficiency\\nmetric required\\nby §\\u2009431.87)\\n\\n\\n\\nHot Water\\nGas-fired\\n≥300,000 and ≤2,500,000\\nThermal Efficiency\\nAppendix A, Section 2.\\n\\n\\nHot Water\\nGas-fired\\n>2,500,000\\nCombustion Efficiency\\nAppendix A, Section 3.\\n\\n\\nHot Water\\nOil-fired\\n≥300,000 and ≤2,500,000\\nThermal Efficiency\\nAppendix A, Section 2.\\n\\n\\nHot Water\\nOil-fired\\n>2,500,000\\nCombustion Efficiency\\nAppendix A, Section 3.\\n\\n\\nSteam\\nGas-fired (all*)\\n≥300,000 and ≤2,500,000\\nThermal Efficiency\\nAppendix A, Section 2.\\n\\n\\nSteam\\nGas-fired (all*)\\n>2,500,000 and ≤5,000,000\\nThermal Efficiency\\nAppendix A, Section 2.\\n\\n\\n\\u2003\\n\\n>5,000,000\\nThermal Efficiency\\nAppendix A, Section 2.OR\\nAppendix A, Section 3 with Section *******.\\n\\n\\n\\nSteam\\nOil-fired\\n≥300,000 and ≤2,500,000\\nThermal Efficiency\\nAppendix A, Section 2.\\n\\n\\nSteam\\nOil-fired\\n>2,500,000 and ≤5,000,000\\nThermal Efficiency\\nAppendix A, Section 2.\\n\\n\\n\\u2003\\n\\n>5,000,000\\nThermal Efficiency\\nAppendix A, Section 2.OR\\nAppendix A, Section 3. with Section *******.\\n\\n\\n\\n*\\u2009Equipment classes for commercial packaged boilers as of July 22, 2009 (74 FR 36355) distinguish between gas-fired natural draft and all other gas-fired (except natural draft).\\n\\n(c) Field Tests. The field test provisions of appendix A may be used only to test a unit of commercial packaged boiler with rated input greater than 5,000,000 Btu/h.\\n[81 FR 89305, Dec. 9, 2016]\\n\\n\\nEnergy Efficiency Standards\\n\\n')]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["loader = WebBaseLoader(\n", "    \"https://www.govinfo.gov/content/pkg/CFR-2018-title10-vol3/xml/CFR-2018-title10-vol3-sec431-86.xml\"\n", ")\n", "loader.default_parser = \"xml\"\n", "docs = loader.load()\n", "docs"]}, {"cell_type": "markdown", "id": "5fa40bb1", "metadata": {}, "source": ["## <PERSON><PERSON> Load\n", "\n", "You can use lazy loading to only load one page at a time in order to minimize memory requirements."]}, {"cell_type": "code", "execution_count": 10, "id": "303d2f4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "10\n", "Energy\n", "3\n", "2018-01-01\n", "2018-01-01\n", "false\n", "Uniform test method for the measurement of energy efficien\n", "{'source': 'https://www.govinfo.gov/content/pkg/CFR-2018-title10-vol3/xml/CFR-2018-title10-vol3-sec431-86.xml'}\n"]}], "source": ["pages = []\n", "for doc in loader.lazy_load():\n", "    pages.append(doc)\n", "\n", "print(pages[0].page_content[:100])\n", "print(pages[0].metadata)"]}, {"cell_type": "markdown", "id": "20e4cf2a-2e5d-4a1f-8c99-526d36e4b873", "metadata": {}, "source": ["### Async"]}, {"cell_type": "code", "execution_count": 12, "id": "0ab4286f-7b3c-4e4b-890d-7b39082b61b7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Fetching pages: 100%|###########################################################################| 1/1 [00:00<00:00, 10.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "10\n", "Energy\n", "3\n", "2018-01-01\n", "2018-01-01\n", "false\n", "Uniform test method for the measurement of energy efficien\n", "{'source': 'https://www.govinfo.gov/content/pkg/CFR-2018-title10-vol3/xml/CFR-2018-title10-vol3-sec431-86.xml'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["pages = []\n", "async for doc in loader.alazy_load():\n", "    pages.append(doc)\n", "\n", "print(pages[0].page_content[:100])\n", "print(pages[0].metadata)"]}, {"cell_type": "markdown", "id": "672264ad", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Using proxies\n", "\n", "Sometimes you might need to use proxies to get around IP blocks. You can pass in a dictionary of proxies to the loader (and `requests` underneath) to use them."]}, {"cell_type": "code", "execution_count": null, "id": "9caf0310", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["loader = WebBaseLoader(\n", "    \"https://www.walmart.com/search?q=parrots\",\n", "    proxies={\n", "        \"http\": \"http://{username}:{password}:@proxy.service.com:6666/\",\n", "        \"https\": \"https://{username}:{password}:@proxy.service.com:6666/\",\n", "    },\n", ")\n", "docs = loader.load()"]}, {"cell_type": "markdown", "id": "c8bdc93c", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `WebBaseLoader` features and configurations head to the API reference: https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.web_base.WebBaseLoader.html"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}