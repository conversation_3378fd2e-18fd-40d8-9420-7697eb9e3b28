{"cells": [{"cell_type": "markdown", "id": "66a7777e", "metadata": {}, "source": ["# Weather\n", "\n", ">[OpenWeatherMap](https://openweathermap.org/) is an open-source weather service provider\n", "\n", "This loader fetches the weather data from the OpenWeatherMap's OneCall API, using the pyowm Python package. You must initialize the loader with your OpenWeatherMap API token and the names of the cities you want the weather data for."]}, {"cell_type": "code", "execution_count": null, "id": "9ec8a3b3", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import WeatherDataLoader"]}, {"cell_type": "code", "execution_count": null, "id": "43128d8d", "metadata": {}, "outputs": [], "source": ["%pip install --upgrade --quiet  pyowm"]}, {"cell_type": "code", "execution_count": null, "id": "51b0f0db", "metadata": {}, "outputs": [], "source": ["# Set API key either by passing it in to constructor directly\n", "# or by setting the environment variable \"OPENWEATHERMAP_API_KEY\".\n", "\n", "from getpass import getpass\n", "\n", "OPENWEATHERMAP_API_KEY = getpass()"]}, {"cell_type": "code", "execution_count": null, "id": "35d6809a", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["loader = WeatherDataLoader.from_params(\n", "    [\"chennai\", \"vellore\"], openweathermap_api_key=OPENWEATHERMAP_API_KEY\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "05fe33b9", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["documents = loader.load()\n", "documents"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}