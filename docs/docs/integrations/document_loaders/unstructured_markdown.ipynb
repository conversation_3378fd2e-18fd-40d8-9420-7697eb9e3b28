{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# UnstructuredMarkdownLoader\n", "\n", "This notebook provides a quick overview for getting started with UnstructuredMarkdown [document loader](https://python.langchain.com/docs/concepts/document_loaders). For detailed documentation of all __ModuleName__Loader features and configurations head to the [API reference](https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.markdown.UnstructuredMarkdownLoader.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/document_loaders/file_loaders/unstructured/)|\n", "| :--- | :--- | :---: | :---: |  :---: |\n", "| [UnstructuredMarkdownLoader](https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.markdown.UnstructuredMarkdownLoader.html) | [langchain_community](https://python.langchain.com/api_reference/community/index.html) | ❌ | ❌ | ✅ | \n", "### Loader features\n", "| Source | Document Lazy Loading | Native Async Support\n", "| :---: | :---: | :---: | \n", "| UnstructuredMarkdownLoader | ✅ | ❌ | \n", "\n", "## Setup\n", "\n", "To access UnstructuredMarkdownLoader document loader you'll need to install the `langchain-community` integration package and the `unstructured` python package.\n", "\n", "### Credentials\n", "\n", "No credentials are needed to use this loader."]}, {"cell_type": "markdown", "metadata": {}, "source": "To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:"}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")\n", "# os.environ[\"LANGSMITH_TRACING\"] = \"true\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation\n", "\n", "Install **langchain_community** and **unstructured**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain_community unstructured"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialization\n", "\n", "Now we can instantiate our model object and load documents. \n", "\n", "You can run the loader in one of two modes: \"single\" and \"elements\". If you use \"single\" mode, the document will be returned as a single `Document` object. If you use \"elements\" mode, the unstructured library will split the document into elements such as `Title` and `NarrativeText`. You can pass in additional `unstructured` kwargs after mode to apply different `unstructured` settings."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import UnstructuredMarkdownLoader\n", "\n", "loader = UnstructuredMarkdownLoader(\n", "    \"./example_data/example.md\",\n", "    mode=\"single\",\n", "    strategy=\"fast\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': './example_data/example.md'}, page_content='Sample Markdown Document\\n\\nIntroduction\\n\\nWelcome to this sample Markdown document. Markdown is a lightweight markup language used for formatting text. It\\'s widely used for documentation, readme files, and more.\\n\\nFeatures\\n\\nHeaders\\n\\nMarkdown supports multiple levels of headers:\\n\\nHeader 1: # Header 1\\n\\nHeader 2: ## Header 2\\n\\nHeader 3: ### Header 3\\n\\nLists\\n\\nUnordered List\\n\\nItem 1\\n\\nItem 2\\n\\nSubitem 2.1\\n\\nSubitem 2.2\\n\\nOrdered List\\n\\nFirst item\\n\\nSecond item\\n\\nThird item\\n\\nLinks\\n\\nOpenAI is an AI research organization.\\n\\nImages\\n\\nHere\\'s an example image:\\n\\nCode\\n\\nInline Code\\n\\nUse code for inline code snippets.\\n\\nCode Block\\n\\n```python def greet(name): return f\"Hello, {name}!\"\\n\\nprint(greet(\"World\")) ```')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["docs = loader.load()\n", "docs[0]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'source': './example_data/example.md'}\n"]}], "source": ["print(docs[0].metadata)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> Load"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': './example_data/example.md', 'link_texts': ['OpenAI'], 'link_urls': ['https://www.openai.com'], 'last_modified': '2024-08-14T15:04:18', 'languages': ['eng'], 'parent_id': 'de1f74bf226224377ab4d8b54f215bb9', 'filetype': 'text/markdown', 'file_directory': './example_data', 'filename': 'example.md', 'category': 'NarrativeText', 'element_id': '898a542a261f7dc65e0072d1e847d535'}, page_content='OpenAI is an AI research organization.')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["page = []\n", "for doc in loader.lazy_load():\n", "    page.append(doc)\n", "    if len(page) >= 10:\n", "        # do some paged operation, e.g.\n", "        # index.upsert(page)\n", "\n", "        page = []\n", "page[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Elements\n", "\n", "In this example we will load in the `elements` mode, which will return a list of the different elements in the markdown document:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["29"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.document_loaders import UnstructuredMarkdownLoader\n", "\n", "loader = UnstructuredMarkdownLoader(\n", "    \"./example_data/example.md\",\n", "    mode=\"elements\",\n", "    strategy=\"fast\",\n", ")\n", "\n", "docs = loader.load()\n", "len(docs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As you see there are 29 elements that were pulled from the `example.md` file. The first element is the title of the document as expected:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Sample Markdown Document'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0].page_content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all UnstructuredMarkdownLoader features and configurations head to the API reference: https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.markdown.UnstructuredMarkdownLoader.html"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}