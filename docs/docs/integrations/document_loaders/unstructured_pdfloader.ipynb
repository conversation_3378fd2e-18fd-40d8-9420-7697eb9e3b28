{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# UnstructuredPDFLoader\n", "\n", "[Unstructured](https://unstructured-io.github.io/unstructured/) supports a common interface for working with unstructured or semi-structured file formats, such as Markdown or PDF. LangChain's [UnstructuredPDFLoader](https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.pdf.UnstructuredPDFLoader.html) integrates with Unstructured to parse PDF documents into LangChain [Document](https://python.langchain.com/api_reference/core/documents/langchain_core.documents.base.Document.html) objects.\n", "\n", "Please see [this page](/docs/integrations/providers/unstructured/) for more information on installing system requirements.\n", "\n", "\n", "### Integration details\n", "\n", "\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/document_loaders/file_loaders/unstructured/)|\n", "| :--- | :--- | :---: | :---: |  :---: |\n", "| [UnstructuredPDFLoader](https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.pdf.UnstructuredPDFLoader.html) | [langchain_community](https://python.langchain.com/api_reference/community/index.html) | ✅ | ❌ | ✅ | \n", "### Loader features\n", "| Source | Document Lazy Loading | Native Async Support\n", "| :---: | :---: | :---: | \n", "| UnstructuredPDFLoader | ✅ | ❌ | \n", "\n", "## Setup\n", "\n", "### Credentials\n", "\n", "No credentials are needed to use this loader."]}, {"cell_type": "markdown", "metadata": {}, "source": ["To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")\n", "# os.environ[\"LANGSMITH_TRACING\"] = \"true\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation\n", "\n", "Install **langchain_community** and **unstructured**."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-community unstructured"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialization\n", "\n", "Now we can initialize our loader:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import UnstructuredPDFLoader\n", "\n", "file_path = \"./example_data/layout-parser-paper.pdf\"\n", "loader = UnstructuredPDFLoader(file_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': './example_data/layout-parser-paper.pdf'}, page_content='1 2 0 2\\n\\nn u J\\n\\n1 2\\n\\n]\\n\\nV C . s c [\\n\\n2 v 8 4 3 5 1 . 3 0 1 2 : v i X r a\\n\\nLayoutParser: A Uniﬁed Toolkit for Deep Learning Based Document Image Analysis\\n\\nZejiang Shen1 ((cid:0)), <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> Li5\\n\\n1 Allen Institute <NAME_EMAIL> 2 Brown <NAME_EMAIL> 3 Harvard University {melis<PERSON><PERSON>,j<PERSON><PERSON> carlson}@fas.harvard.edu 4 University <NAME_EMAIL> 5 University <NAME_EMAIL>\\n\\nAbstract. Recent advances in document image analysis (DIA) have been primarily driven by the application of neural networks. Ideally, research outcomes could be easily deployed in production and extended for further investigation. However, various factors like loosely organized codebases and sophisticated model conﬁgurations complicate the easy reuse of im- portant innovations by a wide audience. Though there have been on-going eﬀorts to improve reusability and simplify deep learning (DL) model development in disciplines like natural language processing and computer vision, none of them are optimized for challenges in the domain of DIA. This represents a major gap in the existing toolkit, as DIA is central to academic research across a wide range of disciplines in the social sciences and humanities. This paper introduces LayoutParser, an open-source library for streamlining the usage of DL in DIA research and applica- tions. The core LayoutParser library comes with a set of simple and intuitive interfaces for applying and customizing DL models for layout de- tection, character recognition, and many other document processing tasks. To promote extensibility, LayoutParser also incorporates a community platform for sharing both pre-trained models and full document digiti- zation pipelines. We demonstrate that LayoutParser is helpful for both lightweight and large-scale digitization pipelines in real-word use cases. The library is publicly available at https://layout-parser.github.io.\\n\\nKeywords: Document Image Analysis · Deep Learning · Layout Analysis · Character Recognition · Open Source library · Toolkit.\\n\\n1\\n\\nIntroduction\\n\\nDeep Learning(DL)-based approaches are the state-of-the-art for a wide range of document image analysis (DIA) tasks including document image classiﬁcation [11,\\n\\n2\\n\\nZ. Shen et al.\\n\\n37], layout detection [38, 22], table detection [26], and scene text detection [4]. A generalized learning-based framework dramatically reduces the need for the manual speciﬁcation of complicated rules, which is the status quo with traditional methods. DL has the potential to transform DIA pipelines and beneﬁt a broad spectrum of large-scale document digitization projects.\\n\\nHowever, there are several practical diﬃculties for taking advantages of re- cent advances in DL-based methods: 1) DL models are notoriously convoluted for reuse and extension. Existing models are developed using distinct frame- works like TensorFlow [1] or PyTorch [24], and the high-level parameters can be obfuscated by implementation details [8]. It can be a time-consuming and frustrating experience to debug, reproduce, and adapt existing models for DIA, and many researchers who would beneﬁt the most from using these methods lack the technical background to implement them from scratch. 2) Document images contain diverse and disparate patterns across domains, and customized training is often required to achieve a desirable detection accuracy. Currently there is no full-ﬂedged infrastructure for easily curating the target document image datasets and ﬁne-tuning or re-training the models. 3) DIA usually requires a sequence of models and other processing to obtain the ﬁnal outputs. Often research teams use DL models and then perform further document analyses in separate processes, and these pipelines are not documented in any central location (and often not documented at all). This makes it diﬃcult for research teams to learn about how full pipelines are implemented and leads them to invest signiﬁcant resources in reinventing the DIA wheel.\\n\\nLayoutParser provides a uniﬁed toolkit to support DL-based document image analysis and processing. To address the aforementioned challenges, LayoutParser is built with the following components:\\n\\n1. An oﬀ-the-shelf toolkit for applying DL models for layout detection, character recognition, and other DIA tasks (Section 3)\\n\\n2. A rich repository of pre-trained neural network models (Model Zoo) that underlies the oﬀ-the-shelf usage\\n\\n3. Comprehensive tools for eﬃcient document image data annotation and model tuning to support diﬀerent levels of customization\\n\\n4. A DL model hub and community platform for the easy sharing, distribu- tion, and discussion of DIA models and pipelines, to promote reusability, reproducibility, and extensibility (Section 4)\\n\\nThe library implements simple and intuitive Python APIs without sacriﬁcing generalizability and versatility, and can be easily installed via pip. Its convenient functions for handling document image data can be seamlessly integrated with existing DIA pipelines. With detailed documentations and carefully curated tutorials, we hope this tool will beneﬁt a variety of end-users, and will lead to advances in applications in both industry and academic research.\\n\\nLayoutParser is well aligned with recent eﬀorts for improving DL model reusability in other disciplines like natural language processing [8, 34] and com- puter vision [35], but with a focus on unique challenges in DIA. We show LayoutParser can be applied in sophisticated and large-scale digitization projects\\n\\nLayoutParser: A Uniﬁed Toolkit for DL-Based DIA\\n\\nthat require precision, eﬃciency, and robustness, as well as simple and light- weight document processing tasks focusing on eﬃcacy and ﬂexibility (Section 5). LayoutParser is being actively maintained, and support for more deep learning models and novel methods in text-based layout analysis methods [37, 34] is planned.\\n\\nThe rest of the paper is organized as follows. Section 2 provides an overview of related work. The core LayoutParser library, DL Model Zoo, and customized model training are described in Section 3, and the DL model hub and commu- nity platform are detailed in Section 4. Section 5 shows two examples of how LayoutParser can be used in practical DIA projects, and Section 6 concludes.\\n\\n2 Related Work\\n\\nRecently, various DL models and datasets have been developed for layout analysis tasks. The dhSegment [22] utilizes fully convolutional networks [20] for segmen- tation tasks on historical documents. Object detection-based methods like Faster R-CNN [28] and Mask R-CNN [12] are used for identifying document elements [38] and detecting tables [30, 26]. Most recently, Graph Neural Networks [29] have also been used in table detection [27]. However, these models are usually implemented individually and there is no uniﬁed framework to load and use such models.\\n\\nThere has been a surge of interest in creating open-source tools for document image processing: a search of document image analysis in Github leads to 5M relevant code pieces 6; yet most of them rely on traditional rule-based methods or provide limited functionalities. The closest prior research to our work is the OCR-D project7, which also tries to build a complete toolkit for DIA. However, similar to the platform developed by Neudecker et al. [21], it is designed for analyzing historical documents, and provides no supports for recent DL models. The DocumentLayoutAnalysis project8 focuses on processing born-digital PDF documents via analyzing the stored PDF data. Repositories like DeepLayout9 and Detectron2-PubLayNet10 are individual deep learning models trained on layout analysis datasets without support for the full DIA pipeline. The Document Analysis and Exploitation (DAE) platform [15] and the DeepDIVA project [2] aim to improve the reproducibility of DIA methods (or DL models), yet they are not actively maintained. OCR engines like Tesseract [14], easyOCR11 and paddleOCR12 usually do not come with comprehensive functionalities for other DIA tasks like layout analysis.\\n\\nRecent years have also seen numerous eﬀorts to create libraries for promoting reproducibility and reusability in the ﬁeld of DL. Libraries like Dectectron2 [35],\\n\\n6 The number shown is obtained by specifying the search type as ‘code’. 7 https://ocr-d.de/en/about 8 https://github.com/BobLd/DocumentLayoutAnalysis 9 https://github.com/leonlulu/DeepLayout 10 https://github.com/hpanwar08/detectron2 11 https://github.com/JaidedAI/EasyOCR 12 https://github.com/PaddlePaddle/PaddleOCR\\n\\n3\\n\\n4\\n\\nZ. Shen et al.\\n\\nDIA Model Hub\\n\\nStorage & Visualization\\n\\nLayout Detection Models\\n\\nOCR Module\\n\\nCustomized Model Training\\n\\nModel Customization\\n\\nCommunity Platform\\n\\nThe Core LayoutParser Library\\n\\nLayout Data Structure\\n\\nEfficient Data Annotation\\n\\nDocument Images\\n\\nDIA Pipeline Sharing\\n\\nFig. 1: The overall architecture of LayoutParser. For an input document image, the core LayoutParser library provides a set of oﬀ-the-shelf tools for layout detection, OCR, visualization, and storage, backed by a carefully designed layout data structure. LayoutParser also supports high level customization via eﬃcient layout annotation and model training functions. These improve model accuracy on the target samples. The community platform enables the easy sharing of DIA models and whole digitization pipelines to promote reusability and reproducibility. A collection of detailed documentation, tutorials and exemplar projects make LayoutParser easy to learn and use.\\n\\nAllenNLP [8] and transformers [34] have provided the community with complete DL-based support for developing and deploying models for general computer vision and natural language processing problems. LayoutParser, on the other hand, specializes speciﬁcally in DIA tasks. LayoutParser is also equipped with a community platform inspired by established model hubs such as Torch Hub [23] and TensorFlow Hub [1]. It enables the sharing of pretrained models as well as full document processing pipelines that are unique to DIA tasks.\\n\\nThere have been a variety of document data collections to facilitate the development of DL models. Some examples include PRImA [3](magazine layouts), PubLayNet [38](academic paper layouts), Table Bank [18](tables in academic papers), Newspaper Navigator Dataset [16, 17](newspaper ﬁgure layouts) and HJDataset [31](historical Japanese document layouts). A spectrum of models trained on these datasets are currently available in the LayoutParser model zoo to support diﬀerent use cases.\\n\\n3 The Core LayoutParser Library\\n\\nAt the core of LayoutParser is an oﬀ-the-shelf toolkit that streamlines DL- based document image analysis. Five components support a simple interface with comprehensive functionalities: 1) The layout detection models enable using pre-trained or self-trained DL models for layout detection with just four lines of code. 2) The detected layout information is stored in carefully engineered\\n\\nLayoutParser: A Uniﬁed Toolkit for DL-Based DIA\\n\\nTable 1: Current layout detection models in the LayoutParser model zoo\\n\\nDataset\\n\\nBase Model1 Large Model Notes\\n\\nPubLayNet [38] PRImA [3] Newspaper [17] TableBank [18] HJDataset [31]\\n\\nF / M M F F F / M\\n\\nM - - F -\\n\\nLayouts of modern scientiﬁc documents Layouts of scanned modern magazines and scientiﬁc reports Layouts of scanned US newspapers from the 20th century Table region on modern scientiﬁc and business document Layouts of history Japanese documents\\n\\n1 For each dataset, we train several models of diﬀerent sizes for diﬀerent needs (the trade-oﬀ between accuracy vs. computational cost). For “base model” and “large model”, we refer to using the ResNet 50 or ResNet 101 backbones [13], respectively. One can train models of diﬀerent architectures, like Faster R-CNN [28] (F) and Mask R-CNN [12] (M). For example, an F in the Large Model column indicates it has a Faster R-CNN model trained using the ResNet 101 backbone. The platform is maintained and a number of additions will be made to the model zoo in coming months.\\n\\nlayout data structures, which are optimized for eﬃciency and versatility. 3) When necessary, users can employ existing or customized OCR models via the uniﬁed API provided in the OCR module. 4) LayoutParser comes with a set of utility functions for the visualization and storage of the layout data. 5) LayoutParser is also highly customizable, via its integration with functions for layout data annotation and model training. We now provide detailed descriptions for each component.\\n\\n3.1 Layout Detection Models\\n\\nIn LayoutParser, a layout model takes a document image as an input and generates a list of rectangular boxes for the target content regions. Diﬀerent from traditional methods, it relies on deep convolutional neural networks rather than manually curated rules to identify content regions. It is formulated as an object detection problem and state-of-the-art models like Faster R-CNN [28] and Mask R-CNN [12] are used. This yields prediction results of high accuracy and makes it possible to build a concise, generalized interface for layout detection. LayoutParser, built upon Detectron2 [35], provides a minimal API that can perform layout detection with only four lines of code in Python:\\n\\n1 import layoutparser as lp 2 image = cv2 . imread ( \" image_file \" ) # load images 3 model = lp . De t e c tro n2 Lay outM odel (\\n\\n\" lp :// PubLayNet / f as t er _ r c nn _ R _ 50 _ F P N_ 3 x / config \" )\\n\\n4 5 layout = model . detect ( image )\\n\\nLayoutParser provides a wealth of pre-trained model weights using various datasets covering diﬀerent languages, time periods, and document types. Due to domain shift [7], the prediction performance can notably drop when models are ap- plied to target samples that are signiﬁcantly diﬀerent from the training dataset. As document structures and layouts vary greatly in diﬀerent domains, it is important to select models trained on a dataset similar to the test samples. A semantic syntax is used for initializing the model weights in LayoutParser, using both the dataset name and model name lp://<dataset-name>/<model-architecture-name>.\\n\\n5\\n\\n6\\n\\nZ. Shen et al.\\n\\nFig. 2: The relationship between the three types of layout data structures. Coordinate supports three kinds of variation; TextBlock consists of the co- ordinate information and extra features like block text, types, and reading orders; a Layout object is a list of all possible layout elements, including other Layout objects. They all support the same set of transformation and operation APIs for maximum ﬂexibility.\\n\\nShown in Table 1, LayoutParser currently hosts 9 pre-trained models trained on 5 diﬀerent datasets. Description of the training dataset is provided alongside with the trained models such that users can quickly identify the most suitable models for their tasks. Additionally, when such a model is not readily available, LayoutParser also supports training customized layout models and community sharing of the models (detailed in Section 3.5).\\n\\n3.2 Layout Data Structures\\n\\nA critical feature of LayoutParser is the implementation of a series of data structures and operations that can be used to eﬃciently process and manipulate the layout elements. In document image analysis pipelines, various post-processing on the layout analysis model outputs is usually required to obtain the ﬁnal outputs. Traditionally, this requires exporting DL model outputs and then loading the results into other pipelines. All model outputs from LayoutParser will be stored in carefully engineered data types optimized for further processing, which makes it possible to build an end-to-end document digitization pipeline within LayoutParser. There are three key components in the data structure, namely the Coordinate system, the TextBlock, and the Layout. They provide diﬀerent levels of abstraction for the layout data, and a set of APIs are supported for transformations or operations on these classes.\\n\\nLayoutParser: A Uniﬁed Toolkit for DL-Based DIA\\n\\nCoordinates are the cornerstones for storing layout information. Currently, three types of Coordinate data structures are provided in LayoutParser, shown in Figure 2. Interval and Rectangle are the most common data types and support specifying 1D or 2D regions within a document. They are parameterized with 2 and 4 parameters. A Quadrilateral class is also implemented to support a more generalized representation of rectangular regions when the document is skewed or distorted, where the 4 corner points can be speciﬁed and a total of 8 degrees of freedom are supported. A wide collection of transformations like shift, pad, and scale, and operations like intersect, union, and is_in, are supported for these classes. Notably, it is common to separate a segment of the image and analyze it individually. LayoutParser provides full support for this scenario via image cropping operations crop_image and coordinate transformations like relative_to and condition_on that transform coordinates to and from their relative representations. We refer readers to Table 2 for a more detailed description of these operations13.\\n\\nBased on Coordinates, we implement the TextBlock class that stores both the positional and extra features of individual layout elements. It also supports specifying the reading orders via setting the parent ﬁeld to the index of the parent object. A Layout class is built that takes in a list of TextBlocks and supports processing the elements in batch. Layout can also be nested to support hierarchical layout structures. They support the same operations and transformations as the Coordinate classes, minimizing both learning and deployment eﬀort.\\n\\n3.3 OCR\\n\\nLayoutParser provides a uniﬁed interface for existing OCR tools. Though there are many OCR tools available, they are usually conﬁgured diﬀerently with distinct APIs or protocols for using them. It can be ineﬃcient to add new OCR tools into an existing pipeline, and diﬃcult to make direct comparisons among the available tools to ﬁnd the best option for a particular project. To this end, LayoutParser builds a series of wrappers among existing OCR engines, and provides nearly the same syntax for using them. It supports a plug-and-play style of using OCR engines, making it eﬀortless to switch, evaluate, and compare diﬀerent OCR modules:\\n\\n1 ocr_agent = lp . TesseractAgent () 2 # Can be easily switched to other OCR software 3 tokens = ocr_agent . detect ( image )\\n\\nThe OCR outputs will also be stored in the aforementioned layout data structures and can be seamlessly incorporated into the digitization pipeline. Currently LayoutParser supports the Tesseract and Google Cloud Vision OCR engines.\\n\\nLayoutParser also comes with a DL-based CNN-RNN OCR model [6] trained with the Connectionist Temporal Classiﬁcation (CTC) loss [10]. It can be used like the other OCR modules, and can be easily trained on customized datasets.\\n\\n13 This is also available in the LayoutParser documentation pages.\\n\\n7\\n\\n8\\n\\nZ. Shen et al.\\n\\nTable 2: All operations supported by the layout elements. The same APIs are supported across diﬀerent layout element classes including Coordinate types, TextBlock and Layout.\\n\\nOperation Name\\n\\nDescription\\n\\nblock.pad(top, bottom, right, left) Enlarge the current block according to the input\\n\\nblock.scale(fx, fy)\\n\\nScale the current block given the ratio in x and y direction\\n\\nblock.shift(dx, dy)\\n\\nMove the current block with the shift distances in x and y direction\\n\\nblock1.is in(block2)\\n\\nWhether block1 is inside of block2\\n\\nblock1.intersect(block2)\\n\\nReturn the intersection region of block1 and block2. Coordinate type to be determined based on the inputs.\\n\\nblock1.union(block2)\\n\\nReturn the union region of block1 and block2. Coordinate type to be determined based on the inputs.\\n\\nblock1.relative to(block2)\\n\\nConvert the absolute coordinates of block1 to relative coordinates to block2\\n\\nblock1.condition on(block2)\\n\\nCalculate the absolute coordinates of block1 given the canvas block2’s absolute coordinates\\n\\nblock.crop image(image)\\n\\nObtain the image segments in the block region\\n\\n3.4 Storage and visualization\\n\\nThe end goal of DIA is to transform the image-based document data into a structured database. LayoutParser supports exporting layout data into diﬀerent formats like JSON, csv, and will add the support for the METS/ALTO XML format 14 . It can also load datasets from layout analysis-speciﬁc formats like COCO [38] and the Page Format [25] for training layout models (Section 3.5). Visualization of the layout detection results is critical for both presentation and debugging. LayoutParser is built with an integrated API for displaying the layout information along with the original document image. Shown in Figure 3, it enables presenting layout data with rich meta information and features in diﬀerent modes. More detailed information can be found in the online LayoutParser documentation page.\\n\\n3.5 Customized Model Training\\n\\nBesides the oﬀ-the-shelf library, LayoutParser is also highly customizable with supports for highly unique and challenging document analysis tasks. Target document images can be vastly diﬀerent from the existing datasets for train- ing layout models, which leads to low layout detection accuracy. Training data\\n\\n14 https://altoxml.github.io\\n\\nLayoutParser: A Uniﬁed Toolkit for DL-Based DIA\\n\\nFig. 3: Layout detection and OCR results visualization generated by the LayoutParser APIs. Mode I directly overlays the layout region bounding boxes and categories over the original image. Mode II recreates the original document via drawing the OCR’d texts at their corresponding positions on the image canvas. In this ﬁgure, tokens in textual regions are ﬁltered using the API and then displayed.\\n\\ncan also be highly sensitive and not sharable publicly. To overcome these chal- lenges, LayoutParser is built with rich features for eﬃcient data annotation and customized model training.\\n\\nLayoutParser incorporates a toolkit optimized for annotating document lay- outs using object-level active learning [32]. With the help from a layout detection model trained along with labeling, only the most important layout objects within each image, rather than the whole image, are required for labeling. The rest of the regions are automatically annotated with high conﬁdence predictions from the layout detection model. This allows a layout dataset to be created more eﬃciently with only around 60% of the labeling budget.\\n\\nAfter the training dataset is curated, LayoutParser supports diﬀerent modes for training the layout models. Fine-tuning can be used for training models on a small newly-labeled dataset by initializing the model with existing pre-trained weights. Training from scratch can be helpful when the source dataset and target are signiﬁcantly diﬀerent and a large training set is available. However, as suggested in Studer et al.’s work[33], loading pre-trained weights on large-scale datasets like ImageNet [5], even from totally diﬀerent domains, can still boost model performance. Through the integrated API provided by LayoutParser, users can easily compare model performances on the benchmark datasets.\\n\\n9\\n\\n10\\n\\nZ. Shen et al.\\n\\nFig. 4: Illustration of (a) the original historical Japanese document with layout detection results and (b) a recreated version of the document image that achieves much better character recognition recall. The reorganization algorithm rearranges the tokens based on the their detected bounding boxes given a maximum allowed height.\\n\\n4 LayoutParser Community Platform\\n\\nAnother focus of LayoutParser is promoting the reusability of layout detection models and full digitization pipelines. Similar to many existing deep learning libraries, LayoutParser comes with a community model hub for distributing layout models. End-users can upload their self-trained models to the model hub, and these models can be loaded into a similar interface as the currently available LayoutParser pre-trained models. For example, the model trained on the News Navigator dataset [17] has been incorporated in the model hub.\\n\\nBeyond DL models, LayoutParser also promotes the sharing of entire doc- ument digitization pipelines. For example, sometimes the pipeline requires the combination of multiple DL models to achieve better accuracy. Currently, pipelines are mainly described in academic papers and implementations are often not pub- licly available. To this end, the LayoutParser community platform also enables the sharing of layout pipelines to promote the discussion and reuse of techniques. For each shared pipeline, it has a dedicated project page, with links to the source code, documentation, and an outline of the approaches. A discussion panel is provided for exchanging ideas. Combined with the core LayoutParser library, users can easily build reusable components based on the shared pipelines and apply them to solve their unique problems.\\n\\n5 Use Cases\\n\\nThe core objective of LayoutParser is to make it easier to create both large-scale and light-weight document digitization pipelines. Large-scale document processing\\n\\nLayoutParser: A Uniﬁed Toolkit for DL-Based DIA\\n\\nfocuses on precision, eﬃciency, and robustness. The target documents may have complicated structures, and may require training multiple layout detection models to achieve the optimal accuracy. Light-weight pipelines are built for relatively simple documents, with an emphasis on development ease, speed and ﬂexibility. Ideally one only needs to use existing resources, and model training should be avoided. Through two exemplar projects, we show how practitioners in both academia and industry can easily build such pipelines using LayoutParser and extract high-quality structured document data for their downstream tasks. The source code for these projects will be publicly available in the LayoutParser community hub.\\n\\n5.1 A Comprehensive Historical Document Digitization Pipeline\\n\\nThe digitization of historical documents can unlock valuable data that can shed light on many important social, economic, and historical questions. Yet due to scan noises, page wearing, and the prevalence of complicated layout structures, ob- taining a structured representation of historical document scans is often extremely complicated. In this example, LayoutParser was used to develop a comprehensive pipeline, shown in Figure 5, to gener- ate high-quality structured data from historical Japanese ﬁrm ﬁnancial ta- bles with complicated layouts. The pipeline applies two layout models to identify diﬀerent levels of document structures and two customized OCR engines for optimized character recog- nition accuracy.\\n\\nAs shown in Figure 4 (a), the document contains columns of text written vertically 15, a common style in Japanese. Due to scanning noise and archaic printing technology, the columns can be skewed or have vari- able widths, and hence cannot be eas- ily identiﬁed via rule-based methods. Within each column, words are sepa- rated by white spaces of variable size, and the vertical positions of objects can be an indicator of their layout type.\\n\\nFig. 5: Illustration of how LayoutParser helps with the historical document digi- tization pipeline.\\n\\n15 A document page consists of eight rows like this. For simplicity we skip the row\\n\\nsegmentation discussion and refer readers to the source code when available.\\n\\n11\\n\\n12\\n\\nZ. Shen et al.\\n\\nTo decipher the complicated layout\\n\\nstructure, two object detection models have been trained to recognize individual columns and tokens, respectively. A small training set (400 images with approxi- mately 100 annotations each) is curated via the active learning based annotation tool [32] in LayoutParser. The models learn to identify both the categories and regions for each token or column via their distinct visual features. The layout data structure enables easy grouping of the tokens within each column, and rearranging columns to achieve the correct reading orders based on the horizontal position. Errors are identiﬁed and rectiﬁed via checking the consistency of the model predictions. Therefore, though trained on a small dataset, the pipeline achieves a high level of layout detection accuracy: it achieves a 96.97 AP [19] score across 5 categories for the column detection model, and a 89.23 AP across 4 categories for the token detection model.\\n\\nA combination of character recognition methods is developed to tackle the unique challenges in this document. In our experiments, we found that irregular spacing between the tokens led to a low character recognition recall rate, whereas existing OCR models tend to perform better on densely-arranged texts. To overcome this challenge, we create a document reorganization algorithm that rearranges the text based on the token bounding boxes detected in the layout analysis step. Figure 4 (b) illustrates the generated image of dense text, which is sent to the OCR APIs as a whole to reduce the transaction costs. The ﬂexible coordinate system in LayoutParser is used to transform the OCR results relative to their original positions on the page.\\n\\nAdditionally, it is common for historical documents to use unique fonts with diﬀerent glyphs, which signiﬁcantly degrades the accuracy of OCR models trained on modern texts. In this document, a special ﬂat font is used for printing numbers and could not be detected by oﬀ-the-shelf OCR engines. Using the highly ﬂexible functionalities from LayoutParser, a pipeline approach is constructed that achieves a high recognition accuracy with minimal eﬀort. As the characters have unique visual structures and are usually clustered together, we train the layout model to identify number regions with a dedicated category. Subsequently, LayoutParser crops images within these regions, and identiﬁes characters within them using a self-trained OCR model based on a CNN-RNN [6]. The model detects a total of 15 possible categories, and achieves a 0.98 Jaccard score16 and a 0.17 average Levinstein distances17 for token prediction on the test set.\\n\\nOverall, it is possible to create an intricate and highly accurate digitization pipeline for large-scale digitization using LayoutParser. The pipeline avoids specifying the complicated rules used in traditional methods, is straightforward to develop, and is robust to outliers. The DL models also generate ﬁne-grained results that enable creative approaches like page reorganization for OCR.\\n\\n16 This measures the overlap between the detected and ground-truth characters, and\\n\\nthe maximum is 1.\\n\\n17 This measures the number of edits from the ground-truth text to the predicted text,\\n\\nand lower is better.\\n\\nLayoutParser: A Uniﬁed Toolkit for DL-Based DIA\\n\\nFig. 6: This lightweight table detector can identify tables (outlined in red) and cells (shaded in blue) in diﬀerent locations on a page. In very few cases (d), it might generate minor error predictions, e.g, failing to capture the top text line of a table.\\n\\n5.2 A light-weight Visual Table Extractor\\n\\nDetecting tables and parsing their structures (table extraction) are of central im- portance for many document digitization tasks. Many previous works [26, 30, 27] and tools 18 have been developed to identify and parse table structures. Yet they might require training complicated models from scratch, or are only applicable for born-digital PDF documents. In this section, we show how LayoutParser can help build a light-weight accurate visual table extractor for legal docket tables using the existing resources with minimal eﬀort.\\n\\nThe extractor uses a pre-trained layout detection model for identifying the table regions and some simple rules for pairing the rows and the columns in the PDF image. Mask R-CNN [12] trained on the PubLayNet dataset [38] from the LayoutParser Model Zoo can be used for detecting table regions. By ﬁltering out model predictions of low conﬁdence and removing overlapping predictions, LayoutParser can identify the tabular regions on each page, which signiﬁcantly simpliﬁes the subsequent steps. By applying the line detection functions within the tabular segments, provided in the utility module from LayoutParser, the pipeline can identify the three distinct columns in the tables. A row clustering method is then applied via analyzing the y coordinates of token bounding boxes in the left-most column, which are obtained from the OCR engines. A non-maximal suppression algorithm is used to remove duplicated rows with extremely small gaps. Shown in Figure 6, the built pipeline can detect tables at diﬀerent positions on a page accurately. Continued tables from diﬀerent pages are concatenated, and a structured table representation has been easily created.\\n\\n18 https://github.com/atlanhq/camelot, https://github.com/tabulapdf/tabula\\n\\n13\\n\\n14\\n\\nZ. Shen et al.\\n\\n6 Conclusion\\n\\nLayoutParser provides a comprehensive toolkit for deep learning-based document image analysis. The oﬀ-the-shelf library is easy to install, and can be used to build ﬂexible and accurate pipelines for processing documents with complicated structures. It also supports high-level customization and enables easy labeling and training of DL models on unique document image datasets. The LayoutParser community platform facilitates sharing DL models and DIA pipelines, inviting discussion and promoting code reproducibility and reusability. The LayoutParser team is committed to keeping the library updated continuously and bringing the most recent advances in DL-based DIA, such as multi-modal document modeling [37, 36, 9] (an upcoming priority), to a diverse audience of end-users.\\n\\nAcknowledgements We thank the anonymous reviewers for their comments and suggestions. This project is supported in part by NSF Grant OIA-2033558 and funding from the Harvard Data Science Initiative and Harvard Catalyst. Zejiang Shen thanks Doug Downey for suggestions.\\n\\nReferences\\n\\n[1] Abadi, M., Agarwal, A., Barham, P., Brevdo, E., Chen, Z., Citro, C., Corrado, G.S., Davis, A., Dean, J., Devin, M., Ghemawat, S., Goodfellow, I., Harp, A., Irving, G., Isard, M., Jia, Y., Jozefowicz, R., Kaiser, L., Kudlur, M., Levenberg, J., Man´e, D., Monga, R., Moore, S., Murray, D., Olah, C., Schuster, M., Shlens, J., Steiner, B., Sutskever, I., Talwar, K., Tucker, P., Vanhoucke, V., Vasudevan, V., Vi´egas, F., Vinyals, O., Warden, P., Wattenberg, M., Wicke, M., Yu, Y., Zheng, X.: TensorFlow: Large-scale machine learning on heterogeneous systems (2015), https://www.tensorflow.org/, software available from tensorﬂow.org\\n\\n[2] Alberti, M., Pondenkandath, V., W¨ursch, M., Ingold, R., Liwicki, M.: Deepdiva: a highly-functional python framework for reproducible experiments. In: 2018 16th International Conference on Frontiers in Handwriting Recognition (ICFHR). pp. 423–428. IEEE (2018)\\n\\n[3] Antonacopoulos, A., Bridson, D., Papadopoulos, C., Pletschacher, S.: A realistic dataset for performance evaluation of document layout analysis. In: 2009 10th International Conference on Document Analysis and Recognition. pp. 296–300. IEEE (2009)\\n\\n[4] Baek, Y., Lee, B., Han, D., Yun, S., Lee, H.: Character region awareness for text detection. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 9365–9374 (2019)\\n\\n[5] Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: ImageNet: A Large-Scale\\n\\nHierarchical Image Database. In: CVPR09 (2009)\\n\\n[6] Deng, Y., Kanervisto, A., Ling, J., Rush, A.M.: Image-to-markup generation with coarse-to-ﬁne attention. In: International Conference on Machine Learning. pp. 980–989. PMLR (2017)\\n\\n[7] Ganin, Y., Lempitsky, V.: Unsupervised domain adaptation by backpropagation. In: International conference on machine learning. pp. 1180–1189. PMLR (2015)\\n\\nLayoutParser: A Uniﬁed Toolkit for DL-Based DIA\\n\\n[8] Gardner, M., Grus, J., Neumann, M., Tafjord, O., Dasigi, P., Liu, N., Peters, M., Schmitz, M., Zettlemoyer, L.: Allennlp: A deep semantic natural language processing platform. arXiv preprint arXiv:1803.07640 (2018) (cid:32)Lukasz Garncarek, Powalski, R., Stanis(cid:32)lawek, T., Topolski, B., Halama, P., Grali´nski, F.: Lambert: Layout-aware (language) modeling using bert for in- formation extraction (2020)\\n\\n[9]\\n\\n[10] Graves, A., Fern´andez, S., Gomez, F., Schmidhuber, J.: Connectionist temporal classiﬁcation: labelling unsegmented sequence data with recurrent neural networks. In: Proceedings of the 23rd international conference on Machine learning. pp. 369–376 (2006)\\n\\n[11] Harley, A.W., Ufkes, A., Derpanis, K.G.: Evaluation of deep convolutional nets for document image classiﬁcation and retrieval. In: 2015 13th International Conference on Document Analysis and Recognition (ICDAR). pp. 991–995. IEEE (2015) [12] He, K., Gkioxari, G., Doll´ar, P., Girshick, R.: Mask r-cnn. In: Proceedings of the\\n\\nIEEE international conference on computer vision. pp. 2961–2969 (2017)\\n\\n[13] He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016)\\n\\n[14] Kay, A.: Tesseract: An open-source optical character recognition engine. Linux J.\\n\\n2007(159), 2 (Jul 2007)\\n\\n[15] Lamiroy, B., Lopresti, D.: An open architecture for end-to-end document analysis benchmarking. In: 2011 International Conference on Document Analysis and Recognition. pp. 42–47. IEEE (2011)\\n\\n[16] Lee, B.C., Weld, D.S.: Newspaper navigator: Open faceted search for 1.5 million images. In: Adjunct Publication of the 33rd Annual ACM Sym- posium on User Interface Software and Technology. p. 120–122. UIST ’20 Adjunct, Association for Computing Machinery, New York, NY, USA (2020). https://doi.org/10.1145/3379350.3416143, https://doi-org.offcampus. lib.washington.edu/10.1145/3379350.3416143\\n\\n[17] Lee, B.C.G., Mears, J., Jakeway, E., Ferriter, M., Adams, C., Yarasavage, N., Thomas, D., Zwaard, K., Weld, D.S.: The Newspaper Navigator Dataset: Extracting Headlines and Visual Content from 16 Million Historic Newspaper Pages in Chronicling America, p. 3055–3062. Association for Computing Machinery, New York, NY, USA (2020), https://doi.org/10.1145/3340531.3412767\\n\\n[18] Li, M., Cui, L., Huang, S., Wei, F., Zhou, M., Li, Z.: Tablebank: Table benchmark for image-based table detection and recognition. arXiv preprint arXiv:1903.01949 (2019)\\n\\n[19] Lin, T.Y., Maire, M., Belongie, S., Hays, J., Perona, P., Ramanan, D., Doll´ar, P., Zitnick, C.L.: Microsoft coco: Common objects in context. In: European conference on computer vision. pp. 740–755. Springer (2014)\\n\\n[20] Long, J., Shelhamer, E., Darrell, T.: Fully convolutional networks for semantic segmentation. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 3431–3440 (2015)\\n\\n[21] Neudecker, C., Schlarb, S., Dogan, Z.M., Missier, P., Suﬁ, S., Williams, A., Wolsten- croft, K.: An experimental workﬂow development platform for historical document digitisation and analysis. In: Proceedings of the 2011 workshop on historical document imaging and processing. pp. 161–168 (2011)\\n\\n[22] Oliveira, S.A., Seguin, B., Kaplan, F.: dhsegment: A generic deep-learning approach for document segmentation. In: 2018 16th International Conference on Frontiers in Handwriting Recognition (ICFHR). pp. 7–12. IEEE (2018)\\n\\n15\\n\\n16\\n\\nZ. Shen et al.\\n\\n[23] Paszke, A., Gross, S., Chintala, S., Chanan, G., Yang, E., DeVito, Z., Lin, Z., Desmaison, A., Antiga, L., Lerer, A.: Automatic diﬀerentiation in pytorch (2017) [24] Paszke, A., Gross, S., Massa, F., Lerer, A., Bradbury, J., Chanan, G., Killeen, T., Lin, Z., Gimelshein, N., Antiga, L., et al.: Pytorch: An imperative style, high-performance deep learning library. arXiv preprint arXiv:1912.01703 (2019) [25] Pletschacher, S., Antonacopoulos, A.: The page (page analysis and ground-truth elements) format framework. In: 2010 20th International Conference on Pattern Recognition. pp. 257–260. IEEE (2010)\\n\\n[26] Prasad, D., Gadpal, A., Kapadni, K., Visave, M., Sultanpure, K.: Cascadetabnet: An approach for end to end table detection and structure recognition from image- based documents. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops. pp. 572–573 (2020)\\n\\n[27] Qasim, S.R., Mahmood, H., Shafait, F.: Rethinking table recognition using graph neural networks. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 142–147. IEEE (2019)\\n\\n[28] Ren, S., He, K., Girshick, R., Sun, J.: Faster r-cnn: Towards real-time object detection with region proposal networks. In: Advances in neural information processing systems. pp. 91–99 (2015)\\n\\n[29] Scarselli, F., Gori, M., Tsoi, A.C., Hagenbuchner, M., Monfardini, G.: The graph neural network model. IEEE transactions on neural networks 20(1), 61–80 (2008) [30] Schreiber, S., Agne, S., Wolf, I., Dengel, A., Ahmed, S.: Deepdesrt: Deep learning for detection and structure recognition of tables in document images. In: 2017 14th IAPR international conference on document analysis and recognition (ICDAR). vol. 1, pp. 1162–1167. IEEE (2017)\\n\\n[31] Shen, Z., Zhang, K., Dell, M.: A large dataset of historical japanese documents with complex layouts. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops. pp. 548–549 (2020)\\n\\n[32] Shen, Z., Zhao, J., Dell, M., Yu, Y., Li, W.: Olala: Object-level active learning\\n\\nbased layout annotation. arXiv preprint arXiv:2010.01762 (2020)\\n\\n[33] Studer, L., Alberti, M., Pondenkandath, V., Goktepe, P., Kolonko, T., Fischer, A., Liwicki, M., Ingold, R.: A comprehensive study of imagenet pre-training for historical document image analysis. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 720–725. IEEE (2019)\\n\\n[34] Wolf, T., Debut, L., Sanh, V., Chaumond, J., Delangue, C., Moi, A., Cistac, P., Rault, T., Louf, R., Funtowicz, M., et al.: Huggingface’s transformers: State-of- the-art natural language processing. arXiv preprint arXiv:1910.03771 (2019) [35] Wu, Y., Kirillov, A., Massa, F., Lo, W.Y., Girshick, R.: Detectron2. https://\\n\\ngithub.com/facebookresearch/detectron2 (2019)\\n\\n[36] Xu, Y., Xu, Y., Lv, T., Cui, L., Wei, F., Wang, G., Lu, Y., Florencio, D., Zhang, C., Che, W., et al.: Layoutlmv2: Multi-modal pre-training for visually-rich document understanding. arXiv preprint arXiv:2012.14740 (2020)\\n\\n[37] Xu, Y., Li, M., Cui, L., Huang, S., Wei, F., Zhou, M.: Layoutlm: Pre-training of\\n\\ntext and layout for document image understanding (2019)\\n\\n[38] Zhong, X., Tang, J., Yepes, A.J.: Publaynet:\\n\\nlargest dataset ever for doc- In: 2019 International Conference on Document IEEE (Sep 2019).\\n\\nument Analysis and Recognition (ICDAR). pp. 1015–1022. https://doi.org/10.1109/ICDAR.2019.00166\\n\\nlayout analysis.')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["docs = loader.load()\n", "docs[0]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'source': './example_data/layout-parser-paper.pdf'}\n"]}], "source": ["print(docs[0].metadata)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Retain Elements\n", "\n", "Under the hood, Unstructured creates different \"elements\" for different chunks of text. By default we combine those together, but you can easily keep that separation by specifying `mode=\"elements\"`."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': './example_data/layout-parser-paper.pdf', 'coordinates': {'points': ((16.34, 213.36), (16.34, 253.36), (36.34, 253.36), (36.34, 213.36)), 'system': 'PixelSpace', 'layout_width': 612, 'layout_height': 792}, 'file_directory': './example_data', 'filename': 'layout-parser-paper.pdf', 'languages': ['eng'], 'last_modified': '2024-07-25T21:28:58', 'page_number': 1, 'filetype': 'application/pdf', 'category': 'UncategorizedText', 'element_id': 'd3ce55f220dfb75891b4394a18bcb973'}, page_content='1 2 0 2')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["file_path = \"./example_data/layout-parser-paper.pdf\"\n", "loader = UnstructuredPDFLoader(file_path, mode=\"elements\")\n", "\n", "data = loader.load()\n", "data[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["See the full set of element types for this particular document:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ListItem', 'NarrativeText', 'Title', 'UncategorizedText'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["set(doc.metadata[\"category\"] for doc in data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Fetching remote PDFs using Unstructured\n", "\n", "This covers how to load online PDFs into a document format that we can use downstream. This can be used for various online PDF sites such as https://open.umn.edu/opentextbooks/textbooks/ and https://arxiv.org/archive/\n", "\n", "Note: all other PDF loaders can also be used to fetch remote PDFs, but `OnlinePDFLoader` is a legacy function, and works specifically with `UnstructuredPDFLoader`."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': '/var/folders/td/vzm913rx77x21csd90g63_7c0000gn/T/tmp3qdyy7e8/tmp.pdf'}, page_content='3 2 0 2\\n\\nb e F 7\\n\\n]\\n\\nG A . h t a m\\n\\n[\\n\\n1 v 3 0 8 3 0 . 2 0 3 2 : v i X r a\\n\\nA WEAK (k, k)-LEFSCHETZ THEOREM FOR PROJECTIVE TORIC ORBIFOLDS\\n\\nWilliam D<PERSON>\\n\\nInstituto de Matem´atica, Estat´ıstica e Computa¸c˜ao Cient´ıﬁca, Universidade Estadual de Campinas (UNICAMP),\\n\\nRua S´ergio Buarque de Holanda 651, 13083-859, Campinas, SP, Brazil\\n\\nFebruary 9, 2023\\n\\nAbstract\\n\\nFirstly we show a generalization of the (1, 1)-Le<PERSON>chet<PERSON> theorem for projective toric orbifolds and secondly we prove that on 2k-dimensional quasi-smooth hyper- surfaces coming from quasi-smooth intersection surfaces, under the <PERSON><PERSON><PERSON> trick, every rational (k, k)-cohomology class is algebraic, i.e., the Hodge conjecture holds on them.\\n\\n1\\n\\nIntroduction\\n\\nIn [3] we proved that, under suitable conditions, on a very general codimension s quasi- smooth intersection subvariety X in a projective toric orbifold Pd Σ with d + s = 2(k + 1) the Hodge conjecture holds, that is, every (p, p)-cohomology class, under the Poincar´e duality is a rational linear combination of fundamental classes of algebraic subvarieties of X. The proof of the above-mentioned result relies, for p ≠ d + 1 − s, on a Lefschetz\\n\\nDate: February 9, 2023 2020 Mathematics Subject Classiﬁcation: 14C30, 14M10, 14J70, 14M25 Keywords: (1,1)- Lefschetz theorem, Hodge conjecture, toric varieties, complete intersection Email: <EMAIL>\\n\\n1\\n\\ntheorem ([7]) and the Hard Lefschetz theorem for projective orbifolds ([11]). When p = d + 1 − s the proof relies on the Cayley trick, a trick which associates to X a quasi-smooth hypersurface Y in a projective vector bundle, and the Cayley Proposition (4.3) which gives an isomorphism of some primitive cohomologies (4.2) of X and Y . The Cayley trick, following the philosophy of Mavlyutov in [7], reduces results known for quasi-smooth hypersurfaces to quasi-smooth intersection subvarieties. The idea in this paper goes the other way around, we translate some results for quasi-smooth intersection subvarieties to quasi-smooth hypersurfaces, mainly the (1, 1)-Lefschetz theorem.\\n\\nAcknowledgement. I thank Prof. Ugo Bruzzo and Tiago Fonseca for useful discus-\\n\\nsions. I also acknowledge support from FAPESP postdoctoral grant No. 2019/23499-7.\\n\\n2 Preliminaries and Notation\\n\\n2.1 Toric varieties\\n\\nLet M be a free abelian group of rank d, let N = Hom(M, Z), and NR = N ⊗Z R.\\n\\nA convex subset σ ⊂ NR is a rational k-dimensional simplicial cone if there exist k linearly independent primitive elements e1, . . . , ek ∈ N such that σ = {µ1e1 + ⋯ + µkek}.\\n\\nDeﬁnition 2.1.\\n\\nThe generators ei are integral if for every i and any nonnegative rational number µ the product µei is in N only if µ is an integer.\\n\\nGiven two rational simplicial cones σ, σ′ one says that σ′ is a face of σ (σ′ < σ) if the set of integral generators of σ′ is a subset of the set of integral generators of σ.\\n\\nA ﬁnite set Σ = {σ1, . . . , σt} of rational simplicial cones is called a rational simplicial complete d-dimensional fan if:\\n\\n1. all faces of cones in Σ are in Σ;\\n\\n2. if σ, σ′ ∈ Σ then σ ∩ σ′ < σ and σ ∩ σ′ < σ′;\\n\\n3. NR = σ1 ∪ ⋅ ⋅ ⋅ ∪ σt.\\n\\nA rational simplicial complete d-dimensional fan Σ deﬁnes a d-dimensional toric variety Σ having only orbifold singularities which we assume to be projective. Moreover, T ∶= Pd N ⊗Z C∗ ≃ (C∗)d is the torus action on Pd Σ. We denote by Σ(i) the i-dimensional cones\\n\\n2\\n\\nof Σ and each ρ ∈ Σ corresponds to an irreducible T -invariant Weil divisor Dρ on Pd Cl(Σ) be the group of Weil divisors on Pd\\n\\nΣ module rational equivalences.\\n\\nΣ. Let\\n\\nThe total coordinate ring of Pd\\n\\nΣ is the polynomial ring S = C[xρ ∣ ρ ∈ Σ(1)], S has the ρ ∈\\n\\nCl(Σ)-grading, a Weil divisor D = ∑ρ∈Σ(1) uρDρ determines the monomial xu ∶= ∏ρ∈Σ(1) xuρ S and conversely deg(xu) = [D] ∈ Cl(Σ).\\n\\nFor a cone σ ∈ Σ, ˆσ is the set of 1-dimensional cone in Σ that are not contained in σ\\n\\nand xˆσ ∶= ∏ρ∈ˆσ xρ is the associated monomial in S.\\n\\nΣ is the monomial ideal BΣ ∶=< xˆσ ∣ σ ∈ Σ > and\\n\\nDeﬁnition 2.2. The irrelevant ideal of Pd the zero locus Z(Σ) ∶= V(BΣ) in the aﬃne space Ad ∶= Spec(S) is the irrelevant locus.\\n\\nProposition 2.3 (Theorem 5.1.11 [5]). The toric variety Pd Σ is a categorical quotient Ad ∖ Z(Σ) by the group Hom(Cl(Σ), C∗) and the group action is induced by the Cl(Σ)- grading of S.\\n\\n2.2 Orbifolds\\n\\nNow we give a brief introduction to complex orbifolds and we mention the needed theorems for the next section. Namely: de Rham theorem and Dolbeault theorem for complex orbifolds.\\n\\nDeﬁnition 2.4. A complex orbifold of complex dimension d is a singular complex space whose singularities are locally isomorphic to quotient singularities Cd/G, for ﬁnite sub- groups G ⊂ Gl(d, C).\\n\\nDeﬁnition 2.5. A diﬀerential form on a complex orbifold Z is deﬁned locally at z ∈ Z as a G-invariant diﬀerential form on Cd where G ⊂ Gl(d, C) and Z is locally isomorphic to Cd/G around z.\\n\\nRoughly speaking the local geometry of orbifolds reduces to local G-invariant geometry. We have a complex of diﬀerential forms (A●(Z), d) and a double complex (A●,●(Z), ∂, ¯∂) of bigraded diﬀerential forms which deﬁne the de Rham and the Dolbeault cohomology groups (for a ﬁxed p ∈ N) respectively:\\n\\ndR(Z, C) ∶=\\n\\nH ●\\n\\nker d im d\\n\\nand H p,●(Z, ¯∂) ∶=\\n\\nker ¯∂ im ¯∂\\n\\nTheorem 2.6 (Theorem 3.4.4 in [4] and Theorem 1.2 in [1] ). Let Z be a compact complex orbifold. There are natural isomorphisms:\\n\\n3\\n\\nH ●\\n\\ndR(Z, C) ≃ H ●(Z, C)\\n\\nH p,●(Z, ¯∂) ≃ H ●(X, Ωp Z )\\n\\n3\\n\\n(1,1)-Lefschetz theorem for projective toric orbifolds\\n\\nDeﬁnition 3.1. A subvariety X ⊂ Pd Z(Σ).\\n\\nΣ is quasi-smooth if V(IX ) ⊂ A#Σ(1) is smooth outside\\n\\nExample 3.2. Quasi-smooth hypersurfaces or more generally quasi-smooth intersection sub- varieties are quasi-smooth subvarieties (see [2] or [7] for more details).\\n\\nRemark 3.3. Quasi-smooth subvarieties are suborbifolds of Pd Σ in the sense of Satake in [8]. Intuitively speaking they are subvarieties whose only singularities come from the ambient space.\\n\\nTheorem 3.4. Let X ⊂ Pd class λ ∈ H 1,1(X) ∩ H 2(X, Z) is algebraic\\n\\nΣ be a quasi-smooth subvariety. Then every (1, 1)-cohomology\\n\\nProof. From the exponential short exact sequence\\n\\n0 → Z → OX → O∗ X\\n\\n→ 0\\n\\nwe have a long exact sequence in cohomology\\n\\nX ) → H 2(X, Z) → H 2(OX ) ≃ H 0,2(X)\\n\\nH 1(O∗\\n\\nwhere the last isomorphisms is due to Steenbrink in [9]. Now, it is enough to prove the commutativity of the next diagram\\n\\nH 2(X, Z)\\n\\nH 2(X, OX )\\n\\nH 2(X, C)\\n\\n≃ Dolbeault\\n\\nde Rham ≃\\n\\n(cid:15)\\n\\n(cid:15)\\n\\nH 2\\n\\ndR(X, C)\\n\\n/\\n\\n/ H 0,2\\n\\n¯∂ (X)\\n\\n4\\n\\n△\\n\\n△\\n\\nThe key points are the de Rham and Dolbeault’s isomorphisms for orbifolds. The rest\\n\\nof the proof follows as the (1, 1)-Lefschetz theorem in [6].\\n\\nRemark 3.5. For k = 1 and Pd Lefschetz theorem.\\n\\nΣ as the projective space, we recover the classical (1, 1)-\\n\\nBy the Hard Lefschetz Theorem for projective orbifolds (see [11] for details) we get an\\n\\nisomorphism of cohomologies :\\n\\nH ●(X, Q) ≃ H 2 dim X−●(X, Q)\\n\\ngiven by the Lefschetz morphism and since it is a morphism of Hodge structures, we have:\\n\\nH 1,1(X, Q) ≃ H dim X−1,dim X−1(X, Q)\\n\\nFor X as before:\\n\\nCorollary 3.6. If the dimension of X is 1, 2 or 3. The Hodge conjecture holds on X.\\n\\nProof. If the dimCX = 1 the result is clear by the Hard Lefschetz theorem for projective orbifolds. The dimension 2 and 3 cases are covered by Theorem 3.5 and the Hard Lefschetz. theorem.\\n\\n4 Cayley trick and Cayley proposition\\n\\nThe Cayley trick is a way to associate to a quasi-smooth intersection subvariety a quasi- smooth hypersurface. Let L1, . . . , Ls be line bundles on Pd Σ be the projective space bundle associated to the vector bundle E = L1 ⊕ ⋯ ⊕ Ls. It is known that P(E) is a (d + s − 1)-dimensional simplicial toric variety whose fan depends on the degrees of the line bundles and the fan Σ. Furthermore, if the Cox ring, without considering the grading, of Pd\\n\\nΣ and let π ∶ P(E) → Pd\\n\\nΣ is C[x1, . . . , xm] then the Cox ring of P(E) is\\n\\nC[x1, . . . , xm, y1, . . . , ys]\\n\\nMoreover for X a quasi-smooth intersection subvariety cut oﬀ by f1, . . . , fs with deg(fi) = [Li] we relate the hypersurface Y cut oﬀ by F = y1f1 + ⋅ ⋅ ⋅ + ysfs which turns out to be quasi-smooth. For more details see Section 2 in [7].\\n\\n5\\n\\n△\\n\\nWe will denote P(E) as Pd+s−1\\n\\nΣ,X to keep track of its relation with X and Pd Σ.\\n\\nThe following is a key remark.\\n\\nRemark 4.1. There is a morphism ι ∶ X → Y ⊂ Pd+s−1 with y ≠ 0 has a preimage. Hence for any subvariety W = V(IW ) ⊂ X ⊂ Pd W ′ ⊂ Y ⊂ Pd+s−1 Σ,X such that π(W ′) = W , i.e., W ′ = {z = (x, y) ∣ x ∈ W }.\\n\\nΣ,X . Moreover every point z ∶= (x, y) ∈ Y Σ there exists\\n\\n△\\n\\nFor X ⊂ Pd\\n\\nΣ a quasi-smooth intersection variety the morphism in cohomology induced\\n\\nby the inclusion i∗ ∶ H d−s(Pd\\n\\nΣ, C) → H d−s(X, C) is injective by Proposition 1.4 in [7].\\n\\nDeﬁnition 4.2. The primitive cohomology of H d−s and H d−s prim(X, Q) with rational coeﬃcients.\\n\\nprim(X) is the quotient H d−s(X, C)/i∗(H d−s(Pd\\n\\nH d−s(Pd\\n\\nΣ, C) and H d−s(X, C) have pure Hodge structures, and the morphism i∗ is com-\\n\\npatible with them, so that H d−s\\n\\nprim(X) gets a pure Hodge structure.\\n\\nThe next Proposition is the Cayley proposition.\\n\\nProposition 4.3. [Proposition 2.3 in [3] ] Let X = X1 ∩⋅ ⋅ ⋅∩Xs be a quasi-smooth intersec- , d+s−3 tion subvariety in Pd 2\\n\\nΣ cut oﬀ by homogeneous polynomials f1 . . . fs. Then for p ≠ d+s−1\\n\\n2\\n\\nH p−1,d+s−1−p\\n\\nprim\\n\\n(Y ) ≃ H p−s,d−p\\n\\nprim (X).\\n\\nCorollary 4.4. If d + s = 2(k + 1),\\n\\nH k+1−s,k+1−s\\n\\nprim\\n\\n(X) ≃ H k,k\\n\\nprim(Y )\\n\\nRemark 4.5. The above isomorphisms are also true with rational coeﬃcients since H ●(X, C) = H ●(X, Q) ⊗Q C. See the beginning of Section 7.1 in [10] for more details.\\n\\n△\\n\\n5 Main result\\n\\nTheorem 5.1. Let Y = {F = y1f1 + ⋯ + ykfk = 0} ⊂ P2k+1 associated to the quasi-smooth intersection surface X = Xf1 ∩ ⋅ ⋅ ⋅ ∩ Xfk ⊂ Pk+2 the Hodge conjecture holds.\\n\\nΣ,X be the quasi-smooth hypersurface Σ . Then on Y\\n\\nProof. If H k,k proposition H k,k\\n\\nprim(X, Q) = 0 we are done. So let us assume H k,k\\n\\nprim(X, Q) ≠ 0. By the Cayley prim(X, Q) and by the (1, 1)-Lefschetz theorem for projective\\n\\nprim(Y, Q) ≃ H 1,1\\n\\n6\\n\\nΣ, C))\\n\\ntoric orbifolds there is a non-zero algebraic basis λC1, . . . , λCn with rational coeﬃcients of H 1,1 prim(X, Q) algebraic curves C1, . . . , Cn in X such that under the Poincar´e duality the class in homology [Ci] goes to λCi, [Ci] ↦ λCi. Recall that the Cox ring of Pk+2 is contained in the Cox ring of P2k+1 Σ,X without considering the Σ ) then (α, 0) ∈ Cl(P2k+1 grading. Considering the grading we have that if α ∈ Cl(Pk+2 Σ,X ). So the polynomials deﬁning Ci ⊂ Pk+2 X,Σ but with diﬀerent degree. Moreover, by Remark 4.1 each Ci is contained in Y = {F = y1f1 + ⋯ + ykfk = 0} and furthermore it has codimension k.\\n\\nprim(X, Q), that is, there are n ∶= h1,1\\n\\ncan be interpreted in P2k+1\\n\\nΣ\\n\\ni=1 is a basis of H k,k It is enough to prove that λCi is diﬀerent from zero in H k,k prim(Y, Q) or equivalently that the cohomology classes {λCi}n i=1 do not come from the ambient space. By contradiction, let us assume that there exists a j and C ⊂ P2k+1 Σ,X , Q) with i∗(λC) = λCj or in terms of homology there exists a (k + 2)-dimensional algebraic subvariety V ⊂ P2k+1 Σ,X such that V ∩ Y = Cj so they are equal as a homology class of P2k+1 Σ,X ,i.e., [V ∩ Y ] = [Cj] . Σ where π ∶ (x, y) ↦ x. Hence It is easy to check that π(V ) ∩ X = Cj as a subvariety of Pk+2 [π(V ) ∩ X] = [Cj] which is equivalent to say that λCj comes from Pk+2 Σ which contradicts the choice of [Cj].\\n\\nClaim: {λCi}n\\n\\nprim(Y, Q).\\n\\nΣ,X such that λC ∈ H k,k(P2k+1\\n\\nRemark 5.2. Into the proof of the previous theorem, the key fact was that on X the Hodge conjecture holds and we translate it to Y by contradiction. So, using an analogous argument we have:\\n\\nProposition 5.3. Let Y = {F = y1fs+⋯+ysfs = 0} ⊂ P2k+1 associated to a quasi-smooth intersection subvariety X = Xf1 ∩ ⋅ ⋅ ⋅ ∩ Xfs ⊂ Pd d + s = 2(k + 1). If the Hodge conjecture holds on X then it holds as well on Y .\\n\\nΣ,X be the quasi-smooth hypersurface Σ such that\\n\\nCorollary 5.4. If the dimension of Y is 2s − 1, 2s or 2s + 1 then the Hodge conjecture holds on Y .\\n\\nProof. By Proposition 5.3 and Corollary 3.6.\\n\\n7\\n\\n△\\n\\nReferences\\n\\n[1] Angella, D. Cohomologies of certain orbifolds. Journal of Geometry and Physics\\n\\n71 (2013), 117–126.\\n\\n[2] Batyrev, V. V., and Cox, D. A. On the Hodge structure of projective hypersur-\\n\\nfaces in toric varieties. Duke Mathematical Journal 75, 2 (Aug 1994).\\n\\n[3] Bruzzo, U., and Montoya, W. On the Hodge conjecture for quasi-smooth in- tersections in toric varieties. S˜ao Paulo J. Math. Sci. Special Section: Geometry in Algebra and Algebra in Geometry (2021).\\n\\n[4] Caramello Jr, F. C. Introduction to orbifolds. arXiv:1909.08699v6 (2019).\\n\\n[5] Cox, D., Little, J., and Schenck, H. Toric varieties, vol. 124. American Math-\\n\\nematical Soc., 2011.\\n\\n[6] Griffiths, P., and Harris, J. Principles of Algebraic Geometry. John Wiley &\\n\\nSons, Ltd, 1978.\\n\\n[7] Mavlyutov, A. R. Cohomology of complete intersections in toric varieties. Pub-\\n\\nlished in Paciﬁc J. of Math. 191 No. 1 (1999), 133–144.\\n\\n[8] Satake, I. On a Generalization of the Notion of Manifold. Proceedings of the National Academy of Sciences of the United States of America 42, 6 (1956), 359–363.\\n\\n[9] Steenbrink, J. H. M. Intersection form for quasi-homogeneous singularities. Com-\\n\\npositio Mathematica 34, 2 (1977), 211–223.\\n\\n[10] Voisin, C. Hodge Theory and Complex Algebraic Geometry I, vol. 1 of Cambridge\\n\\nStudies in Advanced Mathematics. Cambridge University Press, 2002.\\n\\n[11] Wang, Z. Z., and Zaffran, D. A remark on the Hard Lefschetz theorem for K¨ahler orbifolds. Proceedings of the American Mathematical Society 137, 08 (Aug 2009).\\n\\n8')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.document_loaders import OnlinePDFLoader\n", "\n", "loader = OnlinePDFLoader(\"https://arxiv.org/pdf/2302.03803.pdf\")\n", "data = loader.load()\n", "data[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> Load"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["page = []\n", "for doc in loader.lazy_load():\n", "    page.append(doc)\n", "    if len(page) >= 10:\n", "        # do some paged operation, e.g.\n", "        # index.upsert(page)\n", "\n", "        page = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all UnstructuredPDFLoader features and configurations head to the API reference: https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.pdf.UnstructuredPDFLoader.html"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}