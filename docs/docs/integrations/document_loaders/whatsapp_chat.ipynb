{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# WhatsApp Chat\n", "\n", ">[WhatsApp](https://www.whatsapp.com/) (also called `WhatsApp Messenger`) is a freeware, cross-platform, centralized instant messaging (IM) and voice-over-IP (VoIP) service. It allows users to send text and voice messages, make voice and video calls, and share images, documents, user locations, and other content.\n", "\n", "This notebook covers how to load data from the `WhatsApp Chats` into a format that can be ingested into LangChain."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import WhatsAppChatLoader"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["loader = WhatsAppChatLoader(\"example_data/whatsapp_chat.txt\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["loader.load()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "vscode": {"interpreter": {"hash": "384707f4965e853a82006e90614c2e1a578ea1f6eb0ee07a1dd78a657d37dd67"}}}, "nbformat": 4, "nbformat_minor": 4}