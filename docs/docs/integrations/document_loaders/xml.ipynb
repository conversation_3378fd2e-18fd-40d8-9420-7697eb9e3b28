{"cells": [{"cell_type": "markdown", "id": "72ccbe2b", "metadata": {}, "source": ["# UnstructuredXMLLoader\n", "\n", "This notebook provides a quick overview for getting started with UnstructuredXMLLoader [document loader](https://python.langchain.com/docs/concepts/document_loaders). The `UnstructuredXMLLoader` is used to load `XML` files. The loader works with `.xml` files. The page content will be the text extracted from the XML tags.\n", "\n", "\n", "## Overview\n", "### Integration details\n", "\n", "\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/document_loaders/file_loaders/unstructured/)|\n", "| :--- | :--- | :---: | :---: |  :---: |\n", "| [UnstructuredXMLLoader](https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.xml.UnstructuredXMLLoader.html) | [langchain_community](https://python.langchain.com/api_reference/community/index.html) | ✅ | ❌ | ✅ | \n", "### Loader features\n", "| Source | Document Lazy Loading | Native Async Support\n", "| :---: | :---: | :---: | \n", "| UnstructuredXMLLoader | ✅ | ❌ | \n", "\n", "## Setup\n", "\n", "To access UnstructuredXMLLoader document loader you'll need to install the `langchain-community` integration package.\n", "\n", "### Credentials\n", "\n", "No credentials are needed to use the UnstructuredXMLLoader"]}, {"cell_type": "markdown", "id": "fc4ba987", "metadata": {}, "source": "To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:"}, {"cell_type": "code", "execution_count": null, "id": "9fa4d5e5", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")\n", "# os.environ[\"LANGSMITH_TRACING\"] = \"true\""]}, {"cell_type": "markdown", "id": "38e53f22", "metadata": {}, "source": ["### Installation\n", "\n", "Install **langchain_community**."]}, {"cell_type": "code", "execution_count": null, "id": "fcd320ec", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain_community"]}, {"cell_type": "markdown", "id": "a102f199", "metadata": {}, "source": ["## Initialization\n", "\n", "Now we can instantiate our model object and load documents:"]}, {"cell_type": "code", "execution_count": 2, "id": "2d198582", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import UnstructuredXMLLoader\n", "\n", "loader = UnstructuredXMLLoader(\n", "    \"./example_data/factbook.xml\",\n", ")"]}, {"cell_type": "markdown", "id": "9bbb463c", "metadata": {}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 3, "id": "cd875e75", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': './example_data/factbook.xml'}, page_content='United States\\n\\nWashington, DC\\n\\nJoe Biden\\n\\nBaseball\\n\\nCanada\\n\\nOttawa\\n\\nJustin Trudeau\\n\\nHockey\\n\\nFrance\\n\\nParis\\n\\nEmmanuel Macron\\n\\nSoccer\\n\\nTrinidad & Tobado\\n\\nPort of Spain\\n\\nKeith <PERSON>ley\\n\\nTrack & Field')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["docs = loader.load()\n", "docs[0]"]}, {"cell_type": "code", "execution_count": 4, "id": "79b52cc0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'source': './example_data/factbook.xml'}\n"]}], "source": ["print(docs[0].metadata)"]}, {"cell_type": "markdown", "id": "557608e5", "metadata": {}, "source": ["## <PERSON><PERSON> Load"]}, {"cell_type": "code", "execution_count": 5, "id": "e3b9e75c", "metadata": {}, "outputs": [], "source": ["page = []\n", "for doc in loader.lazy_load():\n", "    page.append(doc)\n", "    if len(page) >= 10:\n", "        # do some paged operation, e.g.\n", "        # index.upsert(page)\n", "\n", "        page = []"]}, {"cell_type": "markdown", "id": "712aa98f", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all __ModuleName__Loader features and configurations head to the API reference: https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.xml.UnstructuredXMLLoader.html"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}