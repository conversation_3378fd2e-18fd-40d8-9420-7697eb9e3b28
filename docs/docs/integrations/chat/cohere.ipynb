{"cells": [{"cell_type": "raw", "id": "53fbf15f", "metadata": {}, "source": ["---\n", "sidebar_label: Cohere\n", "---"]}, {"cell_type": "markdown", "id": "bf733a38-db84-4363-89e2-de6735c37230", "metadata": {}, "source": ["# Cohere\n", "\n", "This notebook covers how to get started with [Cohere chat models](https://cohere.com/chat).\n", "\n", "Head to the [API reference](https://python.langchain.com/api_reference/community/chat_models/langchain_community.chat_models.cohere.ChatCohere.html) for detailed documentation of all attributes and methods."]}, {"cell_type": "markdown", "id": "3607d67e-e56c-4102-bbba-df2edc0e109e", "metadata": {}, "source": ["## Setup\n", "\n", "The integration lives in the `langchain-cohere` package. We can install these with:\n", "\n", "```bash\n", "pip install -<PERSON> langchain-cohere\n", "```\n", "\n", "We'll also need to get a [Cohere API key](https://cohere.com/) and set the `COHERE_API_KEY` environment variable:"]}, {"cell_type": "code", "execution_count": 11, "id": "2108b517-1e8d-473d-92fa-4f930e8072a7", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "os.environ[\"COHERE_API_KEY\"] = getpass.getpass()"]}, {"cell_type": "markdown", "id": "cf690fbb", "metadata": {}, "source": ["It's also helpful (but not needed) to set up [LangSmith](https://smith.langchain.com/) for best-in-class observability"]}, {"cell_type": "code", "execution_count": null, "id": "3c2fc2201dc80557", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")"]}, {"cell_type": "markdown", "id": "31f2af10e04dec59", "metadata": {}, "source": ["## Usage\n", "\n", "ChatCohere supports all [ChatModel](/docs/how_to#chat-models) functionality:"]}, {"cell_type": "code", "execution_count": null, "id": "fa83b00a929614ad", "metadata": {}, "outputs": [], "source": ["from langchain_cohere import ChatCohere\n", "from langchain_core.messages import HumanMessage"]}, {"cell_type": "code", "execution_count": 13, "id": "70cf04e8-423a-4ff6-8b09-f11fb711c817", "metadata": {"tags": []}, "outputs": [], "source": ["chat = Chat<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 15, "id": "8199ef8f-eb8b-4253-9ea0-6c24a013ca4c", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content='4 && 5 \\n6 || 7 \\n\\nWould you like to play a game of odds and evens?', additional_kwargs={'documents': None, 'citations': None, 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': '2076b614-52b3-4082-a259-cc92cd3d9fea', 'token_count': {'prompt_tokens': 68, 'response_tokens': 23, 'total_tokens': 91, 'billed_tokens': 77}}, response_metadata={'documents': None, 'citations': None, 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': '2076b614-52b3-4082-a259-cc92cd3d9fea', 'token_count': {'prompt_tokens': 68, 'response_tokens': 23, 'total_tokens': 91, 'billed_tokens': 77}}, id='run-3475e0c8-c89b-4937-9300-e07d652455e1-0')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [HumanMessage(content=\"1\"), HumanMessage(content=\"2 3\")]\n", "chat.invoke(messages)"]}, {"cell_type": "code", "execution_count": 16, "id": "c5fac0e9-05a4-4fc1-a3b3-e5bbb24b971b", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content='4 && 5', additional_kwargs={'documents': None, 'citations': None, 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': 'f0708a92-f874-46ee-9b93-334d616ad92e', 'token_count': {'prompt_tokens': 68, 'response_tokens': 3, 'total_tokens': 71, 'billed_tokens': 57}}, response_metadata={'documents': None, 'citations': None, 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': 'f0708a92-f874-46ee-9b93-334d616ad92e', 'token_count': {'prompt_tokens': 68, 'response_tokens': 3, 'total_tokens': 71, 'billed_tokens': 57}}, id='run-1635e63e-2994-4e7f-986e-152ddfc95777-0')"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["await chat.ainvoke(messages)"]}, {"cell_type": "code", "execution_count": 17, "id": "025be980-e50d-4a68-93dc-c9c7b500ce34", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4 && 5"]}], "source": ["for chunk in chat.stream(messages):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "code", "execution_count": 18, "id": "064288e4-f184-4496-9427-bcf148fa055e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content='4 && 5', additional_kwargs={'documents': None, 'citations': None, 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': '6770ca86-f6c3-4ba3-a285-c4772160612f', 'token_count': {'prompt_tokens': 68, 'response_tokens': 3, 'total_tokens': 71, 'billed_tokens': 57}}, response_metadata={'documents': None, 'citations': None, 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': '6770ca86-f6c3-4ba3-a285-c4772160612f', 'token_count': {'prompt_tokens': 68, 'response_tokens': 3, 'total_tokens': 71, 'billed_tokens': 57}}, id='run-8d6fade2-1b39-4e31-ab23-4be622dd0027-0')]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.batch([messages])"]}, {"cell_type": "markdown", "id": "f1c56460", "metadata": {}, "source": ["## Chaining\n", "\n", "You can also easily combine with a prompt template for easy structuring of user input. We can do this using [LCEL](/docs/concepts/lcel)"]}, {"cell_type": "code", "execution_count": 19, "id": "0851b103", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_template(\"Tell me a joke about {topic}\")\n", "chain = prompt | chat"]}, {"cell_type": "code", "execution_count": 20, "id": "ae950c0f-1691-47f1-b609-273033cae707", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='What color socks do bears wear?\\n\\nThey don’t wear socks, they have bear feet. \\n\\nHope you laughed! If not, maybe this will help: laughter is the best medicine, and a good sense of humor is infectious!', additional_kwargs={'documents': None, 'citations': None, 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': '6edccf44-9bc8-4139-b30e-13b368f3563c', 'token_count': {'prompt_tokens': 68, 'response_tokens': 51, 'total_tokens': 119, 'billed_tokens': 108}}, response_metadata={'documents': None, 'citations': None, 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': '6edccf44-9bc8-4139-b30e-13b368f3563c', 'token_count': {'prompt_tokens': 68, 'response_tokens': 51, 'total_tokens': 119, 'billed_tokens': 108}}, id='run-ef7f9789-0d4d-43bf-a4f7-f2a0e27a5320-0')"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke({\"topic\": \"bears\"})"]}, {"cell_type": "markdown", "id": "12db8d69", "metadata": {}, "source": ["## <PERSON><PERSON> calling\n", "\n", "Cohere supports tool calling functionalities!"]}, {"cell_type": "code", "execution_count": 7, "id": "337e24af", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import (\n", "    HumanMessage,\n", "    ToolMessage,\n", ")\n", "from langchain_core.tools import tool"]}, {"cell_type": "code", "execution_count": 8, "id": "74d292e7", "metadata": {}, "outputs": [], "source": ["@tool\n", "def magic_function(number: int) -> int:\n", "    \"\"\"Applies a magic operation to an integer\n", "    Args:\n", "        number: Number to have magic operation performed on\n", "    \"\"\"\n", "    return number + 10\n", "\n", "\n", "def invoke_tools(tool_calls, messages):\n", "    for tool_call in tool_calls:\n", "        selected_tool = {\"magic_function\": magic_function}[tool_call[\"name\"].lower()]\n", "        tool_output = selected_tool.invoke(tool_call[\"args\"])\n", "        messages.append(ToolMessage(tool_output, tool_call_id=tool_call[\"id\"]))\n", "    return messages\n", "\n", "\n", "tools = [magic_function]"]}, {"cell_type": "code", "execution_count": 9, "id": "ecafcbc6", "metadata": {}, "outputs": [], "source": ["llm_with_tools = chat.bind_tools(tools=tools)\n", "messages = [HumanMessage(content=\"What is the value of magic_function(2)?\")]"]}, {"cell_type": "code", "execution_count": 11, "id": "aa34fc39", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='The value of magic_function(2) is 12.', additional_kwargs={'documents': [{'id': 'magic_function:0:2:0', 'output': '12', 'tool_name': 'magic_function'}], 'citations': [ChatCitation(start=34, end=36, text='12', document_ids=['magic_function:0:2:0'])], 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': '96a55791-0c58-4e2e-bc2a-8550e137c46d', 'token_count': {'input_tokens': 998, 'output_tokens': 59}}, response_metadata={'documents': [{'id': 'magic_function:0:2:0', 'output': '12', 'tool_name': 'magic_function'}], 'citations': [ChatCitation(start=34, end=36, text='12', document_ids=['magic_function:0:2:0'])], 'search_results': None, 'search_queries': None, 'is_search_required': None, 'generation_id': '96a55791-0c58-4e2e-bc2a-8550e137c46d', 'token_count': {'input_tokens': 998, 'output_tokens': 59}}, id='run-f318a9cf-55c8-44f4-91d1-27cf46c6a465-0')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["res = llm_with_tools.invoke(messages)\n", "while res.tool_calls:\n", "    messages.append(res)\n", "    messages = invoke_tools(res.tool_calls, messages)\n", "    res = llm_with_tools.invoke(messages)\n", "\n", "res"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}