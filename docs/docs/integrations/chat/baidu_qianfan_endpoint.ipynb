{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: <PERSON><PERSON>\n", "---"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# QianfanChatEndpoint\n", "\n", "Baidu AI Cloud Qianfan Platform is a one-stop large model development and service operation platform for enterprise developers. Qianfan not only provides including the model of <PERSON><PERSON> (ERNIE-Bot) and the third-party open-source models, but also provides various AI development tools and the whole set of development environment, which facilitates customers to use and develop large model applications easily.\n", "\n", "Basically, those model are split into the following type:\n", "\n", "- Embedding\n", "- <PERSON><PERSON>\n", "- Completion\n", "\n", "In this notebook, we will introduce how to use langchain with [<PERSON><PERSON><PERSON>](https://cloud.baidu.com/doc/WENXINWORKSHOP/index.html) mainly in `Chat` corresponding\n", " to the package `langchain/chat_models` in langchain:\n", "\n", "\n", "## API Initialization\n", "\n", "To use the LLM services based on Bai<PERSON>, you have to initialize these parameters:\n", "\n", "You could either choose to init the AK,SK in environment variables or init params:\n", "\n", "```base\n", "export QIANFAN_AK=XXX\n", "export QIANFAN_SK=XXX\n", "```\n", "\n", "## Current supported models:\n", "\n", "- ERNIE-Bot-turbo (default models)\n", "- ERNIE-<PERSON><PERSON>\n", "- BLOOMZ-7B\n", "- Llama-2-7b-chat\n", "- Llama-2-13b-chat\n", "- Llama-2-70b-chat\n", "- Qianfan-BLOOMZ-7B-compressed\n", "- Qianfan-Chinese-Llama-2-7B\n", "- ChatGLM2-6B-32K\n", "- AquilaChat-7B"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set up"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["\"\"\"For basic init and call\"\"\"\n", "import os\n", "\n", "from langchain_community.chat_models import QianfanChatEndpoint\n", "from langchain_core.language_models.chat_models import HumanMessage\n", "\n", "os.environ[\"QIANFAN_AK\"] = \"Your_api_key\"\n", "os.environ[\"QIANFAN_SK\"] = \"You_secret_Key\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Usage"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='您好！请问您需要什么帮助？我将尽力回答您的问题。')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["chat = QianfanChatEndpoint(streaming=True)\n", "messages = [HumanMessage(content=\"Hello\")]\n", "chat.invoke(messages)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='您好！有什么我可以帮助您的吗？')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["await chat.ainvoke(messages)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content='您好！有什么我可以帮助您的吗？')]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.batch([messages])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Streaming"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["您好！有什么我可以帮助您的吗？\n"]}], "source": ["try:\n", "    for chunk in chat.stream(messages):\n", "        print(chunk.content, end=\"\", flush=True)\n", "except TypeError as e:\n", "    print(\"\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Use different models in Qianfan\n", "\n", "The default model is ERNIE-Bot-turbo, in the case you want to deploy your own model based on Ernie <PERSON> or third-party open-source model, you could follow these steps:\n", "\n", "1. (Optional, if the model are included in the default models, skip it) Deploy your model in Qianfan Console, get your own customized deploy endpoint.\n", "2. Set up the field called `endpoint` in the initialization:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello，可以回答问题了，我会竭尽全力为您解答，请问有什么问题吗？')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["chatBot = QianfanChatEndpoint(\n", "    streaming=True,\n", "    model=\"ERNIE-Bot\",\n", ")\n", "\n", "messages = [HumanMessage(content=\"Hello\")]\n", "chatBot.invoke(messages)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Model Params:\n", "\n", "For now, only `ERNIE-Bot` and `ERNIE-Bot-turbo` support model params below, we might support more models in the future.\n", "\n", "- temperature\n", "- top_p\n", "- penalty_score\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='您好！有什么我可以帮助您的吗？')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.invoke(\n", "    [HumanMessage(content=\"Hello\")],\n", "    **{\"top_p\": 0.4, \"temperature\": 0.1, \"penalty_score\": 1},\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "vscode": {"interpreter": {"hash": "58f7cb64c3a06383b7f18d2a11305edccbad427293a2b4afa7abe8bfc810d4bb"}}}, "nbformat": 4, "nbformat_minor": 2}