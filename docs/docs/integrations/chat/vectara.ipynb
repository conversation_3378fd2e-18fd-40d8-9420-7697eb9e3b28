{"cells": [{"cell_type": "markdown", "id": "134a0785", "metadata": {}, "source": ["[Vectara](https://vectara.com/) is the trusted AI Assistant and Agent platform, which focuses on enterprise readiness for mission-critical applications.\n", "Vectara serverless RAG-as-a-service provides all the components of RAG behind an easy-to-use API, including:\n", "1. A way to extract text from files (PDF, PPT, DOCX, etc)\n", "2. ML-based chunking that provides state-of-the-art performance.\n", "3. The [Boomerang](https://vectara.com/how-boomerang-takes-retrieval-augmented-generation-to-the-next-level-via-grounded-generation/) embeddings model.\n", "4. Its own internal vector database where text chunks and embedding vectors are stored.\n", "5. A query service that automatically encodes the query into embedding, and retrieves the most relevant text segments, including support for [Hybrid Search](https://docs.vectara.com/docs/api-reference/search-apis/lexical-matching) as well as multiple reranking options such as the [multi-lingual relevance reranker](https://www.vectara.com/blog/deep-dive-into-vectara-multilingual-reranker-v1-state-of-the-art-reranker-across-100-languages), [MMR](https://vectara.com/get-diverse-results-and-comprehensive-summaries-with-vectaras-mmr-reranker/), [UDF reranker](https://www.vectara.com/blog/rag-with-user-defined-functions-based-reranking). \n", "6. An LLM for creating a [generative summary](https://docs.vectara.com/docs/learn/grounded-generation/grounded-generation-overview), based on the retrieved documents (context), including citations.\n", "\n", "For more information:\n", "- [Documentation](https://docs.vectara.com/docs/)\n", "- [API Playground](https://docs.vectara.com/docs/rest-api/)\n", "- [Quickstart](https://docs.vectara.com/docs/quickstart)\n", "\n", "\n", "This notebook shows how to use Vectara's [Chat](https://docs.vectara.com/docs/api-reference/chat-apis/chat-apis-overview) functionality, which provides automatic storage of conversation history and ensures follow up questions consider that history.\n", "\n", "### Setup\n", "\n", "To use the `VectaraVectorStore`, you first need to install the partner package.\n"]}, {"cell_type": "code", "execution_count": null, "id": "b4a2f525-4805-4880-8bfa-18fe6f1cd1c7", "metadata": {}, "outputs": [], "source": ["!uv pip install -U pip && uv pip install -qU langchain-vectara"]}, {"cell_type": "markdown", "id": "56372c5b", "metadata": {}, "source": ["## Getting Started\n", "\n", "To get started, use the following steps:\n", "1. If you don't already have one, [Sign up](https://www.vectara.com/integrations/langchain) for your free Vectara trial.\n", "2. Within your account, you can create one or more corpora. Each corpus represents an area that stores text data upon ingestion from input documents. To create a corpus, use the **\"Create Corpus\"** button. You then provide a name to your corpus as well as a description. Optionally, you can define filtering attributes and apply some advanced options. If you click on your created corpus, you can see its name and corpus ID right on the top.\n", "3. Next, you'll need to create API keys to access the corpus. Click on the **\"Access Control\"** tab in the corpus view and then the **\"Create API Key\"** button. Give your key a name, and choose whether you want query-only or query+index for your key. Click \"Create\", and you now have an active API key. Keep this key confidential. \n", "\n", "To use Lang<PERSON>hai<PERSON> with Vectara, you'll need to have these two values: `corpus_key` and `api_key`.\n", "You can provide `VECTARA_API_KEY` to LangChain in two ways:\n", "\n", "## Instantiation\n", "\n", "1. Include in your environment these two variables: `VECTARA_API_KEY`.\n", "\n", "   For example, you can set these variables using os.environ and getpass as follows:\n", "\n", "```python\n", "import os\n", "import getpass\n", "\n", "os.environ[\"VECTARA_API_KEY\"] = getpass.getpass(\"Vectara API Key:\")\n", "```\n", "\n", "2. Add them to the `Vectara` vectorstore constructor:\n", "\n", "```python\n", "vectara = Vectara(\n", "    vectara_api_key=vectara_api_key\n", ")\n", "```\n", "\n", "In this notebook, we assume they are provided in the environment."]}, {"cell_type": "code", "execution_count": 2, "id": "70c4e529", "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"VECTARA_API_KEY\"] = \"<VECTARA_API_KEY>\"\n", "os.environ[\"VECTARA_CORPUS_KEY\"] = \"<VECTARA_CORPUS_KEY>\"\n", "\n", "from langchain_vectara import Vectara\n", "from langchain_vectara.vectorstores import (\n", "    CorpusConfig,\n", "    GenerationConfig,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    SearchConfig,\n", "    VectaraQuery<PERSON>on<PERSON>g,\n", ")"]}, {"cell_type": "markdown", "id": "cdff94be", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> Explained\n", "\n", "In most uses of <PERSON><PERSON><PERSON><PERSON> to create chatbots, one must integrate a special `memory` component that maintains the history of chat sessions and then uses that history to ensure the chatbot is aware of conversation history.\n", "\n", "With Vectara Chat, all of that is performed in the backend by Vectara automatically. You can look at the [Chat](https://docs.vectara.com/docs/api-reference/chat-apis/chat-apis-overview) documentation for the details, to learn more about the internals of how this is implemented, but with <PERSON><PERSON><PERSON><PERSON>, all you have to do is turn that feature on in the Vectara vectorstore.\n", "\n", "Let's see an example. First, we load the SOTU document (remember, text extraction and chunking all occur automatically on the Vectara platform):"]}, {"cell_type": "code", "execution_count": 3, "id": "01c46e92", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_community.document_loaders import TextLoader\n", "\n", "loader = TextLoader(\"../document_loaders/example_data/state_of_the_union.txt\")\n", "documents = loader.load()\n", "\n", "corpus_key = os.getenv(\"VECTARA_CORPUS_KEY\")\n", "vectara = Vectara.from_documents(documents, embedding=None, corpus_key=corpus_key)"]}, {"cell_type": "markdown", "id": "3c96b118", "metadata": {}, "source": ["And now we create a Chat Runnable using the `as_chat` method:"]}, {"cell_type": "code", "execution_count": 4, "id": "1b41a10b-bf68-4689-8f00-9aed7675e2ab", "metadata": {"tags": []}, "outputs": [], "source": ["generation_config = GenerationConfig(\n", "    max_used_search_results=7,\n", "    response_language=\"eng\",\n", "    generation_preset_name=\"vectara-summary-ext-24-05-med-omni\",\n", "    enable_factual_consistency_score=True,\n", ")\n", "search_config = SearchConfig(\n", "    corpora=[CorpusConfig(corpus_key=corpus_key, limit=25)],\n", "    reranker=MmrR<PERSON>ker(diversity_bias=0.2),\n", ")\n", "\n", "config = VectaraQueryConfig(\n", "    search=search_config,\n", "    generation=generation_config,\n", ")\n", "\n", "\n", "bot = vectara.as_chat(config)"]}, {"cell_type": "markdown", "id": "83f38c18-ac82-45f4-a79e-8b37ce1ae115", "metadata": {}, "source": ["\n", "## Invocation\n", "\n", "Here's an example of asking a question with no chat history"]}, {"cell_type": "code", "execution_count": 5, "id": "bc672290-8a8b-4828-a90c-f1bbdd6b3920", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["'The president stated that nominating someone to serve on the United States Supreme Court is one of the most serious constitutional responsibilities. He nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>, describing her as one of the nation’s top legal minds who will continue <PERSON>’s legacy of excellence and noting her experience as a former top litigator in private practice [1].'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["bot.invoke(\"What did the president say about <PERSON><PERSON><PERSON>?\")[\"answer\"]"]}, {"cell_type": "markdown", "id": "8c26a83d-c945-4458-b54a-c6bd7f391303", "metadata": {}, "source": ["Here's an example of asking a question with some chat history"]}, {"cell_type": "code", "execution_count": 6, "id": "9c95460b-7116-4155-a9d2-c0fb027ee592", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["'Yes, the president mentioned that <PERSON><PERSON><PERSON> succeeded <PERSON> [1].'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["bot.invoke(\"Did he mention who she suceeded?\")[\"answer\"]"]}, {"cell_type": "markdown", "id": "2324cdc6-98bf-4708-b8cd-02a98b1e5b67", "metadata": {}, "source": ["## Chat with streaming\n", "\n", "Of course, the chatbot interface also supports streaming.\n", "Instead of the `invoke` method, you simply use `stream`:"]}, {"cell_type": "code", "execution_count": 7, "id": "936dc62f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The president acknowledged the significant impact of COVID-19 on the nation, expressing understanding of the public's fatigue and frustration. He emphasized the need to view COVID-19 not as a partisan issue but as a serious disease, urging unity among Americans. The president highlighted the progress made, noting that severe cases have decreased significantly, and mentioned new CDC guidelines allowing most Americans to be mask-free. He also pointed out the efforts to vaccinate the nation and provide economic relief, and the ongoing commitment to vaccinate the world [2], [3], [5]."]}], "source": ["output = {}\n", "curr_key = None\n", "for chunk in bot.stream(\"what did he said about the covid?\"):\n", "    for key in chunk:\n", "        if key not in output:\n", "            output[key] = chunk[key]\n", "        else:\n", "            output[key] += chunk[key]\n", "        if key == \"answer\":\n", "            print(chunk[key], end=\"\", flush=True)\n", "        curr_key = key"]}, {"cell_type": "markdown", "id": "cefdf72b1d90085a", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Chaining\n", "\n", "For additional capabilities, you can use chaining."]}, {"cell_type": "code", "execution_count": 33, "id": "167bc806-395e-46bf-80cc-3c5d43164f42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["So, the president talked about how the COVID-19 sickness has affected a lot of people in the country. He said that it's important for everyone to work together to fight the sickness, no matter what political party they are in. The president also mentioned that they are working hard to give vaccines to people to help protect them from getting sick. They are also giving money and help to people who need it, like food, housing, and cheaper health insurance. The president also said that they are sending vaccines to many other countries to help people all around the world stay healthy.\n"]}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai.chat_models import ChatOpenAI\n", "\n", "llm = ChatOpenAI(temperature=0)\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that explains the stuff to a five year old.  <PERSON><PERSON><PERSON> is providing the answer.\",\n", "        ),\n", "        (\"human\", \"{vectara_response}\"),\n", "    ]\n", ")\n", "\n", "\n", "def get_vectara_response(question: dict) -> str:\n", "    \"\"\"\n", "    Calls <PERSON><PERSON>tara as_chat and returns the answer string.  This encapsulates\n", "    the Vectara call.\n", "    \"\"\"\n", "    try:\n", "        response = bot.invoke(question[\"question\"])\n", "        return response[\"answer\"]\n", "    except Exception as e:\n", "        return \"I'm sorry, I couldn't get an answer from <PERSON><PERSON><PERSON>.\"\n", "\n", "\n", "# Create the chain\n", "chain = get_vectara_response | prompt | llm | StrOutputParser()\n", "\n", "\n", "# Invoke the chain\n", "result = chain.invoke({\"question\": \"what did he say about the covid?\"})\n", "print(result)"]}, {"cell_type": "markdown", "id": "3b8bb761-db4a-436c-8939-41e9f8652083", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## API reference\n", "\n", "You can look at the [Chat](https://docs.vectara.com/docs/api-reference/chat-apis/chat-apis-overview) documentation for the details."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}