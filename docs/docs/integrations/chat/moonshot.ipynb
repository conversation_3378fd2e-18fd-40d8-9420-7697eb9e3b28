{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: Moonshot\n", "---"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["# MoonshotChat\n", "\n", "[Moonshot](https://platform.moonshot.cn/) is a Chinese startup that provides LLM service for companies and individuals.\n", "\n", "This example goes over how to use <PERSON><PERSON><PERSON><PERSON> to interact with Moonshot Inference for Chat."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "# Generate your api key from: https://platform.moonshot.cn/console/api-keys\n", "os.environ[\"MOONSHOT_API_KEY\"] = \"MOONSHOT_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models.moonshot import MoonshotChat\n", "from langchain_core.messages import HumanMessage, SystemMessage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat = MoonshotChat()\n", "# or use a specific model\n", "# Available models: https://platform.moonshot.cn/docs\n", "# chat = MoonshotChat(model=\"moonshot-v1-128k\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages = [\n", "    SystemMessage(\n", "        content=\"You are a helpful assistant that translates English to French.\"\n", "    ),\n", "    HumanMessage(\n", "        content=\"Translate this sentence from English to French. I love programming.\"\n", "    ),\n", "]\n", "\n", "chat.invoke(messages)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}