{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {}, "source": ["---\n", "sidebar_label: Cerebras\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatCerebras\n", "\n", "This notebook provides a quick overview for getting started with Cerebras [chat models](/docs/concepts/chat_models). For detailed documentation of all ChatCerebras features and configurations head to the [API reference](https://python.langchain.com/api_reference/cerebras/chat_models/langchain_cerebras.chat_models.ChatCerebras.html#).\n", "\n", "At Cerebras, we've developed the world's largest and fastest AI processor, the Wafer-Scale Engine-3 (WSE-3). The Cerebras CS-3 system, powered by the WSE-3, represents a new class of AI supercomputer that sets the standard for generative AI training and inference with unparalleled performance and scalability.\n", "\n", "With <PERSON><PERSON><PERSON><PERSON> as your inference provider, you can:\n", "- Achieve unprecedented speed for AI inference workloads\n", "- Build commercially with high throughput\n", "- Effortlessly scale your AI workloads with our seamless clustering technology\n", "\n", "Our CS-3 systems can be quickly and easily clustered to create the largest AI supercomputers in the world, making it simple to place and run the largest models. Leading corporations, research institutions, and governments are already using Cerebras solutions to develop proprietary models and train popular open-source models.\n", "\n", "Want to experience the power of Cerebras? Check out our [website](https://cerebras.ai) for more resources and explore options for accessing our technology through the Cerebras Cloud or on-premise deployments!\n", "\n", "For more information about Cerebras Cloud, visit [cloud.cerebras.ai](https://cloud.cerebras.ai/). Our API reference is available at [inference-docs.cerebras.ai](https://inference-docs.cerebras.ai/).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/chat/cerebras) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatCerebras](https://python.langchain.com/api_reference/cerebras/chat_models/langchain_cerebras.chat_models.ChatCerebras.html#) | [langchain-cerebras](https://python.langchain.com/api_reference/cerebras/index.html) | ❌ | beta | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-cerebras?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-cerebras?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling/) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ✅ | ❌ | ❌ | ❌ | ✅ | ✅  | ✅ | ❌ |\n", "\n", "## Setup\n", "\n", "```bash\n", "pip install langchain-cerebras\n", "```\n", "\n", "### Credentials\n", "\n", "Get an API Key from [cloud.cerebras.ai](https://cloud.cerebras.ai/) and add it to your environment variables:\n", "```\n", "export CEREBRAS_API_KEY=\"your-api-key-here\"\n", "```"]}, {"cell_type": "code", "execution_count": 8, "id": "ce19c2d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your Cerebras API key:  ········\n"]}], "source": ["import getpass\n", "import os\n", "\n", "if \"CEREBRAS_API_KEY\" not in os.environ:\n", "    os.environ[\"CEREBRAS_API_KEY\"] = getpass.getpass(\"Enter your Cerebras API key: \")"]}, {"cell_type": "markdown", "id": "72ee0c4b-9764-423a-9dbf-95129e185210", "metadata": {}, "source": "To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:"}, {"cell_type": "code", "execution_count": 9, "id": "a15d341e-3e26-4ca3-830b-5aab30ed66de", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")\n", "# os.environ[\"LANGSMITH_TRACING\"] = \"true\""]}, {"cell_type": "markdown", "id": "0730d6a1-c893-4840-9817-5e5251676d5d", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain Cerebras integration lives in the `langchain-cerebras` package:"]}, {"cell_type": "code", "execution_count": null, "id": "652d6238-1f87-422a-b135-f5abbb8652fc", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-cerebras"]}, {"cell_type": "markdown", "id": "ea69675d", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:\n"]}, {"cell_type": "code", "execution_count": null, "id": "21155898", "metadata": {}, "outputs": [], "source": ["from langchain_cerebras import ChatCerebras\n", "\n", "llm = ChatCerebras(\n", "    model=\"llama-3.3-70b\",\n", "    # other params...\n", ")"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 13, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Je adore le programmation.', response_metadata={'token_usage': {'completion_tokens': 7, 'prompt_tokens': 35, 'total_tokens': 42}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_be27ec77ff', 'finish_reason': 'stop'}, id='run-e5d66faf-019c-4ac6-9265-71093b13202d-0', usage_metadata={'input_tokens': 35, 'output_tokens': 7, 'total_tokens': 42})"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 14, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Ich liebe Programmieren!\\n\\n(Literally: I love programming!)', response_metadata={'token_usage': {'completion_tokens': 14, 'prompt_tokens': 30, 'total_tokens': 44}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_be27ec77ff', 'finish_reason': 'stop'}, id='run-e1d2ebb8-76d1-471b-9368-3b68d431f16a-0', usage_metadata={'input_tokens': 30, 'output_tokens': 14, 'total_tokens': 44})"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_cerebras import ChatCerebras\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "llm = ChatCerebras(\n", "    model=\"llama-3.3-70b\",\n", "    # other params...\n", ")\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "0ec73a0e", "metadata": {}, "source": ["## Streaming"]}, {"cell_type": "code", "execution_count": 15, "id": "46fd21a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OH BOY! Let me tell you all about LIONS!\n", "\n", "Lions are the kings of the jungle! They're really big and have beautiful, fluffy manes around their necks. The mane is like a big, golden crown!\n", "\n", "Lions live in groups called prides. A pride is like a big family, and the lionesses (that's what we call the female lions) take care of the babies. The lionesses are like the mommies, and they teach the babies how to hunt and play.\n", "\n", "Lions are very good at hunting. They work together to catch their food, like zebras and antelopes. They're super fast and can run really, really fast!\n", "\n", "But lions are also very sleepy. They like to take long naps in the sun, and they can sleep for up to 20 hours a day! Can you imagine sleeping that much?\n", "\n", "Lions are also very loud. They roar really loudly to talk to each other. It's like they're saying, \"ROA<PERSON>! I'm the king of the jungle!\"\n", "\n", "And guess what? Lions are very social. They like to play and cuddle with each other. They're like big, furry teddy bears!\n", "\n", "So, that's lions! Aren't they just the coolest?"]}], "source": ["from langchain_cerebras import ChatCerebras\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "llm = ChatCerebras(\n", "    model=\"llama-3.3-70b\",\n", "    # other params...\n", ")\n", "\n", "system = \"You are an expert on animals who must answer questions in a manner that a 5 year old can understand.\"\n", "human = \"I want to learn more about this animal: {animal}\"\n", "prompt = ChatPromptTemplate.from_messages([(\"system\", system), (\"human\", human)])\n", "\n", "chain = prompt | llm\n", "\n", "for chunk in chain.stream({\"animal\": \"Lion\"}):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "markdown", "id": "f67b6132", "metadata": {}, "source": ["## Async"]}, {"cell_type": "code", "execution_count": 19, "id": "a3a45baf", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Ice', response_metadata={'token_usage': {'completion_tokens': 2, 'prompt_tokens': 36, 'total_tokens': 38}, 'model_name': 'llama3-8b-8192', 'system_fingerprint': 'fp_be27ec77ff', 'finish_reason': 'stop'}, id='run-7434bdde-1bec-44cf-827b-8d978071dfe8-0', usage_metadata={'input_tokens': 36, 'output_tokens': 2, 'total_tokens': 38})"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_cerebras import ChatCerebras\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "llm = ChatCerebras(\n", "    model=\"llama-3.3-70b\",\n", "    # other params...\n", ")\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"human\",\n", "            \"Let's play a game of opposites. What's the opposite of {topic}? Just give me the answer with no extra input.\",\n", "        )\n", "    ]\n", ")\n", "chain = prompt | llm\n", "await chain.ainvoke({\"topic\": \"fire\"})"]}, {"cell_type": "markdown", "id": "4f9d9945", "metadata": {}, "source": ["## Async Streaming"]}, {"cell_type": "code", "execution_count": 27, "id": "c7448e0f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["In the distant reaches of the cosmos, there existed a peculiar phenomenon known as the \"Eclipse of Eternity,\" a swirling vortex of darkness that had been shrouded in mystery for eons. It was said that this blackhole, born from the cataclysmic collision of two ancient stars, had been slowly devouring the fabric of space-time itself, warping the very essence of reality. As the celestial bodies of the galaxy danced around it, they began to notice a strange, almost imperceptible distortion in the fabric of space, as if the blackhole's gravitational pull was exerting an influence on the very course of events itself.\n", "\n", "As the centuries passed, astronomers from across the galaxy became increasingly fascinated by the Eclipse of Eternity, pouring over ancient texts and scouring the cosmos for any hint of its secrets. One such scholar, a brilliant and reclusive astrophysicist named Dr. <PERSON>, became obsessed with unraveling the mysteries of the blackhole. She spent years pouring over ancient texts, deciphering cryptic messages and hidden codes that hinted at the existence of a long-lost civilization that had once thrived in the heart of the blackhole itself. According to legend, this ancient civilization had possessed knowledge of the cosmos that was beyond human comprehension, and had used their mastery of the universe to create the Eclipse of Eternity as a gateway to other dimensions.\n", "\n", "As Dr. <PERSON><PERSON> delved deeper into her research, she began to experience strange and vivid dreams, visions that seemed to transport her to the very heart of the blackhole itself. In these dreams, she saw ancient beings, their faces twisted in agony as they were consumed by the void. She saw stars and galaxies, their light warped and distorted by the blackhole's gravitational pull. And she saw the Eclipse of Eternity itself, its swirling vortex of darkness pulsing with an otherworldly energy that seemed to be calling to her. As the dreams grew more vivid and more frequent, Dr<PERSON> <PERSON><PERSON> became convinced that she was being drawn into the heart of the blackhole, and that the secrets of the universe lay waiting for her on the other side."]}], "source": ["from langchain_cerebras import ChatCerebras\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "llm = ChatCerebras(\n", "    model=\"llama-3.3-70b\",\n", "    # other params...\n", ")\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"human\",\n", "            \"Write a long convoluted story about {subject}. I want {num_paragraphs} paragraphs.\",\n", "        )\n", "    ]\n", ")\n", "chain = prompt | llm\n", "\n", "async for chunk in chain.astream({\"num_paragraphs\": 3, \"subject\": \"blackholes\"}):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatCerebras features and configurations head to the API reference: https://python.langchain.com/api_reference/cerebras/chat_models/langchain_cerebras.chat_models.ChatCerebras.html#"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}}, "nbformat": 4, "nbformat_minor": 5}