{"cells": [{"cell_type": "raw", "id": "eb65deaa", "metadata": {}, "source": ["---\n", "sidebar_label: vLLM Chat\n", "---"]}, {"cell_type": "markdown", "id": "8f82e243-f4ee-44e2-b417-099b6401ae3e", "metadata": {}, "source": ["# vLLM Chat\n", "\n", "vLLM can be deployed as a server that mimics the OpenAI API protocol. This allows vLLM to be used as a drop-in replacement for applications using OpenAI API. This server can be queried in the same format as OpenAI API.\n", "\n", "## Overview\n", "This will help you get started with vLLM [chat models](/docs/concepts/chat_models), which leverages the `langchain-openai` package. For detailed documentation of all `ChatOpenAI` features and configurations head to the [API reference](https://python.langchain.com/api_reference/openai/chat_models/langchain_openai.chat_models.base.ChatOpenAI.html).\n", "\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | JS support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatOpenAI](https://python.langchain.com/api_reference/openai/chat_models/langchain_openai.chat_models.base.ChatOpenAI.html) | [langchain_openai](https://python.langchain.com/api_reference/openai/) | ✅ | beta | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain_openai?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain_openai?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "Specific model features, such as tool calling, support for multi-modal inputs, support for token-level streaming, etc., will depend on the hosted model.\n", "\n", "## Setup\n", "\n", "See the vLLM docs [here](https://docs.vllm.ai/en/latest/).\n", "\n", "To access vLLM models through LangChain, you'll need to install the `langchain-openai` integration package.\n", "\n", "### Credentials\n", "\n", "Authentication will depend on specifics of the inference server."]}, {"cell_type": "markdown", "id": "c3b1707a-cf2c-4367-94e3-436c43402503", "metadata": {}, "source": "To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:"}, {"cell_type": "code", "execution_count": null, "id": "1e40bd5e-cbaa-41ef-aaf9-0858eb207184", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")"]}, {"cell_type": "markdown", "id": "0739b647-609b-46d3-bdd3-e86fe4463288", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain vLLM integration can be accessed via the `langchain-openai` package:"]}, {"cell_type": "code", "execution_count": null, "id": "7afcfbdc-56aa-4529-825a-8acbe7aa5241", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-openai"]}, {"cell_type": "markdown", "id": "2cf576d6-7b67-4937-bf99-39071e85720c", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "060a2e3d-d42f-4221-bd09-a9a06544dcd3", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage, SystemMessage\n", "from langchain_core.prompts.chat import (\n", "    ChatPromptTemplate,\n", "    HumanMessagePromptTemplate,\n", "    SystemMessagePromptTemplate,\n", ")\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "code", "execution_count": 14, "id": "bf24d732-68a9-44fd-b05d-4903ce5620c6", "metadata": {"tags": []}, "outputs": [], "source": ["inference_server_url = \"http://localhost:8000/v1\"\n", "\n", "llm = ChatOpenAI(\n", "    model=\"mosaicml/mpt-7b\",\n", "    openai_api_key=\"EMPTY\",\n", "    openai_api_base=inference_server_url,\n", "    max_tokens=5,\n", "    temperature=0,\n", ")"]}, {"cell_type": "markdown", "id": "34b18328-5e8b-4ff2-9b89-6fbb76b5c7f0", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 15, "id": "aea4e363-5688-4b07-82ed-6aa8153c2377", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content=' Io amo programmare', additional_kwargs={}, example=False)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    SystemMessage(\n", "        content=\"You are a helpful assistant that translates English to Italian.\"\n", "    ),\n", "    HumanMessage(\n", "        content=\"Translate the following sentence from English to Italian: I love programming.\"\n", "    ),\n", "]\n", "llm.invoke(messages)"]}, {"cell_type": "markdown", "id": "a580a1e4-11a3-4277-bfba-bfb414ac7201", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": null, "id": "dd0f4043-48bd-4245-8bdb-e7669666a277", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "265f5d51-0a76-4808-8d13-ef598ee6e366", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all features and configurations exposed via `langchain-openai`, head to the API reference: https://python.langchain.com/api_reference/openai/chat_models/langchain_openai.chat_models.base.ChatOpenAI.html\n", "\n", "Refer to the vLLM [documentation](https://docs.vllm.ai/en/latest/) as well."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}