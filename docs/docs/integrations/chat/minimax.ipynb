{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: MiniMax\n", "---"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# MiniMaxChat\n", "\n", "[Minimax](https://api.minimax.chat) is a Chinese startup that provides LLM service for companies and individuals.\n", "\n", "This example goes over how to use LangChain to interact with MiniMax Inference for Chat."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"MINIMAX_GROUP_ID\"] = \"MINIMAX_GROUP_ID\"\n", "os.environ[\"MINIMAX_API_KEY\"] = \"MINIMAX_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models import MiniMaxChat\n", "from langchain_core.messages import HumanMessage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat = MiniMaxChat()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat(\n", "    [\n", "        HumanMessage(\n", "            content=\"Translate this sentence from English to French. I love programming.\"\n", "        )\n", "    ]\n", ")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}