{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {}, "source": ["---\n", "sidebar_label: CloudflareWorkersAI\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatCloudflareWorkersAI\n", "\n", "\n", "This will help you get started with CloudflareWorkersAI [chat models](/docs/concepts/chat_models). For detailed documentation of all ChatCloudflareWorkersAI features and configurations head to the [API reference](https://python.langchain.com/docs/integrations/chat/cloudflare_workersai/).\n", "\n", "\n", "## Overview\n", "### Integration details\n", "\n", "\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/chat/cloudflare) | Package downloads | Package latest |\n", "| :--- | :--- |:-----:|:------------:|:------------------------------------------------------------------------:| :---: | :---: |\n", "| [ChatCloudflareWorkersAI](https://python.langchain.com/docs/integrations/chat/cloudflare_workersai/) | [langchain-cloudflare](https://pypi.org/project/langchain-cloudflare/) |   ✅   |      ❌       |                                     ❌                                     | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-cloudflare?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-cloudflare?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "|:-----------------------------------------:|:----------------------------------------------------:|:---------:|:----------------------------------------------:|:-----------:|:-----------:|:-----------------------------------------------------:|:------------:|:------------------------------------------------------:|:----------------------------------:|\n", "|                     ✅                     |                          ✅                           |     ✅     |                       ❌                        |      ❌      |      ❌      |                           ❌                           |      ❌       |                             ✅                            |                 ❌                  | \n", "\n", "## Setup\n", "\n", "To access CloudflareWorkersAI models you'll need to create a/an CloudflareWorkersAI account, get an API key, and install the `langchain-cloudflare` integration package.\n", "\n", "### Credentials\n", "\n", "\n", "Head to https://www.cloudflare.com/developer-platform/products/workers-ai/ to sign up to CloudflareWorkersAI and generate an API key. Once you've done this set the CF_AI_API_KEY environment variable and the CF_ACCOUNT_ID environment variable:"]}, {"cell_type": "code", "execution_count": null, "id": "433e8d2b-9519-4b49-b2c4-7ab65b046c94", "metadata": {"is_executing": true}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.getenv(\"CF_AI_API_KEY\"):\n", "    os.environ[\"CF_AI_API_KEY\"] = getpass.getpass(\n", "        \"Enter your CloudflareWorkersAI API key: \"\n", "    )\n", "\n", "if not os.getenv(\"CF_ACCOUNT_ID\"):\n", "    os.environ[\"CF_ACCOUNT_ID\"] = getpass.getpass(\n", "        \"Enter your CloudflareWorkersAI account ID: \"\n", "    )"]}, {"cell_type": "markdown", "id": "72ee0c4b-9764-423a-9dbf-95129e185210", "metadata": {}, "source": ["If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:"]}, {"cell_type": "code", "execution_count": null, "id": "a15d341e-3e26-4ca3-830b-5aab30ed66de", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")"]}, {"cell_type": "markdown", "id": "0730d6a1-c893-4840-9817-5e5251676d5d", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain CloudflareWorkersAI integration lives in the `langchain-cloudflare` package:"]}, {"cell_type": "code", "execution_count": null, "id": "652d6238-1f87-422a-b135-f5abbb8652fc", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-cloudflare"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:\n", "\n", "- Update model instantiation with relevant params."]}, {"cell_type": "code", "execution_count": 35, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {"ExecuteTime": {"end_time": "2025-04-07T17:48:31.193773Z", "start_time": "2025-04-07T17:48:31.179196Z"}}, "outputs": [], "source": ["from langchain_cloudflare.chat_models import ChatCloudflareWorkersAI\n", "\n", "llm = ChatCloudflareWorkersAI(\n", "    model=\"@cf/meta/llama-3.3-70b-instruct-fp8-fast\",\n", "    temperature=0,\n", "    max_tokens=1024,\n", "    # other params...\n", ")"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation\n"]}, {"cell_type": "code", "execution_count": 19, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"J'adore la programmation.\", additional_kwargs={}, response_metadata={'token_usage': {'prompt_tokens': 37, 'completion_tokens': 9, 'total_tokens': 46}, 'model_name': '@cf/meta/llama-3.3-70b-instruct-fp8-fast'}, id='run-995d1970-b6be-49f3-99ae-af4cdba02304-0', usage_metadata={'input_tokens': 37, 'output_tokens': 9, 'total_tokens': 46})"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 20, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore la programmation.\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:\n"]}, {"cell_type": "code", "execution_count": 21, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Ich liebe das Programmieren.', additional_kwargs={}, response_metadata={'token_usage': {'prompt_tokens': 32, 'completion_tokens': 7, 'total_tokens': 39}, 'model_name': '@cf/meta/llama-3.3-70b-instruct-fp8-fast'}, id='run-d1b677bc-194e-4473-90f1-aa65e8e46d50-0', usage_metadata={'input_tokens': 32, 'output_tokens': 7, 'total_tokens': 39})"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "d1ee55bc-ffc8-4cfa-801c-993953a08cfd", "metadata": {}, "source": ["## Structured Outputs"]}, {"cell_type": "code", "execution_count": 22, "id": "91cae406-14d7-46c9-b942-2d1476588423", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'setup': 'Why did the cat join a band?',\n", " 'punchline': 'Because it wanted to be the purr-cussionist',\n", " 'rating': '8'}"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["json_schema = {\n", "    \"title\": \"joke\",\n", "    \"description\": \"Joke to tell user.\",\n", "    \"type\": \"object\",\n", "    \"properties\": {\n", "        \"setup\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"The setup of the joke\",\n", "        },\n", "        \"punchline\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"The punchline to the joke\",\n", "        },\n", "        \"rating\": {\n", "            \"type\": \"integer\",\n", "            \"description\": \"How funny the joke is, from 1 to 10\",\n", "            \"default\": None,\n", "        },\n", "    },\n", "    \"required\": [\"setup\", \"punchline\"],\n", "}\n", "structured_llm = llm.with_structured_output(json_schema)\n", "\n", "structured_llm.invoke(\"Tell me a joke about cats\")"]}, {"cell_type": "markdown", "id": "dbfc0c43-e76b-446e-bbb1-d351640bb7be", "metadata": {}, "source": ["## Bind tools"]}, {"cell_type": "code", "execution_count": 36, "id": "0765265e-4d00-4030-bf48-7e8d8c9af2ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'validate_user',\n", "  'args': {'user_id': '123',\n", "   'addresses': '[\"123 Fake St in Boston MA\", \"234 Pretend Boulevard in Houston TX\"]'},\n", "  'id': '31ec7d6a-9ce5-471b-be64-8ea0492d1387',\n", "  'type': 'tool_call'}]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import List\n", "\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def validate_user(user_id: int, addresses: List[str]) -> bool:\n", "    \"\"\"Validate user using historical addresses.\n", "\n", "    Args:\n", "        user_id (int): the user ID.\n", "        addresses (List[str]): Previous addresses as a list of strings.\n", "    \"\"\"\n", "    return True\n", "\n", "\n", "llm_with_tools = llm.bind_tools([validate_user])\n", "\n", "result = llm_with_tools.invoke(\n", "    \"Could you validate user 123? They previously lived at \"\n", "    \"123 Fake St in Boston MA and 234 Pretend Boulevard in \"\n", "    \"Houston TX.\"\n", ")\n", "result.tool_calls"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "https://developers.cloudflare.com/workers-ai/\n", "https://developers.cloudflare.com/agents/"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}