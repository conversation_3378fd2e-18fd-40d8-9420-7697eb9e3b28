{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: Azure ML Endpoint\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# AzureMLChatOnlineEndpoint\n", "\n", ">[Azure Machine Learning](https://azure.microsoft.com/en-us/products/machine-learning/) is a platform used to build, train, and deploy machine learning models. Users can explore the types of models to deploy in the Model Catalog, which provides foundational and general purpose models from different providers.\n", ">\n", ">In general, you need to deploy models in order to consume its predictions (inference). In `Azure Machine Learning`, [Online Endpoints](https://learn.microsoft.com/en-us/azure/machine-learning/concept-endpoints) are used to deploy these models with a real-time serving. They are based on the ideas of `Endpoints` and `Deployments` which allow you to decouple the interface of your production workload from the implementation that serves it.\n", "\n", "This notebook goes over how to use a chat model hosted on an `Azure Machine Learning Endpoint`."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models.azureml_endpoint import AzureMLChatOnlineEndpoint"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set up\n", "\n", "You must [deploy a model on Azure ML](https://learn.microsoft.com/en-us/azure/machine-learning/how-to-use-foundation-models?view=azureml-api-2#deploying-foundation-models-to-endpoints-for-inferencing) or [to Azure AI studio](https://learn.microsoft.com/en-us/azure/ai-studio/how-to/deploy-models-open) and obtain the following parameters:\n", "\n", "* `endpoint_url`: The REST endpoint url provided by the endpoint.\n", "* `endpoint_api_type`: Use `endpoint_type='dedicated'` when deploying models to **Dedicated endpoints** (hosted managed infrastructure). Use `endpoint_type='serverless'` when deploying models using the **Pay-as-you-go** offering (model as a service).\n", "* `endpoint_api_key`: The API key provided by the endpoint"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Content Formatter\n", "\n", "The `content_formatter` parameter is a handler class for transforming the request and response of an AzureML endpoint to match with required schema. Since there are a wide range of models in the model catalog, each of which may process data differently from one another, a `ContentFormatterBase` class is provided to allow users to transform data to their liking. The following content formatters are provided:\n", "\n", "* `CustomOpenAIChatContentFormatter`: Formats request and response data for models like LLaMa2-chat that follow the OpenAI API spec for request and response.\n", "\n", "*Note: `langchain.chat_models.azureml_endpoint.LlamaChatContentFormatter` is being deprecated and replaced with `langchain.chat_models.azureml_endpoint.CustomOpenAIChatContentFormatter`.*\n", "\n", "You can implement custom content formatters specific for your model deriving from the class `langchain_community.llms.azureml_endpoint.ContentFormatterBase`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Examples\n", "\n", "The following section contains examples about how to use this class:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example: Chat completions with real-time endpoints"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='  The Collatz Conjecture is one of the most famous unsolved problems in mathematics, and it has been the subject of much study and research for many years. While it is impossible to predict with certainty whether the conjecture will ever be solved, there are several reasons why it is considered a challenging and important problem:\\n\\n1. Simple yet elusive: The Collatz Conjecture is a deceptively simple statement that has proven to be extraordinarily difficult to prove or disprove. Despite its simplicity, the conjecture has eluded some of the brightest minds in mathematics, and it remains one of the most famous open problems in the field.\\n2. Wide-ranging implications: The Collatz Conjecture has far-reaching implications for many areas of mathematics, including number theory, algebra, and analysis. A solution to the conjecture could have significant impacts on these fields and potentially lead to new insights and discoveries.\\n3. Computational evidence: While the conjecture remains unproven, extensive computational evidence supports its validity. In fact, no counterexample to the conjecture has been found for any starting value up to 2^64 (a number', additional_kwargs={}, example=False)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.chat_models.azureml_endpoint import (\n", "    AzureMLEndpointApiType,\n", "    CustomOpenAIChatContentFormatter,\n", ")\n", "from langchain_core.messages import HumanMessage\n", "\n", "chat = AzureMLChatOnlineEndpoint(\n", "    endpoint_url=\"https://<your-endpoint>.<your_region>.inference.ml.azure.com/score\",\n", "    endpoint_api_type=AzureMLEndpointApiType.dedicated,\n", "    endpoint_api_key=\"my-api-key\",\n", "    content_formatter=CustomOpenAIChatContentFormatter(),\n", ")\n", "response = chat.invoke(\n", "    [HumanMessage(content=\"Will the Collatz conjecture ever be solved?\")]\n", ")\n", "response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example: Chat completions with pay-as-you-go deployments (model as a service)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat = AzureMLChatOnlineEndpoint(\n", "    endpoint_url=\"https://<your-endpoint>.<your_region>.inference.ml.azure.com/v1/chat/completions\",\n", "    endpoint_api_type=AzureMLEndpointApiType.serverless,\n", "    endpoint_api_key=\"my-api-key\",\n", "    content_formatter=CustomOpenAIChatContentFormatter,\n", ")\n", "response = chat.invoke(\n", "    [HumanMessage(content=\"Will the Collatz conjecture ever be solved?\")]\n", ")\n", "response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you need to pass additional parameters to the model, use `model_kwargs` argument:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat = AzureMLChatOnlineEndpoint(\n", "    endpoint_url=\"https://<your-endpoint>.<your_region>.inference.ml.azure.com/v1/chat/completions\",\n", "    endpoint_api_type=AzureMLEndpointApiType.serverless,\n", "    endpoint_api_key=\"my-api-key\",\n", "    content_formatter=CustomOpenAIChatContentFormatter,\n", "    model_kwargs={\"temperature\": 0.8},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Parameters can also be passed during invocation:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = chat.invoke(\n", "    [HumanMessage(content=\"Will the Collatz conjecture ever be solved?\")],\n", "    max_tokens=512,\n", ")\n", "response"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 4}