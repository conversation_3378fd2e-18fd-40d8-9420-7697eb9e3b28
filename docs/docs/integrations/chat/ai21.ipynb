{"cells": [{"cell_type": "raw", "id": "4cebeec0", "metadata": {}, "source": ["---\n", "sidebar_label: AI21 Labs\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatAI21\n", "\n", "This notebook covers how to get started with AI21 chat models.\n", "Note that different chat models support different parameters. See the [AI21 documentation](https://docs.ai21.com/reference) to learn more about the parameters in your chosen model.\n", "[See all AI21's LangChain components.](https://pypi.org/project/langchain-ai21/)\n", "\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/chat/__package_name_short_snake__) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatAI21](https://python.langchain.com/api_reference/ai21/chat_models/langchain_ai21.chat_models.ChatAI21.html#langchain_ai21.chat_models.ChatAI21) | [langchain-ai21](https://python.langchain.com/api_reference/ai21/index.html) | ❌ | beta | ✅ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-ai21?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-ai21?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ | ❌ |\n", "\n", "\n", "## Setup"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["### Credentials\n", "\n", "We'll need to get an [AI21 API key](https://docs.ai21.com/) and set the `AI21_API_KEY` environment variable:\n"]}, {"cell_type": "code", "execution_count": null, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "from getpass import getpass\n", "\n", "if \"AI21_API_KEY\" not in os.environ:\n", "    os.environ[\"AI21_API_KEY\"] = getpass()"]}, {"cell_type": "markdown", "id": "f6844fff-3702-4489-ab74-732f69f3b9d7", "metadata": {}, "source": ["To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:"]}, {"cell_type": "code", "execution_count": null, "id": "7c2e19d3-7c58-4470-9e1a-718b27a32056", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")"]}, {"cell_type": "markdown", "id": "98e22f31-8acc-42d6-916d-415d1263c56e", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "markdown", "id": "f9699cd9-58f2-450e-aa64-799e66906c0f", "metadata": {}, "source": ["!pip install -qU langchain-ai21"]}, {"cell_type": "markdown", "id": "4828829d3da430ce", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": null, "id": "c40756fb-cbf8-4d44-a293-3989d707237e", "metadata": {}, "outputs": [], "source": ["from langchain_ai21 import ChatAI21\n", "\n", "llm = ChatAI21(model=\"jamba-instruct\", temperature=0)"]}, {"cell_type": "markdown", "id": "2bdc5d68-2a19-495e-8c04-d11adc86d3ae", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": null, "id": "46b982dc-5d8a-46da-a711-81c03ccd6adc", "metadata": {}, "outputs": [], "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "markdown", "id": "10a30f84-b531-4fd5-8b5b-91512fbdc75b", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": null, "id": "39353473fce5dd2e", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "39c0ccd229927eab", "metadata": {}, "source": ["# Tool Calls / Function Calling"]}, {"cell_type": "markdown", "id": "2bf6b40be07fe2d4", "metadata": {}, "source": ["This example shows how to use tool calling with AI21 models:"]}, {"cell_type": "code", "execution_count": null, "id": "a181a28df77120fb", "metadata": {}, "outputs": [], "source": ["import os\n", "from getpass import getpass\n", "\n", "from langchain_ai21.chat_models import ChatAI21\n", "from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage\n", "from langchain_core.tools import tool\n", "from langchain_core.utils.function_calling import convert_to_openai_tool\n", "\n", "if \"AI21_API_KEY\" not in os.environ:\n", "    os.environ[\"AI21_API_KEY\"] = getpass()\n", "\n", "\n", "@tool\n", "def get_weather(location: str, date: str) -> str:\n", "    \"\"\"“Provide the weather for the specified location on the given date.”\"\"\"\n", "    if location == \"New York\" and date == \"2024-12-05\":\n", "        return \"25 celsius\"\n", "    elif location == \"New York\" and date == \"2024-12-06\":\n", "        return \"27 celsius\"\n", "    elif location == \"London\" and date == \"2024-12-05\":\n", "        return \"22 celsius\"\n", "    return \"32 celsius\"\n", "\n", "\n", "llm = ChatAI21(model=\"jamba-1.5-mini\")\n", "\n", "llm_with_tools = llm.bind_tools([convert_to_openai_tool(get_weather)])\n", "\n", "chat_messages = [\n", "    SystemMessage(\n", "        content=\"You are a helpful assistant. You can use the provided tools \"\n", "        \"to assist with various tasks and provide accurate information\"\n", "    )\n", "]\n", "\n", "human_messages = [\n", "    HumanMessage(\n", "        content=\"What is the forecast for the weather in New York on December 5, 2024?\"\n", "    ),\n", "    HumanMessage(content=\"And what about the 2024-12-06?\"),\n", "    HumanMessage(content=\"OK, thank you.\"),\n", "    HumanMessage(content=\"What is the expected weather in London on December 5, 2024?\"),\n", "]\n", "\n", "\n", "for human_message in human_messages:\n", "    print(f\"User: {human_message.content}\")\n", "    chat_messages.append(human_message)\n", "    response = llm_with_tools.invoke(chat_messages)\n", "    chat_messages.append(response)\n", "    if response.tool_calls:\n", "        tool_call = response.tool_calls[0]\n", "        if tool_call[\"name\"] == \"get_weather\":\n", "            weather = get_weather.invoke(\n", "                {\n", "                    \"location\": tool_call[\"args\"][\"location\"],\n", "                    \"date\": tool_call[\"args\"][\"date\"],\n", "                }\n", "            )\n", "            chat_messages.append(\n", "                ToolMessage(content=weather, tool_call_id=tool_call[\"id\"])\n", "            )\n", "            llm_answer = llm_with_tools.invoke(chat_messages)\n", "            print(f\"Assistant: {llm_answer.content}\")\n", "    else:\n", "        print(f\"Assistant: {response.content}\")"]}, {"cell_type": "markdown", "id": "e79de691-9dd6-4697-b57e-59a4a3cc073a", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatAI21 features and configurations head to the API reference: https://python.langchain.com/api_reference/ai21/chat_models/langchain_ai21.chat_models.ChatAI21.html"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}