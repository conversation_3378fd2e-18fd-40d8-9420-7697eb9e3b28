{"cells": [{"cell_type": "raw", "id": "ce3672d3", "metadata": {}, "source": ["---\n", "sidebar_label: Prompt<PERSON><PERSON>er ChatOpenAI\n", "---"]}, {"attachments": {}, "cell_type": "markdown", "id": "959300d4", "metadata": {}, "source": ["# PromptLayerChatOpenAI\n", "\n", "This example showcases how to connect to [PromptLayer](https://www.promptlayer.com) to start recording your ChatOpenAI requests."]}, {"attachments": {}, "cell_type": "markdown", "id": "6a45943e", "metadata": {}, "source": ["## Install PromptLayer\n", "The `promptlayer` package is required to use PromptLayer with OpenAI. Install `promptlayer` using pip."]}, {"cell_type": "code", "execution_count": null, "id": "dbe09bd8", "metadata": {"vscode": {"languageId": "powershell"}}, "outputs": [], "source": ["pip install promptlayer"]}, {"cell_type": "markdown", "id": "536c1dfa", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 2, "id": "c16da3b5", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from langchain_community.chat_models import PromptLayerChatOpenAI\n", "from langchain_core.messages import HumanMessage"]}, {"attachments": {}, "cell_type": "markdown", "id": "8564ce7d", "metadata": {}, "source": ["## Set the Environment API Key\n", "You can create a PromptLayer API Key at [www.promptlayer.com](https://www.promptlayer.com) by clicking the settings cog in the navbar.\n", "\n", "Set it as an environment variable called `PROMPTLAYER_API_KEY`."]}, {"cell_type": "code", "execution_count": 5, "id": "46ba25dc", "metadata": {}, "outputs": [], "source": ["os.environ[\"PROMPTLAYER_API_KEY\"] = \"**********\""]}, {"attachments": {}, "cell_type": "markdown", "id": "bf0294de", "metadata": {}, "source": ["## Use the PromptLayerOpenAI LLM like normal\n", "*You can optionally pass in `pl_tags` to track your requests with Pro<PERSON><PERSON><PERSON><PERSON>'s tagging feature.*"]}, {"cell_type": "code", "execution_count": 4, "id": "3acf0069", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='to take a nap in a cozy spot. I search around for a suitable place and finally settle on a soft cushion on the window sill. I curl up into a ball and close my eyes, relishing the warmth of the sun on my fur. As I drift off to sleep, I can hear the birds chirping outside and feel the gentle breeze blowing through the window. This is the life of a contented cat.', additional_kwargs={})"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["chat = PromptLayerChatOpenAI(pl_tags=[\"langchain\"])\n", "chat([HumanMessage(content=\"I am a cat and I want\")])"]}, {"attachments": {}, "cell_type": "markdown", "id": "a2d76826", "metadata": {}, "source": ["**The above request should now appear on your [PromptLayer dashboard](https://www.promptlayer.com).**"]}, {"attachments": {}, "cell_type": "markdown", "id": "c43803d1", "metadata": {}, "source": ["## Using PromptLayer Track\n", "If you would like to use any of the [PromptLayer tracking features](https://magniv.notion.site/Track-4deee1b1f7a34c1680d085f82567dab9), you need to pass the argument `return_pl_id` when instantiating the PromptLayer LLM to get the request id.  "]}, {"cell_type": "code", "execution_count": null, "id": "b7d4db01", "metadata": {}, "outputs": [], "source": ["import promptlayer\n", "\n", "chat = PromptLayerChatOpenAI(return_pl_id=True)\n", "chat_results = chat.generate([[HumanMessage(content=\"I am a cat and I want\")]])\n", "\n", "for res in chat_results.generations:\n", "    pl_request_id = res[0].generation_info[\"pl_request_id\"]\n", "    promptlayer.track.score(request_id=pl_request_id, score=100)"]}, {"cell_type": "markdown", "id": "13e56507", "metadata": {}, "source": ["Using this allows you to track the performance of your model in the PromptLayer dashboard. If you are using a prompt template, you can attach a template to a request as well.\n", "Overall, this gives you the opportunity to track the performance of different templates and models in the PromptLayer dashboard."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "vscode": {"interpreter": {"hash": "8a5edab282632443219e051e4ade2d1d5bbc671c781051bf1437897cbdfea0f1"}}}, "nbformat": 4, "nbformat_minor": 5}