{"cells": [{"cell_type": "raw", "id": "a016701c", "metadata": {}, "source": ["---\n", "sidebar_label: Perplexity\n", "---"]}, {"cell_type": "markdown", "id": "bf733a38-db84-4363-89e2-de6735c37230", "metadata": {}, "source": ["# ChatPerplexity\n", "\n", "\n", "This page will help you get started with Perplexity [chat models](../../concepts/chat_models.mdx). For detailed documentation of all `ChatPerplexity` features and configurations head to the [API reference](https://python.langchain.com/api_reference/perplexity/chat_models/langchain_perplexity.chat_models.ChatPerplexity.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/chat/xai) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatPerplexity](https://python.langchain.com/api_reference/perplexity/chat_models/langchain_perplexity.chat_models.ChatPerplexity.html) | [langchain-perplexity](https://python.langchain.com/api_reference/perplexity/perplexity.html) | ❌ | beta | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-perplexity?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-perplexity?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](../../how_to/tool_calling.ipynb) | [Structured output](../../how_to/structured_output.ipynb) | JSON mode | [Image input](../../how_to/multimodal_inputs.ipynb) | Audio input | Video input | [Token-level streaming](../../how_to/chat_streaming.ipynb) | Native async | [Token usage](../../how_to/chat_token_usage_tracking.ipynb) | [Logprobs](../../how_to/logprobs.ipynb) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ❌ | ✅ | ✅ | ❌ | ❌ | ❌ | ✅ | ❌ | ✅ | ❌ |\n", "\n", "## Setup\n", "\n", "To access Perplexity models you'll need to create a Perplexity account, get an API key, and install the `langchain-perplexity` integration package.\n", "\n", "### Credentials\n", "\n", "Head to [this page](https://www.perplexity.ai/) to sign up for Perplexity and generate an API key. Once you've done this set the `PPLX_API_KEY` environment variable:"]}, {"cell_type": "code", "execution_count": null, "id": "2243f329", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if \"PPLX_API_KEY\" not in os.environ:\n", "    os.environ[\"PPLX_API_KEY\"] = getpass.getpass(\"Enter your Perplexity API key: \")"]}, {"cell_type": "markdown", "id": "7dfe47c4", "metadata": {}, "source": ["To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:"]}, {"cell_type": "code", "execution_count": null, "id": "10a791fa", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")\n", "# os.environ[\"LANGSMITH_TRACING\"] = \"true\""]}, {"cell_type": "code", "execution_count": null, "id": "d4a7c55d-b235-4ca4-a579-c90cc9570da9", "metadata": {"ExecuteTime": {"end_time": "2024-01-19T11:25:00.590587Z", "start_time": "2024-01-19T11:25:00.127293Z"}, "tags": []}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_perplexity import ChatPerplexity"]}, {"cell_type": "markdown", "id": "b26e2035-2f81-4451-ba44-fa2e2d5aeb62", "metadata": {}, "source": ["The code provided assumes that your PPLX_API_KEY is set in your environment variables. If you would like to manually specify your API key and also choose a different model, you can use the following code:"]}, {"cell_type": "code", "execution_count": null, "id": "d986aac6-1bae-4608-8514-d3ba5b35b10e", "metadata": {}, "outputs": [], "source": ["chat = ChatPerplexity(temperature=0, pplx_api_key=\"YOUR_API_KEY\", model=\"sonar\")"]}, {"cell_type": "markdown", "id": "97a8ce3a", "metadata": {}, "source": ["You can check a list of available models [here](https://docs.perplexity.ai/docs/model-cards). For reproducibility, we can set the API key dynamically by taking it as an input in this notebook."]}, {"cell_type": "code", "execution_count": 3, "id": "70cf04e8-423a-4ff6-8b09-f11fb711c817", "metadata": {"ExecuteTime": {"end_time": "2024-01-19T11:25:04.349676Z", "start_time": "2024-01-19T11:25:03.964930Z"}, "tags": []}, "outputs": [], "source": ["chat = ChatPerplexity(temperature=0, model=\"sonar\")"]}, {"cell_type": "code", "execution_count": 4, "id": "8199ef8f-eb8b-4253-9ea0-6c24a013ca4c", "metadata": {"ExecuteTime": {"end_time": "2024-01-19T11:25:07.274418Z", "start_time": "2024-01-19T11:25:05.898031Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["'The Higgs Boson is an elementary subatomic particle that plays a crucial role in the Standard Model of particle physics, which accounts for three of the four fundamental forces governing the behavior of our universe: the strong and weak nuclear forces, electromagnetism, and gravity. The Higgs Boson is important for several reasons:\\n\\n1. **Final Elementary Particle**: The Higgs Boson is the last elementary particle waiting to be discovered under the Standard Model. Its detection helps complete the Standard Model and further our understanding of the fundamental forces in the universe.\\n\\n2. **Mass Generation**: The Higgs Boson is responsible for giving mass to other particles, a process that occurs through its interaction with the Higgs field. This mass generation is essential for the formation of atoms, molecules, and the visible matter we observe in the universe.\\n\\n3. **Implications for New Physics**: While the detection of the Higgs Boson has confirmed many aspects of the Standard Model, it also opens up new possibilities for discoveries beyond the Standard Model. Further research on the Higgs Boson could reveal insights into the nature of dark matter, supersymmetry, and other exotic phenomena.\\n\\n4. **Advancements in Technology**: The search for the Higgs Boson has led to significant advancements in technology, such as the development of artificial intelligence and machine learning algorithms used in particle accelerators like the Large Hadron Collider (LHC). These advancements have not only contributed to the discovery of the Higgs Boson but also have potential applications in various other fields.\\n\\nIn summary, the Higgs Boson is important because it completes the Standard Model, plays a crucial role in mass generation, hints at new physics phenomena beyond the Standard Model, and drives advancements in technology.\\n'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["system = \"You are a helpful assistant.\"\n", "human = \"{input}\"\n", "prompt = ChatPromptTemplate.from_messages([(\"system\", system), (\"human\", human)])\n", "\n", "chain = prompt | chat\n", "response = chain.invoke({\"input\": \"Why is the Higgs Boson important?\"})\n", "response.content"]}, {"cell_type": "markdown", "id": "de6d8d5a", "metadata": {}, "source": ["You can format and structure the prompts like you would typically. In the following example, we ask the model to tell us a joke about cats."]}, {"cell_type": "code", "execution_count": 5, "id": "c5fac0e9-05a4-4fc1-a3b3-e5bbb24b971b", "metadata": {"ExecuteTime": {"end_time": "2024-01-19T11:25:10.448733Z", "start_time": "2024-01-19T11:25:08.866277Z"}, "tags": []}, "outputs": [{"data": {"text/plain": ["'Here\\'s a joke about cats:\\n\\nWhy did the cat want math lessons from a mermaid?\\n\\nBecause it couldn\\'t find its \"core purpose\" in life!\\n\\nRemember, cats are unique and fascinating creatures, and each one has its own special traits and abilities. While some may see them as mysterious or even a bit aloof, they are still beloved pets that bring joy and companionship to their owners. So, if your cat ever seeks guidance from a mermaid, just remember that they are on their own journey to self-discovery!\\n'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["chat = ChatPerplexity(temperature=0, model=\"sonar\")\n", "prompt = ChatPromptTemplate.from_messages([(\"human\", \"Tell me a joke about {topic}\")])\n", "chain = prompt | chat\n", "response = chain.invoke({\"topic\": \"cats\"})\n", "response.content"]}, {"cell_type": "markdown", "id": "a7f8f61b", "metadata": {}, "source": ["## Using Perplexity-specific parameters through `ChatPerplexity`\n", "\n", "You can also use Perplexity-specific parameters through the ChatPerplexity class. For example, parameters like search_domain_filter, return_images, return_related_questions or search_recency_filter using the extra_body parameter as shown below:"]}, {"cell_type": "code", "execution_count": null, "id": "73960f51", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Sure, here's a cat joke for you:\\n\\nWhy are cats bad storytellers?\\n\\nBecause they only have one tale. (Pun alert!)\\n\\nSource: OneLineFun.com [4]\""]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["chat = ChatPerplexity(temperature=0.7, model=\"sonar\")\n", "response = chat.invoke(\n", "    \"Tell me a joke about cats\", extra_body={\"search_recency_filter\": \"week\"}\n", ")\n", "response.content"]}, {"cell_type": "markdown", "id": "382335a6", "metadata": {}, "source": ["### Accessing the search results metadata\n", "\n", "Perplexity often provides a list of the web pages it consulted (“search_results”).\n", "You don't need to pass any special parameter — the list is placed in\n", "`response.additional_kwargs[\"search_results\"]`.\n"]}, {"cell_type": "code", "execution_count": null, "id": "2b09214a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The tallest mountain in South America is Aconcagua. It has a summit elevation of approximately 6,961 meters (22,838 feet), making it not only the highest peak in South America but also the highest mountain in the Americas, the Western Hemisphere, and the Southern Hemisphere[1][2][4].\n", "\n", "Aconcagua is located in the Principal Cordillera of the Andes mountain range, in Mendoza Province, Argentina, near the border with Chile[1][2][4]. It is of volcanic origin but is not an active volcano[4]. The mountain is part of Aconcagua Provincial Park and features several glaciers, including the large Ventisquero Horcones Inferior glacier[1].\n", "\n", "In summary, Aconcagua stands as the tallest mountain in South America at about 6,961 meters (22,838 feet) in height.\n"]}, {"data": {"text/plain": ["[{'title': 'Aconcagua - Wikipedia',\n", "  'url': 'https://en.wikipedia.org/wiki/Aconcagua',\n", "  'date': None},\n", " {'title': 'The 10 Highest Mountains in South America - Much Better Adventures',\n", "  'url': 'https://www.muchbetteradventures.com/magazine/highest-mountains-south-america/',\n", "  'date': '2023-07-05'}]"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["chat = ChatPerplexity(temperature=0, model=\"sonar\")\n", "\n", "response = chat.invoke(\n", "    \"What is the tallest mountain in South America?\",\n", ")\n", "\n", "# Main answer\n", "print(response.content)\n", "\n", "# First two supporting search results\n", "response.additional_kwargs[\"search_results\"][:2]"]}, {"cell_type": "markdown", "id": "13d93dc4", "metadata": {}, "source": ["## `ChatPerplexity` also supports streaming functionality:"]}, {"cell_type": "code", "execution_count": 6, "id": "025be980-e50d-4a68-93dc-c9c7b500ce34", "metadata": {"ExecuteTime": {"end_time": "2024-01-19T11:25:24.438696Z", "start_time": "2024-01-19T11:25:14.687480Z"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here is a list of some famous tourist attractions in Pakistan:\n", "\n", "1. **Minar-e-Pakistan**: A 62-meter high minaret in Lahore that represents the history of Pakistan.\n", "2. **Badshahi Mosque**: A historic mosque in Lahore with a capacity of 10,000 worshippers.\n", "3. **Shalimar Gardens**: A beautiful garden in Lahore with landscaped grounds and a series of cascading pools.\n", "4. **Pakistan Monument**: A national monument in Islamabad representing the four provinces and three districts of Pakistan.\n", "5. **National Museum of Pakistan**: A museum in Karachi showcasing the country's cultural history.\n", "6. **Faisal Mosque**: A large mosque in Islamabad that can accommodate up to 300,000 worshippers.\n", "7. **Clifton Beach**: A popular beach in Karachi offering water activities and recreational facilities.\n", "8. **Kartarpur Corridor**: A visa-free border crossing and religious corridor connecting Gurdwara Darbar Sahib in Pakistan to Gurudwara Sri Kartarpur Sahib in India.\n", "9. **Mohenjo-daro**: An ancient Indus Valley civilization site in Sindh, Pakistan, dating back to around 2500 BCE.\n", "10. **Hunza Valley**: A picturesque valley in Gilgit-Baltistan known for its stunning mountain scenery and unique culture.\n", "\n", "These attractions showcase the rich history, diverse culture, and natural beauty of Pakistan, making them popular destinations for both local and international tourists.\n"]}], "source": ["chat = ChatPerplexity(temperature=0.7, model=\"sonar\")\n", "\n", "for chunk in chat.stream(\"Give me a list of famous tourist attractions in Pakistan\"):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "markdown", "id": "397c43de", "metadata": {}, "source": ["## `ChatPerplexity` Supports Structured Outputs for Tier 3+ Users"]}, {"cell_type": "code", "execution_count": 6, "id": "1bae9b80-394a-4340-9c30-612c136b742a", "metadata": {}, "outputs": [{"data": {"text/plain": ["AnswerFormat(first_name='<PERSON>', last_name='<PERSON>', year_of_birth=1963, num_seasons_in_nba=15)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from pydantic import BaseModel\n", "\n", "\n", "class AnswerFormat(BaseModel):\n", "    first_name: str\n", "    last_name: str\n", "    year_of_birth: int\n", "    num_seasons_in_nba: int\n", "\n", "\n", "chat = ChatPerplexity(temperature=0.7, model=\"sonar-pro\")\n", "structured_chat = chat.with_structured_output(AnswerFormat)\n", "response = structured_chat.invoke(\n", "    \"Tell me about <PERSON>. Return your answer \"\n", "    \"as JSON with keys first_name (str), last_name (str), \"\n", "    \"year_of_birth (int), and num_seasons_in_nba (int).\"\n", ")\n", "response"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}