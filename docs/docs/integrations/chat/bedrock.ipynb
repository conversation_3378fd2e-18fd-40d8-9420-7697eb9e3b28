{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {}, "source": ["---\n", "sidebar_label: AWS Bedrock\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatBedrock\n", "\n", "This doc will help you get started with AWS Bedrock [chat models](/docs/concepts/chat_models). Amazon Bedrock is a fully managed service that offers a choice of high-performing foundation models (FMs) from leading AI companies like AI21 Labs, Anthropic, Cohere, Meta, Stability AI, and Amazon via a single API, along with a broad set of capabilities you need to build generative AI applications with security, privacy, and responsible AI. Using Amazon Bedrock, you can easily experiment with and evaluate top FMs for your use case, privately customize them with your data using techniques such as fine-tuning and Retrieval Augmented Generation (RAG), and build agents that execute tasks using your enterprise systems and data sources. Since Amazon Bedrock is serverless, you don't have to manage any infrastructure, and you can securely integrate and deploy generative AI capabilities into your applications using the AWS services you are already familiar with.\n", "\n", "AWS Bedrock maintains a [Converse API](https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_Converse.html) which provides a unified conversational interface for Bedrock models. This API does not yet support custom models. You can see a list of all [models that are supported here](https://docs.aws.amazon.com/bedrock/latest/userguide/conversation-inference.html).\n", "\n", ":::info\n", "\n", "We recommend the Converse API for users who do not need to use custom models. It can be accessed using [ChatBedrockConverse](https://python.langchain.com/api_reference/aws/chat_models/langchain_aws.chat_models.bedrock_converse.ChatBedrockConverse.html).\n", "\n", ":::\n", "\n", "For detailed documentation of all Bedrock features and configurations head to the [API reference](https://python.langchain.com/api_reference/aws/chat_models/langchain_aws.chat_models.bedrock_converse.ChatBedrockConverse.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/chat/bedrock) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatBedrock](https://python.langchain.com/api_reference/aws/chat_models/langchain_aws.chat_models.bedrock.ChatBedrock.html) | [langchain-aws](https://python.langchain.com/api_reference/aws/index.html) | ❌ | beta | ✅ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-aws?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-aws?style=flat-square&label=%20) |\n", "| [ChatBedrockConverse](https://python.langchain.com/api_reference/aws/chat_models/langchain_aws.chat_models.bedrock_converse.ChatBedrockConverse.html) | [langchain-aws](https://python.langchain.com/api_reference/aws/index.html) | ❌ | beta | ✅ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-aws?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-aws?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "\n", "The below apply to both `ChatBedrock` and `ChatBedrockConverse`.\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ❌ | ✅ | ❌ | ❌ | ✅ | ❌ | ✅ | ❌ |\n", "\n", "## Setup\n", "\n", "To access Bedrock models you'll need to create an AWS account, set up the Bedrock API service, get an access key ID and secret key, and install the `langchain-aws` integration package.\n", "\n", "### Credentials\n", "\n", "Head to the [AWS docs](https://docs.aws.amazon.com/bedrock/latest/userguide/setting-up.html) to sign up to AWS and setup your credentials.\n", "\n", "Alternatively, `ChatBedrockConverse` will read from the following environment variables by default:"]}, {"cell_type": "code", "execution_count": null, "id": "0f65be92", "metadata": {}, "outputs": [], "source": ["# os.environ[\"AWS_ACCESS_KEY_ID\"] = \"...\"\n", "# os.environ[\"AWS_SECRET_ACCESS_KEY\"] = \"...\"\n", "\n", "# Not required unless using temporary credentials.\n", "# os.environ[\"AWS_SESSION_TOKEN\"] = \"...\""]}, {"cell_type": "markdown", "id": "3baad5a9", "metadata": {}, "source": ["You'll also need to turn on model access for your account, which you can do by following [these instructions](https://docs.aws.amazon.com/bedrock/latest/userguide/model-access.html)."]}, {"cell_type": "markdown", "id": "72ee0c4b-9764-423a-9dbf-95129e185210", "metadata": {}, "source": ["To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:"]}, {"cell_type": "code", "execution_count": null, "id": "a15d341e-3e26-4ca3-830b-5aab30ed66de", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")\n", "# os.environ[\"LANGSMITH_TRACING\"] = \"true\""]}, {"cell_type": "markdown", "id": "0730d6a1-c893-4840-9817-5e5251676d5d", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain Bedrock integration lives in the `langchain-aws` package:"]}, {"cell_type": "code", "execution_count": null, "id": "652d6238-1f87-422a-b135-f5abbb8652fc", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-aws"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["from langchain_aws import ChatBedrockConverse\n", "\n", "llm = ChatBedrockConverse(\n", "    model_id=\"anthropic.claude-3-5-sonnet-20240620-v1:0\",\n", "    # region_name=...,\n", "    # aws_access_key_id=...,\n", "    # aws_secret_access_key=...,\n", "    # aws_session_token=...,\n", "    # temperature=...,\n", "    # max_tokens=...,\n", "    # other params...\n", ")"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 2, "id": "fcd8de52-4a1b-4875-b463-d41b031e06a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"J'adore la programmation.\", additional_kwargs={}, response_metadata={'ResponseMetadata': {'RequestId': 'b07d1630-06f2-44b1-82bf-e82538dd2215', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 16 Apr 2025 19:35:34 GMT', 'content-type': 'application/json', 'content-length': '206', 'connection': 'keep-alive', 'x-amzn-requestid': 'b07d1630-06f2-44b1-82bf-e82538dd2215'}, 'RetryAttempts': 0}, 'stopReason': 'end_turn', 'metrics': {'latencyMs': [488]}, 'model_name': 'anthropic.claude-3-5-sonnet-20240620-v1:0'}, id='run-d09ed928-146a-4336-b1fd-b63c9e623494-0', usage_metadata={'input_tokens': 29, 'output_tokens': 11, 'total_tokens': 40, 'input_token_details': {'cache_creation': 0, 'cache_read': 0}})"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 3, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore la programmation.\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "id": "4da16f3e-e80b-48c0-8036-c1cc5f7c8c05", "metadata": {}, "source": ["### Streaming\n", "\n", "Note that `ChatBedrockConverse` emits content blocks while streaming:"]}, {"cell_type": "code", "execution_count": 4, "id": "605e04fa-1a76-47ac-8c92-fe128659663e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content=[] additional_kwargs={} response_metadata={} id='run-d0e0836e-7146-4c3d-97c7-ad23dac6febd'\n", "content=[{'type': 'text', 'text': 'J', 'index': 0}] additional_kwargs={} response_metadata={} id='run-d0e0836e-7146-4c3d-97c7-ad23dac6febd'\n", "content=[{'type': 'text', 'text': \"'adore la\", 'index': 0}] additional_kwargs={} response_metadata={} id='run-d0e0836e-7146-4c3d-97c7-ad23dac6febd'\n", "content=[{'type': 'text', 'text': ' programmation.', 'index': 0}] additional_kwargs={} response_metadata={} id='run-d0e0836e-7146-4c3d-97c7-ad23dac6febd'\n", "content=[{'index': 0}] additional_kwargs={} response_metadata={} id='run-d0e0836e-7146-4c3d-97c7-ad23dac6febd'\n", "content=[] additional_kwargs={} response_metadata={'stopReason': 'end_turn'} id='run-d0e0836e-7146-4c3d-97c7-ad23dac6febd'\n", "content=[] additional_kwargs={} response_metadata={'metrics': {'latencyMs': 600}, 'model_name': 'anthropic.claude-3-5-sonnet-20240620-v1:0'} id='run-d0e0836e-7146-4c3d-97c7-ad23dac6febd' usage_metadata={'input_tokens': 29, 'output_tokens': 11, 'total_tokens': 40, 'input_token_details': {'cache_creation': 0, 'cache_read': 0}}\n"]}], "source": ["for chunk in llm.stream(messages):\n", "    print(chunk)"]}, {"cell_type": "markdown", "id": "0ef05abb-9c04-4dc3-995e-f857779644d5", "metadata": {}, "source": ["You can filter to text using the [.text()](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.AIMessage.html#langchain_core.messages.ai.AIMessage.text) method on the output:"]}, {"cell_type": "code", "execution_count": 5, "id": "2a4e743f-ea7d-4e5a-9b12-f9992362de8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|J|'adore la| programmation.||||"]}], "source": ["for chunk in llm.stream(messages):\n", "    print(chunk.text(), end=\"|\")"]}, {"cell_type": "markdown", "id": "a009400a", "metadata": {}, "source": ["## Extended Thinking \n", "\n", "This guide focuses on implementing Extended Thinking using AWS Bedrock with <PERSON><PERSON><PERSON><PERSON>'s `ChatBedrockConverse` integration.\n", "\n", "### Supported Models\n", "\n", "Extended Thinking is available for the following Claude models on AWS Bedrock:\n", "\n", "| Model | Model ID |\n", "|-------|----------|\n", "| **<PERSON> 4** | `anthropic.claude-opus-4-20250514-v1:0` |\n", "| **<PERSON> 4** | `anthropic.claude-sonnet-4-20250514-v1:0` |\n", "| **Claude 3.7 Sonnet** | `us.anthropic.claude-3-7-sonnet-20250219-v1:0` |\n"]}, {"cell_type": "code", "execution_count": null, "id": "abc790ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=[{'type': 'reasoning_content', 'reasoning_content': {'text': 'The user wants me to translate \"I love programming\" from English to French.\\n\\n\"I love\" translates to \"J\\'aime\" or \"J\\'adore\" in French\\n\"Programming\" translates to \"la programmation\" in French\\n\\nSo the translation would be \"J\\'aime la programmation\" or \"J\\'adore la programmation\"\\n\\nBoth are correct, but \"J\\'aime\" is more commonly used for expressing love/liking something.', 'signature': 'EpgECkgIBRABGAIqQDub6nRpiusjbxZONXVlGXg5ZjUY1Eka1Yp4oBBHmRqGjId+StTBPuwD3CXLyb2rUDRhSc3hTpTM4krVqlFZrIsSDI/WLa1mu38DDqt1HRoMUjm+jF+03MZFD+WQIjBZtHaYiqgY0JQgU0NdXDwwBSZX44gXwuX9EDekh12VM1ysq+WxVtkp0WMU0dKCJo4q/QKpguFFlZtEZjF9PftzOgTIyy+1H5pY+Dsb2pnrGtfAgwTR7PuZ/d8ibY0A8ywjVEZtGm+PtcnCJiK53BWxhGYOtxnfN/RRKtuZhvPQj+QQOWeRWqH+GcbeISCgyTYn5WG75fmVL707byjQZ3IuhMfyZWmiTFE2fc4Jn/bxX7OsU+DbTWv2K1a+g7eW+dvQwYzCBO1hfEn4699/CHII8UAcHh1L3bnxOWGKkeVQ0KMfgfwVb0vuGG4QBYKIDs87QL414i69D68DxqCTZAHK4lMA6Xs7zW+m0MMCct4iHRnJI8kat1mlBEpMz6NRo9KacZJXpLJxofIU4ho7R5/QHccdni0IidNkUtrLBSB3toNJoQEcStts2UR67NHTxn47zk1/hi4v4Ahtw9OEQFONaH6XaG1wjpqEdjQ8/Tmg9eB6ZLoQ4sQfhcMF8Uo3hHbBY8jA3jZ+9pa9VbuVbO6Eup8NX3XXZm2nk50OMWX7hBwgBmlZbEew6pWFu7+13EkYAQ=='}}, {'type': 'text', 'text': \"J'aime la programmation.\"}], additional_kwargs={}, response_metadata={'ResponseMetadata': {'RequestId': '169ca92f-19c9-480c-9fc3-4e5284507e67', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Tue, 22 Jul 2025 04:40:22 GMT', 'content-type': 'application/json', 'content-length': '1498', 'connection': 'keep-alive', 'x-amzn-requestid': '169ca92f-19c9-480c-9fc3-4e5284507e67'}, 'RetryAttempts': 0}, 'stopReason': 'end_turn', 'metrics': {'latencyMs': [2839]}, 'model_name': 'us.anthropic.claude-sonnet-4-20250514-v1:0'}, id='run--42e05e5d-ba86-4dce-9e29-2a4ba32c5804-0', usage_metadata={'input_tokens': 58, 'output_tokens': 122, 'total_tokens': 180, 'input_token_details': {'cache_creation': 0, 'cache_read': 0}})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_aws import ChatBedrockConverse\n", "\n", "llm = ChatBedrockConverse(\n", "    model_id=\"us.anthropic.claude-sonnet-4-20250514-v1:0\",\n", "    region_name=\"us-west-2\",\n", "    max_tokens=4096,\n", "    additional_model_request_fields={\n", "        \"thinking\": {\"type\": \"enabled\", \"budget_tokens\": 1024},\n", "    },\n", ")\n", "\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 4, "id": "7fb27b941602401d91542211134fc71a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'type': 'reasoning_content', 'reasoning_content': {'text': 'The user wants me to translate \"I love programming\" from English to French.\\n\\n\"I love\" translates to \"J\\'aime\" or \"J\\'adore\" in French\\n\"Programming\" translates to \"la programmation\" in French\\n\\nSo the translation would be \"J\\'aime la programmation\" or \"J\\'adore la programmation\"\\n\\nBoth are correct, but \"J\\'aime\" is more commonly used for expressing love/liking something.', 'signature': 'EpgECkgIBRABGAIqQDub6nRpiusjbxZONXVlGXg5ZjUY1Eka1Yp4oBBHmRqGjId+StTBPuwD3CXLyb2rUDRhSc3hTpTM4krVqlFZrIsSDI/WLa1mu38DDqt1HRoMUjm+jF+03MZFD+WQIjBZtHaYiqgY0JQgU0NdXDwwBSZX44gXwuX9EDekh12VM1ysq+WxVtkp0WMU0dKCJo4q/QKpguFFlZtEZjF9PftzOgTIyy+1H5pY+Dsb2pnrGtfAgwTR7PuZ/d8ibY0A8ywjVEZtGm+PtcnCJiK53BWxhGYOtxnfN/RRKtuZhvPQj+QQOWeRWqH+GcbeISCgyTYn5WG75fmVL707byjQZ3IuhMfyZWmiTFE2fc4Jn/bxX7OsU+DbTWv2K1a+g7eW+dvQwYzCBO1hfEn4699/CHII8UAcHh1L3bnxOWGKkeVQ0KMfgfwVb0vuGG4QBYKIDs87QL414i69D68DxqCTZAHK4lMA6Xs7zW+m0MMCct4iHRnJI8kat1mlBEpMz6NRo9KacZJXpLJxofIU4ho7R5/QHccdni0IidNkUtrLBSB3toNJoQEcStts2UR67NHTxn47zk1/hi4v4Ahtw9OEQFONaH6XaG1wjpqEdjQ8/Tmg9eB6ZLoQ4sQfhcMF8Uo3hHbBY8jA3jZ+9pa9VbuVbO6Eup8NX3XXZm2nk50OMWX7hBwgBmlZbEew6pWFu7+13EkYAQ=='}}, {'type': 'text', 'text': \"J'aime la programmation.\"}]\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "id": "f1eb1ce1", "metadata": {}, "source": ["### How extended thinking works\n", "\n", "When extended thinking is turned on, <PERSON> creates thinking content blocks where it outputs its internal reasoning. <PERSON> incorporates insights from this reasoning before crafting a final response. The API response will include thinking content blocks, followed by text content blocks."]}, {"cell_type": "code", "execution_count": 5, "id": "951d8206", "metadata": {}, "outputs": [{"data": {"text/plain": ["[('system',\n", "  'You are a helpful assistant that translates English to French. Translate the user sentence.'),\n", " ('human', 'I love programming.'),\n", " ('ai',\n", "  [{'type': 'reasoning_content',\n", "    'reasoning_content': {'text': 'The user wants me to translate \"I love programming\" from English to French.\\n\\n\"I love\" translates to \"J\\'aime\" or \"J\\'adore\" in French\\n\"Programming\" translates to \"la programmation\" in French\\n\\nSo the translation would be \"J\\'aime la programmation\" or \"J\\'adore la programmation\"\\n\\nBoth are correct, but \"J\\'aime\" is more commonly used for expressing love/liking something.',\n", "     'signature': 'EpgECkgIBRABGAIqQDub6nRpiusjbxZONXVlGXg5ZjUY1Eka1Yp4oBBHmRqGjId+StTBPuwD3CXLyb2rUDRhSc3hTpTM4krVqlFZrIsSDI/WLa1mu38DDqt1HRoMUjm+jF+03MZFD+WQIjBZtHaYiqgY0JQgU0NdXDwwBSZX44gXwuX9EDekh12VM1ysq+WxVtkp0WMU0dKCJo4q/QKpguFFlZtEZjF9PftzOgTIyy+1H5pY+Dsb2pnrGtfAgwTR7PuZ/d8ibY0A8ywjVEZtGm+PtcnCJiK53BWxhGYOtxnfN/RRKtuZhvPQj+QQOWeRWqH+GcbeISCgyTYn5WG75fmVL707byjQZ3IuhMfyZWmiTFE2fc4Jn/bxX7OsU+DbTWv2K1a+g7eW+dvQwYzCBO1hfEn4699/CHII8UAcHh1L3bnxOWGKkeVQ0KMfgfwVb0vuGG4QBYKIDs87QL414i69D68DxqCTZAHK4lMA6Xs7zW+m0MMCct4iHRnJI8kat1mlBEpMz6NRo9KacZJXpLJxofIU4ho7R5/QHccdni0IidNkUtrLBSB3toNJoQEcStts2UR67NHTxn47zk1/hi4v4Ahtw9OEQFONaH6XaG1wjpqEdjQ8/Tmg9eB6ZLoQ4sQfhcMF8Uo3hHbBY8jA3jZ+9pa9VbuVbO6Eup8NX3XXZm2nk50OMWX7hBwgBmlZbEew6pWFu7+13EkYAQ=='}},\n", "   {'type': 'text', 'text': \"J'aime la programmation.\"}]),\n", " ('human', 'I love <PERSON>')]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["next_messages = messages + [(\"ai\", ai_msg.content), (\"human\", \"I love AI\")]\n", "next_messages"]}, {"cell_type": "code", "execution_count": 6, "id": "9d8c506c", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=[{'type': 'reasoning_content', 'reasoning_content': {'text': 'The user wants me to translate \"I love AI\" from English to French. \\n\\n\"I love\" translates to \"J\\'aime\" in French.\\n\"AI\" stands for \"Artificial Intelligence\" which in French is \"Intelligence Artificielle\" or abbreviated as \"IA\".\\n\\nSo the translation would be \"J\\'aime l\\'IA\" (using the abbreviation) or \"J\\'aime l\\'intelligence artificielle\" (using the full term).\\n\\nI think using the abbreviation \"IA\" would be more natural and commonly used, similar to how we use \"AI\" in English.', 'signature': 'EoMFCkgIBRABGAIqQOwp9d0YWm8NctfL9lf1MeWR1OxeAKB3Es19Lei2bdHQ4W0ezTK4wVcm/VLM+7kICX2aB9RAmUD5sJxoKHfdX38SDIR/aSJhHZifGOHqwBoMhzNsyPmB7FFNvNESIjBMVRpRUDTFGn5+nL0x5CjWhKA8H/XFnKYRrUyMYb1n7lCQA7BeEjsaWwxZ3YV9rZsq6APuaXaA40Bt+KnpPOo06r72L/DceliRAw1a6cuT5E0Dv0eIAOYblbXaKYn0jy8UzTUuctOP3As/zT5pK5yC+Rx0d2l9kuP3+COERM98u0R04bWn6qh0HcyE+zNc7c4YWkncjdmOxF/j6OxhcMhZEoX2035v9eUJ9+O/u1xaff08YAEfg7TGWrSIwalpjs1mzWA9ijKg8YyjmXjWnMeFn0z6LDqLaaKc+nC8IN9SLwA/eHpf/ayoEgmogn7gWzijW8MDbnlwpQDS75wK7An3RMEcpWD/OXrKb1EhWKEmOBro5BOTGsfK3ZDveRL0aCBINdOu+AHMQDFXJ04cRDEjs9GE3YC218UcFtS42TFO7/Ct5CYCTknETPx93zcGTOM2VPOZ02Uem1A7Nda/Fa4l2b03EUEtwlgske5K1RbeohN9sclxYsxX5nGJ5sSZurVCk9plkyTG3aiPvbohfVVarVgukKoKwoMDYz5rHVscWlUe+qeqJE/H+KKlhtzO+lWWDN4knqeYsZ55flO5Hq4vT20QCYnF8hcUx07ngGKXuGID9n5kFnLsP8sBUHYKm7bmopFFZvfPcmsqiV9yvG/8Ly9DHbmY5ZwxyrbdJCFT6HD6kq/mEBDftZ6dhmyKMimJBfbTj7d3VAILbRgB'}}, {'type': 'text', 'text': \"J'aime l'IA.\"}], additional_kwargs={}, response_metadata={'ResponseMetadata': {'RequestId': '023799d6-7ed5-4e49-8ad7-7460a49a9a45', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Tue, 22 Jul 2025 04:40:34 GMT', 'content-type': 'application/json', 'content-length': '1737', 'connection': 'keep-alive', 'x-amzn-requestid': '023799d6-7ed5-4e49-8ad7-7460a49a9a45'}, 'RetryAttempts': 0}, 'stopReason': 'end_turn', 'metrics': {'latencyMs': [3473]}, 'model_name': 'us.anthropic.claude-sonnet-4-20250514-v1:0'}, id='run--ca8abc92-60a9-4bd1-93b4-7788496eda7a-0', usage_metadata={'input_tokens': 75, 'output_tokens': 153, 'total_tokens': 228, 'input_token_details': {'cache_creation': 0, 'cache_read': 0}})"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["ai_msg = llm.invoke(next_messages)\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 7, "id": "e53e3ebb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'type': 'reasoning_content', 'reasoning_content': {'text': 'The user wants me to translate \"I love AI\" from English to French. \\n\\n\"I love\" translates to \"J\\'aime\" in French.\\n\"AI\" stands for \"Artificial Intelligence\" which in French is \"Intelligence Artificielle\" or abbreviated as \"IA\".\\n\\nSo the translation would be \"J\\'aime l\\'IA\" (using the abbreviation) or \"J\\'aime l\\'intelligence artificielle\" (using the full term).\\n\\nI think using the abbreviation \"IA\" would be more natural and commonly used, similar to how we use \"AI\" in English.', 'signature': 'EoMFCkgIBRABGAIqQOwp9d0YWm8NctfL9lf1MeWR1OxeAKB3Es19Lei2bdHQ4W0ezTK4wVcm/VLM+7kICX2aB9RAmUD5sJxoKHfdX38SDIR/aSJhHZifGOHqwBoMhzNsyPmB7FFNvNESIjBMVRpRUDTFGn5+nL0x5CjWhKA8H/XFnKYRrUyMYb1n7lCQA7BeEjsaWwxZ3YV9rZsq6APuaXaA40Bt+KnpPOo06r72L/DceliRAw1a6cuT5E0Dv0eIAOYblbXaKYn0jy8UzTUuctOP3As/zT5pK5yC+Rx0d2l9kuP3+COERM98u0R04bWn6qh0HcyE+zNc7c4YWkncjdmOxF/j6OxhcMhZEoX2035v9eUJ9+O/u1xaff08YAEfg7TGWrSIwalpjs1mzWA9ijKg8YyjmXjWnMeFn0z6LDqLaaKc+nC8IN9SLwA/eHpf/ayoEgmogn7gWzijW8MDbnlwpQDS75wK7An3RMEcpWD/OXrKb1EhWKEmOBro5BOTGsfK3ZDveRL0aCBINdOu+AHMQDFXJ04cRDEjs9GE3YC218UcFtS42TFO7/Ct5CYCTknETPx93zcGTOM2VPOZ02Uem1A7Nda/Fa4l2b03EUEtwlgske5K1RbeohN9sclxYsxX5nGJ5sSZurVCk9plkyTG3aiPvbohfVVarVgukKoKwoMDYz5rHVscWlUe+qeqJE/H+KKlhtzO+lWWDN4knqeYsZ55flO5Hq4vT20QCYnF8hcUx07ngGKXuGID9n5kFnLsP8sBUHYKm7bmopFFZvfPcmsqiV9yvG/8Ly9DHbmY5ZwxyrbdJCFT6HD6kq/mEBDftZ6dhmyKMimJBfbTj7d3VAILbRgB'}}, {'type': 'text', 'text': \"J'aime l'IA.\"}]\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "id": "a77519e5-897d-41a0-a9bb-55300fa79efc", "metadata": {}, "source": ["## Prompt caching\n", "\n", "Bedrock supports [caching](https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-caching.html) of elements of your prompts, including messages and tools. This allows you to re-use large documents, instructions, [few-shot documents](/docs/concepts/few_shot_prompting/), and other data to reduce latency and costs.\n", "\n", ":::note\n", "\n", "Not all models support prompt caching. See supported models [here](https://docs.aws.amazon.com/bedrock/latest/userguide/prompt-caching.html#prompt-caching-models).\n", "\n", ":::\n", "\n", "To enable caching on an element of a prompt, mark its associated content block using the `cachePoint` key. See example below:"]}, {"cell_type": "code", "execution_count": 1, "id": "d5f63d01-85e8-4797-a2be-0fea747a6049", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First invocation:\n", "{'cache_creation': 1528, 'cache_read': 0}\n", "\n", "Second:\n", "{'cache_creation': 0, 'cache_read': 1528}\n"]}], "source": ["import requests\n", "from langchain_aws import ChatBedrockConverse\n", "\n", "llm = ChatBedrockConverse(model=\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\")\n", "\n", "# <PERSON><PERSON><PERSON> readme\n", "get_response = requests.get(\n", "    \"https://raw.githubusercontent.com/langchain-ai/langchain/master/README.md\"\n", ")\n", "readme = get_response.text\n", "\n", "messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": [\n", "            {\n", "                \"type\": \"text\",\n", "                \"text\": \"What's <PERSON><PERSON><PERSON><PERSON>, according to its README?\",\n", "            },\n", "            {\n", "                \"type\": \"text\",\n", "                \"text\": f\"{readme}\",\n", "            },\n", "            {\n", "                \"cachePoint\": {\"type\": \"default\"},\n", "            },\n", "        ],\n", "    },\n", "]\n", "\n", "response_1 = llm.invoke(messages)\n", "response_2 = llm.invoke(messages)\n", "\n", "usage_1 = response_1.usage_metadata[\"input_token_details\"]\n", "usage_2 = response_2.usage_metadata[\"input_token_details\"]\n", "\n", "print(f\"First invocation:\\n{usage_1}\")\n", "print(f\"\\nSecond:\\n{usage_2}\")"]}, {"cell_type": "markdown", "id": "1b550667-af5b-4557-b84f-c8f865dad6cb", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 7, "id": "6033f3fa-0e96-46e3-abb3-1530928fea88", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"Here's the German translation:\\n\\nIch liebe das Programmieren.\", additional_kwargs={}, response_metadata={'ResponseMetadata': {'RequestId': '1de3d7c0-8062-4f7e-bb8a-8f725b97a8b0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 16 Apr 2025 19:32:51 GMT', 'content-type': 'application/json', 'content-length': '243', 'connection': 'keep-alive', 'x-amzn-requestid': '1de3d7c0-8062-4f7e-bb8a-8f725b97a8b0'}, 'RetryAttempts': 0}, 'stopReason': 'end_turn', 'metrics': {'latencyMs': [719]}, 'model_name': 'anthropic.claude-3-5-sonnet-20240620-v1:0'}, id='run-7021fcd7-704e-496b-a92e-210139614402-0', usage_metadata={'input_tokens': 23, 'output_tokens': 19, 'total_tokens': 42, 'input_token_details': {'cache_creation': 0, 'cache_read': 0}})"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatBedrock features and configurations head to the API reference: https://python.langchain.com/api_reference/aws/chat_models/langchain_aws.chat_models.bedrock.ChatBedrock.html\n", "\n", "For detailed documentation of all ChatBedrockConverse features and configurations head to the API reference: https://python.langchain.com/api_reference/aws/chat_models/langchain_aws.chat_models.bedrock_converse.ChatBedrockConverse.html"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}