{"cells": [{"cell_type": "raw", "id": "b8782af6-f49e-40da-9f7a-4765113b17ed", "metadata": {}, "source": ["---\n", "sidebar_class_name: hidden\n", "---"]}, {"cell_type": "markdown", "id": "5125a1e3", "metadata": {}, "source": ["# [Deprecated] Experimental Anthropic Tools Wrapper\n", "\n", ":::warning\n", "\n", "The Anthropic API officially supports tool-calling so this workaround is no longer needed. Please use [ChatAnthropic](/docs/integrations/chat/anthropic) with `langchain-anthropic>=0.1.15`.\n", "\n", ":::\n", "\n", "This notebook shows how to use an experimental wrapper around Anthropic that gives it tool calling and structured output capabilities. It follows Anthropic's guide [here](https://docs.anthropic.com/claude/docs/functions-external-tools)\n", "\n", "The wrapper is available from the `langchain-anthropic` package, and it also requires the optional dependency `defusedxml` for parsing XML output from the llm.\n", "\n", "Note: this is a beta feature that will be replaced by <PERSON><PERSON><PERSON>'s formal implementation of tool calling, but it is useful for testing and experimentation in the meantime."]}, {"cell_type": "code", "execution_count": 1, "id": "378be79b", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-anthropic defusedxml\n", "from langchain_anthropic.experimental import ChatAnthropicTools"]}, {"cell_type": "markdown", "id": "65499965", "metadata": {}, "source": ["## Tool Binding\n", "\n", "`ChatAnthropicTools` exposes a `bind_tools` method that allows you to pass in Pydantic models or BaseTools to the llm."]}, {"cell_type": "code", "execution_count": 3, "id": "e1d535f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', additional_kwargs={'tool_calls': [{'function': {'name': 'Person', 'arguments': '{\"name\": \"<PERSON><PERSON>\", \"age\": \"27\"}'}, 'type': 'function'}]})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from pydantic import BaseModel\n", "\n", "\n", "class Person(BaseModel):\n", "    name: str\n", "    age: int\n", "\n", "\n", "model = ChatAnthropicTools(model=\"claude-3-opus-20240229\").bind_tools(tools=[Person])\n", "model.invoke(\"I am a 27 year old named <PERSON><PERSON>\")"]}, {"cell_type": "markdown", "id": "fcc9eaf4", "metadata": {}, "source": ["## Structured Output\n", "\n", "`ChatAnthropicTools` also implements the [`with_structured_output` spec](/docs/how_to/structured_output) for extracting values. Note: this may not be as stable as with models that explicitly offer tool calling."]}, {"cell_type": "code", "execution_count": 4, "id": "0779c320", "metadata": {}, "outputs": [{"data": {"text/plain": ["Person(name='<PERSON><PERSON>', age=27)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["chain = ChatAnthropicTools(model=\"claude-3-opus-20240229\").with_structured_output(\n", "    Person\n", ")\n", "chain.invoke(\"I am a 27 year old named <PERSON><PERSON>\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}