{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: <PERSON><PERSON><PERSON>\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Chat with Baichuan-192K\n", "\n", "Baichuan chat models API by Baichuan Intelligent Technology. For more information, see [https://platform.baichuan-ai.com/docs/api](https://platform.baichuan-ai.com/docs/api)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2023-10-17T15:14:24.186131Z", "start_time": "2023-10-17T15:14:23.831767Z"}}, "outputs": [], "source": ["from langchain_community.chat_models import ChatBaichuan\n", "from langchain_core.messages import HumanMessage"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2023-10-17T15:14:24.191123Z", "start_time": "2023-10-17T15:14:24.186330Z"}}, "outputs": [], "source": ["chat = ChatBaichuan(baichuan_api_key=\"YOUR_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, you can set your API key with:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"BAICHUAN_API_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2023-10-17T15:14:25.853218Z", "start_time": "2023-10-17T15:14:24.192408Z"}}, "outputs": [{"data": {"text/plain": ["AIMessage(content='首先，我们需要确定闰年的二月有多少天。闰年的二月有29天。\\n\\n然后，我们可以计算你的月薪：\\n\\n日薪 = 月薪 / (当月天数)\\n\\n所以，你的月薪 = 日薪 * 当月天数\\n\\n将数值代入公式：\\n\\n月薪 = 8元/天 * 29天 = 232元\\n\\n因此，你在闰年的二月的月薪是232元。')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["chat([HumanMessage(content=\"我日薪8块钱，请问在闰年的二月，我月薪多少\")])"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["## Chat with Baichuan-192K with Streaming"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2023-10-17T15:14:25.870044Z", "start_time": "2023-10-17T15:14:25.863381Z"}, "collapsed": false}, "outputs": [], "source": ["chat = ChatBaichuan(\n", "    baichuan_api_key=\"YOUR_API_KEY\",\n", "    streaming=True,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2023-10-17T15:14:27.153546Z", "start_time": "2023-10-17T15:14:25.868470Z"}, "collapsed": false}, "outputs": [{"data": {"text/plain": ["AIMessageChunk(content='首先，我们需要确定闰年的二月有多少天。闰年的二月有29天。\\n\\n然后，我们可以计算你的月薪：\\n\\n日薪 = 月薪 / (当月天数)\\n\\n所以，你的月薪 = 日薪 * 当月天数\\n\\n将数值代入公式：\\n\\n月薪 = 8元/天 * 29天 = 232元\\n\\n因此，你在闰年的二月的月薪是232元。')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["chat([HumanMessage(content=\"我日薪8块钱，请问在闰年的二月，我月薪多少\")])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}