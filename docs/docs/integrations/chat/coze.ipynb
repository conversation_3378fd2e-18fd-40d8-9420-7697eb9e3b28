{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: <PERSON><PERSON>\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Chat with <PERSON><PERSON>\n", "\n", "ChatCoze chat models API by coze.com. For more information, see [https://www.coze.com/open/docs/chat](https://www.coze.com/open/docs/chat)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T15:14:24.186131Z", "start_time": "2024-04-25T15:14:23.831767Z"}}, "outputs": [], "source": ["from langchain_community.chat_models import ChatCoze\n", "from langchain_core.messages import HumanMessage"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T15:14:24.191123Z", "start_time": "2024-04-25T15:14:24.186330Z"}}, "outputs": [], "source": ["chat = ChatCoze(\n", "    coze_api_base=\"YOUR_API_BASE\",\n", "    coze_api_key=\"YOUR_API_KEY\",\n", "    bot_id=\"YOUR_BOT_ID\",\n", "    user=\"YOUR_USER_ID\",\n", "    conversation_id=\"YOUR_CONVERSATION_ID\",\n", "    streaming=False,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, you can set your API key and API base with:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"COZE_API_KEY\"] = \"YOUR_API_KEY\"\n", "os.environ[\"COZE_API_BASE\"] = \"YOUR_API_BASE\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T15:14:25.853218Z", "start_time": "2024-04-25T15:14:24.192408Z"}}, "outputs": [{"data": {"text/plain": ["AIMessage(content='为你找到关于coze的信息如下：\n", "\n", "Coze是一个由字节跳动推出的AI聊天机器人和应用程序编辑开发平台。\n", "\n", "用户无论是否有编程经验，都可以通过该平台快速创建各种类型的聊天机器人、智能体、AI应用和插件，并将其部署在社交平台和即时聊天应用程序中。\n", "\n", "国际版使用的模型比国内版更强大。')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["chat([HumanMessage(content=\"什么是扣子(coze)\")])"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["## Chat with <PERSON><PERSON>ing"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T15:14:25.870044Z", "start_time": "2024-04-25T15:14:25.863381Z"}, "collapsed": false}, "outputs": [], "source": ["chat = ChatCoze(\n", "    coze_api_base=\"YOUR_API_BASE\",\n", "    coze_api_key=\"YOUR_API_KEY\",\n", "    bot_id=\"YOUR_BOT_ID\",\n", "    user=\"YOUR_USER_ID\",\n", "    conversation_id=\"YOUR_CONVERSATION_ID\",\n", "    streaming=True,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2024-04-25T15:14:27.153546Z", "start_time": "2024-04-25T15:14:25.868470Z"}, "collapsed": false}, "outputs": [{"data": {"text/plain": ["AIMessageChunk(content='为你查询到Coze是一个由字节跳动推出的AI聊天机器人和应用程序编辑开发平台。')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["chat([HumanMessage(content=\"什么是扣子(coze)\")])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}