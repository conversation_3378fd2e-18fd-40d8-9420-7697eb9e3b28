{"cells": [{"cell_type": "raw", "id": "71b5cfca", "metadata": {}, "source": ["---\n", "sidebar_label: Llama API\n", "---"]}, {"cell_type": "markdown", "id": "90a1faf2", "metadata": {}, "source": ["# ChatLlamaAPI\n", "\n", "This notebook shows how to use Lang<PERSON>hain with [LlamaAPI](https://llama-api.com/) - a hosted version of Llama2 that adds in support for function calling."]}, {"cell_type": "markdown", "id": "f5b652cf", "metadata": {}, "source": ["%pip install --upgrade --quiet  llamaapi"]}, {"cell_type": "code", "execution_count": 2, "id": "bfd385fd", "metadata": {}, "outputs": [], "source": ["from llamaapi import LlamaAPI\n", "\n", "# Replace 'Your_API_Token' with your actual API token\n", "llama = LlamaAPI(\"Your_API_Token\")"]}, {"cell_type": "code", "execution_count": 4, "id": "632eb3e5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.9.1/envs/langchain/lib/python3.9/site-packages/deeplake/util/check_latest_version.py:32: UserWarning: A newer version of deeplake (3.6.12) is available. It's recommended that you update to the latest version using `pip install -U deeplake`.\n", "  warnings.warn(\n"]}], "source": ["from langchain_experimental.llms import ChatLlamaAPI"]}, {"cell_type": "code", "execution_count": 5, "id": "6f850e82", "metadata": {}, "outputs": [], "source": ["model = ChatLlamaAPI(client=llama)"]}, {"cell_type": "code", "execution_count": 6, "id": "975c2bf4", "metadata": {}, "outputs": [], "source": ["from langchain.chains import create_tagging_chain\n", "\n", "schema = {\n", "    \"properties\": {\n", "        \"sentiment\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"the sentiment encountered in the passage\",\n", "        },\n", "        \"aggressiveness\": {\n", "            \"type\": \"integer\",\n", "            \"description\": \"a 0-10 score of how aggressive the passage is\",\n", "        },\n", "        \"language\": {\"type\": \"string\", \"description\": \"the language of the passage\"},\n", "    }\n", "}\n", "\n", "chain = create_tagging_chain(schema, model)"]}, {"cell_type": "code", "execution_count": 7, "id": "ef9638c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'sentiment': 'aggressive', 'aggressiveness': 8, 'language': 'english'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.run(\"give me your money\")"]}, {"cell_type": "code", "execution_count": null, "id": "238b4f62", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 5}