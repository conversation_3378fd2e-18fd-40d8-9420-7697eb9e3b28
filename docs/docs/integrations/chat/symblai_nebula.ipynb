{"cells": [{"cell_type": "raw", "id": "53fbf15f", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Nebula (Symbl.ai)\n", "---"]}, {"cell_type": "markdown", "id": "bf733a38-db84-4363-89e2-de6735c37230", "metadata": {}, "source": ["# Nebula (Symbl.ai)\n", "\n", "This notebook covers how to get started with [Nebula](https://docs.symbl.ai/docs/nebula-llm) - Symbl.ai's chat model.\n", "\n", "### Integration details\n", "Head to the [API reference](https://docs.symbl.ai/reference/nebula-chat) for detailed documentation.\n", "\n", "### Model features: TODO"]}, {"cell_type": "markdown", "id": "3607d67e-e56c-4102-bbba-df2edc0e109e", "metadata": {}, "source": ["## Setup\n", "\n", "### Credentials\n", "To get started, request a [Nebula API key](https://platform.symbl.ai/#/login) and set the `NEBULA_API_KEY` environment variable:"]}, {"cell_type": "code", "execution_count": 2, "id": "2108b517-1e8d-473d-92fa-4f930e8072a7", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "os.environ[\"NEBULA_API_KEY\"] = getpass.getpass()"]}, {"cell_type": "markdown", "id": "68b44357", "metadata": {}, "source": ["### Installation\n", "The integration is set up in the `langchain-community` package."]}, {"cell_type": "markdown", "id": "4c26754b-b3c9-4d93-8f36-43049bd943bf", "metadata": {}, "source": ["## Instantiation"]}, {"cell_type": "code", "execution_count": null, "id": "0fdd26e7", "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models.symblai_nebula import ChatNebula\n", "from langchain_core.messages import AIMessage, HumanMessage, SystemMessage"]}, {"cell_type": "code", "execution_count": 4, "id": "70cf04e8-423a-4ff6-8b09-f11fb711c817", "metadata": {"tags": []}, "outputs": [], "source": ["chat = ChatNebula(max_tokens=1024, temperature=0.5)"]}, {"cell_type": "markdown", "id": "2a915547", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 5, "id": "8199ef8f-eb8b-4253-9ea0-6c24a013ca4c", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content=[{'role': 'human', 'text': 'What is the capital of France?'}, {'role': 'assistant', 'text': 'The capital of France is Paris.'}])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    SystemMessage(\n", "        content=\"You are a helpful assistant that answers general knowledge questions.\"\n", "    ),\n", "    HumanMessage(content=\"What is the capital of France?\"),\n", "]\n", "chat.invoke(messages)"]}, {"cell_type": "markdown", "id": "9723913f", "metadata": {}, "source": ["### Async"]}, {"cell_type": "code", "execution_count": 6, "id": "c5fac0e9-05a4-4fc1-a3b3-e5bbb24b971b", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content=[{'role': 'human', 'text': 'What is the capital of France?'}, {'role': 'assistant', 'text': 'The capital of France is Paris.'}])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["await chat.ainvoke(messages)"]}, {"cell_type": "markdown", "id": "e0a1d3b4", "metadata": {}, "source": ["### Streaming"]}, {"cell_type": "code", "execution_count": 9, "id": "025be980-e50d-4a68-93dc-c9c7b500ce34", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" The capital of France is Paris."]}], "source": ["for chunk in chat.stream(messages):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "markdown", "id": "9f91b7c7", "metadata": {}, "source": ["### <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 12, "id": "054dc648", "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content=[{'role': 'human', 'text': 'What is the capital of France?'}, {'role': 'assistant', 'text': 'The capital of France is Paris.'}])]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.batch([messages])"]}, {"cell_type": "markdown", "id": "e59a5519", "metadata": {}, "source": ["## Chaining"]}, {"cell_type": "code", "execution_count": 18, "id": "6455f67b", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_template(\"Tell me a joke about {topic}\")\n", "chain = prompt | chat"]}, {"cell_type": "code", "execution_count": 19, "id": "deb1e2a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=[{'role': 'human', 'text': 'Tell me a joke about cows'}, {'role': 'assistant', 'text': \"Sure, here's a joke about cows:\\n\\nWhy did the cow cross the road?\\n\\nTo get to the udder side!\"}])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke({\"topic\": \"cows\"})"]}, {"cell_type": "markdown", "id": "bb9d4755", "metadata": {}, "source": ["## API reference\n", "\n", "Check out the [API reference](https://python.langchain.com/api_reference/community/chat_models/langchain_community.chat_models.symblai_nebula.ChatNebula.html) for more detail."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}