{"cells": [{"cell_type": "raw", "id": "1c95cd76", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: IBM watsonx.ai\n", "---"]}, {"cell_type": "markdown", "id": "70996d8a", "metadata": {}, "source": ["# ChatWatsonx\n", "\n", ">ChatWatsonx is a wrapper for IBM [watsonx.ai](https://www.ibm.com/products/watsonx-ai) foundation models.\n", "\n", "The aim of these examples is to show how to communicate with `watsonx.ai` models using `LangChain` LLMs API."]}, {"cell_type": "markdown", "id": "ef7b088a", "metadata": {}, "source": ["## Overview\n", "\n", "### Integration details\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/chat/ibm/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatWatsonx](https://python.langchain.com/api_reference/ibm/chat_models/langchain_ibm.chat_models.ChatWatsonx.html) | [langchain-ibm](https://python.langchain.com/api_reference/ibm/index.html) | ❌ | ❌ | ✅ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-ibm?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-ibm?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling/) | [Structured output](/docs/how_to/structured_output/) | JSON mode | Image input | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ✅ | ✅ | ❌ | ❌ | ✅ | ❌ | ✅ | ✅ | "]}, {"cell_type": "markdown", "id": "f406e092", "metadata": {}, "source": ["## Setup\n", "\n", "To access IBM watsonx.ai models you'll need to create an IBM watsonx.ai account, get an API key, and install the `langchain-ibm` integration package.\n", "\n", "### Credentials\n", "\n", "The cell below defines the credentials required to work with watsonx Foundation Model inferencing.\n", "\n", "**Action:** Provide the IBM Cloud user API key. For details, see\n", "[Managing user API keys](https://cloud.ibm.com/docs/account?topic=account-userapikey&interface=ui)."]}, {"cell_type": "code", "execution_count": 2, "id": "11d572a1", "metadata": {}, "outputs": [], "source": ["import os\n", "from getpass import getpass\n", "\n", "watsonx_api_key = getpass()\n", "os.environ[\"WATSONX_APIKEY\"] = watsonx_api_key"]}, {"cell_type": "markdown", "id": "c59782a7", "metadata": {}, "source": ["Additionally you are able to pass additional secrets as an environment variable. "]}, {"cell_type": "code", "execution_count": null, "id": "f98c573c", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"WATSONX_URL\"] = \"your service instance url\"\n", "os.environ[\"WATSONX_TOKEN\"] = \"your token for accessing the CPD cluster\"\n", "os.environ[\"WATSONX_PASSWORD\"] = \"your password for accessing the CPD cluster\"\n", "os.environ[\"WATSONX_USERNAME\"] = \"your username for accessing the CPD cluster\"\n", "os.environ[\"WATSONX_INSTANCE_ID\"] = \"your instance_id for accessing the CPD cluster\""]}, {"cell_type": "markdown", "id": "b3dc9176", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain IBM integration lives in the `langchain-ibm` package:"]}, {"cell_type": "code", "execution_count": null, "id": "387eda86", "metadata": {}, "outputs": [], "source": ["!pip install -qU langchain-ibm"]}, {"cell_type": "markdown", "id": "e36acbef", "metadata": {}, "source": ["## Instantiation\n", "\n", "You might need to adjust model `parameters` for different models or tasks. For details, refer to [Available TextChatParameters](https://ibm.github.io/watsonx-ai-python-sdk/fm_schema.html#ibm_watsonx_ai.foundation_models.schema.TextChatParameters)."]}, {"cell_type": "code", "execution_count": 5, "id": "407cd500", "metadata": {}, "outputs": [], "source": ["parameters = {\n", "    \"temperature\": 0.9,\n", "    \"max_tokens\": 200,\n", "}"]}, {"cell_type": "markdown", "id": "2b586538", "metadata": {}, "source": ["Initialize the `WatsonxLLM` class with the previously set parameters.\n", "\n", "\n", "**Note**: \n", "\n", "- To provide context for the API call, you must pass the `project_id` or `space_id`. To get your project or space ID, open your project or space, go to the **Manage** tab, and click **General**. For more information see: [Project documentation](https://www.ibm.com/docs/en/watsonx-as-a-service?topic=projects) or [Deployment space documentation](https://www.ibm.com/docs/en/watsonx/saas?topic=spaces-creating-deployment).\n", "- Depending on the region of your provisioned service instance, use one of the urls listed in [watsonx.ai API Authentication](https://ibm.github.io/watsonx-ai-python-sdk/setup_cloud.html#authentication).\n", "\n", "In this example, we’ll use the `project_id` and Dallas URL.\n", "\n", "\n", "You need to specify the `model_id` that will be used for inferencing. You can find the list of all the available models in [Supported chat models](https://ibm.github.io/watsonx-ai-python-sdk/fm_helpers.html#ibm_watsonx_ai.foundation_models_manager.FoundationModelsManager.get_chat_model_specs)."]}, {"cell_type": "code", "execution_count": null, "id": "e3568e91", "metadata": {}, "outputs": [], "source": ["from langchain_ibm import ChatWatsonx\n", "\n", "chat = ChatWatsonx(\n", "    model_id=\"ibm/granite-34b-code-instruct\",\n", "    url=\"https://us-south.ml.cloud.ibm.com\",\n", "    project_id=\"PASTE YOUR PROJECT_ID HERE\",\n", "    params=parameters,\n", ")"]}, {"cell_type": "markdown", "id": "2202f4e0", "metadata": {}, "source": ["Alternatively, you can use Cloud Pak for Data credentials. For details, see [watsonx.ai software setup](https://ibm.github.io/watsonx-ai-python-sdk/setup_cpd.html).  "]}, {"cell_type": "code", "execution_count": null, "id": "243ecccb", "metadata": {}, "outputs": [], "source": ["chat = ChatWatsonx(\n", "    model_id=\"ibm/granite-34b-code-instruct\",\n", "    url=\"PASTE YOUR URL HERE\",\n", "    username=\"PASTE YOUR USERNAME HERE\",\n", "    password=\"PASTE YOUR PASSWORD HERE\",\n", "    instance_id=\"openshift\",\n", "    version=\"4.8\",\n", "    project_id=\"PASTE YOUR PROJECT_ID HERE\",\n", "    params=parameters,\n", ")"]}, {"cell_type": "markdown", "id": "96ed13d4", "metadata": {}, "source": ["Instead of `model_id`, you can also pass the `deployment_id` of the previously [deployed model with reference to a Prompt Template](https://cloud.ibm.com/apidocs/watsonx-ai#deployments-text-chat)."]}, {"cell_type": "code", "execution_count": null, "id": "08e66c88", "metadata": {}, "outputs": [], "source": ["chat = ChatWatsonx(\n", "    deployment_id=\"PASTE YOUR DEPLOYMENT_ID HERE\",\n", "    url=\"https://us-south.ml.cloud.ibm.com\",\n", "    project_id=\"PASTE YOUR PROJECT_ID HERE\",\n", "    params=parameters,\n", ")"]}, {"cell_type": "markdown", "id": "3d29767c", "metadata": {}, "source": ["For certain requirements, there is an option to pass the IBM's [`APIClient`](https://ibm.github.io/watsonx-ai-python-sdk/base.html#apiclient) object into the `ChatWatsonx` class."]}, {"cell_type": "code", "execution_count": null, "id": "0ae9531e", "metadata": {}, "outputs": [], "source": ["from ibm_watsonx_ai import APIClient\n", "\n", "api_client = APIClient(...)\n", "\n", "chat = ChatWatsonx(\n", "    model_id=\"ibm/granite-34b-code-instruct\",\n", "    watsonx_client=api_client,\n", ")"]}, {"cell_type": "markdown", "id": "f571001d", "metadata": {}, "source": ["## Invocation\n", "\n", "To obtain completions, you can call the model directly using a string prompt."]}, {"cell_type": "code", "execution_count": 8, "id": "beea2b5b", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"J'adore que tu escois de écouter de la rock ! \", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 19, 'prompt_tokens': 34, 'total_tokens': 53}, 'model_name': 'ibm/granite-34b-code-instruct', 'system_fingerprint': '', 'finish_reason': 'stop'}, id='chat-ef888fc41f0d4b37903b622250ff7528', usage_metadata={'input_tokens': 34, 'output_tokens': 19, 'total_tokens': 53})"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Invocation\n", "\n", "messages = [\n", "    (\"system\", \"You are a helpful assistant that translates English to French.\"),\n", "    (\n", "        \"human\",\n", "        \"I love you for listening to <PERSON>.\",\n", "    ),\n", "]\n", "\n", "chat.invoke(messages)"]}, {"cell_type": "code", "execution_count": 9, "id": "8ab1a25a", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='horses are quadrupedal mammals that are members of the family Equidae. They are typically farm animals, competing in horse racing and other forms of equine competition. With over 200 breeds, horses are diverse in their physical appearance and behavior. They are intelligent, social animals that are often used for transportation, food, and entertainment.', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 89, 'prompt_tokens': 29, 'total_tokens': 118}, 'model_name': 'ibm/granite-34b-code-instruct', 'system_fingerprint': '', 'finish_reason': 'stop'}, id='chat-9a6e28abb3d448aaa4f83b677a9fd653', usage_metadata={'input_tokens': 29, 'output_tokens': 89, 'total_tokens': 118})"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Invocation multiple chat\n", "from langchain_core.messages import (\n", "    HumanMessage,\n", "    SystemMessage,\n", ")\n", "\n", "system_message = SystemMessage(\n", "    content=\"You are a helpful assistant which telling short-info about provided topic.\"\n", ")\n", "human_message = HumanMessage(content=\"horse\")\n", "\n", "chat.invoke([system_message, human_message])"]}, {"cell_type": "markdown", "id": "20e4b568", "metadata": {}, "source": ["## Chaining\n", "Create `ChatPromptTemplate` objects which will be responsible for creating a random question."]}, {"cell_type": "code", "execution_count": 10, "id": "dd919925", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "system = (\n", "    \"You are a helpful assistant that translates {input_language} to {output_language}.\"\n", ")\n", "human = \"{input}\"\n", "prompt = ChatPromptTemplate.from_messages([(\"system\", system), (\"human\", human)])"]}, {"cell_type": "markdown", "id": "1a013a53", "metadata": {}, "source": ["Provide a inputs and run the chain."]}, {"cell_type": "code", "execution_count": 11, "id": "68160377", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Ich liebe Python.', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 7, 'prompt_tokens': 28, 'total_tokens': 35}, 'model_name': 'ibm/granite-34b-code-instruct', 'system_fingerprint': '', 'finish_reason': 'stop'}, id='chat-fef871190b6047a7a3e68c58b3810c33', usage_metadata={'input_tokens': 28, 'output_tokens': 7, 'total_tokens': 35})"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["chain = prompt | chat\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love Python\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "d2c9da33", "metadata": {}, "source": ["## Streaming the Model output \n", "\n", "You can stream the model output."]}, {"cell_type": "code", "execution_count": 12, "id": "3f63166a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Moon is the fifth largest moon in the solar system and the largest relative to its host planet. It is the fifth brightest object in Earth's night sky after the Sun, the stars, the Milky Way, and the Moon itself. It orbits around the Earth at an average distance of 238,855 miles (384,400 kilometers). The Moon's gravity is about one-sixthth of Earth's and thus allows for the formation of tides on Earth. The Moon is thought to have formed around 4.5 billion years ago from debris from a collision between Earth and a Mars-sized body named Theia. The Moon is effectively immutable, with its current characteristics remaining from formation. Aside from Earth, the Moon is the only other natural satellite of Earth. The most widely accepted theory is that it formed from the debris of a collision"]}], "source": ["system_message = SystemMessage(\n", "    content=\"You are a helpful assistant which telling short-info about provided topic.\"\n", ")\n", "human_message = HumanMessage(content=\"moon\")\n", "\n", "for chunk in chat.stream([system_message, human_message]):\n", "    print(chunk.content, end=\"\")"]}, {"cell_type": "markdown", "id": "5a7a2aa1", "metadata": {}, "source": ["## Batch the Model output \n", "\n", "You can batch the model output."]}, {"cell_type": "code", "execution_count": 13, "id": "9e948729", "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content='The cat is a popular domesticated carnivorous mammal that belongs to the family Felidae. Cats arefriendly, intelligent, and independent animals that are well-known for their playful behavior, agility, and ability to hunt prey. cats come in a wide range of breeds, each with their own unique physical and behavioral characteristics. They are kept as pets worldwide due to their affectionate nature and companionship. Cats are important members of the household and are often involved in everything from childcare to entertainment.', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 127, 'prompt_tokens': 28, 'total_tokens': 155}, 'model_name': 'ibm/granite-34b-code-instruct', 'system_fingerprint': '', 'finish_reason': 'stop'}, id='chat-fa452af0a0fa4a668b6a704aecd7d718', usage_metadata={'input_tokens': 28, 'output_tokens': 127, 'total_tokens': 155}),\n", " AIMessage(content='Dogs are domesticated animals that belong to the Canidae family, also known as wolves. They are one of the most popular pets worldwide, known for their loyalty and affection towards their owners. Dogs come in various breeds, each with unique characteristics, and are trained for different purposes such as hunting, herding, or guarding. They require a lot of exercise and mental stimulation to stay healthy and happy, and they need proper training and socialization to be well-behaved. Dogs are also known for their playful and energetic nature, making them great companions for people of all ages.', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 144, 'prompt_tokens': 28, 'total_tokens': 172}, 'model_name': 'ibm/granite-34b-code-instruct', 'system_fingerprint': '', 'finish_reason': 'stop'}, id='chat-cae7663c50cf4f3499726821cc2f0ec7', usage_metadata={'input_tokens': 28, 'output_tokens': 144, 'total_tokens': 172})]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["message_1 = [\n", "    SystemMessage(\n", "        content=\"You are a helpful assistant which telling short-info about provided topic.\"\n", "    ),\n", "    HumanMessage(content=\"cat\"),\n", "]\n", "message_2 = [\n", "    SystemMessage(\n", "        content=\"You are a helpful assistant which telling short-info about provided topic.\"\n", "    ),\n", "    HumanMessage(content=\"dog\"),\n", "]\n", "\n", "chat.batch([message_1, message_2])"]}, {"cell_type": "markdown", "id": "c739e1fe", "metadata": {}, "source": ["## <PERSON><PERSON> calling\n", "\n", "### ChatWatsonx.bind_tools()"]}, {"cell_type": "code", "execution_count": null, "id": "328fce76", "metadata": {}, "outputs": [], "source": ["from langchain_ibm import ChatWatsonx\n", "\n", "chat = ChatWatsonx(\n", "    model_id=\"mistralai/mistral-large\",\n", "    url=\"https://us-south.ml.cloud.ibm.com\",\n", "    project_id=\"PASTE YOUR PROJECT_ID HERE\",\n", "    params=parameters,\n", ")"]}, {"cell_type": "code", "execution_count": 2, "id": "e1633a73", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class GetWeather(BaseModel):\n", "    \"\"\"Get the current weather in a given location\"\"\"\n", "\n", "    location: str = Field(..., description=\"The city and state, e.g. San Francisco, CA\")\n", "\n", "\n", "llm_with_tools = chat.bind_tools([GetWeather])"]}, {"cell_type": "code", "execution_count": 3, "id": "3bf9b8ab", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'chatcmpl-tool-6c06a19bbe824d78a322eb193dbde12d', 'type': 'function', 'function': {'name': 'GetWeat<PERSON>', 'arguments': '{\"location\": \"Los Angeles, CA\"}'}}, {'id': 'chatcmpl-tool-493542e46f1141bfbfeb5deae6c9e086', 'type': 'function', 'function': {'name': 'GetWeather', 'arguments': '{\"location\": \"New York, NY\"}'}}]}, response_metadata={'token_usage': {'completion_tokens': 46, 'prompt_tokens': 95, 'total_tokens': 141}, 'model_name': 'mistralai/mistral-large', 'system_fingerprint': '', 'finish_reason': 'tool_calls'}, id='chat-027f2bdb217e4238909cb26d3e8a8fbf', tool_calls=[{'name': '<PERSON><PERSON><PERSON><PERSON>', 'args': {'location': 'Los Angeles, CA'}, 'id': 'chatcmpl-tool-6c06a19bbe824d78a322eb193dbde12d', 'type': 'tool_call'}, {'name': 'GetWeather', 'args': {'location': 'New York, NY'}, 'id': 'chatcmpl-tool-493542e46f1141bfbfeb5deae6c9e086', 'type': 'tool_call'}], usage_metadata={'input_tokens': 95, 'output_tokens': 46, 'total_tokens': 141})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ai_msg = llm_with_tools.invoke(\n", "    \"Which city is hotter today: LA or NY?\",\n", ")\n", "ai_msg"]}, {"cell_type": "markdown", "id": "ba03dbf4", "metadata": {}, "source": ["### AIMessage.tool_calls\n", "Notice that the AIMessage has a `tool_calls` attribute. This contains in a standardized ToolCall format that is model-provider agnostic."]}, {"cell_type": "code", "execution_count": 4, "id": "38f10ba7", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': '<PERSON><PERSON><PERSON><PERSON>',\n", "  'args': {'location': 'Los Angeles, CA'},\n", "  'id': 'chatcmpl-tool-6c06a19bbe824d78a322eb193dbde12d',\n", "  'type': 'tool_call'},\n", " {'name': '<PERSON><PERSON><PERSON><PERSON>',\n", "  'args': {'location': 'New York, NY'},\n", "  'id': 'chatcmpl-tool-493542e46f1141bfbfeb5deae6c9e086',\n", "  'type': 'tool_call'}]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["ai_msg.tool_calls"]}, {"cell_type": "markdown", "id": "95fcbf93", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `ChatWatsonx` features and configurations head to the [API reference](https://python.langchain.com/api_reference/ibm/chat_models/langchain_ibm.chat_models.ChatWatsonx.html)."]}], "metadata": {"kernelspec": {"display_name": "langchain_ibm", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}