{"cells": [{"cell_type": "raw", "id": "910f5772b6af13c9", "metadata": {"collapsed": false}, "source": ["---\n", "sidebar_label: Upstage\n", "---"]}, {"cell_type": "markdown", "id": "433f5422ad8e1efa", "metadata": {"collapsed": false}, "source": ["# ChatUpstage\n", "\n", "This notebook covers how to get started with Upstage chat models.\n", "\n", "## Installation\n", "\n", "Install `langchain-upstage` package.\n", "\n", "```bash\n", "pip install -U langchain-upstage\n", "```"]}, {"cell_type": "markdown", "id": "b3c5c4627fe95eae", "metadata": {"collapsed": false}, "source": ["## Environment Setup\n", "\n", "Make sure to set the following environment variables:\n", "\n", "- `UPSTAGE_API_KEY`: Your Upstage API key from [Upstage console](https://console.upstage.ai/).\n", "\n", "## Usage"]}, {"cell_type": "code", "execution_count": null, "id": "20a0067b", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"UPSTAGE_API_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "id": "8a4d650d76a33494", "metadata": {"collapsed": false, "is_executing": true}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_upstage import ChatUpstage\n", "\n", "chat = ChatUpstage()"]}, {"cell_type": "code", "execution_count": null, "id": "a1679b5cafaf88b9", "metadata": {"collapsed": false}, "outputs": [], "source": ["# using chat invoke\n", "chat.invoke(\"Hello, how are you?\")"]}, {"cell_type": "code", "execution_count": null, "id": "698a788a63b5c3e5", "metadata": {"collapsed": false}, "outputs": [], "source": ["# using chat stream\n", "for m in chat.stream(\"Hello, how are you?\"):\n", "    print(m)"]}, {"cell_type": "markdown", "id": "36f8a703", "metadata": {}, "source": ["## Chaining"]}, {"cell_type": "code", "execution_count": null, "id": "efa06617e5d4f6b2", "metadata": {"collapsed": false}, "outputs": [], "source": ["# using chain\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant that translates English to French.\"),\n", "        (\"human\", \"Translate this sentence from English to French. {english_text}.\"),\n", "    ]\n", ")\n", "chain = prompt | chat\n", "\n", "chain.invoke({\"english_text\": \"Hello, how are you?\"})"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}