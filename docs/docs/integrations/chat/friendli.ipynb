{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: ChatFriendli\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatFriendli\n", "\n", "> [Friendli](https://friendli.ai/) enhances AI application performance and optimizes cost savings with scalable, efficient deployment options, tailored for high-demand AI workloads.\n", "\n", "This tutorial guides you through integrating `Chat<PERSON>riendli` for chat applications using LangChain. `Chat<PERSON>riendli` offers a flexible approach to generating conversational AI responses, supporting both synchronous and asynchronous calls."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "Ensure the `langchain_community` and `friendli-client` are installed.\n", "\n", "```sh\n", "pip install -U langchain-community friendli-client.\n", "```\n", "\n", "Sign in to [Friendli Suite](https://suite.friendli.ai/) to create a Personal Access Token, and set it as the `FRIENDLI_TOKEN` environment."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if \"FRIENDLI_TOKEN\" not in os.environ:\n", "    os.environ[\"FRIENDLI_TOKEN\"] = getpass.getpass(\"Friendi Personal Access Token: \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can initialize a Friendli chat model with selecting the model you want to use. The default model is `mixtral-8x7b-instruct-v0-1`. You can check the available models at [docs.friendli.ai](https://docs.periflow.ai/guides/serverless_endpoints/pricing#text-generation-models)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models.friendli import ChatFriendli\n", "\n", "chat = ChatFriendli(model=\"meta-llama-3.1-8b-instruct\", max_tokens=100, temperature=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Usage\n", "\n", "`FrienliChat` supports all methods of [`ChatModel`](/docs/how_to#chat-models) including async APIs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use functionality of  `invoke`, `batch`, `generate`, and `stream`."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-d47c1056-54e8-4ea9-ad63-07cf74b834b7-0')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages.human import HumanMessage\n", "from langchain_core.messages.system import SystemMessage\n", "\n", "system_message = SystemMessage(content=\"Answer questions as short as you can.\")\n", "human_message = HumanMessage(content=\"Tell me a joke.\")\n", "messages = [system_message, human_message]\n", "\n", "chat.invoke(messages)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-36775b84-2a7a-48f0-8c68-df23ffffe4b2-0'),\n", " AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-b204be41-bc06-4d3a-9f74-e66ab1e60e4f-0')]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.batch([messages, messages])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["LLMResult(generations=[[ChatGeneration(text=\"Why don't eggs tell jokes? They'd crack each other up.\", message=AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-2e4cb949-8c51-40d5-92a0-cd0ac577db83-0'))], [ChatGeneration(text=\"Why don't eggs tell jokes? They'd crack each other up.\", message=AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-afcdd1be-463c-4e50-9731-7a9f5958e396-0'))]], llm_output={}, run=[RunInfo(run_id=UUID('2e4cb949-8c51-40d5-92a0-cd0ac577db83')), RunInfo(run_id=UUID('afcdd1be-463c-4e50-9731-7a9f5958e396'))], type='LLMResult')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.generate([messages, messages])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Why don't eggs tell jokes? They'd crack each other up."]}], "source": ["for chunk in chat.stream(messages):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use all functionality of async APIs: `ainvoke`, `abatch`, `agenerate`, and `astream`."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-ba8062fb-68af-47b8-bd7b-d1e01b914744-0')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["await chat.ainvoke(messages)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-5d2c77ab-2637-45da-8bbe-1b1f18a22369-0'),\n", " AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-f1338470-8b52-4d6e-9428-a694a08ae484-0')]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["await chat.abatch([messages, messages])"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["LLMResult(generations=[[ChatGeneration(text=\"Why don't eggs tell jokes? They'd crack each other up.\", message=AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-d4e44569-39cc-40cc-93fc-de53e599fd51-0'))], [ChatGeneration(text=\"Why don't eggs tell jokes? They'd crack each other up.\", message=AIMessage(content=\"Why don't eggs tell jokes? They'd crack each other up.\", additional_kwargs={}, response_metadata={}, id='run-54647cc2-bee3-4154-ad00-2e547993e6d7-0'))]], llm_output={}, run=[RunInfo(run_id=UUID('d4e44569-39cc-40cc-93fc-de53e599fd51')), RunInfo(run_id=UUID('54647cc2-bee3-4154-ad00-2e547993e6d7'))], type='LLMResult')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["await chat.agenerate([messages, messages])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Why don't eggs tell jokes? They'd crack each other up."]}], "source": ["async for chunk in chat.astream(messages):\n", "    print(chunk.content, end=\"\", flush=True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}