{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Llama.cpp\n", "\n", ">[llama.cpp python](https://github.com/abetlen/llama-cpp-python) library is a simple Python bindings for `@ggerganov`\n", ">[llama.cpp](https://github.com/ggerganov/llama.cpp).\n", ">\n", ">This package provides:\n", ">\n", "> - Low-level access to C API via ctypes interface.\n", "> - High-level Python API for text completion\n", ">   - `OpenAI`-like API\n", ">   - `<PERSON><PERSON><PERSON><PERSON>` compatibility\n", ">   - `LlamaIndex` compatibility\n", "> - OpenAI compatible web server\n", ">   - Local Copilot replacement\n", ">   - Function Calling support\n", ">   - Vision API support\n", ">   - Multiple Models\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overview\n", "\n", "### Integration details\n", "| Class | Package | Local | Serializable | JS support |\n", "| :--- | :--- | :---: | :---: |  :---: |\n", "| [ChatLlamaCpp](https://python.langchain.com/api_reference/community/chat_models/langchain_community.chat_models.llamacpp.ChatLlamaCpp.html) | [langchain-community](https://python.langchain.com/api_reference/community/index.html) | ✅ | ❌ | ❌ |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | Image input | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ | ✅ | \n", "\n", "## Setup\n", "\n", "To get started and use **all** the features shown below, we recommend using a model that has been fine-tuned for tool-calling.\n", "\n", "We will use [\n", "Hermes-2-Pro-Llama-3-8B-GGUF](https://huggingface.co/NousResearch/Hermes-2-Pro-Llama-3-8B-GGUF) from NousResearch. \n", "\n", "> Hermes 2 Pro is an upgraded version of Nous Hermes 2, consisting of an updated and cleaned version of the OpenHermes 2.5 Dataset, as well as a newly introduced Function Calling and JSON Mode dataset developed in-house. This new version of Hermes maintains its excellent general task and conversation capabilities - but also excels at Function Calling\n", "\n", "See our guides on local models to go deeper:\n", "\n", "* [Run LLMs locally](https://python.langchain.com/v0.1/docs/guides/development/local_llms/)\n", "* [Using local models with RAG](https://python.langchain.com/v0.1/docs/use_cases/question_answering/local_retrieval_qa/)\n", "\n", "### Installation\n", "\n", "The LangChain LlamaCpp integration lives in the `langchain-community` and `llama-cpp-python` packages:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-community llama-cpp-python"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Path to your model weights\n", "local_model = \"local/path/to/Hermes-2-Pro-Llama-3-8B-Q8_0.gguf\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import multiprocessing\n", "\n", "from langchain_community.chat_models import ChatLlamaCpp\n", "\n", "llm = ChatLlamaCpp(\n", "    temperature=0.5,\n", "    model_path=local_model,\n", "    n_ctx=10000,\n", "    n_gpu_layers=8,\n", "    n_batch=300,  # Should be between 1 and n_ctx, consider the amount of VRAM in your GPU.\n", "    max_tokens=512,\n", "    n_threads=multiprocessing.cpu_count() - 1,\n", "    repeat_penalty=1.5,\n", "    top_p=0.5,\n", "    verbose=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'aime programmer. (In France, \"programming\" is often used in its original sense of scheduling or organizing events.) \n", "\n", "If you meant computer-programming: \n", "Je suis amoureux de la programmation informatique.\n", "\n", "(You might also say simply 'programmation', which would be understood as both meanings - depending on context).\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> calling\n", "\n", "Firstly, it works mostly the same as OpenAI Function Calling\n", "\n", "OpenAI has a [tool calling](https://platform.openai.com/docs/guides/function-calling) (we use \"tool calling\" and \"function calling\" interchangeably here) API that lets you describe tools and their arguments, and have the model return a JSON object with a tool to invoke and the inputs to that tool. tool-calling is extremely useful for building tool-using chains and agents, and for getting structured outputs from models more generally.\n", "\n", "With `ChatLlamaCpp.bind_tools`, we can easily pass in Pydantic classes, dict schemas, LangChain tools, or even functions as tools to the model. Under the hood, these are converted to an OpenAI tool schema, which looks like:\n", "```\n", "{\n", "    \"name\": \"...\",\n", "    \"description\": \"...\",\n", "    \"parameters\": {...}  # JSONSchema\n", "}\n", "```\n", "and passed in every model invocation.\n", "\n", "\n", "However, it cannot automatically trigger a function/tool, we need to force it by specifying the 'tool choice' parameter. This parameter is typically formatted as described below.\n", "\n", "```{\"type\": \"function\", \"function\": {\"name\": <<tool_name>>}}.```"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class WeatherInput(BaseModel):\n", "    location: str = Field(description=\"The city and state, e.g. San Francisco, CA\")\n", "    unit: str = Field(enum=[\"celsius\", \"fahrenheit\"])\n", "\n", "\n", "@tool(\"get_current_weather\", args_schema=WeatherInput)\n", "def get_weather(location: str, unit: str):\n", "    \"\"\"Get the current weather in a given location\"\"\"\n", "    return f\"Now the weather in {location} is 22 {unit}\"\n", "\n", "\n", "llm_with_tools = llm.bind_tools(\n", "    tools=[get_weather],\n", "    tool_choice={\"type\": \"function\", \"function\": {\"name\": \"get_current_weather\"}},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ai_msg = llm_with_tools.invoke(\n", "    \"what is the weather like in HCMC in celsius\",\n", ")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'get_current_weather',\n", "  'args': {'location': 'Ho Chi Minh City', 'unit': 'celsius'},\n", "  'id': 'call__0_get_current_weather_cmpl-394d9943-0a1f-425b-8139-d2826c1431f2'}]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["ai_msg.tool_calls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class MagicFunctionInput(BaseModel):\n", "    magic_function_input: int = Field(description=\"The input value for magic function\")\n", "\n", "\n", "@tool(\"get_magic_function\", args_schema=MagicFunctionInput)\n", "def magic_function(magic_function_input: int):\n", "    \"\"\"Get the value of magic function for an input.\"\"\"\n", "    return magic_function_input + 2\n", "\n", "\n", "llm_with_tools = llm.bind_tools(\n", "    tools=[magic_function],\n", "    tool_choice={\"type\": \"function\", \"function\": {\"name\": \"get_magic_function\"}},\n", ")\n", "\n", "ai_msg = llm_with_tools.invoke(\n", "    \"What is magic function of 3?\",\n", ")\n", "\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'get_magic_function',\n", "  'args': {'magic_function_input': 3},\n", "  'id': 'call__0_get_magic_function_cmpl-cd83a994-b820-4428-957c-48076c68335a'}]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["ai_msg.tool_calls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Structured output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.utils.function_calling import convert_to_openai_tool\n", "from pydantic import BaseModel\n", "\n", "\n", "class Joke(BaseModel):\n", "    \"\"\"A setup to a joke and the punchline.\"\"\"\n", "\n", "    setup: str\n", "    punchline: str\n", "\n", "\n", "dict_schema = convert_to_openai_tool(Joke)\n", "structured_llm = llm.with_structured_output(dict_schema)\n", "result = structured_llm.invoke(\"Tell me a joke about birds\")\n", "result"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'setup': '- Why did the chicken cross the playground?',\n", " 'punchline': '\\n\\n- To get to its gilded cage on the other side!'}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Streaming\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for chunk in llm.stream(\"what is 25x5\"):\n", "    print(chunk.content, end=\"\\n\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatLlamaCpp features and configurations, head to the API reference: https://python.langchain.com/api_reference/community/chat_models/langchain_community.chat_models.llamacpp.ChatLlamaCpp.html"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}