{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Snowflake Cortex\n", "\n", "[Snowflake Cortex](https://docs.snowflake.com/en/user-guide/snowflake-cortex/llm-functions) gives you instant access to industry-leading large language models (LLMs) trained by researchers at companies like Mistral, Reka, Meta, and Google, including [Snowflake Arctic](https://www.snowflake.com/en/data-cloud/arctic/), an open enterprise-grade model developed by Snowflake.\n", "\n", "This example goes over how to use LangChain to interact with Snowflake Cortex."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation and setup\n", "\n", "We start by installing the `snowflake-snowpark-python` library, using the command below. Then we configure the credentials for connecting to Snowflake, as environment variables or pass them directly."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install --upgrade --quiet snowflake-snowpark-python"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "# First step is to set up the environment variables, to connect to Snowflake,\n", "# you can also pass these snowflake credentials while instantiating the model\n", "\n", "if os.environ.get(\"SNOWFLAKE_ACCOUNT\") is None:\n", "    os.environ[\"SNOWFLAKE_ACCOUNT\"] = getpass.getpass(\"Account: \")\n", "\n", "if os.environ.get(\"SNOWFLAKE_USERNAME\") is None:\n", "    os.environ[\"SNOWFLAKE_USERNAME\"] = getpass.getpass(\"Username: \")\n", "\n", "if os.environ.get(\"SNOWFLAKE_PASSWORD\") is None:\n", "    os.environ[\"SNOWFLAKE_PASSWORD\"] = getpass.getpass(\"Password: \")\n", "\n", "if os.environ.get(\"SNOWFLAKE_DATABASE\") is None:\n", "    os.environ[\"SNOWFLAKE_DATABASE\"] = getpass.getpass(\"Database: \")\n", "\n", "if os.environ.get(\"SNOWFLAKE_SCHEMA\") is None:\n", "    os.environ[\"SNOWFLAKE_SCHEMA\"] = getpass.getpass(\"Schema: \")\n", "\n", "if os.environ.get(\"SNOWFLAKE_WAREHOUSE\") is None:\n", "    os.environ[\"SNOWFLAKE_WAREHOUSE\"] = getpass.getpass(\"Warehouse: \")\n", "\n", "if os.environ.get(\"SNOWFLAKE_ROLE\") is None:\n", "    os.environ[\"SNOWFLAKE_ROLE\"] = getpass.getpass(\"Role: \")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models import ChatSnowflakeCortex\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "# By default, we'll be using the cortex provided model: `mistral-large`, with function: `complete`\n", "chat = ChatSnowflakeCortex()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The above cell assumes that your Snowflake credentials are set in your environment variables. If you would rather manually specify them, use the following code:\n", "\n", "```python\n", "chat = ChatSnowflakeCortex(\n", "    # Change the default cortex model and function\n", "    model=\"mistral-large\",\n", "    cortex_function=\"complete\",\n", "\n", "    # Change the default generation parameters\n", "    temperature=0,\n", "    max_tokens=10,\n", "    top_p=0.95,\n", "\n", "    # Specify your Snowflake Credentials\n", "    account=\"YOUR_SNOWFLAKE_ACCOUNT\",\n", "    username=\"YOUR_SNOWFLAKE_USERNAME\",\n", "    password=\"YOUR_SNOWFLAKE_PASSWORD\",\n", "    database=\"YOUR_SNOWFLAKE_DATABASE\",\n", "    schema=\"YOUR_SNOWFLAKE_SCHEMA\",\n", "    role=\"YOUR_SNOWFLAKE_ROLE\",\n", "    warehouse=\"YOUR_SNOWFLAKE_WAREHOUSE\"\n", ")\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calling the chat model\n", "We can now call the chat model using the `invoke` or `stream` methods."]}, {"cell_type": "markdown", "metadata": {}, "source": ["messages = [\n", "    SystemMessage(content=\"You are a friendly assistant.\"),\n", "    HumanMessage(content=\"What are large language models?\"),\n", "]\n", "chat.invoke(messages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stream"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample input prompt\n", "messages = [\n", "    SystemMessage(content=\"You are a friendly assistant.\"),\n", "    HumanMessage(content=\"What are large language models?\"),\n", "]\n", "\n", "# Invoke the stream method and print each chunk as it arrives\n", "print(\"Stream Method Response:\")\n", "for chunk in chat._stream(messages):\n", "    print(chunk.message.content)"]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}