{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: <PERSON><PERSON>\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatKonko\n", "\n", "# <PERSON><PERSON>\n", "\n", ">[Ko<PERSON>](https://www.konko.ai/) API is a fully managed Web API designed to help application developers:\n", "\n", "\n", "1. **Select** the right open source or proprietary LLMs for their application\n", "2. **Build** applications faster with integrations to leading application frameworks and fully managed APIs\n", "3. **Fine tune** smaller open-source LLMs to achieve industry-leading performance at a fraction of the cost\n", "4. **Deploy production-scale APIs** that meet security, privacy, throughput, and latency SLAs without infrastructure set-up or administration using Konko AI's SOC 2 compliant, multi-cloud infrastructure\n", "\n", "\n", "This example goes over how to use <PERSON><PERSON><PERSON><PERSON> to interact with <PERSON><PERSON><PERSON>` ChatCompletion [models](https://docs.konko.ai/docs/list-of-models#konko-hosted-models-for-chatcompletion)\n", "\n", "To run this notebook, you'll need Konko API key. Sign in to our web app to [create an API key](https://platform.konko.ai/settings/api-keys) to access models\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_community.chat_models import ChatKonko\n", "from langchain_core.messages import HumanMessage, SystemMessage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Set Environment Variables\n", "\n", "1. You can set environment variables for \n", "   1. KONKO_API_KEY (Required)\n", "   2. OPENAI_API_KEY (Optional)\n", "2. In your current shell session, use the export command:\n", "\n", "```shell\n", "export KONKO_API_KEY={your_KONKO_API_KEY_here}\n", "export OPENAI_API_KEY={your_OPENAI_API_KEY_here} #Optional\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calling a model\n", "\n", "Find a model on the [<PERSON><PERSON> overview page](https://docs.konko.ai/docs/list-of-models)\n", "\n", "Another way to find the list of models running on the <PERSON><PERSON> instance is through this [endpoint](https://docs.konko.ai/reference/get-models).\n", "\n", "From here, we can initialize our model:\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["chat = ChatKonko(max_tokens=400, model=\"meta-llama/llama-2-13b-chat\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"  Sure thing! The Big Bang Theory is a scientific theory that explains the origins of the universe. In short, it suggests that the universe began as an infinitely hot and dense point around 13.8 billion years ago and expanded rapidly. This expansion continues to this day, and it's what makes the universe look the way it does.\\n\\nHere's a brief overview of the key points:\\n\\n1. The universe started as a singularity, a point of infinite density and temperature.\\n2. The singularity expanded rapidly, causing the universe to cool and expand.\\n3. As the universe expanded, particles began to form, including protons, neutrons, and electrons.\\n4. These particles eventually came together to form atoms, and later, stars and galaxies.\\n5. The universe is still expanding today, and the rate of this expansion is accelerating.\\n\\nThat's the Big Bang Theory in a nutshell! It's a pretty mind-blowing idea when you think about it, and it's supported by a lot of scientific evidence. Do you have any other questions about it?\")"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    SystemMessage(content=\"You are a helpful assistant.\"),\n", "    HumanMessage(content=\"Explain Big Bang Theory briefly\"),\n", "]\n", "chat(messages)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}, "vscode": {"interpreter": {"hash": "a0a0263b650d907a3bfe41c0f8d6a63a071b884df3cfdc1579f00cdc1aed6b03"}}}, "nbformat": 4, "nbformat_minor": 4}