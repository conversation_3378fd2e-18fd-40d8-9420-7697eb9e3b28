{"cells": [{"cell_type": "raw", "id": "66107bdd", "metadata": {}, "source": ["---\n", "sidebar_label: Volc Engine Maas\n", "---"]}, {"cell_type": "markdown", "id": "404758628c7b20f6", "metadata": {"collapsed": false}, "source": ["# VolcEngineMaasChat\n", "\n", "This notebook provides you with a guide on how to get started with volc engine maas chat models."]}, {"cell_type": "code", "execution_count": null, "id": "2cd2ebd9d023c4d3", "metadata": {"collapsed": false}, "outputs": [], "source": ["# Install the package\n", "%pip install --upgrade --quiet  volcengine"]}, {"cell_type": "code", "execution_count": 20, "id": "51e7f967cb78f5b7", "metadata": {"ExecuteTime": {"end_time": "2023-11-27T10:43:37.131292Z", "start_time": "2023-11-27T10:43:37.127250Z"}, "collapsed": false}, "outputs": [], "source": ["from langchain_community.chat_models import VolcEngineMaasChat\n", "from langchain_core.messages import HumanMessage"]}, {"cell_type": "code", "execution_count": 25, "id": "139667d44689f9e0", "metadata": {"ExecuteTime": {"end_time": "2023-11-27T10:43:49.911867Z", "start_time": "2023-11-27T10:43:49.908329Z"}, "collapsed": false}, "outputs": [], "source": ["chat = VolcEngineMaasChat(volc_engine_maas_ak=\"your ak\", volc_engine_maas_sk=\"your sk\")"]}, {"cell_type": "markdown", "id": "e84ebc4feedcc739", "metadata": {"collapsed": false}, "source": ["or you can set access_key and secret_key in your environment variables\n", "```bash\n", "export VOLC_ACCESSKEY=YOUR_AK\n", "export VOLC_SECRETKEY=YOUR_SK\n", "```"]}, {"cell_type": "code", "execution_count": 26, "id": "35da18414ad17aa0", "metadata": {"ExecuteTime": {"end_time": "2023-11-27T10:43:53.101852Z", "start_time": "2023-11-27T10:43:51.741041Z"}, "collapsed": false}, "outputs": [{"data": {"text/plain": ["AIMessage(content='好的，这是一个笑话：\\n\\n为什么鸟儿不会玩电脑游戏？\\n\\n因为它们没有翅膀！')"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["chat([HumanMessage(content=\"给我讲个笑话\")])"]}, {"cell_type": "markdown", "id": "a55e5a9ed80ec49e", "metadata": {"collapsed": false}, "source": ["# volc engine maas chat with stream"]}, {"cell_type": "code", "execution_count": 27, "id": "b4e4049980ac68ef", "metadata": {"ExecuteTime": {"end_time": "2023-11-27T10:43:55.120405Z", "start_time": "2023-11-27T10:43:55.114707Z"}, "collapsed": false}, "outputs": [], "source": ["chat = VolcEngineMaasChat(\n", "    volc_engine_maas_ak=\"your ak\",\n", "    volc_engine_maas_sk=\"your sk\",\n", "    streaming=True,\n", ")"]}, {"cell_type": "code", "execution_count": 28, "id": "fe709a4ffb5c811d", "metadata": {"ExecuteTime": {"end_time": "2023-11-27T10:43:58.775294Z", "start_time": "2023-11-27T10:43:56.799401Z"}, "collapsed": false}, "outputs": [{"data": {"text/plain": ["AIMessage(content='好的，这是一个笑话：\\n\\n三岁的女儿说她会造句了，妈妈让她用“年轻”造句，女儿说：“妈妈减肥，一年轻了好几斤”。')"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["chat([HumanMessage(content=\"给我讲个笑话\")])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}