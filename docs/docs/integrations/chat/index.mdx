---
sidebar_position: 0
sidebar_class_name: hidden
keywords: [compatibility]
---

# Chat models

[Chat models](/docs/concepts/chat_models) are language models that use a sequence of [messages](/docs/concepts/messages) as inputs and return messages as outputs (as opposed to using plain text). These are generally newer models.

:::info

If you'd like to write your own chat model, see [this how-to](/docs/how_to/custom_chat_model/).
If you'd like to contribute an integration, see [Contributing integrations](/docs/contributing/how_to/integrations/).

:::

import ChatModelTabs from "@theme/ChatModelTabs";

<ChatModelTabs overrideParams={{openai: {model: "gpt-4o-mini"}}} />

```python
model.invoke("Hello, world!")
```

## Featured Providers

:::info
While all these LangChain classes support the indicated advanced feature, you may have
to open the provider-specific documentation to learn which hosted models or backends support
the feature.
:::

import { CategoryTable, IndexTable } from "@theme/FeatureTables";

<CategoryTable category="chat" />

## All chat models

<IndexTable />
