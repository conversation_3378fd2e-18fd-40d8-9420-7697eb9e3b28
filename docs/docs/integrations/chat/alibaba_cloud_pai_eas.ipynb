{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: Alibaba Cloud PAI EAS\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Alibaba Cloud PAI EAS\n", "\n", ">[Alibaba Cloud PAI (Platform for AI)](https://www.alibabacloud.com/help/en/pai/?spm=a2c63.p38356.0.0.c26a426ckrxUwZ) is a lightweight and cost-efficient machine learning platform that uses cloud-native technologies. It provides you with an end-to-end modelling service. It accelerates model training based on tens of billions of features and hundreds of billions of samples in more than 100 scenarios.\n", "\n", ">[Machine Learning Platform for AI of Alibaba Cloud](https://www.alibabacloud.com/help/en/machine-learning-platform-for-ai/latest/what-is-machine-learning-pai) is a machine learning or deep learning engineering platform intended for enterprises and developers. It provides easy-to-use, cost-effective, high-performance, and easy-to-scale plug-ins that can be applied to various industry scenarios. With over 140 built-in optimization algorithms, `Machine Learning Platform for AI` provides whole-process AI engineering capabilities including data labelling (`PAI-iTAG`), model building (`PAI-Designer` and `PAI-DSW`), model training (`PAI-DLC`), compilation optimization, and inference deployment (`PAI-EAS`).\n", ">\n", ">`PAI-EAS` supports different types of hardware resources, including CPUs and GPUs, and features high throughput and low latency. It allows you to deploy large-scale complex models with a few clicks and perform elastic scale-ins and scale-outs in real-time. It also provides a comprehensive O&M and monitoring system."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup EAS Service\n", "\n", "Set up environment variables to init EAS service URL and token.\n", "Use [this document](https://www.alibabacloud.com/help/en/pai/user-guide/service-deployment/) for more information.\n", "\n", "```bash\n", "export EAS_SERVICE_URL=XXX\n", "export EAS_SERVICE_TOKEN=XXX\n", "```\n", "Another option is to use this code:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from langchain_community.chat_models import PaiEasChatEndpoint\n", "from langchain_core.language_models.chat_models import HumanMessage\n", "\n", "os.environ[\"EAS_SERVICE_URL\"] = \"Your_EAS_Service_URL\"\n", "os.environ[\"EAS_SERVICE_TOKEN\"] = \"Your_EAS_Service_Token\"\n", "chat = PaiEasChatEndpoint(\n", "    eas_service_url=os.environ[\"EAS_SERVICE_URL\"],\n", "    eas_service_token=os.environ[\"EAS_SERVICE_TOKEN\"],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run Chat Model\n", "\n", "You can use the default settings to call EAS service as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output = chat.invoke([HumanMessage(content=\"write a funny joke\")])\n", "print(\"output:\", output)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Or, call EAS service with new inference params:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kwargs = {\"temperature\": 0.8, \"top_p\": 0.8, \"top_k\": 5}\n", "output = chat.invoke([HumanMessage(content=\"write a funny joke\")], **kwargs)\n", "print(\"output:\", output)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Or, run a stream call to get a stream response:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outputs = chat.stream([HumanMessage(content=\"hi\")], streaming=True)\n", "for output in outputs:\n", "    print(\"stream output:\", output)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}