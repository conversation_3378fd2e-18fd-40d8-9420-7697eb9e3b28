{"cells": [{"cell_type": "raw", "id": "b4154fbe", "metadata": {}, "source": ["---\n", "sidebar_label: YandexGPT\n", "---"]}, {"cell_type": "markdown", "id": "af63c9db-e4bd-4d3b-a4d7-7927f5541734", "metadata": {}, "source": ["# ChatYandexGPT\n", "\n", "This notebook goes over how to use Langchain with [YandexGPT](https://cloud.yandex.com/en/services/yandexgpt) chat model.\n", "\n", "To use, you should have the `yandexcloud` python package installed."]}, {"cell_type": "code", "execution_count": null, "id": "f3a8f9cb-ff03-4fb8-8185-ff19f2b8fc89", "metadata": {}, "outputs": [], "source": ["%pip install --upgrade --quiet  yandexcloud"]}, {"cell_type": "markdown", "id": "95fa21fb-3669-43fb-bb92-91de7bc591bc", "metadata": {}, "source": ["First, you should [create service account](https://cloud.yandex.com/en/docs/iam/operations/sa/create) with the `ai.languageModels.user` role.\n", "\n", "Next, you have two authentication options:\n", "- [IAM token](https://cloud.yandex.com/en/docs/iam/operations/iam-token/create-for-sa).\n", "    You can specify the token in a constructor parameter `iam_token` or in an environment variable `YC_IAM_TOKEN`.\n", "\n", "- [API key](https://cloud.yandex.com/en/docs/iam/operations/api-key/create)\n", "    You can specify the key in a constructor parameter `api_key` or in an environment variable `YC_API_KEY`.\n", "\n", "To specify the model you can use `model_uri` parameter, see [the documentation](https://cloud.yandex.com/en/docs/yandexgpt/concepts/models#yandexgpt-generation) for more details.\n", "\n", "By default, the latest version of `yandexgpt-lite` is used from the folder specified in the parameter `folder_id` or `YC_FOLDER_ID` environment variable."]}, {"cell_type": "code", "execution_count": 1, "id": "eba2d63b-f871-4f61-b55f-f6092bdc297a", "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models import ChatYandexGPT\n", "from langchain_core.messages import HumanMessage, SystemMessage"]}, {"cell_type": "code", "execution_count": 2, "id": "75905d9a-dfae-43aa-95b9-a160280e43f7", "metadata": {}, "outputs": [], "source": ["chat_model = ChatYandexGPT()"]}, {"cell_type": "code", "execution_count": 3, "id": "40844fe7-7fe5-4679-b6c9-1b3238807bdc", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Je adore le programmement.')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["answer = chat_model.invoke(\n", "    [\n", "        SystemMessage(\n", "            content=\"You are a helpful assistant that translates English to French.\"\n", "        ),\n", "        HumanMessage(content=\"I love programming.\"),\n", "    ]\n", ")\n", "answer"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}