{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Dappier AI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Dappier: Powering AI with Dynamic, Real-Time Data Models**\n", "\n", "Dappier offers a cutting-edge platform that grants developers immediate access to a wide array of real-time data models spanning news, entertainment, finance, market data, weather, and beyond. With our pre-trained data models, you can supercharge your AI applications, ensuring they deliver precise, up-to-date responses and minimize inaccuracies.\n", "\n", "Dappier data models help you build next-gen LLM apps with trusted, up-to-date content from the world's leading brands. Unleash your creativity and enhance any GPT App or AI workflow with actionable, proprietary, data through a simple API. Augment your AI with proprietary data from trusted sources is the best way to ensure factual, up-to-date, responses with fewer hallucinations no matter the question.\n", "\n", "For Developers, By Developers\n", "Designed with developers in mind, Da<PERSON>ier simplifies the journey from data integration to monetization, providing clear, straightforward paths to deploy and earn from your AI models. Experience the future of monetization infrastructure for the new internet at **https://dappier.com/**."]}, {"cell_type": "markdown", "metadata": {}, "source": ["This example goes over how to use LangChain to interact with Dappier AI models\n", "\n", "-----------------------------------------------------------------------------------"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use one of our Dappier AI Data Models, you will need an API key. Please visit Dappier Platform (https://platform.dappier.com/) to log in and create an API key in your profile.\n", "\n", "\n", "You can find more details on the API reference : https://docs.dappier.com/introduction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To work with our Dappier Chat Model you can pass the key directly through the parameter named dappier_api_key when initiating the class\n", "or set as an environment variable.\n", "\n", "```bash\n", "export DAPPIER_API_KEY=\"...\"\n", "```\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models.dappier import ChatDappierAI\n", "from langchain_core.messages import HumanMessage"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["chat = ChatDappierAI(\n", "    dappier_endpoint=\"https://api.dappier.com/app/datamodelconversation\",\n", "    dappier_model=\"dm_01hpsxyfm2fwdt2zet9cg6fdxt\",\n", "    dappier_api_key=\"...\",\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hey there! The Kansas City Chiefs won Super Bowl LVIII in 2024. They beat the San Francisco 49ers in overtime with a final score of 25-22. It was quite the game! 🏈')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [HumanMessage(content=\"Who won the super bowl in 2024?\")]\n", "chat.invoke(messages)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='The Kansas City Chiefs won Super Bowl LVIII in 2024! 🏈')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["await chat.ainvoke(messages)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 4}