{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: <PERSON>\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ErnieBotChat\n", "\n", "[ERNIE-<PERSON><PERSON>](https://cloud.baidu.com/doc/WENXINWORKSHOP/s/jlil56u11) is a large language model developed by Baidu, covering a huge amount of Chinese data.\n", "This notebook covers how to get started with ErnieBot chat models."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Deprecated Warning**\n", "\n", "We recommend users switch from `langchain_community.chat_models.ErnieBotChat` to `langchain_community.chat_models.QianfanChatEndpoint`.\n", "\n", "documentation for `QianfanChatEndpoint` is [here](/docs/integrations/chat/baidu_qianfan_endpoint/).\n", "\n", "There are 4 reasons why we recommend users to use `QianfanChatEndpoint`:\n", "\n", "1. `QianfanChatEndpoint` supports more LLMs in the Qianfan platform.\n", "2. `QianfanChatEndpoint` supports streaming mode.\n", "3. `QianfanChatEndpoint` support function calling usage.\n", "4. `ErnieBot<PERSON><PERSON>` is no longer maintained and has been deprecated."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Some tips for migration:\n", "\n", "- change `ernie_client_id` to `qianfan_ak`, also change `ernie_client_secret` to `qianfan_sk`.\n", "- install `qianfan` package. like `pip install qianfan`\n", "- change `ErnieBotChat` to `QianfanChatEndpoint`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models.baidu_qianfan_endpoint import QianfanChatEndpoint\n", "\n", "chat = QianfanChatEndpoint(\n", "    qianfan_ak=\"your qianfan ak\",\n", "    qianfan_sk=\"your qianfan sk\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Usage"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models import ErnieBotChat\n", "from langchain_core.messages import HumanMessage\n", "\n", "chat = ErnieBotChat(\n", "    ernie_client_id=\"YOUR_CLIENT_ID\", ernie_client_secret=\"YOUR_CLIENT_SECRET\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["or you can set `client_id` and `client_secret` in your environment variables\n", "```bash\n", "export ERNIE_CLIENT_ID=YOUR_CLIENT_ID\n", "export ERNIE_CLIENT_SECRET=YOUR_CLIENT_SECRET\n", "```"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello, I am an artificial intelligence language model. My purpose is to help users answer questions or provide information. What can I do for you?', additional_kwargs={}, example=False)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["chat([HumanMessage(content=\"hello there, who are you?\")])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 4}