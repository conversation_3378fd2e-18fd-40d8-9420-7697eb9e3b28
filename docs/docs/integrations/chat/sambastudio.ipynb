{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: SambaStudio\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatSambaStudio\n", "\n", "This will help you get started with SambaStudio [chat models](/docs/concepts/chat_models). For detailed documentation of all ChatStudio features and configurations head to the [API reference](https://docs.sambanova.ai/sambastudio/latest/index.html).\n", "\n", "**[SambaNova](https://sambanova.ai/)'s** [SambaStudio](https://docs.sambanova.ai/sambastudio/latest/sambastudio-intro.html) SambaStudio is a rich, GUI-based platform that provides the functionality to train, deploy, and manage models in SambaNova [DataScale](https://sambanova.ai/products/datascale) systems.\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | JS support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatSambaStudio](https://docs.sambanova.ai/sambastudio/latest/index.html) | [langchain-sambanova](https://python.langchain.com/docs/integrations/providers/sambanova/) | ❌ | ❌ | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain_sambanova?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain_sambanova?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ❌ | \n", "\n", "## Setup\n", "\n", "To access ChatSambaStudio models you will need to [deploy an endpoint](https://docs.sambanova.ai/sambastudio/latest/language-models.html) in your SambaStudio platform, install the `langchain_sambanova` integration package.\n", "\n", "```bash\n", "pip <PERSON> langchain-sambanova\n", "```\n", "\n", "### Credentials\n", "\n", "Get the URL and API Key from your SambaStudio deployed endpoint and add them to your environment variables:\n", "\n", "``` bash\n", "export SAMBASTUDIO_URL=\"sambastudio-url-key-here\"\n", "export SAMBASTUDIO_API_KEY=\"your-api-key-here\"\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.getenv(\"SAMBASTUDIO_URL\"):\n", "    os.environ[\"SAMBASTUDIO_URL\"] = getpass.getpass(\"Enter your SambaStudio URL: \")\n", "if not os.getenv(\"SAMBASTUDIO_API_KEY\"):\n", "    os.environ[\"SAMBASTUDIO_API_KEY\"] = getpass.getpass(\n", "        \"Enter your SambaStudio API key: \"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain __SambaStudio__ integration lives in the `langchain_sambanova` package:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-sambanova"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_sambanova import ChatSambaStudio\n", "\n", "llm = ChatSambaStudio(\n", "    model=\"Meta-Llama-3-70B-Instruct-4096\",  # set if using a Bundle endpoint\n", "    max_tokens=1024,\n", "    temperature=0.7,\n", "    top_p=0.01,\n", "    do_sample=True,\n", "    process_prompt=\"True\",  # set if using a Bundle endpoint\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"J'adore la programmation.\", response_metadata={'id': 'item0', 'partial': False, 'value': {'completion': \"J'adore la programmation.\", 'logprobs': {'text_offset': [], 'top_logprobs': []}, 'prompt': '<|start_header_id|>system<|end_header_id|>\\n\\nYou are a helpful assistant that translates English to French. Translate the user sentence.<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\nI love programming.<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n', 'stop_reason': 'end_of_text', 'tokens': ['J', \"'\", 'ad', 'ore', ' la', ' programm', 'ation', '.'], 'total_tokens_count': 43}, 'params': {}, 'status': None}, id='item0')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French.\"\n", "        \"Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore la programmation.\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Ich liebe das Programmieren.', response_metadata={'id': 'item0', 'partial': False, 'value': {'completion': 'Ich liebe das Programmieren.', 'logprobs': {'text_offset': [], 'top_logprobs': []}, 'prompt': '<|start_header_id|>system<|end_header_id|>\\n\\nYou are a helpful assistant that translates English to German.<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\nI love programming.<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n', 'stop_reason': 'end_of_text', 'tokens': ['Ich', ' liebe', ' das', ' Programm', 'ieren', '.'], 'total_tokens_count': 36}, 'params': {}, 'status': None}, id='item0')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} \"\n", "            \"to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Streaming"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Arrr, ye landlubber! Ye be wantin' to learn about owls, eh? Well, matey, settle yerself down with a pint o' grog and listen close, for I be tellin' ye about these fascinatin' creatures o' the night!\n", "\n", "Owls be birds, but not just any birds, me hearty! They be nocturnal, meanin' they do their huntin' at night, when the rest o' the world be sleepin'. And they be experts at it, too! Their big, round eyes be designed for seein' in the dark, with a special reflective layer called the tapetum lucidum that helps 'em spot prey in the shadows. It's like havin' a built-in lantern, savvy?\n", "\n", "But that be not all, me matey! Owls also have acute hearin', which helps 'em pinpoint the slightest sounds in the dark. And their ears be asymmetrical, meanin' one ear be higher than the other, which gives 'em better depth perception. It's like havin' a built-in sonar system, arrr!\n", "\n", "Now, ye might be wonderin' how owls fly so silently, like ghosts in the night. Well, it be because o' their special feathers, me hearty! They have soft, fringed feathers on their wings that help reduce noise and turbulence, makin' 'em the sneakiest flyers on the seven seas... er, skies!\n", "\n", "Owls come in all shapes and sizes, from the tiny elf owl to the great grey owl, which be one o' the largest owl species in the world. And they be found on every continent, except Antarctica, o' course. They be solitary creatures, but some species be known to form long-term monogamous relationships, like the barn owl and its mate.\n", "\n", "So, there ye have it, me hearty! Owls be amazin' creatures, with their clever adaptations and stealthy ways. Now, go forth and spread the word about these magnificent birds o' the night! And remember, if ye ever encounter an owl in the wild, be sure to show respect and keep a weather eye open, or ye might just find yerself on the receivin' end o' a silent, flyin' tackle! Arrr!"]}], "source": ["system = \"You are a helpful assistant with pirate accent.\"\n", "human = \"I want to learn more about this animal: {animal}\"\n", "prompt = ChatPromptTemplate.from_messages([(\"system\", system), (\"human\", human)])\n", "\n", "chain = prompt | llm\n", "\n", "for chunk in chain.stream({\"animal\": \"owl\"}):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Async"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='The capital of France is Paris.', response_metadata={'id': 'item0', 'partial': False, 'value': {'completion': 'The capital of France is Paris.', 'logprobs': {'text_offset': [], 'top_logprobs': []}, 'prompt': '<|start_header_id|>user<|end_header_id|>\\n\\nwhat is the capital of France?<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n', 'stop_reason': 'end_of_text', 'tokens': ['The', ' capital', ' of', ' France', ' is', ' Paris', '.'], 'total_tokens_count': 24}, 'params': {}, 'status': None}, id='item0')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"human\",\n", "            \"what is the capital of {country}?\",\n", "        )\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "await chain.ainvoke({\"country\": \"France\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Async Streaming"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Quantum computers use quantum bits (qubits) to process multiple possibilities simultaneously, exponentially faster than classical computers, enabling breakthroughs in fields like cryptography, optimization, and simulation."]}], "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"human\",\n", "            \"in less than {num_words} words explain me {topic} \",\n", "        )\n", "    ]\n", ")\n", "chain = prompt | llm\n", "\n", "async for chunk in chain.astream({\"num_words\": 30, \"topic\": \"quantum computers\"}):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> calling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "from langchain_core.messages import HumanMessage, ToolMessage\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def get_time(kind: str = \"both\") -> str:\n", "    \"\"\"Returns current date, current time or both.\n", "    Args:\n", "        kind: date, time or both\n", "    \"\"\"\n", "    if kind == \"date\":\n", "        date = datetime.now().strftime(\"%m/%d/%Y\")\n", "        return f\"Current date: {date}\"\n", "    elif kind == \"time\":\n", "        time = datetime.now().strftime(\"%H:%M:%S\")\n", "        return f\"Current time: {time}\"\n", "    else:\n", "        date = datetime.now().strftime(\"%m/%d/%Y\")\n", "        time = datetime.now().strftime(\"%H:%M:%S\")\n", "        return f\"Current date: {date}, Current time: {time}\"\n", "\n", "\n", "tools = [get_time]\n", "\n", "\n", "def invoke_tools(tool_calls, messages):\n", "    available_functions = {tool.name: tool for tool in tools}\n", "    for tool_call in tool_calls:\n", "        selected_tool = available_functions[tool_call[\"name\"]]\n", "        tool_output = selected_tool.invoke(tool_call[\"args\"])\n", "        print(f\"Tool output: {tool_output}\")\n", "        messages.append(ToolMessage(tool_output, tool_call_id=tool_call[\"id\"]))\n", "    return messages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llm_with_tools = llm.bind_tools(tools=tools)\n", "messages = [\n", "    HumanMessage(\n", "        content=\"I need to schedule a meeting for two weeks from today. \"\n", "        \"Can you tell me the exact date of the meeting?\"\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Intermediate model response: [{'name': 'get_time', 'args': {'kind': 'date'}, 'id': 'call_4092d5dd21cd4eb494', 'type': 'tool_call'}]\n", "Tool output: Current date: 11/07/2024\n", "final response: The meeting will be exactly two weeks from today, which would be 25/07/2024.\n"]}], "source": ["response = llm_with_tools.invoke(messages)\n", "while len(response.tool_calls) > 0:\n", "    print(f\"Intermediate model response: {response.tool_calls}\")\n", "    messages.append(response)\n", "    messages = invoke_tools(response.tool_calls, messages)\n", "response = llm_with_tools.invoke(messages)\n", "\n", "print(f\"final response: {response.content}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Structured Outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["Joke(setup='Why did the cat join a band?', punchline='Because it wanted to be the purr-cussionist!')"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class Joke(BaseModel):\n", "    \"\"\"Joke to tell user.\"\"\"\n", "\n", "    setup: str = Field(description=\"The setup of the joke\")\n", "    punchline: str = Field(description=\"The punchline to the joke\")\n", "\n", "\n", "structured_llm = llm.with_structured_output(Joke)\n", "\n", "structured_llm.invoke(\"Tell me a joke about cats\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all SambaStudio features and configurations head to the API reference: https://docs.sambanova.ai/sambastudio/latest/api-ref-landing.html"]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}