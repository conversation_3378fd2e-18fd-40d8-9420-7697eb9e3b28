{"cells": [{"cell_type": "raw", "id": "59148044", "metadata": {}, "source": ["---\n", "sidebar_label: GPTRouter\n", "---"]}, {"attachments": {}, "cell_type": "markdown", "id": "bf733a38-db84-4363-89e2-de6735c37230", "metadata": {}, "source": ["# GPTRouter\n", "\n", "[GPTRouter](https://github.com/Writesonic/GPTRouter) is an open source LLM API Gateway that offers a universal API for 30+ LLMs, vision, and image models, with smart fallbacks based on uptime and latency, automatic retries, and streaming.\n", "\n", " \n", "This notebook covers how to get started with using Langchain + the GPTRouter I/O library. \n", "\n", "* Set `GPT_ROUTER_API_KEY` environment variable\n", "* or use the `gpt_router_api_key` keyword argument"]}, {"cell_type": "code", "execution_count": 14, "id": "d0133ddd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: GPTRouter in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (0.1.3)\n", "Requirement already satisfied: pydantic==2.5.2 in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from GPTRouter) (2.5.2)\n", "Requirement already satisfied: httpx>=0.25.2 in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from GPTRouter) (0.25.2)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from pydantic==2.5.2->GPTRouter) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.14.5 in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from pydantic==2.5.2->GPTRouter) (2.14.5)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from pydantic==2.5.2->GPTRouter) (4.8.0)\n", "Requirement already satisfied: idna in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from httpx>=0.25.2->GPTRouter) (3.6)\n", "Requirement already satisfied: anyio in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from httpx>=0.25.2->GPTRouter) (3.7.1)\n", "Requirement already satisfied: sniffio in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from httpx>=0.25.2->GPTRouter) (1.3.0)\n", "Requirement already satisfied: certifi in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from httpx>=0.25.2->GPTRouter) (2023.11.17)\n", "Requirement already satisfied: httpcore==1.* in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from httpx>=0.25.2->GPTRouter) (1.0.2)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from httpcore==1.*->httpx>=0.25.2->GPTRouter) (0.14.0)\n", "Requirement already satisfied: exceptiongroup in /Users/<USER>/.pyenv/versions/3.10.13/envs/langchain_venv5/lib/python3.10/site-packages (from anyio->httpx>=0.25.2->GPTRouter) (1.2.0)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m23.0.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m23.3.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install --upgrade --quiet  GPTRouter"]}, {"cell_type": "code", "execution_count": 15, "id": "d4a7c55d-b235-4ca4-a579-c90cc9570da9", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_community.chat_models import GPTRouter\n", "from langchain_community.chat_models.gpt_router import GPTRouterModel\n", "from langchain_core.messages import HumanMessage"]}, {"cell_type": "code", "execution_count": 16, "id": "b8a9914b", "metadata": {}, "outputs": [], "source": ["anthropic_claude = GPTRouterModel(name=\"claude-instant-1.2\", provider_name=\"anthropic\")"]}, {"cell_type": "code", "execution_count": 17, "id": "70cf04e8-423a-4ff6-8b09-f11fb711c817", "metadata": {"tags": []}, "outputs": [], "source": ["chat = GPTRouter(models_priority_list=[anthropic_claude])"]}, {"cell_type": "code", "execution_count": 18, "id": "8199ef8f-eb8b-4253-9ea0-6c24a013ca4c", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\" J'aime programmer.\")"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    HumanMessage(\n", "        content=\"Translate this sentence from English to French. I love programming.\"\n", "    )\n", "]\n", "chat(messages)"]}, {"attachments": {}, "cell_type": "markdown", "id": "c361ab1e-8c0c-4206-9e3c-9d1424a12b9c", "metadata": {}, "source": ["## `GPTRouter` also supports async and streaming functionality:"]}, {"cell_type": "code", "execution_count": 19, "id": "93a21c5c-6ef9-4688-be60-b2e1f94842fb", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_core.callbacks import CallbackManager, StreamingStdOutCallbackHandler"]}, {"cell_type": "code", "execution_count": 20, "id": "c5fac0e9-05a4-4fc1-a3b3-e5bbb24b971b", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["LLMResult(generations=[[ChatGeneration(text=\" J'aime programmer.\", generation_info={'finish_reason': 'stop_sequence'}, message=AIMessage(content=\" J'aime programmer.\"))]], llm_output={}, run=[RunInfo(run_id=UUID('9885f27f-c35a-4434-9f37-c254259762a5'))])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["await chat.agenerate([messages])"]}, {"cell_type": "code", "execution_count": 21, "id": "025be980-e50d-4a68-93dc-c9c7b500ce34", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" J'aime programmer."]}, {"data": {"text/plain": ["AIMessage(content=\" J'aime programmer.\")"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["chat = GPTRouter(\n", "    models_priority_list=[anthropic_claude],\n", "    streaming=True,\n", "    verbose=True,\n", "    callback_manager=CallbackManager([StreamingStdOutCallbackHandler()]),\n", ")\n", "chat(messages)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}