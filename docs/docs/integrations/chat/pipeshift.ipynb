{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {}, "source": ["---\n", "sidebar_label: Pipeshift\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatPipeshift\n", "\n", "This will help you get started with Pipeshift [chat models](/docs/concepts/chat_models/). For detailed documentation of all ChatPipeshift features and configurations head to the [API reference](https://dashboard.pipeshift.com/docs).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | JS support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatPipeshift](https://dashboard.pipeshift.com/docs) | [langchain-pipeshift](https://pypi.org/project/langchain-pipeshift/) | ❌ | -| ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-pipeshift?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-pipeshift?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ❌ | ❌ | ✅ | ✅ | ❌ | ❌ | ✅ | ✅ | ✅ | - | \n", "\n", "## Setup\n", "\n", "To access Pipeshift models you'll need to create an account on Pipeshift, get an API key, and install the `langchain-pipeshift` integration package.\n", "\n", "### Credentials\n", "\n", "Head to [Pipeshift](https://dashboard.pipeshift.com) to sign up to Pipeshift and generate an API key. Once you've done this set the PIPESHIFT_API_KEY environment variable:"]}, {"cell_type": "code", "execution_count": 1, "id": "433e8d2b-9519-4b49-b2c4-7ab65b046c94", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.getenv(\"PIPESHIFT_API_KEY\"):\n", "    os.environ[\"PIPESHIFT_API_KEY\"] = getpass.getpass(\"Enter your Pipeshift API key: \")"]}, {"cell_type": "markdown", "id": "72ee0c4b-9764-423a-9dbf-95129e185210", "metadata": {}, "source": ["If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:"]}, {"cell_type": "code", "execution_count": 2, "id": "a15d341e-3e26-4ca3-830b-5aab30ed66de", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")"]}, {"cell_type": "markdown", "id": "0730d6a1-c893-4840-9817-5e5251676d5d", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain Pipeshift integration lives in the `langchain-pipeshift` package:"]}, {"cell_type": "code", "execution_count": 3, "id": "652d6238-1f87-422a-b135-f5abbb8652fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install -qU langchain-pipeshift"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 4, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["from langchain_pipeshift import ChatPipeshift\n", "\n", "llm = ChatPipeshift(\n", "    model=\"meta-llama/Meta-Llama-3.1-8B-Instruct\",\n", "    temperature=0,\n", "    max_tokens=512,\n", "    # other params...\n", ")"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 5, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Here is the translation:\\n\\nJe suis amoureux du programme. \\n\\nHowever, a more common translation would be:\\n\\nJ\\'aime programmer.\\n\\nNote that \"Je suis amoureux\" typically implies romantic love, whereas \"J\\'aime\" is a more casual way to express affection or enjoyment for an activity, in this case, programming.', additional_kwargs={}, response_metadata={}, id='run-5cad8e5c-d089-44a8-8dcd-22736cde7d7b-0')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 6, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here is the translation:\n", "\n", "Je suis amoureux du programme. \n", "\n", "However, a more common translation would be:\n", "\n", "J'aime programmer.\n", "\n", "Note that \"Je suis amoureux\" typically implies romantic love, whereas \"J'aime\" is a more casual way to express affection or enjoyment for an activity, in this case, programming.\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 7, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"Das ist schön! Du liebst Programmieren! (That's great! You love programming!)\\n\\nWould you like to know the German translation of a specific programming-related term or phrase, or would you like me to help you with something else?\", additional_kwargs={}, response_metadata={}, id='run-8a4b7d56-23d9-43a7-8fb2-e05f556d94bd-0')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatPipeshift features and configurations head to the API reference: https://dashboard.pipeshift.com/docs"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}