{"cells": [{"cell_type": "markdown", "id": "3ddface67cd10a87", "metadata": {"collapsed": false}, "source": ["# SparkLLM Chat\n", "\n", "SparkLLM chat models API by iFlyTek. For more information, see [iFlyTek Open Platform](https://www.xfyun.cn/)."]}, {"cell_type": "markdown", "id": "b895d98989d4de01", "metadata": {}, "source": ["## Basic use"]}, {"cell_type": "code", "execution_count": 10, "id": "43daa39972d4c533", "metadata": {"ExecuteTime": {"end_time": "2024-02-17T07:55:44.526904Z", "start_time": "2024-02-17T07:55:43.166698Z"}, "collapsed": false}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I help you today?')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\"For basic init and call\"\"\"\n", "from langchain_community.chat_models import ChatSparkLLM\n", "from langchain_core.messages import HumanMessage\n", "\n", "chat = ChatSparkLLM(\n", "    spark_app_id=\"<app_id>\", spark_api_key=\"<api_key>\", spark_api_secret=\"<api_secret>\"\n", ")\n", "message = HumanMessage(content=\"Hello\")\n", "chat([message])"]}, {"cell_type": "markdown", "id": "df755f4c5689510", "metadata": {"collapsed": false}, "source": ["- Get SparkLLM's app_id, api_key and api_secret from [iFlyTek SparkLLM API Console](https://console.xfyun.cn/services/bm3) (for more info, see [iFlyTek SparkLLM Intro](https://xinghuo.xfyun.cn/sparkapi) ), then set environment variables `IFLYTEK_SPARK_APP_ID`, `IFLYTEK_SPARK_API_KEY` and `IFLYTEK_SPARK_API_SECRET` or pass parameters when creating `ChatSparkLLM` as the demo above."]}, {"cell_type": "markdown", "id": "984e32ee47bc6772", "metadata": {"collapsed": false}, "source": ["## For ChatSparkLLM with Streaming"]}, {"cell_type": "code", "execution_count": 13, "id": "7dc162bd65fec08f", "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! How can I help you today?"]}], "source": ["chat = ChatSparkLLM(\n", "    spark_app_id=\"<app_id>\",\n", "    spark_api_key=\"<api_key>\",\n", "    spark_api_secret=\"<api_secret>\",\n", "    streaming=True,\n", ")\n", "for chunk in chat.stream(\"Hello!\"):\n", "    print(chunk.content, end=\"\")"]}, {"cell_type": "markdown", "id": "566c85e0", "metadata": {}, "source": ["## For v2"]}, {"cell_type": "code", "execution_count": null, "id": "3103ebdf", "metadata": {}, "outputs": [], "source": ["\"\"\"For basic init and call\"\"\"\n", "from langchain_community.chat_models import ChatSparkLLM\n", "from langchain_core.messages import HumanMessage\n", "\n", "chat = ChatSparkLLM(\n", "    spark_app_id=\"<app_id>\",\n", "    spark_api_key=\"<api_key>\",\n", "    spark_api_secret=\"<api_secret>\",\n", "    spark_api_url=\"wss://spark-api.xf-yun.com/v2.1/chat\",\n", "    spark_llm_domain=\"generalv2\",\n", ")\n", "message = HumanMessage(content=\"Hello\")\n", "chat([message])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}