{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["---\n", "sidebar_label: <PERSON><PERSON>\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatReka\n", "\n", "This notebook provides a quick overview for getting started with <PERSON><PERSON> [chat models](../../concepts/chat_models.mdx). \n", "\n", "Reka has several chat models. You can find information about their latest models and their costs, context windows, and supported input types in the [Reka docs](https://docs.reka.ai/available-models).\n", "\n", "\n", "\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | JS support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatReka] | [langchain_community](https://python.langchain.com/api_reference/community/index.html) | ✅ | ❌ | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain_community?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain_community?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ❌ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ | \n", "\n", "## Setup\n", "\n", "To access Reka models you'll need to create a Reka developer account, get an API key, and install the `langchain_community` integration package and the reka python package via 'pip install reka-api'.\n", "\n", "### Credentials\n", "\n", "Head to https://platform.reka.ai/ to sign up for Re<PERSON> and generate an API key. Once you've done this set the REKA_API_KEY environment variable:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain __ModuleName__ integration lives in the `langchain_community` package:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install -qU langchain_community reka-api"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "os.environ[\"REKA_API_KEY\"] = getpass.getpass(\"Enter your Reka API key: \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Optional: use <PERSON><PERSON> to trace the execution of the model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your Langsmith API key: \")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models import ChatReka\n", "\n", "model = ChatReka()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=' Hello! How can I help you today? If you have a question, need assistance, or just want to chat, feel free to let me know. Have a great day!\\n\\n', additional_kwargs={}, response_metadata={}, id='run-61522ec2-0587-4fd5-a492-5b205fd8860c-0')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["model.invoke(\"hi\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Images input "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" The image shows an indoor setting with no visible windows or natural light, and there are no indicators of weather conditions. The focus is on a cat sitting on a computer keyboard, and the background includes a computer monitor and various office supplies.\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "image_url = \"https://v0.docs.reka.ai/_images/000000245576.jpg\"\n", "\n", "message = HumanMessage(\n", "    content=[\n", "        {\"type\": \"text\", \"text\": \"describe the weather in this image\"},\n", "        {\n", "            \"type\": \"image_url\",\n", "            \"image_url\": {\"url\": image_url},\n", "        },\n", "    ],\n", ")\n", "response = model.invoke([message])\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Multiple images as input"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" The first image features two German Shepherds, one adult and one puppy, in a vibrant, lush green setting. The adult dog is carrying a large stick in its mouth, running through what appears to be a grassy field, with the puppy following close behind. Both dogs exhibit striking physical characteristics typical of the breed, such as pointed ears and dense fur.\n", "\n", "The second image shows a close-up of a single cat with striking blue eyes, likely a breed like the Siberian or Maine Coon, in a natural outdoor setting. The cat's fur is lighter, possibly a mix of white and gray, and it has a more subdued expression compared to the dogs. The background is blurred, suggesting a focus on the cat's face.\n", "\n", "Overall, the differences lie in the subjects (two dogs vs. one cat), the setting (lush, vibrant grassy field vs. a more muted outdoor background), and the overall mood and activity depicted (playful and active vs. serene and focused).\n"]}], "source": ["message = HumanMessage(\n", "    content=[\n", "        {\"type\": \"text\", \"text\": \"What are the difference between the two images? \"},\n", "        {\n", "            \"type\": \"image_url\",\n", "            \"image_url\": {\n", "                \"url\": \"https://cdn.pixabay.com/photo/2019/07/23/13/51/shepherd-dog-4357790_1280.jpg\"\n", "            },\n", "        },\n", "        {\n", "            \"type\": \"image_url\",\n", "            \"image_url\": {\n", "                \"url\": \"https://cdn.pixabay.com/photo/2024/02/17/00/18/cat-8578562_1280.jpg\"\n", "            },\n", "        },\n", "    ],\n", ")\n", "response = model.invoke([message])\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chaining"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=' Ich liebe Programmieren.\\n\\n', additional_kwargs={}, response_metadata={}, id='run-ffc4ace1-b73a-4fb3-ad0f-57e60a0f9b8d-0')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | model\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use with Tavily api search"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tool use and agent creation\n", "\n", "## Define the tools\n", "\n", "We first need to create the tools we want to use. Our main tool of choice will be Tavily - a search engine. We have a built-in tool in LangChain to easily use Tavily search engine as tool.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "os.environ[\"TAVILY_API_KEY\"] = getpass.getpass(\"Enter your Tavily API key: \")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'url': 'https://www.weatherapi.com/', 'content': \"{'location': {'name': 'San Francisco', 'region': 'California', 'country': 'United States of America', 'lat': 37.775, 'lon': -122.4183, 'tz_id': 'America/Los_Angeles', 'localtime_epoch': 1730484342, 'localtime': '2024-11-01 11:05'}, 'current': {'last_updated_epoch': 1730484000, 'last_updated': '2024-11-01 11:00', 'temp_c': 11.1, 'temp_f': 52.0, 'is_day': 1, 'condition': {'text': 'Mist', 'icon': '//cdn.weatherapi.com/weather/64x64/day/143.png', 'code': 1030}, 'wind_mph': 2.9, 'wind_kph': 4.7, 'wind_degree': 247, 'wind_dir': 'WSW', 'pressure_mb': 1019.0, 'pressure_in': 30.08, 'precip_mm': 0.0, 'precip_in': 0.0, 'humidity': 100, 'cloud': 100, 'feelslike_c': 11.1, 'feelslike_f': 52.0, 'windchill_c': 10.3, 'windchill_f': 50.5, 'heatindex_c': 10.8, 'heatindex_f': 51.5, 'dewpoint_c': 10.4, 'dewpoint_f': 50.6, 'vis_km': 2.8, 'vis_miles': 1.0, 'uv': 3.0, 'gust_mph': 3.8, 'gust_kph': 6.1}}\"}, {'url': 'https://weatherspark.com/h/m/557/2024/1/Historical-Weather-in-January-2024-in-San-Francisco-California-United-States', 'content': 'San Francisco Temperature History January 2024\\nHourly Temperature in January 2024 in San Francisco\\nCompare San Francisco to another city:\\nCloud Cover in January 2024 in San Francisco\\nDaily Precipitation in January 2024 in San Francisco\\nObserved Weather in January 2024 in San Francisco\\nHours of Daylight and Twilight in January 2024 in San Francisco\\nSunrise & Sunset with Twilight in January 2024 in San Francisco\\nSolar Elevation and Azimuth in January 2024 in San Francisco\\nMoon Rise, Set & Phases in January 2024 in San Francisco\\nHumidity Comfort Levels in January 2024 in San Francisco\\nWind Speed in January 2024 in San Francisco\\nHourly Wind Speed in January 2024 in San Francisco\\nHourly Wind Direction in 2024 in San Francisco\\nAtmospheric Pressure in January 2024 in San Francisco\\nData Sources\\n See all nearby weather stations\\nLatest Report — 1:56 PM\\nFri, Jan 12, 2024\\xa0\\xa0\\xa0\\xa04 min ago\\xa0\\xa0\\xa0\\xa0UTC 21:56\\nCall Sign KSFO\\nTemp.\\n54.0°F\\nPrecipitation\\nNo Report\\nWind\\n8.1 mph\\nCloud Cover\\nMostly Cloudy\\n14,000 ft\\nRaw: KSFO 122156Z 08007KT 10SM FEW030 SCT050 BKN140 12/07 A3022 While having the tremendous advantages of temporal and spatial completeness, these reconstructions: (1) are based on computer models that may have model-based errors, (2) are coarsely sampled on a 50 km grid and are therefore unable to reconstruct the local variations of many microclimates, and (3) have particular difficulty with the weather in some coastal areas, especially small islands.\\n We further caution that our travel scores are only as good as the data that underpin them, that weather conditions at any given location and time are unpredictable and variable, and that the definition of the scores reflects a particular set of preferences that may not agree with those of any particular reader.\\n January 2024 Weather History in San Francisco California, United States\\nThe data for this report comes from the San Francisco International Airport.'}]\n"]}], "source": ["from langchain_community.tools.tavily_search import TavilySearchResults\n", "\n", "search = TavilySearchResults(max_results=2)\n", "search_results = search.invoke(\"what is the weather in SF\")\n", "print(search_results)\n", "# If we want, we can create other tools.\n", "# Once we have all the tools we want, we can put them in a list that we will reference later.\n", "tools = [search]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can now see what it is like to enable this model to do tool calling. In order to enable that we use .bind_tools to give the language model knowledge of these tools\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["model_with_tools = model.bind_tools(tools)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can now call the model. Let's first call it with a normal message, and see how it responds. We can look at both the content field as well as the tool_calls field.\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ContentString:  Hello! How can I help you today? If you have a question or need information on a specific topic, feel free to ask. Just type your search query and I'll do my best to assist using the available function.\n", "\n", "\n", "ToolCalls: []\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "response = model_with_tools.invoke([HumanMessage(content=\"Hi!\")])\n", "\n", "print(f\"ContentString: {response.content}\")\n", "print(f\"ToolCalls: {response.tool_calls}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's try calling it with some input that would expect a tool to be called.\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ContentString: \n", "ToolCalls: [{'name': 'tavily_search_results_json', 'args': {'query': 'weather in SF'}, 'id': '2548c622-3553-42df-8220-39fde0632bdb', 'type': 'tool_call'}]\n"]}], "source": ["response = model_with_tools.invoke([HumanMessage(content=\"What's the weather in SF?\")])\n", "\n", "print(f\"ContentString: {response.content}\")\n", "print(f\"ToolCalls: {response.tool_calls}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that there's now no text content, but there is a tool call! It wants us to call the Tavily Search tool.\n", "\n", "This isn't calling that tool yet - it's just telling us to. In order to actually call it, we'll want to create our agent."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create the agent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have defined the tools and the LLM, we can create the agent. We will be using LangGraph to construct the agent. Currently, we are using a high level interface to construct the agent, but the nice thing about LangGraph is that this high-level interface is backed by a low-level, highly controllable API in case you want to modify the agent logic.\n", "\n", "Now, we can initialize the agent with the LLM and the tools.\n", "\n", "Note that we are passing in the model, not model_with_tools. That is because `create_react_agent` will call `.bind_tools` for us under the hood."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "agent_executor = create_react_agent(model, tools)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's now try it out on an example where it should be invoking the tool"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[HumanMessage(content='hi!', additional_kwargs={}, response_metadata={}, id='0ab1f3c7-9079-42d4-8a8a-13af5f6c226b'),\n", " AIMessage(content=' Hello! How can I help you today? If you have a question or need information on a specific topic, feel free to ask. For example, you can start with a search query like \"latest news on climate change\" or \"biography of <PERSON>\".\\n\\n', additional_kwargs={}, response_metadata={}, id='run-276d9dcd-13f3-481d-b562-8fe3962d9ba1-0')]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["response = agent_executor.invoke({\"messages\": [HumanMessage(content=\"hi!\")]})\n", "\n", "response[\"messages\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In order to see exactly what is happening under the hood (and to make sure it's not calling a tool) we can take a look at the LangSmith trace: https://smith.langchain.com/public/2372d9c5-855a-45ee-80f2-94b63493563d/r"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["[HumanMessage(content='whats the weather in sf?', additional_kwargs={}, response_metadata={}, id='af276c61-3df7-4241-8cb0-81d1f1477bb3'),\n", " AIMessage(content='', additional_kwargs={'tool_calls': [{'id': '86da84b8-0d44-444f-8448-7f134f9afa41', 'type': 'function', 'function': {'name': 'tavily_search_results_json', 'arguments': '{\"query\": \"weather in SF\"}'}}]}, response_metadata={}, id='run-abe1b8e2-98a6-4f69-8f95-278ac8c141ff-0', tool_calls=[{'name': 'tavily_search_results_json', 'args': {'query': 'weather in SF'}, 'id': '86da84b8-0d44-444f-8448-7f134f9afa41', 'type': 'tool_call'}]),\n", " ToolMessage(content='[{\"url\": \"https://www.weatherapi.com/\", \"content\": \"{\\'location\\': {\\'name\\': \\'San Francisco\\', \\'region\\': \\'California\\', \\'country\\': \\'United States of America\\', \\'lat\\': 37.775, \\'lon\\': -122.4183, \\'tz_id\\': \\'America/Los_Angeles\\', \\'localtime_epoch\\': 1730483436, \\'localtime\\': \\'2024-11-01 10:50\\'}, \\'current\\': {\\'last_updated_epoch\\': 1730483100, \\'last_updated\\': \\'2024-11-01 10:45\\', \\'temp_c\\': 11.4, \\'temp_f\\': 52.5, \\'is_day\\': 1, \\'condition\\': {\\'text\\': \\'Mist\\', \\'icon\\': \\'//cdn.weatherapi.com/weather/64x64/day/143.png\\', \\'code\\': 1030}, \\'wind_mph\\': 2.2, \\'wind_kph\\': 3.6, \\'wind_degree\\': 237, \\'wind_dir\\': \\'WSW\\', \\'pressure_mb\\': 1019.0, \\'pressure_in\\': 30.08, \\'precip_mm\\': 0.0, \\'precip_in\\': 0.0, \\'humidity\\': 100, \\'cloud\\': 100, \\'feelslike_c\\': 11.8, \\'feelslike_f\\': 53.2, \\'windchill_c\\': 11.2, \\'windchill_f\\': 52.1, \\'heatindex_c\\': 11.7, \\'heatindex_f\\': 53.0, \\'dewpoint_c\\': 10.1, \\'dewpoint_f\\': 50.1, \\'vis_km\\': 2.8, \\'vis_miles\\': 1.0, \\'uv\\': 3.0, \\'gust_mph\\': 3.0, \\'gust_kph\\': 4.9}}\"}, {\"url\": \"https://www.timeanddate.com/weather/@z-us-94134/ext\", \"content\": \"Forecasted weather conditions the coming 2 weeks for San Francisco. Sign in. News. News Home; Astronomy News; Time Zone News ... 01 pm: Mon Nov 11: 60 / 53 °F: Tstorms early. Broken clouds. 54 °F: 19 mph: ↑: 70%: 58%: 0.20\\\\\" 0 (Low) 6:46 am: 5:00 pm * Updated Monday, October 28, 2024 2:24:10 pm San Francisco time - Weather by CustomWeather\"}]', name='tavily_search_results_json', id='de8c8d78-ae24-4a8a-9c73-795c1e4fdd41', tool_call_id='86da84b8-0d44-444f-8448-7f134f9afa41', artifact={'query': 'weather in SF', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': 'Weather in San Francisco', 'url': 'https://www.weatherapi.com/', 'content': \"{'location': {'name': 'San Francisco', 'region': 'California', 'country': 'United States of America', 'lat': 37.775, 'lon': -122.4183, 'tz_id': 'America/Los_Angeles', 'localtime_epoch': 1730483436, 'localtime': '2024-11-01 10:50'}, 'current': {'last_updated_epoch': 1730483100, 'last_updated': '2024-11-01 10:45', 'temp_c': 11.4, 'temp_f': 52.5, 'is_day': 1, 'condition': {'text': 'Mist', 'icon': '//cdn.weatherapi.com/weather/64x64/day/143.png', 'code': 1030}, 'wind_mph': 2.2, 'wind_kph': 3.6, 'wind_degree': 237, 'wind_dir': 'WSW', 'pressure_mb': 1019.0, 'pressure_in': 30.08, 'precip_mm': 0.0, 'precip_in': 0.0, 'humidity': 100, 'cloud': 100, 'feelslike_c': 11.8, 'feelslike_f': 53.2, 'windchill_c': 11.2, 'windchill_f': 52.1, 'heatindex_c': 11.7, 'heatindex_f': 53.0, 'dewpoint_c': 10.1, 'dewpoint_f': 50.1, 'vis_km': 2.8, 'vis_miles': 1.0, 'uv': 3.0, 'gust_mph': 3.0, 'gust_kph': 4.9}}\", 'score': 0.9989501, 'raw_content': None}, {'title': 'San Francisco, USA 14 day weather forecast - timeanddate.com', 'url': 'https://www.timeanddate.com/weather/@z-us-94134/ext', 'content': 'Forecasted weather conditions the coming 2 weeks for San Francisco. Sign in. News. News Home; Astronomy News; Time Zone News ... 01 pm: Mon Nov 11: 60 / 53 °F: Tstorms early. Broken clouds. 54 °F: 19 mph: ↑: 70%: 58%: 0.20\" 0 (Low) 6:46 am: 5:00 pm * Updated Monday, October 28, 2024 2:24:10 pm San Francisco time - Weather by CustomWeather', 'score': 0.9938309, 'raw_content': None}], 'response_time': 3.56}),\n", " AIMessage(content=' The current weather in San Francisco is mist with a temperature of 11.4°C (52.5°F). There is a 100% humidity and the wind is blowing at 2.2 mph from the WSW direction. The forecast for the coming weeks shows a mix of cloudy and partly cloudy days with some chances of thunderstorms. Temperatures are expected to range between 53°F and 60°F.\\n\\n', additional_kwargs={}, response_metadata={}, id='run-de4207d6-e8e8-4382-ad16-4de0dcf0812a-0')]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["response = agent_executor.invoke(\n", "    {\"messages\": [HumanMessage(content=\"whats the weather in sf?\")]}\n", ")\n", "response[\"messages\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can check out the <PERSON><PERSON><PERSON> trace to make sure it's calling the search tool effectively.\n", "\n", "https://smith.langchain.com/public/013ef704-654b-4447-8428-637b343d646e/r"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We've seen how the agent can be called with `.invoke` to get a final response. If the agent executes multiple steps, this may take a while. To show intermediate progress, we can stream back messages as they occur.\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'agent': {'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': '2457d3ea-f001-4b8c-a1ed-3dc3d1381639', 'type': 'function', 'function': {'name': 'tavily_search_results_json', 'arguments': '{\"query\": \"weather in San Francisco\"}'}}]}, response_metadata={}, id='run-0363deab-84d2-4319-bb1e-b55b47fe2274-0', tool_calls=[{'name': 'tavily_search_results_json', 'args': {'query': 'weather in San Francisco'}, 'id': '2457d3ea-f001-4b8c-a1ed-3dc3d1381639', 'type': 'tool_call'}])]}}\n", "----\n", "{'tools': {'messages': [ToolMessage(content='[{\"url\": \"https://www.weatherapi.com/\", \"content\": \"{\\'location\\': {\\'name\\': \\'San Francisco\\', \\'region\\': \\'California\\', \\'country\\': \\'United States of America\\', \\'lat\\': 37.775, \\'lon\\': -122.4183, \\'tz_id\\': \\'America/Los_Angeles\\', \\'localtime_epoch\\': 1730483636, \\'localtime\\': \\'2024-11-01 10:53\\'}, \\'current\\': {\\'last_updated_epoch\\': 1730483100, \\'last_updated\\': \\'2024-11-01 10:45\\', \\'temp_c\\': 11.4, \\'temp_f\\': 52.5, \\'is_day\\': 1, \\'condition\\': {\\'text\\': \\'Mist\\', \\'icon\\': \\'//cdn.weatherapi.com/weather/64x64/day/143.png\\', \\'code\\': 1030}, \\'wind_mph\\': 2.2, \\'wind_kph\\': 3.6, \\'wind_degree\\': 237, \\'wind_dir\\': \\'WSW\\', \\'pressure_mb\\': 1019.0, \\'pressure_in\\': 30.08, \\'precip_mm\\': 0.0, \\'precip_in\\': 0.0, \\'humidity\\': 100, \\'cloud\\': 100, \\'feelslike_c\\': 11.8, \\'feelslike_f\\': 53.2, \\'windchill_c\\': 11.2, \\'windchill_f\\': 52.1, \\'heatindex_c\\': 11.7, \\'heatindex_f\\': 53.0, \\'dewpoint_c\\': 10.1, \\'dewpoint_f\\': 50.1, \\'vis_km\\': 2.8, \\'vis_miles\\': 1.0, \\'uv\\': 3.0, \\'gust_mph\\': 3.0, \\'gust_kph\\': 4.9}}\"}, {\"url\": \"https://weather.com/weather/monthly/l/69bedc6a5b6e977993fb3e5344e3c06d8bc36a1fb6754c3ddfb5310a3c6d6c87\", \"content\": \"Weather.com brings you the most accurate monthly weather forecast for San Francisco, CA with average/record and high/low temperatures, precipitation and more. ... 11. 66 ° 55 ° 12. 69 ° 60\"}]', name='tavily_search_results_json', id='e675f99b-130f-4e98-8477-badd45938d9d', tool_call_id='2457d3ea-f001-4b8c-a1ed-3dc3d1381639', artifact={'query': 'weather in San Francisco', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': 'Weather in San Francisco', 'url': 'https://www.weatherapi.com/', 'content': \"{'location': {'name': 'San Francisco', 'region': 'California', 'country': 'United States of America', 'lat': 37.775, 'lon': -122.4183, 'tz_id': 'America/Los_Angeles', 'localtime_epoch': 1730483636, 'localtime': '2024-11-01 10:53'}, 'current': {'last_updated_epoch': 1730483100, 'last_updated': '2024-11-01 10:45', 'temp_c': 11.4, 'temp_f': 52.5, 'is_day': 1, 'condition': {'text': 'Mist', 'icon': '//cdn.weatherapi.com/weather/64x64/day/143.png', 'code': 1030}, 'wind_mph': 2.2, 'wind_kph': 3.6, 'wind_degree': 237, 'wind_dir': 'WSW', 'pressure_mb': 1019.0, 'pressure_in': 30.08, 'precip_mm': 0.0, 'precip_in': 0.0, 'humidity': 100, 'cloud': 100, 'feelslike_c': 11.8, 'feelslike_f': 53.2, 'windchill_c': 11.2, 'windchill_f': 52.1, 'heatindex_c': 11.7, 'heatindex_f': 53.0, 'dewpoint_c': 10.1, 'dewpoint_f': 50.1, 'vis_km': 2.8, 'vis_miles': 1.0, 'uv': 3.0, 'gust_mph': 3.0, 'gust_kph': 4.9}}\", 'score': 0.9968992, 'raw_content': None}, {'title': 'Monthly Weather Forecast for San Francisco, CA - weather.com', 'url': 'https://weather.com/weather/monthly/l/69bedc6a5b6e977993fb3e5344e3c06d8bc36a1fb6754c3ddfb5310a3c6d6c87', 'content': 'Weather.com brings you the most accurate monthly weather forecast for San Francisco, CA with average/record and high/low temperatures, precipitation and more. ... 11. 66 ° 55 ° 12. 69 ° 60', 'score': 0.97644573, 'raw_content': None}], 'response_time': 3.16})]}}\n", "----\n", "{'agent': {'messages': [AIMessage(content=' The current weather in San Francisco is misty with a temperature of 11.4°C (52.5°F). The wind is blowing at 2.2 mph (3.6 kph) from the WSW direction. The humidity is at 100%, and the visibility is 2.8 km (1.0 miles). The monthly forecast shows average temperatures ranging from 55°F to 66°F (13°C to 19°C) with some precipitation expected.\\n\\n', additional_kwargs={}, response_metadata={}, id='run-99ccf444-d286-4244-a5a5-7b1b511153a6-0')]}}\n", "----\n"]}], "source": ["for chunk in agent_executor.stream(\n", "    {\"messages\": [HumanMessage(content=\"whats the weather in sf?\")]}\n", "):\n", "    print(chunk)\n", "    print(\"----\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference"]}, {"cell_type": "markdown", "metadata": {}, "source": ["https://docs.reka.ai/quick-start"]}], "metadata": {"kernelspec": {"display_name": "langchain_reka", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}