{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ChatHuggingFace\n", "\n", "This will help you get started with `langchain_huggingface` [chat models](/docs/concepts/chat_models). For detailed documentation of all `ChatHuggingFace` features and configurations head to the [API reference](https://python.langchain.com/api_reference/huggingface/chat_models/langchain_huggingface.chat_models.huggingface.ChatHuggingFace.html). For a list of models supported by Hugging Face check out [this page](https://huggingface.co/models).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | JS support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatHuggingFace](https://python.langchain.com/api_reference/huggingface/chat_models/langchain_huggingface.chat_models.huggingface.ChatHuggingFace.html) | [langchain-huggingface](https://python.langchain.com/api_reference/huggingface/index.html) | ✅ | beta | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain_huggingface?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain_huggingface?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ❌ | ✅ | ✅ | ❌ | \n", "\n", "## Setup\n", "\n", "To access Hugging Face models you'll need to create a Hugging Face account, get an API key, and install the `langchain-huggingface` integration package.\n", "\n", "### Credentials\n", "\n", "Generate a [Hugging Face Access Token](https://huggingface.co/docs/hub/security-tokens) and store it as an environment variable: `HUGGINGFACEHUB_API_TOKEN`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.getenv(\"HUGGINGFACEHUB_API_TOKEN\"):\n", "    os.environ[\"HUGGINGFACEHUB_API_TOKEN\"] = getpass.getpass(\"Enter your token: \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation\n", "\n", "| Class | Package | Local | Serializable | JS support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatHuggingFace](https://python.langchain.com/api_reference/huggingface/chat_models/langchain_huggingface.chat_models.huggingface.ChatHuggingFace.html) | [langchain-huggingface](https://python.langchain.com/api_reference/huggingface/index.html) | ✅ | ❌ | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain_huggingface?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain_huggingface?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | \n", "\n", "## Setup\n", "\n", "To access `langchain_huggingface` models you'll need to create a `Hugging Face` account, get an API key, and install the `langchain-huggingface` integration package.\n", "\n", "### Credentials\n", "\n", "You'll need to have a [Hugging Face Access Token](https://huggingface.co/docs/hub/security-tokens) saved as an environment variable: `HUGGINGFACEHUB_API_TOKEN`."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "os.environ[\"HUGGINGFACEHUB_API_TOKEN\"] = getpass.getpass(\n", "    \"Enter your Hugging Face API key: \"\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m24.1.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install --upgrade --quiet  langchain-huggingface text-generation transformers google-search-results numexpr langchainhub sentencepiece jinja2 bitsandbytes accelerate"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "You can instantiate a `ChatHuggingFace` model in two different ways, either from a `HuggingFaceEndpoint` or from a `HuggingFacePipeline`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `HuggingFaceEndpoint`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The token has not been saved to the git credentials helper. Pass `add_to_git_credential=True` in this function directly or `--add-to-git-credential` if using via `huggingface-cli` if you want to set the git credential as well.\n", "Token is valid (permission: fineGrained).\n", "Your token has been saved to /Users/<USER>/.cache/huggingface/token\n", "Login successful\n"]}], "source": ["from langchain_huggingface import ChatHuggingFace, HuggingFaceEndpoint\n", "\n", "llm = HuggingFaceEndpoint(\n", "    repo_id=\"deepseek-ai/DeepSeek-R1-0528\",\n", "    task=\"text-generation\",\n", "    max_new_tokens=512,\n", "    do_sample=False,\n", "    repetition_penalty=1.03,\n", "    provider=\"auto\",  # let Hugging Face choose the best provider for you\n", ")\n", "\n", "chat_model = ChatHuggingFace(llm=llm)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's take advantage of [Inference Providers](https://huggingface.co/docs/inference-providers) to run the model on specific third-party providers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llm = HuggingFaceEndpoint(\n", "    repo_id=\"deepseek-ai/DeepSeek-R1-0528\",\n", "    task=\"text-generation\",\n", "    provider=\"hyperbolic\",  # set your provider here\n", "    # provider=\"nebius\",\n", "    # provider=\"together\",\n", ")\n", "\n", "chat_model = ChatHuggingFace(llm=llm)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `HuggingFacePipeline`"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "da32ae8ec8864ccfb480044fe2eec065", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/638 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ee1891b7e5f64fba88ba35f444e598fb", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json:   0%|          | 0.00/23.9k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9ff1ec7f575b42adb608c15955de7888", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading shards:   0%|          | 0/8 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5214696698814b919f561647a684d1e4", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00008.safetensors:   0%|          | 0.00/1.89G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9ac334c69a2048a0a77340cca44d8c80", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00008.safetensors:   0%|          | 0.00/1.95G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "465ad1a51d414e0daf1cd9308455be94", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00003-of-00008.safetensors:   0%|          | 0.00/1.98G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a329c43c3d574df0afd38c7457cc639c", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00004-of-00008.safetensors:   0%|          | 0.00/1.95G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a736a6c4023542af8c6ecc232b823d18", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00005-of-00008.safetensors:   0%|          | 0.00/1.98G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8bdee70b843d433e8236fff83ecda022", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00006-of-00008.safetensors:   0%|          | 0.00/1.95G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5ecb6103e0304ae188a14d598119a361", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00007-of-00008.safetensors:   0%|          | 0.00/1.98G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "174e3cb487bd453c9c70d7614254a35e", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00008-of-00008.safetensors:   0%|          | 0.00/816M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "28f8c233b04b45d7800e12c785a8c4bc", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/8 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "449dfa023dc8430fbcde94544ba01c4f", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/111 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from langchain_huggingface import ChatHuggingFace, HuggingFacePipeline\n", "\n", "llm = HuggingFacePipeline.from_model_id(\n", "    model_id=\"HuggingFaceH4/zephyr-7b-beta\",\n", "    task=\"text-generation\",\n", "    pipeline_kwargs=dict(\n", "        max_new_tokens=512,\n", "        do_sample=False,\n", "        repetition_penalty=1.03,\n", "    ),\n", ")\n", "\n", "chat_model = ChatHuggingFace(llm=llm)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Instatiating with Quantization\n", "\n", "To run a quantized version of your model, you can specify a `bitsandbytes` quantization config as follows:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from transformers import BitsAndBytesConfig\n", "\n", "quantization_config = BitsAndBytesConfig(\n", "    load_in_4bit=True,\n", "    bnb_4bit_quant_type=\"nf4\",\n", "    bnb_4bit_compute_dtype=\"float16\",\n", "    bnb_4bit_use_double_quant=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["and pass it to the `HuggingFacePipeline` as a part of its `model_kwargs`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llm = HuggingFacePipeline.from_model_id(\n", "    model_id=\"HuggingFaceH4/zephyr-7b-beta\",\n", "    task=\"text-generation\",\n", "    pipeline_kwargs=dict(\n", "        max_new_tokens=512,\n", "        do_sample=False,\n", "        repetition_penalty=1.03,\n", "        return_full_text=False,\n", "    ),\n", "    model_kwargs={\"quantization_config\": quantization_config},\n", ")\n", "\n", "chat_model = ChatHuggingFace(llm=llm)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import (\n", "    HumanMessage,\n", "    SystemMessage,\n", ")\n", "\n", "messages = [\n", "    SystemMessage(content=\"You're a helpful assistant\"),\n", "    HumanMessage(\n", "        content=\"What happens when an unstoppable force meets an immovable object?\"\n", "    ),\n", "]\n", "\n", "ai_msg = chat_model.invoke(messages)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["According to the popular phrase and hypothetical scenario, when an unstoppable force meets an immovable object, a paradoxical situation arises as both forces are seemingly contradictory. On one hand, an unstoppable force is an entity that cannot be stopped or prevented from moving forward, while on the other hand, an immovable object is something that cannot be moved or displaced from its position. \n", "\n", "In this scenario, it is un\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `ChatHuggingFace` features and configurations head to the API reference: https://python.langchain.com/api_reference/huggingface/chat_models/langchain_huggingface.chat_models.huggingface.ChatHuggingFace.html"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatHuggingFace features and configurations head to the API reference: https://python.langchain.com/api_reference/huggingface/chat_models/langchain_huggingface.chat_models.huggingface.ChatHuggingFace.html"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}