{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Eden AI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Eden AI is revolutionizing the AI landscape by uniting the best AI providers, empowering users to unlock limitless possibilities and tap into the true potential of artificial intelligence. With an all-in-one comprehensive and hassle-free platform, it allows users to deploy AI features to production lightning fast, enabling effortless access to the full breadth of AI capabilities via a single API. (website: https://edenai.co/)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This example goes over how to use Lang<PERSON><PERSON><PERSON> to interact with Eden AI models\n", "\n", "-----------------------------------------------------------------------------------"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`EdenAI` goes beyond mere model invocation. It empowers you with advanced features, including:\n", "\n", "- **Multiple Providers**: Gain access to a diverse range of language models offered by various providers, giving you the freedom to choose the best-suited model for your use case.\n", "\n", "- **Fallback Mechanism**: Set a fallback mechanism to ensure seamless operations even if the primary provider is unavailable, you can easily switches to an alternative provider.\n", "\n", "- **Usage Tracking**: Track usage statistics on a per-project and per-API key basis. This feature allows you to monitor and manage resource consumption effectively.\n", "\n", "- **Monitoring and Observability**: `EdenAI` provides comprehensive monitoring and observability tools on the platform. Monitor the performance of your language models, analyze usage patterns, and gain valuable insights to optimize your applications.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Accessing the EDENAI's API requires an API key, \n", "\n", "which you can get by creating an account https://app.edenai.run/user/register  and heading here https://app.edenai.run/admin/iam/api-keys\n", "\n", "Once we have a key we'll want to set it as an environment variable by running:\n", "\n", "```bash\n", "export EDENAI_API_KEY=\"...\"\n", "```\n", "\n", "You can find more details on the API reference : https://docs.edenai.co/reference"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you'd prefer not to set an environment variable you can pass the key in directly via the edenai_api_key named parameter\n", "\n", " when initiating the EdenAI Chat Model class."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models.edenai import ChatEdenAI\n", "from langchain_core.messages import HumanMessage"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["chat = ChatEdenAI(\n", "    edenai_api_key=\"...\", provider=\"openai\", temperature=0.2, max_tokens=250\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I assist you today?')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [HumanMessage(content=\"Hello !\")]\n", "chat.invoke(messages)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I assist you today?')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["await chat.ainvoke(messages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Streaming and Batching\n", "\n", "`ChatEdenAI` supports streaming and batching. Below is an example."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! How can I assist you today?"]}], "source": ["for chunk in chat.stream(messages):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content='Hello! How can I assist you today?')]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.batch([messages])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fallback mecanism\n", "\n", "With Eden AI you can set a fallback mechanism to ensure seamless operations even if the primary provider is unavailable, you can easily switches to an alternative provider."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["chat = ChatEdenAI(\n", "    edenai_api_key=\"...\",\n", "    provider=\"openai\",\n", "    temperature=0.2,\n", "    max_tokens=250,\n", "    fallback_providers=\"google\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this example, you can use Google as a backup provider if OpenAI encounters any issues.\n", "\n", "For more information and details about Eden AI, check out this link: : https://docs.edenai.co/docs/additional-parameters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chaining Calls\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_template(\n", "    \"What is a good name for a company that makes {product}?\"\n", ")\n", "chain = prompt | chat"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='VitalBites')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke({\"product\": \"healthy snacks\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Tools\n", "\n", "### bind_tools()\n", "\n", "With `ChatEdenAI.bind_tools`, we can easily pass in Pydantic classes, dict schemas, LangChain tools, or even functions as tools to the model."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "llm = ChatEdenAI(provider=\"openai\", temperature=0.2, max_tokens=500)\n", "\n", "\n", "class GetWeather(BaseModel):\n", "    \"\"\"Get the current weather in a given location\"\"\"\n", "\n", "    location: str = Field(..., description=\"The city and state, e.g. San Francisco, CA\")\n", "\n", "\n", "llm_with_tools = llm.bind_tools([GetWeather])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', response_metadata={'openai': {'status': 'success', 'generated_text': None, 'message': [{'role': 'user', 'message': 'what is the weather like in San Francisco', 'tools': [{'name': 'GetWeather', 'description': 'Get the current weather in a given location', 'parameters': {'type': 'object', 'properties': {'location': {'description': 'The city and state, e.g. San Francisco, CA', 'type': 'string'}}, 'required': ['location']}}], 'tool_calls': None}, {'role': 'assistant', 'message': None, 'tools': None, 'tool_calls': [{'id': 'call_tRpAO7KbQwgTjlka70mCQJdo', 'name': 'GetWeather', 'arguments': '{\"location\":\"San Francisco\"}'}]}], 'cost': 0.000194}}, id='run-5c44c01a-d7bb-4df6-835e-bda596080399-0', tool_calls=[{'name': '<PERSON><PERSON><PERSON><PERSON>', 'args': {'location': 'San Francisco'}, 'id': 'call_tRpAO7KbQwgTjlka70mCQJdo'}])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ai_msg = llm_with_tools.invoke(\n", "    \"what is the weather like in San Francisco\",\n", ")\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': '<PERSON><PERSON><PERSON><PERSON>',\n", "  'args': {'location': 'San Francisco'},\n", "  'id': 'call_tRpAO7KbQwgTjlka70mCQJdo'}]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["ai_msg.tool_calls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### with_structured_output()\n", "\n", "The BaseChatModel.with_structured_output interface makes it easy to get structured output from chat models. You can use ChatEdenAI.with_structured_output, which uses tool-calling under the hood), to get the model to more reliably return an output in a specific format:\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["GetWeather(location='San Francisco')"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["structured_llm = llm.with_structured_output(GetWeather)\n", "structured_llm.invoke(\n", "    \"what is the weather like in San Francisco\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Passing Tool Results to model\n", "\n", "Here is a full example of how to use a tool. Pass the tool output to the model, and get the result back from the model"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["'11 + 11 = 22'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage, ToolMessage\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def add(a: int, b: int) -> int:\n", "    \"\"\"Adds a and b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a + b\n", "\n", "\n", "llm = ChatEdenAI(\n", "    provider=\"openai\",\n", "    max_tokens=1000,\n", "    temperature=0.2,\n", ")\n", "\n", "llm_with_tools = llm.bind_tools([add], tool_choice=\"required\")\n", "\n", "query = \"What is 11 + 11?\"\n", "\n", "messages = [HumanMessage(query)]\n", "ai_msg = llm_with_tools.invoke(messages)\n", "messages.append(ai_msg)\n", "\n", "tool_call = ai_msg.tool_calls[0]\n", "tool_output = add.invoke(tool_call[\"args\"])\n", "\n", "# This append the result from our tool to the model\n", "messages.append(ToolMessage(tool_output, tool_call_id=tool_call[\"id\"]))\n", "\n", "llm_with_tools.invoke(messages).content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Streaming\n", "\n", "Eden AI does not currently support streaming tool calls. Attempting to stream will yield a single final message."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/Projects/edenai-langchain/libs/community/langchain_community/chat_models/edenai.py:603: UserWarning: stream: Tool use is not yet supported in streaming mode.\n", "  warnings.warn(\"stream: Tool use is not yet supported in streaming mode.\")\n"]}, {"data": {"text/plain": ["[AIMessageChunk(content='', id='run-fae32908-ec48-4ab2-ad96-bb0d0511754f', tool_calls=[{'name': 'add', 'args': {'a': 9, 'b': 9}, 'id': 'call_n0Tm7I9zERWa6UpxCAVCweLN'}], tool_call_chunks=[{'name': 'add', 'args': '{\"a\": 9, \"b\": 9}', 'id': 'call_n0Tm7I9zERWa6UpxCAVCweLN', 'index': 0}])]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["list(llm_with_tools.stream(\"What's 9 + 9\"))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}