{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {}, "source": ["---\n", "sidebar_label: Ollama\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatOllama\n", "\n", "[O<PERSON><PERSON>](https://ollama.com/) allows you to run open-source large language models, such as `gpt-oss`, locally.\n", "\n", "`ollama` bundles model weights, configuration, and data into a single package, defined by a Modelfile.\n", "\n", "It optimizes setup and configuration details, including GPU usage.\n", "\n", "For a complete list of supported models and model variants, see the [Ollama model library](https://github.com/jmorganca/ollama#model-library).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [JS support](https://js.langchain.com/docs/integrations/chat/ollama) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatOllama](https://python.langchain.com/api_reference/ollama/chat_models/langchain_ollama.chat_models.ChatOllama.html#chatollama) | [langchain-ollama](https://python.langchain.com/api_reference/ollama/index.html) | ✅ | ❌ | ✅ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-ollama?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-ollama?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling/) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: |:----------------------------------------------------:| :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ |                          ✅                           | ✅ | ✅ | ❌ | ❌ | ✅ | ✅ | ❌ | ❌ |\n", "\n", "## Setup\n", "\n", "First, follow [these instructions](https://github.com/ollama/ollama?tab=readme-ov-file#ollama) to set up and run a local Ollama instance:\n", "\n", "* [Download](https://ollama.ai/download) and install Ollama onto the available supported platforms (including Windows Subsystem for Linux aka WSL, macOS, and Linux)\n", "    * macOS users can install via Homebrew with `brew install ollama` and start with `brew services start ollama`\n", "* Fetch available LLM model via `ollama pull <name-of-model>`\n", "    * View a list of available models via the [model library](https://ollama.ai/library)\n", "    * e.g., `ollama pull gpt-oss:20b`\n", "* This will download the default tagged version of the model. Typically, the default points to the latest, smallest sized-parameter model.\n", "\n", "> On Mac, the models will be download to `~/.ollama/models`\n", ">\n", "> On Linux (or WSL), the models will be stored at `/usr/share/ollama/.ollama/models`\n", "\n", "* Specify the exact version of the model of interest as such `ollama pull gpt-oss:20b` (View the [various tags for the `Vicuna`](https://ollama.ai/library/vicuna/tags) model in this instance)\n", "* To view all pulled models, use `ollama list`\n", "* To chat directly with a model from the command line, use `ollama run <name-of-model>`\n", "* View the [Ollama documentation](https://github.com/ollama/ollama/blob/main/docs/README.md) for more commands. You can run `ollama help` in the terminal to see available commands.\n"]}, {"cell_type": "markdown", "id": "72ee0c4b-9764-423a-9dbf-95129e185210", "metadata": {}, "source": ["To enable automated tracing of your model calls, set your [LangSmith](https://docs.smith.langchain.com/) API key:"]}, {"cell_type": "code", "execution_count": null, "id": "a15d341e-3e26-4ca3-830b-5aab30ed66de", "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")"]}, {"cell_type": "markdown", "id": "0730d6a1-c893-4840-9817-5e5251676d5d", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain Ollama integration lives in the `langchain-ollama` package:"]}, {"cell_type": "code", "execution_count": null, "id": "652d6238-1f87-422a-b135-f5abbb8652fc", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-ollama"]}, {"cell_type": "markdown", "id": "b18bd692076f7cf7", "metadata": {}, "source": [":::warning\n", "Make sure you're using the latest Ollama version!\n", ":::\n", "\n", "Update by running:"]}, {"cell_type": "code", "execution_count": null, "id": "b7a05cba95644c2e", "metadata": {}, "outputs": [], "source": ["%pip install -U ollama"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:\n"]}, {"cell_type": "code", "execution_count": 9, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["from langchain_ollama import ChatOllama\n", "\n", "llm = ChatOllama(\n", "    model=\"llama3.1\",\n", "    temperature=0,\n", "    # other params...\n", ")"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 10, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content='The translation of \"I love programming\" in French is:\\n\\n\"J\\'adore le programmation.\"', additional_kwargs={}, response_metadata={'model': 'llama3.1', 'created_at': '2025-06-25T18:43:00.483666Z', 'done': True, 'done_reason': 'stop', 'total_duration': 619971208, 'load_duration': 27793125, 'prompt_eval_count': 35, 'prompt_eval_duration': 36354583, 'eval_count': 22, 'eval_duration': 555182667, 'model_name': 'llama3.1'}, id='run--348bb5ef-9dd9-4271-bc7e-a9ddb54c28c1-0', usage_metadata={'input_tokens': 35, 'output_tokens': 22, 'total_tokens': 57})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 11, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The translation of \"I love programming\" in French is:\n", "\n", "\"J'adore le programmation.\"\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 12, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='\"Programmieren ist meine Leidenschaft.\"\\n\\n(I translated \"programming\" to the German word \"Programmieren\", and added \"ist meine Leidenschaft\" which means \"is my passion\")', additional_kwargs={}, response_metadata={'model': 'llama3.1', 'created_at': '2025-06-25T18:43:29.350032Z', 'done': True, 'done_reason': 'stop', 'total_duration': 1194744459, 'load_duration': 26982500, 'prompt_eval_count': 30, 'prompt_eval_duration': 117043458, 'eval_count': 41, 'eval_duration': 1049892167, 'model_name': 'llama3.1'}, id='run--efc6436e-2346-43d9-8118-3c20b3cdf0d0-0', usage_metadata={'input_tokens': 30, 'output_tokens': 41, 'total_tokens': 71})"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "0f51345d-0a9d-43f1-8fca-d0662cb8e21b", "metadata": {}, "source": ["## <PERSON><PERSON> calling\n", "\n", "We can use [tool calling](/docs/concepts/tool_calling/) with an LLM [that has been fine-tuned for tool use](https://ollama.com/search?&c=tools) such as `gpt-oss`:\n", "\n", "```\n", "ollama pull gpt-oss:20b\n", "```\n", "\n", "Details on creating custom tools are available in [this guide](/docs/how_to/custom_tools/). Below, we demonstrate how to create a tool using the `@tool` decorator on a normal python function."]}, {"cell_type": "code", "execution_count": null, "id": "f767015f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'name': 'validate_user', 'args': {'addresses': ['123 Fake St, Boston, MA', '234 Pretend Boulevard, Houston, TX'], 'user_id': '123'}, 'id': 'aef33a32-a34b-4b37-b054-e0d85584772f', 'type': 'tool_call'}]\n"]}], "source": ["from typing import List\n", "\n", "from langchain_core.messages import AIMessage\n", "from langchain_core.tools import tool\n", "from langchain_ollama import ChatOllama\n", "\n", "\n", "@tool\n", "def validate_user(user_id: int, addresses: List[str]) -> bool:\n", "    \"\"\"Validate user using historical addresses.\n", "\n", "    Args:\n", "        user_id (int): the user ID.\n", "        addresses (List[str]): Previous addresses as a list of strings.\n", "    \"\"\"\n", "    return True\n", "\n", "\n", "llm = ChatOllama(\n", "    model=\"gpt-oss:20b\",\n", "    validate_model_on_init=True,\n", "    temperature=0,\n", ").bind_tools([validate_user])\n", "\n", "result = llm.invoke(\n", "    \"Could you validate user 123? They previously lived at \"\n", "    \"123 Fake St in Boston MA and 234 Pretend Boulevard in \"\n", "    \"Houston TX.\"\n", ")\n", "\n", "if isinstance(result, AIMessage) and result.tool_calls:\n", "    print(result.tool_calls)"]}, {"cell_type": "markdown", "id": "4c5e0197", "metadata": {}, "source": ["## Multi-modal\n", "\n", "Ollama has limited support for multi-modal LLMs, such as [gemma3](https://ollama.com/library/gemma3)\n", "\n", "Be sure to update Ollama so that you have the most recent version to support multi-modal."]}, {"cell_type": "code", "execution_count": null, "id": "69920d39", "metadata": {}, "outputs": [], "source": ["%pip install pillow"]}, {"cell_type": "code", "execution_count": 15, "id": "36c9b1c2", "metadata": {}, "outputs": [{"data": {"text/html": ["<img src=\"data:image/jpeg;base64,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\" />"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import base64\n", "from io import BytesIO\n", "\n", "from IPython.display import HTML, display\n", "from PIL import Image\n", "\n", "\n", "def convert_to_base64(pil_image):\n", "    \"\"\"\n", "    Convert PIL images to Base64 encoded strings\n", "\n", "    :param pil_image: PIL image\n", "    :return: Re-sized Base64 string\n", "    \"\"\"\n", "\n", "    buffered = BytesIO()\n", "    pil_image.save(buffered, format=\"JPEG\")  # You can change the format if needed\n", "    img_str = base64.b64encode(buffered.getvalue()).decode(\"utf-8\")\n", "    return img_str\n", "\n", "\n", "def plt_img_base64(img_base64):\n", "    \"\"\"\n", "    Disply base64 encoded string as image\n", "\n", "    :param img_base64:  Base64 string\n", "    \"\"\"\n", "    # Create an HTML img tag with the base64 string as the source\n", "    image_html = f'<img src=\"data:image/jpeg;base64,{img_base64}\" />'\n", "    # Display the image by rendering the HTML\n", "    display(HTML(image_html))\n", "\n", "\n", "file_path = \"../../../static/img/ollama_example_img.jpg\"\n", "pil_image = Image.open(file_path)\n", "\n", "image_b64 = convert_to_base64(pil_image)\n", "plt_img_base64(image_b64)"]}, {"cell_type": "code", "execution_count": 16, "id": "32b3ba7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["90%\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "from langchain_ollama import ChatOllama\n", "\n", "llm = ChatOllama(model=\"bakllava\", temperature=0)\n", "\n", "\n", "def prompt_func(data):\n", "    text = data[\"text\"]\n", "    image = data[\"image\"]\n", "\n", "    image_part = {\n", "        \"type\": \"image_url\",\n", "        \"image_url\": f\"data:image/jpeg;base64,{image}\",\n", "    }\n", "\n", "    content_parts = []\n", "\n", "    text_part = {\"type\": \"text\", \"text\": text}\n", "\n", "    content_parts.append(image_part)\n", "    content_parts.append(text_part)\n", "\n", "    return [HumanMessage(content=content_parts)]\n", "\n", "\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "chain = prompt_func | llm | StrOutputParser()\n", "\n", "query_chain = chain.invoke(\n", "    {\"text\": \"What is the Dollar-based gross retention rate?\", \"image\": image_b64}\n", ")\n", "\n", "print(query_chain)"]}, {"cell_type": "markdown", "id": "fb6a331f-1507-411f-89e5-c4d598154f3c", "metadata": {}, "source": ["## Reasoning models and custom message roles\n", "\n", "Some models, such as IBM's [Granite 3.2](https://ollama.com/library/granite3.2), support custom message roles to enable thinking processes.\n", "\n", "To access Granite 3.2's thinking features, pass a message with a `\"control\"` role with content set to `\"thinking\"`. Because `\"control\"` is a non-standard message role, we can use a [ChatMessage](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.chat.ChatMessage.html) object to implement it:"]}, {"cell_type": "code", "execution_count": 1, "id": "d7309fa7-990e-4c20-b1f0-b155624ecf37", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here is my thought process:\n", "The user is asking for the value of 3 raised to the power of 3, which is a basic exponentiation operation.\n", "\n", "Here is my response:\n", "\n", "3^3 (read as \"3 to the power of 3\") equals 27. \n", "\n", "This calculation is performed by multiplying 3 by itself three times: 3*3*3 = 27.\n"]}], "source": ["from langchain_core.messages import ChatMessage, HumanMessage\n", "from langchain_ollama import ChatOllama\n", "\n", "llm = ChatOllama(model=\"granite3.2:8b\")\n", "\n", "messages = [\n", "    ChatMessage(role=\"control\", content=\"thinking\"),\n", "    HumanMessage(\"What is 3^3?\"),\n", "]\n", "\n", "response = llm.invoke(messages)\n", "print(response.content)"]}, {"cell_type": "markdown", "id": "6271d032-da40-44d4-9b52-58370e164be3", "metadata": {}, "source": ["Note that the model exposes its thought process in addition to its final response."]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatOllama features and configurations head to the [API reference](https://python.langchain.com/api_reference/ollama/chat_models/langchain_ollama.chat_models.ChatOllama.html)."]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}