{"cells": [{"cell_type": "markdown", "id": "e815de6298bf07ca", "metadata": {}, "source": ["# Chat Writer\n", "\n", "This notebook provides a quick overview for getting started with Writer [chat](/docs/concepts/chat_models/).\n", "\n", "Writer has several chat models. You can find information about their latest models and their costs, context windows, and supported input types in the [Writer docs](https://dev.writer.com/home).\n", "\n", "\n", "## Overview\n", "\n", "### Integration details\n", "| Class                                                                                                                    | Package          | Local | Serializable | JS support |                                        Package downloads                                         |                                        Package latest                                         |\n", "|:-------------------------------------------------------------------------------------------------------------------------|:-----------------| :---: | :---: |:----------:|:------------------------------------------------------------------------------------------------:|:---------------------------------------------------------------------------------------------:|\n", "| [ChatWriter](https://github.com/writer/langchain-writer/blob/main/langchain_writer/chat_models.py#L308) | [langchain-writer](https://pypi.org/project/langchain-writer/) |      ❌       |                                       ❌                                       | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-writer?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-writer?style=flat-square&label=%20) |\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling) | Structured output | JSON mode | Image input | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async |         [Token usage](/docs/how_to/chat_token_usage_tracking/)          | Logprobs |\n", "| :---: |:-----------------:| :---: | :---: |  :---: | :---: | :---: | :---: |:--------------------------------:|:--------:|\n", "| ✅ |         ❌         | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ |                ✅                 |    ❌     |"]}, {"cell_type": "markdown", "id": "3fd9903e685808d9", "metadata": {}, "source": ["### Credentials\n", "\n", "Sign up for [Writer AI Studio](https://app.writer.com/aistudio/signup?utm_campaign=devrel) and follow this [Quickstart](https://dev.writer.com/api-guides/quickstart) to obtain an API key. Then, set the WRITER_API_KEY environment variable:"]}, {"cell_type": "code", "id": "433e8d2b-9519-4b49-b2c4-7ab65b046c94", "metadata": {"jupyter": {"is_executing": true}}, "source": ["import getpass\n", "import os\n", "\n", "if not os.getenv(\"WRITER_API_KEY\"):\n", "    os.environ[\"WRITER_API_KEY\"] = getpass.getpass(\"Enter your Writer API key: \")"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "72ee0c4b-9764-423a-9dbf-95129e185210", "metadata": {}, "source": ["If you want to get automated tracing of your model calls, you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:"]}, {"cell_type": "code", "id": "a15d341e-3e26-4ca3-830b-5aab30ed66de", "metadata": {}, "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "0730d6a1-c893-4840-9817-5e5251676d5d", "metadata": {}, "source": ["### Installation\n", "\n", "`ChatWriter` is available from the `langchain-writer` package. Install it with:"]}, {"cell_type": "code", "id": "652d6238-1f87-422a-b135-f5abbb8652fc", "metadata": {}, "source": ["%pip install -qU langchain-writer"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["### Instantiation\n", "\n", "Now we can instantiate our model object in order to generate chat completions:"]}, {"cell_type": "code", "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "source": ["from langchain_writer import ChatWriter\n", "\n", "llm = ChatWriter(\n", "    model=\"palmyra-x-004\",\n", "    temperature=0,\n", "    max_tokens=None,\n", "    timeout=None,\n", "    max_retries=2,\n", ")"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Usage\n", "\n", "To use the model, you pass in a list of messages and call the `invoke` method:"]}, {"cell_type": "code", "id": "62e0dbc3", "metadata": {"tags": []}, "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "5cf7293d", "metadata": {}, "source": ["Then, you can access the content of the message:"]}, {"cell_type": "code", "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "source": ["print(ai_msg.content)"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "4391289ce0a80e19", "metadata": {}, "source": ["## Streaming\n", "\n", "You can also stream the response. First, create a stream:"]}, {"cell_type": "code", "id": "4a0f2112b3a4c79e", "metadata": {}, "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming. Sing a song about it\"),\n", "]\n", "ai_stream = llm.stream(messages)\n", "ai_stream"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "23cc74b6", "metadata": {}, "source": ["Then, iterate over the stream to get the chunks:"]}, {"cell_type": "code", "id": "8c4b7b9b9308c757", "metadata": {}, "source": ["for chunk in ai_stream:\n", "    print(chunk.content, end=\"\")"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "e632bf7d0873f933", "metadata": {}, "source": ["## <PERSON><PERSON> calling\n", "\n", "Writer models like Palmyra X 004 support [tool calling](https://dev.writer.com/api-guides/tool-calling), which lets you describe tools and their arguments. The model will return a JSON object with a tool to invoke and the inputs to that tool.\n", "\n", "### Binding tools\n", "\n", "With `ChatWriter.bind_tools`, you can easily pass in Pydantic classes, dictionary schemas, LangChain tools, or even functions as tools to the model. Under the hood, these are converted to tool schemas, which look like this:\n", "```\n", "{\n", "    \"name\": \"...\",\n", "    \"description\": \"...\",\n", "    \"parameters\": {...}  # JSONSchema\n", "}\n", "```\n", "These are passed in every model invocation.\n", "\n", "For example, to use a tool that gets the weather in a given location, you can define a Pydantic class and pass it to `ChatWriter.bind_tools`:"]}, {"cell_type": "code", "id": "47e2f0faceca533", "metadata": {}, "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class GetWeather(BaseModel):\n", "    \"\"\"Get the current weather in a given location\"\"\"\n", "\n", "    location: str = Field(..., description=\"The city and state, e.g. San Francisco, CA\")\n", "\n", "\n", "llm.bind_tools([GetWeather])"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "68e22d3b", "metadata": {}, "source": ["Then, you can invoke the model with the tool:"]}, {"cell_type": "code", "id": "765527dd533ec967", "metadata": {}, "source": ["ai_msg = llm.invoke(\n", "    \"what is the weather like in New York City\",\n", ")\n", "ai_msg"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "57544bdf", "metadata": {}, "source": ["Finally, you can access the tool calls and proceed to execute your functions:"]}, {"cell_type": "code", "id": "f361c4769e772fe", "metadata": {}, "source": ["print(ai_msg.tool_calls)"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "3baf53021834d2ff", "metadata": {}, "source": ["### A note on tool binding\n", "\n", "The `ChatWriter.bind_tools()` method does not create a new instance with bound tools, but stores the received `tools` and `tool_choice` in the initial class instance attributes to pass them as parameters during the Palmyra LLM call while using `ChatWriter` invocation. This approach allows the support of different tool types, e.g. `function` and `graph`. `Graph` is one of the remotely called Writer Palmyra tools. For further information, visit our [docs](https://dev.writer.com/api-guides/knowledge-graph#knowledge-graph). \n", "\n", "For more information about tool usage in LangChain, visit the [LangChain tool calling documentation](https://python.langchain.com/docs/concepts/tool_calling/)."]}, {"cell_type": "markdown", "id": "a4674b1b82ce9d1f", "metadata": {}, "source": ["## Batching\n", "\n", "You can also batch requests and set the `max_concurrency`:"]}, {"cell_type": "code", "id": "c8a217f6190747fe", "metadata": {}, "source": ["ai_batch = llm.batch(\n", "    [\n", "        \"How to cook pancakes?\",\n", "        \"How to compose poem?\",\n", "        \"How to run faster?\",\n", "    ],\n", "    config={\"max_concurrency\": 3},\n", ")\n", "ai_batch"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "2eb81e1d", "metadata": {}, "source": ["Then, iterate over the batch to get the results:"]}, {"cell_type": "code", "id": "b6a228d448f3df23", "metadata": {}, "source": ["for batch in ai_batch:\n", "    print(batch.content)\n", "    print(\"-\" * 100)"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "58a9ab241fe09a71", "metadata": {}, "source": ["## Asynchronous usage\n", "\n", "All features above (invocation, streaming, batching, tools calling) also support asynchronous usage."]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Prompt templates\n", "\n", "[Prompt templates](https://python.langchain.com/docs/concepts/prompt_templates/) help to translate user input and parameters into instructions for a language model. You can use `ChatWriter` with a prompt template like so:\n"]}, {"cell_type": "code", "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "For detailed documentation of all ChatWriter features and configurations, head to the [API reference](https://python.langchain.com/api_reference/writer/chat_models/langchain_writer.chat_models.ChatWriter.html#langchain_writer.chat_models.ChatWriter).\n", "\n", "## Additional resources\n", "You can find information about Writer's models (including costs, context windows, and supported input types) and tools in the [Writer docs](https://dev.writer.com/home)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}