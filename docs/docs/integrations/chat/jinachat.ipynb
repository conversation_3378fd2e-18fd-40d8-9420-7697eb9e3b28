{"cells": [{"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# JinaChat\n", "\n", "This notebook covers how to get started with JinaChat chat models."]}, {"cell_type": "code", "execution_count": 4, "id": "522686de", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_community.chat_models import JinaChat\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "from langchain_core.prompts.chat import (\n", "    ChatPromptTemplate,\n", "    HumanMessagePromptTemplate,\n", "    SystemMessagePromptTemplate,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [], "source": ["chat = JinaChat(temperature=0)"]}, {"cell_type": "code", "execution_count": 10, "id": "ce16ad78-8e6f-48cd-954e-98be75eb5836", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"J'aime programmer.\", additional_kwargs={}, example=False)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    SystemMessage(\n", "        content=\"You are a helpful assistant that translates English to French.\"\n", "    ),\n", "    HumanMessage(\n", "        content=\"Translate this sentence from English to French. I love programming.\"\n", "    ),\n", "]\n", "chat(messages)"]}, {"cell_type": "markdown", "id": "778f912a-66ea-4a5d-b3de-6c7db4baba26", "metadata": {}, "source": ["You can make use of templating by using a `MessagePromptTemplate`. You can build a `ChatPromptTemplate` from one or more `MessagePromptTemplates`. You can use `ChatPromptTemplate`'s `format_prompt` -- this returns a `PromptValue`, which you can convert to a string or Message object, depending on whether you want to use the formatted value as input to an llm or chat model.\n", "\n", "For convenience, there is a `from_template` method exposed on the template. If you were to use this template, this is what it would look like:"]}, {"cell_type": "code", "execution_count": 11, "id": "180c5cc8", "metadata": {}, "outputs": [], "source": ["template = (\n", "    \"You are a helpful assistant that translates {input_language} to {output_language}.\"\n", ")\n", "system_message_prompt = SystemMessagePromptTemplate.from_template(template)\n", "human_template = \"{text}\"\n", "human_message_prompt = HumanMessagePromptTemplate.from_template(human_template)"]}, {"cell_type": "code", "execution_count": 9, "id": "fbb043e6", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"J'aime programmer.\", additional_kwargs={}, example=False)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["chat_prompt = ChatPromptTemplate.from_messages(\n", "    [system_message_prompt, human_message_prompt]\n", ")\n", "\n", "# get a chat completion from the formatted messages\n", "chat(\n", "    chat_prompt.format_prompt(\n", "        input_language=\"English\", output_language=\"French\", text=\"I love programming.\"\n", "    ).to_messages()\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c095285d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 5}