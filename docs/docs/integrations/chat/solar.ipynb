{"cells": [{"cell_type": "markdown", "id": "516cad96-cfcb-4dd1-b70e-ecaef33e60ba", "metadata": {}, "source": ["Deprecated since version 0.0.34: Use langchain_upstage.ChatUpstage instead."]}, {"cell_type": "code", "execution_count": 5, "id": "a9667088-04e1-4f67-8221-a0072a2d635f", "metadata": {"execution": {"iopub.execute_input": "2024-03-06T17:04:59.273702Z", "iopub.status.busy": "2024-03-06T17:04:59.272602Z", "iopub.status.idle": "2024-03-06T17:05:00.129177Z", "shell.execute_reply": "2024-03-06T17:05:00.124594Z", "shell.execute_reply.started": "2024-03-06T17:04:59.273646Z"}}, "outputs": [{"data": {"text/plain": ["AIMessage(content='저는 대형 언어 모델 프로젝트를 구축하고 싶습니다.')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "os.environ[\"SOLAR_API_KEY\"] = \"SOLAR_API_KEY\"\n", "\n", "from langchain_community.chat_models.solar import SolarChat\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "chat = SolarChat(max_tokens=1024)\n", "\n", "messages = [\n", "    SystemMessage(\n", "        content=\"You are a helpful assistant who translates English to Korean.\"\n", "    ),\n", "    HumanMessage(\n", "        content=\"Translate this sentence from English to Korean. I want to build a project of large language model.\"\n", "    ),\n", "]\n", "\n", "chat.invoke(messages)"]}, {"cell_type": "code", "execution_count": null, "id": "8cb792fe-2844-4969-a9e9-f4c0f97b1699", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}