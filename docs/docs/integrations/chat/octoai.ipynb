{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ChatOctoAI\n", "\n", "[OctoAI](https://docs.octoai.cloud/docs) offers easy access to efficient compute and enables users to integrate their choice of AI models into applications. The `OctoAI` compute service helps you run, tune, and scale AI applications easily.\n", "\n", "This notebook demonstrates the use of `langchain.chat_models.ChatOctoAI` for [OctoAI endpoints](https://octoai.cloud/text).\n", "\n", "## Setup\n", "\n", "To run our example app, there are two simple steps to take:\n", "\n", "1. Get an API Token from [your OctoAI account page](https://octoai.cloud/settings).\n", "   \n", "2. Paste your API token in the code cell below or use the `octoai_api_token` keyword argument.\n", "\n", "Note: If you want to use a different model than the [available models](https://octoai.cloud/text?selectedTags=Chat), you can containerize the model and make a custom OctoAI endpoint yourself, by following [Build a Container from Python](https://octo.ai/docs/bring-your-own-model/advanced-build-a-container-from-scratch-in-python) and [Create a Custom Endpoint from a Container](https://octo.ai/docs/bring-your-own-model/create-custom-endpoints-from-a-container/create-custom-endpoints-from-a-container) and then updating your `OCTOAI_API_BASE` environment variable.\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OCTOAI_API_TOKEN\"] = \"OCTOAI_API_TOKEN\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models import ChatOctoAI\n", "from langchain_core.messages import HumanMessage, SystemMessage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["chat = ChatOctoAI(max_tokens=300, model_name=\"mixtral-8x7b-instruct\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages = [\n", "    SystemMessage(content=\"You are a helpful assistant.\"),\n", "    HumanMessage(content=\"Tell me about <PERSON> da Vinci briefly.\"),\n", "]\n", "print(chat(messages).content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON> (1452-1519) was an Italian polymath who is often considered one of the greatest painters in history. However, his genius extended far beyond art. He was also a scientist, inventor, mathematician, engineer, anatomist, geologist, and cartographer.\n", "\n", "<PERSON> is best known for his paintings such as the Mona Lisa, The Last Supper, and The Virgin of the Rocks. His scientific studies were ahead of his time, and his notebooks contain detailed drawings and descriptions of various machines, human anatomy, and natural phenomena.\n", "\n", "Despite never receiving a formal education, <PERSON> <PERSON>'s insatiable curiosity and observational skills made him a pioneer in many fields. His work continues to inspire and influence artists, scientists, and thinkers today."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}, "vscode": {"interpreter": {"hash": "97697b63fdcee0a640856f91cb41326ad601964008c341809e43189d1cab1047"}}}, "nbformat": 4, "nbformat_minor": 4}