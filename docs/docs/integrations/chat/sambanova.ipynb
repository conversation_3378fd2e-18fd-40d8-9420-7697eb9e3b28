{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: SambaNovaCloud\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatSambaNovaCloud\n", "\n", "This will help you get started with SambaNovaCloud [chat models](/docs/concepts/chat_models/). For detailed documentation of all ChatSambaNovaCloud features and configurations head to the [API reference](https://docs.sambanova.ai/cloud/docs/get-started/overview).\n", "\n", "**[<PERSON><PERSON><PERSON><PERSON>](https://sambanova.ai/)'s** [SambaNova Cloud](https://cloud.sambanova.ai/) is a platform for performing inference with open-source models\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | JS support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatSambaNovaCloud](https://docs.sambanova.ai/cloud/docs/get-started/overview) | [langchain-sambanova](https://python.langchain.com/docs/integrations/providers/sambanova/) | ❌ | ❌ | ❌ | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain_sambanova?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain_sambanova?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](//docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ❌ | \n", "\n", "## Setup\n", "\n", "To access ChatSambaNovaCloud models you will need to create a [SambaNovaCloud](https://cloud.sambanova.ai/) account, get an API key, install the `langchain_sambanova` integration package.\n", "\n", "```bash\n", "pip <PERSON> langchain-sambanova\n", "```\n", "\n", "### Credentials\n", "\n", "Get an API Key from [cloud.sambanova.ai](https://cloud.sambanova.ai/apis) and add it to your environment variables:\n", "\n", "``` bash\n", "export SAMBANOVA_API_KEY=\"your-api-key-here\"\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.getenv(\"SAMBANOVA_API_KEY\"):\n", "    os.environ[\"SAMBANOVA_API_KEY\"] = getpass.getpass(\n", "        \"Enter your SambaNova Cloud API key: \"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass(\"Enter your LangSmith API key: \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain __SambaNovaCloud__ integration lives in the `langchain_sambanova` package:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-sambanova"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_sambanova import ChatSambaNovaCloud\n", "\n", "llm = ChatSambaNovaCloud(\n", "    model=\"Meta-Llama-3.3-70B-Instruct\",\n", "    max_tokens=1024,\n", "    temperature=0.7,\n", "    top_p=0.01,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"J'adore la programmation.\", additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'usage': {'acceptance_rate': 7, 'completion_tokens': 8, 'completion_tokens_after_first_per_sec': 195.0204119588971, 'completion_tokens_after_first_per_sec_first_ten': 618.3422770734173, 'completion_tokens_per_sec': 53.25837044790076, 'end_time': 1731535338.1864908, 'is_last_response': True, 'prompt_tokens': 55, 'start_time': 1731535338.0133238, 'time_to_first_token': 0.13727331161499023, 'total_latency': 0.15021112986973353, 'total_tokens': 63, 'total_tokens_per_sec': 419.4096672772185}, 'model_name': 'Meta-Llama-3.1-70B-Instruct', 'system_fingerprint': 'fastcoe', 'created': 1731535338}, id='f04b7c2c-bc46-47e0-9c6b-19a002e8f390')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = [\n", "    (\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. \"\n", "        \"Translate the user sentence.\",\n", "    ),\n", "    (\"human\", \"I love programming.\"),\n", "]\n", "ai_msg = llm.invoke(messages)\n", "ai_msg"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore la programmation.\n"]}], "source": ["print(ai_msg.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Ich liebe das Programmieren.', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'usage': {'acceptance_rate': 2.3333333333333335, 'completion_tokens': 6, 'completion_tokens_after_first_per_sec': 106.06729752831038, 'completion_tokens_after_first_per_sec_first_ten': 204.92722183833433, 'completion_tokens_per_sec': 26.32497272023831, 'end_time': 1731535339.9997504, 'is_last_response': True, 'prompt_tokens': 50, 'start_time': 1731535339.7539687, 'time_to_first_token': 0.19864177703857422, 'total_latency': 0.22792046410696848, 'total_tokens': 56, 'total_tokens_per_sec': 245.6997453888909}, 'model_name': 'Meta-Llama-3.1-70B-Instruct', 'system_fingerprint': 'fastcoe', 'created': 1731535339}, id='dfe0bee6-b297-472e-ac9d-29906d162dcb')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} \"\n", "            \"to {output_language}.\",\n", "        ),\n", "        (\"human\", \"{input}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "chain.invoke(\n", "    {\n", "        \"input_language\": \"English\",\n", "        \"output_language\": \"German\",\n", "        \"input\": \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Streaming"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Yer lookin' fer some knowledge about owls, eh? Alright then, matey, settle yerself down with a pint o' grog and listen close. \n", "\n", "Owls be a fascinatin' lot, with their big round eyes and silent wings. They be birds o' prey, which means they hunt other creatures fer food. There be over 220 species o' owls, rangin' in size from the tiny Elf Owl (which be smaller than a parrot) to the Great Grey Owl (which be as big as a small eagle).\n", "\n", "One o' the most interestin' things about owls be their eyes. They be huge, with some species havin' eyes that be as big as their brains! This lets 'em see in the dark, which be perfect fer nocturnal huntin'. They also have special feathers on their faces that help 'em hear better, and their ears be specially designed to pinpoint sounds.\n", "\n", "Owls be known fer their silent flight, which be due to the special shape o' their wings. They be able to fly without makin' a sound, which be perfect fer sneakin' up on prey. They also be very agile, with some species able to fly through tight spaces and make sharp turns.\n", "\n", "Some o' the most common species o' owls include:\n", "\n", "* Barn Owl: A medium-sized owl with a heart-shaped face and a screechin' call.\n", "* Tawny Owl: A large owl with a distinctive hootin' call and a reddish-brown plumage.\n", "* Great Horned Owl: A big owl with ear tufts and a deep hootin' call.\n", "* Snowy Owl: A white owl with a round face and a soft, hootin' call.\n", "\n", "Owls be found all over the world, in a variety o' habitats, from forests to deserts. They be an important part o' many ecosystems, helpin' to keep populations o' small mammals and birds under control.\n", "\n", "So there ye have it, matey! Owls be amazin' creatures, with their big eyes, silent wings, and sharp talons. Now go forth and spread the word about these fascinatin' birds!"]}], "source": ["system = \"You are a helpful assistant with pirate accent.\"\n", "human = \"I want to learn more about this animal: {animal}\"\n", "prompt = ChatPromptTemplate.from_messages([(\"system\", system), (\"human\", human)])\n", "\n", "chain = prompt | llm\n", "\n", "for chunk in chain.stream({\"animal\": \"owl\"}):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Async"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='The capital of France is Paris.', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'usage': {'acceptance_rate': 1, 'completion_tokens': 7, 'completion_tokens_after_first_per_sec': 442.126212227688, 'completion_tokens_after_first_per_sec_first_ten': 0, 'completion_tokens_per_sec': 46.28540439646366, 'end_time': 1731535343.0321083, 'is_last_response': True, 'prompt_tokens': 42, 'start_time': 1731535342.8808727, 'time_to_first_token': 0.137664794921875, 'total_latency': 0.15123558044433594, 'total_tokens': 49, 'total_tokens_per_sec': 323.99783077524563}, 'model_name': 'Meta-Llama-3.1-70B-Instruct', 'system_fingerprint': 'fastcoe', 'created': 1731535342}, id='c4b8c714-df38-4206-9aa8-fc8231f7275a')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"human\",\n", "            \"what is the capital of {country}?\",\n", "        )\n", "    ]\n", ")\n", "\n", "chain = prompt | llm\n", "await chain.ainvoke({\"country\": \"France\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Async Streaming"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Quantum computers use quantum bits (qubits) to process info, leveraging superposition and entanglement to perform calculations exponentially faster than classical computers for certain complex problems."]}], "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"human\",\n", "            \"in less than {num_words} words explain me {topic} \",\n", "        )\n", "    ]\n", ")\n", "chain = prompt | llm\n", "\n", "async for chunk in chain.astream({\"num_words\": 30, \"topic\": \"quantum computers\"}):\n", "    print(chunk.content, end=\"\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> calling"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "from langchain_core.messages import HumanMessage, ToolMessage\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def get_time(kind: str = \"both\") -> str:\n", "    \"\"\"Returns current date, current time or both.\n", "    Args:\n", "        kind(str): date, time or both\n", "    Returns:\n", "        str: current date, current time or both\n", "    \"\"\"\n", "    if kind == \"date\":\n", "        date = datetime.now().strftime(\"%m/%d/%Y\")\n", "        return f\"Current date: {date}\"\n", "    elif kind == \"time\":\n", "        time = datetime.now().strftime(\"%H:%M:%S\")\n", "        return f\"Current time: {time}\"\n", "    else:\n", "        date = datetime.now().strftime(\"%m/%d/%Y\")\n", "        time = datetime.now().strftime(\"%H:%M:%S\")\n", "        return f\"Current date: {date}, Current time: {time}\"\n", "\n", "\n", "tools = [get_time]\n", "\n", "\n", "def invoke_tools(tool_calls, messages):\n", "    available_functions = {tool.name: tool for tool in tools}\n", "    for tool_call in tool_calls:\n", "        selected_tool = available_functions[tool_call[\"name\"]]\n", "        tool_output = selected_tool.invoke(tool_call[\"args\"])\n", "        print(f\"Tool output: {tool_output}\")\n", "        messages.append(ToolMessage(tool_output, tool_call_id=tool_call[\"id\"]))\n", "    return messages"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["llm_with_tools = llm.bind_tools(tools=tools)\n", "messages = [\n", "    HumanMessage(\n", "        content=\"I need to schedule a meeting for two weeks from today. \"\n", "        \"Can you tell me the exact date of the meeting?\"\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Intermediate model response: [{'name': 'get_time', 'args': {'kind': 'date'}, 'id': 'call_7352ce7a18e24a7c9d', 'type': 'tool_call'}]\n", "Tool output: Current date: 11/13/2024\n", "final response: The meeting should be scheduled for two weeks from November 13th, 2024.\n"]}], "source": ["response = llm_with_tools.invoke(messages)\n", "while len(response.tool_calls) > 0:\n", "    print(f\"Intermediate model response: {response.tool_calls}\")\n", "    messages.append(response)\n", "    messages = invoke_tools(response.tool_calls, messages)\n", "    response = llm_with_tools.invoke(messages)\n", "\n", "print(f\"final response: {response.content}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Structured Outputs"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["Joke(setup='Why did the cat join a band?', punchline='Because it wanted to be the purr-cussionist!')"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class Joke(BaseModel):\n", "    \"\"\"Joke to tell user.\"\"\"\n", "\n", "    setup: str = Field(description=\"The setup of the joke\")\n", "    punchline: str = Field(description=\"The punchline to the joke\")\n", "\n", "\n", "structured_llm = llm.with_structured_output(Joke)\n", "\n", "structured_llm.invoke(\"Tell me a joke about cats\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Input Image"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["multimodal_llm = ChatSambaNovaCloud(\n", "    model=\"Llama-3.2-11B-Vision-Instruct\",\n", "    max_tokens=1024,\n", "    temperature=0.7,\n", "    top_p=0.01,\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The weather in this image is a serene and peaceful atmosphere, with a blue sky and white clouds, suggesting a pleasant day with mild temperatures and gentle breezes.\n"]}], "source": ["import base64\n", "\n", "import httpx\n", "\n", "image_url = (\n", "    \"https://images.pexels.com/photos/147411/italy-mountains-dawn-daybreak-147411.jpeg\"\n", ")\n", "image_data = base64.b64encode(httpx.get(image_url).content).decode(\"utf-8\")\n", "\n", "message = HumanMessage(\n", "    content=[\n", "        {\"type\": \"text\", \"text\": \"describe the weather in this image in 1 sentence\"},\n", "        {\n", "            \"type\": \"image_url\",\n", "            \"image_url\": {\"url\": f\"data:image/jpeg;base64,{image_data}\"},\n", "        },\n", "    ],\n", ")\n", "response = multimodal_llm.invoke([message])\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all SambaNovaCloud features and configurations head to the API reference: https://docs.sambanova.ai/cloud/docs/get-started/overview"]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}