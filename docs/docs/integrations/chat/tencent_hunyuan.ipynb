{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: <PERSON><PERSON>\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tencent <PERSON>\n", "\n", ">[Tencent's hybrid model API](https://cloud.tencent.com/document/product/1729) (`Hunyuan API`) \n", "> implements dialogue communication, content generation, \n", "> analysis and understanding, and can be widely used in various scenarios such as intelligent \n", "> customer service, intelligent marketing, role playing, advertising copywriting, product description,\n", "> script creation, resume generation, article writing, code generation, data analysis, and content\n", "> analysis.\n", "\n", "See [more information](https://cloud.tencent.com/document/product/1729) for more details."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2023-10-19T10:20:38.718834Z", "start_time": "2023-10-19T10:20:38.264050Z"}}, "outputs": [], "source": ["from langchain_community.chat_models import ChatHunyuan\n", "from langchain_core.messages import HumanMessage"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2023-10-19T10:19:53.529876Z", "start_time": "2023-10-19T10:19:53.526210Z"}}, "outputs": [], "source": ["chat = ChatHunyuan(\n", "    hunyuan_app_id=111111111,\n", "    hunyuan_secret_id=\"YOUR_SECRET_ID\",\n", "    hunyuan_secret_key=\"YOUR_SECRET_KEY\",\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2023-10-19T10:19:56.054289Z", "start_time": "2023-10-19T10:19:53.531078Z"}}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"J'aime programmer.\")"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["chat(\n", "    [\n", "        HumanMessage(\n", "            content=\"You are a helpful assistant that translates English to French.Translate this sentence from English to French. I love programming.\"\n", "        )\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {"collapsed": false, "jupyter": {"outputs_hidden": false}}, "source": ["## Using ChatHunyuan with Streaming"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2023-10-19T10:20:41.507720Z", "start_time": "2023-10-19T10:20:41.496456Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": ["chat = ChatHunyuan(\n", "    hunyuan_app_id=\"YOUR_APP_ID\",\n", "    hunyuan_secret_id=\"YOUR_SECRET_ID\",\n", "    hunyuan_secret_key=\"YOUR_SECRET_KEY\",\n", "    streaming=True,\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2023-10-19T10:20:46.275673Z", "start_time": "2023-10-19T10:20:44.241097Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [{"data": {"text/plain": ["AIMessageChunk(content=\"J'aime programmer.\")"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["chat(\n", "    [\n", "        HumanMessage(\n", "            content=\"You are a helpful assistant that translates English to French.Translate this sentence from English to French. I love programming.\"\n", "        )\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"start_time": "2023-10-19T10:19:56.233477Z"}, "collapsed": false, "jupyter": {"outputs_hidden": false}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}