{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: PremAI\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatPremAI\n", "\n", "[PremAI](https://premai.io/) is an all-in-one platform that simplifies the creation of robust, production-ready applications powered by Generative AI. By streamlining the development process, PremAI allows you to concentrate on enhancing user experience and driving overall growth for your application. You can quickly start using our platform [here](https://docs.premai.io/quick-start).\n", "\n", "This example goes over how to use <PERSON><PERSON><PERSON><PERSON> to interact with different chat models with `ChatPremAI`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation and setup\n", "\n", "We start by installing `langchain` and `premai-sdk`. You can type the following command to install:\n", "\n", "```bash\n", "pip install premai langchain\n", "```\n", "\n", "Before proceeding further, please make sure that you have made an account on PremAI and already created a project. If not, please refer to the [quick start](https://docs.premai.io/introduction) guide to get started with the PremAI platform. Create your first project and grab your API key."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models import ChatPremAI\n", "from langchain_core.messages import HumanMessage, SystemMessage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setup PremAI client in LangChain\n", "\n", "Once we imported our required modules, let's setup our client. For now let's assume that our `project_id` is `8`. But make sure you use your project-id, otherwise it will throw error.\n", "\n", "To use langchain with prem, you do not need to pass any model name or set any parameters with our chat-client. By default it will use the model name and parameters used in the [LaunchPad](https://docs.premai.io/get-started/launchpad). \n", "\n", "> Note: If you change the `model` or any other parameters like `temperature`  or `max_tokens` while setting the client, it will override existing default configurations, that was used in LaunchPad.   "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "# First step is to set up the env variable.\n", "# you can also pass the API key while instantiating the model but this\n", "# comes under a best practices to set it as env variable.\n", "\n", "if os.environ.get(\"PREMAI_API_KEY\") is None:\n", "    os.environ[\"PREMAI_API_KEY\"] = getpass.getpass(\"PremAI API Key:\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# By default it will use the model which was deployed through the platform\n", "# in my case it will is \"gpt-4o\"\n", "\n", "chat = ChatPremAI(project_id=1234, model_name=\"gpt-4o\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Chat <PERSON>\n", "\n", "`ChatPremAI` supports two methods: `invoke` (which is the same as `generate`) and `stream`. \n", "\n", "The first one will give us a static result. Whereas the second one will stream tokens one by one. Here's how you can generate chat-like completions. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I am an AI language model created by OpenAI, designed to assist with answering questions and providing information based on the context provided. How can I help you today?\n"]}], "source": ["human_message = HumanMessage(content=\"Who are you?\")\n", "\n", "response = chat.invoke([human_message])\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Above looks interesting right? I set my default lanchpad system-prompt as: `Always sound like a pirate` You can also, override the default system prompt if you need to. Here's how you can do it. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"I'm your friendly assistant! How can I help you today?\", response_metadata={'document_chunks': [{'repository_id': 1985, 'document_id': 1306, 'chunk_id': 173899, 'document_name': '[D] Difference between sparse and dense informati…', 'similarity_score': 0.****************, 'content': \"with the difference or anywhere\\nwhere I can read about it?\\n\\n\\n      17                  9\\n\\n\\n      u/ScotiabankCanada        •  Promoted\\n\\n\\n                       Accelerate your study permit process\\n                       with Scotiabank's Student GIC\\n                       Program. We're here to help you tur…\\n\\n\\n                       startright.scotiabank.com         Learn More\\n\\n\\n                            Add a Comment\\n\\n\\nSort by:   Best\\n\\n\\n      DinosParkour      • 1y ago\\n\\n\\n     Dense Retrieval (DR) m\"}]}, id='run-510bbd0e-3f8f-4095-9b1f-c2d29fd89719-0')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["system_message = SystemMessage(content=\"You are a friendly assistant.\")\n", "human_message = HumanMessage(content=\"Who are you?\")\n", "\n", "chat.invoke([system_message, human_message])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can provide system prompt here like this:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/prem/langchain/libs/community/langchain_community/chat_models/premai.py:355: UserWarning: WARNING: Parameter top_p is not supported in kwargs.\n", "  warnings.warn(f\"WARNING: Parameter {key} is not supported in kwargs.\")\n"]}, {"data": {"text/plain": ["AIMessage(content=\"Hello! I'm your friendly assistant. How can I\", response_metadata={'document_chunks': [{'repository_id': 1985, 'document_id': 1306, 'chunk_id': 173899, 'document_name': '[D] Difference between sparse and dense informati…', 'similarity_score': 0.****************, 'content': \"with the difference or anywhere\\nwhere I can read about it?\\n\\n\\n      17                  9\\n\\n\\n      u/ScotiabankCanada        •  Promoted\\n\\n\\n                       Accelerate your study permit process\\n                       with Scotiabank's Student GIC\\n                       Program. We're here to help you tur…\\n\\n\\n                       startright.scotiabank.com         Learn More\\n\\n\\n                            Add a Comment\\n\\n\\nSort by:   Best\\n\\n\\n      DinosParkour      • 1y ago\\n\\n\\n     Dense Retrieval (DR) m\"}]}, id='run-c4b06b98-4161-4cca-8495-fd2fc98fa8f8-0')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.invoke([system_message, human_message], temperature=0.7, max_tokens=10, top_p=0.95)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> If you are going to place system prompt here, then it will override your system prompt that was fixed while deploying the application from the platform. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Native RAG Support with Prem Repositories\n", "\n", "Prem Repositories which allows users to upload documents (.txt, .pdf etc) and connect those repositories to the LLMs. You can think Prem repositories as native RAG, where each repository can be considered as a vector database. You can connect multiple repositories. You can learn more about repositories [here](https://docs.premai.io/get-started/repositories).\n", "\n", "Repositories are also supported in langchain premai. Here is how you can do it. "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["query = \"Which models are used for dense retrieval\"\n", "repository_ids = [\n", "    1985,\n", "]\n", "repositories = dict(ids=repository_ids, similarity_threshold=0.3, limit=3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First we start by defining our repository with some repository ids. Make sure that the ids are valid repository ids. You can learn more about how to get the repository id [here](https://docs.premai.io/get-started/repositories). \n", "\n", "> Please note: Similar like `model_name` when you invoke the argument `repositories`, then you are potentially overriding the repositories connected in the launchpad. \n", "\n", "Now, we connect the repository with our chat object to invoke RAG based generations. "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense retrieval models typically include:\n", "\n", "1. **BERT-based Models**: Such as DPR (Dense Passage Retrieval) which uses BERT for encoding queries and passages.\n", "2. **ColBERT**: A model that combines BERT with late interaction mechanisms.\n", "3. **ANCE (Approximate Nearest Neighbor Negative Contrastive Estimation)**: Uses BERT and focuses on efficient retrieval.\n", "4. **TCT-ColBERT**: A variant of ColBERT that uses a two-tower\n", "{\n", "    \"document_chunks\": [\n", "        {\n", "            \"repository_id\": 1985,\n", "            \"document_id\": 1306,\n", "            \"chunk_id\": 173899,\n", "            \"document_name\": \"[D] Difference between sparse and dense informati\\u2026\",\n", "            \"similarity_score\": 0.****************,\n", "            \"content\": \"with the difference or anywhere\\nwhere I can read about it?\\n\\n\\n      17                  9\\n\\n\\n      u/ScotiabankCanada        \\u2022  Promoted\\n\\n\\n                       Accelerate your study permit process\\n                       with Scotiabank's Student GIC\\n                       Program. We're here to help you tur\\u2026\\n\\n\\n                       startright.scotiabank.com         Learn More\\n\\n\\n                            Add a Comment\\n\\n\\nSort by:   Best\\n\\n\\n      DinosParkour      \\u2022 1y ago\\n\\n\\n     Dense Retrieval (DR) m\"\n", "        }\n", "    ]\n", "}\n"]}], "source": ["import json\n", "\n", "response = chat.invoke(query, max_tokens=100, repositories=repositories)\n", "\n", "print(response.content)\n", "print(json.dumps(response.response_metadata, indent=4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> Ideally, you do not need to connect Repository IDs here to get Retrieval Augmented Generations. You can still get the same result if you have connected the repositories in prem platform. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prem Templates\n", "\n", "Writing Prompt Templates can be super messy. Prompt templates are long, hard to manage, and must be continuously tweaked to improve and keep the same throughout the application. \n", "\n", "With **Prem**, writing and managing prompts can be super easy. The **_Templates_** tab inside the [launchpad](https://docs.premai.io/get-started/launchpad) helps you write as many prompts you need and use it inside the SDK to make your application running using those prompts. You can read more about Prompt Templates [here](https://docs.premai.io/get-started/prem-templates). \n", "\n", "To use Prem Templates natively with <PERSON><PERSON><PERSON><PERSON>, you need to pass an id the `HumanMessage`. This id should be the name the variable of your prompt template. the `content` in `HumanMessage` should be the value of that variable. \n", "\n", "let's say for example, if your prompt template was this:\n", "\n", "```text\n", "Say hello to my name and say a feel-good quote\n", "from my age. My name is: {name} and age is {age}\n", "```\n", "\n", "So now your human_messages should look like:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["human_messages = [\n", "    HumanMessage(content=\"Shawn\", id=\"name\"),\n", "    HumanMessage(content=\"22\", id=\"age\"),\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "Pass this `human_messages` to ChatPremAI Client. Please note: Do not forget to\n", "pass the additional `template_id` to invoke generation with Prem Templates. If you are not aware of `template_id` you can learn more about that [in our docs](https://docs.premai.io/get-started/prem-templates). Here is an example:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["template_id = \"78069ce8-xxxxx-xxxxx-xxxx-xxx\"\n", "response = chat.invoke([human_messages], template_id=template_id)\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Prem Template feature is available in streaming too. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Streaming\n", "\n", "In this section, let's see how we can stream tokens using langchain and PremAI. Here's how you do it. "]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["It looks like your message got cut off. If you need information about Dense Retrieval (DR) or any other topic, please provide more details or clarify your question."]}], "source": ["import sys\n", "\n", "for chunk in chat.stream(\"hello how are you\"):\n", "    sys.stdout.write(chunk.content)\n", "    sys.stdout.flush()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Similar to above, if you want to override the system-prompt and the generation parameters, you need to add the following:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Woof! 🐾 How can I help you today? Want to play fetch or maybe go for a walk 🐶🦴"]}], "source": ["import sys\n", "\n", "# For some experimental reasons if you want to override the system prompt then you\n", "# can pass that here too. However it is not recommended to override system prompt\n", "# of an already deployed model.\n", "\n", "for chunk in chat.stream(\n", "    \"hello how are you\",\n", "    system_prompt=\"act like a dog\",\n", "    temperature=0.7,\n", "    max_tokens=200,\n", "):\n", "    sys.stdout.write(chunk.content)\n", "    sys.stdout.flush()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tool/Function Calling\n", "\n", "LangChain PremAI supports tool/function calling. Tool/function calling allows a model to respond to a given prompt by generating output that matches a user-defined schema. \n", "\n", "- You can learn all about tool calling in details [in our documentation here](https://docs.premai.io/get-started/function-calling).\n", "- You can learn more about langchain tool calling in [this part of the docs](https://python.langchain.com/v0.1/docs/modules/model_io/chat/function_calling).\n", "\n", "**NOTE:**\n", "The current version of LangChain ChatPremAI do not support function/tool calling with streaming support. Streaming support along with function calling will come soon. \n", "\n", "#### Passing tools to model\n", "\n", "In order to pass tools and let the LLM choose the tool it needs to call, we need to pass a tool schema. A tool schema is the function definition along with proper docstring on what does the function do, what each argument of the function is etc. Below are some simple arithmetic functions with their schema. \n", "\n", "**NOTE:** When defining function/tool schema, do not forget to add information around the function arguments, otherwise it would throw error."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "# Define the schema for function arguments\n", "class OperationInput(BaseModel):\n", "    a: int = Field(description=\"First number\")\n", "    b: int = Field(description=\"Second number\")\n", "\n", "\n", "# Now define the function where schema for argument will be OperationInput\n", "@tool(\"add\", args_schema=OperationInput, return_direct=True)\n", "def add(a: int, b: int) -> int:\n", "    \"\"\"Adds a and b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a + b\n", "\n", "\n", "@tool(\"multiply\", args_schema=OperationInput, return_direct=True)\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiplies a and b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a * b"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Binding tool schemas with our LLM\n", "\n", "We will now use the `bind_tools` method to convert our above functions to a \"tool\" and binding it with the model. This means we are going to pass these tool informations everytime we invoke the model. "]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["tools = [add, multiply]\n", "llm_with_tools = chat.bind_tools(tools)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After this, we get the response from the model which is now binded with the tools. "]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["query = \"What is 3 * 12? Also, what is 11 + 49?\"\n", "\n", "messages = [HumanMessage(query)]\n", "ai_msg = llm_with_tools.invoke(messages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As we can see, when our chat model is binded with tools, then based on the given prompt, it calls the correct set of the tools and sequentially. "]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'multiply',\n", "  'args': {'a': 3, 'b': 12},\n", "  'id': 'call_A9FL20u12lz6TpOLaiS6rFa8'},\n", " {'name': 'add',\n", "  'args': {'a': 11, 'b': 49},\n", "  'id': 'call_MPKYGLHbf39csJIyb5BZ9xIk'}]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["ai_msg.tool_calls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We append this message shown above to the LLM which acts as a context and makes the LLM aware that what all functions it has called. "]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["messages.append(ai_msg)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Since tool calling happens into two phases, where:\n", "\n", "1. in our first call, we gathered all the tools that the LLM decided to tool, so that it can get the result as an added context to give more accurate and hallucination free result. \n", "\n", "2. in our second call, we will parse those set of tools decided by LLM and run them (in our case it will be the functions we defined, with the LLM's extracted arguments) and pass this result to the LLM"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import ToolMessage\n", "\n", "for tool_call in ai_msg.tool_calls:\n", "    selected_tool = {\"add\": add, \"multiply\": multiply}[tool_call[\"name\"].lower()]\n", "    tool_output = selected_tool.invoke(tool_call[\"args\"])\n", "    messages.append(ToolMessage(tool_output, tool_call_id=tool_call[\"id\"]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, we call the LLM (binded with the tools) with the function response added in it's context. "]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The final answers are:\n", "\n", "- 3 * 12 = 36\n", "- 11 + 49 = 60\n"]}], "source": ["response = llm_with_tools.invoke(messages)\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Defining tool schemas: Pydantic class\n", "\n", "Above we have shown how to define schema using `tool` decorator, however we can equivalently define the schema using Pydantic. Pydantic is useful when your tool inputs are more complex:"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers.openai_tools import PydanticToolsParser\n", "\n", "\n", "class add(BaseModel):\n", "    \"\"\"Add two integers together.\"\"\"\n", "\n", "    a: int = Field(..., description=\"First integer\")\n", "    b: int = Field(..., description=\"Second integer\")\n", "\n", "\n", "class multiply(BaseModel):\n", "    \"\"\"Multiply two integers together.\"\"\"\n", "\n", "    a: int = Field(..., description=\"First integer\")\n", "    b: int = Field(..., description=\"Second integer\")\n", "\n", "\n", "tools = [add, multiply]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we can bind them to chat models and directly get the result:"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["[multiply(a=3, b=12), add(a=11, b=49)]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["chain = llm_with_tools | PydanticToolsParser(tools=[multiply, add])\n", "chain.invoke(query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, as done above, we parse this and run this functions and call the LLM once again to get the result."]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}