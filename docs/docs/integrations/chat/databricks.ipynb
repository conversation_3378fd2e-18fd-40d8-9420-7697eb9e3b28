{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Databricks\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatDatabricks\n", "\n", "> [Databricks](https://www.databricks.com/) Lakehouse Platform unifies data, analytics, and AI on one platform. \n", "\n", "This notebook provides a quick overview for getting started with Databricks [chat models](/docs/concepts/chat_models). For detailed documentation of all ChatDatabricks features and configurations head to the [API reference](https://python.langchain.com/api_reference/community/chat_models/langchain_community.chat_models.databricks.ChatDatabricks.html).\n", "\n", "## Overview\n", "\n", "`ChatDatabricks` class wraps a chat model endpoint hosted on [Databricks Model Serving](https://docs.databricks.com/en/machine-learning/model-serving/index.html). This example notebook shows how to wrap your serving endpoint and use it as a chat model in your LangChain application.\n", "\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [ChatDatabricks](https://python.langchain.com/api_reference/community/chat_models/langchain_community.chat_models.databricks.ChatDatabricks.html) | [databricks-langchain](https://python.langchain.com/docs/integrations/providers/databricks/) | ❌ | beta | ![PyPI - Downloads](https://img.shields.io/pypi/dm/langchain-databricks?style=flat-square&label=%20) | ![PyPI - Version](https://img.shields.io/pypi/v/langchain-databricks?style=flat-square&label=%20) |\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling/) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |  ✅ | ✅ | ✅ | ❌ | \n", "\n", "### Supported Methods\n", "\n", "`ChatDatabricks` supports all methods of `ChatModel` including async APIs.\n", "\n", "\n", "### Endpoint Requirement\n", "\n", "The serving endpoint `ChatDatabricks` wraps must have OpenAI-compatible chat input/output format ([reference](https://mlflow.org/docs/latest/llms/deployments/index.html#chat)). As long as the input format is compatible, `ChatDatabricks` can be used for any endpoint type hosted on [Databricks Model Serving](https://docs.databricks.com/en/machine-learning/model-serving/index.html):\n", "\n", "1. Foundation Models - Curated list of state-of-the-art foundation models such as DRBX, Llama3, Mixtral-8x7B, and etc. These endpoint are ready to use in your Databricks workspace without any set up.\n", "2. Custom Models - You can also deploy custom models to a serving endpoint via MLflow with\n", "your choice of framework such as LangChain, Pytorch, Transformers, etc.\n", "3. External Models - Databricks endpoints can serve models that are hosted outside Databricks as a proxy, such as proprietary model service like OpenAI GPT4.\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["## Setup\n", "\n", "To access Databricks models you'll need to create a Databricks account, set up credentials (only if you are outside Databricks workspace), and install required packages.\n", "\n", "### Credentials (only if you are outside Databricks)\n", "\n", "If you are running LangChain app inside Databricks, you can skip this step.\n", "\n", "Otherwise, you need manually set the Databricks workspace hostname and personal access token to `DATABRICKS_HOST` and `DATABRICKS_TOKEN` environment variables, respectively. See [Authentication Documentation](https://docs.databricks.com/en/dev-tools/auth/index.html#databricks-personal-access-tokens) for how to get an access token."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter your Databricks access token:  ········\n"]}], "source": ["import getpass\n", "import os\n", "\n", "os.environ[\"DATABRICKS_HOST\"] = \"https://your-workspace.cloud.databricks.com\"\n", "if \"DATABRICKS_TOKEN\" not in os.environ:\n", "    os.environ[\"DATABRICKS_TOKEN\"] = getpass.getpass(\n", "        \"Enter your Databricks access token: \"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain Databricks integration lives in the `databricks-langchain` package."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -qU databricks-langchain"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We first demonstrates how to query DBRX-instruct model hosted as Foundation Models endpoint with `ChatDatabricks`.\n", "\n", "For other type of endpoints, there are some difference in how to set up the endpoint itself, however, once the endpoint is ready, there is no difference in how to query it with `ChatDatabricks`. Please refer to the bottom of this notebook for the examples with other type of endpoints."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from databricks_langchain import ChatDatabricks\n", "\n", "chat_model = ChatDatabricks(\n", "    endpoint=\"databricks-dbrx-instruct\",\n", "    temperature=0.1,\n", "    max_tokens=256,\n", "    # See https://python.langchain.com/api_reference/community/chat_models/langchain_community.chat_models.databricks.ChatDatabricks.html for other supported parameters\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='MLflow is an open-source platform for managing end-to-end machine learning workflows. It was introduced by Databricks in 2018. MLflow provides tools for tracking experiments, packaging and sharing code, and deploying models. It is designed to work with any machine learning library and can be used in a variety of environments, including local machines, virtual machines, and cloud-based clusters. MLflow aims to streamline the machine learning development lifecycle, making it easier for data scientists and engineers to collaborate and deploy models into production.', response_metadata={'prompt_tokens': 229, 'completion_tokens': 104, 'total_tokens': 333}, id='run-d3fb4d06-3e10-4471-83c9-c282cc62b74d-0')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["chat_model.invoke(\"What is MLflow?\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Databricks Model Serving is a feature of the Databricks platform that allows data scientists and engineers to easily deploy machine learning models into production. With Model Serving, you can host, manage, and serve machine learning models as APIs, making it easy to integrate them into applications and business processes. It supports a variety of popular machine learning frameworks, including TensorFlow, PyTorch, and scikit-learn, and provides tools for monitoring and managing the performance of deployed models. Model Serving is designed to be scalable, secure, and easy to use, making it a great choice for organizations that want to quickly and efficiently deploy machine learning models into production.', response_metadata={'prompt_tokens': 35, 'completion_tokens': 130, 'total_tokens': 165}, id='run-b3feea21-223e-4105-8627-41d647d5ccab-0')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# You can also pass a list of messages\n", "messages = [\n", "    (\"system\", \"You are a chatbot that can answer questions about Databricks.\"),\n", "    (\"user\", \"What is Databricks Model Serving?\"),\n", "]\n", "chat_model.invoke(messages)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chaining\n", "Similar to other chat models, `ChatDatabricks` can be used as a part of a complex chain."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"Unity Catalog is a new data catalog feature in Databricks that allows you to discover, manage, and govern all your data assets across your data landscape, including data lakes, data warehouses, and data marts. It provides a centralized repository for storing and managing metadata, data lineage, and access controls for all your data assets. Unity Catalog enables data teams to easily discover and access the data they need, while ensuring compliance with data privacy and security regulations. It is designed to work seamlessly with Databricks' Lakehouse platform, providing a unified experience for managing and analyzing all your data.\", response_metadata={'prompt_tokens': 32, 'completion_tokens': 118, 'total_tokens': 150}, id='run-82d72624-f8df-4c0d-a976-919feec09a55-0')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a chatbot that can answer questions about {topic}.\",\n", "        ),\n", "        (\"user\", \"{question}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | chat_model\n", "chain.invoke(\n", "    {\n", "        \"topic\": \"Databricks\",\n", "        \"question\": \"What is Unity Catalog?\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invocation (streaming)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I|'m| an| AI| and| don|'t| have| feelings|,| but| I|'m| here| and| ready| to| assist| you|.| How| can| I| help| you| today|?||"]}], "source": ["for chunk in chat_model.stream(\"How are you?\"):\n", "    print(chunk.content, end=\"|\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Async Invocation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "\n", "country = [\"Japan\", \"Italy\", \"Australia\"]\n", "futures = [chat_model.ainvoke(f\"Where is the capital of {c}?\") for c in country]\n", "await asyncio.gather(*futures)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> calling\n", "\n", "ChatDatabricks supports OpenAI-compatible tool calling API that lets you describe tools and their arguments, and have the model return a JSON object with a tool to invoke and the inputs to that tool. tool-calling is extremely useful for building tool-using chains and agents, and for getting structured outputs from models more generally.\n", "\n", "With `ChatDatabricks.bind_tools`, we can easily pass in Pydantic classes, dict schemas, LangChain tools, or even functions as tools to the model. Under the hood these are converted to the OpenAI-compatible tool schemas, which looks like:\n", "\n", "```\n", "{\n", "    \"name\": \"...\",\n", "    \"description\": \"...\",\n", "    \"parameters\": {...}  # JSONSchema\n", "}\n", "```\n", "\n", "and passed in every model invocation."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class GetWeather(BaseModel):\n", "    \"\"\"Get the current weather in a given location\"\"\"\n", "\n", "    location: str = Field(..., description=\"The city and state, e.g. San Francisco, CA\")\n", "\n", "\n", "class GetPopulation(BaseModel):\n", "    \"\"\"Get the current population in a given location\"\"\"\n", "\n", "    location: str = Field(..., description=\"The city and state, e.g. San Francisco, CA\")\n", "\n", "\n", "llm_with_tools = chat_model.bind_tools([GetWeather, GetPopulation])\n", "ai_msg = llm_with_tools.invoke(\n", "    \"Which city is hotter today and which is bigger: LA or NY?\"\n", ")\n", "print(ai_msg.tool_calls)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Wrapping Custom Model Endpoint\n", "\n", "Prerequisites:\n", "\n", "* An LLM was registered and deployed to [a Databricks serving endpoint](https://docs.databricks.com/machine-learning/model-serving/index.html) via MLflow. The endpoint must have OpenAI-compatible chat input/output format ([reference](https://mlflow.org/docs/latest/llms/deployments/index.html#chat))\n", "* You have [\"Can Query\" permission](https://docs.databricks.com/security/auth-authz/access-control/serving-endpoint-acl.html) to the endpoint.\n", "\n", "Once the endpoint is ready, the usage pattern is identical to that of Foundation Models."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat_model_custom = ChatDatabricks(\n", "    endpoint=\"YOUR_ENDPOINT_NAME\",\n", "    temperature=0.1,\n", "    max_tokens=256,\n", ")\n", "\n", "chat_model_custom.invoke(\"How are you?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Wrapping External Models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Prerequisite: Create Proxy Endpoint\n", "\n", "First, create a new Databricks serving endpoint that proxies requests to the target external model. The endpoint creation should be fairy quick for proxying external models.\n", "\n", "This requires registering your OpenAI API Key within the Databricks secret manager as follows:\n", "```sh\n", "# Replace `<scope>` with your scope\n", "databricks secrets create-scope <scope>\n", "databricks secrets put-secret <scope> openai-api-key --string-value $OPENAI_API_KEY\n", "```\n", "\n", "For how to set up Databricks CLI and manage secrets, please refer to https://docs.databricks.com/en/security/secrets/secrets.html"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from mlflow.deployments import get_deploy_client\n", "\n", "client = get_deploy_client(\"databricks\")\n", "\n", "secret = \"secrets/<scope>/openai-api-key\"  # replace `<scope>` with your scope\n", "endpoint_name = \"my-chat\"  # rename this if my-chat already exists\n", "client.create_endpoint(\n", "    name=endpoint_name,\n", "    config={\n", "        \"served_entities\": [\n", "            {\n", "                \"name\": \"my-chat\",\n", "                \"external_model\": {\n", "                    \"name\": \"gpt-3.5-turbo\",\n", "                    \"provider\": \"openai\",\n", "                    \"task\": \"llm/v1/chat\",\n", "                    \"openai_config\": {\n", "                        \"openai_api_key\": \"{{\" + secret + \"}}\",\n", "                    },\n", "                },\n", "            }\n", "        ],\n", "    },\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once the endpoint status has become \"Ready\", you can query the endpoint in the same way as other types of endpoints."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat_model_external = ChatDatabricks(\n", "    endpoint=endpoint_name,\n", "    temperature=0.1,\n", "    max_tokens=256,\n", ")\n", "chat_model_external.invoke(\"How to use Databricks?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Function calling on Databricks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Databricks Function Calling is OpenAI-compatible and is only available during model serving as part of Foundation Model APIs.\n", "\n", "See [Databricks function calling introduction](https://docs.databricks.com/en/machine-learning/model-serving/function-calling.html#supported-models) for supported models."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llm = ChatDatabricks(endpoint=\"databricks-meta-llama-3-70b-instruct\")\n", "tools = [\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_current_weather\",\n", "            \"description\": \"Get the current weather in a given location\",\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"location\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The city and state, e.g. San Francisco, CA\",\n", "                    },\n", "                    \"unit\": {\"type\": \"string\", \"enum\": [\"celsius\", \"fahrenheit\"]},\n", "                },\n", "            },\n", "        },\n", "    }\n", "]\n", "\n", "# supported tool_choice values: \"auto\", \"required\", \"none\", function name in string format,\n", "# or a dictionary as {\"type\": \"function\", \"function\": {\"name\": <<tool_name>>}}\n", "model = llm.bind_tools(tools, tool_choice=\"auto\")\n", "\n", "messages = [{\"role\": \"user\", \"content\": \"What is the current temperature of Chicago?\"}]\n", "print(model.invoke(messages))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["See [Databricks Unity Catalog](docs/integrations/tools/databricks.ipynb) about how to use UC functions in chains."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatDatabricks features and configurations head to the API reference: https://api-docs.databricks.com/python/databricks-ai-bridge/latest/databricks_langchain.html#databricks_langchain.ChatDatabricks"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}