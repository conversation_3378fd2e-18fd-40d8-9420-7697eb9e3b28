{"cells": [{"cell_type": "markdown", "id": "b3d1705d", "metadata": {}, "source": ["# GMail\n", "\n", "This loader goes over how to load data from GMail. There are many ways you could want to load data from GMail. This loader is currently fairly opinionated in how to do so. The way it does it is it first looks for all messages that you have sent. It then looks for messages where you are responding to a previous email. It then fetches that previous email, and creates a training example of that email, followed by your email.\n", "\n", "Note that there are clear limitations here. For example, all examples created are only looking at the previous email for context.\n", "\n", "To use:\n", "\n", "- Set up a Google Developer Account: Go to the Google Developer Console, create a project, and enable the Gmail API for that project. This will give you a credentials.json file that you'll need later.\n", "\n", "- Install the Google Client Library: Run the following command to install the Google Client Library:"]}, {"cell_type": "code", "execution_count": null, "id": "********", "metadata": {}, "outputs": [], "source": ["%pip install --upgrade --quiet  google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client"]}, {"cell_type": "code", "execution_count": 6, "id": "be18f796", "metadata": {}, "outputs": [], "source": ["import os.path\n", "\n", "from google.auth.transport.requests import Request\n", "from google.oauth2.credentials import Credentials\n", "from google_auth_oauthlib.flow import InstalledAppFlow\n", "\n", "SCOPES = [\"https://www.googleapis.com/auth/gmail.readonly\"]\n", "\n", "\n", "creds = None\n", "# The file token.json stores the user's access and refresh tokens, and is\n", "# created automatically when the authorization flow completes for the first\n", "# time.\n", "if os.path.exists(\"email_token.json\"):\n", "    creds = Credentials.from_authorized_user_file(\"email_token.json\", SCOPES)\n", "# If there are no (valid) credentials available, let the user log in.\n", "if not creds or not creds.valid:\n", "    if creds and creds.expired and creds.refresh_token:\n", "        creds.refresh(Request())\n", "    else:\n", "        flow = InstalledAppFlow.from_client_secrets_file(\n", "            # your creds file here. Please create json file as here https://cloud.google.com/docs/authentication/getting-started\n", "            \"creds.json\",\n", "            SCOPES,\n", "        )\n", "        creds = flow.run_local_server(port=0)\n", "    # Save the credentials for the next run\n", "    with open(\"email_token.json\", \"w\") as token:\n", "        token.write(creds.to_json())"]}, {"cell_type": "code", "execution_count": 7, "id": "a2793ba0", "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_loaders.gmail import GMailLoader"]}, {"cell_type": "code", "execution_count": 10, "id": "2154597f", "metadata": {}, "outputs": [], "source": ["loader = GMailLoader(creds=creds, n=3)"]}, {"cell_type": "code", "execution_count": 11, "id": "0b7d11bd", "metadata": {}, "outputs": [], "source": ["data = loader.load()"]}, {"cell_type": "code", "execution_count": 13, "id": "74764bc7", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Sometimes there can be errors which we silently ignore\n", "len(data)"]}, {"cell_type": "code", "execution_count": 14, "id": "d9360a85", "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_loaders.utils import (\n", "    map_ai_messages,\n", ")"]}, {"cell_type": "code", "execution_count": 17, "id": "a9646f7a", "metadata": {}, "outputs": [], "source": ["# This makes messages <NAME_EMAIL> the AI Messages\n", "# This means you will train an LLM to predict as if it's responding as hchase\n", "training_data = list(\n", "    map_ai_messages(data, sender=\"<PERSON> <<EMAIL>>\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d1a182f0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.1"}}, "nbformat": 4, "nbformat_minor": 5}