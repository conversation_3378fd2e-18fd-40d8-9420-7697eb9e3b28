{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON> for Lang<PERSON><PERSON><PERSON>\n", "\n", "This notebook demonstrates how to use the `RedisCache` and `RedisSemanticCache` classes from the langchain-redis package to implement caching for LLM responses."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's install the required dependencies and ensure we have a Redis instance running."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -U langchain-core langchain-redis langchain-openai redis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Ensure you have a Redis server running. You can start one using Docker with:\n", "\n", "```\n", "docker run -d -p 6379:6379 redis:latest\n", "```\n", "\n", "Or install and run Redis locally according to your operating system's instructions."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connecting to Redis at: redis://redis:6379\n"]}], "source": ["import os\n", "\n", "# Use the environment variable if set, otherwise default to localhost\n", "REDIS_URL = os.getenv(\"REDIS_URL\", \"redis://localhost:6379\")\n", "print(f\"Connecting to Redis at: {REDIS_URL}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Importing Required Libraries"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from langchain.globals import set_llm_cache\n", "from langchain.schema import Generation\n", "from langchain_openai import OpenAI, OpenAIEmbeddings\n", "from langchain_redis import RedisCache, RedisSemanticCache"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import langchain_core\n", "import langchain_openai\n", "import openai\n", "import redis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Set OpenAI API key"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API key not found in environment variables.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Please enter your OpenAI API key:  ········\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OpenAI API key has been set for this session.\n"]}], "source": ["from getpass import getpass\n", "\n", "# Check if OPENAI_API_KEY is already set in the environment\n", "openai_api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "if not openai_api_key:\n", "    print(\"OpenAI API key not found in environment variables.\")\n", "    openai_api_key = getpass(\"Please enter your OpenAI API key: \")\n", "\n", "    # Set the API key for the current session\n", "    os.environ[\"OPENAI_API_KEY\"] = openai_api_key\n", "    print(\"OpenAI API key has been set for this session.\")\n", "else:\n", "    print(\"OpenAI API key found in environment variables.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using RedisCache"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First call (not cached):\n", "Result: \n", "\n", "Caching is the process of storing frequently accessed data in a temporary storage location for faster retrieval. This helps to reduce the time and resources needed to access the data from its original source. Caching is commonly used in computer systems, web browsers, and databases to improve performance and efficiency.\n", "Time: 1.16 seconds\n", "\n", "Second call (cached):\n", "Result: \n", "\n", "Caching is the process of storing frequently accessed data in a temporary storage location for faster retrieval. This helps to reduce the time and resources needed to access the data from its original source. Caching is commonly used in computer systems, web browsers, and databases to improve performance and efficiency.\n", "Time: 0.05 seconds\n", "\n", "Speed improvement: 25.40x faster\n", "<PERSON><PERSON> cleared\n"]}], "source": ["# Initialize RedisCache\n", "redis_cache = RedisCache(redis_url=REDIS_URL)\n", "\n", "# Set the cache for Lang<PERSON><PERSON><PERSON> to use\n", "set_llm_cache(redis_cache)\n", "\n", "# Initialize the language model\n", "llm = OpenAI(temperature=0)\n", "\n", "\n", "# Function to measure execution time\n", "def timed_completion(prompt):\n", "    start_time = time.time()\n", "    result = llm.invoke(prompt)\n", "    end_time = time.time()\n", "    return result, end_time - start_time\n", "\n", "\n", "# First call (not cached)\n", "prompt = \"Explain the concept of caching in three sentences.\"\n", "result1, time1 = timed_completion(prompt)\n", "print(f\"First call (not cached):\\nResult: {result1}\\nTime: {time1:.2f} seconds\\n\")\n", "\n", "# Second call (should be cached)\n", "result2, time2 = timed_completion(prompt)\n", "print(f\"Second call (cached):\\nResult: {result2}\\nTime: {time2:.2f} seconds\\n\")\n", "\n", "print(f\"Speed improvement: {time1 / time2:.2f}x faster\")\n", "\n", "# Clear the cache\n", "redis_cache.clear()\n", "print(\"Cache cleared\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using RedisSemanticCache"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original query:\n", "Prompt: What is the capital of France?\n", "Result: \n", "\n", "The capital of France is Paris.\n", "Time: 1.52 seconds\n", "\n", "Similar query:\n", "Prompt: Can you tell me the capital city of France?\n", "Result: \n", "\n", "The capital of France is Paris.\n", "Time: 0.29 seconds\n", "\n", "Speed improvement: 5.22x faster\n", "Semantic cache cleared\n"]}], "source": ["# Initialize RedisSemanticCache\n", "embeddings = OpenAIEmbeddings()\n", "semantic_cache = RedisSemanticCache(\n", "    redis_url=REDIS_URL, embeddings=embeddings, distance_threshold=0.2\n", ")\n", "\n", "# Set the cache for Lang<PERSON><PERSON><PERSON> to use\n", "set_llm_cache(semantic_cache)\n", "\n", "\n", "# Function to test semantic cache\n", "def test_semantic_cache(prompt):\n", "    start_time = time.time()\n", "    result = llm.invoke(prompt)\n", "    end_time = time.time()\n", "    return result, end_time - start_time\n", "\n", "\n", "# Original query\n", "original_prompt = \"What is the capital of France?\"\n", "result1, time1 = test_semantic_cache(original_prompt)\n", "print(\n", "    f\"Original query:\\nPrompt: {original_prompt}\\nResult: {result1}\\nTime: {time1:.2f} seconds\\n\"\n", ")\n", "\n", "# Semantically similar query\n", "similar_prompt = \"Can you tell me the capital city of France?\"\n", "result2, time2 = test_semantic_cache(similar_prompt)\n", "print(\n", "    f\"Similar query:\\nPrompt: {similar_prompt}\\nResult: {result2}\\nTime: {time2:.2f} seconds\\n\"\n", ")\n", "\n", "print(f\"Speed improvement: {time1 / time2:.2f}x faster\")\n", "\n", "# Clear the semantic cache\n", "semantic_cache.clear()\n", "print(\"Semantic cache cleared\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Usage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Custom TTL (Time-To-Live)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cached result: Cached response\n", "Waiting for TTL to expire...\n", "Result after TTL: Not found (expired)\n"]}], "source": ["# Initialize RedisCache with custom TTL\n", "ttl_cache = RedisCache(redis_url=REDIS_URL, ttl=5)  # 60 seconds TTL\n", "\n", "# Update a cache entry\n", "ttl_cache.update(\"test_prompt\", \"test_llm\", [Generation(text=\"Cached response\")])\n", "\n", "# Retrieve the cached entry\n", "cached_result = ttl_cache.lookup(\"test_prompt\", \"test_llm\")\n", "print(f\"Cached result: {cached_result[0].text if cached_result else 'Not found'}\")\n", "\n", "# Wait for TTL to expire\n", "print(\"Waiting for TTL to expire...\")\n", "time.sleep(6)\n", "\n", "# Try to retrieve the expired entry\n", "expired_result = ttl_cache.lookup(\"test_prompt\", \"test_llm\")\n", "print(\n", "    f\"Result after TTL: {expired_result[0].text if expired_result else 'Not found (expired)'}\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Customizing RedisSemanticCache"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original result: \n", "\n", "The largest planet in our solar system is Jupiter.\n", "Similar query result: \n", "\n", "The largest planet in our solar system is Jupiter.\n"]}], "source": ["# Initialize RedisSemanticCache with custom settings\n", "custom_semantic_cache = RedisSemanticCache(\n", "    redis_url=REDIS_URL,\n", "    embeddings=embeddings,\n", "    distance_threshold=0.1,  # Stricter similarity threshold\n", "    ttl=3600,  # 1 hour TTL\n", "    name=\"custom_cache\",  # Custom cache name\n", ")\n", "\n", "# Test the custom semantic cache\n", "set_llm_cache(custom_semantic_cache)\n", "\n", "test_prompt = \"What's the largest planet in our solar system?\"\n", "result, _ = test_semantic_cache(test_prompt)\n", "print(f\"Original result: {result}\")\n", "\n", "# Try a slightly different query\n", "similar_test_prompt = \"Which planet is the biggest in the solar system?\"\n", "similar_result, _ = test_semantic_cache(similar_test_prompt)\n", "print(f\"Similar query result: {similar_result}\")\n", "\n", "# Clean up\n", "custom_semantic_cache.clear()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "This notebook demonstrated the usage of `RedisCache` and `RedisSemanticCache` from the langchain-redis package. These caching mechanisms can significantly improve the performance of LLM-based applications by reducing redundant API calls and leveraging semantic similarity for intelligent caching. The Redis-based implementation provides a fast, scalable, and flexible solution for caching in distributed systems."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}