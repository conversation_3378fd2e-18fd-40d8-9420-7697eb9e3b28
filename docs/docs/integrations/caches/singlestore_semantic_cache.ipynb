{"cells": [{"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# SingleStoreSemanticCache\n", "\n", "This example demonstrates how to get started with the SingleStore semantic cache.\n", "\n", "### Integration Overview\n", "\n", "`SingleStoreSemanticCache` leverages `SingleStoreVectorStore` to cache LLM responses directly in a SingleStore database, enabling efficient semantic retrieval and reuse of results.\n", "\n", "### Integration details\n", "\n", "\n", "\n", "| Class | Package | JS support |\n", "| :--- | :--- |  :---: |\n", "| SingleStoreSemanticCache | langchain_singlestore | ❌ | "]}, {"cell_type": "markdown", "id": "0730d6a1-c893-4840-9817-5e5251676d5d", "metadata": {}, "source": ["## Installation\n", "\n", "This cache lives in the `langchain-singlestore` package:"]}, {"cell_type": "code", "execution_count": null, "id": "652d6238-1f87-422a-b135-f5abbb8652fc", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-singlestore"]}, {"cell_type": "markdown", "id": "5c5f2839-4020-424e-9fc9-07777eede442", "metadata": {}, "source": ["## Usage"]}, {"cell_type": "code", "execution_count": null, "id": "51a60dbe-9f2e-4e04-bb62-23968f17164a", "metadata": {}, "outputs": [], "source": ["from langchain_core.globals import set_llm_cache\n", "from langchain_singlestore import SingleStoreSemanticCache\n", "\n", "set_llm_cache(\n", "    SingleStoreSemanticCache(\n", "        embedding=YourEmbeddings(),\n", "        host=\"root:pass@localhost:3306/db\",\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cddda8ef", "metadata": {}, "outputs": [], "source": ["%%time\n", "# The first time, it is not yet in cache, so it should take longer\n", "llm.invoke(\"Tell me a joke\")"]}, {"cell_type": "code", "execution_count": null, "id": "c474168f", "metadata": {}, "outputs": [], "source": ["%%time\n", "# The second time, while not a direct hit, the question is semantically similar to the original question,\n", "# so it uses the cached result!\n", "llm.invoke(\"Tell me one joke\")"]}], "metadata": {"kernelspec": {"display_name": "langchain-singlestore-BD1RbQ07-py3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}