{"cells": [{"cell_type": "markdown", "id": "700a516b", "metadata": {}, "source": ["# OpenAI Adapter\n", "\n", "**Please ensure OpenAI library is version 1.0.0 or higher; otherwise, refer to the older doc [OpenAI Adapter(Old)](/docs/integrations/adapters/openai-old/).**\n", "\n", "A lot of people get started with OpenAI but want to explore other models. LangChain's integrations with many model providers make this easy to do so. While LangChain has it's own message and model APIs, we've also made it as easy as possible to explore other models by exposing an adapter to adapt LangChain models to the OpenAI api.\n", "\n", "At the moment this only deals with output and does not return other information (token counts, stop reasons, etc)."]}, {"cell_type": "code", "execution_count": 1, "id": "6017f26a", "metadata": {}, "outputs": [], "source": ["import openai\n", "from langchain_community.adapters import openai as lc_openai"]}, {"cell_type": "markdown", "id": "b522ceda", "metadata": {}, "source": ["## chat.completions.create"]}, {"cell_type": "code", "execution_count": 2, "id": "1d22eb61", "metadata": {}, "outputs": [], "source": ["messages = [{\"role\": \"user\", \"content\": \"hi\"}]"]}, {"cell_type": "markdown", "id": "d550d3ad", "metadata": {}, "source": ["Original OpenAI call"]}, {"cell_type": "code", "execution_count": 3, "id": "012d81ae", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'content': 'Hello! How can I assist you today?',\n", " 'role': 'assistant',\n", " 'function_call': None,\n", " 'tool_calls': None}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["result = openai.chat.completions.create(\n", "    messages=messages, model=\"gpt-3.5-turbo\", temperature=0\n", ")\n", "result.choices[0].message.model_dump()"]}, {"cell_type": "markdown", "id": "db5b5500", "metadata": {}, "source": ["LangChain OpenAI wrapper call"]}, {"cell_type": "code", "execution_count": 4, "id": "c67a5ac8", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'role': 'assistant', 'content': 'Hello! How can I help you today?'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["lc_result = lc_openai.chat.completions.create(\n", "    messages=messages, model=\"gpt-3.5-turbo\", temperature=0\n", ")\n", "\n", "lc_result.choices[0].message  # Attribute access"]}, {"cell_type": "code", "execution_count": 5, "id": "37a6e461-8608-47f6-ac45-12ad753c062a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'role': 'assistant', 'content': 'Hello! How can I help you today?'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["lc_result[\"choices\"][0][\"message\"]  # Also compatible with index access"]}, {"cell_type": "markdown", "id": "034ba845", "metadata": {}, "source": ["Swapping out model providers"]}, {"cell_type": "code", "execution_count": 6, "id": "f7c94827", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'role': 'assistant', 'content': 'Hello! How can I assist you today?'}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["lc_result = lc_openai.chat.completions.create(\n", "    messages=messages, model=\"claude-2\", temperature=0, provider=\"ChatAnthropic\"\n", ")\n", "lc_result.choices[0].message"]}, {"cell_type": "markdown", "id": "cb3f181d", "metadata": {}, "source": ["## chat.completions.stream"]}, {"cell_type": "markdown", "id": "f7b8cd18", "metadata": {}, "source": ["Original OpenAI call"]}, {"cell_type": "code", "execution_count": 7, "id": "fd8cb1ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'content': '', 'function_call': None, 'role': 'assistant', 'tool_calls': None}\n", "{'content': 'Hello', 'function_call': None, 'role': None, 'tool_calls': None}\n", "{'content': '!', 'function_call': None, 'role': None, 'tool_calls': None}\n", "{'content': ' How', 'function_call': None, 'role': None, 'tool_calls': None}\n", "{'content': ' can', 'function_call': None, 'role': None, 'tool_calls': None}\n", "{'content': ' I', 'function_call': None, 'role': None, 'tool_calls': None}\n", "{'content': ' assist', 'function_call': None, 'role': None, 'tool_calls': None}\n", "{'content': ' you', 'function_call': None, 'role': None, 'tool_calls': None}\n", "{'content': ' today', 'function_call': None, 'role': None, 'tool_calls': None}\n", "{'content': '?', 'function_call': None, 'role': None, 'tool_calls': None}\n", "{'content': None, 'function_call': None, 'role': None, 'tool_calls': None}\n"]}], "source": ["for c in openai.chat.completions.create(\n", "    messages=messages, model=\"gpt-3.5-turbo\", temperature=0, stream=True\n", "):\n", "    print(c.choices[0].delta.model_dump())"]}, {"cell_type": "markdown", "id": "0b2a076b", "metadata": {}, "source": ["LangChain OpenAI wrapper call"]}, {"cell_type": "code", "execution_count": 8, "id": "9521218c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'role': 'assistant', 'content': ''}\n", "{'content': 'Hello'}\n", "{'content': '!'}\n", "{'content': ' How'}\n", "{'content': ' can'}\n", "{'content': ' I'}\n", "{'content': ' assist'}\n", "{'content': ' you'}\n", "{'content': ' today'}\n", "{'content': '?'}\n", "{}\n"]}], "source": ["for c in lc_openai.chat.completions.create(\n", "    messages=messages, model=\"gpt-3.5-turbo\", temperature=0, stream=True\n", "):\n", "    print(c.choices[0].delta)"]}, {"cell_type": "markdown", "id": "0fc39750", "metadata": {}, "source": ["Swapping out model providers"]}, {"cell_type": "code", "execution_count": 9, "id": "68f0214e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'role': 'assistant', 'content': ''}\n", "{'content': 'Hello'}\n", "{'content': '!'}\n", "{'content': ' How'}\n", "{'content': ' can'}\n", "{'content': ' I'}\n", "{'content': ' assist'}\n", "{'content': ' you'}\n", "{'content': ' today'}\n", "{'content': '?'}\n", "{}\n"]}], "source": ["for c in lc_openai.chat.completions.create(\n", "    messages=messages,\n", "    model=\"claude-2\",\n", "    temperature=0,\n", "    stream=True,\n", "    provider=\"ChatAnthropic\",\n", "):\n", "    print(c[\"choices\"][0][\"delta\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}