{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Context\n", "\n", ">[Context](https://context.ai/) provides user analytics for LLM-powered products and features.\n", "\n", "With `Context`, you can start understanding your users and improving their experiences in less than 30 minutes.\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["In this guide we will show you how to integrate with Context."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Installation and Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install --upgrade --quiet  langchain langchain-openai langchain-community context-python"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Getting API Credentials\n", "\n", "To get your Context API token:\n", "\n", "1. Go to the settings page within your Context account (https://with.context.ai/settings).\n", "2. Generate a new API Token.\n", "3. Store this token somewhere secure."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Setup Context\n", "\n", "To use the `ContextCallbackHandler`, import the handler from Langchain and instantiate it with your Context API token.\n", "\n", "Ensure you have installed the `context-python` package before using the handler."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-03-06T19:05:26.534124Z", "iopub.status.busy": "2024-03-06T19:05:26.533924Z", "iopub.status.idle": "2024-03-06T19:05:26.798727Z", "shell.execute_reply": "2024-03-06T19:05:26.798135Z", "shell.execute_reply.started": "2024-03-06T19:05:26.534109Z"}}, "outputs": [], "source": ["from langchain_community.callbacks.context_callback import ContextCallbackHandler"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "token = os.environ[\"CONTEXT_API_TOKEN\"]\n", "\n", "context_callback = ContextCallbackHandler(token)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Usage\n", "### Context callback within a chat model\n", "\n", "The Context callback handler can be used to directly record transcripts between users and AI assistants."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "from langchain_openai import ChatOpenAI\n", "\n", "token = os.environ[\"CONTEXT_API_TOKEN\"]\n", "\n", "chat = ChatOpenAI(\n", "    headers={\"user_id\": \"123\"}, temperature=0, callbacks=[ContextCallbackHandler(token)]\n", ")\n", "\n", "messages = [\n", "    SystemMessage(\n", "        content=\"You are a helpful assistant that translates English to French.\"\n", "    ),\n", "    HumanMessage(content=\"I love programming.\"),\n", "]\n", "\n", "print(chat(messages))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Context callback within Chains\n", "\n", "The Context callback handler can also be used to record the inputs and outputs of chains. Note that intermediate steps of the chain are not recorded - only the starting inputs and final outputs.\n", "\n", "__Note:__ Ensure that you pass the same context object to the chat model and the chain.\n", "\n", "Wrong:\n", "> ```python\n", "> chat = ChatOpenAI(temperature=0.9, callbacks=[ContextCallbackHandler(token)])\n", "> chain = LLMChain(llm=chat, prompt=chat_prompt_template, callbacks=[ContextCallbackHandler(token)])\n", "> ```\n", "\n", "Correct:\n", ">```python\n", ">handler = ContextCallbackHandler(token)\n", ">chat = ChatOpenAI(temperature=0.9, callbacks=[callback])\n", ">chain = LLMChain(llm=chat, prompt=chat_prompt_template, callbacks=[callback])\n", ">```\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from langchain.chains import LLMChain\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_core.prompts.chat import (\n", "    ChatPromptTemplate,\n", "    HumanMessagePromptTemplate,\n", ")\n", "from langchain_openai import ChatOpenAI\n", "\n", "token = os.environ[\"CONTEXT_API_TOKEN\"]\n", "\n", "human_message_prompt = HumanMessagePromptTemplate(\n", "    prompt=PromptTemplate(\n", "        template=\"What is a good name for a company that makes {product}?\",\n", "        input_variables=[\"product\"],\n", "    )\n", ")\n", "chat_prompt_template = ChatPromptTemplate.from_messages([human_message_prompt])\n", "callback = ContextCallbackHandler(token)\n", "chat = ChatOpenAI(temperature=0.9, callbacks=[callback])\n", "chain = LLMChain(llm=chat, prompt=chat_prompt_template, callbacks=[callback])\n", "print(chain.run(\"colorful socks\"))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "vscode": {"interpreter": {"hash": "a53ebf4a859167383b364e7e7521d0add3c2dbbdecce4edf676e8c4634ff3fbb"}}}, "nbformat": 4, "nbformat_minor": 4}