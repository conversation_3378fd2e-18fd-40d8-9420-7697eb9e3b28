{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON>\n", "\n", ">[Argilla](https://argilla.io/) is an open-source data curation platform for LLMs.\n", "> Using <PERSON><PERSON>illa, everyone can build robust language models through faster data curation \n", "> using both human and machine feedback. We provide support for each step in the MLOps cycle, \n", "> from data labeling to model monitoring.\n", "\n", "<a target=\"_blank\" href=\"https://colab.research.google.com/github/langchain-ai/langchain/blob/master/docs/docs/integrations/callbacks/argilla.ipynb\">\n", "  <img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/>\n", "</a>"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["In this guide we will demonstrate how to track the inputs and responses of your LLM to generate a dataset in Argilla, using the `ArgillaCallbackHandler`.\n", "\n", "It's useful to keep track of the inputs and outputs of your LLMs to generate datasets for future fine-tuning. This is especially useful when you're using a LLM to generate data for a specific task, such as question answering, summarization, or translation."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"tags": []}, "source": ["## Installation and Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install --upgrade --quiet  langchain langchain-openai argilla"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Getting API Credentials\n", "\n", "To get the Argilla API credentials, follow the next steps:\n", "\n", "1. Go to your Argilla UI.\n", "2. Click on your profile picture and go to \"My settings\".\n", "3. Then copy the API Key.\n", "\n", "In Argilla the API URL will be the same as the URL of your Argilla UI.\n", "\n", "To get the OpenAI API credentials, please visit https://platform.openai.com/account/api-keys"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"ARGILLA_API_URL\"] = \"...\"\n", "os.environ[\"ARGILLA_API_KEY\"] = \"...\"\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"...\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Setup Argilla\n", "\n", "To use the `ArgillaCallbackHandler` we will need to create a new `FeedbackDataset` in Argilla to keep track of your LLM experiments. To do so, please use the following code:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import argilla as rg"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from packaging.version import parse as parse_version\n", "\n", "if parse_version(rg.__version__) < parse_version(\"1.8.0\"):\n", "    raise RuntimeError(\n", "        \"`FeedbackDataset` is only available in Argilla v1.8.0 or higher, please \"\n", "        \"upgrade `argilla` as `pip install argilla --upgrade`.\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = rg.FeedbackDataset(\n", "    fields=[\n", "        rg.<PERSON><PERSON><PERSON>(name=\"prompt\"),\n", "        rg.<PERSON><PERSON><PERSON>(name=\"response\"),\n", "    ],\n", "    questions=[\n", "        rg.<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "            name=\"response-rating\",\n", "            description=\"How would you rate the quality of the response?\",\n", "            values=[1, 2, 3, 4, 5],\n", "            required=True,\n", "        ),\n", "        rg.TextQuestion(\n", "            name=\"response-feedback\",\n", "            description=\"What feedback do you have for the response?\",\n", "            required=False,\n", "        ),\n", "    ],\n", "    guidelines=\"You're asked to rate the quality of the response and provide feedback.\",\n", ")\n", "\n", "rg.init(\n", "    api_url=os.environ[\"ARGILLA_API_URL\"],\n", "    api_key=os.environ[\"ARGILLA_API_KEY\"],\n", ")\n", "\n", "dataset.push_to_argilla(\"langchain-dataset\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["> 📌 NOTE: at the moment, just the prompt-response pairs are supported as `FeedbackDataset.fields`, so the `ArgillaCallbackHandler` will just track the prompt i.e. the LLM input, and the response i.e. the LLM output."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Tracking"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To use the `ArgillaCallbackHandler` you can either use the following code, or just reproduce one of the examples presented in the following sections."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.callbacks.argilla_callback import ArgillaCallbackHandler\n", "\n", "argilla_callback = ArgillaCallbackHandler(\n", "    dataset_name=\"langchain-dataset\",\n", "    api_url=os.environ[\"ARGILLA_API_URL\"],\n", "    api_key=os.environ[\"ARGILLA_API_KEY\"],\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Scenario 1: Tracking an LLM\n", "\n", "First, let's just run a single LLM a few times and capture the resulting prompt-response pairs in Argilla."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["LLMResult(generations=[[Generation(text='\\n\\nQ: What did the fish say when he hit the wall? \\nA: Dam.', generation_info={'finish_reason': 'stop', 'logprobs': None})], [Generation(text='\\n\\nThe Moon \\n\\nThe moon is high in the midnight sky,\\nSparkling like a star above.\\nThe night so peaceful, so serene,\\nFilling up the air with love.\\n\\nEver changing and renewing,\\nA never-ending light of grace.\\nThe moon remains a constant view,\\nA reminder of life’s gentle pace.\\n\\nThrough time and space it guides us on,\\nA never-fading beacon of hope.\\nThe moon shines down on us all,\\nAs it continues to rise and elope.', generation_info={'finish_reason': 'stop', 'logprobs': None})], [Generation(text='\\n\\nQ. What did one magnet say to the other magnet?\\nA. \"I find you very attractive!\"', generation_info={'finish_reason': 'stop', 'logprobs': None})], [Generation(text=\"\\n\\nThe world is charged with the grandeur of God.\\nIt will flame out, like shining from shook foil;\\nIt gathers to a greatness, like the ooze of oil\\nCrushed. Why do men then now not reck his rod?\\n\\nGenerations have trod, have trod, have trod;\\nAnd all is seared with trade; bleared, smeared with toil;\\nAnd wears man's smudge and shares man's smell: the soil\\nIs bare now, nor can foot feel, being shod.\\n\\nAnd for all this, nature is never spent;\\nThere lives the dearest freshness deep down things;\\nAnd though the last lights off the black West went\\nOh, morning, at the brown brink eastward, springs —\\n\\nBecause the Holy Ghost over the bent\\nWorld broods with warm breast and with ah! bright wings.\\n\\n~Gerard Manley Hopkins\", generation_info={'finish_reason': 'stop', 'logprobs': None})], [Generation(text='\\n\\nQ: What did one ocean say to the other ocean?\\nA: Nothing, they just waved.', generation_info={'finish_reason': 'stop', 'logprobs': None})], [Generation(text=\"\\n\\nA poem for you\\n\\nOn a field of green\\n\\nThe sky so blue\\n\\nA gentle breeze, the sun above\\n\\nA beautiful world, for us to love\\n\\nLife is a journey, full of surprise\\n\\nFull of joy and full of surprise\\n\\nBe brave and take small steps\\n\\nThe future will be revealed with depth\\n\\nIn the morning, when dawn arrives\\n\\nA fresh start, no reason to hide\\n\\nSomewhere down the road, there's a heart that beats\\n\\nBelieve in yourself, you'll always succeed.\", generation_info={'finish_reason': 'stop', 'logprobs': None})]], llm_output={'token_usage': {'completion_tokens': 504, 'total_tokens': 528, 'prompt_tokens': 24}, 'model_name': 'text-davinci-003'})"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.callbacks.stdout import StdOutCallbackHandler\n", "from langchain_openai import OpenAI\n", "\n", "argilla_callback = ArgillaCallbackHandler(\n", "    dataset_name=\"langchain-dataset\",\n", "    api_url=os.environ[\"ARGILLA_API_URL\"],\n", "    api_key=os.environ[\"ARGILLA_API_KEY\"],\n", ")\n", "callbacks = [StdOutCallbackH<PERSON><PERSON>(), argilla_callback]\n", "\n", "llm = OpenAI(temperature=0.9, callbacks=callbacks)\n", "llm.generate([\"Tell me a joke\", \"Tell me a poem\"] * 3)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["![Argilla <PERSON> with Lang<PERSON>hain LLM input-response](https://docs.argilla.io/en/latest/_images/llm.png)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Scenario 2: Tracking an LLM in a chain\n", "\n", "Then we can create a chain using a prompt template, and then track the initial prompt and the final response in Argilla."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3m<PERSON><PERSON> are a playwright. Given the title of play, it is your job to write a synopsis for that title.\n", "Title: Documentary about Bigfoot in Paris\n", "Playwright: This is a synopsis for the above play:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["[{'text': \"\\n\\nDocumentary about Bigfoot in Paris focuses on the story of a documentary filmmaker and their search for evidence of the legendary Bigfoot creature in the city of Paris. The play follows the filmmaker as they explore the city, meeting people from all walks of life who have had encounters with the mysterious creature. Through their conversations, the filmmaker unravels the story of Bigfoot and finds out the truth about the creature's presence in Paris. As the story progresses, the filmmaker learns more and more about the mysterious creature, as well as the different perspectives of the people living in the city, and what they think of the creature. In the end, the filmmaker's findings lead them to some surprising and heartwarming conclusions about the creature's existence and the importance it holds in the lives of the people in Paris.\"}]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chains import LLMChain\n", "from langchain_core.callbacks.stdout import StdOutCallbackHandler\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_openai import OpenAI\n", "\n", "argilla_callback = ArgillaCallbackHandler(\n", "    dataset_name=\"langchain-dataset\",\n", "    api_url=os.environ[\"ARGILLA_API_URL\"],\n", "    api_key=os.environ[\"ARGILLA_API_KEY\"],\n", ")\n", "callbacks = [StdOutCallbackH<PERSON><PERSON>(), argilla_callback]\n", "llm = OpenAI(temperature=0.9, callbacks=callbacks)\n", "\n", "template = \"\"\"You are a playwright. Given the title of play, it is your job to write a synopsis for that title.\n", "Title: {title}\n", "Playwright: This is a synopsis for the above play:\"\"\"\n", "prompt_template = PromptTemplate(input_variables=[\"title\"], template=template)\n", "synopsis_chain = LLMChain(llm=llm, prompt=prompt_template, callbacks=callbacks)\n", "\n", "test_prompts = [{\"title\": \"Documentary about Bigfoot in Paris\"}]\n", "synopsis_chain.apply(test_prompts)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["![Argilla U<PERSON> with LangChain Chain input-response](https://docs.argilla.io/en/latest/_images/chain.png)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Scenario 3: Using an Agent with Tools\n", "\n", "Finally, as a more advanced workflow, you can create an agent that uses some tools. So that `ArgillaCallbackHandler` will keep track of the input and the output, but not about the intermediate steps/thoughts, so that given a prompt we log the original prompt and the final response to that given prompt."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["> Note that for this scenario we'll be using Google Search API (Serp API) so you will need to both install `google-search-results` as `pip install google-search-results`, and to set the Serp API Key as `os.environ[\"SERPAPI_API_KEY\"] = \"...\"` (you can find it at https://serpapi.com/dashboard), otherwise the example below won't work."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m I need to answer a historical question\n", "Action: Search\n", "Action Input: \"who was the first president of the United States of America\" \u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3m<PERSON><PERSON><PERSON>\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m <PERSON> was the first president\n", "Final Answer: <PERSON> was the first president of the United States of America.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'<PERSON> was the first president of the United States of America.'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.agents import AgentType, initialize_agent, load_tools\n", "from langchain_core.callbacks.stdout import StdOutCallbackHandler\n", "from langchain_openai import OpenAI\n", "\n", "argilla_callback = ArgillaCallbackHandler(\n", "    dataset_name=\"langchain-dataset\",\n", "    api_url=os.environ[\"ARGILLA_API_URL\"],\n", "    api_key=os.environ[\"ARGILLA_API_KEY\"],\n", ")\n", "callbacks = [StdOutCallbackH<PERSON><PERSON>(), argilla_callback]\n", "llm = OpenAI(temperature=0.9, callbacks=callbacks)\n", "\n", "tools = load_tools([\"serpapi\"], llm=llm, callbacks=callbacks)\n", "agent = initialize_agent(\n", "    tools,\n", "    llm,\n", "    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,\n", "    callbacks=callbacks,\n", ")\n", "agent.run(\"Who was the first president of the United States of America?\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["![Argilla <PERSON> with LangChain Agent input-response](https://docs.argilla.io/en/latest/_images/agent.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "vscode": {"interpreter": {"hash": "a53ebf4a859167383b364e7e7521d0add3c2dbbdecce4edf676e8c4634ff3fbb"}}}, "nbformat": 4, "nbformat_minor": 4}