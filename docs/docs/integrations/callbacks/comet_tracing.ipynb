{"cells": [{"cell_type": "markdown", "id": "5371a9bb", "metadata": {}, "source": ["# Comet Tracing\n", "\n", "There are two ways to trace your <PERSON><PERSON><PERSON><PERSON> executions with <PERSON>:\n", "\n", "1. Setting the `LANGCHAIN_COMET_TRACING` environment variable to \"true\". This is the recommended way.\n", "2. Import the `CometTracer` manually and pass it explicitely."]}, {"cell_type": "code", "execution_count": null, "id": "17c04cc6-c93d-4b6c-a033-e897577f4ed1", "metadata": {"ExecuteTime": {"end_time": "2023-05-18T12:47:46.580776Z", "start_time": "2023-05-18T12:47:46.577833Z"}, "tags": []}, "outputs": [], "source": ["import os\n", "\n", "import comet_llm\n", "from langchain_openai import OpenAI\n", "\n", "os.environ[\"LANGCHAIN_COMET_TRACING\"] = \"true\"\n", "\n", "# Connect to Comet if no API Key is set\n", "comet_llm.init()\n", "\n", "# comet documentation to configure comet using env variables\n", "# https://www.comet.com/docs/v2/api-and-sdk/llm-sdk/configuration/\n", "# here we are configuring the comet project\n", "os.environ[\"COMET_PROJECT_NAME\"] = \"comet-example-langchain-tracing\"\n", "\n", "from langchain.agents import AgentType, initialize_agent, load_tools"]}, {"cell_type": "code", "execution_count": null, "id": "1b62cd48", "metadata": {"ExecuteTime": {"end_time": "2023-05-18T12:47:47.445229Z", "start_time": "2023-05-18T12:47:47.436424Z"}, "tags": []}, "outputs": [], "source": ["# Agent run with tracing. Ensure that OPENAI_API_KEY is set appropriately to run this example.\n", "\n", "llm = OpenAI(temperature=0)\n", "tools = load_tools([\"llm-math\"], llm=llm)"]}, {"cell_type": "code", "execution_count": null, "id": "bfa16b79-aa4b-4d41-a067-70d1f593f667", "metadata": {"ExecuteTime": {"end_time": "2023-05-18T12:48:01.816137Z", "start_time": "2023-05-18T12:47:49.109574Z"}, "tags": []}, "outputs": [], "source": ["agent = initialize_agent(\n", "    tools, llm, agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION, verbose=True\n", ")\n", "\n", "agent.run(\"What is 2 raised to .123243 power?\")  # this should be traced\n", "# An url for the chain like the following should print in your console:\n", "# https://www.comet.com/<workspace>/<project_name>\n", "# The url can be used to view the LLM chain in Comet."]}, {"cell_type": "code", "execution_count": null, "id": "5e212e7d", "metadata": {}, "outputs": [], "source": ["# Now, we unset the environment variable and use a context manager.\n", "if \"LANGCHAIN_COMET_TRACING\" in os.environ:\n", "    del os.environ[\"LANGCHAIN_COMET_TRACING\"]\n", "\n", "from langchain_community.callbacks.tracers.comet import CometTracer\n", "\n", "tracer = CometTracer()\n", "\n", "# Recreate the LLM, tools and agent and passing the callback to each of them\n", "llm = OpenAI(temperature=0)\n", "tools = load_tools([\"llm-math\"], llm=llm)\n", "agent = initialize_agent(\n", "    tools, llm, agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION, verbose=True\n", ")\n", "\n", "agent.run(\n", "    \"What is 2 raised to .123243 power?\", callbacks=[tracer]\n", ")  # this should be traced"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}