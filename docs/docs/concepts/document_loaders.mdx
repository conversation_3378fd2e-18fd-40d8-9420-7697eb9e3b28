# Document loaders
<span data-heading-keywords="document loader,document loaders"></span>

:::info[Prerequisites]

* [Document loaders API reference](/docs/how_to/#document-loaders)
:::

Document loaders are designed to load document objects. LangChain has hundreds of integrations with various data sources to load data from: Slack, Notion, Google Drive, etc.

## Integrations

You can find available integrations on the [Document loaders integrations page](/docs/integrations/document_loaders/).

## Interface

Documents loaders implement the [BaseLoader interface](https://python.langchain.com/api_reference/core/document_loaders/langchain_core.document_loaders.base.BaseLoader.html).

Each DocumentLoader has its own specific parameters, but they can all be invoked in the same way with the `.load` method or `.lazy_load`.

Here's a simple example:

```python
from langchain_community.document_loaders.csv_loader import CSVLoader

loader = CSVLoader(
    ...  # <-- Integration specific parameters here
)
data = loader.load()
```

When working with large datasets, you can use the `.lazy_load` method:

```python
for document in loader.lazy_load():
    print(document)
```

## Related resources

Please see the following resources for more information:

* [How-to guides for document loaders](/docs/how_to/#document-loaders)
* [Document API reference](https://python.langchain.com/api_reference/core/documents/langchain_core.documents.base.Document.html)
* [Document loaders integrations](/docs/integrations/document_loaders/)
