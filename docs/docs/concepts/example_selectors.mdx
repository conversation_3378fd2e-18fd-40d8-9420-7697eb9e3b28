# Example selectors

:::note Prerequisites

- [Chat models](/docs/concepts/chat_models/)
- [Few-shot prompting](/docs/concepts/few_shot_prompting/)
:::

## Overview

One common prompting technique for achieving better performance is to include examples as part of the prompt. This is known as [few-shot prompting](/docs/concepts/few_shot_prompting).

This gives the [language model](/docs/concepts/chat_models/) concrete examples of how it should behave.
Sometimes these examples are hardcoded into the prompt, but for more advanced situations it may be nice to dynamically select them.

**Example Selectors** are classes responsible for selecting and then formatting examples into prompts.

## Related resources

* [Example selector how-to guides](/docs/how_to/#example-selectors)
