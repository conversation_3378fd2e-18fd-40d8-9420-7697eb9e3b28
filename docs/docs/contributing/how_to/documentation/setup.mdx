---
sidebar_class_name: "hidden"
---

# Setup

LangChain documentation consists of two components:

1. Main Documentation: Hosted at [python.langchain.com](https://python.langchain.com/),
this comprehensive resource serves as the primary user-facing documentation.
It covers a wide array of topics, including tutorials, use cases, integrations,
and more, offering extensive guidance on building with LangChain.
The content for this documentation lives in the `/docs` directory of the monorepo.
2. In-code Documentation: This is documentation of the codebase itself, which is also
used to generate the externally facing [API Reference](https://python.langchain.com/api_reference/).
The content for the API reference is autogenerated by scanning the docstrings in the codebase. For this reason we ask that
developers document their code well.

The API Reference is largely autogenerated by [sphinx](https://www.sphinx-doc.org/en/master/) from the code.

We appreciate all contributions to the documentation, whether it be fixing a typo,
adding a new tutorial or example and whether it be in the main documentation or the API Reference.

Similar to linting, we recognize documentation can be annoying. If you do not want
to do it, please contact a project maintainer, and they can help you with it. We do not want this to be a blocker for good code getting contributed.

## 📜 Main documentation

The content for the main documentation is located in the `/docs` directory of the monorepo.

The documentation is written using a combination of ipython notebooks (`.ipynb` files)
and markdown (`.mdx` files). The notebooks are converted to markdown
and then built using [Docusaurus 2](https://docusaurus.io/).

Feel free to make contributions to the main documentation! 🥰

After modifying the documentation:

1. Run the linting and formatting commands (see below) to ensure that the documentation is well-formatted and free of errors.
2. Optionally build the documentation locally to verify that the changes look good.
3. Make a pull request with the changes.
4. You can preview and verify that the changes are what you wanted by clicking the `View deployment` or `Visit Preview` buttons on the pull request `Conversation` page. This will take you to a preview of the documentation changes.

## ⚒️ Linting and building documentation locally

After writing up the documentation, you may want to lint and build the documentation
locally to ensure that it looks good and is free of errors.

If you're unable to build it locally that's okay as well, as you will be able to
see a preview of the documentation on the pull request page.


### Building

The code that builds the documentation is located in the `/docs` directory of the monorepo.

In the following commands, the prefix `api_` indicates that those are operations for the API Reference.

You can build the documentation as outlined below:

```bash
make docs_build
make api_docs_build
```

### Viewing documentation locally

After building the main documentation, you can view it locally by starting a development server:

```bash
# For main documentation (after running `make docs_build`)
cd docs && make start
```

This will start a development server where you can view the documentation in your browser. The exact url will be shown to you during the start process. The server will automatically reload when you make changes to the documentation files under the `build/` directory (e.g. for temporary tests - changes you wish to persist should be put under `docs/docs/`).

:::tip

You can specify a different port by setting the `PORT` environment variable:

```bash
cd docs && PORT=3000 make start
```

:::

The API Reference documentation is built as static HTML files and will be automatically opened directly in your browser.

You can also view the API Reference for a specific package by specifying the package name and installing the package if necessary dependencies:

```bash
# Opens the API Reference for the `ollama` package in your default browser
uv pip install -e libs/partners/ollama
make api_docs_quick_preview API_PKG=ollama
```

:::tip

The `make api_docs_build` command takes a long time. If you're making cosmetic changes to the API docs and want to see how they look, use:

```bash
make api_docs_quick_preview
```

which will just build a small subset of the API reference (the `text-splitters` package).

:::

Finally, run the link checker from the project root to ensure all links are valid:

```bash
make docs_linkcheck
make api_docs_linkcheck
```

To clean up the documentation build artifacts, you can run:

```bash
make clean

# Or to clean specific documentation artifacts
make docs_clean
make api_docs_clean
```

### Formatting and linting

The Main Documentation is linted from the **monorepo root**. To lint the main documentation, run the following from there:

```bash
make lint
```

If you have formatting-related errors, you can fix them automatically with:

```bash
make format
```

## ⌨️ In-code documentation

The in-code documentation is largely autogenerated by [sphinx](https://www.sphinx-doc.org/en/master/) from the code following [reStructuredText](https://www.sphinx-doc.org/en/master/usage/restructuredtext/basics.html).

For the API reference to be useful, the codebase must be well-documented. This means that all functions, classes, and methods should have a docstring that explains what they do, what the arguments are, and what the return value is. This is a good practice in general, but it is especially important for LangChain because the API reference is the primary resource for developers to understand how to use the codebase.

We generally follow the [Google Python Style Guide](https://google.github.io/styleguide/pyguide.html#38-comments-and-docstrings) for docstrings.

Here is an example of a well-documented function:

```python

def my_function(arg1: int, arg2: str) -> float:
    """This is a short description of the function. (It should be a single sentence.)

    This is a longer description of the function. It should explain what
    the function does, what the arguments are, and what the return value is.
    It should wrap at 88 characters.

    Examples:
        This is a section for examples of how to use the function.

        .. code-block:: python

            my_function(1, "hello")

    Args:
        arg1: This is a description of arg1. We do not need to specify the type since
            it is already specified in the function signature.
        arg2: This is a description of arg2.

    Returns:
        This is a description of the return value.
    """
    return 3.14
```

### Formatting and linting

The in-code documentation is linted from the directories belonging to the packages
being documented.

For example, if you're working on the `langchain-ollama` package, you would change
the working directory to the the package directory:

```bash
cd [root]/libs/partners/ollama
```

Then you can run the following commands to lint and format the in-code documentation:

```bash
make format
make lint
```

## Verify documentation changes

After pushing documentation changes to the repository, you can preview and verify that the changes are
what you wanted by clicking the `View deployment` or `Visit Preview` buttons on the pull request `Conversation` page.
This will take you to a preview of the documentation changes.
This preview is created by [Vercel](https://vercel.com/docs/getting-started-with-vercel).
