---
pagination_next: null
pagination_prev: null
---

# How to publish an integration package from a template

:::danger
This guide is a work-in-progress.
:::

First, duplicate this template repository: https://github.com/langchain-ai/integration-repo-template

In this guide, we will create a `libs/langchain-parrot-link` folder, simulating the creation
of a partner package for a fake company, "Parrot Link AI".

A package is
installed by users with `pip install langchain-{partner}`, and the package members
can be imported with code like:

```python
from langchain_{partner} import X
```

## Set up a new package

To set up a new partner package, use the latest version of the LangChain CLI. You can install or update it with:

```bash
pip install -U langchain-cli
```

Let's say you want to create a new partner package working for a company called Parrot Link AI.

Then, run the following command to create a new partner package:

```bash
mkdir libs
cd libs/
langchain-cli integration new
> Name: parrot-link
> Name of integration in PascalCase [ParrotLink]: ParrotLink
```

This will create a new package in `libs/parrot-link` with the following structure:

```
libs/parrot-link/
  langchain_parrot_link/ # folder containing your package
    ...
  tests/
    ...
  docs/ # bootstrapped docs notebooks, must be moved to /docs in monorepo root
    ...
  scripts/ # scripts for CI
    ...
  LICENSE
  README.md # fill out with information about your package
  Makefile # default commands for CI
  pyproject.toml # package metadata, mostly managed by Poetry
  poetry.lock # package lockfile, managed by Poetry
  .gitignore
```

## Implement your package

First, add any dependencies your package needs, such as your company's SDK:

```bash
poetry add parrot-link-sdk
```

If you need separate dependencies for type checking, you can add them to the `typing` group with:

```bash
poetry add --group typing types-parrot-link-sdk
```

Then, implement your package in `libs/partners/parrot-link/langchain_parrot_link`.

By default, this will include stubs for a Chat Model, an LLM, and/or a Vector Store. You should delete any of the files you won't use and remove them from `__init__.py`.

## Write Unit and Integration Tests

Some basic tests are presented in the `tests/` directory. You should add more tests to cover your package's functionality.

For information on running and implementing tests, see the [Testing guide](../testing.mdx).

## Write documentation

Documentation is generated from Jupyter notebooks in the `docs/` directory. You should place the notebooks with examples
to the relevant `docs/docs/integrations` directory in the monorepo root.

## (If Necessary) Deprecate community integration

Note: this is only necessary if you're migrating an existing community integration into
a partner package. If the component you're integrating is net-new to LangChain (i.e.
not already in the `community` package), you can skip this step.

Let's pretend we migrated our `ChatParrotLink` chat model from the community package to
the partner package. We would need to deprecate the old model in the community package.

We would do that by adding a `@deprecated` decorator to the old model as follows, in
`libs/community/langchain_community/chat_models/parrot_link.py`.

Before our change, our chat model might look like this:

```python
class ChatParrotLink(BaseChatModel):
  ...
```

After our change, it would look like this:

```python
from langchain_core._api.deprecation import deprecated

@deprecated(
    since="0.0.<next community version>",
    removal="1.0.0",
    alternative_import="langchain_parrot_link.ChatParrotLink"
)
class ChatParrotLink(BaseChatModel):
  ...
```

You should do this for *each* component that you're migrating to the partner package.

## Additional steps

Contributor steps:

- [ ] Add secret names to manual integrations workflow in `.github/workflows/_integration_test.yml`
- [ ] Add secrets to release workflow (for pre-release testing) in `.github/workflows/_release.yml`
- [ ] set up pypi and test pypi projects
- [ ] add credential secrets to Github Actions
- [ ] add package to conda-forge
