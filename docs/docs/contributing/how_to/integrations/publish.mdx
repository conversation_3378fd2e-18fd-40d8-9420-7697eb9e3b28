---
pagination_prev: contributing/how_to/integrations/standard_tests
pagination_next: null
---

# Publishing your package

Now that your package is implemented and tested, you can:

1. Publish your package to PyPi
2. Add documentation for your package to the LangChain Monorepo

## Publishing your package to PyPi

This guide assumes you have already implemented your package and written tests for it. If you haven't done that yet, please refer to the [implementation guide](../package) and the [testing guide](../standard_tests).

Note that Poetry is not required to publish a package to PyPi, and we're using it in this guide end-to-end for convenience.
You are welcome to publish your package using any other method you prefer.

First, make sure you have a PyPi account and have logged in with Poetry:

<details>
    <summary>How to create a PyPi Token</summary>

1. Go to the [PyPi website](https://pypi.org/) and create an account.
2. Verify your email address by clicking the link that PyPi emails to you.
3. Go to your account settings and click "Generate Recovery Codes" to enable 2FA. To generate an API token, you **must** have 2FA enabled currently.
4. Go to your account settings and [generate a new API token](https://pypi.org/manage/account/token/).

</details>

```bash
poetry config pypi-token.pypi <your-pypi-token>
```

Next, build your package:

```bash
poetry build
```

Finally, publish your package to PyPi:

```bash
poetry publish
```

You're all set! Your package is now available on PyPi and can be installed with `pip install langchain-parrot-link`.

## Adding documentation to the LangChain Monorepo

To add documentation for your package to the LangChain Monorepo, you will need to:

1. Fork and clone the LangChain Monorepo
2. Make a "Provider Page" at `docs/docs/integrations/providers/<your-package-name>.ipynb`
3. Make "Component Pages" at `docs/docs/integrations/<component-type>/<your-package-name>.ipynb`
4. Register your package in `libs/packages.yml`
5. Submit a PR with **only these changes** to the LangChain Monorepo

### Fork and clone the LangChain Monorepo

First, fork the [LangChain Monorepo](https://github.com/langchain-ai/langchain) to your GitHub account.

Next, clone the repository to your local machine:

```bash
git clone https://github.com/<your-username>/langchain.git
```

You're now ready to make your PR!

### Bootstrap your documentation pages with the langchain-cli (recommended)

To make it easier to create the necessary documentation pages, you can use the `langchain-cli` to bootstrap them for you.

First, install the latest version of the `langchain-cli` package:

```bash
pip install --upgrade langchain-cli
```

To see the available commands to bootstrap your documentation pages, run:

```bash
langchain-cli integration create-doc --help
```

Let's bootstrap a provider page from the root of the monorepo:

```bash
langchain-cli integration create-doc \
    --component-type Provider \
    --destination-dir docs/docs/integrations/providers \
    --name parrot-link \
    --name-class ParrotLink
```

And a chat model component page:

```bash
langchain-cli integration create-doc \
    --component-type ChatModel \
    --destination-dir docs/docs/integrations/chat \
    --name parrot-link \
    --name-class ParrotLink
```

And a vector store component page:

```bash
langchain-cli integration create-doc \
    --component-type VectorStore \
    --destination-dir docs/docs/integrations/vectorstores \
    --name parrot-link \
    --name-class ParrotLink
```

These commands will create the following 3 files, which you should fill out with information about your package:

- `docs/docs/integrations/providers/parrot_link.ipynb`
- `docs/docs/integrations/chat/parrot_link.ipynb`
- `docs/docs/integrations/vectorstores/parrot_link.ipynb`

### Manually create your documentation pages (if you prefer)

If you prefer to create the documentation pages manually, you can create the same files listed
above and fill them out with information about your package.

You can view the templates that the CLI uses to create these files [here](https://github.com/langchain-ai/langchain/tree/master/libs/cli/langchain_cli/integration_template/docs) if helpful!

### Register your package in `libs/packages.yml`

Finally, add your package to the end of the `libs/packages.yml` file in the LangChain Monorepo.

```yaml
packages:
  - name: langchain-parrot-link
    repo: <your github handle>/<your repo>
    path: .
```

For `path`, you can use `.` if your package is in the root of your repository, or specify a subdirectory (e.g. `libs/parrot-link`) if it is in a subdirectory.

If you followed the [package bootstrapping guide](../package), then your path is `.`.

### Submit a PR with your changes

Once you have completed these steps, you can submit a PR to the LangChain Monorepo with **only these changes**.

If you have additional changes to request, please submit them in a separate PR.
