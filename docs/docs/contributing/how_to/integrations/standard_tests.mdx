---
pagination_next: contributing/how_to/integrations/publish
pagination_prev: contributing/how_to/integrations/package
---
# How to add standard tests to an integration

When creating either a custom class for yourself or to publish in a LangChain integration, it is important to add standard tests to ensure it works as expected. This guide will show you how to add standard tests to each integration type.

## Setup

First, let's install 2 dependencies:

- `langchain-core` will define the interfaces we want to import to define our custom tool.
- `langchain-tests` will provide the standard tests we want to use, as well as pytest plugins necessary to run them. Recommended to pin to the latest version: <img src="https://img.shields.io/pypi/v/langchain-tests" style={{position:"relative",top:4,left:3}} />

:::note

Because added tests in new versions of `langchain-tests` can break your CI/CD pipelines, we recommend pinning the
version of `langchain-tests` to avoid unexpected changes.

:::

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<Tabs>
    <TabItem value="poetry" label="Poetry" default>
If you followed the [previous guide](../package), you should already have these dependencies installed!

```bash
poetry add langchain-core
poetry add --group test langchain-tests==<latest_version>
poetry install --with test
```
    </TabItem>
    <TabItem value="pip" label="Pip">
```bash
pip install -U langchain-core langchain-tests

# install current package in editable mode
pip install --editable .
```
    </TabItem>
</Tabs>

## Add and configure standard tests

There are 2 namespaces in the `langchain-tests` package:

- [unit tests](../../../concepts/testing.mdx#unit-tests) (`langchain_tests.unit_tests`): designed to be used to test the component in isolation and without access to external services
- [integration tests](../../../concepts/testing.mdx#integration-tests) (`langchain_tests.integration_tests`): designed to be used to test the component with access to external services (in particular, the external service that the component is designed to interact with).

Both types of tests are implemented as [`pytest` class-based test suites](https://docs.pytest.org/en/7.1.x/getting-started.html#group-multiple-tests-in-a-class).

By subclassing the base classes for each type of standard test (see below), you get all of the standard tests for that type, and you
can override the properties that the test suite uses to configure the tests.

In order to run the tests in the same way as this guide, we recommend subclassing these
classes in test files under two test subdirectories:

- `tests/unit_tests` for unit tests
- `tests/integration_tests` for integration tests

### Implementing standard tests

import CodeBlock from '@theme/CodeBlock';

In the following tabs, we show how to implement the standard tests for
each component type:

<Tabs>

    <TabItem value="chat_models" label="Chat models">

To configure standard tests for a chat model, we subclass `ChatModelUnitTests` and `ChatModelIntegrationTests`. On each subclass, we override the following `@property` methods to specify the chat model to be tested and the chat model's configuration:

| Property | Description |
| --- | --- |
| `chat_model_class` | The class for the chat model to be tested |
| `chat_model_params` | The parameters to pass to the chat
model's constructor |

Additionally, chat model standard tests test a range of behaviors, from the most basic requirements (generating a response to a query) to optional capabilities like multi-modal support and tool-calling. For a test run to be successful:

1. If a feature is intended to be supported by the model, it should pass;
2. If a feature is not intended to be supported by the model, it should be skipped.

Tests for "optional" capabilities are controlled via a set of properties that can be overridden on the test model subclass.

You can see the **entire list of configurable capabilities** in the API references for
[unit tests](https://python.langchain.com/api_reference/standard_tests/unit_tests/langchain_tests.unit_tests.chat_models.ChatModelUnitTests.html)
and [integration tests](https://python.langchain.com/api_reference/standard_tests/integration_tests/langchain_tests.integration_tests.chat_models.ChatModelIntegrationTests.html).

For example, to enable integration tests for image inputs, we can implement

```python
@property
def supports_image_inputs(self) -> bool:
    return True
```

on the integration test class.

:::note

Details on what tests are run, how each test can be skipped, and troubleshooting tips for each test can be found in the API references. See details:

- [Unit tests API reference](https://python.langchain.com/api_reference/standard_tests/unit_tests/langchain_tests.unit_tests.chat_models.ChatModelUnitTests.html)
- [Integration tests API reference](https://python.langchain.com/api_reference/standard_tests/integration_tests/langchain_tests.integration_tests.chat_models.ChatModelIntegrationTests.html)

:::

Unit test example:

import ChatUnitSource from '../../../../src/theme/integration_template/tests/unit_tests/test_chat_models.py';

<CodeBlock language="python" title="tests/unit_tests/test_chat_models.py">
{
    ChatUnitSource.replaceAll('__ModuleName__', 'ParrotLink')
        .replaceAll('__package_name__', 'langchain-parrot-link')
        .replaceAll('__MODULE_NAME__', 'PARROT_LINK')
        .replaceAll('__module_name__', 'langchain_parrot_link')
}
</CodeBlock>

Integration test example:


import ChatIntegrationSource from '../../../../src/theme/integration_template/tests/integration_tests/test_chat_models.py';

<CodeBlock language="python" title="tests/integration_tests/test_chat_models.py">
{
    ChatIntegrationSource.replaceAll('__ModuleName__', 'ParrotLink')
        .replaceAll('__package_name__', 'langchain-parrot-link')
        .replaceAll('__MODULE_NAME__', 'PARROT_LINK')
        .replaceAll('__module_name__', 'langchain_parrot_link')
}
</CodeBlock>

    </TabItem>
    <TabItem value="vector_stores" label="Vector stores">


Here's how you would configure the standard tests for a typical vector store (using
`ParrotVectorStore` as a placeholder):

Vector store tests do not have optional capabilities to be configured at this time.

import VectorStoreIntegrationSource from '../../../../src/theme/integration_template/tests/integration_tests/test_vectorstores.py';

<CodeBlock language="python" title="tests/integration_tests/test_vectorstores.py">
{
    VectorStoreIntegrationSource.replaceAll('__ModuleName__', 'Parrot')
        .replaceAll('__package_name__', 'langchain-parrot-link')
        .replaceAll('__MODULE_NAME__', 'PARROT')
        .replaceAll('__module_name__', 'langchain_parrot_link')
}
</CodeBlock>

Configuring the tests consists of implementing pytest fixtures for setting up an
empty vector store and tearing down the vector store after the test run ends.

| Fixture | Description |
| --- | --- |
| `vectorstore` | A generator that yields an empty vector store for unit tests. The vector store is cleaned up after the test run ends. |

For example, below is the `VectorStoreIntegrationTests` class for the [Chroma](https://python.langchain.com/docs/integrations/vectorstores/chroma/)
integration:

```python
from typing import Generator

import pytest
from langchain_core.vectorstores import VectorStore
from langchain_tests.integration_tests.vectorstores import VectorStoreIntegrationTests

from langchain_chroma import Chroma


class TestChromaStandard(VectorStoreIntegrationTests):
    @pytest.fixture()
    def vectorstore(self) -> Generator[VectorStore, None, None]:  # type: ignore
        """Get an empty vectorstore for unit tests."""
        store = Chroma(embedding_function=self.get_embeddings())
        try:
            yield store
        finally:
            store.delete_collection()
            pass

```

Note that before the initial `yield`, we instantiate the vector store with an
[embeddings](/docs/concepts/embedding_models/) object. This is a pre-defined
["fake" embeddings model](https://python.langchain.com/api_reference/standard_tests/integration_tests/langchain_tests.integration_tests.vectorstores.VectorStoreIntegrationTests.html#langchain_tests.integration_tests.vectorstores.VectorStoreIntegrationTests.get_embeddings)
that will generate short, arbitrary vectors for documents. You can use a different
embeddings object if desired.

In the `finally` block, we call whatever integration-specific logic is needed to
bring the vector store to a clean state. This logic is executed in between each test
(e.g., even if tests fail).

:::note

Details on what tests are run and troubleshooting tips for each test can be found in the [API reference](https://python.langchain.com/api_reference/standard_tests/integration_tests/langchain_tests.integration_tests.vectorstores.VectorStoreIntegrationTests.html).

:::


    </TabItem>
    <TabItem value="embeddings" label="Embeddings">

To configure standard tests for an embeddings model, we subclass `EmbeddingsUnitTests` and `EmbeddingsIntegrationTests`. On each subclass, we override the following `@property` methods to specify the embeddings model to be tested and the embeddings model's configuration:

| Property | Description |
| --- | --- |
| `embeddings_class` | The class for the embeddings model to be tested |
| `embedding_model_params` | The parameters to pass to the embeddings model's constructor |

:::note

Details on what tests are run, how each test can be skipped, and troubleshooting tips for each test can be found in the API references. See details:

- [Unit tests API reference](https://python.langchain.com/api_reference/standard_tests/unit_tests/langchain_tests.unit_tests.embeddings.EmbeddingsUnitTests.html)
- [Integration tests API reference](https://python.langchain.com/api_reference/standard_tests/integration_tests/langchain_tests.integration_tests.embeddings.EmbeddingsIntegrationTests.html)

:::

Unit test example:

import EmbeddingsUnitSource from '../../../../src/theme/integration_template/tests/unit_tests/test_embeddings.py';

<CodeBlock language="python" title="tests/unit_tests/test_embeddings.py">
{
    EmbeddingsUnitSource.replaceAll('__ModuleName__', 'ParrotLink')
        .replaceAll('__package_name__', 'langchain-parrot-link')
        .replaceAll('__MODULE_NAME__', 'PARROT_LINK')
        .replaceAll('__module_name__', 'langchain_parrot_link')
}
</CodeBlock>

Integration test example:


```python title="tests/integration_tests/test_embeddings.py"
from typing import Type

from langchain_parrot_link.embeddings import ParrotLinkEmbeddings
from langchain_tests.integration_tests import EmbeddingsIntegrationTests


class TestParrotLinkEmbeddingsIntegration(EmbeddingsIntegrationTests):
    @property
    def embeddings_class(self) -> Type[ParrotLinkEmbeddings]:
        return ParrotLinkEmbeddings

    @property
    def embedding_model_params(self) -> dict:
        return {"model": "nest-embed-001"}
```

import EmbeddingsIntegrationSource from '../../../../src/theme/integration_template/tests/integration_tests/test_embeddings.py';

<CodeBlock language="python" title="tests/integration_tests/test_embeddings.py">
{
    EmbeddingsIntegrationSource.replaceAll('__ModuleName__', 'ParrotLink')
        .replaceAll('__package_name__', 'langchain-parrot-link')
        .replaceAll('__MODULE_NAME__', 'PARROT_LINK')
        .replaceAll('__module_name__', 'langchain_parrot_link')
}
</CodeBlock>

    </TabItem>
    <TabItem value="tools" label="Tools">

To configure standard tests for a tool, we subclass `ToolsUnitTests` and
`ToolsIntegrationTests`. On each subclass, we override the following `@property` methods
to specify the tool to be tested and the tool's configuration:

| Property | Description |
| --- | --- |
| `tool_constructor` | The constructor for the tool to be tested, or an instantiated tool. |
| `tool_constructor_params` | The parameters to pass to the tool (optional). |
| `tool_invoke_params_example` | An example of the parameters to pass to the tool's `invoke` method. |

If you are testing a tool class and pass a class like `MyTool` to `tool_constructor`, you can pass the parameters to the constructor in `tool_constructor_params`.

If you are testing an instantiated tool, you can pass the instantiated tool to `tool_constructor` and do not
override `tool_constructor_params`.

:::note

Details on what tests are run, how each test can be skipped, and troubleshooting tips for each test can be found in the API references. See details:

- [Unit tests API reference](https://python.langchain.com/api_reference/standard_tests/unit_tests/langchain_tests.unit_tests.tools.ToolsUnitTests.html)
- [Integration tests API reference](https://python.langchain.com/api_reference/standard_tests/integration_tests/langchain_tests.integration_tests.tools.ToolsIntegrationTests.html)

:::

import ToolsUnitSource from '../../../../src/theme/integration_template/tests/unit_tests/test_tools.py';

<CodeBlock language="python" title="tests/unit_tests/test_tools.py">
{
    ToolsUnitSource.replaceAll('__ModuleName__', 'Parrot')
        .replaceAll('__package_name__', 'langchain-parrot-link')
        .replaceAll('__MODULE_NAME__', 'PARROT')
        .replaceAll('__module_name__', 'langchain_parrot_link')
}
</CodeBlock>

import ToolsIntegrationSource from '../../../../src/theme/integration_template/tests/integration_tests/test_tools.py';

<CodeBlock language="python" title="tests/integration_tests/test_tools.py">
{
    ToolsIntegrationSource.replaceAll('__ModuleName__', 'Parrot')
        .replaceAll('__package_name__', 'langchain-parrot-link')
        .replaceAll('__MODULE_NAME__', 'PARROT')
        .replaceAll('__module_name__', 'langchain_parrot_link')
}
</CodeBlock>

    </TabItem>

    <TabItem value="retrievers" label="Retrievers">

To configure standard tests for a retriever, we subclass `RetrieversUnitTests` and
`RetrieversIntegrationTests`. On each subclass, we override the following `@property` methods

| Property | Description |
| --- | --- |
| `retriever_constructor` | The class for the retriever to be tested |
| `retriever_constructor_params` | The parameters to pass to the retriever's constructor |
| `retriever_query_example` | An example of the query to pass to the retriever's `invoke` method |

:::note

Details on what tests are run and troubleshooting tips for each test can be found in the [API reference](https://python.langchain.com/api_reference/standard_tests/integration_tests/langchain_tests.integration_tests.retrievers.RetrieversIntegrationTests.html).

:::

import RetrieverIntegrationSource from '../../../../src/theme/integration_template/tests/integration_tests/test_retrievers.py';

<CodeBlock language="python" title="tests/integration_tests/test_retrievers.py">
{
    RetrieverIntegrationSource.replaceAll('__ModuleName__', 'Parrot')
        .replaceAll('__package_name__', 'langchain-parrot-link')
        .replaceAll('__MODULE_NAME__', 'PARROT')
        .replaceAll('__module_name__', 'langchain_parrot_link')
}
</CodeBlock>

    </TabItem>
</Tabs>

---

### Running the tests

You can run these with the following commands from your project root

<Tabs>
    <TabItem value="poetry" label="Poetry" default>

```bash
# run unit tests without network access
poetry run pytest --disable-socket --allow-unix-socket --asyncio-mode=auto tests/unit_tests

# run integration tests
poetry run pytest --asyncio-mode=auto tests/integration_tests
```

    </TabItem>
    <TabItem value="pip" label="Pip">

```bash
# run unit tests without network access
pytest --disable-socket --allow-unix-socket --asyncio-mode=auto tests/unit_tests

# run integration tests
pytest --asyncio-mode=auto tests/integration_tests
```

    </TabItem>
</Tabs>

## Test suite information and troubleshooting

For a full list of the standard test suites that are available, as well as
information on which tests are included and how to troubleshoot common issues,
see the [Standard Tests API Reference](https://python.langchain.com/api_reference/standard_tests/index.html).

You can see troubleshooting guides under the individual test suites listed in that API Reference. For example,
[here is the guide for `ChatModelIntegrationTests.test_usage_metadata`](https://python.langchain.com/api_reference/standard_tests/integration_tests/langchain_tests.integration_tests.chat_models.ChatModelIntegrationTests.html#langchain_tests.integration_tests.chat_models.ChatModelIntegrationTests.test_usage_metadata).
