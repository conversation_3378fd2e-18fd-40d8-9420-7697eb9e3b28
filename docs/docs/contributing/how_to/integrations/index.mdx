---
pagination_prev: null
pagination_next: contributing/how_to/integrations/package
---

# Contribute integrations

Integrations are a core component of LangChain.
LangChain provides standard interfaces for several different components (language models, vector stores, etc) that are crucial when building LLM applications.


## Why contribute an integration to LangChain?

- **Discoverability:** LangChain is the most used framework for building LLM applications, with over 20 million monthly downloads. LangChain integrations are discoverable by a large community of GenAI builders.
- **Interoperability:** LangChain components expose a standard interface, allowing developers to easily swap them for each other. If you implement a LangChain integration, any developer using a different component will easily be able to swap yours in.
- **Best Practices:** Through their standard interface, LangChain components encourage and facilitate best practices (streaming, async, etc)


## Components to integrate

:::info

See the [Conceptual Guide](../../../concepts/index.mdx) for an overview of all components
supported in LangChain

:::

While any component can be integrated into LangChain, there are specific types of integrations we encourage more:

<table>
  <tr>
    <th>Integrate these ✅</th>
    <th>Not these ❌</th>
  </tr>
  <tr>
    <td>
      <ul>
        <li>Chat Models</li>
        <li>Tools/Toolkits</li>
        <li>Retrievers</li>
        <li>Vector Stores</li>
        <li>Embedding Models</li>
      </ul>
    </td>
    <td>
      <ul>
        <li>LLMs (Text-Completion Models)</li>
        <li>Document Loaders</li>
        <li>Key-Value Stores</li>
        <li>Document Transformers</li>
        <li>Model Caches</li>
        <li>Graphs</li>
        <li>Message Histories</li>
        <li>Callbacks</li>
        <li>Chat Loaders</li>
        <li>Adapters</li>
        <li>Other abstractions</li>
      </ul>
    </td>
  </tr>
</table>

## How to contribute an integration

In order to contribute an integration, you should follow these steps:

1. Confirm that your integration is in the [list of components](#components-to-integrate) we are currently encouraging.
2. [Implement your package](/docs/contributing/how_to/integrations/package/) and publish it to a public github repository.
3. [Implement the standard tests](./standard_tests) for your integration and successfully run them.
4. [Publish your integration](./publish.mdx) by publishing the package to PyPi and add docs in the `docs/docs/integrations` directory of the LangChain monorepo.
5. [Optional] Open and merge a PR to add documentation for your integration to the official LangChain docs.
6. [Optional] Engage with the LangChain team for joint co-marketing ([see below](#co-marketing)).

## Co-marketing

With over 20 million monthly downloads, LangChain has a large audience of developers
building LLM applications. Beyond just listing integrations, we aim to highlight
high-quality, educational examples that inspire developers and advance the ecosystem.

While we occasionally share integrations, we prioritize content that provides
meaningful insights and best practices. Our main social channels are Twitter and
LinkedIn, where we highlight the best examples.

Here are some heuristics for types of content we are excited to promote:

- **Educational content:** Blogs, YouTube videos and other media showcasing educational content. Note that we prefer content that is NOT framed as "here's how to use integration XYZ", but rather "here's how to do ABC", as we find that is more educational and helpful for developers.
- **End-to-end applications:** End-to-end applications are great resources for developers looking to build. We prefer to highlight applications that are more complex/agentic in nature, and that use [LangGraph](https://github.com/langchain-ai/langgraph) as the orchestration framework. We get particularly excited about anything involving long-term memory, human-in-the-loop interaction patterns, or multi-agent architectures.
- **Research:** We love highlighting novel research! Whether it is research built on top of LangChain or that integrates with it.

## Further reading
To get started, let's learn [how to implement an integration package](/docs/contributing/how_to/integrations/package/) for LangChain.
