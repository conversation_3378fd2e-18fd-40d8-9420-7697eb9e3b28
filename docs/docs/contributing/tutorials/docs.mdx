# Make your first docs PR

This tutorial will guide you through making a simple documentation edit, like correcting a typo.

### **Prerequisites**
- GitHub account.
- Familiarity with [GitHub pull requests](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/proposing-changes-to-your-work-with-pull-requests/about-pull-requests) (basic understanding).

---

## Editing a documentation page on GitHub

Sometimes you want to make a small change, like fixing a typo, and the easiest way to do this is to use GitHub's editor directly.

### **Steps**

1. **Navigate to the documentation page in the LangChain docs:**
   - On the documentation page, find the green "Edit this page" link at the bottom of the page.
   - Click the button to be directed to the GitHub editor.
   - If the file you're editing is a Jupyter Notebook (.ipynb) instead of a Markdown (.md, .mdx)
        file, we recommend following the steps in section 3.

2. **Fork the repository:**
   - If you haven't already, GitHub will prompt you to fork the repository to your account.
   - Make sure to fork the repository into your **personal account and not an organization** ([why?](../reference/faq.mdx#how-do-i-allow-maintainers-to-edit-my-pr)).
   - Click the "Fork this repository" button to create a copy of the repository under your account.
   - After forking, you'll automatically be redirected to the correct editor.

3. **Make your changes:**
   - Correct the typo directly in the GitHub editor.

4. **Commit your changes:**
   - Click the "Commit changes..." button at the top-right corner of the page.
   - Give your commit a title like "Fix typo in X section."
   - Optionally, write an extended commit description.
   - Click "Propose changes"

5. **Submit a pull request (PR):**
   - GitHub will redirect you to a page where you can create a pull request.
   - First, review your proposed changes to ensure they are correct.
   - Click **Create pull request**.
   - Give your PR a title like `docs: Fix typo in X section`.
   - Follow the checklist in the PR description template.

## Getting a review

Once you've submitted the pull request, it will be reviewed by the maintainers. You may receive feedback or requests for changes. Keep an eye on the PR to address any comments.

Docs PRs are typically reviewed within a few days, but it may take longer depending on the complexity of the change and the availability of maintainers.

For more information on reviews, see the [Review Process](../reference/review_process.mdx).

## More information

See our [how-to guides](../how_to/documentation/index.mdx) for more information on contributing to documentation:
