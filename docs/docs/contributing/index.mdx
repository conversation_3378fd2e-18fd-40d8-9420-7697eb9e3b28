---
sidebar_position: 0
---
# Welcome Contributors

Hi there! Thank you for your interest in contributing to <PERSON><PERSON>hai<PERSON>.
As an open-source project in a fast developing field, we are extremely open to contributions, whether they involve new features, improved infrastructure, better documentation, or bug fixes.

## Tutorials

More coming soon! We are working on tutorials to help you make your first contribution to the project.

- [**Make your first docs PR**](tutorials/docs.mdx)

## How-to guides

- [**Documentation**](how_to/documentation/index.mdx): Help improve our docs, including this one!
- [**Code**](how_to/code/index.mdx): Help us write code, fix bugs, or improve our infrastructure.
- [**Integrations**](how_to/integrations/index.mdx): Help us integrate with your favorite vendors and tools.
- [**Standard Tests**](how_to/integrations/standard_tests): Ensure your integration passes an expected set of tests.

## Reference

- [**Repository Structure**](reference/repo_structure.mdx): Understand the high level structure of the repository.
- [**Review Process**](reference/review_process.mdx): Learn about the review process for pull requests.
- [**Frequently Asked Questions (FAQ)**](reference/faq.mdx): Get answers to common questions about contributing.

## Community

### 💭 Forum

We have a [LangChain Forum](https://forum.langchain.com/) where users can ask usage questions, discuss design decisions, and propose new features.

If you are able to help answer questions, please do so! This will allow the maintainers to spend more time focused on development and bug fixing.

### 🚩 GitHub Issues

Our [issues](https://github.com/langchain-ai/langchain/issues) page is kept up to date with bugs, docs improvements, and triaged feature requests that are being worked on.

There is a [taxonomy of labels](https://github.com/langchain-ai/langchain/labels?sort=count-desc)
to help with sorting and discovery of issues of interest. Please use these to help
organize issues. Check out the [Help Wanted](https://github.com/langchain-ai/langchain/labels/help%20wanted)
and [Good First Issue](https://github.com/langchain-ai/langchain/labels/good%20first%20issue)
tags for recommendations.

If you start working on an issue, please assign it to yourself.

If you are adding an issue, please try to keep it focused on a single, modular bug/improvement/feature.
If two issues are related, or blocking, please link them rather than combining them.

We will try to keep these issues as up-to-date as possible, though
with the rapid rate of development in this field some may get out of date.
If you notice this happening, please let us know.

### 📢 Community Slack

We have a [community slack](https://www.langchain.com/join-community) where you can ask questions, get help, and discuss the project with other contributors and users.

### 🙋 Getting Help

Our goal is to have the simplest developer setup possible. Should you experience any difficulty getting setup, please
ask in [community slack](https://www.langchain.com/join-community) or open a [forum post](https://forum.langchain.com/).

In a similar vein, we do enforce certain linting, formatting, and documentation standards in the codebase.
If you are finding these difficult (or even just annoying) to work with, feel free to ask in [community slack](https://www.langchain.com/join-community)!
