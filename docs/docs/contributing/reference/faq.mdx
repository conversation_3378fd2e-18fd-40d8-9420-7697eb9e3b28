---
sidebar_position: 6
sidebar_label: FAQ
---
# Frequently Asked Questions

## Pull Requests (PRs)

### How do I allow maintainers to edit my PR?

When you submit a pull request, there may be additional changes
necessary before merging it. Oftentimes, it is more efficient for the
maintainers to make these changes themselves before merging, rather than asking you
to do so in code review.

By default, most pull requests will have a
`✅ Maintainers are allowed to edit this pull request.`
badge in the right-hand sidebar.

If you do not see this badge, you may have this setting off for the fork you are
pull-requesting from. See [this Github docs page](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/working-with-forks/allowing-changes-to-a-pull-request-branch-created-from-a-fork)
for more information.

Notably, Github doesn't allow this setting to be enabled for forks in **organizations** ([issue](https://github.com/orgs/community/discussions/5634)).
If you are working in an organization, we recommend submitting your PR from a personal
fork in order to enable this setting.

### Why hasn't my PR been reviewed?

Please reference our [Review Process](review_process.mdx).

### Why was my PR closed?

Please reference our [Review Process](review_process.mdx).

### I think my PR was closed in a way that didn't follow the review process. What should I do?

Tag `@ccurme` in the PR comments referencing the portion of the review
process that you believe was not followed. We'll take a look!
