# Dependents

Dependents stats for `langchain-ai/langchain`

[![](https://img.shields.io/static/v1?label=Used%20by&message=41717&color=informational&logo=slickpic)](https://github.com/langchain-ai/langchain/network/dependents)
[![](https://img.shields.io/static/v1?label=Used%20by%20(public)&message=538&color=informational&logo=slickpic)](https://github.com/langchain-ai/langchain/network/dependents)
[![](https://img.shields.io/static/v1?label=Used%20by%20(private)&message=41179&color=informational&logo=slickpic)](https://github.com/langchain-ai/langchain/network/dependents)


[update: `2023-12-08`; only dependent repositories with Stars > 100]


| Repository | Stars  |
| :--------  | -----: |
|[<PERSON><PERSON><PERSON>/gpt-engineer](https://github.com/AntonOsika/gpt-engineer) | 46514 |
|[imartinez/privateGPT](https://github.com/imartinez/privateGPT) | 44439 |
|[LAION-AI/Open-Assistant](https://github.com/LAION-AI/Open-Assistant) | 35906 |
|[hpcaitech/ColossalAI](https://github.com/hpcaitech/ColossalAI) | 35528 |
|[moymix/TaskMatrix](https://github.com/moymix/TaskMatrix) | 34342 |
|[geekan/MetaGPT](https://github.com/geekan/MetaGPT) | 31126 |
|[streamlit/streamlit](https://github.com/streamlit/streamlit) | 28911 |
|[reworkd/AgentGPT](https://github.com/reworkd/AgentGPT) | 27833 |
|[StanGirard/quivr](https://github.com/StanGirard/quivr) | 26032 |
|[OpenBB-finance/OpenBBTerminal](https://github.com/OpenBB-finance/OpenBBTerminal) | 24946 |
|[run-llama/llama_index](https://github.com/run-llama/llama_index) | 24859 |
|[jmorganca/ollama](https://github.com/jmorganca/ollama) | 20849 |
|[openai/chatgpt-retrieval-plugin](https://github.com/openai/chatgpt-retrieval-plugin) | 20249 |
|[chatchat-space/Langchain-Chatchat](https://github.com/chatchat-space/Langchain-Chatchat) | 19305 |
|[mindsdb/mindsdb](https://github.com/mindsdb/mindsdb) | 19172 |
|[PromtEngineer/localGPT](https://github.com/PromtEngineer/localGPT) | 17528 |
|[cube-js/cube](https://github.com/cube-js/cube) | 16575 |
|[mlflow/mlflow](https://github.com/mlflow/mlflow) | 16000 |
|[mudler/LocalAI](https://github.com/mudler/LocalAI) | 14067 |
|[logspace-ai/langflow](https://github.com/logspace-ai/langflow) | 13679 |
|[GaiZhenbiao/ChuanhuChatGPT](https://github.com/GaiZhenbiao/ChuanhuChatGPT) | 13648 |
|[arc53/DocsGPT](https://github.com/arc53/DocsGPT) | 13423 |
|[openai/evals](https://github.com/openai/evals) | 12649 |
|[airbytehq/airbyte](https://github.com/airbytehq/airbyte) | 12460 |
|[langgenius/dify](https://github.com/langgenius/dify) | 11859 |
|[databrickslabs/dolly](https://github.com/databrickslabs/dolly) | 10672 |
|[AIGC-Audio/AudioGPT](https://github.com/AIGC-Audio/AudioGPT) | 9437 |
|[langchain-ai/langchainjs](https://github.com/langchain-ai/langchainjs) | 9227 |
|[gventuri/pandas-ai](https://github.com/gventuri/pandas-ai) | 9203 |
|[aws/amazon-sagemaker-examples](https://github.com/aws/amazon-sagemaker-examples) | 9079 |
|[h2oai/h2ogpt](https://github.com/h2oai/h2ogpt) | 8945 |
|[PipedreamHQ/pipedream](https://github.com/PipedreamHQ/pipedream) | 7550 |
|[bentoml/OpenLLM](https://github.com/bentoml/OpenLLM) | 6957 |
|[THUDM/ChatGLM3](https://github.com/THUDM/ChatGLM3) | 6801 |
|[microsoft/promptflow](https://github.com/microsoft/promptflow) | 6776 |
|[cpacker/MemGPT](https://github.com/cpacker/MemGPT) | 6642 |
|[joshpxyne/gpt-migrate](https://github.com/joshpxyne/gpt-migrate) | 6482 |
|[zauberzeug/nicegui](https://github.com/zauberzeug/nicegui) | 6037 |
|[embedchain/embedchain](https://github.com/embedchain/embedchain) | 6023 |
|[mage-ai/mage-ai](https://github.com/mage-ai/mage-ai) | 6019 |
|[assafelovic/gpt-researcher](https://github.com/assafelovic/gpt-researcher) | 5936 |
|[sweepai/sweep](https://github.com/sweepai/sweep) | 5855 |
|[wenda-LLM/wenda](https://github.com/wenda-LLM/wenda) | 5766 |
|[zilliztech/GPTCache](https://github.com/zilliztech/GPTCache) | 5710 |
|[pdm-project/pdm](https://github.com/pdm-project/pdm) | 5665 |
|[GreyDGL/PentestGPT](https://github.com/GreyDGL/PentestGPT) | 5568 |
|[gkamradt/langchain-tutorials](https://github.com/gkamradt/langchain-tutorials) | 5507 |
|[Shaunwei/RealChar](https://github.com/Shaunwei/RealChar) | 5501 |
|[facebookresearch/llama-recipes](https://github.com/facebookresearch/llama-recipes) | 5477 |
|[serge-chat/serge](https://github.com/serge-chat/serge) | 5221 |
|[run-llama/rags](https://github.com/run-llama/rags) | 4916 |
|[openchatai/OpenChat](https://github.com/openchatai/OpenChat) | 4870 |
|[danswer-ai/danswer](https://github.com/danswer-ai/danswer) | 4774 |
|[langchain-ai/opengpts](https://github.com/langchain-ai/opengpts) | 4709 |
|[postgresml/postgresml](https://github.com/postgresml/postgresml) | 4639 |
|[MineDojo/Voyager](https://github.com/MineDojo/Voyager) | 4582 |
|[intel-analytics/BigDL](https://github.com/intel-analytics/BigDL) | 4581 |
|[yihong0618/xiaogpt](https://github.com/yihong0618/xiaogpt) | 4359 |
|[RayVentura/ShortGPT](https://github.com/RayVentura/ShortGPT) | 4357 |
|[Azure-Samples/azure-search-openai-demo](https://github.com/Azure-Samples/azure-search-openai-demo) | 4317 |
|[madawei2699/myGPTReader](https://github.com/madawei2699/myGPTReader) | 4289 |
|[apache/nifi](https://github.com/apache/nifi) | 4098 |
|[langchain-ai/chat-langchain](https://github.com/langchain-ai/chat-langchain) | 4091 |
|[aiwaves-cn/agents](https://github.com/aiwaves-cn/agents) | 4073 |
|[krishnaik06/The-Grand-Complete-Data-Science-Materials](https://github.com/krishnaik06/The-Grand-Complete-Data-Science-Materials) | 4065 |
|[khoj-ai/khoj](https://github.com/khoj-ai/khoj) | 4016 |
|[Azure/azure-sdk-for-python](https://github.com/Azure/azure-sdk-for-python) | 3941 |
|[PrefectHQ/marvin](https://github.com/PrefectHQ/marvin) | 3915 |
|[OpenBMB/ToolBench](https://github.com/OpenBMB/ToolBench) | 3799 |
|[marqo-ai/marqo](https://github.com/marqo-ai/marqo) | 3771 |
|[kyegomez/tree-of-thoughts](https://github.com/kyegomez/tree-of-thoughts) | 3688 |
|[Unstructured-IO/unstructured](https://github.com/Unstructured-IO/unstructured) | 3543 |
|[llm-workflow-engine/llm-workflow-engine](https://github.com/llm-workflow-engine/llm-workflow-engine) | 3515 |
|[shroominic/codeinterpreter-api](https://github.com/shroominic/codeinterpreter-api) | 3425 |
|[openchatai/OpenCopilot](https://github.com/openchatai/OpenCopilot) | 3418 |
|[josStorer/RWKV-Runner](https://github.com/josStorer/RWKV-Runner) | 3297 |
|[whitead/paper-qa](https://github.com/whitead/paper-qa) | 3280 |
|[homanp/superagent](https://github.com/homanp/superagent) | 3258 |
|[ParisNeo/lollms-webui](https://github.com/ParisNeo/lollms-webui) | 3199 |
|[OpenBMB/AgentVerse](https://github.com/OpenBMB/AgentVerse) | 3099 |
|[project-baize/baize-chatbot](https://github.com/project-baize/baize-chatbot) | 3090 |
|[OpenGVLab/InternGPT](https://github.com/OpenGVLab/InternGPT) | 2989 |
|[xlang-ai/OpenAgents](https://github.com/xlang-ai/OpenAgents) | 2825 |
|[dataelement/bisheng](https://github.com/dataelement/bisheng) | 2797 |
|[Mintplex-Labs/anything-llm](https://github.com/Mintplex-Labs/anything-llm) | 2784 |
|[OpenBMB/BMTools](https://github.com/OpenBMB/BMTools) | 2734 |
|[run-llama/llama-hub](https://github.com/run-llama/llama-hub) | 2721 |
|[SamurAIGPT/EmbedAI](https://github.com/SamurAIGPT/EmbedAI) | 2647 |
|[NVIDIA/NeMo-Guardrails](https://github.com/NVIDIA/NeMo-Guardrails) | 2637 |
|[X-D-Lab/LangChain-ChatGLM-Webui](https://github.com/X-D-Lab/LangChain-ChatGLM-Webui) | 2532 |
|[GerevAI/gerev](https://github.com/GerevAI/gerev) | 2517 |
|[keephq/keep](https://github.com/keephq/keep) | 2448 |
|[yanqiangmiffy/Chinese-LangChain](https://github.com/yanqiangmiffy/Chinese-LangChain) | 2397 |
|[OpenGVLab/Ask-Anything](https://github.com/OpenGVLab/Ask-Anything) | 2324 |
|[IntelligenzaArtificiale/Free-Auto-GPT](https://github.com/IntelligenzaArtificiale/Free-Auto-GPT) | 2241 |
|[YiVal/YiVal](https://github.com/YiVal/YiVal) | 2232 |
|[jupyterlab/jupyter-ai](https://github.com/jupyterlab/jupyter-ai) | 2189 |
|[Farama-Foundation/PettingZoo](https://github.com/Farama-Foundation/PettingZoo) | 2136 |
|[microsoft/TaskWeaver](https://github.com/microsoft/TaskWeaver) | 2126 |
|[hwchase17/notion-qa](https://github.com/hwchase17/notion-qa) | 2083 |
|[FlagOpen/FlagEmbedding](https://github.com/FlagOpen/FlagEmbedding) | 2053 |
|[paulpierre/RasaGPT](https://github.com/paulpierre/RasaGPT) | 1999 |
|[hegelai/prompttools](https://github.com/hegelai/prompttools) | 1984 |
|[mckinsey/vizro](https://github.com/mckinsey/vizro) | 1951 |
|[vocodedev/vocode-python](https://github.com/vocodedev/vocode-python) | 1868 |
|[dot-agent/openAMS](https://github.com/dot-agent/openAMS) | 1796 |
|[explodinggradients/ragas](https://github.com/explodinggradients/ragas) | 1766 |
|[AI-Citizen/SolidGPT](https://github.com/AI-Citizen/SolidGPT) | 1761 |
|[Kav-K/GPTDiscord](https://github.com/Kav-K/GPTDiscord) | 1696 |
|[run-llama/sec-insights](https://github.com/run-llama/sec-insights) | 1654 |
|[avinashkranjan/Amazing-Python-Scripts](https://github.com/avinashkranjan/Amazing-Python-Scripts) | 1635 |
|[microsoft/WhatTheHack](https://github.com/microsoft/WhatTheHack) | 1629 |
|[noahshinn/reflexion](https://github.com/noahshinn/reflexion) | 1625 |
|[psychic-api/psychic](https://github.com/psychic-api/psychic) | 1618 |
|[Forethought-Technologies/AutoChain](https://github.com/Forethought-Technologies/AutoChain) | 1611 |
|[pinterest/querybook](https://github.com/pinterest/querybook) | 1586 |
|[refuel-ai/autolabel](https://github.com/refuel-ai/autolabel) | 1553 |
|[jina-ai/langchain-serve](https://github.com/jina-ai/langchain-serve) | 1537 |
|[jina-ai/dev-gpt](https://github.com/jina-ai/dev-gpt) | 1522 |
|[agiresearch/OpenAGI](https://github.com/agiresearch/OpenAGI) | 1493 |
|[ttengwang/Caption-Anything](https://github.com/ttengwang/Caption-Anything) | 1484 |
|[greshake/llm-security](https://github.com/greshake/llm-security) | 1483 |
|[promptfoo/promptfoo](https://github.com/promptfoo/promptfoo) | 1480 |
|[milvus-io/bootcamp](https://github.com/milvus-io/bootcamp) | 1477 |
|[richardyc/Chrome-GPT](https://github.com/richardyc/Chrome-GPT) | 1475 |
|[melih-unsal/DemoGPT](https://github.com/melih-unsal/DemoGPT) | 1428 |
|[YORG-AI/Open-Assistant](https://github.com/YORG-AI/Open-Assistant) | 1419 |
|[101dotxyz/GPTeam](https://github.com/101dotxyz/GPTeam) | 1416 |
|[jina-ai/thinkgpt](https://github.com/jina-ai/thinkgpt) | 1408 |
|[mmz-001/knowledge_gpt](https://github.com/mmz-001/knowledge_gpt) | 1398 |
|[intel/intel-extension-for-transformers](https://github.com/intel/intel-extension-for-transformers) | 1387 |
|[Azure/azureml-examples](https://github.com/Azure/azureml-examples) | 1385 |
|[lunasec-io/lunasec](https://github.com/lunasec-io/lunasec) | 1367 |
|[eyurtsev/kor](https://github.com/eyurtsev/kor) | 1355 |
|[xusenlinzy/api-for-open-llm](https://github.com/xusenlinzy/api-for-open-llm) | 1325 |
|[griptape-ai/griptape](https://github.com/griptape-ai/griptape) | 1323 |
|[SuperDuperDB/superduperdb](https://github.com/SuperDuperDB/superduperdb) | 1290 |
|[cofactoryai/textbase](https://github.com/cofactoryai/textbase) | 1284 |
|[psychic-api/rag-stack](https://github.com/psychic-api/rag-stack) | 1260 |
|[filip-michalsky/SalesGPT](https://github.com/filip-michalsky/SalesGPT) | 1250 |
|[nod-ai/SHARK](https://github.com/nod-ai/SHARK) | 1237 |
|[pluralsh/plural](https://github.com/pluralsh/plural) | 1234 |
|[cheshire-cat-ai/core](https://github.com/cheshire-cat-ai/core) | 1194 |
|[LC1332/Chat-Haruhi-Suzumiya](https://github.com/LC1332/Chat-Haruhi-Suzumiya) | 1184 |
|[poe-platform/server-bot-quick-start](https://github.com/poe-platform/server-bot-quick-start) | 1182 |
|[microsoft/X-Decoder](https://github.com/microsoft/X-Decoder) | 1180 |
|[juncongmoo/chatllama](https://github.com/juncongmoo/chatllama) | 1171 |
|[visual-openllm/visual-openllm](https://github.com/visual-openllm/visual-openllm) | 1156 |
|[alejandro-ao/ask-multiple-pdfs](https://github.com/alejandro-ao/ask-multiple-pdfs) | 1153 |
|[ThousandBirdsInc/chidori](https://github.com/ThousandBirdsInc/chidori) | 1152 |
|[irgolic/AutoPR](https://github.com/irgolic/AutoPR) | 1137 |
|[SamurAIGPT/Camel-AutoGPT](https://github.com/SamurAIGPT/Camel-AutoGPT) | 1083 |
|[ray-project/llm-applications](https://github.com/ray-project/llm-applications) | 1080 |
|[run-llama/llama-lab](https://github.com/run-llama/llama-lab) | 1072 |
|[jiran214/GPT-vup](https://github.com/jiran214/GPT-vup) | 1041 |
|[MetaGLM/FinGLM](https://github.com/MetaGLM/FinGLM) | 1035 |
|[peterw/Chat-with-Github-Repo](https://github.com/peterw/Chat-with-Github-Repo) | 1020 |
|[Anil-matcha/ChatPDF](https://github.com/Anil-matcha/ChatPDF) | 991 |
|[langchain-ai/langserve](https://github.com/langchain-ai/langserve) | 983 |
|[THUDM/AgentTuning](https://github.com/THUDM/AgentTuning) | 976 |
|[rlancemartin/auto-evaluator](https://github.com/rlancemartin/auto-evaluator) | 975 |
|[codeacme17/examor](https://github.com/codeacme17/examor) | 964 |
|[all-in-aigc/gpts-works](https://github.com/all-in-aigc/gpts-works) | 946 |
|[Ikaros-521/AI-Vtuber](https://github.com/Ikaros-521/AI-Vtuber) | 946 |
|[microsoft/Llama-2-Onnx](https://github.com/microsoft/Llama-2-Onnx) | 898 |
|[cirediatpl/FigmaChain](https://github.com/cirediatpl/FigmaChain) | 895 |
|[ricklamers/shell-ai](https://github.com/ricklamers/shell-ai) | 893 |
|[modelscope/modelscope-agent](https://github.com/modelscope/modelscope-agent) | 893 |
|[seanpixel/Teenage-AGI](https://github.com/seanpixel/Teenage-AGI) | 886 |
|[ajndkr/lanarky](https://github.com/ajndkr/lanarky) | 880 |
|[kennethleungty/Llama-2-Open-Source-LLM-CPU-Inference](https://github.com/kennethleungty/Llama-2-Open-Source-LLM-CPU-Inference) | 872 |
|[corca-ai/EVAL](https://github.com/corca-ai/EVAL) | 846 |
|[hwchase17/chat-your-data](https://github.com/hwchase17/chat-your-data) | 841 |
|[kreneskyp/ix](https://github.com/kreneskyp/ix) | 821 |
|[Link-AGI/AutoAgents](https://github.com/Link-AGI/AutoAgents) | 820 |
|[truera/trulens](https://github.com/truera/trulens) | 794 |
|[Dataherald/dataherald](https://github.com/Dataherald/dataherald) | 788 |
|[sunlabuiuc/PyHealth](https://github.com/sunlabuiuc/PyHealth) | 783 |
|[jondurbin/airoboros](https://github.com/jondurbin/airoboros) | 783 |
|[pyspark-ai/pyspark-ai](https://github.com/pyspark-ai/pyspark-ai) | 782 |
|[confident-ai/deepeval](https://github.com/confident-ai/deepeval) | 780 |
|[billxbf/ReWOO](https://github.com/billxbf/ReWOO) | 777 |
|[langchain-ai/streamlit-agent](https://github.com/langchain-ai/streamlit-agent) | 776 |
|[akshata29/entaoai](https://github.com/akshata29/entaoai) | 771 |
|[LambdaLabsML/examples](https://github.com/LambdaLabsML/examples) | 770 |
|[getmetal/motorhead](https://github.com/getmetal/motorhead) | 768 |
|[Dicklesworthstone/swiss_army_llama](https://github.com/Dicklesworthstone/swiss_army_llama) | 757 |
|[ruoccofabrizio/azure-open-ai-embeddings-qna](https://github.com/ruoccofabrizio/azure-open-ai-embeddings-qna) | 757 |
|[msoedov/langcorn](https://github.com/msoedov/langcorn) | 754 |
|[e-johnstonn/BriefGPT](https://github.com/e-johnstonn/BriefGPT) | 753 |
|[microsoft/sample-app-aoai-chatGPT](https://github.com/microsoft/sample-app-aoai-chatGPT) | 749 |
|[explosion/spacy-llm](https://github.com/explosion/spacy-llm) | 731 |
|[MiuLab/Taiwan-LLM](https://github.com/MiuLab/Taiwan-LLM) | 716 |
|[whyiyhw/chatgpt-wechat](https://github.com/whyiyhw/chatgpt-wechat) | 702 |
|[Azure-Samples/openai](https://github.com/Azure-Samples/openai) | 692 |
|[iusztinpaul/hands-on-llms](https://github.com/iusztinpaul/hands-on-llms) | 687 |
|[safevideo/autollm](https://github.com/safevideo/autollm) | 682 |
|[OpenGenerativeAI/GenossGPT](https://github.com/OpenGenerativeAI/GenossGPT) | 669 |
|[NoDataFound/hackGPT](https://github.com/NoDataFound/hackGPT) | 663 |
|[AILab-CVC/GPT4Tools](https://github.com/AILab-CVC/GPT4Tools) | 662 |
|[langchain-ai/auto-evaluator](https://github.com/langchain-ai/auto-evaluator) | 657 |
|[yvann-ba/Robby-chatbot](https://github.com/yvann-ba/Robby-chatbot) | 639 |
|[alexanderatallah/window.ai](https://github.com/alexanderatallah/window.ai) | 635 |
|[amosjyng/langchain-visualizer](https://github.com/amosjyng/langchain-visualizer) | 630 |
|[microsoft/PodcastCopilot](https://github.com/microsoft/PodcastCopilot) | 621 |
|[aws-samples/aws-genai-llm-chatbot](https://github.com/aws-samples/aws-genai-llm-chatbot) | 616 |
|[NeumTry/NeumAI](https://github.com/NeumTry/NeumAI) | 605 |
|[namuan/dr-doc-search](https://github.com/namuan/dr-doc-search) | 599 |
|[plastic-labs/tutor-gpt](https://github.com/plastic-labs/tutor-gpt) | 595 |
|[marimo-team/marimo](https://github.com/marimo-team/marimo) | 591 |
|[yakami129/VirtualWife](https://github.com/yakami129/VirtualWife) | 586 |
|[xuwenhao/geektime-ai-course](https://github.com/xuwenhao/geektime-ai-course) | 584 |
|[jonra1993/fastapi-alembic-sqlmodel-async](https://github.com/jonra1993/fastapi-alembic-sqlmodel-async) | 573 |
|[dgarnitz/vectorflow](https://github.com/dgarnitz/vectorflow) | 568 |
|[yeagerai/yeagerai-agent](https://github.com/yeagerai/yeagerai-agent) | 564 |
|[daveebbelaar/langchain-experiments](https://github.com/daveebbelaar/langchain-experiments) | 563 |
|[traceloop/openllmetry](https://github.com/traceloop/openllmetry) | 559 |
|[Agenta-AI/agenta](https://github.com/Agenta-AI/agenta) | 546 |
|[michaelthwan/searchGPT](https://github.com/michaelthwan/searchGPT) | 545 |
|[jina-ai/agentchain](https://github.com/jina-ai/agentchain) | 544 |
|[mckaywrigley/repo-chat](https://github.com/mckaywrigley/repo-chat) | 533 |
|[marella/chatdocs](https://github.com/marella/chatdocs) | 532 |
|[opentensor/bittensor](https://github.com/opentensor/bittensor) | 532 |
|[DjangoPeng/openai-quickstart](https://github.com/DjangoPeng/openai-quickstart) | 527 |
|[freddyaboulton/gradio-tools](https://github.com/freddyaboulton/gradio-tools) | 517 |
|[sidhq/Multi-GPT](https://github.com/sidhq/Multi-GPT) | 515 |
|[alejandro-ao/langchain-ask-pdf](https://github.com/alejandro-ao/langchain-ask-pdf) | 514 |
|[sajjadium/ctf-archives](https://github.com/sajjadium/ctf-archives) | 507 |
|[continuum-llms/chatgpt-memory](https://github.com/continuum-llms/chatgpt-memory) | 502 |
|[steamship-core/steamship-langchain](https://github.com/steamship-core/steamship-langchain) | 494 |
|[mpaepper/content-chatbot](https://github.com/mpaepper/content-chatbot) | 493 |
|[langchain-ai/langchain-aiplugin](https://github.com/langchain-ai/langchain-aiplugin) | 492 |
|[logan-markewich/llama_index_starter_pack](https://github.com/logan-markewich/llama_index_starter_pack) | 483 |
|[datawhalechina/llm-universe](https://github.com/datawhalechina/llm-universe) | 475 |
|[leondz/garak](https://github.com/leondz/garak) | 464 |
|[RedisVentures/ArXivChatGuru](https://github.com/RedisVentures/ArXivChatGuru) | 461 |
|[Anil-matcha/Chatbase](https://github.com/Anil-matcha/Chatbase) | 455 |
|[Aiyu-awa/luna-ai](https://github.com/Aiyu-awa/luna-ai) | 450 |
|[DataDog/dd-trace-py](https://github.com/DataDog/dd-trace-py) | 450 |
|[Azure-Samples/miyagi](https://github.com/Azure-Samples/miyagi) | 449 |
|[poe-platform/poe-protocol](https://github.com/poe-platform/poe-protocol) | 447 |
|[onlyphantom/llm-python](https://github.com/onlyphantom/llm-python) | 446 |
|[junruxiong/IncarnaMind](https://github.com/junruxiong/IncarnaMind) | 441 |
|[CarperAI/OpenELM](https://github.com/CarperAI/OpenELM) | 441 |
|[daodao97/chatdoc](https://github.com/daodao97/chatdoc) | 437 |
|[showlab/VLog](https://github.com/showlab/VLog) | 436 |
|[wandb/weave](https://github.com/wandb/weave) | 420 |
|[QwenLM/Qwen-Agent](https://github.com/QwenLM/Qwen-Agent) | 419 |
|[huchenxucs/ChatDB](https://github.com/huchenxucs/ChatDB) | 416 |
|[jerlendds/osintbuddy](https://github.com/jerlendds/osintbuddy) | 411 |
|[monarch-initiative/ontogpt](https://github.com/monarch-initiative/ontogpt) | 408 |
|[mallorbc/Finetune_LLMs](https://github.com/mallorbc/Finetune_LLMs) | 406 |
|[JayZeeDesign/researcher-gpt](https://github.com/JayZeeDesign/researcher-gpt) | 405 |
|[rsaryev/talk-codebase](https://github.com/rsaryev/talk-codebase) | 401 |
|[langchain-ai/langsmith-cookbook](https://github.com/langchain-ai/langsmith-cookbook) | 398 |
|[mtenenholtz/chat-twitter](https://github.com/mtenenholtz/chat-twitter) | 398 |
|[morpheuslord/GPT_Vuln-analyzer](https://github.com/morpheuslord/GPT_Vuln-analyzer) | 391 |
|[MagnivOrg/prompt-layer-library](https://github.com/MagnivOrg/prompt-layer-library) | 387 |
|[JohnSnowLabs/langtest](https://github.com/JohnSnowLabs/langtest) | 384 |
|[mrwadams/attackgen](https://github.com/mrwadams/attackgen) | 381 |
|[codefuse-ai/Test-Agent](https://github.com/codefuse-ai/Test-Agent) | 380 |
|[personoids/personoids-lite](https://github.com/personoids/personoids-lite) | 379 |
|[mosaicml/examples](https://github.com/mosaicml/examples) | 378 |
|[steamship-packages/langchain-production-starter](https://github.com/steamship-packages/langchain-production-starter) | 370 |
|[FlagAI-Open/Aquila2](https://github.com/FlagAI-Open/Aquila2) | 365 |
|[Mintplex-Labs/vector-admin](https://github.com/Mintplex-Labs/vector-admin) | 365 |
|[NimbleBoxAI/ChainFury](https://github.com/NimbleBoxAI/ChainFury) | 357 |
|[BlackHC/llm-strategy](https://github.com/BlackHC/llm-strategy) | 354 |
|[lilacai/lilac](https://github.com/lilacai/lilac) | 352 |
|[preset-io/promptimize](https://github.com/preset-io/promptimize) | 351 |
|[yuanjie-ai/ChatLLM](https://github.com/yuanjie-ai/ChatLLM) | 347 |
|[andylokandy/gpt-4-search](https://github.com/andylokandy/gpt-4-search) | 346 |
|[zhoudaquan/ChatAnything](https://github.com/zhoudaquan/ChatAnything) | 343 |
|[rgomezcasas/dotfiles](https://github.com/rgomezcasas/dotfiles) | 343 |
|[tigerlab-ai/tiger](https://github.com/tigerlab-ai/tiger) | 342 |
|[HumanSignal/label-studio-ml-backend](https://github.com/HumanSignal/label-studio-ml-backend) | 334 |
|[nasa-petal/bidara](https://github.com/nasa-petal/bidara) | 334 |
|[momegas/megabots](https://github.com/momegas/megabots) | 334 |
|[Cheems-Seminar/grounded-segment-any-parts](https://github.com/Cheems-Seminar/grounded-segment-any-parts) | 330 |
|[CambioML/pykoi](https://github.com/CambioML/pykoi) | 326 |
|[Nuggt-dev/Nuggt](https://github.com/Nuggt-dev/Nuggt) | 326 |
|[wandb/edu](https://github.com/wandb/edu) | 326 |
|[Haste171/langchain-chatbot](https://github.com/Haste171/langchain-chatbot) | 324 |
|[sugarforever/LangChain-Tutorials](https://github.com/sugarforever/LangChain-Tutorials) | 322 |
|[liangwq/Chatglm_lora_multi-gpu](https://github.com/liangwq/Chatglm_lora_multi-gpu) | 321 |
|[ur-whitelab/chemcrow-public](https://github.com/ur-whitelab/chemcrow-public) | 320 |
|[itamargol/openai](https://github.com/itamargol/openai) | 318 |
|[gia-guar/JARVIS-ChatGPT](https://github.com/gia-guar/JARVIS-ChatGPT) | 304 |
|[SpecterOps/Nemesis](https://github.com/SpecterOps/Nemesis) | 302 |
|[facebookresearch/personal-timeline](https://github.com/facebookresearch/personal-timeline) | 302 |
|[hnawaz007/pythondataanalysis](https://github.com/hnawaz007/pythondataanalysis) | 301 |
|[Chainlit/cookbook](https://github.com/Chainlit/cookbook) | 300 |
|[airobotlab/KoChatGPT](https://github.com/airobotlab/KoChatGPT) | 300 |
|[GPT-Fathom/GPT-Fathom](https://github.com/GPT-Fathom/GPT-Fathom) | 299 |
|[kaarthik108/snowChat](https://github.com/kaarthik108/snowChat) | 299 |
|[kyegomez/swarms](https://github.com/kyegomez/swarms) | 296 |
|[LangStream/langstream](https://github.com/LangStream/langstream) | 295 |
|[genia-dev/GeniA](https://github.com/genia-dev/GeniA) | 294 |
|[shamspias/customizable-gpt-chatbot](https://github.com/shamspias/customizable-gpt-chatbot) | 291 |
|[TsinghuaDatabaseGroup/DB-GPT](https://github.com/TsinghuaDatabaseGroup/DB-GPT) | 290 |
|[conceptofmind/toolformer](https://github.com/conceptofmind/toolformer) | 283 |
|[sullivan-sean/chat-langchainjs](https://github.com/sullivan-sean/chat-langchainjs) | 283 |
|[AutoPackAI/beebot](https://github.com/AutoPackAI/beebot) | 282 |
|[pablomarin/GPT-Azure-Search-Engine](https://github.com/pablomarin/GPT-Azure-Search-Engine) | 282 |
|[gkamradt/LLMTest_NeedleInAHaystack](https://github.com/gkamradt/LLMTest_NeedleInAHaystack) | 280 |
|[gustavz/DataChad](https://github.com/gustavz/DataChad) | 280 |
|[Safiullah-Rahu/CSV-AI](https://github.com/Safiullah-Rahu/CSV-AI) | 278 |
|[hwchase17/chroma-langchain](https://github.com/hwchase17/chroma-langchain) | 275 |
|[AkshitIreddy/Interactive-LLM-Powered-NPCs](https://github.com/AkshitIreddy/Interactive-LLM-Powered-NPCs) | 268 |
|[ennucore/clippinator](https://github.com/ennucore/clippinator) | 267 |
|[artitw/text2text](https://github.com/artitw/text2text) | 264 |
|[anarchy-ai/LLM-VM](https://github.com/anarchy-ai/LLM-VM) | 263 |
|[wpydcr/LLM-Kit](https://github.com/wpydcr/LLM-Kit) | 262 |
|[streamlit/llm-examples](https://github.com/streamlit/llm-examples) | 262 |
|[paolorechia/learn-langchain](https://github.com/paolorechia/learn-langchain) | 262 |
|[yym68686/ChatGPT-Telegram-Bot](https://github.com/yym68686/ChatGPT-Telegram-Bot) | 261 |
|[PradipNichite/Youtube-Tutorials](https://github.com/PradipNichite/Youtube-Tutorials) | 259 |
|[radi-cho/datasetGPT](https://github.com/radi-cho/datasetGPT) | 259 |
|[ur-whitelab/exmol](https://github.com/ur-whitelab/exmol) | 259 |
|[ml6team/fondant](https://github.com/ml6team/fondant) | 254 |
|[bborn/howdoi.ai](https://github.com/bborn/howdoi.ai) | 254 |
|[rahulnyk/knowledge_graph](https://github.com/rahulnyk/knowledge_graph) | 253 |
|[recalign/RecAlign](https://github.com/recalign/RecAlign) | 248 |
|[hwchase17/langchain-streamlit-template](https://github.com/hwchase17/langchain-streamlit-template) | 248 |
|[fetchai/uAgents](https://github.com/fetchai/uAgents) | 247 |
|[arthur-ai/bench](https://github.com/arthur-ai/bench) | 247 |
|[miaoshouai/miaoshouai-assistant](https://github.com/miaoshouai/miaoshouai-assistant) | 246 |
|[RoboCoachTechnologies/GPT-Synthesizer](https://github.com/RoboCoachTechnologies/GPT-Synthesizer) | 244 |
|[langchain-ai/web-explorer](https://github.com/langchain-ai/web-explorer) | 242 |
|[kaleido-lab/dolphin](https://github.com/kaleido-lab/dolphin) | 242 |
|[PJLab-ADG/DriveLikeAHuman](https://github.com/PJLab-ADG/DriveLikeAHuman) | 241 |
|[stepanogil/autonomous-hr-chatbot](https://github.com/stepanogil/autonomous-hr-chatbot) | 238 |
|[WongSaang/chatgpt-ui-server](https://github.com/WongSaang/chatgpt-ui-server) | 236 |
|[nexus-stc/stc](https://github.com/nexus-stc/stc) | 235 |
|[yeagerai/genworlds](https://github.com/yeagerai/genworlds) | 235 |
|[Gentopia-AI/Gentopia](https://github.com/Gentopia-AI/Gentopia) | 235 |
|[alphasecio/langchain-examples](https://github.com/alphasecio/langchain-examples) | 235 |
|[grumpyp/aixplora](https://github.com/grumpyp/aixplora) | 232 |
|[shaman-ai/agent-actors](https://github.com/shaman-ai/agent-actors) | 232 |
|[darrenburns/elia](https://github.com/darrenburns/elia) | 231 |
|[orgexyz/BlockAGI](https://github.com/orgexyz/BlockAGI) | 231 |
|[handrew/browserpilot](https://github.com/handrew/browserpilot) | 226 |
|[su77ungr/CASALIOY](https://github.com/su77ungr/CASALIOY) | 225 |
|[nicknochnack/LangchainDocuments](https://github.com/nicknochnack/LangchainDocuments) | 225 |
|[dbpunk-labs/octogen](https://github.com/dbpunk-labs/octogen) | 224 |
|[langchain-ai/weblangchain](https://github.com/langchain-ai/weblangchain) | 222 |
|[CL-lau/SQL-GPT](https://github.com/CL-lau/SQL-GPT) | 222 |
|[alvarosevilla95/autolang](https://github.com/alvarosevilla95/autolang) | 221 |
|[showlab/UniVTG](https://github.com/showlab/UniVTG) | 220 |
|[edreisMD/plugnplai](https://github.com/edreisMD/plugnplai) | 219 |
|[hardbyte/qabot](https://github.com/hardbyte/qabot) | 216 |
|[microsoft/azure-openai-in-a-day-workshop](https://github.com/microsoft/azure-openai-in-a-day-workshop) | 215 |
|[Azure-Samples/chat-with-your-data-solution-accelerator](https://github.com/Azure-Samples/chat-with-your-data-solution-accelerator) | 214 |
|[amadad/agentcy](https://github.com/amadad/agentcy) | 213 |
|[snexus/llm-search](https://github.com/snexus/llm-search) | 212 |
|[afaqueumer/DocQA](https://github.com/afaqueumer/DocQA) | 206 |
|[plchld/InsightFlow](https://github.com/plchld/InsightFlow) | 205 |
|[yasyf/compress-gpt](https://github.com/yasyf/compress-gpt) | 205 |
|[benthecoder/ClassGPT](https://github.com/benthecoder/ClassGPT) | 205 |
|[voxel51/voxelgpt](https://github.com/voxel51/voxelgpt) | 204 |
|[jbrukh/gpt-jargon](https://github.com/jbrukh/gpt-jargon) | 204 |
|[emarco177/ice_breaker](https://github.com/emarco177/ice_breaker) | 204 |
|[tencentmusic/supersonic](https://github.com/tencentmusic/supersonic) | 202 |
|[Azure-Samples/azure-search-power-skills](https://github.com/Azure-Samples/azure-search-power-skills) | 202 |
|[blob42/Instrukt](https://github.com/blob42/Instrukt) | 201 |
|[langchain-ai/langsmith-sdk](https://github.com/langchain-ai/langsmith-sdk) | 200 |
|[SamPink/dev-gpt](https://github.com/SamPink/dev-gpt) | 200 |
|[ju-bezdek/langchain-decorators](https://github.com/ju-bezdek/langchain-decorators) | 198 |
|[KMnO4-zx/huanhuan-chat](https://github.com/KMnO4-zx/huanhuan-chat) | 196 |
|[Azure-Samples/jp-azureopenai-samples](https://github.com/Azure-Samples/jp-azureopenai-samples) | 192 |
|[hongbo-miao/hongbomiao.com](https://github.com/hongbo-miao/hongbomiao.com) | 190 |
|[CakeCrusher/openplugin](https://github.com/CakeCrusher/openplugin) | 190 |
|[PaddlePaddle/ERNIE-Bot-SDK](https://github.com/PaddlePaddle/ERNIE-Bot-SDK) | 189 |
|[retr0reg/Ret2GPT](https://github.com/retr0reg/Ret2GPT) | 189 |
|[AmineDiro/cria](https://github.com/AmineDiro/cria) | 187 |
|[lancedb/vectordb-recipes](https://github.com/lancedb/vectordb-recipes) | 186 |
|[vaibkumr/prompt-optimizer](https://github.com/vaibkumr/prompt-optimizer) | 185 |
|[aws-ia/ecs-blueprints](https://github.com/aws-ia/ecs-blueprints) | 184 |
|[ethanyanjiali/minChatGPT](https://github.com/ethanyanjiali/minChatGPT) | 183 |
|[MuhammadMoinFaisal/LargeLanguageModelsProjects](https://github.com/MuhammadMoinFaisal/LargeLanguageModelsProjects) | 182 |
|[shauryr/S2QA](https://github.com/shauryr/S2QA) | 181 |
|[summarizepaper/summarizepaper](https://github.com/summarizepaper/summarizepaper) | 180 |
|[NomaDamas/RAGchain](https://github.com/NomaDamas/RAGchain) | 179 |
|[pnkvalavala/repochat](https://github.com/pnkvalavala/repochat) | 179 |
|[ibiscp/LLM-IMDB](https://github.com/ibiscp/LLM-IMDB) | 177 |
|[fengyuli-dev/multimedia-gpt](https://github.com/fengyuli-dev/multimedia-gpt) | 177 |
|[langchain-ai/text-split-explorer](https://github.com/langchain-ai/text-split-explorer) | 175 |
|[iMagist486/ElasticSearch-Langchain-Chatglm2](https://github.com/iMagist486/ElasticSearch-Langchain-Chatglm2) | 175 |
|[limaoyi1/Auto-PPT](https://github.com/limaoyi1/Auto-PPT) | 175 |
|[Open-Swarm-Net/GPT-Swarm](https://github.com/Open-Swarm-Net/GPT-Swarm) | 175 |
|[morpheuslord/HackBot](https://github.com/morpheuslord/HackBot) | 174 |
|[v7labs/benchllm](https://github.com/v7labs/benchllm) | 174 |
|[Coding-Crashkurse/Langchain-Full-Course](https://github.com/Coding-Crashkurse/Langchain-Full-Course) | 174 |
|[dongyh20/Octopus](https://github.com/dongyh20/Octopus) | 173 |
|[kimtth/azure-openai-llm-vector-langchain](https://github.com/kimtth/azure-openai-llm-vector-langchain) | 173 |
|[mayooear/private-chatbot-mpt30b-langchain](https://github.com/mayooear/private-chatbot-mpt30b-langchain) | 173 |
|[zilliztech/akcio](https://github.com/zilliztech/akcio) | 172 |
|[jmpaz/promptlib](https://github.com/jmpaz/promptlib) | 172 |
|[ccurme/yolopandas](https://github.com/ccurme/yolopandas) | 172 |
|[joaomdmoura/CrewAI](https://github.com/joaomdmoura/CrewAI) | 170 |
|[katanaml/llm-mistral-invoice-cpu](https://github.com/katanaml/llm-mistral-invoice-cpu) | 170 |
|[chakkaradeep/pyCodeAGI](https://github.com/chakkaradeep/pyCodeAGI) | 170 |
|[mudler/LocalAGI](https://github.com/mudler/LocalAGI) | 167 |
|[dssjon/biblos](https://github.com/dssjon/biblos) | 165 |
|[kjappelbaum/gptchem](https://github.com/kjappelbaum/gptchem) | 165 |
|[xxw1995/chatglm3-finetune](https://github.com/xxw1995/chatglm3-finetune) | 164 |
|[ArjanCodes/examples](https://github.com/ArjanCodes/examples) | 163 |
|[AIAnytime/Llama2-Medical-Chatbot](https://github.com/AIAnytime/Llama2-Medical-Chatbot) | 163 |
|[RCGAI/SimplyRetrieve](https://github.com/RCGAI/SimplyRetrieve) | 162 |
|[langchain-ai/langchain-teacher](https://github.com/langchain-ai/langchain-teacher) | 162 |
|[menloparklab/falcon-langchain](https://github.com/menloparklab/falcon-langchain) | 162 |
|[flurb18/AgentOoba](https://github.com/flurb18/AgentOoba) | 162 |
|[homanp/vercel-langchain](https://github.com/homanp/vercel-langchain) | 161 |
|[jiran214/langup-ai](https://github.com/jiran214/langup-ai) | 160 |
|[JorisdeJong123/7-Days-of-LangChain](https://github.com/JorisdeJong123/7-Days-of-LangChain) | 160 |
|[GoogleCloudPlatform/data-analytics-golden-demo](https://github.com/GoogleCloudPlatform/data-analytics-golden-demo) | 159 |
|[positive666/Prompt-Can-Anything](https://github.com/positive666/Prompt-Can-Anything) | 159 |
|[luisroque/large_laguage_models](https://github.com/luisroque/large_laguage_models) | 159 |
|[mlops-for-all/mlops-for-all.github.io](https://github.com/mlops-for-all/mlops-for-all.github.io) | 158 |
|[wandb/wandbot](https://github.com/wandb/wandbot) | 158 |
|[elastic/elasticsearch-labs](https://github.com/elastic/elasticsearch-labs) | 157 |
|[shroominic/funcchain](https://github.com/shroominic/funcchain) | 157 |
|[deeppavlov/dream](https://github.com/deeppavlov/dream) | 156 |
|[mluogh/eastworld](https://github.com/mluogh/eastworld) | 154 |
|[georgesung/llm_qlora](https://github.com/georgesung/llm_qlora) | 154 |
|[RUC-GSAI/YuLan-Rec](https://github.com/RUC-GSAI/YuLan-Rec) | 153 |
|[KylinC/ChatFinance](https://github.com/KylinC/ChatFinance) | 152 |
|[Dicklesworthstone/llama2_aided_tesseract](https://github.com/Dicklesworthstone/llama2_aided_tesseract) | 152 |
|[c0sogi/LLMChat](https://github.com/c0sogi/LLMChat) | 152 |
|[eunomia-bpf/GPTtrace](https://github.com/eunomia-bpf/GPTtrace) | 152 |
|[ErikBjare/gptme](https://github.com/ErikBjare/gptme) | 152 |
|[Klingefjord/chatgpt-telegram](https://github.com/Klingefjord/chatgpt-telegram) | 152 |
|[RoboCoachTechnologies/ROScribe](https://github.com/RoboCoachTechnologies/ROScribe) | 151 |
|[Aggregate-Intellect/sherpa](https://github.com/Aggregate-Intellect/sherpa) | 151 |
|[3Alan/DocsMind](https://github.com/3Alan/DocsMind) | 151 |
|[tangqiaoyu/ToolAlpaca](https://github.com/tangqiaoyu/ToolAlpaca) | 150 |
|[kulltc/chatgpt-sql](https://github.com/kulltc/chatgpt-sql) | 150 |
|[mallahyari/drqa](https://github.com/mallahyari/drqa) | 150 |
|[MedalCollector/Orator](https://github.com/MedalCollector/Orator) | 149 |
|[Teahouse-Studios/akari-bot](https://github.com/Teahouse-Studios/akari-bot) | 149 |
|[realminchoi/babyagi-ui](https://github.com/realminchoi/babyagi-ui) | 148 |
|[ssheng/BentoChain](https://github.com/ssheng/BentoChain) | 148 |
|[solana-labs/chatgpt-plugin](https://github.com/solana-labs/chatgpt-plugin) | 147 |
|[aurelio-labs/arxiv-bot](https://github.com/aurelio-labs/arxiv-bot) | 147 |
|[Jaseci-Labs/jaseci](https://github.com/Jaseci-Labs/jaseci) | 146 |
|[menloparklab/langchain-cohere-qdrant-doc-retrieval](https://github.com/menloparklab/langchain-cohere-qdrant-doc-retrieval) | 146 |
|[trancethehuman/entities-extraction-web-scraper](https://github.com/trancethehuman/entities-extraction-web-scraper) | 144 |
|[peterw/StoryStorm](https://github.com/peterw/StoryStorm) | 144 |
|[grumpyp/chroma-langchain-tutorial](https://github.com/grumpyp/chroma-langchain-tutorial) | 144 |
|[gh18l/CrawlGPT](https://github.com/gh18l/CrawlGPT) | 142 |
|[langchain-ai/langchain-aws-template](https://github.com/langchain-ai/langchain-aws-template) | 142 |
|[yasyf/summ](https://github.com/yasyf/summ) | 141 |
|[petehunt/langchain-github-bot](https://github.com/petehunt/langchain-github-bot) | 141 |
|[hirokidaichi/wanna](https://github.com/hirokidaichi/wanna) | 140 |
|[jina-ai/fastapi-serve](https://github.com/jina-ai/fastapi-serve) | 139 |
|[zenml-io/zenml-projects](https://github.com/zenml-io/zenml-projects) | 139 |
|[jlonge4/local_llama](https://github.com/jlonge4/local_llama) | 139 |
|[smyja/blackmaria](https://github.com/smyja/blackmaria) | 138 |
|[ChuloAI/BrainChulo](https://github.com/ChuloAI/BrainChulo) | 137 |
|[log1stics/voice-generator-webui](https://github.com/log1stics/voice-generator-webui) | 137 |
|[davila7/file-gpt](https://github.com/davila7/file-gpt) | 137 |
|[dcaribou/transfermarkt-datasets](https://github.com/dcaribou/transfermarkt-datasets) | 136 |
|[ciare-robotics/world-creator](https://github.com/ciare-robotics/world-creator) | 135 |
|[Undertone0809/promptulate](https://github.com/Undertone0809/promptulate) | 134 |
|[fixie-ai/fixie-examples](https://github.com/fixie-ai/fixie-examples) | 134 |
|[run-llama/ai-engineer-workshop](https://github.com/run-llama/ai-engineer-workshop) | 133 |
|[definitive-io/code-indexer-loop](https://github.com/definitive-io/code-indexer-loop) | 131 |
|[mortium91/langchain-assistant](https://github.com/mortium91/langchain-assistant) | 131 |
|[baidubce/bce-qianfan-sdk](https://github.com/baidubce/bce-qianfan-sdk) | 130 |
|[Ngonie-x/langchain_csv](https://github.com/Ngonie-x/langchain_csv) | 130 |
|[IvanIsCoding/ResuLLMe](https://github.com/IvanIsCoding/ResuLLMe) | 130 |
|[AnchoringAI/anchoring-ai](https://github.com/AnchoringAI/anchoring-ai) | 129 |
|[Azure/business-process-automation](https://github.com/Azure/business-process-automation) | 128 |
|[athina-ai/athina-sdk](https://github.com/athina-ai/athina-sdk) | 126 |
|[thunlp/ChatEval](https://github.com/thunlp/ChatEval) | 126 |
|[prof-frink-lab/slangchain](https://github.com/prof-frink-lab/slangchain) | 126 |
|[vietanhdev/pautobot](https://github.com/vietanhdev/pautobot) | 125 |
|[awslabs/generative-ai-cdk-constructs](https://github.com/awslabs/generative-ai-cdk-constructs) | 124 |
|[sdaaron/QueryGPT](https://github.com/sdaaron/QueryGPT) | 124 |
|[rabbitmetrics/langchain-13-min](https://github.com/rabbitmetrics/langchain-13-min) | 124 |
|[AutoLLM/AutoAgents](https://github.com/AutoLLM/AutoAgents) | 122 |
|[nicknochnack/Nopenai](https://github.com/nicknochnack/Nopenai) | 122 |
|[wombyz/HormoziGPT](https://github.com/wombyz/HormoziGPT) | 122 |
|[dotvignesh/PDFChat](https://github.com/dotvignesh/PDFChat) | 122 |
|[topoteretes/PromethAI-Backend](https://github.com/topoteretes/PromethAI-Backend) | 121 |
|[nftblackmagic/flask-langchain](https://github.com/nftblackmagic/flask-langchain) | 121 |
|[vishwasg217/finsight](https://github.com/vishwasg217/finsight) | 120 |
|[snap-stanford/MLAgentBench](https://github.com/snap-stanford/MLAgentBench) | 120 |
|[Azure/app-service-linux-docs](https://github.com/Azure/app-service-linux-docs) | 120 |
|[nyanp/chat2plot](https://github.com/nyanp/chat2plot) | 120 |
|[ant4g0nist/polar](https://github.com/ant4g0nist/polar) | 119 |
|[aws-samples/cdk-eks-blueprints-patterns](https://github.com/aws-samples/cdk-eks-blueprints-patterns) | 119 |
|[aws-samples/amazon-kendra-langchain-extensions](https://github.com/aws-samples/amazon-kendra-langchain-extensions) | 119 |
|[Xueheng-Li/SynologyChatbotGPT](https://github.com/Xueheng-Li/SynologyChatbotGPT) | 119 |
|[CodeAlchemyAI/ViLT-GPT](https://github.com/CodeAlchemyAI/ViLT-GPT) | 117 |
|[Lin-jun-xiang/docGPT-langchain](https://github.com/Lin-jun-xiang/docGPT-langchain) | 117 |
|[ademakdogan/ChatSQL](https://github.com/ademakdogan/ChatSQL) | 116 |
|[aniketmaurya/llm-inference](https://github.com/aniketmaurya/llm-inference) | 115 |
|[xuwenhao/mactalk-ai-course](https://github.com/xuwenhao/mactalk-ai-course) | 115 |
|[cmooredev/RepoReader](https://github.com/cmooredev/RepoReader) | 115 |
|[abi/autocommit](https://github.com/abi/autocommit) | 115 |
|[MIDORIBIN/langchain-gpt4free](https://github.com/MIDORIBIN/langchain-gpt4free) | 114 |
|[finaldie/auto-news](https://github.com/finaldie/auto-news) | 114 |
|[Anil-matcha/Youtube-to-chatbot](https://github.com/Anil-matcha/Youtube-to-chatbot) | 114 |
|[avrabyt/MemoryBot](https://github.com/avrabyt/MemoryBot) | 114 |
|[Capsize-Games/airunner](https://github.com/Capsize-Games/airunner) | 113 |
|[atisharma/llama_farm](https://github.com/atisharma/llama_farm) | 113 |
|[mbchang/data-driven-characters](https://github.com/mbchang/data-driven-characters) | 112 |
|[fiddler-labs/fiddler-auditor](https://github.com/fiddler-labs/fiddler-auditor) | 112 |
|[dirkjbreeuwer/gpt-automated-web-scraper](https://github.com/dirkjbreeuwer/gpt-automated-web-scraper) | 111 |
|[Appointat/Chat-with-Document-s-using-ChatGPT-API-and-Text-Embedding](https://github.com/Appointat/Chat-with-Document-s-using-ChatGPT-API-and-Text-Embedding) | 111 |
|[hwchase17/langchain-gradio-template](https://github.com/hwchase17/langchain-gradio-template) | 111 |
|[artas728/spelltest](https://github.com/artas728/spelltest) | 110 |
|[NVIDIA/GenerativeAIExamples](https://github.com/NVIDIA/GenerativeAIExamples) | 109 |
|[Azure/aistudio-copilot-sample](https://github.com/Azure/aistudio-copilot-sample) | 108 |
|[codefuse-ai/codefuse-chatbot](https://github.com/codefuse-ai/codefuse-chatbot) | 108 |
|[apirrone/Memento](https://github.com/apirrone/Memento) | 108 |
|[e-johnstonn/GPT-Doc-Summarizer](https://github.com/e-johnstonn/GPT-Doc-Summarizer) | 108 |
|[salesforce/BOLAA](https://github.com/salesforce/BOLAA) | 107 |
|[Erol444/gpt4-openai-api](https://github.com/Erol444/gpt4-openai-api) | 106 |
|[linjungz/chat-with-your-doc](https://github.com/linjungz/chat-with-your-doc) | 106 |
|[crosleythomas/MirrorGPT](https://github.com/crosleythomas/MirrorGPT) | 106 |
|[panaverse/learn-generative-ai](https://github.com/panaverse/learn-generative-ai) | 105 |
|[Azure/azure-sdk-tools](https://github.com/Azure/azure-sdk-tools) | 105 |
|[malywut/gpt_examples](https://github.com/malywut/gpt_examples) | 105 |
|[ritun16/chain-of-verification](https://github.com/ritun16/chain-of-verification) | 104 |
|[langchain-ai/langchain-benchmarks](https://github.com/langchain-ai/langchain-benchmarks) | 104 |
|[lightninglabs/LangChainBitcoin](https://github.com/lightninglabs/LangChainBitcoin) | 104 |
|[flepied/second-brain-agent](https://github.com/flepied/second-brain-agent) | 103 |
|[llmapp/openai.mini](https://github.com/llmapp/openai.mini) | 102 |
|[gimlet-ai/tddGPT](https://github.com/gimlet-ai/tddGPT) | 102 |
|[jlonge4/gpt_chatwithPDF](https://github.com/jlonge4/gpt_chatwithPDF) | 102 |
|[agentification/RAFA_code](https://github.com/agentification/RAFA_code) | 101 |
|[pacman100/DHS-LLM-Workshop](https://github.com/pacman100/DHS-LLM-Workshop) | 101 |
|[aws-samples/private-llm-qa-bot](https://github.com/aws-samples/private-llm-qa-bot) | 101 |


_Generated by [github-dependents-info](https://github.com/nvuillam/github-dependents-info)_

`github-dependents-info --repo "langchain-ai/langchain" --markdownfile dependents.md --minstars 100 --sort stars`
