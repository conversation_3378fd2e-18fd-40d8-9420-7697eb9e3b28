[comment: Please, a reference example here "docs/integrations/arxiv.md"]::
[comment: Use this template to create a new .md file in "docs/integrations/"]::

# Title_REPLACE_ME

[comment: Only one Tile/H1 is allowed!]::

>
[comment: Description: After reading this description, a reader should decide if this integration is good enough to try/follow reading OR]::
[comment: go to read the next integration doc. ]::
[comment: Description should include a link to the source for follow reading.]::

## Installation and Setup

[comment: Installation and Setup: All necessary additional package installations and setups for Tokens, etc]::

```bash
pip install package_name_REPLACE_ME
```

[comment: OR this text:]::

There isn't any special setup for it.

[comment: The next H2/## sections with names of the integration modules, like "LLM", "Text Embedding Models", etc]::
[comment: see "Modules" in the "index.html" page]::
[comment: Each H2 section should include a link to an example(s) and a Python code with the import of the integration class]::
[comment: Below are several example sections. Remove all unnecessary sections. Add all necessary sections not provided here.]::

## LLM

See a [usage example](/docs/integrations/llms/INCLUDE_REAL_NAME).

```python
from langchain_community.llms import integration_class_REPLACE_ME
```

## Text Embedding Models

See a [usage example](/docs/integrations/text_embedding/INCLUDE_REAL_NAME).

```python
from langchain_community.embeddings import integration_class_REPLACE_ME
```

## Chat models

See a [usage example](/docs/integrations/chat/INCLUDE_REAL_NAME).

```python
from langchain_community.chat_models import integration_class_REPLACE_ME
```

## Document Loader

See a [usage example](/docs/integrations/document_loaders/INCLUDE_REAL_NAME).

```python
from langchain_community.document_loaders import integration_class_REPLACE_ME
```
