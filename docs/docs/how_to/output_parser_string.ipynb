{"cells": [{"cell_type": "markdown", "id": "1d6024e0-3847-4418-b8a8-6b8f83adf4c2", "metadata": {}, "source": ["# How to parse text from message objects\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [Chat models](/docs/concepts/chat_models/)\n", "- [Messages](/docs/concepts/messages/)\n", "- [Output parsers](/docs/concepts/output_parsers/)\n", "- [LangChain Expression Language (LCEL)](/docs/concepts/lcel/)\n", "\n", ":::\n", "\n", "<PERSON><PERSON><PERSON><PERSON> [message](/docs/concepts/messages/) objects support content in a [variety of formats](/docs/concepts/messages/#content), including text, [multimodal data](/docs/concepts/multimodality/), and a list of [content block](/docs/concepts/messages/#aimessage) dicts.\n", "\n", "The format of [Chat model](/docs/concepts/chat_models/) response content may depend on the provider. For example, the chat model for [Anthropic](/docs/integrations/chat/anthropic/) will return string content for typical string input:"]}, {"cell_type": "code", "execution_count": 1, "id": "8ac74999-0740-4178-8efd-32a855592f71", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hi there! How are you doing today? Is there anything I can help you with?'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_anthropic import ChatAnthropic\n", "\n", "llm = ChatAnthropic(model=\"claude-3-5-haiku-latest\")\n", "\n", "response = llm.invoke(\"Hello\")\n", "response.content"]}, {"cell_type": "markdown", "id": "69b7c3ae-0022-4737-9db7-f44db3402de2", "metadata": {}, "source": ["But when tool calls are generated, the response content is structured into content blocks that convey the model's reasoning process:"]}, {"cell_type": "code", "execution_count": 2, "id": "8c87553e-4f85-46c4-8f1e-666f6a261a50", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'text': \"I'll help you get the current weather for San Francisco, California. Let me check that for you right away.\",\n", "  'type': 'text'},\n", " {'id': 'toolu_015PwwcKxWYctKfY3pruHFyy',\n", "  'input': {'location': 'San Francisco, CA'},\n", "  'name': 'get_weather',\n", "  'type': 'tool_use'}]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def get_weather(location: str) -> str:\n", "    \"\"\"Get the weather from a location.\"\"\"\n", "\n", "    return \"<PERSON>.\"\n", "\n", "\n", "llm_with_tools = llm.bind_tools([get_weather])\n", "\n", "response = llm_with_tools.invoke(\"What's the weather in San Francisco, CA?\")\n", "response.content"]}, {"cell_type": "markdown", "id": "039f6d62-098f-42c9-8b07-43cb1f2a831b", "metadata": {}, "source": ["To automatically parse text from message objects irrespective of the format of the underlying content, we can use [StrOutputParser](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.string.StrOutputParser.html). We can compose it with a chat model as follows:"]}, {"cell_type": "code", "execution_count": 3, "id": "0bb9b4dd-64a9-463d-9c71-df147630f3c3", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "\n", "chain = llm_with_tools | StrOutputParser()"]}, {"cell_type": "markdown", "id": "4929c724-471f-4f77-a231-36e9af9418a3", "metadata": {}, "source": ["[StrOutputParser](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.string.StrOutputParser.html) simplifies the extraction of text from message objects:"]}, {"cell_type": "code", "execution_count": 4, "id": "9cbb8848-9101-465e-b230-0f7af6fb4105", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I'll help you check the weather in San Francisco, CA right away.\n"]}], "source": ["response = chain.invoke(\"What's the weather in San Francisco, CA?\")\n", "print(response)"]}, {"cell_type": "markdown", "id": "13642ad5-325d-4d9b-b97e-cac40345bfbc", "metadata": {}, "source": ["This is particularly useful in streaming contexts:"]}, {"cell_type": "code", "execution_count": 5, "id": "28eeace3-3896-497f-93ad-544cbfb7f15c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|I'll| help| you get| the current| weather for| San Francisco, California|. Let| me retrieve| that| information for you.||||||||||"]}], "source": ["for chunk in chain.stream(\"What's the weather in San Francisco, CA?\"):\n", "    print(chunk, end=\"|\")"]}, {"cell_type": "markdown", "id": "858e2071-a483-404e-9eca-c73a4466fd83", "metadata": {}, "source": ["See the [API Reference](https://python.langchain.com/api_reference/core/output_parsers/langchain_core.output_parsers.string.StrOutputParser.html) for more information."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}