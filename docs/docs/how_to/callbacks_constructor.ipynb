{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to propagate callbacks  constructor\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Callbacks](/docs/concepts/callbacks)\n", "- [Custom callback handlers](/docs/how_to/custom_callbacks)\n", "\n", ":::\n", "\n", "Most LangChain modules allow you to pass `callbacks` directly into the constructor (i.e., initializer). In this case, the callbacks will only be called for that instance (and any nested runs).\n", "\n", ":::warning\n", "Constructor callbacks are scoped only to the object they are defined on. They are **not** inherited by children of the object. This can lead to confusing behavior,\n", "and it's generally better to pass callbacks as a run time argument.\n", ":::\n", "\n", "Here's an example:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "%pip install -qU langchain langchain_anthropic\n", "\n", "import getpass\n", "import os\n", "\n", "os.environ[\"ANTHROPIC_API_KEY\"] = getpass.getpass()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chat model started\n", "Chat model ended, response: generations=[[ChatGeneration(text='1 + 2 = 3', message=AIMessage(content='1 + 2 = 3', additional_kwargs={}, response_metadata={'id': 'msg_01DQMbSk263KpY2vouHM5gsC', 'model': 'claude-3-7-sonnet-20250219', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'cache_creation_input_tokens': 0, 'cache_read_input_tokens': 0, 'input_tokens': 16, 'output_tokens': 13, 'server_tool_use': None, 'service_tier': 'standard'}, 'model_name': 'claude-3-7-sonnet-20250219'}, id='run--ab896e4e-c3fd-48b1-a41a-b6b525cbc041-0', usage_metadata={'input_tokens': 16, 'output_tokens': 13, 'total_tokens': 29, 'input_token_details': {'cache_read': 0, 'cache_creation': 0}}))]] llm_output={'id': 'msg_01DQMbSk263KpY2vouHM5gsC', 'model': 'claude-3-7-sonnet-20250219', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'cache_creation_input_tokens': 0, 'cache_read_input_tokens': 0, 'input_tokens': 16, 'output_tokens': 13, 'server_tool_use': None, 'service_tier': 'standard'}, 'model_name': 'claude-3-7-sonnet-20250219'} run=None type='LLMResult'\n"]}, {"data": {"text/plain": ["AIMessage(content='1 + 2 = 3', additional_kwargs={}, response_metadata={'id': 'msg_01DQMbSk263KpY2vouHM5gsC', 'model': 'claude-3-7-sonnet-20250219', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'cache_creation_input_tokens': 0, 'cache_read_input_tokens': 0, 'input_tokens': 16, 'output_tokens': 13, 'server_tool_use': None, 'service_tier': 'standard'}, 'model_name': 'claude-3-7-sonnet-20250219'}, id='run--ab896e4e-c3fd-48b1-a41a-b6b525cbc041-0', usage_metadata={'input_tokens': 16, 'output_tokens': 13, 'total_tokens': 29, 'input_token_details': {'cache_read': 0, 'cache_creation': 0}})"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Any, Dict, List\n", "\n", "from langchain_anthropic import ChatAnthropic\n", "from langchain_core.callbacks import BaseCallbackHandler\n", "from langchain_core.messages import BaseMessage\n", "from langchain_core.outputs import LLMResult\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "\n", "class LoggingHandler(BaseCallbackHandler):\n", "    def on_chat_model_start(\n", "        self, serialized: Dict[str, Any], messages: List[List[BaseMessage]], **kwargs\n", "    ) -> None:\n", "        print(\"Chat model started\")\n", "\n", "    def on_llm_end(self, response: LLMResult, **kwargs) -> None:\n", "        print(f\"Chat model ended, response: {response}\")\n", "\n", "    def on_chain_start(\n", "        self, serialized: Dict[str, Any], inputs: Dict[str, Any], **kwargs\n", "    ) -> None:\n", "        print(f\"Chain {serialized.get('name')} started\")\n", "\n", "    def on_chain_end(self, outputs: Dict[str, Any], **kwargs) -> None:\n", "        print(f\"Chain ended, outputs: {outputs}\")\n", "\n", "\n", "callbacks = [Lo<PERSON><PERSON><PERSON><PERSON>()]\n", "llm = ChatAnthropic(model=\"claude-3-7-sonnet-20250219\", callbacks=callbacks)\n", "prompt = ChatPromptTemplate.from_template(\"What is 1 + {number}?\")\n", "\n", "chain = prompt | llm\n", "\n", "chain.invoke({\"number\": \"2\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can see that we only see events from the chat model run - no chain events from the prompt or broader chain.\n", "\n", "## Next steps\n", "\n", "You've now learned how to pass callbacks into a constructor.\n", "\n", "Next, check out the other how-to guides in this section, such as how to [pass callbacks at runtime](/docs/how_to/callbacks_runtime)."]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}