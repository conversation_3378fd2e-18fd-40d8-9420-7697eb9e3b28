{"cells": [{"cell_type": "markdown", "id": "612eac0a", "metadata": {}, "source": ["# How to do retrieval with contextual compression\n", "\n", "One challenge with [retrieval](/docs/concepts/retrieval/) is that usually you don't know the specific queries your document storage system will face when you ingest data into the system. This means that the information most relevant to a query may be buried in a document with a lot of irrelevant text. Passing that full document through your application can lead to more expensive LLM calls and poorer responses.\n", "\n", "Contextual compression is meant to fix this. The idea is simple: instead of immediately returning retrieved documents as-is, you can compress them using the context of the given query, so that only the relevant information is returned. “Compressing” here refers to both compressing the contents of an individual document and filtering out documents wholesale.\n", "\n", "To use the Contextual Compression Retriever, you'll need:\n", "\n", "- a base [retriever](/docs/concepts/retrievers/)\n", "- a Document Compressor\n", "\n", "The Contextual Compression Retriever passes queries to the base retriever, takes the initial documents and passes them through the Document Compressor. The Document Compressor takes a list of documents and shortens it by reducing the contents of documents or dropping documents altogether.\n", "\n", "## Get started"]}, {"cell_type": "code", "execution_count": 1, "id": "e0029369", "metadata": {}, "outputs": [], "source": ["# Helper function for printing docs\n", "\n", "\n", "def pretty_print_docs(docs):\n", "    print(\n", "        f\"\\n{'-' * 100}\\n\".join(\n", "            [f\"Document {i + 1}:\\n\\n\" + d.page_content for i, d in enumerate(docs)]\n", "        )\n", "    )"]}, {"cell_type": "markdown", "id": "9d2360fc", "metadata": {}, "source": ["## Using a vanilla vector store retriever\n", "Let's start by initializing a simple vector store retriever and storing the 2023 State of the Union speech (in chunks). We can see that given an example question our retriever returns one or two relevant docs and a few irrelevant docs. And even the relevant docs have a lot of irrelevant information in them.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "25c26947-958d-4219-8ca0-daa3a51bd344", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document 1:\n", "\n", "Tonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \n", "\n", "Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service. \n", "\n", "One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \n", "\n", "And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \n", "\n", "And if we are to advance liberty and justice, we need to secure the Border and fix the immigration system. \n", "\n", "We can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.  \n", "\n", "We’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.  \n", "\n", "We’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster. \n", "\n", "We’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders.\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "And for our LGBTQ+ Americans, let’s finally get the bipartisan Equality Act to my desk. The onslaught of state laws targeting transgender Americans and their families is wrong. \n", "\n", "As I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \n", "\n", "While it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice. \n", "\n", "And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \n", "\n", "So tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \n", "\n", "First, beat the opioid epidemic.\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "Tonight, I’m announcing a crackdown on these companies overcharging American businesses and consumers. \n", "\n", "And as Wall Street firms take over more nursing homes, quality in those homes has gone down and costs have gone up.  \n", "\n", "That ends on my watch. \n", "\n", "Medicare is going to set higher standards for nursing homes and make sure your loved ones get the care they deserve and expect. \n", "\n", "We’ll also cut costs and keep the economy going strong by giving workers a fair shot, provide more training and apprenticeships, hire them based on their skills not degrees. \n", "\n", "Let’s pass the Paycheck Fairness Act and paid leave.  \n", "\n", "Raise the minimum wage to $15 an hour and extend the Child Tax Credit, so no one has to raise a family in poverty. \n", "\n", "Let’s increase Pell Grants and increase our historic support of HBCUs, and invest in what <PERSON>—our First Lady who teaches full-time—calls America’s best-kept secret: community colleges.\n"]}], "source": ["from langchain_community.document_loaders import TextLoader\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import CharacterTextSplitter\n", "\n", "documents = TextLoader(\"state_of_the_union.txt\").load()\n", "text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)\n", "texts = text_splitter.split_documents(documents)\n", "retriever = FAISS.from_documents(texts, OpenAIEmbeddings()).as_retriever()\n", "\n", "docs = retriever.invoke(\"What did the president say about <PERSON><PERSON><PERSON>\")\n", "pretty_print_docs(docs)"]}, {"cell_type": "markdown", "id": "3473c553", "metadata": {}, "source": ["## Adding contextual compression with an `LLMChainExtractor`\n", "Now let's wrap our base retriever with a `ContextualCompressionRetriever`. We'll add an `LLMChainExtractor`, which will iterate over the initially returned documents and extract from each only the content that is relevant to the query.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "d83e3c63-bcde-43e9-998e-35bf2ebef49b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document 1:\n", "\n", "I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>.\n"]}], "source": ["from langchain.retrievers import ContextualCompressionRetriever\n", "from langchain.retrievers.document_compressors import LLMChainExtractor\n", "from langchain_openai import OpenAI\n", "\n", "llm = OpenAI(temperature=0)\n", "compressor = LLMChainExtractor.from_llm(llm)\n", "compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=compressor, base_retriever=retriever\n", ")\n", "\n", "compressed_docs = compression_retriever.invoke(\n", "    \"What did the president say about <PERSON><PERSON><PERSON>\"\n", ")\n", "pretty_print_docs(compressed_docs)"]}, {"cell_type": "markdown", "id": "8a97cd9b", "metadata": {}, "source": ["## More built-in compressors: filters\n", "### `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>`\n", "The `LLMChainFilter` is slightly simpler but more robust compressor that uses an LLM chain to decide which of the initially retrieved documents to filter out and which ones to return, without manipulating the document contents.\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "39b13654-01d9-4006-9550-5f3e77cb4f23", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document 1:\n", "\n", "Tonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \n", "\n", "Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service. \n", "\n", "One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \n", "\n", "And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.\n"]}], "source": ["from langchain.retrievers.document_compressors import LLMChainFilter\n", "\n", "_filter = LLMChainFilter.from_llm(llm)\n", "compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=_filter, base_retriever=retriever\n", ")\n", "\n", "compressed_docs = compression_retriever.invoke(\n", "    \"What did the president say about <PERSON><PERSON><PERSON>\"\n", ")\n", "pretty_print_docs(compressed_docs)"]}, {"cell_type": "markdown", "id": "14002ec8-7ee5-4f91-9315-dd21c3808776", "metadata": {}, "source": ["### `LLMListwiseRerank`\n", "\n", "[LLMListwiseRerank](https://python.langchain.com/api_reference/langchain/retrievers/langchain.retrievers.document_compressors.listwise_rerank.LLMListwiseRerank.html) uses [zero-shot listwise document reranking](https://arxiv.org/pdf/2305.02156) and functions similarly to `LLMChainFilter` as a robust but more expensive option. It is recommended to use a more powerful LLM.\n", "\n", "Note that `LLMListwiseRerank` requires a model with the [with_structured_output](/docs/integrations/chat/) method implemented."]}, {"cell_type": "code", "execution_count": 6, "id": "4ab9ee9f-917e-4d6f-9344-eb7f01533228", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document 1:\n", "\n", "Tonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \n", "\n", "Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service. \n", "\n", "One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \n", "\n", "And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.\n"]}], "source": ["from langchain.retrievers.document_compressors import LLMListwiseRerank\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "\n", "_filter = LLMListwiseRerank.from_llm(llm, top_n=1)\n", "compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=_filter, base_retriever=retriever\n", ")\n", "\n", "compressed_docs = compression_retriever.invoke(\n", "    \"What did the president say about <PERSON><PERSON><PERSON>\"\n", ")\n", "pretty_print_docs(compressed_docs)"]}, {"cell_type": "markdown", "id": "7194da42", "metadata": {}, "source": ["### `EmbeddingsFilter`\n", "\n", "Making an extra LLM call over each retrieved document is expensive and slow. The `EmbeddingsFilter` provides a cheaper and faster option by embedding the documents and query and only returning those documents which have sufficiently similar embeddings to the query.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "ee8d9486-db9a-4e24-aa11-ae40f34cc908", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document 1:\n", "\n", "Tonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \n", "\n", "Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service. \n", "\n", "One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \n", "\n", "And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \n", "\n", "And if we are to advance liberty and justice, we need to secure the Border and fix the immigration system. \n", "\n", "We can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.  \n", "\n", "We’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.  \n", "\n", "We’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster. \n", "\n", "We’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders.\n"]}], "source": ["from langchain.retrievers.document_compressors import EmbeddingsFilter\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "embeddings = OpenAIEmbeddings()\n", "embeddings_filter = EmbeddingsFilter(embeddings=embeddings, similarity_threshold=0.76)\n", "compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=embeddings_filter, base_retriever=retriever\n", ")\n", "\n", "compressed_docs = compression_retriever.invoke(\n", "    \"What did the president say about <PERSON><PERSON><PERSON>\"\n", ")\n", "pretty_print_docs(compressed_docs)"]}, {"cell_type": "markdown", "id": "2074462b", "metadata": {}, "source": ["## Stringing compressors and document transformers together\n", "Using the `DocumentCompressorPipeline` we can also easily combine multiple compressors in sequence. Along with compressors we can add `BaseDocumentTransformer`s to our pipeline, which don't perform any contextual compression but simply perform some transformation on a set of documents. For example `TextSplitter`s can be used as document transformers to split documents into smaller pieces, and the `EmbeddingsRedundantFilter` can be used to filter out redundant documents based on embedding similarity between documents.\n", "\n", "Below we create a compressor pipeline by first splitting our docs into smaller chunks, then removing redundant documents, and then filtering based on relevance to the query.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "617a1756", "metadata": {}, "outputs": [], "source": ["from langchain.retrievers.document_compressors import DocumentCompressorPipeline\n", "from langchain_community.document_transformers import EmbeddingsRedundantFilter\n", "from langchain_text_splitters import CharacterTextSplitter\n", "\n", "splitter = CharacterTextSplitter(chunk_size=300, chunk_overlap=0, separator=\". \")\n", "redundant_filter = EmbeddingsRedundantFilter(embeddings=embeddings)\n", "relevant_filter = EmbeddingsFilter(embeddings=embeddings, similarity_threshold=0.76)\n", "pipeline_compressor = DocumentCompressorPipeline(\n", "    transformers=[splitter, redundant_filter, relevant_filter]\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "40b9c1db-7ac2-4257-935a-b107da50bb43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document 1:\n", "\n", "One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \n", "\n", "And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "As I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \n", "\n", "While it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \n", "\n", "And if we are to advance liberty and justice, we need to secure the Border and fix the immigration system. \n", "\n", "We can do both\n"]}], "source": ["compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=pipeline_compressor, base_retriever=retriever\n", ")\n", "\n", "compressed_docs = compression_retriever.invoke(\n", "    \"What did the president say about <PERSON><PERSON><PERSON>\"\n", ")\n", "pretty_print_docs(compressed_docs)"]}, {"cell_type": "code", "execution_count": null, "id": "78581dcb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}