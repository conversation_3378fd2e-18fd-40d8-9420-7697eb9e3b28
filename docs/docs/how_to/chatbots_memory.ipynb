{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_position: 1\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to add memory to chatbots\n", "\n", "A key feature of chatbots is their ability to use the content of previous conversational turns as context. This state management can take several forms, including:\n", "\n", "- Simply stuffing previous messages into a chat model prompt.\n", "- The above, but trimming old messages to reduce the amount of distracting information the model has to deal with.\n", "- More complex modifications like synthesizing summaries for long running conversations.\n", "\n", "We'll go into more detail on a few techniques below!\n", "\n", ":::note\n", "\n", "This how-to guide previously built a chatbot using [RunnableWithMessageHistory](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.history.RunnableWithMessageHistory.html). You can access this version of the guide in the [v0.2 docs](https://python.langchain.com/v0.2/docs/how_to/chatbots_memory/).\n", "\n", "As of the v0.3 release of LangChain, we recommend that LangChain users take advantage of [LangGraph persistence](https://langchain-ai.github.io/langgraph/concepts/persistence/) to incorporate `memory` into new LangChain applications.\n", "\n", "If your code is already relying on `RunnableWithMessageHistory` or `BaseChatMessageHistory`, you do **not** need to make any changes. We do not plan on deprecating this functionality in the near future as it works for simple chat applications and any code that uses `RunnableWithMessageHistory` will continue to work as expected.\n", "\n", "Please see [How to migrate to LangGraph Memory](/docs/versions/migrating_memory/) for more details.\n", ":::\n", "\n", "## Setup\n", "\n", "You'll need to install a few packages, and have your OpenAI API key set as an environment variable named `OPENAI_API_KEY`:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": ["OpenAI API Key: ········\n"]}], "source": ["%pip install --upgrade --quiet langchain langchain-openai langgraph\n", "\n", "import getpass\n", "import os\n", "\n", "if not os.environ.get(\"OPENAI_API_KEY\"):\n", "    os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"OpenAI API Key:\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's also set up a chat model that we'll use for the below examples."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"gpt-4o-mini\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Message passing\n", "\n", "The simplest form of memory is simply passing chat history messages into a chain. Here's an example:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I said, \"I love programming\" in French: \"J'adore la programmation.\"\n"]}], "source": ["from langchain_core.messages import AIMessage, HumanMessage, SystemMessage\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        SystemMessage(\n", "            content=\"You are a helpful assistant. Answer all questions to the best of your ability.\"\n", "        ),\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | model\n", "\n", "ai_msg = chain.invoke(\n", "    {\n", "        \"messages\": [\n", "            HumanMessage(\n", "                content=\"Translate from English to French: I love programming.\"\n", "            ),\n", "            AIMessage(content=\"J'adore la programmation.\"),\n", "            HumanMessage(content=\"What did you just say?\"),\n", "        ],\n", "    }\n", ")\n", "print(ai_msg.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that by passing the previous conversation into a chain, it can use it as context to answer questions. This is the basic concept underpinning chatbot memory - the rest of the guide will demonstrate convenient techniques for passing or reformatting messages."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Automatic history management\n", "\n", "The previous examples pass messages to the chain (and model) explicitly. This is a completely acceptable approach, but it does require external management of new messages. LangChain also provides a way to build applications that have memory using LangGraph's [persistence](https://langchain-ai.github.io/langgraph/concepts/persistence/). You can [enable persistence](https://langchain-ai.github.io/langgraph/how-tos/persistence/) in LangGraph applications by providing a `checkpointer` when compiling the graph."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import START, MessagesState, StateGraph\n", "\n", "workflow = StateGraph(state_schema=MessagesState)\n", "\n", "\n", "# Define the function that calls the model\n", "def call_model(state: MessagesState):\n", "    system_prompt = (\n", "        \"You are a helpful assistant. Answer all questions to the best of your ability.\"\n", "    )\n", "    messages = [SystemMessage(content=system_prompt)] + state[\"messages\"]\n", "    response = model.invoke(messages)\n", "    return {\"messages\": response}\n", "\n", "\n", "# Define the node and edge\n", "workflow.add_node(\"model\", call_model)\n", "workflow.add_edge(START, \"model\")\n", "\n", "# Add simple in-memory checkpointer\n", "# highlight-start\n", "memory = MemorySaver()\n", "app = workflow.compile(checkpointer=memory)\n", "# highlight-end"]}, {"cell_type": "markdown", "metadata": {}, "source": [" We'll pass the latest input to the conversation here and let <PERSON><PERSON><PERSON><PERSON> keep track of the conversation history using the checkpointer:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Translate to French: I love programming.', additional_kwargs={}, response_metadata={}, id='be5e7099-3149-4293-af49-6b36c8ccd71b'),\n", "  AIMessage(content=\"J'aime programmer.\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 4, 'prompt_tokens': 35, 'total_tokens': 39, 'completion_tokens_details': {'reasoning_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_e9627b5346', 'finish_reason': 'stop', 'logprobs': None}, id='run-8a753d7a-b97b-4d01-a661-626be6f41b38-0', usage_metadata={'input_tokens': 35, 'output_tokens': 4, 'total_tokens': 39})]}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke(\n", "    {\"messages\": [HumanMessage(content=\"Translate to French: I love programming.\")]},\n", "    config={\"configurable\": {\"thread_id\": \"1\"}},\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Translate to French: I love programming.', additional_kwargs={}, response_metadata={}, id='be5e7099-3149-4293-af49-6b36c8ccd71b'),\n", "  AIMessage(content=\"J'aime programmer.\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 4, 'prompt_tokens': 35, 'total_tokens': 39, 'completion_tokens_details': {'reasoning_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_e9627b5346', 'finish_reason': 'stop', 'logprobs': None}, id='run-8a753d7a-b97b-4d01-a661-626be6f41b38-0', usage_metadata={'input_tokens': 35, 'output_tokens': 4, 'total_tokens': 39}),\n", "  HumanMessage(content='What did I just ask you?', additional_kwargs={}, response_metadata={}, id='c667529b-7c41-4cc0-9326-0af47328b816'),\n", "  AIMessage(content='You asked me to translate \"I love programming\" into French.', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 13, 'prompt_tokens': 54, 'total_tokens': 67, 'completion_tokens_details': {'reasoning_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_1bb46167f9', 'finish_reason': 'stop', 'logprobs': None}, id='run-134a7ea0-d3a4-4923-bd58-25e5a43f6a1f-0', usage_metadata={'input_tokens': 54, 'output_tokens': 13, 'total_tokens': 67})]}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke(\n", "    {\"messages\": [HumanMessage(content=\"What did I just ask you?\")]},\n", "    config={\"configurable\": {\"thread_id\": \"1\"}},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Modifying chat history\n", "\n", "Modifying stored chat messages can help your chatbot handle a variety of situations. Here are some examples:\n", "\n", "### Trimming messages\n", "\n", "LLMs and chat models have limited context windows, and even if you're not directly hitting limits, you may want to limit the amount of distraction the model has to deal with. One solution is trim the history messages before passing them to the model. Let's use an example history with the `app` we declared above:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content=\"Hey there! I'm N<PERSON><PERSON>.\", additional_kwargs={}, response_metadata={}, id='6b4cab70-ce18-49b0-bb06-267bde44e037'),\n", "  AIMessage(content='Hello!', additional_kwargs={}, response_metadata={}, id='ba3714f4-8876-440b-a651-efdcab2fcb4c'),\n", "  HumanMessage(content='How are you today?', additional_kwargs={}, response_metadata={}, id='08d032c0-1577-4862-a3f2-5c1b90687e21'),\n", "  AIMessage(content='Fine thanks!', additional_kwargs={}, response_metadata={}, id='21790e16-db05-4537-9a6b-ecad0fcec436'),\n", "  HumanMessage(content=\"What's my name?\", additional_kwargs={}, response_metadata={}, id='c933eca3-5fd8-4651-af16-20fe2d49c216'),\n", "  AIMessage(content='Your name is Nemo.', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 5, 'prompt_tokens': 63, 'total_tokens': 68, 'completion_tokens_details': {'reasoning_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_1bb46167f9', 'finish_reason': 'stop', 'logprobs': None}, id='run-a0b21acc-9dbb-4fb6-a953-392020f37d88-0', usage_metadata={'input_tokens': 63, 'output_tokens': 5, 'total_tokens': 68})]}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["demo_ephemeral_chat_history = [\n", "    HumanMessage(content=\"Hey there! I'm N<PERSON><PERSON>.\"),\n", "    AIMessage(content=\"Hello!\"),\n", "    HumanMessage(content=\"How are you today?\"),\n", "    AIMessage(content=\"Fine thanks!\"),\n", "]\n", "\n", "app.invoke(\n", "    {\n", "        \"messages\": demo_ephemeral_chat_history\n", "        + [HumanMessage(content=\"What's my name?\")]\n", "    },\n", "    config={\"configurable\": {\"thread_id\": \"2\"}},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see the app remembers the preloaded name.\n", "\n", "But let's say we have a very small context window, and we want to trim the number of messages passed to the model to only the 2 most recent ones. We can use the built in [trim_messages](/docs/how_to/trim_messages/) util to trim messages based on their token count before they reach our prompt. In this case we'll count each message as 1 \"token\" and keep only the last two messages:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import trim_messages\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import START, MessagesState, StateGraph\n", "\n", "# Define trimmer\n", "# highlight-start\n", "# count each message as 1 \"token\" (token_counter=len) and keep only the last two messages\n", "trimmer = trim_messages(strategy=\"last\", max_tokens=2, token_counter=len)\n", "# highlight-end\n", "\n", "workflow = StateGraph(state_schema=MessagesState)\n", "\n", "\n", "# Define the function that calls the model\n", "def call_model(state: MessagesState):\n", "    # highlight-start\n", "    trimmed_messages = trimmer.invoke(state[\"messages\"])\n", "    system_prompt = (\n", "        \"You are a helpful assistant. Answer all questions to the best of your ability.\"\n", "    )\n", "    messages = [SystemMessage(content=system_prompt)] + trimmed_messages\n", "    # highlight-end\n", "    response = model.invoke(messages)\n", "    return {\"messages\": response}\n", "\n", "\n", "# Define the node and edge\n", "workflow.add_node(\"model\", call_model)\n", "workflow.add_edge(START, \"model\")\n", "\n", "# Add simple in-memory checkpointer\n", "memory = MemorySaver()\n", "app = workflow.compile(checkpointer=memory)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's call this new app and check the response"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content=\"Hey there! I'm N<PERSON><PERSON>.\", additional_kwargs={}, response_metadata={}, id='6b4cab70-ce18-49b0-bb06-267bde44e037'),\n", "  AIMessage(content='Hello!', additional_kwargs={}, response_metadata={}, id='ba3714f4-8876-440b-a651-efdcab2fcb4c'),\n", "  HumanMessage(content='How are you today?', additional_kwargs={}, response_metadata={}, id='08d032c0-1577-4862-a3f2-5c1b90687e21'),\n", "  AIMessage(content='Fine thanks!', additional_kwargs={}, response_metadata={}, id='21790e16-db05-4537-9a6b-ecad0fcec436'),\n", "  HumanMessage(content='What is my name?', additional_kwargs={}, response_metadata={}, id='a22ab7c5-8617-4821-b3e9-a9e7dca1ff78'),\n", "  AIMessage(content=\"I'm sorry, but I don't have access to personal information about you unless you share it with me. How can I assist you today?\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 27, 'prompt_tokens': 39, 'total_tokens': 66, 'completion_tokens_details': {'reasoning_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_1bb46167f9', 'finish_reason': 'stop', 'logprobs': None}, id='run-f7b32d72-9f57-4705-be7e-43bf1c3d293b-0', usage_metadata={'input_tokens': 39, 'output_tokens': 27, 'total_tokens': 66})]}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke(\n", "    {\n", "        \"messages\": demo_ephemeral_chat_history\n", "        + [HumanMessage(content=\"What is my name?\")]\n", "    },\n", "    config={\"configurable\": {\"thread_id\": \"3\"}},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that `trim_messages` was called and only the two most recent messages will be passed to the model. In this case, this means that the model forgot the name we gave it."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Check out our [how to guide on trimming messages](/docs/how_to/trim_messages/) for more."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary memory\n", "\n", "We can use this same pattern in other ways too. For example, we could use an additional LLM call to generate a summary of the conversation before calling our app. Let's recreate our chat history:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["demo_ephemeral_chat_history = [\n", "    HumanMessage(content=\"Hey there! I'm N<PERSON><PERSON>.\"),\n", "    AIMessage(content=\"Hello!\"),\n", "    HumanMessage(content=\"How are you today?\"),\n", "    AIMessage(content=\"Fine thanks!\"),\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And now, let's update the model-calling function to distill previous interactions into a summary:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage, RemoveMessage\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import START, MessagesState, StateGraph\n", "\n", "workflow = StateGraph(state_schema=MessagesState)\n", "\n", "\n", "# Define the function that calls the model\n", "def call_model(state: MessagesState):\n", "    system_prompt = (\n", "        \"You are a helpful assistant. \"\n", "        \"Answer all questions to the best of your ability. \"\n", "        \"The provided chat history includes a summary of the earlier conversation.\"\n", "    )\n", "    system_message = SystemMessage(content=system_prompt)\n", "    message_history = state[\"messages\"][:-1]  # exclude the most recent user input\n", "    # Summarize the messages if the chat history reaches a certain size\n", "    if len(message_history) >= 4:\n", "        last_human_message = state[\"messages\"][-1]\n", "        # Invoke the model to generate conversation summary\n", "        summary_prompt = (\n", "            \"Distill the above chat messages into a single summary message. \"\n", "            \"Include as many specific details as you can.\"\n", "        )\n", "        summary_message = model.invoke(\n", "            message_history + [HumanMessage(content=summary_prompt)]\n", "        )\n", "\n", "        # Delete messages that we no longer want to show up\n", "        delete_messages = [RemoveMessage(id=m.id) for m in state[\"messages\"]]\n", "        # Re-add user message\n", "        human_message = HumanMessage(content=last_human_message.content)\n", "        # Call the model with summary & response\n", "        response = model.invoke([system_message, summary_message, human_message])\n", "        message_updates = [summary_message, human_message, response] + delete_messages\n", "    else:\n", "        message_updates = model.invoke([system_message] + state[\"messages\"])\n", "\n", "    return {\"messages\": message_updates}\n", "\n", "\n", "# Define the node and edge\n", "workflow.add_node(\"model\", call_model)\n", "workflow.add_edge(START, \"model\")\n", "\n", "# Add simple in-memory checkpointer\n", "memory = MemorySaver()\n", "app = workflow.compile(checkpointer=memory)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see if it remembers the name we gave it:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [AIMessage(content=\"<PERSON><PERSON><PERSON> greeted me, and I responded positively, indicating that I'm doing well.\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 16, 'prompt_tokens': 60, 'total_tokens': 76, 'completion_tokens_details': {'reasoning_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_1bb46167f9', 'finish_reason': 'stop', 'logprobs': None}, id='run-ee42f98d-907d-4bad-8f16-af2db789701d-0', usage_metadata={'input_tokens': 60, 'output_tokens': 16, 'total_tokens': 76}),\n", "  HumanMessage(content='What did I say my name was?', additional_kwargs={}, response_metadata={}, id='788555ea-5b1f-4c29-a2f2-a92f15d147be'),\n", "  AIMessage(content='You mentioned that your name is <PERSON><PERSON><PERSON>.', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 8, 'prompt_tokens': 67, 'total_tokens': 75, 'completion_tokens_details': {'reasoning_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_1bb46167f9', 'finish_reason': 'stop', 'logprobs': None}, id='run-099a43bd-a284-4969-bb6f-0be486614cd8-0', usage_metadata={'input_tokens': 67, 'output_tokens': 8, 'total_tokens': 75})]}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke(\n", "    {\n", "        \"messages\": demo_ephemeral_chat_history\n", "        + [HumanMessage(\"What did I say my name was?\")]\n", "    },\n", "    config={\"configurable\": {\"thread_id\": \"4\"}},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that invoking the app again will keep accumulating the history until it reaches the specified number of messages (four in our case). At that point we will generate another summary generated from the initial summary plus new messages and so on."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}