{"cells": [{"cell_type": "markdown", "id": "0fee7096", "metadata": {}, "source": ["# How to use the output-fixing parser\n", "\n", "This [output parser](/docs/concepts/output_parsers/) wraps another output parser, and in the event that the first one fails it calls out to another LLM to fix any errors.\n", "\n", "But we can do other things besides throw errors. Specifically, we can pass the misformatted output, along with the formatted instructions, to the model and ask it to fix it.\n", "\n", "For this example, we'll use the above Pydantic output parser. Here's what happens if we pass it a result that does not comply with the schema:"]}, {"cell_type": "code", "execution_count": 1, "id": "9bad594d", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from langchain_core.exceptions import OutputParserException\n", "from langchain_core.output_parsers import PydanticOutputParser\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field"]}, {"cell_type": "code", "execution_count": 2, "id": "15283e0b", "metadata": {}, "outputs": [], "source": ["class Actor(BaseModel):\n", "    name: str = <PERSON>(description=\"name of an actor\")\n", "    film_names: List[str] = Field(description=\"list of names of films they starred in\")\n", "\n", "\n", "actor_query = \"Generate the filmography for a random actor.\"\n", "\n", "parser = PydanticOutputParser(pydantic_object=Actor)"]}, {"cell_type": "code", "execution_count": 3, "id": "072d2d4c", "metadata": {}, "outputs": [], "source": ["misformatted = \"{'name': '<PERSON>', 'film_names': ['<PERSON>']}\""]}, {"cell_type": "code", "execution_count": 4, "id": "4cbb35b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Invalid json output: {'name': '<PERSON>', 'film_names': ['<PERSON>']}\n", "For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/OUTPUT_PARSING_FAILURE\n"]}], "source": ["try:\n", "    parser.parse(misformatted)\n", "except OutputParserException as e:\n", "    print(e)"]}, {"cell_type": "markdown", "id": "723c559d", "metadata": {}, "source": ["Now we can construct and use a `OutputFixingParser`. This output parser takes as an argument another output parser but also an LLM with which to try to correct any formatting mistakes."]}, {"cell_type": "code", "execution_count": 5, "id": "4aaccbf1", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers import OutputFixingParser\n", "\n", "new_parser = OutputFixingParser.from_llm(parser=parser, llm=ChatOpenAI())"]}, {"cell_type": "code", "execution_count": 6, "id": "8031c22d", "metadata": {}, "outputs": [{"data": {"text/plain": ["Actor(name='<PERSON>', film_names=['<PERSON>'])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["new_parser.parse(misformatted)"]}, {"cell_type": "markdown", "id": "84498e02", "metadata": {}, "source": ["Find out api documentation for [OutputFixingParser](https://python.langchain.com/api_reference/langchain/output_parsers/langchain.output_parsers.fix.OutputFixingParser.html#langchain.output_parsers.fix.OutputFixingParser)."]}, {"cell_type": "code", "execution_count": null, "id": "bc7af2a0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}