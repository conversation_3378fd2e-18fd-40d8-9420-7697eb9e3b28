{"cells": [{"cell_type": "markdown", "id": "e3da9a3f-f583-4ba6-994e-0e8c1158f5eb", "metadata": {}, "source": ["# How to create a custom chat model class\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [Chat models](/docs/concepts/chat_models)\n", "\n", ":::\n", "\n", "In this guide, we'll learn how to create a custom [chat model](/docs/concepts/chat_models/) using LangChain abstractions.\n", "\n", "Wrapping your LLM with the standard [`BaseChatModel`](https://python.langchain.com/api_reference/core/language_models/langchain_core.language_models.chat_models.BaseChatModel.html) interface allow you to use your LLM in existing LangChain programs with minimal code modifications!\n", "\n", "As a bonus, your LLM will automatically become a LangChain [Runnable](/docs/concepts/runnables/) and will benefit from some optimizations out of the box (e.g., batch via a threadpool), async support, the `astream_events` API, etc.\n", "\n", "## Inputs and outputs\n", "\n", "First, we need to talk about **[messages](/docs/concepts/messages/)**, which are the inputs and outputs of chat models.\n", "\n", "### Messages\n", "\n", "Chat models take messages as inputs and return a message as output. \n", "\n", "LangChain has a few [built-in message types](/docs/concepts/messages):\n", "\n", "| Message Type          | Description                                                                                     |\n", "|-----------------------|-------------------------------------------------------------------------------------------------|\n", "| `SystemMessage`       | Used for priming AI behavior, usually passed in as the first of a sequence of input messages.   |\n", "| `HumanMessage`        | Represents a message from a person interacting with the chat model.                             |\n", "| `AIMessage`           | Represents a message from the chat model. This can be either text or a request to invoke a tool.|\n", "| `FunctionMessage` / `ToolMessage` | Message for passing the results of tool invocation back to the model.               |\n", "| `AIMessageChunk` / `HumanMessageChunk` / ... | Chunk variant of each type of message. |\n", "\n", "\n", ":::note\n", "`ToolMessage` and `FunctionMessage` closely follow OpenAI's `function` and `tool` roles.\n", "\n", "This is a rapidly developing field and as more models add function calling capabilities. Expect that there will be additions to this schema.\n", ":::"]}, {"cell_type": "code", "execution_count": 4, "id": "c5046e6a-8b09-4a99-b6e6-7a605aac5738", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_core.messages import (\n", "    AIMessage,\n", "    BaseMessage,\n", "    FunctionMessage,\n", "    HumanMessage,\n", "    SystemMessage,\n", "    ToolMessage,\n", ")"]}, {"cell_type": "markdown", "id": "53033447-8260-4f53-bd6f-b2f744e04e75", "metadata": {}, "source": ["### Streaming Variant\n", "\n", "All the chat messages have a streaming variant that contains `Chunk` in the name."]}, {"cell_type": "code", "execution_count": 2, "id": "d4656e9d-bfa1-4703-8f79-762fe6421294", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain_core.messages import (\n", "    AIMessageChunk,\n", "    FunctionMessageChunk,\n", "    HumanMessageChunk,\n", "    SystemMessageChunk,\n", "    ToolMessageChunk,\n", ")"]}, {"cell_type": "markdown", "id": "81ebf3f4-c760-4898-b921-fdb469453d4a", "metadata": {}, "source": ["These chunks are used when streaming output from chat models, and they all define an additive property!"]}, {"cell_type": "code", "execution_count": 3, "id": "9c15c299-6f8a-49cf-a072-09924fd44396", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessageChunk(content='Hello World!')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["AIMessageChunk(content=\"Hello\") + AIMessageChunk(content=\" World!\")"]}, {"cell_type": "markdown", "id": "bbfebea1", "metadata": {}, "source": ["## Base Chat Model\n", "\n", "Let's implement a chat model that echoes back the first `n` characters of the last message in the prompt!\n", "\n", "To do so, we will inherit from `BaseChatModel` and we'll need to implement the following:\n", "\n", "| Method/Property                    | Description                                                       | Required/Optional  |\n", "|------------------------------------|-------------------------------------------------------------------|--------------------|\n", "| `_generate`                        | Use to generate a chat result from a prompt                       | Required           |\n", "| `_llm_type` (property)             | Used to uniquely identify the type of the model. Used for logging.| Required           |\n", "| `_identifying_params` (property)   | Represent model parameterization for tracing purposes.            | Optional           |\n", "| `_stream`                          | Use to implement streaming.                                       | Optional           |\n", "| `_agenerate`                       | Use to implement a native async method.                           | Optional           |\n", "| `_astream`                         | Use to implement async version of `_stream`.                      | Optional           |\n", "\n", "\n", ":::tip\n", "The `_astream` implementation uses `run_in_executor` to launch the sync `_stream` in a separate thread if `_stream` is implemented, otherwise it fallsback to use `_agenerate`.\n", "\n", "You can use this trick if you want to reuse the `_stream` implementation, but if you're able to implement code that's natively async that's a better solution since that code will run with less overhead.\n", ":::"]}, {"cell_type": "markdown", "id": "8e7047bd-c235-46f6-85e1-d6d7e0868eb1", "metadata": {}, "source": ["### Implementation"]}, {"cell_type": "code", "execution_count": null, "id": "25ba32e5-5a6d-49f4-bb68-911827b84d61", "metadata": {"tags": []}, "outputs": [], "source": ["from typing import Any, Dict, Iterator, List, Optional\n", "\n", "from langchain_core.callbacks import (\n", "    CallbackManager<PERSON><PERSON>,\n", ")\n", "from langchain_core.language_models import BaseChatModel\n", "from langchain_core.messages import (\n", "    AIMessage,\n", "    AIMessageChunk,\n", "    BaseMessage,\n", ")\n", "from langchain_core.messages.ai import UsageMetadata\n", "from langchain_core.outputs import ChatGeneration, ChatGenerationChunk, ChatResult\n", "from pydantic import Field\n", "\n", "\n", "class ChatParrotLink(BaseChatModel):\n", "    \"\"\"A custom chat model that echoes the first `parrot_buffer_length` characters\n", "    of the input.\n", "\n", "    When contributing an implementation to LangChain, carefully document\n", "    the model including the initialization parameters, include\n", "    an example of how to initialize the model and include any relevant\n", "    links to the underlying models documentation or API.\n", "\n", "    Example:\n", "\n", "        .. code-block:: python\n", "\n", "            model = ChatParrotLink(parrot_buffer_length=2, model=\"bird-brain-001\")\n", "            result = model.invoke([HumanMessage(content=\"hello\")])\n", "            result = model.batch([[HumanMessage(content=\"hello\")],\n", "                                 [HumanMessage(content=\"world\")]])\n", "    \"\"\"\n", "\n", "    model_name: str = Field(alias=\"model\")\n", "    \"\"\"The name of the model\"\"\"\n", "    parrot_buffer_length: int\n", "    \"\"\"The number of characters from the last message of the prompt to be echoed.\"\"\"\n", "    temperature: Optional[float] = None\n", "    max_tokens: Optional[int] = None\n", "    timeout: Optional[int] = None\n", "    stop: Optional[List[str]] = None\n", "    max_retries: int = 2\n", "\n", "    def _generate(\n", "        self,\n", "        messages: List[BaseMessage],\n", "        stop: Optional[List[str]] = None,\n", "        run_manager: Optional[CallbackManagerForLLMRun] = None,\n", "        **kwargs: Any,\n", "    ) -> ChatResult:\n", "        \"\"\"Override the _generate method to implement the chat model logic.\n", "\n", "        This can be a call to an API, a call to a local model, or any other\n", "        implementation that generates a response to the input prompt.\n", "\n", "        Args:\n", "            messages: the prompt composed of a list of messages.\n", "            stop: a list of strings on which the model should stop generating.\n", "                  If generation stops due to a stop token, the stop token itself\n", "                  SHOULD BE INCLUDED as part of the output. This is not enforced\n", "                  across models right now, but it's a good practice to follow since\n", "                  it makes it much easier to parse the output of the model\n", "                  downstream and understand why generation stopped.\n", "            run_manager: A run manager with callbacks for the LLM.\n", "        \"\"\"\n", "        # Replace this with actual logic to generate a response from a list\n", "        # of messages.\n", "        last_message = messages[-1]\n", "        tokens = last_message.content[: self.parrot_buffer_length]\n", "        ct_input_tokens = sum(len(message.content) for message in messages)\n", "        ct_output_tokens = len(tokens)\n", "        message = AIMessage(\n", "            content=tokens,\n", "            additional_kwargs={},  # Used to add additional payload to the message\n", "            response_metadata={  # Use for response metadata\n", "                \"time_in_seconds\": 3,\n", "                \"model_name\": self.model_name,\n", "            },\n", "            usage_metadata={\n", "                \"input_tokens\": ct_input_tokens,\n", "                \"output_tokens\": ct_output_tokens,\n", "                \"total_tokens\": ct_input_tokens + ct_output_tokens,\n", "            },\n", "        )\n", "        ##\n", "\n", "        generation = ChatGeneration(message=message)\n", "        return ChatResult(generations=[generation])\n", "\n", "    def _stream(\n", "        self,\n", "        messages: List[BaseMessage],\n", "        stop: Optional[List[str]] = None,\n", "        run_manager: Optional[CallbackManagerForLLMRun] = None,\n", "        **kwargs: Any,\n", "    ) -> Iterator[ChatGenerationChunk]:\n", "        \"\"\"Stream the output of the model.\n", "\n", "        This method should be implemented if the model can generate output\n", "        in a streaming fashion. If the model does not support streaming,\n", "        do not implement it. In that case streaming requests will be automatically\n", "        handled by the _generate method.\n", "\n", "        Args:\n", "            messages: the prompt composed of a list of messages.\n", "            stop: a list of strings on which the model should stop generating.\n", "                  If generation stops due to a stop token, the stop token itself\n", "                  SHOULD BE INCLUDED as part of the output. This is not enforced\n", "                  across models right now, but it's a good practice to follow since\n", "                  it makes it much easier to parse the output of the model\n", "                  downstream and understand why generation stopped.\n", "            run_manager: A run manager with callbacks for the LLM.\n", "        \"\"\"\n", "        last_message = messages[-1]\n", "        tokens = str(last_message.content[: self.parrot_buffer_length])\n", "        ct_input_tokens = sum(len(message.content) for message in messages)\n", "\n", "        for token in tokens:\n", "            usage_metadata = UsageMetadata(\n", "                {\n", "                    \"input_tokens\": ct_input_tokens,\n", "                    \"output_tokens\": 1,\n", "                    \"total_tokens\": ct_input_tokens + 1,\n", "                }\n", "            )\n", "            ct_input_tokens = 0\n", "            chunk = ChatGenerationChunk(\n", "                message=AIMessageChunk(content=token, usage_metadata=usage_metadata)\n", "            )\n", "\n", "            if run_manager:\n", "                # This is optional in newer versions of LangChain\n", "                # The on_llm_new_token will be called automatically\n", "                run_manager.on_llm_new_token(token, chunk=chunk)\n", "\n", "            yield chunk\n", "\n", "        # Let's add some other information (e.g., response metadata)\n", "        chunk = ChatGenerationChunk(\n", "            message=AIMessageChunk(\n", "                content=\"\",\n", "                response_metadata={\"time_in_sec\": 3, \"model_name\": self.model_name},\n", "            )\n", "        )\n", "        if run_manager:\n", "            # This is optional in newer versions of LangChain\n", "            # The on_llm_new_token will be called automatically\n", "            run_manager.on_llm_new_token(token, chunk=chunk)\n", "        yield chunk\n", "\n", "    @property\n", "    def _llm_type(self) -> str:\n", "        \"\"\"Get the type of language model used by this chat model.\"\"\"\n", "        return \"echoing-chat-model-advanced\"\n", "\n", "    @property\n", "    def _identifying_params(self) -> Dict[str, Any]:\n", "        \"\"\"Return a dictionary of identifying parameters.\n", "\n", "        This information is used by the LangChain callback system, which\n", "        is used for tracing purposes make it possible to monitor LLMs.\n", "        \"\"\"\n", "        return {\n", "            # The model name allows users to specify custom token counting\n", "            # rules in LLM monitoring applications (e.g., in LangSmith users\n", "            # can provide per token pricing for their model and monitor\n", "            # costs for the given LLM.)\n", "            \"model_name\": self.model_name,\n", "        }"]}, {"cell_type": "markdown", "id": "1e9af284-f2d3-44e2-ac6a-09b73d89ada3", "metadata": {}, "source": ["### Let's test it 🧪\n", "\n", "The chat model will implement the standard `Runnable` interface of LangChain which many of the LangChain abstractions support!"]}, {"cell_type": "code", "execution_count": 5, "id": "27689f30-dcd2-466b-ba9d-f60b7d434110", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Meo', additional_kwargs={}, response_metadata={'time_in_seconds': 3}, id='run-cf11aeb6-8ab6-43d7-8c68-c1ef89b6d78e-0', usage_metadata={'input_tokens': 26, 'output_tokens': 3, 'total_tokens': 29})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["model = ChatParrotLink(parrot_buffer_length=3, model=\"my_custom_model\")\n", "\n", "model.invoke(\n", "    [\n", "        HumanMessage(content=\"hello!\"),\n", "        AIMessage(content=\"Hi there human!\"),\n", "        HumanMessage(content=\"Meow!\"),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "406436df-31bf-466b-9c3d-39db9d6b6407", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage(content='hel', additional_kwargs={}, response_metadata={'time_in_seconds': 3}, id='run-618e5ed4-d611-4083-8cf1-c270726be8d9-0', usage_metadata={'input_tokens': 5, 'output_tokens': 3, 'total_tokens': 8})"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["model.invoke(\"hello\")"]}, {"cell_type": "code", "execution_count": 7, "id": "a72ffa46-6004-41ef-bbe4-56fa17a029e2", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["[AIMessage(content='hel', additional_kwargs={}, response_metadata={'time_in_seconds': 3}, id='run-eea4ed7d-d750-48dc-90c0-7acca1ff388f-0', usage_metadata={'input_tokens': 5, 'output_tokens': 3, 'total_tokens': 8}),\n", " AIMessage(content='goo', additional_kwargs={}, response_metadata={'time_in_seconds': 3}, id='run-07cfc5c1-3c62-485f-b1e0-3d46e1547287-0', usage_metadata={'input_tokens': 7, 'output_tokens': 3, 'total_tokens': 10})]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["model.batch([\"hello\", \"goodbye\"])"]}, {"cell_type": "code", "execution_count": 8, "id": "3633be2c-2ea0-42f9-a72f-3b5240690b55", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["c|a|t||"]}], "source": ["for chunk in model.stream(\"cat\"):\n", "    print(chunk.content, end=\"|\")"]}, {"cell_type": "markdown", "id": "3f8a7c42-aec4-4116-adf3-93133d409827", "metadata": {}, "source": ["Please see the implementation of `_astream` in the model! If you do not implement it, then no output will stream.!"]}, {"cell_type": "code", "execution_count": 9, "id": "b7d73995-eeab-48c6-a7d8-32c98ba29fc2", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["c|a|t||"]}], "source": ["async for chunk in model.astream(\"cat\"):\n", "    print(chunk.content, end=\"|\")"]}, {"cell_type": "markdown", "id": "f80dc55b-d159-4527-9191-407a7c6d6042", "metadata": {}, "source": ["Let's try to use the astream events API which will also help double check that all the callbacks were implemented!"]}, {"cell_type": "code", "execution_count": 10, "id": "17840eba-8ff4-4e73-8e4f-85f16eb1c9d0", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'event': 'on_chat_model_start', 'run_id': '3f0b5501-5c78-45b3-92fc-8322a6a5024a', 'name': 'ChatParrotLink', 'tags': [], 'metadata': {}, 'data': {'input': 'cat'}, 'parent_ids': []}\n", "{'event': 'on_chat_model_stream', 'run_id': '3f0b5501-5c78-45b3-92fc-8322a6a5024a', 'tags': [], 'metadata': {}, 'name': 'ChatParrotLink', 'data': {'chunk': AIMessageChunk(content='c', additional_kwargs={}, response_metadata={}, id='run-3f0b5501-5c78-45b3-92fc-8322a6a5024a', usage_metadata={'input_tokens': 3, 'output_tokens': 1, 'total_tokens': 4})}, 'parent_ids': []}\n", "{'event': 'on_chat_model_stream', 'run_id': '3f0b5501-5c78-45b3-92fc-8322a6a5024a', 'tags': [], 'metadata': {}, 'name': 'ChatParrotLink', 'data': {'chunk': AIMessageChunk(content='a', additional_kwargs={}, response_metadata={}, id='run-3f0b5501-5c78-45b3-92fc-8322a6a5024a', usage_metadata={'input_tokens': 0, 'output_tokens': 1, 'total_tokens': 1})}, 'parent_ids': []}\n", "{'event': 'on_chat_model_stream', 'run_id': '3f0b5501-5c78-45b3-92fc-8322a6a5024a', 'tags': [], 'metadata': {}, 'name': 'ChatParrotLink', 'data': {'chunk': AIMessageChunk(content='t', additional_kwargs={}, response_metadata={}, id='run-3f0b5501-5c78-45b3-92fc-8322a6a5024a', usage_metadata={'input_tokens': 0, 'output_tokens': 1, 'total_tokens': 1})}, 'parent_ids': []}\n", "{'event': 'on_chat_model_stream', 'run_id': '3f0b5501-5c78-45b3-92fc-8322a6a5024a', 'tags': [], 'metadata': {}, 'name': 'ChatParrotLink', 'data': {'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={'time_in_sec': 3}, id='run-3f0b5501-5c78-45b3-92fc-8322a6a5024a')}, 'parent_ids': []}\n", "{'event': 'on_chat_model_end', 'name': 'ChatParrotLink', 'run_id': '3f0b5501-5c78-45b3-92fc-8322a6a5024a', 'tags': [], 'metadata': {}, 'data': {'output': AIMessageChunk(content='cat', additional_kwargs={}, response_metadata={'time_in_sec': 3}, id='run-3f0b5501-5c78-45b3-92fc-8322a6a5024a', usage_metadata={'input_tokens': 3, 'output_tokens': 3, 'total_tokens': 6})}, 'parent_ids': []}\n"]}], "source": ["async for event in model.astream_events(\"cat\", version=\"v1\"):\n", "    print(event)"]}, {"cell_type": "markdown", "id": "44ee559b-b1da-4851-8c97-420ab394aff9", "metadata": {}, "source": ["## Contributing\n", "\n", "We appreciate all chat model integration contributions. \n", "\n", "Here's a checklist to help make sure your contribution gets added to <PERSON><PERSON><PERSON><PERSON>:\n", "\n", "Documentation:\n", "\n", "* The model contains doc-strings for all initialization arguments, as these will be surfaced in the [API Reference](https://python.langchain.com/api_reference/langchain/index.html).\n", "* The class doc-string for the model contains a link to the model API if the model is powered by a service.\n", "\n", "Tests:\n", "\n", "* [ ] Add unit or integration tests to the overridden methods. Verify that `invoke`, `ainvoke`, `batch`, `stream` work if you've over-ridden the corresponding code.\n", "\n", "\n", "Streaming (if you're implementing it):\n", "\n", "* [ ] Implement the _stream method to get streaming working\n", "\n", "Stop Token Behavior:\n", "\n", "* [ ] Stop token should be respected\n", "* [ ] Stop token should be INCLUDED as part of the response\n", "\n", "Secret API Keys:\n", "\n", "* [ ] If your model connects to an API it will likely accept API keys as part of its initialization. Use Pydantic's `SecretStr` type for secrets, so they don't get accidentally printed out when folks print the model.\n", "\n", "\n", "Identifying Params:\n", "\n", "* [ ] Include a `model_name` in identifying params\n", "\n", "\n", "Optimizations:\n", "\n", "Consider providing native async support to reduce the overhead from the model!\n", " \n", "* [ ] Provided a native async of `_agenerate` (used by `ainvoke`)\n", "* [ ] Provided a native async of `_astream` (used by `astream`)\n", "\n", "## Next steps\n", "\n", "You've now learned how to create your own custom chat models.\n", "\n", "Next, check out the other how-to guides chat models in this section, like [how to get a model to return structured output](/docs/how_to/structured_output) or [how to track chat model token usage](/docs/how_to/chat_token_usage_tracking)."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}