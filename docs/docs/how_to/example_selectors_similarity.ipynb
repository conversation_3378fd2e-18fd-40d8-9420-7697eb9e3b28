{"cells": [{"cell_type": "markdown", "id": "8c1e7149", "metadata": {}, "source": ["# How to select examples by similarity\n", "\n", "This object selects [examples](/docs/concepts/example_selectors/) based on similarity to the inputs. It does this by finding the examples with the embeddings that have the greatest cosine similarity with the inputs.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "abc30764", "metadata": {}, "outputs": [], "source": ["from langchain_chroma import Chroma\n", "from langchain_core.example_selectors import SemanticSimilarityExampleSelector\n", "from langchain_core.prompts import FewShotPromptTemplate, PromptTemplate\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "example_prompt = PromptTemplate(\n", "    input_variables=[\"input\", \"output\"],\n", "    template=\"Input: {input}\\nOutput: {output}\",\n", ")\n", "\n", "# Examples of a pretend task of creating antonyms.\n", "examples = [\n", "    {\"input\": \"happy\", \"output\": \"sad\"},\n", "    {\"input\": \"tall\", \"output\": \"short\"},\n", "    {\"input\": \"energetic\", \"output\": \"lethargic\"},\n", "    {\"input\": \"sunny\", \"output\": \"gloomy\"},\n", "    {\"input\": \"windy\", \"output\": \"calm\"},\n", "]"]}, {"cell_type": "code", "execution_count": 2, "id": "8a37fc84", "metadata": {}, "outputs": [], "source": ["example_selector = SemanticSimilarityExampleSelector.from_examples(\n", "    # The list of examples available to select from.\n", "    examples,\n", "    # The embedding class used to produce embeddings which are used to measure semantic similarity.\n", "    OpenAIEmbeddings(),\n", "    # The VectorStore class that is used to store the embeddings and do a similarity search over.\n", "    Chroma,\n", "    # The number of examples to produce.\n", "    k=1,\n", ")\n", "similar_prompt = FewShotPromptTemplate(\n", "    # We provide an ExampleSelector instead of examples.\n", "    example_selector=example_selector,\n", "    example_prompt=example_prompt,\n", "    prefix=\"Give the antonym of every input\",\n", "    suffix=\"Input: {adjective}\\nOutput:\",\n", "    input_variables=[\"adjective\"],\n", ")"]}, {"cell_type": "code", "execution_count": 3, "id": "eabd2020", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Give the antonym of every input\n", "\n", "Input: happy\n", "Output: sad\n", "\n", "Input: worried\n", "Output:\n"]}], "source": ["# Input is a feeling, so should select the happy/sad example\n", "print(similar_prompt.format(adjective=\"worried\"))"]}, {"cell_type": "code", "execution_count": 4, "id": "c02225a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Give the antonym of every input\n", "\n", "Input: tall\n", "Output: short\n", "\n", "Input: large\n", "Output:\n"]}], "source": ["# Input is a measurement, so should select the tall/short example\n", "print(similar_prompt.format(adjective=\"large\"))"]}, {"cell_type": "code", "execution_count": 5, "id": "09836c64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Give the antonym of every input\n", "\n", "Input: enthusiastic\n", "Output: apathetic\n", "\n", "Input: passionate\n", "Output:\n"]}], "source": ["# You can add new examples to the SemanticSimilarityExampleSelector as well\n", "similar_prompt.example_selector.add_example(\n", "    {\"input\": \"enthusiastic\", \"output\": \"apathetic\"}\n", ")\n", "print(similar_prompt.format(adjective=\"passionate\"))"]}, {"cell_type": "code", "execution_count": null, "id": "92e2c85f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.1"}}, "nbformat": 4, "nbformat_minor": 5}