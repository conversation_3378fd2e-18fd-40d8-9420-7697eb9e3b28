{"cells": [{"cell_type": "markdown", "id": "fc0db1bc", "metadata": {}, "source": ["# How to reorder retrieved results to mitigate the \"lost in the middle\" effect\n", "\n", "Substantial performance degradations in [RAG](/docs/tutorials/rag) applications have been [documented](https://arxiv.org/abs/2307.03172) as the number of retrieved documents grows (e.g., beyond ten). In brief: models are liable to miss relevant information in the middle of long contexts.\n", "\n", "By contrast, queries against vector stores will typically return documents in descending order of relevance (e.g., as measured by cosine similarity of [embeddings](/docs/concepts/embedding_models)).\n", "\n", "To mitigate the [\"lost in the middle\"](https://arxiv.org/abs/2307.03172) effect, you can re-order documents after retrieval such that the most relevant documents are positioned at extrema (e.g., the first and last pieces of context), and the least relevant documents are positioned in the middle. In some cases this can help surface the most relevant information to LLMs.\n", "\n", "The [LongContextReorder](https://python.langchain.com/api_reference/community/document_transformers/langchain_community.document_transformers.long_context_reorder.LongContextReorder.html) document transformer implements this re-ordering procedure. Below we demonstrate an example."]}, {"cell_type": "code", "execution_count": null, "id": "2074fdaa-edff-468a-970f-6f5f26e93d4a", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain langchain-community langchain-openai"]}, {"cell_type": "markdown", "id": "c97eaaf2-34b7-4770-9949-e1abc4ca5226", "metadata": {}, "source": ["First we embed some artificial documents and index them in a basic in-memory vector store. We will use [OpenAI](/docs/integrations/providers/openai/) embeddings, but any LangChain vector store or embeddings model will suffice."]}, {"cell_type": "code", "execution_count": 1, "id": "49cbcd8e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- The Celtics are my favourite team.\n", "- This is a document about the Boston Celtics\n", "- The Boston Celtics won the game by 20 points\n", "- <PERSON><PERSON> is one of the best Celtics players.\n", "- Basquetball is a great sport.\n", "- <PERSON> was an iconic NBA player.\n", "- This is just a random text.\n", "- I simply love going to the movies\n", "- Fly me to the moon is one of my favourite songs.\n", "- Elden Ring is one of the best games in the last 15 years.\n"]}], "source": ["from langchain_core.vectorstores import InMemoryVectorStore\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "# Get embeddings.\n", "embeddings = OpenAIEmbeddings()\n", "\n", "texts = [\n", "    \"Basquetball is a great sport.\",\n", "    \"Fly me to the moon is one of my favourite songs.\",\n", "    \"The Celtics are my favourite team.\",\n", "    \"This is a document about the Boston Celtics\",\n", "    \"I simply love going to the movies\",\n", "    \"The Boston Celtics won the game by 20 points\",\n", "    \"This is just a random text.\",\n", "    \"Elden Ring is one of the best games in the last 15 years.\",\n", "    \"<PERSON><PERSON> is one of the best Celtics players.\",\n", "    \"<PERSON> was an iconic NBA player.\",\n", "]\n", "\n", "# Create a retriever\n", "retriever = InMemoryVectorStore.from_texts(texts, embedding=embeddings).as_retriever(\n", "    search_kwargs={\"k\": 10}\n", ")\n", "query = \"What can you tell me about the Celtics?\"\n", "\n", "# Get relevant documents ordered by relevance score\n", "docs = retriever.invoke(query)\n", "for doc in docs:\n", "    print(f\"- {doc.page_content}\")"]}, {"cell_type": "markdown", "id": "175d031a-43fa-42f4-93c4-2ba52c3c3ee5", "metadata": {}, "source": ["Note that documents are returned in descending order of relevance to the query. The `LongContextReorder` document transformer will implement the re-ordering described above:"]}, {"cell_type": "code", "execution_count": 2, "id": "555f7a0d-74d1-44f3-949d-6e758aa9f43a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- This is a document about the Boston Celtics\n", "- <PERSON><PERSON> is one of the best Celtics players.\n", "- <PERSON> was an iconic NBA player.\n", "- I simply love going to the movies\n", "- Elden Ring is one of the best games in the last 15 years.\n", "- Fly me to the moon is one of my favourite songs.\n", "- This is just a random text.\n", "- Basquetball is a great sport.\n", "- The Boston Celtics won the game by 20 points\n", "- The Celtics are my favourite team.\n"]}], "source": ["from langchain_community.document_transformers import LongContextReorder\n", "\n", "# Reorder the documents:\n", "# Less relevant document will be at the middle of the list and more\n", "# relevant elements at beginning / end.\n", "reordering = LongContextReorder()\n", "reordered_docs = reordering.transform_documents(docs)\n", "\n", "# Confirm that the 4 relevant documents are at beginning and end.\n", "for doc in reordered_docs:\n", "    print(f\"- {doc.page_content}\")"]}, {"cell_type": "markdown", "id": "a8d2ef0c-c397-4d8d-8118-3f7acf86d241", "metadata": {}, "source": ["Below, we show how to incorporate the re-ordered documents into a simple question-answering chain:"]}, {"cell_type": "code", "execution_count": 3, "id": "f533c74e-21cb-4f21-b063-4c66754a4122", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Boston Celtics are a professional basketball team known for their rich history and success in the NBA. L. Kornet is recognized as one of the best players on the team, and the Celtics recently won a game by 20 points. The Celtics are favored by some fans, as indicated by the statement, \"The Celtics are my favourite team.\" Overall, they have a strong following and are considered a significant part of basketball culture.\n"]}], "source": ["from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")\n", "\n", "prompt_template = \"\"\"\n", "Given these texts:\n", "-----\n", "{context}\n", "-----\n", "Please answer the following question:\n", "{query}\n", "\"\"\"\n", "\n", "prompt = PromptTemplate(\n", "    template=prompt_template,\n", "    input_variables=[\"context\", \"query\"],\n", ")\n", "\n", "# Create and invoke the chain:\n", "chain = create_stuff_documents_chain(llm, prompt)\n", "response = chain.invoke({\"context\": reordered_docs, \"query\": query})\n", "print(response)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}