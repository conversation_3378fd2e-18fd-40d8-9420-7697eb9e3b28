{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to disable parallel tool calling\n", "\n", ":::info Provider-specific\n", "\n", "This API is currently only supported by OpenAI and Anthropic.\n", "\n", ":::\n", "\n", "OpenAI tool calling performs tool calling in parallel by default. That means that if we ask a question like \"What is the weather in Tokyo, New York, and Chicago?\" and we have a tool for getting the weather, it will call the tool 3 times in parallel. We can force it to call only a single tool once by using the ``parallel_tool_call`` parameter."]}, {"cell_type": "markdown", "metadata": {}, "source": ["First let's set up our tools and model:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def add(a: int, b: int) -> int:\n", "    \"\"\"Adds a and b.\"\"\"\n", "    return a + b\n", "\n", "\n", "@tool\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiplies a and b.\"\"\"\n", "    return a * b\n", "\n", "\n", "tools = [add, multiply]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from getpass import getpass\n", "\n", "from langchain.chat_models import init_chat_model\n", "\n", "if \"OPENAI_API_KEY\" not in os.environ:\n", "    os.environ[\"OPENAI_API_KEY\"] = getpass()\n", "\n", "llm = init_chat_model(\"openai:gpt-4.1-mini\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's show a quick example of how disabling parallel tool calls work:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'add',\n", "  'args': {'a': 2, 'b': 2},\n", "  'id': 'call_Hh4JOTCDM85Sm9Pr84VKrWu5'}]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["llm_with_tools = llm.bind_tools(tools, parallel_tool_calls=False)\n", "llm_with_tools.invoke(\"Please call the first tool two times\").tool_calls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As we can see, even though we explicitly told the model to call a tool twice, by disabling parallel tool calls the model was constrained to only calling one."]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 4}