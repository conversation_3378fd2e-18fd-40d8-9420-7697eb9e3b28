{"cells": [{"cell_type": "markdown", "id": "9a8bceb3-95bd-4496-bb9e-57655136e070", "metadata": {}, "source": ["# How to convert Runnables to Tools\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Runnables](/docs/concepts/runnables)\n", "- [Tools](/docs/concepts/tools)\n", "- [Agents](/docs/tutorials/agents)\n", "\n", ":::\n", "\n", "Here we will demonstrate how to convert a LangChain `Runnable` into a tool that can be used by agents, chains, or chat models.\n", "\n", "## Dependencies\n", "\n", "**Note**: this guide requires `langchain-core` >= 0.2.13. We will also use [OpenAI](/docs/integrations/providers/openai/) for embeddings, but any LangChain embeddings should suffice. We will use a simple [LangGraph](https://langchain-ai.github.io/langgraph/) agent for demonstration purposes."]}, {"cell_type": "code", "execution_count": null, "id": "92341f48-2c29-4ce9-8ab8-0a7c7a7c98a1", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langchain-core langchain-openai langgraph"]}, {"cell_type": "markdown", "id": "2b0dcc1a-48e8-4a81-b920-3563192ce076", "metadata": {}, "source": ["Lang<PERSON><PERSON><PERSON> [tools](/docs/concepts/tools) are interfaces that an agent, chain, or chat model can use to interact with the world. See [here](/docs/how_to/#tools) for how-to guides covering tool-calling, built-in tools, custom tools, and more information.\n", "\n", "LangChain tools-- instances of [BaseTool](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.BaseTool.html)-- are [Runnables](/docs/concepts/runnables) with additional constraints that enable them to be invoked effectively by language models:\n", "\n", "- Their inputs are constrained to be serializable, specifically strings and Python `dict` objects;\n", "- They contain names and descriptions indicating how and when they should be used;\n", "- They may contain a detailed [args_schema](https://python.langchain.com/docs/how_to/custom_tools/) for their arguments. That is, while a tool (as a `Runnable`) might accept a single `dict` input, the specific keys and type information needed to populate a dict should be specified in the `args_schema`.\n", "\n", "Runnables that accept string or `dict` input can be converted to tools using the [as_tool](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.Runnable.html#langchain_core.runnables.base.Runnable.as_tool) method, which allows for the specification of names, descriptions, and additional schema information for arguments."]}, {"cell_type": "markdown", "id": "b4d76680-1b6b-4862-8c4f-22766a1d41f2", "metadata": {}, "source": ["## Basic usage\n", "\n", "With typed `dict` input:"]}, {"cell_type": "code", "execution_count": 2, "id": "b2cc4231-64a3-4733-a284-932dcbf2fcc3", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from langchain_core.runnables import RunnableLambda\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class Args(TypedDict):\n", "    a: int\n", "    b: List[int]\n", "\n", "\n", "def f(x: Args) -> str:\n", "    return str(x[\"a\"] * max(x[\"b\"]))\n", "\n", "\n", "runnable = RunnableLambda(f)\n", "as_tool = runnable.as_tool(\n", "    name=\"My tool\",\n", "    description=\"Explanation of when to use tool.\",\n", ")"]}, {"cell_type": "code", "execution_count": 3, "id": "57f2d435-624d-459a-903d-8509fbbde610", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Explanation of when to use tool.\n"]}, {"data": {"text/plain": ["{'properties': {'a': {'title': 'A', 'type': 'integer'},\n", "  'b': {'items': {'type': 'integer'}, 'title': 'B', 'type': 'array'}},\n", " 'required': ['a', 'b'],\n", " 'title': 'My tool',\n", " 'type': 'object'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["print(as_tool.description)\n", "\n", "as_tool.args_schema.model_json_schema()"]}, {"cell_type": "code", "execution_count": 4, "id": "54ae7384-a03d-4fa4-8cdf-9604a4bc39ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["'6'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["as_tool.invoke({\"a\": 3, \"b\": [1, 2]})"]}, {"cell_type": "markdown", "id": "9038f587-4613-4f50-b349-135f9e7e3b15", "metadata": {}, "source": ["Without typing information, arg types can be specified via `arg_types`:"]}, {"cell_type": "code", "execution_count": 5, "id": "169f733c-4936-497f-8577-ee769dc16b88", "metadata": {}, "outputs": [], "source": ["from typing import Any, Dict\n", "\n", "\n", "def g(x: Dict[str, Any]) -> str:\n", "    return str(x[\"a\"] * max(x[\"b\"]))\n", "\n", "\n", "runnable = RunnableLambda(g)\n", "as_tool = runnable.as_tool(\n", "    name=\"My tool\",\n", "    description=\"Explanation of when to use tool.\",\n", "    arg_types={\"a\": int, \"b\": List[int]},\n", ")"]}, {"cell_type": "markdown", "id": "32b1a992-8997-4c98-8eb2-c9fe9431b799", "metadata": {}, "source": ["Alternatively, the schema can be fully specified by directly passing the desired [args_schema](https://python.langchain.com/api_reference/core/tools/langchain_core.tools.BaseTool.html#langchain_core.tools.BaseTool.args_schema) for the tool:"]}, {"cell_type": "code", "execution_count": 6, "id": "eb102705-89b7-48dc-9158-d36d5f98ae8e", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class GSchema(BaseModel):\n", "    \"\"\"Apply a function to an integer and list of integers.\"\"\"\n", "\n", "    a: int = Field(..., description=\"Integer\")\n", "    b: List[int] = Field(..., description=\"List of ints\")\n", "\n", "\n", "runnable = RunnableLambda(g)\n", "as_tool = runnable.as_tool(GSchema)"]}, {"cell_type": "markdown", "id": "7c474d85-4e01-4fae-9bba-0c6c8c26475c", "metadata": {}, "source": ["String input is also supported:"]}, {"cell_type": "code", "execution_count": 7, "id": "c475282a-58d6-4c2b-af7d-99b73b7d8a13", "metadata": {}, "outputs": [], "source": ["def f(x: str) -> str:\n", "    return x + \"a\"\n", "\n", "\n", "def g(x: str) -> str:\n", "    return x + \"z\"\n", "\n", "\n", "runnable = RunnableLambda(f) | g\n", "as_tool = runnable.as_tool()"]}, {"cell_type": "code", "execution_count": 8, "id": "ad6d8d96-3a87-40bd-a2ac-44a8acde0a8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'baz'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["as_tool.invoke(\"b\")"]}, {"cell_type": "markdown", "id": "89fdb3a7-d228-48f0-8f73-262af4febb58", "metadata": {}, "source": ["## In agents\n", "\n", "Below we will incorporate <PERSON><PERSON><PERSON><PERSON> as tools in an [agent](/docs/concepts/agents) application. We will demonstrate with:\n", "\n", "- a document [retriever](/docs/concepts/retrievers);\n", "- a simple [RAG](/docs/tutorials/rag/) chain, allowing an agent to delegate relevant queries to it.\n", "\n", "We first instantiate a chat model that supports [tool calling](/docs/how_to/tool_calling/):\n", "\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n"]}, {"cell_type": "code", "execution_count": 9, "id": "d06c9f2a-4475-450f-9106-54db1d99623b", "metadata": {}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)"]}, {"cell_type": "markdown", "id": "e8a2038a-d762-4196-b5e3-fdb89c11e71d", "metadata": {}, "source": ["Following the [RAG tutorial](/docs/tutorials/rag/), let's first construct a retriever:"]}, {"cell_type": "code", "execution_count": 10, "id": "23d2a47e-6712-4294-81c8-2c1d76b4bb81", "metadata": {}, "outputs": [], "source": ["from langchain_core.documents import Document\n", "from langchain_core.vectorstores import InMemoryVectorStore\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "documents = [\n", "    Document(\n", "        page_content=\"Dogs are great companions, known for their loyalty and friendliness.\",\n", "    ),\n", "    Document(\n", "        page_content=\"Cats are independent pets that often enjoy their own space.\",\n", "    ),\n", "]\n", "\n", "vectorstore = InMemoryVectorStore.from_documents(\n", "    documents, embedding=OpenAIEmbeddings()\n", ")\n", "\n", "retriever = vectorstore.as_retriever(\n", "    search_type=\"similarity\",\n", "    search_kwargs={\"k\": 1},\n", ")"]}, {"cell_type": "markdown", "id": "9ba737ac-43a2-4a6f-b855-5bd0305017f1", "metadata": {}, "source": ["We next create use a simple pre-built [LangGraph agent](https://python.langchain.com/docs/tutorials/agents/) and provide it the tool:"]}, {"cell_type": "code", "execution_count": 11, "id": "c939cf2a-60e9-4afd-8b47-84d76ccb13f5", "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "tools = [\n", "    retriever.as_tool(\n", "        name=\"pet_info_retriever\",\n", "        description=\"Get information about pets.\",\n", "    )\n", "]\n", "agent = create_react_agent(llm, tools)"]}, {"cell_type": "code", "execution_count": 12, "id": "be29437b-a187-4a0a-9a5d-419c56f2434e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'agent': {'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_W8cnfOjwqEn4cFcg19LN9mYD', 'function': {'arguments': '{\"__arg1\":\"dogs\"}', 'name': 'pet_info_retriever'}, 'type': 'function'}]}, response_metadata={'token_usage': {'completion_tokens': 19, 'prompt_tokens': 60, 'total_tokens': 79}, 'model_name': 'gpt-4o-mini', 'system_fingerprint': None, 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-d7f81de9-1fb7-4caf-81ed-16dcdb0b2ab4-0', tool_calls=[{'name': 'pet_info_retriever', 'args': {'__arg1': 'dogs'}, 'id': 'call_W8cnfOjwqEn4cFcg19LN9mYD'}], usage_metadata={'input_tokens': 60, 'output_tokens': 19, 'total_tokens': 79})]}}\n", "----\n", "{'tools': {'messages': [ToolMessage(content=\"[Document(id='86f835fe-4bbe-4ec6-aeb4-489a8b541707', page_content='Dogs are great companions, known for their loyalty and friendliness.')]\", name='pet_info_retriever', tool_call_id='call_W8cnfOjwqEn4cFcg19LN9mYD')]}}\n", "----\n", "{'agent': {'messages': [AIMessage(content='Dogs are known for being great companions, known for their loyalty and friendliness.', response_metadata={'token_usage': {'completion_tokens': 18, 'prompt_tokens': 134, 'total_tokens': 152}, 'model_name': 'gpt-4o-mini', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-9ca5847a-a5eb-44c0-a774-84cc2c5bbc5b-0', usage_metadata={'input_tokens': 134, 'output_tokens': 18, 'total_tokens': 152})]}}\n", "----\n"]}], "source": ["for chunk in agent.stream({\"messages\": [(\"human\", \"What are dogs known for?\")]}):\n", "    print(chunk)\n", "    print(\"----\")"]}, {"cell_type": "markdown", "id": "96f2ac9c-36f4-4b7a-ae33-f517734c86aa", "metadata": {}, "source": ["See [Lang<PERSON><PERSON> trace](https://smith.langchain.com/public/44e438e3-2faf-45bd-b397-5510fc145eb9/r) for the above run."]}, {"cell_type": "markdown", "id": "a722fd8a-b957-4ba7-b408-35596b76835f", "metadata": {}, "source": ["Going further, we can create a simple [RAG](/docs/tutorials/rag/) chain that takes an additional parameter-- here, the \"style\" of the answer."]}, {"cell_type": "code", "execution_count": 13, "id": "bea518c9-c711-47c2-b8cc-dbd102f71f09", "metadata": {}, "outputs": [], "source": ["from operator import itemgetter\n", "\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnablePassthrough\n", "\n", "system_prompt = \"\"\"\n", "You are an assistant for question-answering tasks.\n", "Use the below context to answer the question. If\n", "you don't know the answer, say you don't know.\n", "Use three sentences maximum and keep the answer\n", "concise.\n", "\n", "Answer in the style of {answer_style}.\n", "\n", "Question: {question}\n", "\n", "Context: {context}\n", "\"\"\"\n", "\n", "prompt = ChatPromptTemplate.from_messages([(\"system\", system_prompt)])\n", "\n", "rag_chain = (\n", "    {\n", "        \"context\": itemgetter(\"question\") | retriever,\n", "        \"question\": itemgetter(\"question\"),\n", "        \"answer_style\": itemgetter(\"answer_style\"),\n", "    }\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "markdown", "id": "955a23db-5218-4c34-8486-450a2ddb3443", "metadata": {}, "source": ["Note that the input schema for our chain contains the required arguments, so it converts to a tool without further specification:"]}, {"cell_type": "code", "execution_count": 14, "id": "2c9f6e61-80ed-4abb-8e77-84de3ccbc891", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'properties': {'question': {'title': 'Question'},\n", "  'answer_style': {'title': 'Answer Style'}},\n", " 'required': ['question', 'answer_style'],\n", " 'title': '<PERSON>nableParallel<context,question,answer_style>Input',\n", " 'type': 'object'}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["rag_chain.input_schema.model_json_schema()"]}, {"cell_type": "code", "execution_count": 15, "id": "a3f9cf5b-8c71-4b0f-902b-f92e028780c9", "metadata": {}, "outputs": [], "source": ["rag_tool = rag_chain.as_tool(\n", "    name=\"pet_expert\",\n", "    description=\"Get information about pets.\",\n", ")"]}, {"cell_type": "markdown", "id": "4570615b-8f96-4d97-ae01-1c08b14be584", "metadata": {}, "source": ["Below we again invoke the agent. Note that the agent populates the required parameters in its `tool_calls`:"]}, {"cell_type": "code", "execution_count": 18, "id": "06409913-a2ad-400f-a202-7b8dd2ef483a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'agent': {'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_17iLPWvOD23zqwd1QVQ00Y63', 'function': {'arguments': '{\"question\":\"What are dogs known for according to pirates?\",\"answer_style\":\"quote\"}', 'name': 'pet_expert'}, 'type': 'function'}]}, response_metadata={'token_usage': {'completion_tokens': 28, 'prompt_tokens': 59, 'total_tokens': 87}, 'model_name': 'gpt-4o-mini', 'system_fingerprint': None, 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-7fef44f3-7bba-4e63-8c51-2ad9c5e65e2e-0', tool_calls=[{'name': 'pet_expert', 'args': {'question': 'What are dogs known for according to pirates?', 'answer_style': 'quote'}, 'id': 'call_17iLPWvOD23zqwd1QVQ00Y63'}], usage_metadata={'input_tokens': 59, 'output_tokens': 28, 'total_tokens': 87})]}}\n", "----\n", "{'tools': {'messages': [ToolMessage(content='\"Dogs are known for their loyalty and friendliness, making them great companions for pirates on long sea voyages.\"', name='pet_expert', tool_call_id='call_17iLPWvOD23zqwd1QVQ00Y63')]}}\n", "----\n", "{'agent': {'messages': [AIMessage(content='According to pirates, dogs are known for their loyalty and friendliness, making them great companions for pirates on long sea voyages.', response_metadata={'token_usage': {'completion_tokens': 27, 'prompt_tokens': 119, 'total_tokens': 146}, 'model_name': 'gpt-4o-mini', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-5a30edc3-7be0-4743-b980-ca2f8cad9b8d-0', usage_metadata={'input_tokens': 119, 'output_tokens': 27, 'total_tokens': 146})]}}\n", "----\n"]}], "source": ["agent = create_react_agent(llm, [rag_tool])\n", "\n", "for chunk in agent.stream(\n", "    {\"messages\": [(\"human\", \"What would a pirate say dogs are known for?\")]}\n", "):\n", "    print(chunk)\n", "    print(\"----\")"]}, {"cell_type": "markdown", "id": "96cc9bc3-e79e-49a8-9915-428ea225358b", "metadata": {}, "source": ["See [Lang<PERSON><PERSON> trace](https://smith.langchain.com/public/147ae4e6-4dfb-4dd9-8ca0-5c5b954f08ac/r) for the above run."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}