{"cells": [{"cell_type": "raw", "id": "df7d42b9-58a6-434c-a2d7-0b61142f6d3e", "metadata": {}, "source": ["---\n", "sidebar_position: 6\n", "---"]}, {"cell_type": "markdown", "id": "f2195672-0cab-4967-ba8a-c6544635547d", "metadata": {}, "source": ["# How to construct filters for query analysis\n", "\n", "We may want to do query analysis to extract filters to pass into retrievers. One way we ask the LLM to represent these filters is as a Pydantic model. There is then the issue of converting that Pydantic model into a filter that can be passed into a retriever. \n", "\n", "This can be done manually, but <PERSON><PERSON><PERSON><PERSON> also provides some \"Translators\" that are able to translate from a common syntax into filters specific to each retriever. Here, we will cover how to use those translators."]}, {"cell_type": "code", "execution_count": 1, "id": "8ca446a0", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:32:35.834087Z", "iopub.status.busy": "2024-09-11T02:32:35.833763Z", "iopub.status.idle": "2024-09-11T02:32:36.588973Z", "shell.execute_reply": "2024-09-11T02:32:36.588677Z"}}, "outputs": [], "source": ["from typing import Optional\n", "\n", "from langchain.chains.query_constructor.ir import (\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    Comp<PERSON>on,\n", "    Operation,\n", "    Operator,\n", "    StructuredQuery,\n", ")\n", "from langchain_community.query_constructors.chroma import ChromaTranslator\n", "from langchain_community.query_constructors.elasticsearch import ElasticsearchTranslator\n", "from pydantic import BaseModel"]}, {"cell_type": "markdown", "id": "bc1302ff", "metadata": {}, "source": ["In this example, `year` and `author` are both attributes to filter on."]}, {"cell_type": "code", "execution_count": 2, "id": "64055006", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:32:36.590665Z", "iopub.status.busy": "2024-09-11T02:32:36.590527Z", "iopub.status.idle": "2024-09-11T02:32:36.592985Z", "shell.execute_reply": "2024-09-11T02:32:36.592763Z"}}, "outputs": [], "source": ["class Search(BaseModel):\n", "    query: str\n", "    start_year: Optional[int]\n", "    author: Optional[str]"]}, {"cell_type": "code", "execution_count": 3, "id": "44eb6d98", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:32:36.594147Z", "iopub.status.busy": "2024-09-11T02:32:36.594072Z", "iopub.status.idle": "2024-09-11T02:32:36.595777Z", "shell.execute_reply": "2024-09-11T02:32:36.595563Z"}}, "outputs": [], "source": ["search_query = Search(query=\"RAG\", start_year=2022, author=\"LangChain\")"]}, {"cell_type": "code", "execution_count": 4, "id": "e8ba6705", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:32:36.596902Z", "iopub.status.busy": "2024-09-11T02:32:36.596824Z", "iopub.status.idle": "2024-09-11T02:32:36.598805Z", "shell.execute_reply": "2024-09-11T02:32:36.598629Z"}}, "outputs": [], "source": ["def construct_comparisons(query: Search):\n", "    comparisons = []\n", "    if query.start_year is not None:\n", "        comparisons.append(\n", "            Comparison(\n", "                comparator=Comparator.GT,\n", "                attribute=\"start_year\",\n", "                value=query.start_year,\n", "            )\n", "        )\n", "    if query.author is not None:\n", "        comparisons.append(\n", "            Comparison(\n", "                comparator=Comparator.EQ,\n", "                attribute=\"author\",\n", "                value=query.author,\n", "            )\n", "        )\n", "    return comparisons"]}, {"cell_type": "code", "execution_count": 5, "id": "6a79c9da", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:32:36.599989Z", "iopub.status.busy": "2024-09-11T02:32:36.599909Z", "iopub.status.idle": "2024-09-11T02:32:36.601521Z", "shell.execute_reply": "2024-09-11T02:32:36.601306Z"}}, "outputs": [], "source": ["comparisons = construct_comparisons(search_query)"]}, {"cell_type": "code", "execution_count": 6, "id": "2d0e9689", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:32:36.602688Z", "iopub.status.busy": "2024-09-11T02:32:36.602603Z", "iopub.status.idle": "2024-09-11T02:32:36.604171Z", "shell.execute_reply": "2024-09-11T02:32:36.603981Z"}}, "outputs": [], "source": ["_filter = Operation(operator=Operator.AND, arguments=comparisons)"]}, {"cell_type": "code", "execution_count": 7, "id": "e4c0b2ce", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:32:36.605267Z", "iopub.status.busy": "2024-09-11T02:32:36.605190Z", "iopub.status.idle": "2024-09-11T02:32:36.607993Z", "shell.execute_reply": "2024-09-11T02:32:36.607796Z"}}, "outputs": [{"data": {"text/plain": ["{'bool': {'must': [{'range': {'metadata.start_year': {'gt': 2022}}},\n", "   {'term': {'metadata.author.keyword': '<PERSON><PERSON><PERSON><PERSON>'}}]}}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ElasticsearchTranslator().visit_operation(_filter)"]}, {"cell_type": "code", "execution_count": 8, "id": "d75455ae", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:32:36.609091Z", "iopub.status.busy": "2024-09-11T02:32:36.609012Z", "iopub.status.idle": "2024-09-11T02:32:36.611075Z", "shell.execute_reply": "2024-09-11T02:32:36.610869Z"}}, "outputs": [{"data": {"text/plain": ["{'$and': [{'start_year': {'$gt': 2022}}, {'author': {'$eq': '<PERSON><PERSON><PERSON><PERSON>'}}]}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["ChromaTranslator().visit_operation(_filter)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}