{"cells": [{"cell_type": "markdown", "id": "e5715368", "metadata": {}, "source": ["# How to track token usage in ChatModels\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [Chat models](/docs/concepts/chat_models)\n", "\n", ":::\n", "\n", "Tracking [token](/docs/concepts/tokens/) usage to calculate cost is an important part of putting your app in production. This guide goes over how to obtain this information from your LangChain model calls.\n", "\n", "This guide requires `langchain-anthropic` and `langchain-openai >= 0.3.11`."]}, {"cell_type": "code", "execution_count": null, "id": "9c7d1338-dd1b-4d06-b33d-d5cffc49fd6a", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-anthropic langchain-openai"]}, {"cell_type": "markdown", "id": "c71b676a", "metadata": {}, "source": [":::note A note on streaming with OpenAI\n", "\n", "OpenAI's Chat Completions API does not stream token usage statistics by default (see API reference\n", "[here](https://platform.openai.com/docs/api-reference/completions/create#completions-create-stream_options)).\n", "To recover token counts when streaming with `ChatOpenAI` or `AzureChatOpenAI`, set `stream_usage=True` as\n", "demonstrated in this guide.\n", "\n", ":::"]}, {"cell_type": "markdown", "id": "598ae1e2-a52d-4459-81fd-cdc68b06742a", "metadata": {}, "source": ["## Using <PERSON>\n", "\n", "You can use [LangSmith](https://www.langchain.com/langsmith) to help track token usage in your LLM application. See the [LangSmith quick start guide](https://docs.smith.langchain.com/).\n", "\n", "## Using AIMessage.usage_metadata\n", "\n", "A number of model providers return token usage information as part of the chat generation response. When available, this information will be included on the `AIMessage` objects produced by the corresponding model.\n", "\n", "<PERSON><PERSON><PERSON><PERSON> `AIMessage` objects include a [usage_metadata](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.AIMessage.html#langchain_core.messages.ai.AIMessage.usage_metadata) attribute. When populated, this attribute will be a [UsageMetadata](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.UsageMetadata.html) dictionary with standard keys (e.g., `\"input_tokens\"` and `\"output_tokens\"`). They will also include information on cached token usage and tokens from multi-modal data.\n", "\n", "Examples:\n", "\n", "**OpenAI**:"]}, {"cell_type": "code", "execution_count": 1, "id": "b39bf807-4125-4db4-bbf7-28a46afff6b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input_tokens': 8, 'output_tokens': 9, 'total_tokens': 17}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chat_models import init_chat_model\n", "\n", "llm = init_chat_model(model=\"gpt-4o-mini\")\n", "openai_response = llm.invoke(\"hello\")\n", "openai_response.usage_metadata"]}, {"cell_type": "markdown", "id": "2299c44a-2fe6-4d52-a6a2-99ff6d231c73", "metadata": {}, "source": ["**Anthropic**:"]}, {"cell_type": "code", "execution_count": 2, "id": "9c82ff80-ec4e-4049-b019-5f0bbd7df82a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input_tokens': 8, 'output_tokens': 12, 'total_tokens': 20}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_anthropic import ChatAnthropic\n", "\n", "llm = ChatAnthropic(model=\"claude-3-haiku-20240307\")\n", "anthropic_response = llm.invoke(\"hello\")\n", "anthropic_response.usage_metadata"]}, {"cell_type": "markdown", "id": "b4ef2c43-0ff6-49eb-9782-e4070c9da8d7", "metadata": {}, "source": ["### Streaming\n", "\n", "Some providers support token count metadata in a streaming context.\n", "\n", "#### OpenAI\n", "\n", "For example, OpenAI will return a message [chunk](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.ai.AIMessageChunk.html) at the end of a stream with token usage information. This behavior is supported by `langchain-openai >= 0.1.9` and can be enabled by setting `stream_usage=True`. This attribute can also be set when `ChatOpenAI` is instantiated.\n", "\n", ":::note\n", "By default, the last message chunk in a stream will include a `\"finish_reason\"` in the message's `response_metadata` attribute. If we include token usage in streaming mode, an additional chunk containing usage metadata will be added to the end of the stream, such that `\"finish_reason\"` appears on the second to last message chunk.\n", ":::\n"]}, {"cell_type": "code", "execution_count": 4, "id": "07f0c872-6b6c-4fed-a129-9b5a858505be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content='Hello' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content='!' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content=' How' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content=' can' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content=' I' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content=' assist' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content=' you' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content=' today' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content='?' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content='' response_metadata={'finish_reason': 'stop', 'model_name': 'gpt-4o-mini'} id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623'\n", "content='' id='run-adb20c31-60c7-43a2-99b2-d4a53ca5f623' usage_metadata={'input_tokens': 8, 'output_tokens': 9, 'total_tokens': 17}\n"]}], "source": ["llm = init_chat_model(model=\"gpt-4o-mini\")\n", "\n", "aggregate = None\n", "for chunk in llm.stream(\"hello\", stream_usage=True):\n", "    print(chunk)\n", "    aggregate = chunk if aggregate is None else aggregate + chunk"]}, {"cell_type": "markdown", "id": "dd809ded-8b13-4d5f-be5e-277b79d51802", "metadata": {}, "source": ["Note that the usage metadata will be included in the sum of the individual message chunks:"]}, {"cell_type": "code", "execution_count": 5, "id": "3db7bc03-a7d4-4704-92ab-f8ba92ef59ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! How can I assist you today?\n", "{'input_tokens': 8, 'output_tokens': 9, 'total_tokens': 17}\n"]}], "source": ["print(aggregate.content)\n", "print(aggregate.usage_metadata)"]}, {"cell_type": "markdown", "id": "7dba63e8-0ed7-4533-8f0f-78e19c38a25c", "metadata": {}, "source": ["To disable streaming token counts for OpenAI, set `stream_usage` to False, or omit it from the parameters:"]}, {"cell_type": "code", "execution_count": 6, "id": "67117f2b-ce68-4c1e-9556-2d3849f90e1b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content='Hello' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content='!' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content=' How' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content=' can' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content=' I' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content=' assist' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content=' you' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content=' today' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content='?' id='run-8e758550-94b0-4cca-a298-57482793c25d'\n", "content='' response_metadata={'finish_reason': 'stop', 'model_name': 'gpt-4o-mini'} id='run-8e758550-94b0-4cca-a298-57482793c25d'\n"]}], "source": ["aggregate = None\n", "for chunk in llm.stream(\"hello\"):\n", "    print(chunk)"]}, {"cell_type": "markdown", "id": "6a5d9617-be3a-419a-9276-de9c29fa50ae", "metadata": {}, "source": ["You can also enable streaming token usage by setting `stream_usage` when instantiating the chat model. This can be useful when incorporating chat models into LangChain [chains](/docs/concepts/lcel): usage metadata can be monitored when [streaming intermediate steps](/docs/how_to/streaming#using-stream-events) or using tracing software such as [LangSmith](https://docs.smith.langchain.com/).\n", "\n", "See the below example, where we return output structured to a desired schema, but can still observe token usage streamed from intermediate steps."]}, {"cell_type": "code", "execution_count": 8, "id": "0b1523d8-127e-4314-82fa-bd97aca37f9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Token usage: {'input_tokens': 79, 'output_tokens': 23, 'total_tokens': 102}\n", "\n", "setup='Why was the math book sad?' punchline='Because it had too many problems.'\n"]}], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class Joke(BaseModel):\n", "    \"\"\"Joke to tell user.\"\"\"\n", "\n", "    setup: str = Field(description=\"question to set up a joke\")\n", "    punchline: str = Field(description=\"answer to resolve the joke\")\n", "\n", "\n", "llm = init_chat_model(\n", "    model=\"gpt-4o-mini\",\n", "    stream_usage=True,\n", ")\n", "# Under the hood, .with_structured_output binds tools to the\n", "# chat model and appends a parser.\n", "structured_llm = llm.with_structured_output(Joke)\n", "\n", "async for event in structured_llm.astream_events(\"Tell me a joke\"):\n", "    if event[\"event\"] == \"on_chat_model_end\":\n", "        print(f\"Token usage: {event['data']['output'].usage_metadata}\\n\")\n", "    elif event[\"event\"] == \"on_chain_end\" and event[\"name\"] == \"RunnableSequence\":\n", "        print(event[\"data\"][\"output\"])\n", "    else:\n", "        pass"]}, {"cell_type": "markdown", "id": "2bc8d313-4bef-463e-89a5-236d8bb6ab2f", "metadata": {}, "source": ["Token usage is also visible in the corresponding [Lang<PERSON><PERSON> trace](https://smith.langchain.com/public/fe6513d5-7212-4045-82e0-fefa28bc7656/r) in the payload from the chat model."]}, {"cell_type": "markdown", "id": "d6845407-af25-4eed-bc3e-50925c6661e0", "metadata": {}, "source": ["## Using callbacks\n", "\n", ":::info Requires ``langchain-core>=0.3.49``\n", "\n", ":::\n", "\n", "LangChain implements a callback handler and context manager that will track token usage across calls of any chat model that returns `usage_metadata`.\n", "\n", "There are also some API-specific callback context managers that maintain pricing for different models, allowing for cost estimation in real time. They are currently only implemented for the OpenAI API and Bedrock Anthropic API, and are available in `langchain-community`:\n", "\n", "- [get_openai_callback](https://python.langchain.com/api_reference/community/callbacks/langchain_community.callbacks.manager.get_openai_callback.html)\n", "- [get_bedrock_anthropic_callback](https://python.langchain.com/api_reference/community/callbacks/langchain_community.callbacks.manager.get_bedrock_anthropic_callback.html)\n", "\n", "Below, we demonstrate the general-purpose usage metadata callback manager. We can track token usage through configuration or as a context manager."]}, {"cell_type": "markdown", "id": "6f043cb9", "metadata": {}, "source": ["### Tracking token usage through configuration\n", "\n", "To track token usage through configuration, instantiate a `UsageMetadataCallbackHandler` and pass it into the config:"]}, {"cell_type": "code", "execution_count": 17, "id": "b04a4486-72fd-48ce-8f9e-5d281b441195", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'gpt-4o-mini-2024-07-18': {'input_tokens': 8,\n", "  'output_tokens': 10,\n", "  'total_tokens': 18,\n", "  'input_token_details': {'audio': 0, 'cache_read': 0},\n", "  'output_token_details': {'audio': 0, 'reasoning': 0}},\n", " 'claude-3-5-haiku-20241022': {'input_tokens': 8,\n", "  'output_tokens': 21,\n", "  'total_tokens': 29,\n", "  'input_token_details': {'cache_read': 0, 'cache_creation': 0}}}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chat_models import init_chat_model\n", "from langchain_core.callbacks import UsageMetadataCallbackHandler\n", "\n", "llm_1 = init_chat_model(model=\"openai:gpt-4o-mini\")\n", "llm_2 = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "\n", "callback = UsageMetadataCallbackHandler()\n", "result_1 = llm_1.invoke(\"Hello\", config={\"callbacks\": [callback]})\n", "result_2 = llm_2.invoke(\"Hello\", config={\"callbacks\": [callback]})\n", "callback.usage_metadata"]}, {"cell_type": "markdown", "id": "7a290085-e541-4233-afe4-637ec5032bfd", "metadata": {}, "source": ["### Tracking token usage using a context manager\n", "\n", "You can also use `get_usage_metadata_callback` to create a context manager and aggregate usage metadata there:"]}, {"cell_type": "code", "execution_count": 19, "id": "4728f55a-24e1-48cd-a195-09d037821b1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'gpt-4o-mini-2024-07-18': {'input_tokens': 8, 'output_tokens': 10, 'total_tokens': 18, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}, 'claude-3-5-haiku-20241022': {'input_tokens': 8, 'output_tokens': 21, 'total_tokens': 29, 'input_token_details': {'cache_read': 0, 'cache_creation': 0}}}\n"]}], "source": ["from langchain.chat_models import init_chat_model\n", "from langchain_core.callbacks import get_usage_metadata_callback\n", "\n", "llm_1 = init_chat_model(model=\"openai:gpt-4o-mini\")\n", "llm_2 = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "\n", "with get_usage_metadata_callback() as cb:\n", "    llm_1.invoke(\"Hello\")\n", "    llm_2.invoke(\"Hello\")\n", "    print(cb.usage_metadata)"]}, {"cell_type": "markdown", "id": "c0ab6d27", "metadata": {}, "source": ["Either of these methods will aggregate token usage across multiple calls to each model. For example, you can use it in an [agent](https://python.langchain.com/docs/concepts/agents/) to track token usage across repeated calls to one model:"]}, {"cell_type": "code", "execution_count": null, "id": "8acbead9", "metadata": {}, "outputs": [], "source": ["%pip install -qU langgraph"]}, {"cell_type": "code", "execution_count": 20, "id": "fe945078-ee2d-43ba-8cdf-afb2f2f4ecef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What's the weather in Boston?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  get_weather (call_izMdhUYpp9Vhx7DTNAiybzGa)\n", " Call ID: call_izMdhUYpp9Vhx7DTNAiybzGa\n", "  Args:\n", "    location: Boston\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: get_weather\n", "\n", "It's sunny.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The weather in Boston is sunny.\n", "\n", "Total usage: {'gpt-4o-mini-2024-07-18': {'input_token_details': {'audio': 0, 'cache_read': 0}, 'input_tokens': 125, 'total_tokens': 149, 'output_tokens': 24, 'output_token_details': {'audio': 0, 'reasoning': 0}}}\n"]}], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "\n", "# Create a tool\n", "def get_weather(location: str) -> str:\n", "    \"\"\"Get the weather at a location.\"\"\"\n", "    return \"It's sunny.\"\n", "\n", "\n", "callback = UsageMetadataCallbackHandler()\n", "\n", "tools = [get_weather]\n", "agent = create_react_agent(\"openai:gpt-4o-mini\", tools)\n", "for step in agent.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"What's the weather in Boston?\"}]},\n", "    stream_mode=\"values\",\n", "    config={\"callbacks\": [callback]},\n", "):\n", "    step[\"messages\"][-1].pretty_print()\n", "\n", "\n", "print(f\"\\nTotal usage: {callback.usage_metadata}\")"]}, {"cell_type": "markdown", "id": "33172f31", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now seen a few examples of how to track token usage for supported providers.\n", "\n", "Next, check out the other how-to guides chat models in this section, like [how to get a model to return structured output](/docs/how_to/structured_output) or [how to add caching to your chat models](/docs/how_to/chat_model_caching)."]}, {"cell_type": "code", "execution_count": null, "id": "bb40375d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}