{"cells": [{"cell_type": "markdown", "id": "b8982428", "metadata": {}, "source": ["# Run models locally\n", "\n", "## Use case\n", "\n", "The popularity of projects like [llama.cpp](https://github.com/ggerganov/llama.cpp), [Ollama](https://github.com/ollama/ollama), [GPT4All](https://github.com/nomic-ai/gpt4all), [llamafile](https://github.com/Mozilla-Ocho/llamafile), and others underscore the demand to run LLMs locally (on your own device).\n", "\n", "This has at least two important benefits:\n", "\n", "1. **Privacy**: Your data is not sent to a third party, and it is not subject to the terms of service of a commercial service\n", "2. **Cost**: There is no inference fee, which is important for token-intensive applications (e.g., [long-running simulations](https://twitter.com/RLanceMartin/status/1691097659262820352?s=20), summarization)\n", "\n", "## Overview\n", "\n", "Running an LLM locally requires a few things:\n", "\n", "1. **Open-source LLM**: An open-source LLM that can be freely modified and shared \n", "2. **Inference**: Ability to run this LLM on your device w/ acceptable latency\n", "\n", "### Open-source LLMs\n", "\n", "Users can now gain access to a rapidly growing set of [open-source LLMs](https://cameronrwolfe.substack.com/p/the-history-of-open-source-llms-better). \n", "\n", "These LLMs can be assessed across at least two dimensions (see figure):\n", " \n", "1. **Base model**: What is the base-model and how was it trained?\n", "2. **Fine-tuning approach**: Was the base-model fine-tuned and, if so, what [set of instructions](https://cameronrwolfe.substack.com/p/beyond-llama-the-power-of-open-llms#%C2%A7alpaca-an-instruction-following-llama-model) was used?\n", "\n", "![Image description](../../static/img/OSS_LLM_overview.png)\n", "\n", "The relative performance of these models can be assessed using several leaderboards, including:\n", "\n", "1. [LmSys](https://chat.lmsys.org/?arena)\n", "2. [GPT4All](https://gpt4all.io/index.html)\n", "3. [Hugging<PERSON><PERSON>](https://huggingface.co/spaces/lmsys/chatbot-arena-leaderboard)\n", "\n", "### Inference\n", "\n", "A few frameworks for this have emerged to support inference of open-source LLMs on various devices:\n", "\n", "1. [`llama.cpp`](https://github.com/ggerganov/llama.cpp): C++ implementation of llama inference code with [weight optimization / quantization](https://finbarr.ca/how-is-llama-cpp-possible/)\n", "2. [`gpt4all`](https://docs.gpt4all.io/index.html): Optimized C backend for inference\n", "3. [`ollama`](https://github.com/ollama/ollama): Bundles model weights and environment into an app that runs on device and serves the LLM\n", "4. [`llamafile`](https://github.com/Mozilla-Ocho/llamafile): Bundles model weights and everything needed to run the model in a single file, allowing you to run the LLM locally from this file without any additional installation steps\n", "\n", "In general, these frameworks will do a few things:\n", "\n", "1. **Quantization**: Reduce the memory footprint of the raw model weights\n", "2. **Efficient implementation for inference**: Support inference on consumer hardware (e.g., CPU or laptop GPU)\n", "\n", "In particular, see [this excellent post](https://finbarr.ca/how-is-llama-cpp-possible/) on the importance of quantization.\n", "\n", "![Image description](../../static/img/llama-memory-weights.png)\n", "\n", "With less precision, we radically decrease the memory needed to store the LLM in memory.\n", "\n", "In addition, we can see the importance of GPU memory bandwidth [sheet](https://docs.google.com/spreadsheets/d/1OehfHHNSn66BP2h3Bxp2NJTVX97icU0GmCXF6pK23H8/edit#gid=0)!\n", "\n", "A Mac M2 Max is 5-6x faster than a M1 for inference due to the larger GPU memory bandwidth.\n", "\n", "![Image description](../../static/img/llama_t_put.png)\n", "\n", "### Formatting prompts\n", "\n", "Some providers have [chat model](/docs/concepts/chat_models) wrappers that takes care of formatting your input prompt for the specific local model you're using. However, if you are prompting local models with a [text-in/text-out LLM](/docs/concepts/text_llms) wrapper, you may need to use a prompt tailored for your specific model.\n", "\n", "This can [require the inclusion of special tokens](https://huggingface.co/blog/llama2#how-to-prompt-llama-2). [Here's an example for LLaMA 2](https://smith.langchain.com/hub/rlm/rag-prompt-llama).\n", "\n", "## Quickstart\n", "\n", "[Ollama](https://ollama.com/) is one way to easily run inference on macOS.\n", " \n", "The instructions [here](https://github.com/ollama/ollama?tab=readme-ov-file#ollama) provide details, which we summarize:\n", " \n", "* [Download and run](https://ollama.ai/download) the app\n", "* From command line, fetch a model from this [list of options](https://ollama.com/search): e.g., `ollama pull gpt-oss:20b`\n", "* When the app is running, all models are automatically served on `localhost:11434`\n"]}, {"cell_type": "code", "execution_count": null, "id": "29450fc9", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain_ollama"]}, {"cell_type": "code", "execution_count": null, "id": "86178adb", "metadata": {}, "outputs": [{"data": {"text/plain": ["'...<PERSON>!\\n\\nOn July 20, 1969, <PERSON> became the first person to set foot on the lunar surface, famously declaring \"That\\'s one small step for man, one giant leap for mankind\" as he stepped off the lunar module Eagle onto the Moon\\'s surface.\\n\\nWould you like to know more about the Apollo 11 mission or <PERSON>\\'s achievements?'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_ollama import ChatOllama\n", "\n", "llm = ChatOllama(model=\"gpt-oss:20b\", validate_model_on_init=True)\n", "\n", "llm.invoke(\"The first man on the moon was ...\").content"]}, {"cell_type": "markdown", "id": "674cc672", "metadata": {}, "source": ["Stream tokens as they are being generated:"]}, {"cell_type": "code", "execution_count": 3, "id": "1386a852", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["...|"]}, {"name": "stdout", "output_type": "stream", "text": ["<PERSON>| <PERSON>|,| an| American| astronaut|.| He| stepped| out| of| the| lunar| module| Eagle| and| onto| the| surface| of| the| Moon| on| July| |20|,| |196|9|,| famously| declaring|:| \"|That|'s| one| small| step| for| man|,| one| giant| leap| for| mankind|.\"||"]}], "source": ["for chunk in llm.stream(\"The first man on the moon was ...\"):\n", "    print(chunk, end=\"|\", flush=True)"]}, {"cell_type": "markdown", "id": "e5731060", "metadata": {}, "source": ["Ollama also includes a chat model wrapper that handles formatting conversation turns:"]}, {"cell_type": "code", "execution_count": 4, "id": "f14a778a", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='The answer is a historic one!\\n\\nThe first man to walk on the Moon was <PERSON>, an American astronaut and commander of the Apollo 11 mission. On July 20, 1969, <PERSON> stepped out of the lunar module Eagle onto the surface of the Moon, famously declaring:\\n\\n\"That\\'s one small step for man, one giant leap for mankind.\"\\n\\nArmstrong was followed by fellow astronaut <PERSON>, who also walked on the Moon during the mission. <PERSON> remained in orbit around the Moon in the command module Columbia.\\n\\n<PERSON><PERSON> passed away on August 25, 2012, but his legacy as a pioneering astronaut and engineer continues to inspire people around the world!', response_metadata={'model': 'llama3.1:8b', 'created_at': '2024-08-01T00:38:29.176717Z', 'message': {'role': 'assistant', 'content': ''}, 'done_reason': 'stop', 'done': True, 'total_duration': 10681861417, 'load_duration': 34270292, 'prompt_eval_count': 19, 'prompt_eval_duration': 6209448000, 'eval_count': 141, 'eval_duration': 4432022000}, id='run-7bed57c5-7f54-4092-912c-ae49073dcd48-0', usage_metadata={'input_tokens': 19, 'output_tokens': 141, 'total_tokens': 160})"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_ollama import ChatOllama\n", "\n", "chat_model = ChatOllama(model=\"llama3.1:8b\")\n", "\n", "chat_model.invoke(\"Who was the first man on the moon?\")"]}, {"cell_type": "markdown", "id": "5cb27414", "metadata": {}, "source": ["## Environment\n", "\n", "Inference speed is a challenge when running models locally (see above).\n", "\n", "To minimize latency, it is desirable to run models locally on GPU, which ships with many consumer laptops [e.g., Apple devices](https://www.apple.com/newsroom/2022/06/apple-unveils-m2-with-breakthrough-performance-and-capabilities/).\n", "\n", "And even with GPU, the available GPU memory bandwidth (as noted above) is important.\n", "\n", "### Running Apple silicon GPU\n", "\n", "`ollama` and [`llamafile`](https://github.com/Mozilla-Ocho/llamafile?tab=readme-ov-file#gpu-support) will automatically utilize the GPU on Apple devices.\n", " \n", "Other frameworks require the user to set up the environment to utilize the Apple GPU.\n", "\n", "For example, `llama.cpp` python bindings can be configured to use the GPU via [Metal](https://developer.apple.com/metal/).\n", "\n", "Metal is a graphics and compute API created by Apple providing near-direct access to the GPU. \n", "\n", "See the [`llama.cpp`](/docs/integrations/llms/llamacpp) setup [here](https://github.com/abetlen/llama-cpp-python/blob/main/docs/install/macos.md) to enable this.\n", "\n", "In particular, ensure that conda is using the correct virtual environment that you created (`miniforge3`).\n", "\n", "e.g., for me:\n", "\n", "```shell\n", "conda activate /Users/<USER>/miniforge3/envs/llama\n", "```\n", "\n", "With the above confirmed, then:\n", "\n", "```shell\n", "CMAKE_ARGS=\"-DLLAMA_METAL=on\" FORCE_CMAKE=1 pip install -U llama-cpp-python --no-cache-dir\n", "```"]}, {"cell_type": "markdown", "id": "c382e79a", "metadata": {}, "source": ["## LLMs\n", "\n", "There are various ways to gain access to quantized model weights.\n", "\n", "1. [`HuggingFace`](https://huggingface.co/TheBloke) - Many quantized model are available for download and can be run with framework such as [`llama.cpp`](https://github.com/ggerganov/llama.cpp). You can also download models in [`llamafile` format](https://huggingface.co/models?other=llamafile) from HuggingFace.\n", "2. [`gpt4all`](https://gpt4all.io/index.html) - The model explorer offers a leaderboard of metrics and associated quantized models available for download \n", "3. [`ollama`](https://github.com/jmorganca/ollama) - Several models can be accessed directly via `pull`\n", "\n", "### <PERSON><PERSON><PERSON>\n", "\n", "With [Ollama](https://github.com/ollama/ollama), fetch a model via `ollama pull <model family>:<tag>`."]}, {"cell_type": "code", "execution_count": null, "id": "8ecd2f78", "metadata": {}, "outputs": [{"data": {"text/plain": ["' Sure! Here\\'s the answer, broken down step by step:\\n\\nThe first man on the moon was... <PERSON>.\\n\\nHere\\'s how I arrived at that answer:\\n\\n1. The first manned mission to land on the moon was Apollo 11.\\n2. The mission included three astronauts: <PERSON>, <PERSON> \"<PERSON>, and <PERSON>.\\n3. <PERSON> was the mission commander and the first person to set foot on the moon.\\n4. On July 20, 1969, <PERSON> stepped out of the lunar module Eagle and onto the moon\\'s surface, famously declaring \"That\\'s one small step for man, one giant leap for mankind.\"\\n\\nSo, the first man on the moon was <PERSON>!'"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["llm = ChatOllama(model=\"gpt-oss:20b\")\n", "llm.invoke(\"The first man on the moon was ... think step by step\")"]}, {"cell_type": "markdown", "id": "07c8c0d1", "metadata": {}, "source": ["### Llama.cpp\n", "\n", "Llama.cpp is compatible with a [broad set of models](https://github.com/ggerganov/llama.cpp).\n", "\n", "For example, below we run inference on `llama2-13b` with 4 bit quantization downloaded from [HuggingFace](https://huggingface.co/TheBloke/Llama-2-13B-GGML/tree/main).\n", "\n", "As noted above, see the [API reference](https://python.langchain.com/api_reference/langchain/llms/langchain.llms.llamacpp.LlamaCpp.html?highlight=llamacpp#langchain.llms.llamacpp.LlamaCpp) for the full set of parameters. \n", "\n", "From the [llama.cpp API reference docs](https://python.langchain.com/api_reference/community/llms/langchain_community.llms.llamacpp.LlamaCpp.html), a few are worth commenting on:\n", "\n", "`n_gpu_layers`: number of layers to be loaded into GPU memory\n", "\n", "* Value: 1\n", "* Meaning: Only one layer of the model will be loaded into GPU memory (1 is often sufficient).\n", "\n", "`n_batch`: number of tokens the model should process in parallel \n", "\n", "* Value: n_batch\n", "* Meaning: It's recommended to choose a value between 1 and n_ctx (which in this case is set to 2048)\n", "\n", "`n_ctx`: Token context window\n", "\n", "* Value: 2048\n", "* Meaning: The model will consider a window of 2048 tokens at a time\n", "\n", "`f16_kv`: whether the model should use half-precision for the key/value cache\n", "\n", "* Value: True\n", "* Meaning: The model will use half-precision, which can be more memory efficient; Metal only supports True."]}, {"cell_type": "code", "execution_count": null, "id": "5eba38dc", "metadata": {}, "outputs": [], "source": ["%env CMAKE_ARGS=\"-DLLAMA_METAL=on\"\n", "%env FORCE_CMAKE=1\n", "%pip install --upgrade --quiet  llama-cpp-python --no-cache-dir"]}, {"cell_type": "code", "execution_count": null, "id": "a88bf0c8-e989-4bcd-bcb7-4d7757e684f2", "metadata": {}, "outputs": [], "source": ["from langchain_community.llms import LlamaCpp\n", "from langchain_core.callbacks import CallbackManager, StreamingStdOutCallbackHandler\n", "\n", "llm = LlamaCpp(\n", "    model_path=\"/Users/<USER>/Desktop/Code/llama.cpp/models/openorca-platypus2-13b.gguf.q4_0.bin\",\n", "    n_gpu_layers=1,\n", "    n_batch=512,\n", "    n_ctx=2048,\n", "    f16_kv=True,\n", "    callback_manager=CallbackManager([StreamingStdOutCallbackHandler()]),\n", "    verbose=True,\n", ")"]}, {"cell_type": "markdown", "id": "f56f5168", "metadata": {}, "source": ["The console log will show the below to indicate Metal was enabled properly from steps above:\n", "```\n", "ggml_metal_init: allocating\n", "ggml_metal_init: using MPS\n", "```"]}, {"cell_type": "code", "execution_count": 45, "id": "7890a077", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Llama.generate: prefix-match hit\n"]}, {"name": "stdout", "output_type": "stream", "text": [" and use logical reasoning to figure out who the first man on the moon was.\n", "\n", "Here are some clues:\n", "\n", "1. The first man on the moon was an American.\n", "2. He was part of the Apollo 11 mission.\n", "3. He stepped out of the lunar module and became the first person to set foot on the moon's surface.\n", "4. His last name is <PERSON>.\n", "\n", "Now, let's use our reasoning skills to figure out who the first man on the moon was. Based on clue #1, we know that the first man on the moon was an American. <PERSON>lue #2 tells us that he was part of the Apollo 11 mission. <PERSON>lue #3 reveals that he was the first person to set foot on the moon's surface. And finally, clue #4 gives us his last name: <PERSON>.\n", "Therefore, the first man on the moon was <PERSON>!"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "llama_print_timings:        load time =  9623.21 ms\n", "llama_print_timings:      sample time =   143.77 ms /   203 runs   (    0.71 ms per token,  1412.01 tokens per second)\n", "llama_print_timings: prompt eval time =   485.94 ms /     7 tokens (   69.42 ms per token,    14.40 tokens per second)\n", "llama_print_timings:        eval time =  6385.16 ms /   202 runs   (   31.61 ms per token,    31.64 tokens per second)\n", "llama_print_timings:       total time =  7279.28 ms\n"]}, {"data": {"text/plain": ["\" and use logical reasoning to figure out who the first man on the moon was.\\n\\nHere are some clues:\\n\\n1. The first man on the moon was an American.\\n2. He was part of the Apollo 11 mission.\\n3. He stepped out of the lunar module and became the first person to set foot on the moon's surface.\\n4. His last name is <PERSON>.\\n\\nNow, let's use our reasoning skills to figure out who the first man on the moon was. Based on clue #1, we know that the first man on the moon was an American. Clue #2 tells us that he was part of the Apollo 11 mission. <PERSON>lue #3 reveals that he was the first person to set foot on the moon's surface. And finally, clue #4 gives us his last name: <PERSON>.\\nTherefore, the first man on the moon was <PERSON>!\""]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.invoke(\"The first man on the moon was ... Let's think step by step\")"]}, {"cell_type": "markdown", "id": "831ddf7c", "metadata": {}, "source": ["### GPT4All\n", "\n", "We can use model weights downloaded from [GPT4All](/docs/integrations/llms/gpt4all) model explorer.\n", "\n", "Similar to what is shown above, we can run inference and use [the API reference](https://python.langchain.com/api_reference/community/llms/langchain_community.llms.gpt4all.GPT4All.html) to set parameters of interest."]}, {"cell_type": "code", "execution_count": null, "id": "e27baf6e", "metadata": {}, "outputs": [], "source": ["%pip install gpt4all"]}, {"cell_type": "code", "execution_count": null, "id": "915ecd4c-8f6b-4de3-a787-b64cb7c682b4", "metadata": {}, "outputs": [], "source": ["from langchain_community.llms import GPT4All\n", "\n", "llm = GPT4All(\n", "    model=\"/Users/<USER>/Desktop/Code/gpt4all/models/nous-hermes-13b.ggmlv3.q4_0.bin\"\n", ")"]}, {"cell_type": "code", "execution_count": 47, "id": "e3d4526f", "metadata": {}, "outputs": [{"data": {"text/plain": ["\".\\n1) The United States decides to send a manned mission to the moon.2) They choose their best astronauts and train them for this specific mission.3) They build a spacecraft that can take humans to the moon, called the Lunar Module (LM).4) They also create a larger spacecraft, called the Saturn V rocket, which will launch both the LM and the Command Service Module (CSM), which will carry the astronauts into orbit.5) The mission is planned down to the smallest detail: from the trajectory of the rockets to the exact movements of the astronauts during their moon landing.6) On July 16, 1969, the Saturn V rocket launches from Kennedy Space Center in Florida, carrying the Apollo 11 mission crew into space.7) After one and a half orbits around the Earth, the LM separates from the CSM and begins its descent to the moon's surface.8) On July 20, 1969, at 2:56 pm EDT (GMT-4), <PERSON> becomes the first man on the moon. He speaks these\""]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.invoke(\"The first man on the moon was ... Let's think step by step\")"]}, {"cell_type": "markdown", "id": "056854e2-5e4b-4a03-be7e-03192e5c4e1e", "metadata": {}, "source": ["### llamafile\n", "\n", "One of the simplest ways to run an LLM locally is using a [llamafile](https://github.com/Mozilla-Ocho/llamafile). All you need to do is:\n", "\n", "1) Download a llamafile from [HuggingFace](https://huggingface.co/models?other=llamafile)\n", "2) Make the file executable\n", "3) Run the file\n", "\n", "llamafiles bundle model weights and a [specially-compiled](https://github.com/Mozilla-Ocho/llamafile?tab=readme-ov-file#technical-details) version of [`llama.cpp`](https://github.com/ggerganov/llama.cpp) into a single file that can run on most computers without any additional dependencies. They also come with an embedded inference server that provides an [API](https://github.com/Mozilla-Ocho/llamafile/blob/main/llama.cpp/server/README.md#api-endpoints) for interacting with your model. \n", "\n", "Here's a simple bash script that shows all 3 setup steps:\n", "\n", "```bash\n", "# Download a llamafile from HuggingFace\n", "wget https://huggingface.co/jartine/TinyLlama-1.1B-Chat-v1.0-GGUF/resolve/main/TinyLlama-1.1B-Chat-v1.0.Q5_K_M.llamafile\n", "\n", "# Make the file executable. On Windows, instead just rename the file to end in \".exe\".\n", "chmod +x TinyLlama-1.1B-Chat-v1.0.Q5_<PERSON>_<PERSON>.llama<PERSON>le\n", "\n", "# Start the model server. Listens at http://localhost:8080 by default.\n", "./TinyLlama-1.1B-Chat-v1.0.Q5_K_<PERSON>.llamafile --server --nobrowser\n", "```\n", "\n", "After you run the above setup steps, you can use <PERSON><PERSON><PERSON><PERSON> to interact with your model:"]}, {"cell_type": "code", "execution_count": 1, "id": "002e655c-ba18-4db3-ac7b-f33e825d14b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"\\nFirstly, let's imagine the scene where <PERSON> stepped onto the moon. This happened in 1969. The first man on the moon was <PERSON>. We already know that.\\n2nd, let's take a step back. <PERSON> didn't have any special powers. He had to land his spacecraft safely on the moon without injuring anyone or causing any damage. If he failed to do this, he would have been killed along with all those people who were on board the spacecraft.\\n3rd, let's imagine that <PERSON> successfully landed his spacecraft on the moon and made it back to Earth safely. The next step was for him to be hailed as a hero by his people back home. It took years before <PERSON> became an American hero.\\n4th, let's take another step back. Let's imagine that <PERSON> wasn't hailed as a hero, and instead, he was just forgotten. This happened in the 1970s. <PERSON> wasn't recognized for his remarkable achievement on the moon until after he died.\\n5th, let's take another step back. Let's imagine that <PERSON> didn't die in the 1970s and instead, lived to be a hundred years old. This happened in 2036. In the year 2036, <PERSON> would have been a centenarian.\\nNow, let's think about the present. <PERSON> is still alive. He turned 95 years old on July 20th, 2018. If he were to die now, his achievement of becoming the first human being to set foot on the moon would remain an unforgettable moment in history.\\nI hope this helps you understand the significance and importance of <PERSON>'s achievement on the moon!\""]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.llms.llamafile import Llamafile\n", "\n", "llm = Llamafile()\n", "\n", "llm.invoke(\"The first man on the moon was ... Let's think step by step.\")"]}, {"cell_type": "markdown", "id": "6b84e543", "metadata": {}, "source": ["## Prompts\n", "\n", "Some LLMs will benefit from specific prompts.\n", "\n", "For example, LLaMA will use [special tokens](https://twitter.com/RLanceMartin/status/1681879318493003776?s=20).\n", "\n", "We can use `ConditionalPromptSelector` to set prompt based on the model type."]}, {"cell_type": "code", "execution_count": null, "id": "16759b7c-7903-4269-b7b4-f83b313d8091", "metadata": {}, "outputs": [], "source": ["# Set our LLM\n", "llm = LlamaCpp(\n", "    model_path=\"/Users/<USER>/Desktop/Code/llama.cpp/models/openorca-platypus2-13b.gguf.q4_0.bin\",\n", "    n_gpu_layers=1,\n", "    n_batch=512,\n", "    n_ctx=2048,\n", "    f16_kv=True,\n", "    callback_manager=CallbackManager([StreamingStdOutCallbackHandler()]),\n", "    verbose=True,\n", ")"]}, {"cell_type": "markdown", "id": "66656084", "metadata": {}, "source": ["Set the associated prompt based upon the model version."]}, {"cell_type": "code", "execution_count": 58, "id": "8555f5bf", "metadata": {}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['question'], output_parser=None, partial_variables={}, template='<<SYS>> \\n You are an assistant tasked with improving Google search results. \\n <</SYS>> \\n\\n [INST] Generate THREE Google search queries that are similar to this question. The output should be a numbered list of questions and each should have a question mark at the end: \\n\\n {question} [/INST]', template_format='f-string', validate_template=True)"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chains.prompt_selector import ConditionalPromptSelector\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "DEFAULT_LLAMA_SEARCH_PROMPT = PromptTemplate(\n", "    input_variables=[\"question\"],\n", "    template=\"\"\"<<SYS>> \\n You are an assistant tasked with improving Google search \\\n", "results. \\n <</SYS>> \\n\\n [INST] Generate THREE Google search queries that \\\n", "are similar to this question. The output should be a numbered list of questions \\\n", "and each should have a question mark at the end: \\n\\n {question} [/INST]\"\"\",\n", ")\n", "\n", "DEFAULT_SEARCH_PROMPT = PromptTemplate(\n", "    input_variables=[\"question\"],\n", "    template=\"\"\"You are an assistant tasked with improving Google search \\\n", "results. Generate THREE Google search queries that are similar to \\\n", "this question. The output should be a numbered list of questions and each \\\n", "should have a question mark at the end: {question}\"\"\",\n", ")\n", "\n", "QUESTION_PROMPT_SELECTOR = ConditionalPromptSelector(\n", "    default_prompt=DEFAULT_SEARCH_PROMPT,\n", "    conditionals=[(lambda llm: isinstance(llm, LlamaCpp), DEFAULT_LLAMA_SEARCH_PROMPT)],\n", ")\n", "\n", "prompt = QUESTION_PROMPT_SELECTOR.get_prompt(llm)\n", "prompt"]}, {"cell_type": "code", "execution_count": 59, "id": "d0aedfd2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Sure! Here are three similar search queries with a question mark at the end:\n", "\n", "1. Which NBA team did <PERSON><PERSON><PERSON> lead to a championship in the year he was drafted?\n", "2. Who won the Grammy Awards for Best New Artist and Best Female Pop Vocal Performance in the same year that <PERSON> was born?\n", "3. What MLB team did <PERSON> play for when he hit 60 home runs in a single season?"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "llama_print_timings:        load time = 14943.19 ms\n", "llama_print_timings:      sample time =    72.93 ms /   101 runs   (    0.72 ms per token,  1384.87 tokens per second)\n", "llama_print_timings: prompt eval time = 14942.95 ms /    93 tokens (  160.68 ms per token,     6.22 tokens per second)\n", "llama_print_timings:        eval time =  3430.85 ms /   100 runs   (   34.31 ms per token,    29.15 tokens per second)\n", "llama_print_timings:       total time = 18578.26 ms\n"]}, {"data": {"text/plain": ["'  Sure! Here are three similar search queries with a question mark at the end:\\n\\n1. Which NBA team did <PERSON><PERSON><PERSON> lead to a championship in the year he was drafted?\\n2. Who won the Grammy Awards for Best New Artist and Best Female Pop Vocal Performance in the same year that <PERSON> was born?\\n3. What MLB team did <PERSON> Ruth play for when he hit 60 home runs in a single season?'"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["# Chain\n", "chain = prompt | llm\n", "question = \"What NFL team won the Super Bowl in the year that <PERSON> was born?\"\n", "chain.invoke({\"question\": question})"]}, {"cell_type": "markdown", "id": "6e0d37e7-f1d9-4848-bf2c-c22392ee141f", "metadata": {}, "source": ["We also can use the LangChain Prompt Hub to fetch and / or store prompts that are model specific.\n", "\n", "This will work with your [LangSmith API key](https://docs.smith.langchain.com/).\n", "\n", "For example, [here](https://smith.langchain.com/hub/rlm/rag-prompt-llama) is a prompt for RAG with LLaMA-specific tokens."]}, {"cell_type": "markdown", "id": "6ba66260", "metadata": {}, "source": ["## Use cases\n", "\n", "Given an `llm` created from one of the models above, you can use it for [many use cases](/docs/how_to#use-cases).\n", "\n", "For example, you can implement a [RAG application](/docs/tutorials/rag) using the chat models demonstrated here.\n", "\n", "In general, use cases for local LLMs can be driven by at least two factors:\n", "\n", "* **Privacy**: private data (e.g., journals, etc) that a user does not want to share \n", "* **Cost**: text preprocessing (extraction/tagging), summarization, and agent simulations are token-use-intensive tasks\n", "\n", "In addition, [here](https://blog.langchain.dev/using-langsmith-to-support-fine-tuning-of-open-source-llms/) is an overview on fine-tuning, which can utilize open-source LLMs."]}, {"cell_type": "markdown", "id": "14c2c170", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}