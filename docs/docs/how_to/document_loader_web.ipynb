{"cells": [{"cell_type": "markdown", "id": "7414502a-4532-4da3-aef0-71aac4d0d4dd", "metadata": {}, "source": ["# How to load web pages\n", "\n", "This guide covers how to [load](/docs/concepts/document_loaders/) web pages into the LangChain [Document](https://python.langchain.com/api_reference/core/documents/langchain_core.documents.base.Document.html) format that we use downstream. Web pages contain text, images, and other multimedia elements, and are typically represented with HTML. They may include links to other pages or resources.\n", "\n", "<PERSON><PERSON><PERSON><PERSON> integrates with a host of parsers that are appropriate for web pages. The right parser will depend on your needs. Below we demonstrate two possibilities:\n", "\n", "- [Simple and fast](/docs/how_to/document_loader_web#simple-and-fast-text-extraction) parsing, in which we recover one `Document` per web page with its content represented as a \"flattened\" string;\n", "- [Advanced](/docs/how_to/document_loader_web#advanced-parsing) parsing, in which we recover multiple `Document` objects per page, allowing one to identify and traverse sections, links, tables, and other structures.\n", "\n", "## Setup\n", "\n", "For the \"simple and fast\" parsing, we will need `langchain-community` and the `beautifulsoup4` library:"]}, {"cell_type": "code", "execution_count": null, "id": "89bc7be9-ab50-4c5a-860a-deee7b469f67", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-community beautifulsoup4"]}, {"cell_type": "markdown", "id": "a07f5ca3-e2b7-4d9c-b1f2-7547856cbdf7", "metadata": {}, "source": ["For advanced parsing, we will use `langchain-unstructured`:"]}, {"cell_type": "code", "execution_count": null, "id": "8a3ef1fc-dfde-4814-b7f6-b6c0c649f044", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-unstructured"]}, {"cell_type": "markdown", "id": "4ef11005-1bd0-43a3-8d52-ea823c830c34", "metadata": {}, "source": ["## Simple and fast text extraction\n", "\n", "If you are looking for a simple string representation of text that is embedded in a web page, the method below is appropriate. It will return a list of `Document` objects -- one per page -- containing a single string of the page's text. Under the hood it uses the `beautifulsoup4` Python library.\n", "\n", "LangChain document loaders implement `lazy_load` and its async variant, `alazy_load`, which return iterators of `Document objects`. We will use these below."]}, {"cell_type": "code", "execution_count": 1, "id": "7faeccbc-4e56-4b88-99db-2274ed0680c1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["USER_AGENT environment variable not set, consider setting it to identify your requests.\n"]}], "source": ["import bs4\n", "from langchain_community.document_loaders import WebBaseLoader\n", "\n", "page_url = \"https://python.langchain.com/docs/how_to/chatbots_memory/\"\n", "\n", "loader = WebBaseLoader(web_paths=[page_url])\n", "docs = []\n", "async for doc in loader.alazy_load():\n", "    docs.append(doc)\n", "\n", "assert len(docs) == 1\n", "doc = docs[0]"]}, {"cell_type": "code", "execution_count": 2, "id": "21199a0d-3bd2-4410-a060-763649b14691", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'source': 'https://python.langchain.com/docs/how_to/chatbots_memory/', 'title': 'How to add memory to chatbots | \\uf8ffü¶úÔ∏è\\uf8ffüî<PERSON>', 'description': 'A key feature of chatbots is their ability to use content of previous conversation turns as context. This state management can take several forms, including:', 'language': 'en'}\n", "\n", "How to add memory to chatbots | ü¶úÔ∏èüîó <PERSON><PERSON><PERSON>n\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "Skip to main contentShare your thoughts on AI agents. Take the 3-min survey.IntegrationsAPI ReferenceMoreContributingPeopleLangSmithLangGraphLangChain HubLangChain JS/TSv0.3v0.3v0.2v0.1üí¨SearchIntroductionTutorialsBuild a Question Answering application over a Graph DatabaseTutorialsBuild a Simple LLM Application with LCELBuild a Query Analysis SystemBuild a ChatbotConversational RAGBuild an Extraction ChainBuild an AgentTaggingd\n"]}], "source": ["print(f\"{doc.metadata}\\n\")\n", "print(doc.page_content[:500].strip())"]}, {"cell_type": "markdown", "id": "23189e91-5237-4a9e-a4bb-cb79e130c364", "metadata": {}, "source": ["This is essentially a dump of the text from the page's HTML. It may contain extraneous information like headings and navigation bars. If you are familiar with the expected HTML, you can specify desired `<div>` classes and other parameters via BeautifulSoup. Below we parse only the body text of the article:"]}, {"cell_type": "code", "execution_count": 3, "id": "4211b1a6-e636-415b-a556-ae01969399a7", "metadata": {}, "outputs": [], "source": ["loader = WebBaseLoader(\n", "    web_paths=[page_url],\n", "    bs_kwargs={\n", "        \"parse_only\": bs4.<PERSON><PERSON>Strainer(class_=\"theme-doc-markdown markdown\"),\n", "    },\n", "    bs_get_text_kwargs={\"separator\": \" | \", \"strip\": True},\n", ")\n", "\n", "docs = []\n", "async for doc in loader.alazy_load():\n", "    docs.append(doc)\n", "\n", "assert len(docs) == 1\n", "doc = docs[0]"]}, {"cell_type": "code", "execution_count": 4, "id": "7edf6ed0-e22f-4c64-b986-8ba019c14757", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'source': 'https://python.langchain.com/docs/how_to/chatbots_memory/'}\n", "\n", "How to add memory to chatbots | A key feature of chatbots is their ability to use content of previous conversation turns as context. This state management can take several forms, including: | Simply stuffing previous messages into a chat model prompt. | The above, but trimming old messages to reduce the amount of distracting information the model has to deal with. | More complex modifications like synthesizing summaries for long running conversations. | We'll go into more detail on a few techniq\n"]}], "source": ["print(f\"{doc.metadata}\\n\")\n", "print(doc.page_content[:500])"]}, {"cell_type": "code", "execution_count": 5, "id": "6ab1ba2b-3b22-4c5d-8ad3-f6809d075d26", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a greeting. <PERSON><PERSON><PERSON> then asks the AI how it is doing, and the AI responds that it is fine.'), | HumanMessage(content='What did I say my name was?'), | AIMessage(content='You introduced yourself as <PERSON><PERSON><PERSON>. How can I assist you today, Nemo?')] | Note that invoking the chain again will generate another summary generated from the initial summary plus new messages and so on. You could also design a hybrid approach where a certain number of messages are retained in chat history while others are summarized.\n"]}], "source": ["print(doc.page_content[-500:])"]}, {"cell_type": "markdown", "id": "0a411144-a234-4505-956c-930d399ffefb", "metadata": {}, "source": ["Note that this required advance technical knowledge of how the body text is represented in the underlying HTML.\n", "\n", "We can parameterize `WebBaseLoader` with a variety of settings, allowing for specification of request headers, rate limits, and parsers and other kwargs for BeautifulSoup. See its [API reference](https://python.langchain.com/api_reference/community/document_loaders/langchain_community.document_loaders.web_base.WebBaseLoader.html) for detail."]}, {"cell_type": "markdown", "id": "cdfc8f68-2b08-4a6c-9705-c17e6deaf411", "metadata": {}, "source": ["## Advanced parsing\n", "\n", "This method is appropriate if we want more granular control or processing of the page content. Below, instead of generating one `Document` per page and controlling its content via BeautifulSoup, we generate multiple `Document` objects representing distinct structures on a page. These structures can include section titles and their corresponding body texts, lists or enumerations, tables, and more.\n", "\n", "Under the hood it uses the `langchain-unstructured` library. See the [integration docs](/docs/integrations/document_loaders/unstructured_file/) for more information about using [Unstructured](https://docs.unstructured.io/welcome) with <PERSON><PERSON><PERSON><PERSON>."]}, {"cell_type": "code", "execution_count": 6, "id": "7a6bbfef-ebd5-4357-a7f5-9c989dda092d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: Note: NumExpr detected 12 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO: NumExpr defaulting to 8 threads.\n"]}], "source": ["from langchain_unstructured import UnstructuredLoader\n", "\n", "page_url = \"https://python.langchain.com/docs/how_to/chatbots_memory/\"\n", "loader = UnstructuredLoader(web_url=page_url)\n", "\n", "docs = []\n", "async for doc in loader.alazy_load():\n", "    docs.append(doc)"]}, {"cell_type": "markdown", "id": "53a600b0-fcd2-4074-80b6-a1dd2c0d9235", "metadata": {}, "source": ["Note that with no advance knowledge of the page HTML structure, we recover a natural organization of the body text:"]}, {"cell_type": "code", "execution_count": 7, "id": "198b7469-587f-4a80-a49f-440e6157b241", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["How to add memory to chatbots\n", "A key feature of chatbots is their ability to use content of previous conversation turns as context. This state management can take several forms, including:\n", "Simply stuffing previous messages into a chat model prompt.\n", "The above, but trimming old messages to reduce the amount of distracting information the model has to deal with.\n", "More complex modifications like synthesizing summaries for long running conversations.\n", "ERROR! Session/line number was not unique in database. History logging moved to new session 2747\n"]}], "source": ["for doc in docs[:5]:\n", "    print(doc.page_content)"]}, {"cell_type": "markdown", "id": "3f4254b5-5e3b-45c4-9cd0-7ed753687783", "metadata": {}, "source": ["### Extracting content from specific sections"]}, {"cell_type": "markdown", "id": "627d9b7a-31fe-4923-bc9a-4b0caae1d760", "metadata": {}, "source": ["Each `Document` object represents an element of the page. Its metadata contains useful information, such as its category:"]}, {"cell_type": "code", "execution_count": 8, "id": "2fa19a61-a53d-42e0-a01a-7ea99fc40810", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Title: How to add memory to chatbots\n", "NarrativeText: A key feature of chatbots is their ability to use content of previous conversation turns as context. This state management can take several forms, including:\n", "ListItem: Simply stuffing previous messages into a chat model prompt.\n", "ListItem: The above, but trimming old messages to reduce the amount of distracting information the model has to deal with.\n", "ListItem: More complex modifications like synthesizing summaries for long running conversations.\n"]}], "source": ["for doc in docs[:5]:\n", "    print(f\"{doc.metadata['category']}: {doc.page_content}\")"]}, {"cell_type": "markdown", "id": "ca0f8025-58b8-4d2a-95aa-124d7a8ee812", "metadata": {}, "source": ["Elements may also have parent-child relationships -- for example, a paragraph might belong to a section with a title. If a section is of particular interest (e.g., for indexing) we can isolate the corresponding `Document` objects.\n", "\n", "As an example, below we load the content of the \"Setup\" sections for two web pages:"]}, {"cell_type": "code", "execution_count": 9, "id": "793018fd-1365-4a8a-8690-6d51dad2e1cf", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from langchain_core.documents import Document\n", "\n", "\n", "async def _get_setup_docs_from_url(url: str) -> List[Document]:\n", "    loader = UnstructuredLoader(web_url=url)\n", "\n", "    setup_docs = []\n", "    parent_id = -1\n", "    async for doc in loader.alazy_load():\n", "        if doc.metadata[\"category\"] == \"Title\" and doc.page_content.startswith(\"Setup\"):\n", "            parent_id = doc.metadata[\"element_id\"]\n", "        if doc.metadata.get(\"parent_id\") == parent_id:\n", "            setup_docs.append(doc)\n", "\n", "    return setup_docs\n", "\n", "\n", "page_urls = [\n", "    \"https://python.langchain.com/docs/how_to/chatbots_memory/\",\n", "    \"https://python.langchain.com/docs/how_to/chatbots_tools/\",\n", "]\n", "setup_docs = []\n", "for url in page_urls:\n", "    page_setup_docs = await _get_setup_docs_from_url(url)\n", "    setup_docs.extend(page_setup_docs)"]}, {"cell_type": "code", "execution_count": 10, "id": "a67e0745-abfc-4baa-94b3-2e8815bfa52a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'https://python.langchain.com/docs/how_to/chatbots_memory/': \"You'll need to install a few packages, and have your OpenAI API key set as an environment variable named OPENAI_API_KEY:\\n%pip install --upgrade --quiet langchain langchain-openai\\n\\n# Set env var OPENAI_API_KEY or load from a .env file:\\nimport dotenv\\n\\ndotenv.load_dotenv()\\n[33mWARNING: You are using pip version 22.0.4; however, version 23.3.2 is available.\\nYou should consider upgrading via the '/Users/<USER>/.pyenv/versions/3.10.5/bin/python -m pip install --upgrade pip' command.[0m[33m\\n[0mNote: you may need to restart the kernel to use updated packages.\\n\",\n", " 'https://python.langchain.com/docs/how_to/chatbots_tools/': \"For this guide, we'll be using a tool calling agent with a single tool for searching the web. The default will be powered by <PERSON><PERSON>, but you can switch it out for any similar tool. The rest of this section will assume you're using <PERSON><PERSON>.\\nYou'll need to sign up for an account on the Tavily website, and install the following packages:\\n%pip install --upgrade --quiet langchain-community langchain-openai tavily-python\\n\\n# Set env var OPENAI_API_KEY or load from a .env file:\\nimport dotenv\\n\\ndotenv.load_dotenv()\\nYou will also need your OpenAI key set as OPENAI_API_KEY and your Tavily API key set as TAVILY_API_KEY.\\n\"}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from collections import defaultdict\n", "\n", "setup_text = defaultdict(str)\n", "\n", "for doc in setup_docs:\n", "    url = doc.metadata[\"url\"]\n", "    setup_text[url] += f\"{doc.page_content}\\n\"\n", "\n", "dict(setup_text)"]}, {"cell_type": "markdown", "id": "5cd42892-24a6-4969-92c8-c928680be9b5", "metadata": {}, "source": ["### Vector search over page content\n", "\n", "Once we have loaded the page contents into LangChain `Document` objects, we can index them (e.g., for a RAG application) in the usual way. Below we use OpenAI [embeddings](/docs/concepts/embedding_models), although any LangChain embeddings model will suffice."]}, {"cell_type": "code", "execution_count": null, "id": "5a6cbb01-6e0d-418f-9f76-2031622bebb0", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-openai"]}, {"cell_type": "code", "execution_count": 11, "id": "598e612c-180d-494d-8caa-761c89f84eae", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if \"OPENAI_API_KEY\" not in os.environ:\n", "    os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"OpenAI API Key:\")"]}, {"cell_type": "code", "execution_count": 12, "id": "5eeaeb54-ea03-4634-8a79-b60c22ab2b66", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO: HTTP Request: POST https://api.openai.com/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Page https://python.langchain.com/docs/how_to/chatbots_tools/: You'll need to sign up for an account on the Tavily website, and install the following packages:\n", "\n", "Page https://python.langchain.com/docs/how_to/chatbots_tools/: For this guide, we'll be using a tool calling agent with a single tool for searching the web. The default will be powered by <PERSON><PERSON>, but you can switch it out for any similar tool. The rest of this section will assume you're using <PERSON><PERSON>.\n", "\n"]}], "source": ["from langchain_core.vectorstores import InMemoryVectorStore\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "vector_store = InMemoryVectorStore.from_documents(setup_docs, OpenAIEmbeddings())\n", "retrieved_docs = vector_store.similarity_search(\"Install Tavily\", k=2)\n", "for doc in retrieved_docs:\n", "    print(f\"Page {doc.metadata['url']}: {doc.page_content[:300]}\\n\")"]}, {"cell_type": "markdown", "id": "67be9c94-dbde-4fdd-87d0-e83ed6066d2b", "metadata": {}, "source": ["## Other web page loaders\n", "\n", "For a list of available LangChain web page loaders, please see [this table](/docs/integrations/document_loaders/#webpages)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}