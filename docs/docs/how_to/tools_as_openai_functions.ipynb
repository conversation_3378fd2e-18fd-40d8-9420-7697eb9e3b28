{"cells": [{"cell_type": "markdown", "id": "4111c9d4", "metadata": {}, "source": ["# How to convert tools to OpenAI Functions\n", "\n", "This notebook goes over how to use Lang<PERSON>hain [tools](/docs/concepts/tools/) as OpenAI functions."]}, {"cell_type": "code", "execution_count": null, "id": "bb220019-4012-4da4-bfee-01fb8189aa49", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain-community langchain-openai"]}, {"cell_type": "code", "execution_count": 19, "id": "d65d8a60", "metadata": {}, "outputs": [], "source": ["from langchain_community.tools import MoveFileTool\n", "from langchain_core.messages import HumanMessage\n", "from langchain_core.utils.function_calling import convert_to_openai_function\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "code", "execution_count": 20, "id": "abd8dc72", "metadata": {}, "outputs": [], "source": ["model = ChatOpenAI(model=\"gpt-3.5-turbo\")"]}, {"cell_type": "code", "execution_count": 21, "id": "3b3dc766", "metadata": {}, "outputs": [], "source": ["tools = [MoveFileTool()]\n", "functions = [convert_to_openai_function(t) for t in tools]"]}, {"cell_type": "code", "execution_count": 12, "id": "d38c4a22-2e9e-4d15-a9e1-bf8103c6303b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': 'move_file',\n", " 'description': 'Move or rename a file from one location to another',\n", " 'parameters': {'type': 'object',\n", "  'properties': {'source_path': {'description': 'Path of the file to move',\n", "    'type': 'string'},\n", "   'destination_path': {'description': 'New path for the moved file',\n", "    'type': 'string'}},\n", "  'required': ['source_path', 'destination_path']}}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["functions[0]"]}, {"cell_type": "code", "execution_count": 15, "id": "230a7939", "metadata": {}, "outputs": [], "source": ["message = model.invoke(\n", "    [HumanMessage(content=\"move file foo to bar\")], functions=functions\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "c118c940", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', additional_kwargs={'function_call': {'arguments': '{\\n  \"source_path\": \"foo\",\\n  \"destination_path\": \"bar\"\\n}', 'name': 'move_file'}})"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["message"]}, {"cell_type": "code", "execution_count": 8, "id": "d618e3d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': 'move_file',\n", " 'arguments': '{\\n  \"source_path\": \"foo\",\\n  \"destination_path\": \"bar\"\\n}'}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["message.additional_kwargs[\"function_call\"]"]}, {"cell_type": "markdown", "id": "77dd0d9f-2f24-4535-a658-a061f91e009a", "metadata": {}, "source": ["With OpenAI chat models we can also automatically bind and convert function-like objects with `bind_functions`"]}, {"cell_type": "code", "execution_count": 17, "id": "24bb1518-8100-4ac3-acea-04acfac963d1", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', additional_kwargs={'function_call': {'arguments': '{\\n  \"source_path\": \"foo\",\\n  \"destination_path\": \"bar\"\\n}', 'name': 'move_file'}})"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["model_with_functions = model.bind_functions(tools)\n", "model_with_functions.invoke([HumanMessage(content=\"move file foo to bar\")])"]}, {"cell_type": "markdown", "id": "000ec6ff-ca67-4206-ba56-cc2a91b85ce6", "metadata": {}, "source": ["Or we can use the update OpenAI API that uses `tools` and `tool_choice` instead of `functions` and `function_call` by using `ChatOpenAI.bind_tools`:"]}, {"cell_type": "code", "execution_count": 18, "id": "1a333e4e-df55-4e15-9d2e-4fd142d969f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_btkY3xV71cEVAOHnNa5qwo44', 'function': {'arguments': '{\\n  \"source_path\": \"foo\",\\n  \"destination_path\": \"bar\"\\n}', 'name': 'move_file'}, 'type': 'function'}]})"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["model_with_tools = model.bind_tools(tools)\n", "model_with_tools.invoke([HumanMessage(content=\"move file foo to bar\")])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.1"}}, "nbformat": 4, "nbformat_minor": 5}