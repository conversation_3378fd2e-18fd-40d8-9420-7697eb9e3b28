{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to attach callbacks to a runnable\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Callbacks](/docs/concepts/callbacks)\n", "- [Custom callback handlers](/docs/how_to/custom_callbacks)\n", "- [Chaining runnables](/docs/how_to/sequence)\n", "- [Attach runtime arguments to a Runnable](/docs/how_to/binding)\n", "\n", ":::\n", "\n", "If you are composing a chain of runnables and want to reuse callbacks across multiple executions, you can attach callbacks with the [`.with_config()`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.Runnable.html#langchain_core.runnables.base.Runnable.with_config) method. This saves you the need to pass callbacks in each time you invoke the chain.\n", "\n", ":::important\n", "\n", "`with_config()` binds a configuration which will be interpreted as **runtime** configuration. So these callbacks will propagate to all child components.\n", ":::\n", "\n", "Here's an example:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "%pip install -qU langchain langchain_anthropic\n", "\n", "import getpass\n", "import os\n", "\n", "os.environ[\"ANTHROPIC_API_KEY\"] = getpass.getpass()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Error in LoggingHandler.on_chain_start callback: AttributeError(\"'NoneType' object has no attribute 'get'\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Chain ChatPromptTemplate started\n", "Chain ended, outputs: messages=[HumanMessage(content='What is 1 + 2?', additional_kwargs={}, response_metadata={})]\n", "Chat model started\n", "Chat model ended, response: generations=[[ChatGeneration(text='The sum of 1 + 2 is 3.', message=AIMessage(content='The sum of 1 + 2 is 3.', additional_kwargs={}, response_metadata={'id': 'msg_01F1qPrmBD9igfzHdqVipmKX', 'model': 'claude-3-7-sonnet-20250219', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'cache_creation_input_tokens': 0, 'cache_read_input_tokens': 0, 'input_tokens': 16, 'output_tokens': 17, 'server_tool_use': None, 'service_tier': 'standard'}, 'model_name': 'claude-3-7-sonnet-20250219'}, id='run--71edddf3-2474-42dc-ad43-fadb4882c3c8-0', usage_metadata={'input_tokens': 16, 'output_tokens': 17, 'total_tokens': 33, 'input_token_details': {'cache_read': 0, 'cache_creation': 0}}))]] llm_output={'id': 'msg_01F1qPrmBD9igfzHdqVipmKX', 'model': 'claude-3-7-sonnet-20250219', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'cache_creation_input_tokens': 0, 'cache_read_input_tokens': 0, 'input_tokens': 16, 'output_tokens': 17, 'server_tool_use': None, 'service_tier': 'standard'}, 'model_name': 'claude-3-7-sonnet-20250219'} run=None type='LLMResult'\n", "Chain ended, outputs: content='The sum of 1 + 2 is 3.' additional_kwargs={} response_metadata={'id': 'msg_01F1qPrmBD9igfzHdqVipmKX', 'model': 'claude-3-7-sonnet-20250219', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'cache_creation_input_tokens': 0, 'cache_read_input_tokens': 0, 'input_tokens': 16, 'output_tokens': 17, 'server_tool_use': None, 'service_tier': 'standard'}, 'model_name': 'claude-3-7-sonnet-20250219'} id='run--71edddf3-2474-42dc-ad43-fadb4882c3c8-0' usage_metadata={'input_tokens': 16, 'output_tokens': 17, 'total_tokens': 33, 'input_token_details': {'cache_read': 0, 'cache_creation': 0}}\n"]}, {"data": {"text/plain": ["AIMessage(content='The sum of 1 + 2 is 3.', additional_kwargs={}, response_metadata={'id': 'msg_01F1qPrmBD9igfzHdqVipmKX', 'model': 'claude-3-7-sonnet-20250219', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'cache_creation_input_tokens': 0, 'cache_read_input_tokens': 0, 'input_tokens': 16, 'output_tokens': 17, 'server_tool_use': None, 'service_tier': 'standard'}, 'model_name': 'claude-3-7-sonnet-20250219'}, id='run--71edddf3-2474-42dc-ad43-fadb4882c3c8-0', usage_metadata={'input_tokens': 16, 'output_tokens': 17, 'total_tokens': 33, 'input_token_details': {'cache_read': 0, 'cache_creation': 0}})"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Any, Dict, List\n", "\n", "from langchain_anthropic import ChatAnthropic\n", "from langchain_core.callbacks import BaseCallbackHandler\n", "from langchain_core.messages import BaseMessage\n", "from langchain_core.outputs import LLMResult\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "\n", "class LoggingHandler(BaseCallbackHandler):\n", "    def on_chat_model_start(\n", "        self, serialized: Dict[str, Any], messages: List[List[BaseMessage]], **kwargs\n", "    ) -> None:\n", "        print(\"Chat model started\")\n", "\n", "    def on_llm_end(self, response: LLMResult, **kwargs) -> None:\n", "        print(f\"Chat model ended, response: {response}\")\n", "\n", "    def on_chain_start(\n", "        self, serialized: Dict[str, Any], inputs: Dict[str, Any], **kwargs\n", "    ) -> None:\n", "        print(f\"Chain {serialized.get('name')} started\")\n", "\n", "    def on_chain_end(self, outputs: Dict[str, Any], **kwargs) -> None:\n", "        print(f\"Chain ended, outputs: {outputs}\")\n", "\n", "\n", "callbacks = [Lo<PERSON><PERSON><PERSON><PERSON>()]\n", "llm = ChatAnthropic(model=\"claude-3-7-sonnet-20250219\")\n", "prompt = ChatPromptTemplate.from_template(\"What is 1 + {number}?\")\n", "\n", "chain = prompt | llm\n", "\n", "chain_with_callbacks = chain.with_config(callbacks=callbacks)\n", "\n", "chain_with_callbacks.invoke({\"number\": \"2\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The bound callbacks will run for all nested module runs.\n", "\n", "## Next steps\n", "\n", "You've now learned how to attach callbacks to a chain.\n", "\n", "Next, check out the other how-to guides in this section, such as how to [pass callbacks in at runtime](/docs/how_to/callbacks_runtime)."]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}