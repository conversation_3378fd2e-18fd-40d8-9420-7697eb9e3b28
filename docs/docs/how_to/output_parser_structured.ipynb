{"cells": [{"cell_type": "raw", "id": "38831021-76ed-48b3-9f62-d1241a68b6ad", "metadata": {}, "source": ["---\n", "sidebar_position: 3\n", "---"]}, {"cell_type": "markdown", "id": "a745f98b-c495-44f6-a882-757c38992d76", "metadata": {}, "source": ["# How to use output parsers to parse an LLM response into structured format\n", "\n", "Language models output text. But there are times where you want to get more structured information than just text back. While some model providers support [built-in ways to return structured output](/docs/how_to/structured_output), not all do.\n", "\n", "[Output parsers](/docs/concepts/output_parsers/) are classes that help structure language model responses. There are two main methods an output parser must implement:\n", "\n", "- \"Get format instructions\": A method which returns a string containing instructions for how the output of a language model should be formatted.\n", "- \"Parse\": A method which takes in a string (assumed to be the response from a language model) and parses it into some structure.\n", "\n", "And then one optional one:\n", "\n", "- \"Parse with prompt\": A method which takes in a string (assumed to be the response from a language model) and a prompt (assumed to be the prompt that generated such a response) and parses it into some structure. The prompt is largely provided in the event the OutputParser wants to retry or fix the output in some way, and needs information from the prompt to do so.\n", "\n", "## Get started\n", "\n", "Below we go over the main type of output parser, the `PydanticOutputParser`."]}, {"cell_type": "code", "execution_count": 1, "id": "3ad71dfb-e247-459d-b4ae-cb14964ea872", "metadata": {}, "outputs": [{"data": {"text/plain": ["Joke(setup='Why did the tomato turn red?', punchline='Because it saw the salad dressing!')"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.output_parsers import PydanticOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_openai import OpenAI\n", "from pydantic import BaseModel, Field, model_validator\n", "\n", "model = OpenAI(model_name=\"gpt-3.5-turbo-instruct\", temperature=0.0)\n", "\n", "\n", "# Define your desired data structure.\n", "class Joke(BaseModel):\n", "    setup: str = Field(description=\"question to set up a joke\")\n", "    punchline: str = Field(description=\"answer to resolve the joke\")\n", "\n", "    # You can add custom validation logic easily with Pydantic.\n", "    @model_validator(mode=\"before\")\n", "    @classmethod\n", "    def question_ends_with_question_mark(cls, values: dict) -> dict:\n", "        setup = values.get(\"setup\")\n", "        if setup and setup[-1] != \"?\":\n", "            raise ValueError(\"Badly formed question!\")\n", "        return values\n", "\n", "\n", "# Set up a parser + inject instructions into the prompt template.\n", "parser = PydanticOutputParser(pydantic_object=Joke)\n", "\n", "prompt = PromptTemplate(\n", "    template=\"Answer the user query.\\n{format_instructions}\\n{query}\\n\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": parser.get_format_instructions()},\n", ")\n", "\n", "# And a query intended to prompt a language model to populate the data structure.\n", "prompt_and_model = prompt | model\n", "output = prompt_and_model.invoke({\"query\": \"Tell me a joke.\"})\n", "parser.invoke(output)"]}, {"cell_type": "markdown", "id": "75976cd6-78e2-458b-821f-3ddf3683466b", "metadata": {}, "source": ["## LCEL\n", "\n", "Output parsers implement the [Runnable interface](/docs/concepts/runnables), the basic building block of the [LangChain Expression Language (LCEL)](/docs/concepts/lcel). This means they support `invoke`, `ainvoke`, `stream`, `astream`, `batch`, `abatch`, `astream_log` calls.\n", "\n", "Output parsers accept a string or `BaseMessage` as input and can return an arbitrary type."]}, {"cell_type": "code", "execution_count": 2, "id": "34f7ff0c-8443-4eb9-8704-b4f821811d93", "metadata": {}, "outputs": [{"data": {"text/plain": ["Joke(setup='Why did the tomato turn red?', punchline='Because it saw the salad dressing!')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["parser.invoke(output)"]}, {"cell_type": "markdown", "id": "bdebf4a5-57a8-4632-bd17-56723d431cf1", "metadata": {}, "source": ["Instead of manually invoking the parser, we also could've just added it to our `Runnable` sequence:"]}, {"cell_type": "code", "execution_count": 3, "id": "51f7fff5-e9bd-49a1-b5ab-b9ff281b93cb", "metadata": {}, "outputs": [{"data": {"text/plain": ["Joke(setup='Why did the tomato turn red?', punchline='Because it saw the salad dressing!')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["chain = prompt | model | parser\n", "chain.invoke({\"query\": \"Tell me a joke.\"})"]}, {"cell_type": "markdown", "id": "d88590a0-f36b-4ad5-8a56-d300971a6440", "metadata": {}, "source": ["While all parsers support the streaming interface, only certain parsers can stream through partially parsed objects, since this is highly dependent on the output type. Parsers which cannot construct partial objects will simply yield the fully parsed output.\n", "\n", "The `SimpleJsonOutputParser` for example can stream through partial outputs:"]}, {"cell_type": "code", "execution_count": 4, "id": "d7ecfe4d-dae8-4452-98ea-e48bdc498788", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers.json import SimpleJsonOutputParser\n", "\n", "json_prompt = PromptTemplate.from_template(\n", "    \"Return a JSON object with an `answer` key that answers the following question: {question}\"\n", ")\n", "json_parser = SimpleJsonOutputParser()\n", "json_chain = json_prompt | model | json_parser"]}, {"cell_type": "code", "execution_count": 5, "id": "cc2b999e-47aa-41f4-ba6a-13b20a204576", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{},\n", " {'answer': ''},\n", " {'answer': 'An<PERSON>'},\n", " {'answer': '<PERSON>'},\n", " {'answer': '<PERSON><PERSON>'},\n", " {'answer': '<PERSON><PERSON> <PERSON>'},\n", " {'answer': '<PERSON><PERSON>'},\n", " {'answer': '<PERSON><PERSON>'},\n", " {'answer': '<PERSON><PERSON>'},\n", " {'answer': '<PERSON><PERSON>'},\n", " {'answer': '<PERSON><PERSON>'}]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["list(json_chain.stream({\"question\": \"Who invented the microscope?\"}))"]}, {"cell_type": "markdown", "id": "3ca23082-c602-4ee8-af8c-a185b1f42bd1", "metadata": {}, "source": ["Similarly,for `PydanticOutputParser`:"]}, {"cell_type": "code", "execution_count": 6, "id": "a23b7592-1bda-4d3c-93ed-0e650dfb8d37", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Joke(setup='Why did the tomato turn red?', punchline=''),\n", " Joke(setup='Why did the tomato turn red?', punchline='Because'),\n", " Joke(setup='Why did the tomato turn red?', punchline='Because it'),\n", " Joke(setup='Why did the tomato turn red?', punchline='Because it saw'),\n", " Joke(setup='Why did the tomato turn red?', punchline='Because it saw the'),\n", " Joke(setup='Why did the tomato turn red?', punchline='Because it saw the salad'),\n", " Joke(setup='Why did the tomato turn red?', punchline='Because it saw the salad dressing'),\n", " Joke(setup='Why did the tomato turn red?', punchline='Because it saw the salad dressing!')]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["list(chain.stream({\"query\": \"Tell me a joke.\"}))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}