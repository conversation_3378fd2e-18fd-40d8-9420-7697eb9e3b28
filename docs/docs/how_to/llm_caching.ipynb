{"cells": [{"cell_type": "markdown", "id": "b843b5c4", "metadata": {}, "source": ["# How to cache LLM responses\n", "\n", "LangChain provides an optional [caching](/docs/concepts/chat_models/#caching) layer for LLMs. This is useful for two reasons:\n", "\n", "It can save you money by reducing the number of API calls you make to the LLM provider, if you're often requesting the same completion multiple times.\n", "It can speed up your application by reducing the number of API calls you make to the LLM provider.\n"]}, {"cell_type": "code", "execution_count": null, "id": "25b0b0fa", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain_openai langchain_community\n", "\n", "import os\n", "from getpass import getpass\n", "\n", "if \"OPENAI_API_KEY\" not in os.environ:\n", "    os.environ[\"OPENAI_API_KEY\"] = getpass()\n", "# Please manually enter OpenAI Key"]}, {"cell_type": "code", "execution_count": 2, "id": "0aa6d335", "metadata": {}, "outputs": [], "source": ["from langchain_core.globals import set_llm_cache\n", "from langchain_openai import OpenAI\n", "\n", "# To make the caching really obvious, let's use a slower and older model.\n", "# Caching supports newer chat models as well.\n", "llm = OpenAI(model=\"gpt-3.5-turbo-instruct\", n=2, best_of=2)"]}, {"cell_type": "code", "execution_count": 3, "id": "f168ff0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 546 ms, sys: 379 ms, total: 925 ms\n", "Wall time: 1.11 s\n"]}, {"data": {"text/plain": ["\"\\nWhy don't scientists trust atoms?\\n\\nBecause they make up everything!\""]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "from langchain_core.caches import InMemoryCache\n", "\n", "set_llm_cache(InMemoryCache())\n", "\n", "# The first time, it is not yet in cache, so it should take longer\n", "llm.invoke(\"Tell me a joke\")"]}, {"cell_type": "code", "execution_count": 4, "id": "ce7620fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 192 µs, sys: 77 µs, total: 269 µs\n", "Wall time: 270 µs\n"]}, {"data": {"text/plain": ["\"\\nWhy don't scientists trust atoms?\\n\\nBecause they make up everything!\""]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "# The second time it is, so it goes faster\n", "llm.invoke(\"Tell me a joke\")"]}, {"cell_type": "markdown", "id": "4ab452f4", "metadata": {}, "source": ["## SQLite Cache"]}, {"cell_type": "code", "execution_count": 5, "id": "2e65de83", "metadata": {}, "outputs": [], "source": ["!rm .langchain.db"]}, {"cell_type": "code", "execution_count": 6, "id": "0be83715", "metadata": {}, "outputs": [], "source": ["# We can do the same thing with a SQLite cache\n", "from langchain_community.cache import SQLiteCache\n", "\n", "set_llm_cache(SQLiteCache(database_path=\".langchain.db\"))"]}, {"cell_type": "code", "execution_count": 7, "id": "9b427ce7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 10.6 ms, sys: 4.21 ms, total: 14.8 ms\n", "Wall time: 851 ms\n"]}, {"data": {"text/plain": ["\"\\n\\nWhy don't scientists trust atoms?\\n\\nBecause they make up everything!\""]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "# The first time, it is not yet in cache, so it should take longer\n", "llm.invoke(\"Tell me a joke\")"]}, {"cell_type": "code", "execution_count": 8, "id": "87f52611", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 59.7 ms, sys: 63.6 ms, total: 123 ms\n", "Wall time: 134 ms\n"]}, {"data": {"text/plain": ["\"\\n\\nWhy don't scientists trust atoms?\\n\\nBecause they make up everything!\""]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "# The second time it is, so it goes faster\n", "llm.invoke(\"Tell me a joke\")"]}, {"cell_type": "code", "execution_count": null, "id": "6a9bb158", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 5}