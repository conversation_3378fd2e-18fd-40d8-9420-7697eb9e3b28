{"cells": [{"cell_type": "markdown", "id": "cfdf4f09-8125-4ed1-8063-6feed57da8a3", "metadata": {}, "source": ["# How to init any model in one line\n", "\n", "Many LLM applications let end users specify what model provider and model they want the application to be powered by. This requires writing some logic to initialize different [chat models](/docs/concepts/chat_models/) based on some user configuration. The `init_chat_model()` helper method makes it easy to initialize a number of different model integrations without having to worry about import paths and class names.\n", "\n", ":::tip Supported models\n", "\n", "See the [init_chat_model()](https://python.langchain.com/api_reference/langchain/chat_models/langchain.chat_models.base.init_chat_model.html) API reference for a full list of supported integrations.\n", "\n", "Make sure you have the [integration packages](/docs/integrations/chat/) installed for any model providers you want to support. E.g. you should have `langchain-openai` installed to init an OpenAI model.\n", "\n", ":::"]}, {"cell_type": "code", "execution_count": null, "id": "165b0de6-9ae3-4e3d-aa98-4fc8a97c4a06", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain langchain-openai langchain-anthropic langchain-google-genai"]}, {"cell_type": "markdown", "id": "ea2c9f57-a796-45f8-b6f4-3efd3f361a9b", "metadata": {}, "source": ["## Basic usage"]}, {"cell_type": "code", "execution_count": 5, "id": "79e14913-803c-4382-9009-5c6af3d75d35", "metadata": {"execution": {"iopub.execute_input": "2024-09-10T20:22:33.015729Z", "iopub.status.busy": "2024-09-10T20:22:33.015241Z", "iopub.status.idle": "2024-09-10T20:22:39.391716Z", "shell.execute_reply": "2024-09-10T20:22:39.390438Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPT-4o: I’m called ChatGPT. How can I assist you today?\n", "\n", "<PERSON>: My name is <PERSON>. It's nice to meet you!\n", "\n", "Gemini 2.5: I do not have a name. I am a large language model, trained by Google.\n", "\n"]}], "source": ["from langchain.chat_models import init_chat_model\n", "\n", "# Don't forget to set your environment variables for the API keys of the respective providers!\n", "# For example, you can set them in your terminal or in a .env file:\n", "# export OPENAI_API_KEY=\"your_openai_api_key\"\n", "\n", "# Returns a langchain_openai.ChatOpenAI instance.\n", "gpt_4o = init_chat_model(\"gpt-4o\", model_provider=\"openai\", temperature=0)\n", "# Returns a langchain_anthropic.ChatAnthropic instance.\n", "claude_opus = init_chat_model(\n", "    \"claude-3-opus-20240229\", model_provider=\"anthropic\", temperature=0\n", ")\n", "# Returns a langchain_google_vertexai.ChatVertexAI instance.\n", "gemini_15 = init_chat_model(\n", "    \"gemini-2.5-pro\", model_provider=\"google_genai\", temperature=0\n", ")\n", "\n", "# Since all model integrations implement the ChatModel interface, you can use them in the same way.\n", "print(\"GPT-4o: \" + gpt_4o.invoke(\"what's your name\").content + \"\\n\")\n", "print(\"<PERSON> Opus: \" + claude_opus.invoke(\"what's your name\").content + \"\\n\")\n", "print(\"Gemini 2.5: \" + gemini_15.invoke(\"what's your name\").content + \"\\n\")"]}, {"cell_type": "markdown", "id": "f811f219-5e78-4b62-b495-915d52a22532", "metadata": {}, "source": ["## Inferring model provider\n", "\n", "For common and distinct model names `init_chat_model()` will attempt to infer the model provider. See the [API reference](https://python.langchain.com/api_reference/langchain/chat_models/langchain.chat_models.base.init_chat_model.html) for a full list of inference behavior. E.g. any model that starts with `gpt-3...` or `gpt-4...` will be inferred as using model provider `openai`."]}, {"cell_type": "code", "execution_count": null, "id": "0378ccc6-95bc-4d50-be50-fccc193f0a71", "metadata": {"execution": {"iopub.execute_input": "2024-09-10T20:22:39.396908Z", "iopub.status.busy": "2024-09-10T20:22:39.396563Z", "iopub.status.idle": "2024-09-10T20:22:39.444959Z", "shell.execute_reply": "2024-09-10T20:22:39.444646Z"}}, "outputs": [], "source": ["gpt_4o = init_chat_model(\"gpt-4o\", temperature=0)\n", "claude_opus = init_chat_model(\"claude-3-opus-20240229\", temperature=0)\n", "gemini_15 = init_chat_model(\"gemini-2.5-pro\", temperature=0)"]}, {"cell_type": "markdown", "id": "476a44db-c50d-4846-951d-0f1c9ba8bbaa", "metadata": {}, "source": ["## Creating a configurable model\n", "\n", "You can also create a runtime-configurable model by specifying `configurable_fields`. If you don't specify a `model` value, then \"model\" and \"model_provider\" be configurable by default."]}, {"cell_type": "code", "execution_count": 7, "id": "6c037f27-12d7-4e83-811e-4245c0e3ba58", "metadata": {"execution": {"iopub.execute_input": "2024-09-10T20:22:39.446901Z", "iopub.status.busy": "2024-09-10T20:22:39.446773Z", "iopub.status.idle": "2024-09-10T20:22:40.301906Z", "shell.execute_reply": "2024-09-10T20:22:40.300918Z"}}, "outputs": [{"data": {"text/plain": ["AIMessage(content='I’m called ChatGPT. How can I assist you today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 13, 'prompt_tokens': 11, 'total_tokens': 24, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_07871e2ad8', 'id': 'chatcmpl-BwCyyBpMqn96KED6zPhLm4k9SQMiQ', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None}, id='run--fada10c3-4128-406c-b83d-a850d16b365f-0', usage_metadata={'input_tokens': 11, 'output_tokens': 13, 'total_tokens': 24, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["configurable_model = init_chat_model(temperature=0)\n", "\n", "configurable_model.invoke(\n", "    \"what's your name\", config={\"configurable\": {\"model\": \"gpt-4o\"}}\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "321e3036-abd2-4e1f-bcc6-606efd036954", "metadata": {"execution": {"iopub.execute_input": "2024-09-10T20:22:40.316030Z", "iopub.status.busy": "2024-09-10T20:22:40.315628Z", "iopub.status.idle": "2024-09-10T20:22:41.199134Z", "shell.execute_reply": "2024-09-10T20:22:41.198173Z"}}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"My name is <PERSON>. It's nice to meet you!\", additional_kwargs={}, response_metadata={'id': 'msg_01VDGrG9D6yefanbBG9zPJrc', 'model': 'claude-3-5-sonnet-20240620', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'cache_creation_input_tokens': 0, 'cache_read_input_tokens': 0, 'input_tokens': 11, 'output_tokens': 15, 'server_tool_use': None, 'service_tier': 'standard'}, 'model_name': 'claude-3-5-sonnet-20240620'}, id='run--f0156087-debf-4b4b-9aaa-f3328a81ef92-0', usage_metadata={'input_tokens': 11, 'output_tokens': 15, 'total_tokens': 26, 'input_token_details': {'cache_read': 0, 'cache_creation': 0}})"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["configurable_model.invoke(\n", "    \"what's your name\", config={\"configurable\": {\"model\": \"claude-3-5-sonnet-20240620\"}}\n", ")"]}, {"cell_type": "markdown", "id": "7f3b3d4a-4066-45e4-8297-ea81ac8e70b7", "metadata": {}, "source": ["### Configurable model with default values\n", "\n", "We can create a configurable model with default model values, specify which parameters are configurable, and add prefixes to configurable params:"]}, {"cell_type": "code", "execution_count": 6, "id": "814a2289-d0db-401e-b555-d5116112b413", "metadata": {"execution": {"iopub.execute_input": "2024-09-10T20:22:41.203346Z", "iopub.status.busy": "2024-09-10T20:22:41.203004Z", "iopub.status.idle": "2024-09-10T20:22:41.891450Z", "shell.execute_reply": "2024-09-10T20:22:41.890539Z"}}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"I'm an AI created by OpenAI, and I don't have a personal name. How can I assist you today?\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 23, 'prompt_tokens': 11, 'total_tokens': 34}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_25624ae3a5', 'finish_reason': 'stop', 'logprobs': None}, id='run-3380f977-4b89-4f44-bc02-b64043b3166f-0', usage_metadata={'input_tokens': 11, 'output_tokens': 23, 'total_tokens': 34})"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["first_llm = init_chat_model(\n", "    model=\"gpt-4o\",\n", "    temperature=0,\n", "    configurable_fields=(\"model\", \"model_provider\", \"temperature\", \"max_tokens\"),\n", "    config_prefix=\"first\",  # useful when you have a chain with multiple models\n", ")\n", "\n", "first_llm.invoke(\"what's your name\")"]}, {"cell_type": "code", "execution_count": 7, "id": "6c8755ba-c001-4f5a-a497-be3f1db83244", "metadata": {"execution": {"iopub.execute_input": "2024-09-10T20:22:41.896413Z", "iopub.status.busy": "2024-09-10T20:22:41.895967Z", "iopub.status.idle": "2024-09-10T20:22:42.767565Z", "shell.execute_reply": "2024-09-10T20:22:42.766619Z"}}, "outputs": [{"data": {"text/plain": ["AIMessage(content=\"My name is <PERSON>. It's nice to meet you!\", additional_kwargs={}, response_metadata={'id': 'msg_01EFKSWpmsn2PSYPQa4cNHWb', 'model': 'claude-3-5-sonnet-20240620', 'stop_reason': 'end_turn', 'stop_sequence': None, 'usage': {'input_tokens': 11, 'output_tokens': 15}}, id='run-3c58f47c-41b9-4e56-92e7-fb9602e3787c-0', usage_metadata={'input_tokens': 11, 'output_tokens': 15, 'total_tokens': 26})"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["first_llm.invoke(\n", "    \"what's your name\",\n", "    config={\n", "        \"configurable\": {\n", "            \"first_model\": \"claude-3-5-sonnet-20240620\",\n", "            \"first_temperature\": 0.5,\n", "            \"first_max_tokens\": 100,\n", "        }\n", "    },\n", ")"]}, {"cell_type": "markdown", "id": "0072b1a3-7e44-4b4e-8b07-efe1ba91a689", "metadata": {}, "source": ["### Using a configurable model declaratively\n", "\n", "We can call declarative operations like `bind_tools`, `with_structured_output`, `with_configurable`, etc. on a configurable model and chain a configurable model in the same way that we would a regularly instantiated chat model object."]}, {"cell_type": "code", "execution_count": 8, "id": "067dabee-1050-4110-ae24-c48eba01e13b", "metadata": {"execution": {"iopub.execute_input": "2024-09-10T20:22:42.771941Z", "iopub.status.busy": "2024-09-10T20:22:42.771606Z", "iopub.status.idle": "2024-09-10T20:22:43.909206Z", "shell.execute_reply": "2024-09-10T20:22:43.908496Z"}}, "outputs": [{"data": {"text/plain": ["[{'name': 'GetPopulation',\n", "  'args': {'location': 'Los Angeles, CA'},\n", "  'id': 'call_Ga9m8FAArIyEjItHmztPYA22',\n", "  'type': 'tool_call'},\n", " {'name': 'GetPopulation',\n", "  'args': {'location': 'New York, NY'},\n", "  'id': 'call_jh2dEvBaAHRaw5JUDthOs7rt',\n", "  'type': 'tool_call'}]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class GetWeather(BaseModel):\n", "    \"\"\"Get the current weather in a given location\"\"\"\n", "\n", "    location: str = Field(..., description=\"The city and state, e.g. San Francisco, CA\")\n", "\n", "\n", "class GetPopulation(BaseModel):\n", "    \"\"\"Get the current population in a given location\"\"\"\n", "\n", "    location: str = Field(..., description=\"The city and state, e.g. San Francisco, CA\")\n", "\n", "\n", "llm = init_chat_model(temperature=0)\n", "llm_with_tools = llm.bind_tools([GetWeather, GetPopulation])\n", "\n", "llm_with_tools.invoke(\n", "    \"what's bigger in 2024 LA or NYC\", config={\"configurable\": {\"model\": \"gpt-4o\"}}\n", ").tool_calls"]}, {"cell_type": "code", "execution_count": 9, "id": "e57dfe9f-cd24-4e37-9ce9-ccf8daf78f89", "metadata": {"execution": {"iopub.execute_input": "2024-09-10T20:22:43.912746Z", "iopub.status.busy": "2024-09-10T20:22:43.912447Z", "iopub.status.idle": "2024-09-10T20:22:46.437049Z", "shell.execute_reply": "2024-09-10T20:22:46.436093Z"}}, "outputs": [{"data": {"text/plain": ["[{'name': 'GetPopulation',\n", "  'args': {'location': 'Los Angeles, CA'},\n", "  'id': 'toolu_01JMufPf4F4t2zLj7miFeqXp',\n", "  'type': 'tool_call'},\n", " {'name': 'GetPopulation',\n", "  'args': {'location': 'New York City, NY'},\n", "  'id': 'toolu_01RQBHcE8kEEbYTuuS8WqY1u',\n", "  'type': 'tool_call'}]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_with_tools.invoke(\n", "    \"what's bigger in 2024 LA or NYC\",\n", "    config={\"configurable\": {\"model\": \"claude-3-5-sonnet-20240620\"}},\n", ").tool_calls"]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}