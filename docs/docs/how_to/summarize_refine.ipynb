{"cells": [{"cell_type": "markdown", "id": "c47f5b2f-e14c-43e7-a0ab-d71562636624", "metadata": {}, "source": ["---\n", "sidebar_position: 3\n", "keywords: [summarize, summarization, refine]\n", "---"]}, {"cell_type": "markdown", "id": "682a4f53-27db-43ef-a909-dd9ded76051b", "metadata": {}, "source": ["# How to summarize text through iterative refinement\n", "\n", "LLMs can summarize and otherwise distill desired information from text, including large volumes of text. In many cases, especially when the amount of text is large compared to the size of the model's context window, it can be helpful (or necessary) to break up the summarization task into smaller components.\n", "\n", "Iterative refinement represents one strategy for summarizing long texts. The strategy is as follows:\n", "\n", "- Split a text into smaller documents;\n", "- Summarize the first document;\n", "- Refine or update the result based on the next document;\n", "- Repeat through the sequence of documents until finished.\n", "\n", "Note that this strategy is not parallelized. It is especially effective when understanding of a sub-document depends on prior context-- for instance, when summarizing a novel or body of text with an inherent sequence.\n", "\n", "[LangGraph](https://langchain-ai.github.io/langgraph/), built on top of `langchain-core`, is well-suited to this problem:\n", "\n", "- LangGraph allows for individual steps (such as successive summarizations) to be streamed, allowing for greater control of execution;\n", "- LangGraph's [checkpointing](https://langchain-ai.github.io/langgraph/how-tos/persistence/) supports error recovery, extending with human-in-the-loop workflows, and easier incorporation into conversational applications.\n", "- Because it is assembled from modular components, it is also simple to extend or modify (e.g., to incorporate [tool calling](/docs/concepts/tool_calling) or other behavior).\n", "\n", "Below, we demonstrate how to summarize text via iterative refinement."]}, {"cell_type": "markdown", "id": "4aa52e84-d1b5-4b33-b4c4-541156686ef3", "metadata": {}, "source": ["## Load chat model\n", "\n", "Let's first load a chat model:\n", "\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs\n", "  customVarName=\"llm\"\n", "/>\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e5f426fc-cea6-4351-8931-1e422d3c8b69", "metadata": {}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)"]}, {"cell_type": "markdown", "id": "b137fe82-0a53-4910-b53e-b87a297f329d", "metadata": {}, "source": ["## Load documents"]}, {"cell_type": "markdown", "id": "a81dc91d-ae72-4996-b809-d4a9050e815e", "metadata": {}, "source": ["Next, we need some documents to summarize. Below, we generate some toy documents for illustrative purposes. See the document loader [how-to guides](/docs/how_to/#document-loaders) and [integration pages](/docs/integrations/document_loaders/) for additional sources of data. The [summarization tutorial](/docs/tutorials/summarization) also includes an example summarizing a blog post."]}, {"cell_type": "code", "execution_count": 2, "id": "27c8fed0-b2d7-4549-a086-f5ee657efc41", "metadata": {}, "outputs": [], "source": ["from langchain_core.documents import Document\n", "\n", "documents = [\n", "    Document(page_content=\"Apples are red\", metadata={\"title\": \"apple_book\"}),\n", "    Document(page_content=\"Blueberries are blue\", metadata={\"title\": \"blueberry_book\"}),\n", "    Document(page_content=\"Bananas are yelow\", metadata={\"title\": \"banana_book\"}),\n", "]"]}, {"cell_type": "markdown", "id": "84216044-6f1e-4b90-b4fa-29ec305abf51", "metadata": {}, "source": ["## Create graph\n", "\n", "Below we show a LangGraph implementation of this process:\n", "\n", "- We generate a simple chain for the initial summary that plucks out the first document, formats it into a prompt and runs inference with our LLM.\n", "- We generate a second `refine_summary_chain` that operates on each successive document, refining the initial summary.\n", "\n", "We will need to install `langgraph`:"]}, {"cell_type": "code", "execution_count": null, "id": "bf7acdb7-19ca-43ba-98f4-91f5b804da21", "metadata": {}, "outputs": [], "source": ["pip install -qU langgraph"]}, {"cell_type": "code", "execution_count": 3, "id": "669afa40-2708-4fa1-841e-c74a67bd9175", "metadata": {}, "outputs": [], "source": ["import operator\n", "from typing import List, Literal, TypedDict\n", "\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnableConfig\n", "from langgraph.constants import Send\n", "from langgraph.graph import END, START, StateGraph\n", "\n", "# Initial summary\n", "summarize_prompt = ChatPromptTemplate(\n", "    [\n", "        (\"human\", \"Write a concise summary of the following: {context}\"),\n", "    ]\n", ")\n", "initial_summary_chain = summarize_prompt | llm | StrOutputParser()\n", "\n", "# Refining the summary with new docs\n", "refine_template = \"\"\"\n", "Produce a final summary.\n", "\n", "Existing summary up to this point:\n", "{existing_answer}\n", "\n", "New context:\n", "------------\n", "{context}\n", "------------\n", "\n", "Given the new context, refine the original summary.\n", "\"\"\"\n", "refine_prompt = ChatPromptTemplate([(\"human\", refine_template)])\n", "\n", "refine_summary_chain = refine_prompt | llm | StrOutputParser()\n", "\n", "\n", "# We will define the state of the graph to hold the document\n", "# contents and summary. We also include an index to keep track\n", "# of our position in the sequence of documents.\n", "class State(TypedDict):\n", "    contents: List[str]\n", "    index: int\n", "    summary: str\n", "\n", "\n", "# We define functions for each node, including a node that generates\n", "# the initial summary:\n", "async def generate_initial_summary(state: State, config: RunnableConfig):\n", "    summary = await initial_summary_chain.ainvoke(\n", "        state[\"contents\"][0],\n", "        config,\n", "    )\n", "    return {\"summary\": summary, \"index\": 1}\n", "\n", "\n", "# And a node that refines the summary based on the next document\n", "async def refine_summary(state: State, config: RunnableConfig):\n", "    content = state[\"contents\"][state[\"index\"]]\n", "    summary = await refine_summary_chain.ainvoke(\n", "        {\"existing_answer\": state[\"summary\"], \"context\": content},\n", "        config,\n", "    )\n", "\n", "    return {\"summary\": summary, \"index\": state[\"index\"] + 1}\n", "\n", "\n", "# Here we implement logic to either exit the application or refine\n", "# the summary.\n", "def should_refine(state: State) -> Literal[\"refine_summary\", END]:\n", "    if state[\"index\"] >= len(state[\"contents\"]):\n", "        return END\n", "    else:\n", "        return \"refine_summary\"\n", "\n", "\n", "graph = StateGraph(State)\n", "graph.add_node(\"generate_initial_summary\", generate_initial_summary)\n", "graph.add_node(\"refine_summary\", refine_summary)\n", "\n", "graph.add_edge(START, \"generate_initial_summary\")\n", "graph.add_conditional_edges(\"generate_initial_summary\", should_refine)\n", "graph.add_conditional_edges(\"refine_summary\", should_refine)\n", "app = graph.compile()"]}, {"cell_type": "markdown", "id": "cdc11401-8640-4cf8-a713-4031df690cf7", "metadata": {}, "source": ["LangGraph allows the graph structure to be plotted to help visualize its function:"]}, {"cell_type": "code", "execution_count": 4, "id": "21711ff5-4e06-4843-9109-e7d89e679449", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Image\n", "\n", "Image(app.get_graph().draw_mermaid_png())"]}, {"cell_type": "markdown", "id": "74f3e276-f003-4112-ba14-c6952076c4f8", "metadata": {}, "source": ["## Invoke graph\n", "\n", "We can step through the execution as follows, printing out the summary as it is refined:"]}, {"cell_type": "code", "execution_count": 5, "id": "0701bb7d-fbc6-497e-a577-25d56e6e43c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Apples are characterized by their red color.\n", "Apples are characterized by their red color, while blueberries are known for their blue hue.\n", "Apples are characterized by their red color, blueberries are known for their blue hue, and bananas are recognized for their yellow color.\n"]}], "source": ["async for step in app.astream(\n", "    {\"contents\": [doc.page_content for doc in documents]},\n", "    stream_mode=\"values\",\n", "):\n", "    if summary := step.get(\"summary\"):\n", "        print(summary)"]}, {"cell_type": "markdown", "id": "49147724-de8b-44fd-bf13-5ef3432c7c6b", "metadata": {}, "source": ["The final `step` contains the summary as synthesized from the entire set of documents."]}, {"cell_type": "markdown", "id": "f15c225a-db1d-48cf-b135-f588e7d615e6", "metadata": {}, "source": ["## Next steps\n", "\n", "Check out the summarization [how-to guides](/docs/how_to/#summarization) for additional summarization strategies, including those designed for larger volumes of text.\n", "\n", "See [this tutorial](/docs/tutorials/summarization) for more detail on summarization.\n", "\n", "See also the [LangGraph documentation](https://langchain-ai.github.io/langgraph/) for detail on building with LangGraph."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}