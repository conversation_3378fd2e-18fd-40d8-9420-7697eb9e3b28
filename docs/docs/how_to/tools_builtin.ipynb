{"cells": [{"cell_type": "raw", "id": "7f219241", "metadata": {}, "source": ["---\n", "sidebar_position: 4\n", "sidebar_class_name: hidden\n", "---"]}, {"attachments": {}, "cell_type": "markdown", "id": "e8f68de0-7df7-4bfd-9207-3258431426ef", "metadata": {}, "source": ["# How to use built-in tools and toolkits\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [<PERSON><PERSON><PERSON><PERSON>](/docs/concepts/tools)\n", "- [<PERSON><PERSON><PERSON><PERSON>](/docs/concepts/tools)\n", "\n", ":::\n", "\n", "## Tools\n", "\n", "LangChain has a large collection of 3rd party tools. Please visit [Tool Integrations](/docs/integrations/tools/) for a list of the available tools.\n", "\n", ":::important\n", "\n", "When using 3rd party tools, make sure that you understand how the tool works, what permissions\n", "it has. Read over its documentation and check if anything is required from you\n", "from a security point of view. Please see our [security](https://python.langchain.com/docs/security/) \n", "guidelines for more information.\n", "\n", ":::\n", "\n", "Let's try out the [Wikipedia integration](/docs/integrations/tools/wikipedia/)."]}, {"cell_type": "code", "execution_count": 1, "id": "84f70856-b865-4658-9930-7577fb4712ce", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T03:01:11.847104Z", "iopub.status.busy": "2024-09-11T03:01:11.846727Z", "iopub.status.idle": "2024-09-11T03:01:13.200038Z", "shell.execute_reply": "2024-09-11T03:01:13.199355Z"}}, "outputs": [], "source": ["!pip install -qU langchain-community wikipedia"]}, {"cell_type": "code", "execution_count": 2, "id": "b4eaed85-c5a6-4ba9-b401-40258b0131c2", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T03:01:13.203356Z", "iopub.status.busy": "2024-09-11T03:01:13.202996Z", "iopub.status.idle": "2024-09-11T03:01:14.740686Z", "shell.execute_reply": "2024-09-11T03:01:14.739748Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Page: <PERSON><PERSON><PERSON><PERSON>\n", "Summary: LangChain is a framework designed to simplify the creation of applications \n"]}], "source": ["from langchain_community.tools import WikipediaQueryRun\n", "from langchain_community.utilities import WikipediaAPIWrapper\n", "\n", "api_wrapper = WikipediaAPIWrapper(top_k_results=1, doc_content_chars_max=100)\n", "tool = WikipediaQueryRun(api_wrapper=api_wrapper)\n", "\n", "print(tool.invoke({\"query\": \"langchain\"}))"]}, {"cell_type": "markdown", "id": "cb870984-52d5-4453-be35-7072a08c6c14", "metadata": {}, "source": ["The tool has the following defaults associated with it:"]}, {"cell_type": "code", "execution_count": 3, "id": "7f094f01-2e98-4947-acc4-0846963a96e0", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T03:01:14.745018Z", "iopub.status.busy": "2024-09-11T03:01:14.744347Z", "iopub.status.idle": "2024-09-11T03:01:14.752527Z", "shell.execute_reply": "2024-09-11T03:01:14.752112Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: wikipedia\n", "Description: A wrapper around Wikipedia. Useful for when you need to answer general questions about people, places, companies, facts, historical events, or other subjects. Input should be a search query.\n", "args schema: {'query': {'description': 'query to look up on wikipedia', 'title': 'Query', 'type': 'string'}}\n", "returns directly?: <PERSON><PERSON><PERSON>\n"]}], "source": ["print(f\"Name: {tool.name}\")\n", "print(f\"Description: {tool.description}\")\n", "print(f\"args schema: {tool.args}\")\n", "print(f\"returns directly?: {tool.return_direct}\")"]}, {"cell_type": "markdown", "id": "19eee1d5", "metadata": {}, "source": ["## Customizing Default Tools\n", "We can also modify the built in name, description, and JSON schema of the arguments.\n", "\n", "When defining the JSON schema of the arguments, it is important that the inputs remain the same as the function, so you shouldn't change that. But you can define custom descriptions for each input easily."]}, {"cell_type": "code", "execution_count": 4, "id": "1365784c-e666-41c8-a1bb-e50f822b5936", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T03:01:14.755274Z", "iopub.status.busy": "2024-09-11T03:01:14.755068Z", "iopub.status.idle": "2024-09-11T03:01:15.375704Z", "shell.execute_reply": "2024-09-11T03:01:15.374841Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Page: <PERSON><PERSON><PERSON><PERSON>\n", "Summary: LangChain is a framework designed to simplify the creation of applications \n"]}], "source": ["from langchain_community.tools import WikipediaQueryRun\n", "from langchain_community.utilities import WikipediaAPIWrapper\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class WikiInputs(BaseModel):\n", "    \"\"\"Inputs to the wikipedia tool.\"\"\"\n", "\n", "    query: str = Field(\n", "        description=\"query to look up in Wikipedia, should be 3 or less words\"\n", "    )\n", "\n", "\n", "tool = WikipediaQueryRun(\n", "    name=\"wiki-tool\",\n", "    description=\"look up things in wikipedia\",\n", "    args_schema=WikiInputs,\n", "    api_wrapper=api_wrapper,\n", "    return_direct=True,\n", ")\n", "\n", "print(tool.run(\"langchain\"))"]}, {"cell_type": "code", "execution_count": 5, "id": "6e8850d6-6840-443e-a2be-adf64b30975c", "metadata": {"execution": {"iopub.execute_input": "2024-09-11T03:01:15.378598Z", "iopub.status.busy": "2024-09-11T03:01:15.378414Z", "iopub.status.idle": "2024-09-11T03:01:15.382248Z", "shell.execute_reply": "2024-09-11T03:01:15.381801Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: wiki-tool\n", "Description: look up things in wikipedia\n", "args schema: {'query': {'description': 'query to look up in Wikipedia, should be 3 or less words', 'title': 'Query', 'type': 'string'}}\n", "returns directly?: True\n"]}], "source": ["print(f\"Name: {tool.name}\")\n", "print(f\"Description: {tool.description}\")\n", "print(f\"args schema: {tool.args}\")\n", "print(f\"returns directly?: {tool.return_direct}\")"]}, {"attachments": {}, "cell_type": "markdown", "id": "acf0c2f7-ddc6-4633-8cef-59f234321e5c", "metadata": {}, "source": ["## How to use built-in toolkits\n", "\n", "Toolkits are collections of tools that are designed to be used together for specific tasks. They have convenient loading methods.\n", "\n", "All Toolkits expose a `get_tools` method which returns a list of tools.\n", "\n", "You're usually meant to use them this way:\n", "\n", "```python\n", "# Initialize a toolkit\n", "toolkit = ExampleTookit(...)\n", "\n", "# Get list of tools\n", "tools = toolkit.get_tools()\n", "```"]}], "metadata": {"kernelspec": {"display_name": "poetry-venv-311", "language": "python", "name": "poetry-venv-311"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}