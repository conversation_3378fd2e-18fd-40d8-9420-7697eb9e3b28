{"cells": [{"cell_type": "raw", "id": "3243cb05-8243-421f-99fa-98201abb3094", "metadata": {}, "source": ["---\n", "sidebar_position: 3\n", "---"]}, {"cell_type": "markdown", "id": "14b94240", "metadata": {}, "source": ["# How to add ad-hoc tool calling capability to LLMs and Chat Models\n", "\n", ":::caution\n", "\n", "Some models have been fine-tuned for tool calling and provide a dedicated API for tool calling. Generally, such models are better at tool calling than non-fine-tuned models, and are recommended for use cases that require tool calling. Please see the [how to use a chat model to call tools](/docs/how_to/tool_calling) guide for more information.\n", "\n", ":::\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [<PERSON><PERSON><PERSON><PERSON>](/docs/concepts/tools)\n", "- [Function/tool calling](https://python.langchain.com/docs/concepts/tool_calling)\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [LLMs](/docs/concepts/text_llms)\n", "\n", ":::\n", "\n", "In this guide, we'll see how to add **ad-hoc** tool calling support to a chat model. This is an alternative method to invoke tools if you're using a model that does not natively support [tool calling](/docs/how_to/tool_calling).\n", "\n", "We'll do this by simply writing a prompt that will get the model to invoke the appropriate tools. Here's a diagram of the logic:\n", "\n", "![chain](../../static/img/tool_chain.svg)"]}, {"cell_type": "markdown", "id": "a0a22cb8-19e7-450a-9d1b-6848d2c81cd1", "metadata": {}, "source": ["## Setup\n", "\n", "We'll need to install the following packages:"]}, {"cell_type": "code", "execution_count": null, "id": "8c556c5e-b785-428b-8e7d-efd34a2a1adb", "metadata": {}, "outputs": [], "source": ["%pip install --upgrade --quiet langchain langchain-community"]}, {"cell_type": "markdown", "id": "897bc01e-cc2b-4400-8a64-db4aa56085d3", "metadata": {}, "source": ["If you'd like to use <PERSON><PERSON><PERSON>, uncomment the below:"]}, {"cell_type": "code", "execution_count": 26, "id": "5efb4170-b95b-4d29-8f57-09509f3ba6df", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "# os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass()"]}, {"cell_type": "markdown", "id": "7ec6409b-21e5-4d0a-8a46-c4ef0b055dd3", "metadata": {}, "source": ["You can select any of the given models for this how-to guide. Keep in mind that most of these models already [support native tool calling](/docs/integrations/chat/), so using the prompting strategy shown here doesn't make sense for these models, and instead you should follow the [how to use a chat model to call tools](/docs/how_to/tool_calling) guide.\n", "\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs overrideParams={{openai: {model: \"gpt-4\"}}} />\n", "\n", "To illustrate the idea, we'll use `phi3` via Ollama, which does **NOT** have native support for tool calling. If you'd like to use `Ollama` as well follow [these instructions](/docs/integrations/chat/ollama/)."]}, {"cell_type": "code", "execution_count": 24, "id": "424be968-2806-4d1a-a6aa-5499ae20fac5", "metadata": {}, "outputs": [], "source": ["from langchain_community.llms import Ollama\n", "\n", "model = Ollama(model=\"phi3\")"]}, {"cell_type": "markdown", "id": "68946881", "metadata": {}, "source": ["## Create a tool\n", "\n", "First, let's create an `add` and `multiply` tools. For more information on creating custom tools, please see [this guide](/docs/how_to/custom_tools)."]}, {"cell_type": "code", "execution_count": 4, "id": "4548e6fa-0f9b-4d7a-8fa5-66cec0350e5f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--\n", "multiply\n", "Multiply two numbers together.\n", "{'x': {'title': 'X', 'type': 'number'}, 'y': {'title': 'Y', 'type': 'number'}}\n", "--\n", "add\n", "Add two numbers.\n", "{'x': {'title': 'X', 'type': 'integer'}, 'y': {'title': 'Y', 'type': 'integer'}}\n"]}], "source": ["from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def multiply(x: float, y: float) -> float:\n", "    \"\"\"Multiply two numbers together.\"\"\"\n", "    return x * y\n", "\n", "\n", "@tool\n", "def add(x: int, y: int) -> int:\n", "    \"Add two numbers.\"\n", "    return x + y\n", "\n", "\n", "tools = [multiply, add]\n", "\n", "# Let's inspect the tools\n", "for t in tools:\n", "    print(\"--\")\n", "    print(t.name)\n", "    print(t.description)\n", "    print(t.args)"]}, {"cell_type": "code", "execution_count": 5, "id": "be77e780", "metadata": {}, "outputs": [{"data": {"text/plain": ["20.0"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["multiply.invoke({\"x\": 4, \"y\": 5})"]}, {"cell_type": "markdown", "id": "15dd690e-e54d-4209-91a4-181f69a452ac", "metadata": {}, "source": ["## Creating our prompt\n", "\n", "We'll want to write a prompt that specifies the tools the model has access to, the arguments to those tools, and the desired output format of the model. In this case we'll instruct it to output a JSON blob of the form `{\"name\": \"...\", \"arguments\": {...}}`."]}, {"cell_type": "code", "execution_count": 6, "id": "2063b564-25ca-4729-a45f-ba4633175b04", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["multiply(x: float, y: float) -> float - Multiply two numbers together.\n", "add(x: int, y: int) -> int - Add two numbers.\n"]}], "source": ["from langchain_core.output_parsers import JsonOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.tools import render_text_description\n", "\n", "rendered_tools = render_text_description(tools)\n", "print(rendered_tools)"]}, {"cell_type": "code", "execution_count": 17, "id": "f02f1dce-76e7-4ca9-9bac-5af496131fe1", "metadata": {}, "outputs": [], "source": ["system_prompt = f\"\"\"\\\n", "You are an assistant that has access to the following set of tools. \n", "Here are the names and descriptions for each tool:\n", "\n", "{rendered_tools}\n", "\n", "Given the user input, return the name and input of the tool to use. \n", "Return your response as a JSON blob with 'name' and 'arguments' keys.\n", "\n", "The `arguments` should be a dictionary, with keys corresponding \n", "to the argument names and the values corresponding to the requested values.\n", "\"\"\"\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [(\"system\", system_prompt), (\"user\", \"{input}\")]\n", ")"]}, {"cell_type": "code", "execution_count": 18, "id": "f8623e03-60eb-4439-b57b-ecbcebc61b58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"name\": \"add\",\n", "    \"arguments\": {\n", "        \"x\": 3,\n", "        \"y\": 1132\n", "    }\n", "}\n"]}], "source": ["chain = prompt | model\n", "message = chain.invoke({\"input\": \"what's 3 plus 1132\"})\n", "\n", "# Let's take a look at the output from the model\n", "# if the model is an LLM (not a chat model), the output will be a string.\n", "if isinstance(message, str):\n", "    print(message)\n", "else:  # Otherwise it's a chat model\n", "    print(message.content)"]}, {"cell_type": "markdown", "id": "14df2cd5-b6fa-4b10-892d-e8692c7931e5", "metadata": {}, "source": ["## Adding an output parser\n", "\n", "We'll use the `JsonOutputParser` for parsing our models output to JSON."]}, {"cell_type": "code", "execution_count": 19, "id": "f129f5bd-127c-4c95-8f34-8f437da7ca8f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': 'multiply', 'arguments': {'x': 13.0, 'y': 4.0}}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.output_parsers import JsonOutputParser\n", "\n", "chain = prompt | model | JsonOutputParser()\n", "chain.invoke({\"input\": \"what's thirteen times 4\"})"]}, {"cell_type": "markdown", "id": "e1f08255-f146-4f4a-be43-5c21c1d3ae83", "metadata": {}, "source": [":::important\n", "\n", "🎉 Amazing! 🎉 We now instructed our model on how to **request** that a tool be invoked.\n", "\n", "Now, let's create some logic to actually run the tool!\n", ":::"]}, {"cell_type": "markdown", "id": "8e29dd4c-8eb5-457f-92d1-8add076404dc", "metadata": {}, "source": ["## Invoking the tool 🏃\n", "\n", "Now that the model can request that a tool be invoked, we need to write a function that can actually invoke \n", "the tool.\n", "\n", "The function will select the appropriate tool by name, and pass to it the arguments chosen by the model."]}, {"cell_type": "code", "execution_count": 20, "id": "faee95e0-4095-4310-991f-9e9465c6738e", "metadata": {}, "outputs": [], "source": ["from typing import Any, Dict, Optional, TypedDict\n", "\n", "from langchain_core.runnables import RunnableConfig\n", "\n", "\n", "class ToolCallRequest(TypedDict):\n", "    \"\"\"A typed dict that shows the inputs into the invoke_tool function.\"\"\"\n", "\n", "    name: str\n", "    arguments: Dict[str, Any]\n", "\n", "\n", "def invoke_tool(\n", "    tool_call_request: ToolCallRequest, config: Optional[RunnableConfig] = None\n", "):\n", "    \"\"\"A function that we can use the perform a tool invocation.\n", "\n", "    Args:\n", "        tool_call_request: a dict that contains the keys name and arguments.\n", "            The name must match the name of a tool that exists.\n", "            The arguments are the arguments to that tool.\n", "        config: This is configuration information that <PERSON><PERSON><PERSON><PERSON> uses that contains\n", "            things like callbacks, metadata, etc.See LCEL documentation about RunnableConfig.\n", "\n", "    Returns:\n", "        output from the requested tool\n", "    \"\"\"\n", "    tool_name_to_tool = {tool.name: tool for tool in tools}\n", "    name = tool_call_request[\"name\"]\n", "    requested_tool = tool_name_to_tool[name]\n", "    return requested_tool.invoke(tool_call_request[\"arguments\"], config=config)"]}, {"cell_type": "markdown", "id": "f4957532-9e0c-47f6-bb62-0fd789ac1d3e", "metadata": {}, "source": ["Let's test this out 🧪!"]}, {"cell_type": "code", "execution_count": 21, "id": "d0ea3b2a-8fb2-4016-83c8-a5d3e78fedbc", "metadata": {}, "outputs": [{"data": {"text/plain": ["15.0"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["invoke_tool({\"name\": \"multiply\", \"arguments\": {\"x\": 3, \"y\": 5}})"]}, {"cell_type": "markdown", "id": "715af6e1-935d-4bc0-a3d2-646ecf8a329b", "metadata": {}, "source": ["## Let's put it together\n", "\n", "Let's put it together into a chain that creates a calculator with add and multiplication capabilities."]}, {"cell_type": "code", "execution_count": 22, "id": "0555b384-fde6-4404-86e0-7ea199003d58", "metadata": {}, "outputs": [{"data": {"text/plain": ["53.83784653"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["chain = prompt | model | JsonOutputParser() | invoke_tool\n", "chain.invoke({\"input\": \"what's thirteen times 4.14137281\"})"]}, {"cell_type": "markdown", "id": "b4a9c5aa-f60a-4017-af6f-1ff6e04bfb61", "metadata": {}, "source": ["## Returning tool inputs\n", "\n", "It can be helpful to return not only tool outputs but also tool inputs. We can easily do this with LCEL by `RunnablePassthrough.assign`-ing the tool output. This will take whatever the input is to the RunnablePassrthrough components (assumed to be a dictionary) and add a key to it while still passing through everything that's currently in the input:"]}, {"cell_type": "code", "execution_count": 23, "id": "45404406-859d-4caa-8b9d-5838162c80a0", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': 'multiply',\n", " 'arguments': {'x': 13, 'y': 4.14137281},\n", " 'output': 53.83784653}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.runnables import RunnablePassthrough\n", "\n", "chain = (\n", "    prompt | model | JsonOutputParser() | RunnablePassthrough.assign(output=invoke_tool)\n", ")\n", "chain.invoke({\"input\": \"what's thirteen times 4.14137281\"})"]}, {"cell_type": "markdown", "id": "1797fe82-ea35-4cba-834a-1caf9740d184", "metadata": {}, "source": ["## What's next?\n", "\n", "This how-to guide shows the \"happy path\" when the model correctly outputs all the required tool information.\n", "\n", "In reality, if you're using more complex tools, you will start encountering errors from the model, especially for models that have not been fine tuned for tool calling and for less capable models.\n", "\n", "You will need to be prepared to add strategies to improve the output from the model; e.g.,\n", "\n", "1. Provide few shot examples.\n", "2. Add error handling (e.g., catch the exception and feed it back to the LLM to ask it to correct its previous output)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}