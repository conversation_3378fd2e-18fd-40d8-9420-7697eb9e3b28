{"cells": [{"cell_type": "markdown", "id": "6fcd2994-0092-4fa3-9bb1-c9c84babadc5", "metadata": {}, "source": ["# How to pass runtime secrets to runnables\n", "\n", ":::info Requires `langchain-core >= 0.2.22`\n", "\n", ":::\n", "\n", "We can pass in secrets to our [runnables](/docs/concepts/runnables/) at runtime using the `RunnableConfig`. Specifically we can pass in secrets with a `__` prefix to the `configurable` field. This will ensure that these secrets aren't traced as part of the invocation:"]}, {"cell_type": "code", "execution_count": 6, "id": "92e42e91-c277-49de-aa7a-dfb5c993c817", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.runnables import RunnableConfig\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def foo(x: int, config: RunnableConfig) -> int:\n", "    \"\"\"Sum x and a secret int\"\"\"\n", "    return x + config[\"configurable\"][\"__top_secret_int\"]\n", "\n", "\n", "foo.invoke({\"x\": 5}, {\"configurable\": {\"__top_secret_int\": 2, \"traced_key\": \"bar\"}})"]}, {"cell_type": "markdown", "id": "ae3a4fb9-2ce7-46b2-b654-35dff0ae7197", "metadata": {}, "source": ["Looking at the <PERSON><PERSON><PERSON> trace for this run, we can see that \"traced_key\" was recorded (as part of Metadata) while our secret int was not: https://smith.langchain.com/public/aa7e3289-49ca-422d-a408-f6b927210170/r"]}], "metadata": {"kernelspec": {"display_name": "poetry-venv-311", "language": "python", "name": "poetry-venv-311"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}