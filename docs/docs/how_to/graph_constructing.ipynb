{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_position: 4\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to construct knowledge graphs\n", "\n", "In this guide we'll go over the basic ways of constructing a knowledge graph based on unstructured text. The constructured graph can then be used as knowledge base in a [RAG](/docs/concepts/rag/) application.\n", "\n", "## ⚠️ Security note ⚠️\n", "\n", "Constructing knowledge graphs requires executing write access to the database. There are inherent risks in doing this. Make sure that you verify and validate data before importing it. For more on general security best practices, [see here](/docs/security).\n", "\n", "\n", "## Architecture\n", "\n", "At a high-level, the steps of constructing a knowledge graph from text are:\n", "\n", "1. **Extracting structured information from text**: Model is used to extract structured graph information from text.\n", "2. **Storing into graph database**: Storing the extracted structured graph information into a graph database enables downstream RAG applications\n", "\n", "## Setup\n", "\n", "First, get required packages and set environment variables.\n", "In this example, we will be using Neo4j graph database."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m24.3.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install --upgrade --quiet  langchain langchain-neo4j langchain-openai langchain-experimental neo4j"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We default to OpenAI models in this guide."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" ········\n"]}], "source": ["import getpass\n", "import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = getpass.getpass()\n", "\n", "# Uncomment the below to use Lang<PERSON>mith. Not required.\n", "# os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass()\n", "# os.environ[\"LANGSMITH_TRACING\"] = \"true\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we need to define Neo4j credentials and connection.\n", "Follow [these installation steps](https://neo4j.com/docs/operations-manual/current/installation/) to set up a Neo4j database."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from langchain_neo4j import Neo4jGraph\n", "\n", "os.environ[\"NEO4J_URI\"] = \"bolt://localhost:7687\"\n", "os.environ[\"NEO4J_USERNAME\"] = \"neo4j\"\n", "os.environ[\"NEO4J_PASSWORD\"] = \"password\"\n", "\n", "graph = Neo4jGraph(refresh_schema=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## LLM Graph Transformer\n", "\n", "Extracting graph data from text enables the transformation of unstructured information into structured formats, facilitating deeper insights and more efficient navigation through complex relationships and patterns. The `LLMGraphTransformer` converts text documents into structured graph documents by leveraging a LLM to parse and categorize entities and their relationships. The selection of the LLM model significantly influences the output by determining the accuracy and nuance of the extracted graph data.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from langchain_experimental.graph_transformers import LLMGraphTransformer\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(temperature=0, model_name=\"gpt-4-turbo\")\n", "\n", "llm_transformer = LLMGraphTransformer(llm=llm)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we can pass in example text and examine the results."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nodes:[Node(id='<PERSON>', type='Person', properties={}), Node(id='<PERSON>', type='Person', properties={}), Node(id='University Of Paris', type='Organization', properties={})]\n", "Relationships:[Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='<PERSON>', type='Person', properties={}), type='MARRIED', properties={}), Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='University Of Paris', type='Organization', properties={}), type='PROFESSOR', properties={})]\n"]}], "source": ["from langchain_core.documents import Document\n", "\n", "text = \"\"\"\n", "<PERSON>, born in 1867, was a Polish and naturalised-French physicist and chemist who conducted pioneering research on radioactivity.\n", "She was the first woman to win a Nobel Prize, the first person to win a Nobel Prize twice, and the only person to win a Nobel Prize in two scientific fields.\n", "Her husband, <PERSON>, was a co-winner of her first Nobel Prize, making them the first-ever married couple to win the Nobel Prize and launching the <PERSON><PERSON><PERSON> family legacy of five Nobel Prizes.\n", "She was, in 1906, the first woman to become a professor at the University of Paris.\n", "\"\"\"\n", "documents = [Document(page_content=text)]\n", "graph_documents = await llm_transformer.aconvert_to_graph_documents(documents)\n", "print(f\"Nodes:{graph_documents[0].nodes}\")\n", "print(f\"Relationships:{graph_documents[0].relationships}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Examine the following image to better grasp the structure of the generated knowledge graph. \n", "\n", "![graph_construction1.png](../../static/img/graph_construction1.png)\n", "\n", "Note that the graph construction process is non-deterministic since we are using LLM. Therefore, you might get slightly different results on each execution.\n", "\n", "Additionally, you have the flexibility to define specific types of nodes and relationships for extraction according to your requirements."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nodes:[Node(id='<PERSON>', type='Person', properties={}), Node(id='<PERSON>', type='Person', properties={}), Node(id='University Of Paris', type='Organization', properties={})]\n", "Relationships:[Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='<PERSON>', type='Person', properties={}), type='SPOUSE', properties={}), Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='University Of Paris', type='Organization', properties={}), type='WORKED_AT', properties={})]\n"]}], "source": ["llm_transformer_filtered = LLMGraphTransformer(\n", "    llm=llm,\n", "    allowed_nodes=[\"Person\", \"Country\", \"Organization\"],\n", "    allowed_relationships=[\"NATIONALITY\", \"LOCATED_IN\", \"WORKED_AT\", \"SPOUSE\"],\n", ")\n", "graph_documents_filtered = await llm_transformer_filtered.aconvert_to_graph_documents(\n", "    documents\n", ")\n", "print(f\"Nodes:{graph_documents_filtered[0].nodes}\")\n", "print(f\"Relationships:{graph_documents_filtered[0].relationships}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To define the graph schema more precisely, consider using a three-tuple approach for relationships. In this approach, each tuple consists of three elements: the source node, the relationship type, and the target node."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nodes:[Node(id='<PERSON>', type='Person', properties={}), Node(id='<PERSON>', type='Person', properties={}), Node(id='University Of Paris', type='Organization', properties={})]\n", "Relationships:[Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='<PERSON>', type='Person', properties={}), type='SPOUSE', properties={}), Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='University Of Paris', type='Organization', properties={}), type='WORKED_AT', properties={})]\n"]}], "source": ["allowed_relationships = [\n", "    (\"Person\", \"SPOUSE\", \"Person\"),\n", "    (\"Person\", \"NATIONALITY\", \"Country\"),\n", "    (\"Person\", \"WORKED_AT\", \"Organization\"),\n", "]\n", "\n", "llm_transformer_tuple = LLMGraphTransformer(\n", "    llm=llm,\n", "    allowed_nodes=[\"Person\", \"Country\", \"Organization\"],\n", "    allowed_relationships=allowed_relationships,\n", ")\n", "graph_documents_filtered = await llm_transformer_tuple.aconvert_to_graph_documents(\n", "    documents\n", ")\n", "print(f\"Nodes:{graph_documents_filtered[0].nodes}\")\n", "print(f\"Relationships:{graph_documents_filtered[0].relationships}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For a better understanding of the generated graph, we can again visualize it.\n", "\n", "![graph_construction2.png](../../static/img/graph_construction2.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `node_properties` parameter enables the extraction of node properties, allowing the creation of a more detailed graph.\n", "When set to `True`, LLM autonomously identifies and extracts relevant node properties.\n", "Conversely, if `node_properties` is defined as a list of strings, the LLM selectively retrieves only the specified properties from the text."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nodes:[Node(id='<PERSON>', type='Person', properties={'born_year': '1867'}), Node(id='<PERSON>', type='Person', properties={}), Node(id='University Of Paris', type='Organization', properties={}), Node(id='Poland', type='Country', properties={}), Node(id='France', type='Country', properties={})]\n", "Relationships:[Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='Poland', type='Country', properties={}), type='NATIONALITY', properties={}), Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='France', type='Country', properties={}), type='NATIONALITY', properties={}), Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='<PERSON>', type='Person', properties={}), type='SPOUSE', properties={}), Relationship(source=Node(id='<PERSON>', type='Person', properties={}), target=Node(id='University Of Paris', type='Organization', properties={}), type='WORKED_AT', properties={})]\n"]}], "source": ["llm_transformer_props = LLMGraphTransformer(\n", "    llm=llm,\n", "    allowed_nodes=[\"Person\", \"Country\", \"Organization\"],\n", "    allowed_relationships=[\"NATIONALITY\", \"LOCATED_IN\", \"WORKED_AT\", \"SPOUSE\"],\n", "    node_properties=[\"born_year\"],\n", ")\n", "graph_documents_props = await llm_transformer_props.aconvert_to_graph_documents(\n", "    documents\n", ")\n", "print(f\"Nodes:{graph_documents_props[0].nodes}\")\n", "print(f\"Relationships:{graph_documents_props[0].relationships}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Storing to graph database\n", "\n", "The generated graph documents can be stored to a graph database using the `add_graph_documents` method."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["graph.add_graph_documents(graph_documents_props)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Most graph databases support indexes to optimize data import and retrieval. Since we might not know all the node labels in advance, we can handle this by adding a secondary base label to each node using the `baseEntityLabel` parameter."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["graph.add_graph_documents(graph_documents, baseEntityLabel=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Results will look like:\n", "\n", "![graph_construction3.png](../../static/img/graph_construction3.png)\n", "\n", "The final option is to also import the source documents for the extracted nodes and relationships. This approach lets us track which documents each entity appeared in."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["graph.add_graph_documents(graph_documents, include_source=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Graph will have the following structure:\n", "\n", "![graph_construction4.png](../../static/img/graph_construction4.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this visualization, the source document is highlighted in blue, with all entities extracted from it connected by `MENTIONS` relationships."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 4}