{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to debug your LLM apps\n", "\n", "Like building any type of software, at some point you'll need to debug when building with LLMs. A model call will fail, or model output will be misformatted, or there will be some nested model calls and it won't be clear where along the way an incorrect output was created.\n", "\n", "There are three main methods for debugging:\n", "\n", "- Verbose Mode: This adds print statements for \"important\" events in your chain.\n", "- Debug Mode: This add logging statements for ALL events in your chain.\n", "- LangSmith Tracing: This logs events to [LangSmith](https://docs.smith.langchain.com/) to allow for visualization there.\n", "\n", "|                        | Verbose Mode | Debug Mode | LangSmith Tracing |\n", "|------------------------|--------------|------------|-------------------|\n", "| Free                   | ✅            | ✅          | ✅                 |\n", "| UI                     | ❌            | ❌          | ✅                 |\n", "| Persisted              | ❌            | ❌          | ✅                 |\n", "| See all events         | ❌            | ✅          | ✅                 |\n", "| See \"important\" events | ✅            | ❌          | ✅                 |\n", "| Runs Locally           | ✅            | ✅          | ❌                 |\n", "\n", "\n", "## Tracing\n", "\n", "Many of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls.\n", "As these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent.\n", "The best way to do this is with [Lang<PERSON><PERSON>](https://smith.langchain.com).\n", "\n", "After you sign up at the link above, make sure to set your environment variables to start logging traces:\n", "\n", "```shell\n", "export LANGSMITH_TRACING=\"true\"\n", "export LANGSMITH_API_KEY=\"...\"\n", "```\n", "\n", "Or, if in a notebook, you can set them with:\n", "\n", "```python\n", "import getpass\n", "import os\n", "\n", "os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass()\n", "```\n", "\n", "Let's suppose we have an agent, and want to visualize the actions it takes and tool outputs it receives. Without any debugging, here's what we see:\n", "\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs\n", "  customVarName=\"llm\"\n", "/>\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2025-06-17T18:43:16.635161Z", "start_time": "2025-06-17T18:43:16.083726Z"}}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-4-turbo\", temperature=0)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2025-06-17T18:43:37.834817Z", "start_time": "2025-06-17T18:43:17.201171Z"}}, "outputs": [{"data": {"text/plain": ["{'input': 'Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?',\n", " 'output': \"The 2023 film **<PERSON><PERSON><PERSON>** was directed by **<PERSON>**.\\n\\nChristop<PERSON> was born on **July 30, 1970**. To calculate his age in days as of today:\\n\\n1. First, determine the total number of days from his birthdate to today.\\n2. Use the formula: \\\\[ \\\\text{Age in days} = (\\\\text{Current Year} - \\\\text{Birth Year}) \\\\times 365 + \\\\text{Extra Days for Leap Years} + \\\\text{Days from Birthday to Today's Date} \\\\]\\n\\nLet's calculate:\\n\\n- From July 30, 1970, to July 30, 2023, is 53 years.\\n- From July 30, 2023, to December 7, 2023, is 130 days.\\n\\nLeap years between 1970 and 2023 (every 4 years, except century years not divisible by 400):\\n1972, 1976, 1980, 1984, 1988, 1992, 1996, 2000, 2004, 2008, 2012, 2016, 2020. That's 13 leap years.\\n\\nSo, his age in days is:\\n\\\\[ 53 \\\\times 365 + 13 + 130 = 19345 + 13 + 130 = 19488 \\\\text{ days} \\\\]\\n\\nChristop<PERSON> <PERSON> is **19,488 days old** as of today.\"}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.agents import AgentExecutor, create_tool_calling_agent\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_tavily import TavilySearch\n", "\n", "tools = [TavilySearch(max_results=5, topic=\"general\")]\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"You are a helpful assistant.\",\n", "        ),\n", "        (\"placeholder\", \"{chat_history}\"),\n", "        (\"human\", \"{input}\"),\n", "        (\"placeholder\", \"{agent_scratchpad}\"),\n", "    ]\n", ")\n", "\n", "# Construct the Tools agent\n", "agent = create_tool_calling_agent(llm, tools, prompt)\n", "\n", "# Create an agent executor by passing in the agent and tools\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)\n", "agent_executor.invoke(\n", "    {\"input\": \"Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?\"}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We don't get much output, but since we set up LangSmith we can easily see what happened under the hood:\n", "\n", "https://smith.langchain.com/public/a89ff88f-9ddc-4757-a395-3a1b365655bf/r"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `set_debug` and `set_verbose`\n", "\n", "If you're prototyping in Jupyter Notebooks or running Python scripts, it can be helpful to print out the intermediate steps of a chain run.\n", "\n", "There are a number of ways to enable printing at varying degrees of verbosity.\n", "\n", "Note: These still work even with <PERSON><PERSON><PERSON> enabled, so you can have both turned on and running at the same time\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `set_verbose(True)`\n", "\n", "Setting the `verbose` flag will print out inputs and outputs in a slightly more readable format and will skip logging certain raw outputs (like the token usage stats for an LLM call) so that you can focus on application logic."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2025-06-17T18:44:23.893635Z", "start_time": "2025-06-17T18:43:52.414880Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m\n", "Invoking: `tavily_search` with `{'query': 'director of the 2023 film <PERSON><PERSON><PERSON>'}`\n", "\n", "\n", "\u001b[0m\u001b[36;1m\u001b[1;3m{'query': 'director of the 2023 film <PERSON><PERSON><PERSON>', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': '<PERSON><PERSON><PERSON> (film) - Wikipedia', 'url': 'https://en.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(film)', 'content': \"Donate Create account Log in Personal tools Donate Create account Log in Pages for logged out editors learn more Contributions Talk Toggle the table of contents Contents move to sidebar hide (Top) 1 Plot 2 Cast 3 ProductionToggle Production subsection 3.1 Development 3.2 Writing 3.3 Casting 3.4 Filming 3.5 Post-production 4 Music 5 Marketing 6 ReleaseToggle Release subsection 6.1 Theatrical 6.1.1 Classifications and censorship 6.1.2 Bhagavad Gita controversy 6.2 Home media 7 ReceptionToggle Reception subsection 7.1 Box office 7.1.1 United States and Canada 7.1.2 Japan 7.1.3 Other territories 7.2 Critical response 7.3 Influence on legislation 8 Accuracy and omissions 9 Accolades 10 See also 11 Notes 12 References 13 Further reading 14 External links <PERSON><PERSON><PERSON> (film) 70 languages العربية অসমীয়া Azərbaycanca বাংলা Беларуская भोजपुरी Български Bosanski Català Čeština Cymraeg Dansk Deutsch डोटेली Eesti Ελληνικά Español Euskara فارسی Français Gaeilge Galego 한국어 Հայերեն हिन्दी Ido Bahasa Indonesia Italiano עברית Jawa ქართული Қазақша Latina Latviešu Lietuvių Magyar Македонски മലയാളം मराठी مصرى مازِرونی Bahasa Melayu Nederlands नेपाली 日本語 Norsk bokmål Oʻzbekcha / ўзбекча ਪੰਜਾਬੀ Polski Português Română Русский Shqip Simple English Slovenčina Slovenščina کوردی Српски / srpski Suomi Svenska தமிழ் తెలుగు ไทย Тоҷикӣ Türkçe Українська اردو Tiếng Việt 粵語 中文 Edit links Article Talk English Read Edit View history Tools Tools move to sidebar hide Actions Read Edit View history General What links here Related changes Upload file Permanent link Page information Cite this page Get shortened URL Download QR code Expand all Edit interlanguage links Print/export Download as PDF Printable version In other projects Wikimedia Commons Wikiquote Wikidata item From Wikipedia, the free encyclopedia 2023 film by Christopher Nolan | Oppenheimer | | --- | | Theatrical release poster | | Directed by | Christopher Nolan | | Screenplay by | Christopher Nolan | | Based on | American Prometheus by Kai Bird Martin J. Sherwin | | Produced by | Emma Thomas Charles Roven Christopher Nolan | | Starring | Cillian Murphy Emily Blunt Matt Damon Robert Downey Jr. Florence Pugh Josh Hartnett Casey Affleck Rami Malek Kenneth Branagh | | Cinematography | Hoyte van Hoytema | | Edited by | Jennifer Lame | | Music by | Ludwig Göransson | | Production companies | Universal Pictures[1][2] Syncopy[1][2] Atlas Entertainment[1][2] Breakheart Films[2] Peters Creek Entertainment[2] Gadget Films[1][3] | | Distributed by | Universal Pictures | | Release dates | July 11, 2023 (2023-07-11) (Le Grand Rex) July 21, 2023 (2023-07-21) (United States and United Kingdom) | | Running time | 180 minutes[4] | | Countries | United States United Kingdom | | Language | English | | Budget | $100 million[5] | | Box office | $975.8 million[6][7] | Oppenheimer is a 2023 epic biographical drama film written, produced, and directed by Christopher Nolan. [8] It follows the life of J. Robert Oppenheimer, the American theoretical physicist who helped develop the first nuclear weapons during World War II. Based on the 2005 biography American Prometheus by Kai Bird and Martin J. Sherwin, the film dramatizes Oppenheimer's studies, his direction of the Los Alamos Laboratory and his 1954 security hearing. Oppenheimer received critical acclaim and grossed $975 million worldwide, becoming the third-highest-grossing film of 2023, the highest-grossing World War II-related film, the highest-grossing biographical film and the second-highest-grossing R-rated film of all time at the time of its release.\", 'score': 0.9475027, 'raw_content': None}, {'title': 'Oppenheimer | Cast, Film, Length, Plot, Actors, Awards, & Facts ...', 'url': 'https://www.britannica.com/topic/Oppenheimer-film', 'content': 'J. Robert Oppenheimer Robert Downey, Jr. Oppenheimer # Oppenheimer Oppenheimer, American and British dramatic biographical film, released in 2023, that explores the life and legacy of the American physicist J. Robert Oppenheimer, who played a key role in the development of the atomic bomb. Robert Oppenheimer (2005). Film critics’ reaction to Oppenheimer was overwhelmingly positive. Oppenheimer grossed more than $300 million domestically and more than $600 million internationally by the end of November 2023, making it the second highest grossing R-rated film of all time. The film also dominated the Academy Awards nominations, garnering 13 nominations compared with the 8 for Greta Gerwig’s Barbie, which opened the same weekend as Oppenheimer but topped Nolan’s film at the box office.', 'score': 0.76194656, 'raw_content': None}, {'title': 'Oppenheimer (2023) - Full cast & crew - IMDb', 'url': 'https://www.imdb.com/title/tt15398776/fullcredits/', 'content': 'Oppenheimer (2023) - Cast and crew credits, including actors, actresses, directors, writers and more. Menu. ... Oscars Pride Month American Black Film Festival Summer Watch Guide STARmeter Awards Awards Central Festival Central All Events. ... second unit director: visual effects (uncredited) Francesca Kaimer Millea.', 'score': 0.683948, 'raw_content': None}, {'title': \"'Oppenheimer' director Christopher Nolan says the film is his darkest - NPR\", 'url': 'https://www.npr.org/2023/08/14/1193448291/oppenheimer-director-christopher-nolan', 'content': '# \\'Like it or not, we live in Oppenheimer\\'s world,\\' says director Christopher Nolan #### \\'Like it or not, we live in Oppenheimer\\'s world,\\' says director Christopher Nolan But he says the story of Robert Oppenheimer, known as the father of the atomic bomb, stayed with him in a way his other films didn\\'t. Nolan says he was drawn to the tension of Oppenheimer\\'s story — particularly the disconnect between the joy the physicist felt at the success of the Trinity test, and the horror that later resulted. Writer, director and producer Christopher Nolan says Oppenheimer is the \"darkest\" of all the films he\\'s worked on. Writer, director and producer Christopher Nolan says Oppenheimer is the \"darkest\" of all the films he\\'s worked on.', 'score': 0.6255073, 'raw_content': None}, {'title': 'An extended interview with Christopher Nolan, director of Oppenheimer', 'url': 'https://thebulletin.org/premium/2023-07/an-extended-interview-with-christopher-nolan-director-of-oppenheimer/', 'content': 'A group of Manhattan Project scientists and engineers also focused on wider public education on nuclear weapons and energy (and science generally) through the creation of the Bulletin of the Atomic Scientists; Oppenheimer served as the first chair of the magazine’s Board of Sponsors.[5] As time has passed, more evidence has come to light of the bias and unfairness of the process that Dr. Oppenheimer was subjected to while the evidence of his loyalty and love of country have only been further affirmed.”[8] Decades after the fact, records of the Oppenheimer security hearing made it clear that, rather than any disloyalty to the nation, it was his principled opposition to development of the hydrogen bomb—a nuclear fusion-based weapon of immensely greater power than the fission weapons used to decimate Hiroshima and Nagasaki in 1945—that was key to the decision to essentially bar him from government service. Robert Oppenheimer, Los Alamos, Manhattan Project, Nolan, atomic bomb, movie', 'score': 0.32472825, 'raw_content': None}], 'response_time': 0.94}\u001b[0m\u001b[32;1m\u001b[1;3m\n", "Invoking: `tavily_search` with `{'query': 'birthdate of the director of the 2023 film <PERSON><PERSON><PERSON>'}`\n", "\n", "\n", "\u001b[0m\u001b[36;1m\u001b[1;3m{'query': 'birthdate of the director of the 2023 film <PERSON><PERSON><PERSON>', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': '<PERSON><PERSON><PERSON> (film) - Wikipedia', 'url': 'https://en.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(film)', 'content': \"Donate Create account Log in Personal tools Donate Create account Log in Pages for logged out editors learn more Contributions Talk Toggle the table of contents Contents move to sidebar hide (Top) 1 Plot 2 Cast 3 ProductionToggle Production subsection 3.1 Development 3.2 Writing 3.3 Casting 3.4 Filming 3.5 Post-production 4 Music 5 Marketing 6 ReleaseToggle Release subsection 6.1 Theatrical 6.1.1 Classifications and censorship 6.1.2 Bhagavad Gita controversy 6.2 Home media 7 ReceptionToggle Reception subsection 7.1 Box office 7.1.1 United States and Canada 7.1.2 Japan 7.1.3 Other territories 7.2 Critical response 7.3 Influence on legislation 8 Accuracy and omissions 9 Accolades 10 See also 11 Notes 12 References 13 Further reading 14 External links <PERSON><PERSON><PERSON> (film) 70 languages العربية অসমীয়া Azərbaycanca বাংলা Беларуская भोजपुरी Български Bosanski Català Čeština Cymraeg Dansk Deutsch डोटेली Eesti Ελληνικά Español Euskara فارسی Français Gaeilge Galego 한국어 Հայերեն हिन्दी Ido Bahasa Indonesia Italiano עברית Jawa ქართული Қазақша Latina Latviešu Lietuvių Magyar Македонски മലയാളം मराठी مصرى مازِرونی Bahasa Melayu Nederlands नेपाली 日本語 Norsk bokmål Oʻzbekcha / ўзбекча ਪੰਜਾਬੀ Polski Português Română Русский Shqip Simple English Slovenčina Slovenščina کوردی Српски / srpski Suomi Svenska தமிழ் తెలుగు ไทย Тоҷикӣ Türkçe Українська اردو Tiếng Việt 粵語 中文 Edit links Article Talk English Read Edit View history Tools Tools move to sidebar hide Actions Read Edit View history General What links here Related changes Upload file Permanent link Page information Cite this page Get shortened URL Download QR code Expand all Edit interlanguage links Print/export Download as PDF Printable version In other projects Wikimedia Commons Wikiquote Wikidata item From Wikipedia, the free encyclopedia 2023 film by Christopher Nolan | Oppenheimer | | --- | | Theatrical release poster | | Directed by | Christopher Nolan | | Screenplay by | Christopher Nolan | | Based on | American Prometheus by Kai Bird Martin J. Sherwin | | Produced by | Emma Thomas Charles Roven Christopher Nolan | | Starring | Cillian Murphy Emily Blunt Matt Damon Robert Downey Jr. Florence Pugh Josh Hartnett Casey Affleck Rami Malek Kenneth Branagh | | Cinematography | Hoyte van Hoytema | | Edited by | Jennifer Lame | | Music by | Ludwig Göransson | | Production companies | Universal Pictures[1][2] Syncopy[1][2] Atlas Entertainment[1][2] Breakheart Films[2] Peters Creek Entertainment[2] Gadget Films[1][3] | | Distributed by | Universal Pictures | | Release dates | July 11, 2023 (2023-07-11) (Le Grand Rex) July 21, 2023 (2023-07-21) (United States and United Kingdom) | | Running time | 180 minutes[4] | | Countries | United States United Kingdom | | Language | English | | Budget | $100 million[5] | | Box office | $975.8 million[6][7] | Oppenheimer is a 2023 epic biographical drama film written, produced, and directed by Christopher Nolan. [8] It follows the life of J. Robert Oppenheimer, the American theoretical physicist who helped develop the first nuclear weapons during World War II. Based on the 2005 biography American Prometheus by Kai Bird and Martin J. Sherwin, the film dramatizes Oppenheimer's studies, his direction of the Los Alamos Laboratory and his 1954 security hearing. Oppenheimer received critical acclaim and grossed $975 million worldwide, becoming the third-highest-grossing film of 2023, the highest-grossing World War II-related film, the highest-grossing biographical film and the second-highest-grossing R-rated film of all time at the time of its release.\", 'score': 0.9092728, 'raw_content': None}, {'title': 'Oppenheimer (movie) - Simple English Wikipedia, the free encyclopedia', 'url': 'https://simple.wikipedia.org/wiki/Oppenheimer_(movie)', 'content': 'Oppenheimer (movie) - Simple English Wikipedia, the free encyclopedia Oppenheimer (movie) Oppenheimer is a 2023 epic biographical thriller movie written and directed by Christopher Nolan. Robert Oppenheimer, a theoretical physicist who helped create the first nuclear weapons as part of the Manhattan Project. With $975 million at the box office, Oppenheimer is the highest-grossing biographical movie of all time, beating Bohemian Rhapsody (2018).[5][6] Josh Hartnett as Ernest Lawrence, a Nobel-winning nuclear physicist who worked with Oppenheimer at the University of California, Berkeley. Dylan Arnold as Frank Oppenheimer, Robert’s younger brother and a particle physicist who worked on the Manhattan Project. Retrieved from \"https://simple.wikipedia.org/w/index.php?title=Oppenheimer_(movie)&oldid=10077836\" *   2023 movies Oppenheimer (movie)', 'score': 0.7961819, 'raw_content': None}, {'title': 'Oppenheimer | Cast, Film, Length, Plot, Actors, Awards, & Facts ...', 'url': 'https://www.britannica.com/topic/Oppenheimer-film', 'content': 'J. Robert Oppenheimer Robert Downey, Jr. Oppenheimer # Oppenheimer Oppenheimer, American and British dramatic biographical film, released in 2023, that explores the life and legacy of the American physicist J. Robert Oppenheimer, who played a key role in the development of the atomic bomb. Robert Oppenheimer (2005). Film critics’ reaction to Oppenheimer was overwhelmingly positive. Oppenheimer grossed more than $300 million domestically and more than $600 million internationally by the end of November 2023, making it the second highest grossing R-rated film of all time. The film also dominated the Academy Awards nominations, garnering 13 nominations compared with the 8 for Greta Gerwig’s Barbie, which opened the same weekend as Oppenheimer but topped Nolan’s film at the box office.', 'score': 0.6854659, 'raw_content': None}, {'title': 'Oppenheimer (2023) - IMDb', 'url': 'https://www.imdb.com/title/tt15398776/', 'content': \"Oppenheimer (2023) - IMDb Oppenheimer IMDb RATING Robert Oppenheimer, the physicist who had a large hand in the development of the atomic bombs that brought an end to World War II.A dramatization of the life story of J. Robert Oppenheimer, the physicist who had a large hand in the development of the atomic bombs that brought an end to World War II.A dramatization of the life story of J. Robert Oppenheimer, the physicist who had a large hand in the development of the atomic bombs that brought an end to World War II. J. Robert Oppenheimer Cillian Murphy and the cast of Oppenheimer discuss what it's like to work with a singular director like Christopher Nolan. J. Robert Oppenheimer: Albert?\", 'score': 0.5951402, 'raw_content': None}, {'title': 'Oppenheimer (film) - Wikiwand', 'url': 'https://www.wikiwand.com/en/articles/Oppenheimer_(2023_film)', 'content': \"Development Kai Bird (pictured) and Martin J. Sherwin are the authors of J. Robert Oppenheimer's biography American Prometheus (2005), on which the film is based.. Director Sam Mendes was interested in adapting the 2005 J. Robert Oppenheimer biography American Prometheus by Kai Bird and Martin J. Sherwin.After that project failed to materialize, the book was optioned by various filmmakers over\", 'score': 0.3386242, 'raw_content': None}], 'response_time': 4.11}\u001b[0m\u001b[32;1m\u001b[1;3m\n", "Invoking: `tavily_search` with `{'query': 'birthdate of <PERSON>'}`\n", "responded: The 2023 film **<PERSON><PERSON><PERSON>** was directed by **<PERSON>**.\n", "\n", "To calculate <PERSON>'s age in days, I need to find his birthdate. Let me find that information for you.\n", "\n", "\u001b[0m\u001b[36;1m\u001b[1;3m{'query': 'birthdate of <PERSON>', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': '<PERSON>, Family, Bio | Famous Birthdays', 'url': 'https://www.famousbirthdays.com/people/christopher-nolan.html', 'content': '<PERSON> Director Birthday July 30, 1970 Birth Sign Leo Birthplace London, England Age 54 years old #10,366 Most Popular About British-American director, screenwriter, and producer who first received acclaim for his 2000 indie suspense thriller Memento. He then shifted from art-house films to blockbusters with the box office hits The Dark Knight, Inception, and Interstellar. He won his first Academy Awards for Best Director and Best Picture for his 2023 film <PERSON><PERSON><PERSON>. Trivia In 2003, he approached Warner Bros. with his pitch for a new Batman franchise more grounded in a realistic world than a comic book world. He signed a contract with the studio, and produced three Batman features from 2005 to 2012: <PERSON>, The Dark Knight and The Dark Knight Rises.', 'score': 0.8939131, 'raw_content': None}, {'title': '<PERSON> | Biography, Movies, Batman, <PERSON>pen<PERSON>, & Facts ...', 'url': 'https://www.britannica.com/biography/<PERSON>-<PERSON>-British-director', 'content': '<PERSON> (born July 30, 1970, London, England) is a British film director and writer acclaimed for his noirish visual aesthetic and unconventional, often highly conceptual narratives. In 2024 Nolan won an Academy Award for best director for Oppenheimer (2023), which was also named best picture. Nolan’s breakthrough came with the 2000 film Memento, a sleeper hit that he adapted from a short story written by his brother <PERSON> <PERSON>. The film was a critical and popular success and garnered the <PERSON> brothers an Academy Award nomination for best original screenplay. <PERSON>’s highly anticipated Batman <PERSON><PERSON> (2005), starring Christian Bale, focuses on the superhero’s origins and features settings and a tone that are grimmer and more realistic than those of previous Batman films. Nolan’s 2023 film, Oppenheimer, depicts American theoretical physicist  J.', 'score': 0.88822687, 'raw_content': None}, {'title': 'Christopher Nolan: Biography, Movie Director, Filmmaker', 'url': 'https://www.biography.com/movies-tv/christopher-nolan', 'content': 'Opt-Out Icon Christopher Nolan is an Academy Award-winning movie director and screenwriter who’s helmed several hit films, including Inception, The Dark Knight, Interstellar, and Oppenheimer. We may earn commission from links on this page, but we only recommend products we back. Christopher Nolan is a British-American filmmaker known for his complex storytelling in big-budget movies such as Inception (2010), Interstellar (2014) and Tenet (2020). Play Icon We may earn commission from links on this page, but we only recommend products we back. Biography and associated logos are trademarks of A+E Networks®protected in the US and other countries around the globe. Opt-Out Icon', 'score': 0.29651213, 'raw_content': None}, {'title': 'Christopher Nolan \"Film Director\" - Biography, Age and Married', 'url': 'https://biographyhost.com/p/christopher-nolan-biography.html', 'content': 'Christopher Nolan is a renowned British-American filmmaker celebrated for his innovative storytelling in films like Oppenheimer and Inception. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films.', 'score': 0.21290259, 'raw_content': None}, {'title': 'Christopher Nolan - Wikipedia', 'url': 'https://en.wikipedia.org/wiki/Christopher_Nolan', 'content': 'Following a positive word of mouth and screenings in 500 theatres, it earned $40\\xa0million.[41] Memento premiered at the Venice Film Festival in September 2000 to critical acclaim.[42] Joe Morgenstern of The Wall Street Journal wrote in his review, \"I can\\'t remember when a movie has seemed so clever, strangely affecting and slyly funny at the very same time.\"[43] In the book The Philosophy of Neo-Noir, Basil Smith drew a comparison with John Locke\\'s An Essay Concerning Human Understanding, which argues that conscious memories constitute our identities – a theme Nolan explores in the film.[44] Memento earned Nolan many accolades, including nominations for an Academy Award and a Golden Globe Award for Best Screenplay, as well as two Independent Spirit Awards: Best Director and Best Screenplay.[45][46] Six critics listed it as one of the best films of the 2000s.[47] In 2001, Nolan and Emma Thomas founded the production company Syncopy Inc.[48][b]', 'score': 0.15323243, 'raw_content': None}], 'response_time': 2.47}\u001b[0m\u001b[32;1m\u001b[1;3m\n", "Invoking: `tavily_search` with `{'query': 'current date'}`\n", "responded: <PERSON>, the director of the 2023 film **<PERSON><PERSON><PERSON>**, was born on **July 30, 1970**.\n", "\n", "To calculate his age in days as of today, we can use the following formula:\n", "\n", "\\[ \\text{Age in days} = (\\text{Current Date} - \\text{Birthdate}) \\]\n", "\n", "Let's calculate this now.\n", "\n", "\u001b[0m\u001b[36;1m\u001b[1;3m{'query': 'current date', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': \"Today's Date - CalendarDate.com\", 'url': 'https://www.calendardate.com/todays.htm', 'content': \"Details about today's date with count of days, weeks, and months, Sun and Moon cycles, Zodiac signs and holidays. Sunday June 15, 2025 . Home; Calendars. 2025 Calendar; ... Current Season Today: Spring with 6 days until the start of Summer. S. Hemishpere flip seasons - i.e. Winter is Summer.\", 'score': 0.63152665, 'raw_content': None}, {'title': \"What is the date today | Today's Date\", 'url': 'https://www.datetoday.info/', 'content': 'Find out the current date and time in different time zones and formats, such as UTC, America/Los_Angeles, ISO 8601, RFC 2822, Unix Epoch, etc. Learn more about the day of the week, the day of the year, the week number, the month number, and the remaining days of the year.', 'score': 0.60049355, 'raw_content': None}, {'title': 'Current Time', 'url': 'https://www.timeanddate.com/', 'content': 'Current Time. Monday Jun 9, 2025 Washington DC, District of Columbia, USA. Set home location. 5:39: 55 am. World Clock. World Clock. Current local time around the world. Personal World Clock. Set the current time of your favorite locations across time zones. World Clock: current time around the globe', 'score': 0.45914948, 'raw_content': None}, {'title': 'What time is it - Exact time - Any time zone - vClock', 'url': 'https://vclock.com/time/', 'content': 'Online clock. What time is it in different regions of United States, Canada, Australia, Europe and the World. What time is it - Exact time - Any time zone - vClock ... On this website, you can find out the current time and date in any country and city in the world. You can also view the time difference between your location and that of another', 'score': 0.15111576, 'raw_content': None}, {'title': 'Time.is - exact time, any time zone', 'url': 'https://time.is/', 'content': '7 million locations, 58 languages, synchronized with atomic clock time.', 'score': 0.08800977, 'raw_content': None}], 'response_time': 2.62}\u001b[0m\u001b[32;1m\u001b[1;3m\n", "Invoking: `tavily_search` with `{'query': 'days between July 30, 1970 and current date'}`\n", "responded: <PERSON>, the director of the 2023 film **<PERSON><PERSON><PERSON>**, was born on **July 30, 1970**.\n", "\n", "To calculate his age in days as of today, we can use the following formula:\n", "\n", "\\[ \\text{Age in days} = (\\text{Current Date} - \\text{Birthdate}) \\]\n", "\n", "Let's calculate this now.\n", "\n", "\u001b[0m\u001b[36;1m\u001b[1;3m{'query': 'days between July 30, 1970 and current date', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': 'Date Calculator', 'url': 'https://www.calculator.net/date-calculator.html', 'content': 'Days Between Two Dates. Find the number of years, months, weeks, and days between dates. Click \"Settings\" to define holidays. ... The months of April, June, September, and November have 30 days, while the rest have 31 days except for February, which has 28 days in a standard year, and 29 in a leap year. ... to 1 day in 3,030 years with respect', 'score': 0.15738304, 'raw_content': None}, {'title': 'Days Calculator (Days Between Dates)', 'url': 'https://www.gigacalculator.com/calculators/days-between-dates-calculator.php', 'content': 'Days calculator to count how many days between any two dates. Find out how many days there are between any two dates, e.g. days between today and date X in the future, or date Y in the past and today. Calculate how many days you have to a deadline with this free days between dates calculator. Days calculator online for time between dates, including days since or days from a given date.', 'score': 0.15232232, 'raw_content': None}, {'title': 'Days Calculator', 'url': 'https://time-calculator.net/days.html', 'content': 'The days calculator can find the days or duration between two dates and also gives the time interval in years, months, and days. Start Date: Today. End Date: Today. Include last day (+1 day) = Calculate. × Reset. Result:', 'score': 0.1465877, 'raw_content': None}, {'title': 'Date Calculator - Add Days to Date & Days Between Dates', 'url': 'https://timedatecalc.com/date-calculator', 'content': \"How to Add Days to Date. Enter the start date To get started, enter the start date to which you need to add/subtract days (today's date is initially displayed). Use the calendar for more convenient date selection. Enter the number of days Next, enter the time value you need to add or subtract from the start date (years, months, weeks, days).\", 'score': 0.14245868, 'raw_content': None}, {'title': 'Date Duration Calculator: Days Between Dates - timeanddate.com', 'url': 'https://www.timeanddate.com/date/duration.html', 'content': 'Help and Example Use. Some typical uses for the Date Calculators; API Services for Developers. API for Business Date Calculators; Date Calculators. Time and Date Duration - Calculate duration, with both date and time included; Date Calculator - Add or subtract days, months, years; Weekday Calculator - What day is this date?; Birthday Calculator - Find when you are 1 billion seconds old', 'score': 0.12024263, 'raw_content': None}], 'response_time': 2.27}\u001b[0m\u001b[32;1m\u001b[1;3mChristopher Nolan was born on July 30, 1970. As of today, June 15, 2025, he is 19,944 days old.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': 'Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?',\n", " 'output': '<PERSON> was born on July 30, 1970. As of today, June 15, 2025, he is 19,944 days old.'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.globals import set_verbose\n", "\n", "set_verbose(True)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)\n", "agent_executor.invoke(\n", "    {\"input\": \"Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?\"}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `set_debug(True)`\n", "\n", "Setting the global `debug` flag will cause all LangChain components with callback support (chains, models, agents, tools, retrievers) to print the inputs they receive and outputs they generate. This is the most verbose setting and will fully log raw inputs and outputs."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2025-06-17T18:46:44.586990Z", "start_time": "2025-06-17T18:46:27.494581Z"}, "scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad>] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad> > chain:RunnableParallel<agent_scratchpad>] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad> > chain:RunnableParallel<agent_scratchpad> > chain:RunnableLambda] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"\"\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad> > chain:RunnableParallel<agent_scratchpad> > chain:RunnableLambda] [0ms] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"output\": []\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad> > chain:RunnableParallel<agent_scratchpad>] [1ms] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"agent_scratchpad\": []\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad>] [2ms] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"input\": \"Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?\",\n", "  \"intermediate_steps\": [],\n", "  \"agent_scratchpad\": []\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > prompt:ChatPromptTemplate] Entering Prompt run with input:\n", "\u001b[0m{\n", "  \"input\": \"Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?\",\n", "  \"intermediate_steps\": [],\n", "  \"agent_scratchpad\": []\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > prompt:ChatPromptTemplate] [0ms] Exiting Prompt run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[llm/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > llm:ChatOpenAI] Entering LLM run with input:\n", "\u001b[0m{\n", "  \"prompts\": [\n", "    \"System: You are a helpful assistant.\\nHuman: Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?\"\n", "  ]\n", "}\n", "\u001b[36;1m\u001b[1;3m[llm/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > llm:ChatOpenAI] [2.87s] Exiting LLM run with output:\n", "\u001b[0m{\n", "  \"generations\": [\n", "    [\n", "      {\n", "        \"text\": \"\",\n", "        \"generation_info\": {\n", "          \"finish_reason\": \"tool_calls\",\n", "          \"model_name\": \"gpt-4-turbo-2024-04-09\",\n", "          \"system_fingerprint\": \"fp_de235176ee\",\n", "          \"service_tier\": \"default\"\n", "        },\n", "        \"type\": \"ChatGenerationChunk\",\n", "        \"message\": {\n", "          \"lc\": 1,\n", "          \"type\": \"constructor\",\n", "          \"id\": [\n", "            \"langchain\",\n", "            \"schema\",\n", "            \"messages\",\n", "            \"AIMessageChunk\"\n", "          ],\n", "          \"kwargs\": {\n", "            \"content\": \"\",\n", "            \"additional_kwargs\": {\n", "              \"tool_calls\": [\n", "                {\n", "                  \"index\": 0,\n", "                  \"id\": \"call_7470602CBXe0TCtzU9kNddmI\",\n", "                  \"function\": {\n", "                    \"arguments\": \"{\\\"query\\\": \\\"director of the 2023 film <PERSON><PERSON><PERSON>\\\"}\",\n", "                    \"name\": \"tavily_search\"\n", "                  },\n", "                  \"type\": \"function\"\n", "                },\n", "                {\n", "                  \"index\": 1,\n", "                  \"id\": \"call_NcqiDSEUVpwfSKBTSUDRwJTQ\",\n", "                  \"function\": {\n", "                    \"arguments\": \"{\\\"query\\\": \\\"birth date of <PERSON>\\\"}\",\n", "                    \"name\": \"tavily_search\"\n", "                  },\n", "                  \"type\": \"function\"\n", "                }\n", "              ]\n", "            },\n", "            \"response_metadata\": {\n", "              \"finish_reason\": \"tool_calls\",\n", "              \"model_name\": \"gpt-4-turbo-2024-04-09\",\n", "              \"system_fingerprint\": \"fp_de235176ee\",\n", "              \"service_tier\": \"default\"\n", "            },\n", "            \"type\": \"AIMessageChunk\",\n", "            \"id\": \"run--421b146e-04d7-4e72-8c1d-68c9b92995fe\",\n", "            \"tool_calls\": [\n", "              {\n", "                \"name\": \"tavily_search\",\n", "                \"args\": {\n", "                  \"query\": \"director of the 2023 film <PERSON><PERSON><PERSON>\"\n", "                },\n", "                \"id\": \"call_7470602CBXe0TCtzU9kNddmI\",\n", "                \"type\": \"tool_call\"\n", "              },\n", "              {\n", "                \"name\": \"tavily_search\",\n", "                \"args\": {\n", "                  \"query\": \"birth date of <PERSON>\"\n", "                },\n", "                \"id\": \"call_NcqiDSEUVpwfSKBTSUDRwJTQ\",\n", "                \"type\": \"tool_call\"\n", "              }\n", "            ],\n", "            \"tool_call_chunks\": [\n", "              {\n", "                \"name\": \"tavily_search\",\n", "                \"args\": \"{\\\"query\\\": \\\"director of the 2023 film <PERSON><PERSON><PERSON>\\\"}\",\n", "                \"id\": \"call_7470602CBXe0TCtzU9kNddmI\",\n", "                \"index\": 0,\n", "                \"type\": \"tool_call_chunk\"\n", "              },\n", "              {\n", "                \"name\": \"tavily_search\",\n", "                \"args\": \"{\\\"query\\\": \\\"birth date of <PERSON>\\\"}\",\n", "                \"id\": \"call_NcqiDSEUVpwfSKBTSUDRwJTQ\",\n", "                \"index\": 1,\n", "                \"type\": \"tool_call_chunk\"\n", "              }\n", "            ],\n", "            \"invalid_tool_calls\": []\n", "          }\n", "        }\n", "      }\n", "    ]\n", "  ],\n", "  \"llm_output\": null,\n", "  \"run\": null,\n", "  \"type\": \"LLMResult\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > parser:ToolsAgentOutputParser] Entering Parser run with input:\n", "\u001b[0m[inputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > parser:ToolsAgentOutputParser] [0ms] Exiting Parser run with output:\n", "\u001b[0m[outputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence] [2.88s] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[tool/start]\u001b[0m \u001b[1m[chain:AgentExecutor > tool:tavily_search] Entering Tool run with input:\n", "\u001b[0m\"{'query': 'director of the 2023 film <PERSON><PERSON><PERSON>'}\"\n", "\u001b[36;1m\u001b[1;3m[tool/end]\u001b[0m \u001b[1m[chain:AgentExecutor > tool:tavily_search] [2.11s] Exiting Tool run with output:\n", "\u001b[0m\"{'query': 'director of the 2023 film <PERSON><PERSON><PERSON>', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': '<PERSON><PERSON><PERSON> (film) - Wikipedia', 'url': 'https://en.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(film)', 'content': \"Donate Create account Log in Personal tools Donate Create account Log in Pages for logged out editors learn more Contributions Talk Toggle the table of contents Contents move to sidebar hide (Top) 1 Plot 2 Cast 3 ProductionToggle Production subsection 3.1 Development 3.2 Writing 3.3 Casting 3.4 Filming 3.5 Post-production 4 Music 5 Marketing 6 ReleaseToggle Release subsection 6.1 Theatrical 6.1.1 Classifications and censorship 6.1.2 Bhagavad Gita controversy 6.2 Home media 7 ReceptionToggle Reception subsection 7.1 Box office 7.1.1 United States and Canada 7.1.2 Japan 7.1.3 Other territories 7.2 Critical response 7.3 Influence on legislation 8 Accuracy and omissions 9 Accolades 10 See also 11 Notes 12 References 13 Further reading 14 External links Op<PERSON>heimer (film) 70 languages العربية অসমীয়া Azərbaycanca বাংলা Беларуская भोजपुरी Български Bosanski Català Čeština Cymraeg Dansk Deutsch डोटेली Eesti Ελληνικά Español Euskara فارسی Français Gaeilge Galego 한국어 Հայերեն हिन्दी Ido Bahasa Indonesia Italiano עברית Jawa ქართული Қазақша Latina Latviešu Lietuvių Magyar Македонски മലയാളം मराठी مصرى مازِرونی Bahasa Melayu Nederlands नेपाली 日本語 Norsk bokmål Oʻzbekcha / ўзбекча ਪੰਜਾਬੀ Polski Português Română Русский Shqip Simple English Slovenčina Slovenščina کوردی Српски / srpski Suomi Svenska தமிழ் తెలుగు ไทย Тоҷикӣ Türkçe Українська اردو Tiếng Việt 粵語 中文 Edit links Article Talk English Read Edit View history Tools Tools move to sidebar hide Actions Read Edit View history General What links here Related changes Upload file Permanent link Page information Cite this page Get shortened URL Download QR code Expand all Edit interlanguage links Print/export Download as PDF Printable version In other projects Wikimedia Commons Wikiquote Wikidata item From Wikipedia, the free encyclopedia 2023 film by Christopher Nolan | Oppenheimer | | --- | | Theatrical release poster | | Directed by | Christopher Nolan | | Screenplay by | Christopher Nolan | | Based on | American Prometheus by Kai Bird Martin J. Sherwin | | Produced by | Emma Thomas Charles Roven Christopher Nolan | | Starring | Cillian Murphy Emily Blunt Matt Damon Robert Downey Jr. Florence Pugh Josh Hartnett Casey Affleck Rami Malek Kenneth Branagh | | Cinematography | Hoyte van Hoytema | | Edited by | Jennifer Lame | | Music by | Ludwig Göransson | | Production companies | Universal Pictures[1][2] Syncopy[1][2] Atlas Entertainment[1][2] Breakheart Films[2] Peters Creek Entertainment[2] Gadget Films[1][3] | | Distributed by | Universal Pictures | | Release dates | July 11, 2023 (2023-07-11) (Le Grand Rex) July 21, 2023 (2023-07-21) (United States and United Kingdom) | | Running time | 180 minutes[4] | | Countries | United States United Kingdom | | Language | English | | Budget | $100 million[5] | | Box office | $975.8 million[6][7] | Oppenheimer is a 2023 epic biographical drama film written, produced, and directed by Christopher Nolan. [8] It follows the life of J. Robert Oppenheimer, the American theoretical physicist who helped develop the first nuclear weapons during World War II. Based on the 2005 biography American Prometheus by Kai Bird and Martin J. Sherwin, the film dramatizes Oppenheimer's studies, his direction of the Los Alamos Laboratory and his 1954 security hearing. Oppenheimer received critical acclaim and grossed $975 million worldwide, becoming the third-highest-grossing film of 2023, the highest-grossing World War II-related film, the highest-grossing biographical film and the second-highest-grossing R-rated film of all time at the time of its release.\", 'score': 0.9475027, 'raw_content': None}, {'title': 'Oppenheimer | Cast, Film, Length, Plot, Actors, Awards, & Facts ...', 'url': 'https://www.britannica.com/topic/Oppenheimer-film', 'content': 'J. Robert Oppenheimer Robert Downey, Jr. Oppenheimer # Oppenheimer Oppenheimer, American and British dramatic biographical film, released in 2023, that explores the life and legacy of the American physicist J. Robert Oppenheimer, who played a key role in the development of the atomic bomb. Robert Oppenheimer (2005). Film critics’ reaction to Oppenheimer was overwhelmingly positive. Oppenheimer grossed more than $300 million domestically and more than $600 million internationally by the end of November 2023, making it the second highest grossing R-rated film of all time. The film also dominated the Academy Awards nominations, garnering 13 nominations compared with the 8 for Greta Gerwig’s Barbie, which opened the same weekend as Oppenheimer but topped Nolan’s film at the box office.', 'score': 0.76194656, 'raw_content': None}, {'title': 'Oppenheimer (2023) - Full cast & crew - IMDb', 'url': 'https://www.imdb.com/title/tt15398776/fullcredits/', 'content': 'Oppenheimer (2023) - Cast and crew credits, including actors, actresses, directors, writers and more. Menu. ... Oscars Pride Month American Black Film Festival Summer Watch Guide STARmeter Awards Awards Central Festival Central All Events. ... second unit director: visual effects (uncredited) Francesca Kaimer Millea.', 'score': 0.683948, 'raw_content': None}, {'title': \"'Oppenheimer' director Christopher Nolan says the film is his darkest - NPR\", 'url': 'https://www.npr.org/2023/08/14/1193448291/oppenheimer-director-christopher-nolan', 'content': '# \\'Like it or not, we live in Oppenheimer\\'s world,\\' says director Christopher Nolan #### \\'Like it or not, we live in Oppenheimer\\'s world,\\' says director Christopher Nolan But he says the story of Robert Oppenheimer, known as the father of the atomic bomb, stayed with him in a way his other films didn\\'t. Nolan says he was drawn to the tension of Oppenheimer\\'s story — particularly the disconnect between the joy the physicist felt at the success of the Trinity test, and the horror that later resulted. Writer, director and producer Christopher Nolan says Oppenheimer is the \"darkest\" of all the films he\\'s worked on. Writer, director and producer Christopher Nolan says Oppenheimer is the \"darkest\" of all the films he\\'s worked on.', 'score': 0.6255073, 'raw_content': None}, {'title': 'An extended interview with Christopher Nolan, director of Oppenheimer', 'url': 'https://thebulletin.org/premium/2023-07/an-extended-interview-with-christopher-nolan-director-of-oppenheimer/', 'content': 'A group of Manhattan Project scientists and engineers also focused on wider public education on nuclear weapons and energy (and science generally) through the creation of the Bulletin of the Atomic Scientists; Oppenheimer served as the first chair of the magazine’s Board of Sponsors.[5] As time has passed, more evidence has come to light of the bias and unfairness of the process that Dr. Oppenheimer was subjected to while the evidence of his loyalty and love of country have only been further affirmed.”[8] Decades after the fact, records of the Oppenheimer security hearing made it clear that, rather than any disloyalty to the nation, it was his principled opposition to development of the hydrogen bomb—a nuclear fusion-based weapon of immensely greater power than the fission weapons used to decimate Hiroshima and Nagasaki in 1945—that was key to the decision to essentially bar him from government service. Robert Oppenheimer, Los Alamos, Manhattan Project, Nolan, atomic bomb, movie', 'score': 0.32472825, 'raw_content': None}], 'response_time': 1.39}\"\n", "\u001b[32;1m\u001b[1;3m[tool/start]\u001b[0m \u001b[1m[chain:AgentExecutor > tool:tavily_search] Entering Tool run with input:\n", "\u001b[0m\"{'query': 'birth date of <PERSON>'}\"\n", "\u001b[36;1m\u001b[1;3m[tool/end]\u001b[0m \u001b[1m[chain:AgentExecutor > tool:tavily_search] [1.11s] Exiting Tool run with output:\n", "\u001b[0m\"{'query': 'birth date of <PERSON>', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': '<PERSON> | Biography, Movies, Batman, <PERSON>penheimer, & Facts ...', 'url': 'https://www.britannica.com/biography/<PERSON>-<PERSON>-<PERSON>-director', 'content': '<PERSON> (born July 30, 1970, London, England) is a British film director and writer acclaimed for his noirish visual aesthetic and unconventional, often highly conceptual narratives. In 2024 <PERSON> won an Academy Award for best director for <PERSON><PERSON><PERSON> (2023), which was also named best picture. <PERSON>’s breakthrough came with the 2000 film Memento, a sleeper hit that he adapted from a short story written by his brother <PERSON>. The film was a critical and popular success and garnered the <PERSON> brothers an Academy Award nomination for best original screenplay. <PERSON>’s highly anticipated Batman <PERSON> (2005), starring <PERSON>, focuses on the superhero’s origins and features settings and a tone that are grimmer and more realistic than those of previous Batman films. <PERSON>’s 2023 film, <PERSON><PERSON><PERSON>, depicts American theoretical physicist  <PERSON><PERSON>', 'score': 0.8974172, 'raw_content': None}, {'title': '<PERSON> - <PERSON>', 'url': 'https://m.imdb.com/name/nm0634240/', 'content': '<PERSON>. Writer: Tenet. Best known for his cerebral, often nonlinear, storytelling, acclaimed Academy Award winner writer/director/producer Sir <PERSON> was born in London, England. Over the course of more than 25 years of filmmaking, <PERSON> has gone from low-budget independent films to working on some of the biggest blockbusters ever made and became one of the most', 'score': 0.5087155, 'raw_content': None}, {'title': '<PERSON> Nolan: Biography, Movie Director, Filmmaker', 'url': 'https://www.biography.com/movies-tv/christopher-nolan', 'content': 'Opt-Out Icon Christopher Nolan is an Academy Award-winning movie director and screenwriter who’s helmed several hit films, including Inception, The Dark Knight, Interstellar, and Oppenheimer. We may earn commission from links on this page, but we only recommend products we back. Christopher Nolan is a British-American filmmaker known for his complex storytelling in big-budget movies such as Inception (2010), Interstellar (2014) and Tenet (2020). Play Icon We may earn commission from links on this page, but we only recommend products we back. Biography and associated logos are trademarks of A+E Networks®protected in the US and other countries around the globe. Opt-Out Icon', 'score': 0.28185803, 'raw_content': None}, {'title': 'Christopher Nolan \"Film Director\" - Biography, Age and Married', 'url': 'https://biographyhost.com/p/christopher-nolan-biography.html', 'content': 'Christopher Nolan is a renowned British-American filmmaker celebrated for his innovative storytelling in films like Oppenheimer and Inception. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films.', 'score': 0.19905913, 'raw_content': None}, {'title': 'Christopher Nolan - Wikipedia', 'url': 'https://en.wikipedia.org/wiki/Christopher_Nolan', 'content': 'Following a positive word of mouth and screenings in 500 theatres, it earned $40\\xa0million.[41] Memento premiered at the Venice Film Festival in September 2000 to critical acclaim.[42] Joe Morgenstern of The Wall Street Journal wrote in his review, \"I can\\'t remember when a movie has seemed so clever, strangely affecting and slyly funny at the very same time.\"[43] In the book The Philosophy of Neo-Noir, Basil Smith drew a comparison with John Locke\\'s An Essay Concerning Human Understanding, which argues that conscious memories constitute our identities – a theme Nolan explores in the film.[44] Memento earned Nolan many accolades, including nominations for an Academy Award and a Golden Globe Award for Best Screenplay, as well as two Independent Spirit Awards: Best Director and Best Screenplay.[45][46] Six critics listed it as one of the best films of the 2000s.[47] In 2001, Nolan and Emma Thomas founded the production company Syncopy Inc.[48][b]', 'score': 0.1508904, 'raw_content': None}], 'response_time': 0.74}\"\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad>] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad> > chain:RunnableParallel<agent_scratchpad>] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad> > chain:RunnableParallel<agent_scratchpad> > chain:RunnableLambda] Entering Chain run with input:\n", "\u001b[0m{\n", "  \"input\": \"\"\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad> > chain:RunnableParallel<agent_scratchpad> > chain:RunnableLambda] [0ms] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad> > chain:RunnableParallel<agent_scratchpad>] [1ms] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > chain:RunnableAssign<agent_scratchpad>] [2ms] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > prompt:ChatPromptTemplate] Entering Prompt run with input:\n", "\u001b[0m[inputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > prompt:ChatPromptTemplate] [0ms] Exiting Prompt run with output:\n", "\u001b[0m[outputs]\n", "\u001b[32;1m\u001b[1;3m[llm/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > llm:ChatOpenAI] Entering LLM run with input:\n", "\u001b[0m{\n", "  \"prompts\": [\n", "    \"System: You are a helpful assistant.\\nHuman: Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?\\nAI: \\nTool: {\\\"query\\\": \\\"director of the 2023 film <PERSON><PERSON><PERSON>\\\", \\\"follow_up_questions\\\": null, \\\"answer\\\": null, \\\"images\\\": [], \\\"results\\\": [{\\\"title\\\": \\\"<PERSON><PERSON>heimer (film) - Wikipedia\\\", \\\"url\\\": \\\"https://en.wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(film)\\\", \\\"content\\\": \\\"Donate Create account Log in Personal tools Donate Create account Log in Pages for logged out editors learn more Contributions Talk Toggle the table of contents Contents move to sidebar hide (Top) 1 Plot 2 Cast 3 ProductionToggle Production subsection 3.1 Development 3.2 Writing 3.3 Casting 3.4 Filming 3.5 Post-production 4 Music 5 Marketing 6 ReleaseToggle Release subsection 6.1 Theatrical 6.1.1 Classifications and censorship 6.1.2 Bhagavad Gita controversy 6.2 Home media 7 ReceptionToggle Reception subsection 7.1 Box office 7.1.1 United States and Canada 7.1.2 Japan 7.1.3 Other territories 7.2 Critical response 7.3 Influence on legislation 8 Accuracy and omissions 9 Accolades 10 See also 11 Notes 12 References 13 Further reading 14 External links <PERSON><PERSON><PERSON> (film) 70 languages العربية অসমীয়া Azərbaycanca বাংলা Беларуская भोजपुरी Български Bosanski Català Čeština Cymraeg Dansk Deutsch डोटेली Eesti Ελληνικά Español Euskara فارسی Français Gaeilge Galego 한국어 Հայերեն हिन्दी Ido Bahasa Indonesia Italiano עברית Jawa ქართული Қазақша Latina Latviešu Lietuvių Magyar Македонски മലയാളം मराठी مصرى مازِرونی Bahasa Melayu Nederlands नेपाली 日本語 Norsk bokmål Oʻzbekcha / ўзбекча ਪੰਜਾਬੀ Polski Português Română Русский Shqip Simple English Slovenčina Slovenščina کوردی Српски / srpski Suomi Svenska தமிழ் తెలుగు ไทย Тоҷикӣ Türkçe Українська اردو Tiếng Việt 粵語 中文 Edit links Article Talk English Read Edit View history Tools Tools move to sidebar hide Actions Read Edit View history General What links here Related changes Upload file Permanent link Page information Cite this page Get shortened URL Download QR code Expand all Edit interlanguage links Print/export Download as PDF Printable version In other projects Wikimedia Commons Wikiquote Wikidata item From Wikipedia, the free encyclopedia 2023 film by Christopher Nolan | Oppenheimer | | --- | | Theatrical release poster | | Directed by | Christopher Nolan | | Screenplay by | Christopher Nolan | | Based on | American Prometheus by Kai Bird Martin J. Sherwin | | Produced by | Emma Thomas Charles Roven Christopher Nolan | | Starring | Cillian Murphy Emily Blunt Matt Damon Robert Downey Jr. Florence Pugh Josh Hartnett Casey Affleck Rami Malek Kenneth Branagh | | Cinematography | Hoyte van Hoytema | | Edited by | Jennifer Lame | | Music by | Ludwig Göransson | | Production companies | Universal Pictures[1][2] Syncopy[1][2] Atlas Entertainment[1][2] Breakheart Films[2] Peters Creek Entertainment[2] Gadget Films[1][3] | | Distributed by | Universal Pictures | | Release dates | July 11, 2023 (2023-07-11) (Le Grand Rex) July 21, 2023 (2023-07-21) (United States and United Kingdom) | | Running time | 180 minutes[4] | | Countries | United States United Kingdom | | Language | English | | Budget | $100 million[5] | | Box office | $975.8 million[6][7] | Oppenheimer is a 2023 epic biographical drama film written, produced, and directed by Christopher Nolan. [8] It follows the life of J. Robert Oppenheimer, the American theoretical physicist who helped develop the first nuclear weapons during World War II. Based on the 2005 biography American Prometheus by Kai Bird and Martin J. Sherwin, the film dramatizes Oppenheimer's studies, his direction of the Los Alamos Laboratory and his 1954 security hearing. Oppenheimer received critical acclaim and grossed $975 million worldwide, becoming the third-highest-grossing film of 2023, the highest-grossing World War II-related film, the highest-grossing biographical film and the second-highest-grossing R-rated film of all time at the time of its release.\\\", \\\"score\\\": 0.9475027, \\\"raw_content\\\": null}, {\\\"title\\\": \\\"Oppenheimer | Cast, Film, Length, Plot, Actors, Awards, & Facts ...\\\", \\\"url\\\": \\\"https://www.britannica.com/topic/Oppenheimer-film\\\", \\\"content\\\": \\\"J. Robert Oppenheimer Robert Downey, Jr. Oppenheimer # Oppenheimer Oppenheimer, American and British dramatic biographical film, released in 2023, that explores the life and legacy of the American physicist J. Robert Oppenheimer, who played a key role in the development of the atomic bomb. Robert Oppenheimer (2005). Film critics’ reaction to Oppenheimer was overwhelmingly positive. Oppenheimer grossed more than $300 million domestically and more than $600 million internationally by the end of November 2023, making it the second highest grossing R-rated film of all time. The film also dominated the Academy Awards nominations, garnering 13 nominations compared with the 8 for Greta Gerwig’s Barbie, which opened the same weekend as Oppenheimer but topped Nolan’s film at the box office.\\\", \\\"score\\\": 0.76194656, \\\"raw_content\\\": null}, {\\\"title\\\": \\\"Oppenheimer (2023) - Full cast & crew - IMDb\\\", \\\"url\\\": \\\"https://www.imdb.com/title/tt15398776/fullcredits/\\\", \\\"content\\\": \\\"Oppenheimer (2023) - Cast and crew credits, including actors, actresses, directors, writers and more. Menu. ... Oscars Pride Month American Black Film Festival Summer Watch Guide STARmeter Awards Awards Central Festival Central All Events. ... second unit director: visual effects (uncredited) Francesca Kaimer Millea.\\\", \\\"score\\\": 0.683948, \\\"raw_content\\\": null}, {\\\"title\\\": \\\"'Oppenheimer' director Christopher Nolan says the film is his darkest - NPR\\\", \\\"url\\\": \\\"https://www.npr.org/2023/08/14/1193448291/oppenheimer-director-christopher-nolan\\\", \\\"content\\\": \\\"# 'Like it or not, we live in Oppenheimer's world,' says director Christopher Nolan #### 'Like it or not, we live in Oppenheimer's world,' says director Christopher Nolan But he says the story of Robert Oppenheimer, known as the father of the atomic bomb, stayed with him in a way his other films didn't. Nolan says he was drawn to the tension of Oppenheimer's story — particularly the disconnect between the joy the physicist felt at the success of the Trinity test, and the horror that later resulted. Writer, director and producer Christopher Nolan says Oppenheimer is the \\\\\\\"darkest\\\\\\\" of all the films he's worked on. Writer, director and producer Christopher Nolan says Oppenheimer is the \\\\\\\"darkest\\\\\\\" of all the films he's worked on.\\\", \\\"score\\\": 0.6255073, \\\"raw_content\\\": null}, {\\\"title\\\": \\\"An extended interview with Christopher Nolan, director of Oppenheimer\\\", \\\"url\\\": \\\"https://thebulletin.org/premium/2023-07/an-extended-interview-with-christopher-nolan-director-of-oppenheimer/\\\", \\\"content\\\": \\\"A group of Manhattan Project scientists and engineers also focused on wider public education on nuclear weapons and energy (and science generally) through the creation of the Bulletin of the Atomic Scientists; Oppenheimer served as the first chair of the magazine’s Board of Sponsors.[5] As time has passed, more evidence has come to light of the bias and unfairness of the process that Dr. Oppenheimer was subjected to while the evidence of his loyalty and love of country have only been further affirmed.”[8] Decades after the fact, records of the Oppenheimer security hearing made it clear that, rather than any disloyalty to the nation, it was his principled opposition to development of the hydrogen bomb—a nuclear fusion-based weapon of immensely greater power than the fission weapons used to decimate Hiroshima and Nagasaki in 1945—that was key to the decision to essentially bar him from government service. Robert Oppenheimer, Los Alamos, Manhattan Project, Nolan, atomic bomb, movie\\\", \\\"score\\\": 0.32472825, \\\"raw_content\\\": null}], \\\"response_time\\\": 1.39}\\nTool: {\\\"query\\\": \\\"birth date of Christopher Nolan\\\", \\\"follow_up_questions\\\": null, \\\"answer\\\": null, \\\"images\\\": [], \\\"results\\\": [{\\\"title\\\": \\\"Christopher Nolan | Biography, Movies, Batman, Oppenheimer, & Facts ...\\\", \\\"url\\\": \\\"https://www.britannica.com/biography/Christopher-Nolan-British-director\\\", \\\"content\\\": \\\"Christopher Nolan (born July 30, 1970, London, England) is a British film director and writer acclaimed for his noirish visual aesthetic and unconventional, often highly conceptual narratives. In 2024 Nolan won an Academy Award for best director for Oppenheimer (2023), which was also named best picture. Nolan’s breakthrough came with the 2000 film Memento, a sleeper hit that he adapted from a short story written by his brother Jonathan Nolan. The film was a critical and popular success and garnered the Nolan brothers an Academy Award nomination for best original screenplay. Nolan’s highly anticipated Batman Begins (2005), starring Christian Bale, focuses on the superhero’s origins and features settings and a tone that are grimmer and more realistic than those of previous Batman films. Nolan’s 2023 film, Oppenheimer, depicts American theoretical physicist  J.\\\", \\\"score\\\": 0.8974172, \\\"raw_content\\\": null}, {\\\"title\\\": \\\"Christopher Nolan - IMDb\\\", \\\"url\\\": \\\"https://m.imdb.com/name/nm0634240/\\\", \\\"content\\\": \\\"Christopher Nolan. Writer: Tenet. Best known for his cerebral, often nonlinear, storytelling, acclaimed Academy Award winner writer/director/producer Sir Christopher Nolan CBE was born in London, England. Over the course of more than 25 years of filmmaking, Nolan has gone from low-budget independent films to working on some of the biggest blockbusters ever made and became one of the most\\\", \\\"score\\\": 0.5087155, \\\"raw_content\\\": null}, {\\\"title\\\": \\\"Christopher Nolan: Biography, Movie Director, Filmmaker\\\", \\\"url\\\": \\\"https://www.biography.com/movies-tv/christopher-nolan\\\", \\\"content\\\": \\\"Opt-Out Icon Christopher Nolan is an Academy Award-winning movie director and screenwriter who’s helmed several hit films, including Inception, The Dark Knight, Interstellar, and Oppenheimer. We may earn commission from links on this page, but we only recommend products we back. Christopher Nolan is a British-American filmmaker known for his complex storytelling in big-budget movies such as Inception (2010), Interstellar (2014) and Tenet (2020). Play Icon We may earn commission from links on this page, but we only recommend products we back. Biography and associated logos are trademarks of A+E Networks®protected in the US and other countries around the globe. Opt-Out Icon\\\", \\\"score\\\": 0.28185803, \\\"raw_content\\\": null}, {\\\"title\\\": \\\"Christopher Nolan \\\\\\\"Film Director\\\\\\\" - Biography, Age and Married\\\", \\\"url\\\": \\\"https://biographyhost.com/p/christopher-nolan-biography.html\\\", \\\"content\\\": \\\"Christopher Nolan is a renowned British-American filmmaker celebrated for his innovative storytelling in films like Oppenheimer and Inception. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films. Christopher Nolan is a British-American filmmaker renowned for his innovative storytelling and visually stunning films.\\\", \\\"score\\\": 0.19905913, \\\"raw_content\\\": null}, {\\\"title\\\": \\\"Christopher Nolan - Wikipedia\\\", \\\"url\\\": \\\"https://en.wikipedia.org/wiki/Christopher_Nolan\\\", \\\"content\\\": \\\"Following a positive word of mouth and screenings in 500 theatres, it earned $40 million.[41] Memento premiered at the Venice Film Festival in September 2000 to critical acclaim.[42] Joe Morgenstern of The Wall Street Journal wrote in his review, \\\\\\\"I can't remember when a movie has seemed so clever, strangely affecting and slyly funny at the very same time.\\\\\\\"[43] In the book The Philosophy of Neo-Noir, Basil Smith drew a comparison with John Locke's An Essay Concerning Human Understanding, which argues that conscious memories constitute our identities – a theme Nolan explores in the film.[44] Memento earned Nolan many accolades, including nominations for an Academy Award and a Golden Globe Award for Best Screenplay, as well as two Independent Spirit Awards: Best Director and Best Screenplay.[45][46] Six critics listed it as one of the best films of the 2000s.[47] In 2001, Nolan and Emma Thomas founded the production company Syncopy Inc.[48][b]\\\", \\\"score\\\": 0.1508904, \\\"raw_content\\\": null}], \\\"response_time\\\": 0.74}\"\n", "  ]\n", "}\n", "\u001b[36;1m\u001b[1;3m[llm/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > llm:ChatOpenAI] [10.98s] Exiting LLM run with output:\n", "\u001b[0m{\n", "  \"generations\": [\n", "    [\n", "      {\n", "        \"text\": \"The 2023 film **<PERSON><PERSON><PERSON>** was directed by **<PERSON>**.\\n\\nChrist<PERSON><PERSON> was born on **July 30, 1970**. To calculate his age in days as of today:\\n\\n1. First, determine the total number of days from his birthdate to today.\\n2. Use the formula: \\\\[ \\\\text{Age in days} = \\\\text{Current Date} - \\\\text{Birth Date} \\\\]\\n\\nLet's calculate:\\n\\n- Birthdate: July 30, 1970\\n- Today's Date: December 7, 2023\\n\\nUsing a date calculator or similar method, we find that <PERSON> is approximately **19,480 days old** as of today.\",\n", "        \"generation_info\": {\n", "          \"finish_reason\": \"stop\",\n", "          \"model_name\": \"gpt-4-turbo-2024-04-09\",\n", "          \"system_fingerprint\": \"fp_de235176ee\",\n", "          \"service_tier\": \"default\"\n", "        },\n", "        \"type\": \"ChatGenerationChunk\",\n", "        \"message\": {\n", "          \"lc\": 1,\n", "          \"type\": \"constructor\",\n", "          \"id\": [\n", "            \"langchain\",\n", "            \"schema\",\n", "            \"messages\",\n", "            \"AIMessageChunk\"\n", "          ],\n", "          \"kwargs\": {\n", "            \"content\": \"The 2023 film **<PERSON><PERSON><PERSON>** was directed by **<PERSON>**.\\n\\nChrist<PERSON><PERSON> was born on **July 30, 1970**. To calculate his age in days as of today:\\n\\n1. First, determine the total number of days from his birthdate to today.\\n2. Use the formula: \\\\[ \\\\text{Age in days} = \\\\text{Current Date} - \\\\text{Birth Date} \\\\]\\n\\nLet's calculate:\\n\\n- Birthdate: July 30, 1970\\n- Today's Date: December 7, 2023\\n\\nUsing a date calculator or similar method, we find that <PERSON> is approximately **19,480 days old** as of today.\",\n", "            \"response_metadata\": {\n", "              \"finish_reason\": \"stop\",\n", "              \"model_name\": \"gpt-4-turbo-2024-04-09\",\n", "              \"system_fingerprint\": \"fp_de235176ee\",\n", "              \"service_tier\": \"default\"\n", "            },\n", "            \"type\": \"AIMessageChunk\",\n", "            \"id\": \"run--21b0c760-dbf4-45e1-89fd-d1edfa1eb9d5\",\n", "            \"tool_calls\": [],\n", "            \"invalid_tool_calls\": []\n", "          }\n", "        }\n", "      }\n", "    ]\n", "  ],\n", "  \"llm_output\": null,\n", "  \"run\": null,\n", "  \"type\": \"LLMResult\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > parser:ToolsAgentOutputParser] Entering Parser run with input:\n", "\u001b[0m[inputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence > parser:ToolsAgentOutputParser] [0ms] Exiting Parser run with output:\n", "\u001b[0m[outputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor > chain:RunnableSequence] [10.99s] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[chain:AgentExecutor] [17.09s] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"output\": \"The 2023 film **<PERSON><PERSON><PERSON>** was directed by **<PERSON>**.\\n\\nChrist<PERSON><PERSON> was born on **July 30, 1970**. To calculate his age in days as of today:\\n\\n1. First, determine the total number of days from his birthdate to today.\\n2. Use the formula: \\\\[ \\\\text{Age in days} = \\\\text{Current Date} - \\\\text{Birth Date} \\\\]\\n\\nLet's calculate:\\n\\n- Birthdate: July 30, 1970\\n- Today's Date: December 7, 2023\\n\\nUsing a date calculator or similar method, we find that <PERSON> is approximately **19,480 days old** as of today.\"\n", "}\n"]}, {"data": {"text/plain": ["{'input': 'Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?',\n", " 'output': \"The 2023 film **<PERSON><PERSON><PERSON>** was directed by **<PERSON>**.\\n\\nChrist<PERSON><PERSON> was born on **July 30, 1970**. To calculate his age in days as of today:\\n\\n1. First, determine the total number of days from his birthdate to today.\\n2. Use the formula: \\\\[ \\\\text{Age in days} = \\\\text{Current Date} - \\\\text{Birth Date} \\\\]\\n\\nLet's calculate:\\n\\n- Birthdate: July 30, 1970\\n- Today's Date: December 7, 2023\\n\\nUsing a date calculator or similar method, we find that <PERSON> is approximately **19,480 days old** as of today.\"}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.globals import set_debug\n", "\n", "set_debug(True)\n", "set_verbose(False)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)\n", "\n", "agent_executor.invoke(\n", "    {\"input\": \"Who directed the 2023 film <PERSON><PERSON><PERSON> and what is their age in days?\"}\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python (langchain-tutorial-updates)", "language": "python", "name": "langchain-tutorial-updates"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.1"}}, "nbformat": 4, "nbformat_minor": 2}