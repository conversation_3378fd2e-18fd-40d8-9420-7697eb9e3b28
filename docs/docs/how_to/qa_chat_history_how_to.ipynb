{"cells": [{"cell_type": "markdown", "id": "86fc5bb2-017f-434e-8cd6-53ab214a5604", "metadata": {}, "source": ["# How to add chat history\n", "\n", ":::note\n", "\n", "This guide previously used the [RunnableWithMessageHistory](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.history.RunnableWithMessageHistory.html) abstraction. You can access this version of the documentation in the [v0.2 docs](https://python.langchain.com/v0.2/docs/how_to/qa_chat_history_how_to/).\n", "\n", "As of the v0.3 release of LangChain, we recommend that LangChain users take advantage of [LangGraph persistence](https://langchain-ai.github.io/langgraph/concepts/persistence/) to incorporate `memory` into new LangChain applications.\n", "\n", "If your code is already relying on `RunnableWithMessageHistory` or `BaseChatMessageHistory`, you do **not** need to make any changes. We do not plan on deprecating this functionality in the near future as it works for simple chat applications and any code that uses `RunnableWithMessageHistory` will continue to work as expected.\n", "\n", "Please see [How to migrate to LangGraph Memory](/docs/versions/migrating_memory/) for more details.\n", ":::\n", "\n", "\n", "In many [Q&A applications](/docs/concepts/rag/) we want to allow the user to have a back-and-forth conversation, meaning the application needs some sort of \"memory\" of past questions and answers, and some logic for incorporating those into its current thinking.\n", "\n", "In this guide we focus on **adding logic for incorporating historical messages.**\n", "\n", "This is largely a condensed version of the [Conversational RAG tutorial](/docs/tutorials/qa_chat_history).\n", "\n", "We will cover two approaches:\n", "1. [Chains](/docs/how_to/qa_chat_history_how_to#chains), in which we always execute a retrieval step;\n", "2. [Agents](/docs/how_to/qa_chat_history_how_to#agents), in which we give an LLM discretion over whether and how to execute a retrieval step (or multiple steps).\n", "\n", "For the external knowledge source, we will use the same [LLM Powered Autonomous Agents](https://lilianweng.github.io/posts/2023-06-23-agent/) blog post by <PERSON><PERSON> from the [RAG tutorial](/docs/tutorials/rag).\n", "\n", "Both approaches leverage [LangGraph](https://langchain-ai.github.io/langgraph/) as an orchestration framework. LangGraph implements a built-in [persistence layer](https://langchain-ai.github.io/langgraph/concepts/persistence/), making it ideal for chat applications that support multiple conversational turns."]}, {"cell_type": "markdown", "id": "487d8d79-5ee9-4aa4-9fdf-cd5f4303e099", "metadata": {}, "source": ["## Setup\n", "\n", "### Dependencies\n", "\n", "We'll use OpenAI embeddings and an InMemory vector store in this walkthrough, but everything shown here works with any [Embeddings](/docs/concepts/embedding_models), and [VectorStore](/docs/concepts/vectorstores) or [Retriever](/docs/concepts/retrievers). \n", "\n", "We'll use the following packages:"]}, {"cell_type": "code", "execution_count": 1, "id": "ede7fdc0-ef31-483d-bd67-32e4b5c5d527", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --upgrade --quiet langgraph langchain-community beautifulsoup4"]}, {"cell_type": "markdown", "id": "1665e740-ce01-4f09-b9ed-516db0bd326f", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON>\n", "\n", "Many of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls. As these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent. The best way to do this is with [LangSmith](https://smith.langchain.com).\n", "\n", "Note that Lang<PERSON>mith is not needed, but it is helpful. If you do want to use LangSmith, after you sign up at the link above, make sure to set your environment variables to start logging traces:\n", "\n", "```python\n", "os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "if not os.environ.get(\"LANGSMITH_API_KEY\"):\n", "    os.environ[\"LANGSMITH_API_KEY\"] = getpass.getpass()\n", "```\n", "\n", "### Components\n", "\n", "We will need to select three components from Lang<PERSON>hai<PERSON>'s suite of integrations.\n", "\n", "A [chat model](/docs/integrations/chat/):\n", "\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />"]}, {"cell_type": "code", "execution_count": 2, "id": "c5bba77e-6774-4005-8442-468c9f8d23f0", "metadata": {}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")"]}, {"cell_type": "markdown", "id": "36d59333-dabf-46fd-bc52-75990f0767c9", "metadata": {}, "source": ["An [embedding model](/docs/integrations/text_embedding/):\n", "\n", "import EmbeddingTabs from \"@theme/EmbeddingTabs\";\n", "\n", "<EmbeddingTabs/>"]}, {"cell_type": "code", "execution_count": 3, "id": "b74d449a-fdda-45e8-8f7d-c40a5a4e2d4f", "metadata": {}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "embeddings = OpenAIEmbeddings()"]}, {"cell_type": "markdown", "id": "be6adeca-b883-4a3e-9488-49fac5c727b0", "metadata": {}, "source": ["And a [vector store](/docs/integrations/vectorstores/):\n", "\n", "import VectorStoreTabs from \"@theme/VectorStoreTabs\";\n", "\n", "<VectorStoreTabs/>"]}, {"cell_type": "code", "execution_count": 4, "id": "be98ef2a-de81-4b7f-8e20-a7bc2d7ee399", "metadata": {}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "from langchain_core.vectorstores import InMemoryVectorStore\n", "\n", "vector_store = InMemoryVectorStore(embeddings)"]}, {"cell_type": "markdown", "id": "fa6ba684-26cf-4860-904e-a4d51380c134", "metadata": {}, "source": ["## Chains {#chains}\n", "\n", "The [RAG Tutorial](/docs/tutorials/rag) indexes an [LLM Powered Autonomous Agents](https://lilianweng.github.io/posts/2023-06-23-agent/) blog post by <PERSON><PERSON>. We will repeat that here. Below we load the content of the page, split it into sub-documents, and embed the documents into our [vector store](/docs/concepts/vectorstores/):"]}, {"cell_type": "code", "execution_count": 6, "id": "820244ae-74b4-4593-b392-822979dd91b8", "metadata": {}, "outputs": [], "source": ["import bs4\n", "from langchain import hub\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_core.documents import Document\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from typing_extensions import List, TypedDict\n", "\n", "# Load and chunk contents of the blog\n", "loader = WebBaseLoader(\n", "    web_paths=(\"https://lilianweng.github.io/posts/2023-06-23-agent/\",),\n", "    bs_kwargs=dict(\n", "        parse_only=bs4.SoupStrainer(\n", "            class_=(\"post-content\", \"post-title\", \"post-header\")\n", "        )\n", "    ),\n", ")\n", "docs = loader.load()\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "all_splits = text_splitter.split_documents(docs)"]}, {"cell_type": "code", "execution_count": 7, "id": "73920571-8c26-4988-adf1-b0f695fee50a", "metadata": {}, "outputs": [], "source": ["# Index chunks\n", "_ = vector_store.add_documents(documents=all_splits)"]}, {"cell_type": "markdown", "id": "ffd8ccaf-9d43-464b-8b4e-2ba64f8222fd", "metadata": {}, "source": ["As detailed in [Part 2](/docs/tutorials/qa_chat_history) of the RAG tutorial, we can naturally support a conversational experience by representing the flow of the RAG application as a sequence of [messages](/docs/concepts/messages/):\n", "\n", "1. User input as a `HumanMessage`;\n", "2. Vector store query as an `AIMessage` with tool calls;\n", "3. Retrieved documents as a `ToolMessage`;\n", "4. Final response as a `AIMessage`.\n", "\n", "We will use [tool-calling](/docs/concepts/tool_calling/) to facilitate this, which additionally allows the query to be generated by the LLM. We can build a [tool](/docs/concepts/tools) to execute the retrieval step:"]}, {"cell_type": "code", "execution_count": 8, "id": "f5b7914b-28f7-400b-a987-621d8fd533f5", "metadata": {}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "\n", "\n", "@tool(response_format=\"content_and_artifact\")\n", "def retrieve(query: str):\n", "    \"\"\"Retrieve information related to a query.\"\"\"\n", "    retrieved_docs = vector_store.similarity_search(query, k=2)\n", "    serialized = \"\\n\\n\".join(\n", "        (f\"Source: {doc.metadata}\\nContent: {doc.page_content}\")\n", "        for doc in retrieved_docs\n", "    )\n", "    return serialized, retrieved_docs"]}, {"cell_type": "markdown", "id": "1346cdde-a1af-43f2-9387-396e931f8ba5", "metadata": {}, "source": ["We can now build our LangGraph application.\n", "\n", "Note that we compile it with a [checkpointer](https://langchain-ai.github.io/langgraph/concepts/persistence/) to support a back-and-forth conversation. LangGraph comes with a simple [in-memory checkpointer](https://langchain-ai.github.io/langgraph/reference/checkpoints/#memorysaver), which we use below. See its documentation for more detail, including how to use different persistence backends (e.g., SQLite or Postgres)."]}, {"cell_type": "code", "execution_count": 9, "id": "95d7aa8d-0624-4512-910f-9ced77bfd070", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import SystemMessage\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import END, MessagesState, StateGraph\n", "from langgraph.prebuilt import ToolNode, tools_condition\n", "\n", "\n", "# Step 1: Generate an AIMessage that may include a tool-call to be sent.\n", "def query_or_respond(state: MessagesState):\n", "    \"\"\"Generate tool call for retrieval or respond.\"\"\"\n", "    llm_with_tools = llm.bind_tools([retrieve])\n", "    response = llm_with_tools.invoke(state[\"messages\"])\n", "    # MessagesState appends messages to state instead of overwriting\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# Step 2: Execute the retrieval.\n", "tools = ToolNode([retrieve])\n", "\n", "\n", "# Step 3: Generate a response using the retrieved content.\n", "def generate(state: MessagesState):\n", "    \"\"\"Generate answer.\"\"\"\n", "    # Get generated ToolMessages\n", "    recent_tool_messages = []\n", "    for message in reversed(state[\"messages\"]):\n", "        if message.type == \"tool\":\n", "            recent_tool_messages.append(message)\n", "        else:\n", "            break\n", "    tool_messages = recent_tool_messages[::-1]\n", "\n", "    # Format into prompt\n", "    docs_content = \"\\n\\n\".join(doc.content for doc in tool_messages)\n", "    system_message_content = (\n", "        \"You are an assistant for question-answering tasks. \"\n", "        \"Use the following pieces of retrieved context to answer \"\n", "        \"the question. If you don't know the answer, say that you \"\n", "        \"don't know. Use three sentences maximum and keep the \"\n", "        \"answer concise.\"\n", "        \"\\n\\n\"\n", "        f\"{docs_content}\"\n", "    )\n", "    conversation_messages = [\n", "        message\n", "        for message in state[\"messages\"]\n", "        if message.type in (\"human\", \"system\")\n", "        or (message.type == \"ai\" and not message.tool_calls)\n", "    ]\n", "    prompt = [SystemMessage(system_message_content)] + conversation_messages\n", "\n", "    # Run\n", "    response = llm.invoke(prompt)\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# Build graph\n", "graph_builder = StateGraph(MessagesState)\n", "\n", "graph_builder.add_node(query_or_respond)\n", "graph_builder.add_node(tools)\n", "graph_builder.add_node(generate)\n", "\n", "graph_builder.set_entry_point(\"query_or_respond\")\n", "graph_builder.add_conditional_edges(\n", "    \"query_or_respond\",\n", "    tools_condition,\n", "    {END: END, \"tools\": \"tools\"},\n", ")\n", "graph_builder.add_edge(\"tools\", \"generate\")\n", "graph_builder.add_edge(\"generate\", END)\n", "\n", "memory = MemorySaver()\n", "graph = graph_builder.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 10, "id": "e5aeea66-9653-49c9-98d8-113e54eb72cb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "973eb33b-f078-497d-bbbf-998963695b43", "metadata": {}, "source": ["Let's test our application.\n", "\n", "Note that it responds appropriately to messages that do not require an additional retrieval step:"]}, {"cell_type": "code", "execution_count": 11, "id": "4d7911cc-c075-46bc-af7b-88a2e2a2551e", "metadata": {}, "outputs": [], "source": ["# Specify an ID for the thread\n", "config = {\"configurable\": {\"thread_id\": \"abc123\"}}"]}, {"cell_type": "code", "execution_count": 12, "id": "e59541dd-405b-4032-847f-73becf6aefd0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Hello\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hello! How can I assist you today?\n"]}], "source": ["input_message = \"Hello\"\n", "\n", "for step in graph.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": input_message}]},\n", "    stream_mode=\"values\",\n", "    config=config,\n", "):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "d28f25f4-1caa-4997-a886-888d55034995", "metadata": {}, "source": ["And when executing a search, we can stream the steps to observe the query generation, retrieval, and answer generation:"]}, {"cell_type": "code", "execution_count": 14, "id": "1fdbb4bb-8a28-47d9-b3db-1cd9a543ebc4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What is Task Decomposition?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  retrieve (call_RntwX5GMt531biEE9MqSbgLV)\n", " Call ID: call_RntwX5GMt531biEE9MqSbgLV\n", "  Args:\n", "    query: Task Decomposition\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: retrieve\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Fig. 1. Overview of a LLM-powered autonomous agent system.\n", "Component One: Planning#\n", "A complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\n", "Task Decomposition#\n", "Chain of thought (<PERSON><PERSON>; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\n", "Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Task Decomposition is the process of breaking down a complicated task into smaller, more manageable steps. It often involves techniques like Chain of Thought (CoT), where the model is prompted to \"think step by step,\" allowing for better handling of complex tasks. This approach enhances model performance and provides insight into the model's reasoning process.\n"]}], "source": ["input_message = \"What is Task Decomposition?\"\n", "\n", "for step in graph.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": input_message}]},\n", "    stream_mode=\"values\",\n", "    config=config,\n", "):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "06e285cc-7a03-4038-aad0-558efc768bdd", "metadata": {}, "source": ["Finally, because we have compiled our application with a [checkpointer](https://langchain-ai.github.io/langgraph/concepts/persistence/), historical messages are maintained in the state. This allows the model to contextualize user queries:"]}, {"cell_type": "code", "execution_count": 15, "id": "f48c5f7b-5281-4a03-84ed-d2e0cbd228a2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Can you look up some common ways of doing it?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  retrieve (call_kwO5rYPyJ0MftYKoKRFjKpZM)\n", " Call ID: call_kwO5rYPyJ0MftYKoKRFjKpZM\n", "  Args:\n", "    query: common methods for task decomposition\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: retrieve\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\n", "Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Fig. 1. Overview of a LLM-powered autonomous agent system.\n", "Component One: Planning#\n", "A complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\n", "Task Decomposition#\n", "Chain of thought (<PERSON><PERSON>; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Common ways of Task Decomposition include: (1) using large language models (LLMs) with simple prompts like \"Steps for XYZ\" or \"What are the subgoals for achieving XYZ?\"; (2) utilizing task-specific instructions, such as \"Write a story outline\" for creative tasks; and (3) incorporating human inputs to guide the decomposition process.\n"]}], "source": ["input_message = \"Can you look up some common ways of doing it?\"\n", "\n", "for step in graph.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": input_message}]},\n", "    stream_mode=\"values\",\n", "    config=config,\n", "):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "8e8f8d1e-0255-4b14-81e5-8f5c015194a2", "metadata": {}, "source": ["Note that we can observe the full sequence of messages sent to the chat model-- including tool calls and retrieved context-- in the [Lang<PERSON>mith trace](https://smith.langchain.com/public/3c85919e-9609-4a0d-8df1-21726f8f3e5c/r).\n", "\n", "The conversation history can also be inspected via the state of the application:"]}, {"cell_type": "code", "execution_count": 18, "id": "7686b874-3a85-499f-82b5-28a85c4c768c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Hello\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hello! How can I assist you today?\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What is Task Decomposition?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  retrieve (call_RntwX5GMt531biEE9MqSbgLV)\n", " Call ID: call_RntwX5GMt531biEE9MqSbgLV\n", "  Args:\n", "    query: Task Decomposition\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: retrieve\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Fig. 1. Overview of a LLM-powered autonomous agent system.\n", "Component One: Planning#\n", "A complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\n", "Task Decomposition#\n", "Chain of thought (<PERSON><PERSON>; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\n", "Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Task Decomposition is the process of breaking down a complicated task into smaller, more manageable steps. It often involves techniques like Chain of Thought (CoT), where the model is prompted to \"think step by step,\" allowing for better handling of complex tasks. This approach enhances model performance and provides insight into the model's reasoning process.\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Can you look up some common ways of doing it?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  retrieve (call_kwO5rYPyJ0MftYKoKRFjKpZM)\n", " Call ID: call_kwO5rYPyJ0MftYKoKRFjKpZM\n", "  Args:\n", "    query: common methods for task decomposition\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: retrieve\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\n", "Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Fig. 1. Overview of a LLM-powered autonomous agent system.\n", "Component One: Planning#\n", "A complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\n", "Task Decomposition#\n", "Chain of thought (<PERSON><PERSON>; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Common ways of Task Decomposition include: (1) using large language models (LLMs) with simple prompts like \"Steps for XYZ\" or \"What are the subgoals for achieving XYZ?\"; (2) utilizing task-specific instructions, such as \"Write a story outline\" for creative tasks; and (3) incorporating human inputs to guide the decomposition process.\n"]}], "source": ["chat_history = graph.get_state(config).values[\"messages\"]\n", "for message in chat_history:\n", "    message.pretty_print()"]}, {"cell_type": "markdown", "id": "861da8ed-d890-4fdc-a3bf-30433db61e0d", "metadata": {}, "source": ["## Agents {#agents}\n", "\n", "[Agents](/docs/concepts/agents) leverage the reasoning capabilities of LLMs to make decisions during execution. Using agents allows you to offload additional discretion over the retrieval process. Although their behavior is less predictable than the above \"chain\", they are able to execute multiple retrieval steps in service of a query, or iterate on a single search.\n", "\n", "Below we assemble a minimal RAG agent. Using LangGraph's [pre-built ReAct agent constructor](https://langchain-ai.github.io/langgraph/how-tos/#langgraph.prebuilt.chat_agent_executor.create_react_agent), we can do this in one line.\n", "\n", ":::tip\n", "\n", "Check out LangGraph's [Agentic RAG](https://langchain-ai.github.io/langgraph/tutorials/rag/langgraph_agentic_rag/) tutorial for more advanced formulations.\n", "\n", ":::"]}, {"cell_type": "code", "execution_count": 19, "id": "809cc747-2135-40a2-8e73-e4556343ee64", "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "agent_executor = create_react_agent(llm, [retrieve], checkpointer=memory)"]}, {"cell_type": "markdown", "id": "e4a842f4-d295-4d94-8469-4412c89965d9", "metadata": {}, "source": ["Let's inspect the graph:"]}, {"cell_type": "code", "execution_count": 20, "id": "032e2b45-a78b-4778-afb5-f2f2ef296ad4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Image(agent_executor.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "5423e086-35e2-41ef-a313-6aba053bc02b", "metadata": {}, "source": ["The key difference from our earlier implementation is that instead of a final generation step that ends the run, here the tool invocation loops back to the original LLM call. The model can then either answer the question using the retrieved context, or generate another tool call to obtain more information.\n", "\n", "Let's test this out. We construct a question that would typically require an iterative sequence of retrieval steps to answer:"]}, {"cell_type": "code", "execution_count": 21, "id": "6ccf0204-6e64-413f-a110-e5dcb8a1c329", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What is the standard method for Task Decomposition?\n", "\n", "Once you get the answer, look up common extensions of that method.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  retrieve (call_rxBqio7dxthnMuzjr4AIquSZ)\n", " Call ID: call_rxBqio7dxthnMuzjr4AIquSZ\n", "  Args:\n", "    query: standard method for Task Decomposition\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: retrieve\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\n", "Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Fig. 1. Overview of a LLM-powered autonomous agent system.\n", "Component One: Planning#\n", "A complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\n", "Task Decomposition#\n", "Chain of thought (<PERSON><PERSON>; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  retrieve (call_kmQMRWCKeBdtXdlJi8yZD9CO)\n", " Call ID: call_kmQMRWCKeBdtXdlJi8yZD9CO\n", "  Args:\n", "    query: common extensions of Task Decomposition methods\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: retrieve\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\n", "Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.\n", "\n", "Source: {'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/'}\n", "Content: Fig. 1. Overview of a LLM-powered autonomous agent system.\n", "Component One: Planning#\n", "A complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\n", "Task Decomposition#\n", "Chain of thought (<PERSON><PERSON>; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The standard method for Task Decomposition involves breaking down complex tasks into smaller, manageable steps. Here are the main techniques:\n", "\n", "1. **Chain of Thought (CoT)**: This prompting technique encourages a model to \"think step by step,\" allowing it to utilize more computational resources during testing to decompose challenging tasks into simpler parts. CoT not only simplifies tasks but also provides insights into the model's reasoning process.\n", "\n", "2. **Simple Prompting**: This can involve straightforward queries like \"Steps for XYZ\" or \"What are the subgoals for achieving XYZ?\" to guide the model in identifying the necessary steps.\n", "\n", "3. **Task-specific Instructions**: Using specific prompts tailored to the task at hand, such as \"Write a story outline\" for creative writing, allows for more directed decomposition.\n", "\n", "4. **Human Inputs**: Involving human expertise can also aid in breaking down tasks effectively.\n", "\n", "### Common Extensions of Task Decomposition Methods\n", "\n", "1. **Tree of Thoughts**: This method extends CoT by exploring multiple reasoning possibilities at each step. It decomposes the problem into various thought steps and generates multiple thoughts per step, forming a tree structure. This can utilize search processes like breadth-first search (BFS) or depth-first search (DFS) to evaluate states through classifiers or majority voting.\n", "\n", "These extensions build on the basic principles of task decomposition, enhancing the depth and breadth of reasoning applied to complex tasks.\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"def234\"}}\n", "\n", "input_message = (\n", "    \"What is the standard method for Task Decomposition?\\n\\n\"\n", "    \"Once you get the answer, look up common extensions of that method.\"\n", ")\n", "\n", "for event in agent_executor.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": input_message}]},\n", "    stream_mode=\"values\",\n", "    config=config,\n", "):\n", "    event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "4e3452e7-89ce-4f18-8def-e0983b823347", "metadata": {}, "source": ["Note that the agent:\n", "\n", "1. Generates a query to search for a standard method for task decomposition;\n", "2. Receiving the answer, generates a second query to search for common extensions of it;\n", "3. Having received all necessary context, answers the question.\n", "\n", "We can see the full sequence of steps, along with latency and other metadata, in the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/48cbd35e-9ac1-49ab-8c09-500d54c06b81/r)."]}, {"cell_type": "markdown", "id": "cd6bf4f4-74f4-419d-9e26-f0ed83cf05fa", "metadata": {}, "source": ["## Next steps\n", "\n", "We've covered the steps to build a basic conversational Q&A application:\n", "\n", "- We used chains to build a predictable application that generates search queries for each user input;\n", "- We used agents to build an application that \"decides\" when and how to generate search queries.\n", "\n", "To explore different types of retrievers and retrieval strategies, visit the [retrievers](/docs/how_to#retrievers) section of the how-to guides.\n", "\n", "For a detailed walkthrough of <PERSON><PERSON><PERSON><PERSON>'s conversation memory abstractions, visit the [How to add message history (memory)](/docs/how_to/message_history) LCEL page.\n", "\n", "To learn more about agents, head to the [Agents Modules](/docs/tutorials/agents)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}