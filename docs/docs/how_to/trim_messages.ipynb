{"cells": [{"cell_type": "markdown", "id": "eaad9a82-0592-4315-9931-0621054bdd0e", "metadata": {}, "source": ["# How to trim messages\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Messages](/docs/concepts/messages)\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [Chaining](/docs/how_to/sequence/)\n", "- [Chat history](/docs/concepts/chat_history)\n", "\n", "The methods in this guide also require `langchain-core>=0.2.9`.\n", "\n", ":::\n", "\n", "All models have finite context windows, meaning there's a limit to how many [tokens](/docs/concepts/tokens/) they can take as input. If you have very long messages or a chain/agent that accumulates a long message history, you'll need to manage the length of the messages you're passing in to the model.\n", "\n", "[trim_messages](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.trim_messages.html) can be used to reduce the size of a chat history to a specified token count or specified message count.\n", "\n", "\n", "If passing the trimmed chat history back into a chat model directly, the trimmed chat history should satisfy the following properties:\n", "\n", "1. The resulting chat history should be **valid**. Usually this means that the following properties should be satisfied:\n", "   - The chat history **starts** with either (1) a `HumanMessage` or (2) a [SystemMessage](/docs/concepts/messages/#systemmessage) followed by a `HumanMessage`.\n", "   - The chat history **ends** with either a `HumanMessage` or a `ToolMessage`.\n", "   - A `ToolMessage` can only appear after an `AIMessage` that involved a tool call.\n", "\n", "   This can be achieved by setting `start_on=\"human\"` and `ends_on=(\"human\", \"tool\")`.\n", "3. It includes recent messages and drops old messages in the chat history.\n", "   This can be achieved by setting `strategy=\"last\"`.\n", "4. Usually, the new chat history should include the `SystemMessage` if it\n", "   was present in the original chat history since the `SystemMessage` includes\n", "   special instructions to the chat model. The `SystemMessage` is almost always\n", "   the first message in the history if present. This can be achieved by setting\n", "   `include_system=True`."]}, {"cell_type": "markdown", "id": "e4bffc37-78c0-46c3-ad0c-b44de0ed3e90", "metadata": {}, "source": ["## Trimming based on token count\n", "\n", "Here, we'll trim the chat history based on token count. The trimmed chat history will produce a **valid** chat history that includes the `SystemMessage`.\n", "\n", "To keep the most recent messages, we set `strategy=\"last\"`.  We'll also set `include_system=True` to include the `SystemMessage`, and `start_on=\"human\"` to make sure the resulting chat history is valid. \n", "\n", "This is a good default configuration when using `trim_messages` based on token count. Remember to adjust `token_counter` and `max_tokens` for your use case. Keep in mind that new queries added to the chat history will be included in the token count unless you trim prior to adding the new query.\n", "\n", "Notice that for our `token_counter` we can pass in a function (more on that below) or a language model (since language models have a message token counting method). It makes sense to pass in a model when you're trimming your messages to fit into the context window of that specific model:"]}, {"cell_type": "code", "execution_count": null, "id": "c9bed5ea-8aee-4d43-a717-77a431a02d2e", "metadata": {}, "outputs": [], "source": ["pip install -qU langchain-openai"]}, {"cell_type": "code", "execution_count": 2, "id": "40ea972c-d424-4bc4-9f2e-82f01c3d7598", "metadata": {}, "outputs": [{"data": {"text/plain": ["[SystemMessage(content=\"you're a good assistant, you always respond with a joke.\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='what do you call a speechless parrot', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import (\n", "    AIMessage,\n", "    HumanMessage,\n", "    SystemMessage,\n", "    ToolMessage,\n", "    trim_messages,\n", ")\n", "from langchain_core.messages.utils import count_tokens_approximately\n", "\n", "messages = [\n", "    SystemMessage(\"you're a good assistant, you always respond with a joke.\"),\n", "    HumanMessage(\"i wonder why it's called langchain\"),\n", "    AIMessage(\n", "        'Well, I guess they thought \"WordRope\" and \"SentenceString\" just didn\\'t have the same ring to it!'\n", "    ),\n", "    HumanMessage(\"and who is harrison chasing anyways\"),\n", "    AIMessage(\n", "        \"Hmmm let me think.\\n\\nWhy, he's probably chasing after the last cup of coffee in the office!\"\n", "    ),\n", "    HumanMessage(\"what do you call a speechless parrot\"),\n", "]\n", "\n", "\n", "trim_messages(\n", "    messages,\n", "    # Keep the last <= n_count tokens of the messages.\n", "    strategy=\"last\",\n", "    # highlight-start\n", "    # Remember to adjust based on your model\n", "    # or else pass a custom token_counter\n", "    token_counter=count_tokens_approximately,\n", "    # highlight-end\n", "    # Most chat models expect that chat history starts with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a SystemMessage followed by a HumanMessage\n", "    # highlight-start\n", "    # Remember to adjust based on the desired conversation\n", "    # length\n", "    max_tokens=45,\n", "    # highlight-end\n", "    # Most chat models expect that chat history starts with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a SystemMessage followed by a HumanMessage\n", "    start_on=\"human\",\n", "    # Most chat models expect that chat history ends with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a ToolMessage\n", "    end_on=(\"human\", \"tool\"),\n", "    # Usually, we want to keep the SystemMessage\n", "    # if it's present in the original history.\n", "    # The SystemMessage has special instructions for the model.\n", "    include_system=True,\n", "    allow_partial=False,\n", ")"]}, {"cell_type": "markdown", "id": "28fcfc94-0d4a-415c-9506-8ae7634253a2", "metadata": {}, "source": ["## Trimming based on message count\n", "\n", "Alternatively, we can trim the chat history based on **message count**, by setting `token_counter=len`. In this case, each message will count as a single token, and `max_tokens` will control\n", "the maximum number of messages.\n", "\n", "This is a good default configuration when using `trim_messages` based on message count. Remember to adjust `max_tokens` for your use case."]}, {"cell_type": "code", "execution_count": 3, "id": "c8fdedae-0e6b-4901-a222-81fc95e265c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["[SystemMessage(content=\"you're a good assistant, you always respond with a joke.\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='and who is harrison chasing anyways', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content=\"Hmmm let me think.\\n\\nWhy, he's probably chasing after the last cup of coffee in the office!\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='what do you call a speechless parrot', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["trim_messages(\n", "    messages,\n", "    # Keep the last <= n_count tokens of the messages.\n", "    strategy=\"last\",\n", "    # highlight-next-line\n", "    token_counter=len,\n", "    # When token_counter=len, each message\n", "    # will be counted as a single token.\n", "    # highlight-start\n", "    # Remember to adjust for your use case\n", "    max_tokens=5,\n", "    # highlight-end\n", "    # Most chat models expect that chat history starts with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a SystemMessage followed by a HumanMessage\n", "    start_on=\"human\",\n", "    # Most chat models expect that chat history ends with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a ToolMessage\n", "    end_on=(\"human\", \"tool\"),\n", "    # Usually, we want to keep the SystemMessage\n", "    # if it's present in the original history.\n", "    # The SystemMessage has special instructions for the model.\n", "    include_system=True,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "id": "9367857f-7f9a-4d17-9f9c-6ffc5aae909c", "metadata": {}, "source": ["## Advanced Usage\n", "\n", "You can use `trim_messages` as a building-block to create more complex processing logic.\n", "\n", "If we want to allow splitting up the contents of a message we can specify `allow_partial=True`:"]}, {"cell_type": "code", "execution_count": 4, "id": "0265eba7-c8f3-4495-bcbb-17cd7ede3ece", "metadata": {}, "outputs": [{"data": {"text/plain": ["[SystemMessage(content=\"you're a good assistant, you always respond with a joke.\", additional_kwargs={}, response_metadata={}),\n", " AIMessage(content=\"\\nWhy, he's probably chasing after the last cup of coffee in the office!\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='what do you call a speechless parrot', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["trim_messages(\n", "    messages,\n", "    max_tokens=56,\n", "    strategy=\"last\",\n", "    token_counter=count_tokens_approximately,\n", "    include_system=True,\n", "    allow_partial=True,\n", ")"]}, {"cell_type": "markdown", "id": "245bee9b-e515-4e89-8f2a-84bda9a25de8", "metadata": {}, "source": ["By default, the `SystemMessage` will not be included, so you can drop it by either setting `include_system=False` or by dropping the `include_system` argument."]}, {"cell_type": "code", "execution_count": 5, "id": "94351736-28a1-44a3-aac7-82356c81d171", "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content=\"Hmmm let me think.\\n\\nWhy, he's probably chasing after the last cup of coffee in the office!\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='what do you call a speechless parrot', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["trim_messages(\n", "    messages,\n", "    max_tokens=45,\n", "    strategy=\"last\",\n", "    token_counter=count_tokens_approximately,\n", ")"]}, {"cell_type": "markdown", "id": "7f5d391d-235b-4091-b2de-c22866b478f3", "metadata": {}, "source": ["We can perform the flipped operation of getting the *first* `max_tokens` by specifying `strategy=\"first\"`:"]}, {"cell_type": "code", "execution_count": 6, "id": "5f56ae54-1a39-4019-9351-3b494c003d5b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[SystemMessage(content=\"you're a good assistant, you always respond with a joke.\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content=\"i wonder why it's called langchain\", additional_kwargs={}, response_metadata={})]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["trim_messages(\n", "    messages,\n", "    max_tokens=45,\n", "    strategy=\"first\",\n", "    token_counter=count_tokens_approximately,\n", ")"]}, {"cell_type": "markdown", "id": "0625c094-380f-4485-b2d2-e5dfa83fe299", "metadata": {}, "source": ["## Using `ChatModel` as a token counter\n", "\n", "You can pass a ChatModel as a token-counter. This will use `ChatModel.get_num_tokens_from_messages`. Let's demonstrate how to use it with OpenAI:"]}, {"cell_type": "code", "execution_count": 7, "id": "9ef35359-1b7a-4918-ab41-30bec69fb3dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["[SystemMessage(content=\"you're a good assistant, you always respond with a joke.\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content=\"i wonder why it's called langchain\", additional_kwargs={}, response_metadata={})]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "trim_messages(\n", "    messages,\n", "    max_tokens=45,\n", "    strategy=\"first\",\n", "    token_counter=ChatOpenAI(model=\"gpt-4o\"),\n", ")"]}, {"cell_type": "markdown", "id": "ab70bf70-1e5a-4d51-b9b8-a823bf2cf532", "metadata": {}, "source": ["## Writing a custom token counter\n", "\n", "We can write a custom token counter function that takes in a list of messages and returns an int."]}, {"cell_type": "code", "execution_count": 8, "id": "d930c089-e8e6-4980-9d39-11d41e794772", "metadata": {}, "outputs": [], "source": ["pip install -qU tiktoken"]}, {"cell_type": "code", "execution_count": 9, "id": "1c1c3b1e-2ece-49e7-a3b6-e69877c1633b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[SystemMessage(content=\"you're a good assistant, you always respond with a joke.\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='what do you call a speechless parrot', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import List\n", "\n", "import tiktoken\n", "from langchain_core.messages import BaseMessage, ToolMessage\n", "\n", "\n", "def str_token_counter(text: str) -> int:\n", "    enc = tiktoken.get_encoding(\"o200k_base\")\n", "    return len(enc.encode(text))\n", "\n", "\n", "def tiktoken_counter(messages: List[BaseMessage]) -> int:\n", "    \"\"\"Approximately reproduce https://github.com/openai/openai-cookbook/blob/main/examples/How_to_count_tokens_with_tiktoken.ipynb\n", "\n", "    For simplicity only supports str Message.contents.\n", "    \"\"\"\n", "    num_tokens = 3  # every reply is primed with <|start|>assistant<|message|>\n", "    tokens_per_message = 3\n", "    tokens_per_name = 1\n", "    for msg in messages:\n", "        if isinstance(msg, HumanMessage):\n", "            role = \"user\"\n", "        elif isinstance(msg, AIMessage):\n", "            role = \"assistant\"\n", "        elif isinstance(msg, ToolMessage):\n", "            role = \"tool\"\n", "        elif isinstance(msg, SystemMessage):\n", "            role = \"system\"\n", "        else:\n", "            raise ValueError(f\"Unsupported messages type {msg.__class__}\")\n", "        num_tokens += (\n", "            tokens_per_message\n", "            + str_token_counter(role)\n", "            + str_token_counter(msg.content)\n", "        )\n", "        if msg.name:\n", "            num_tokens += tokens_per_name + str_token_counter(msg.name)\n", "    return num_tokens\n", "\n", "\n", "trim_messages(\n", "    messages,\n", "    # highlight-next-line\n", "    token_counter=tiktoken_counter,\n", "    # Keep the last <= n_count tokens of the messages.\n", "    strategy=\"last\",\n", "    # When token_counter=len, each message\n", "    # will be counted as a single token.\n", "    # highlight-start\n", "    # Remember to adjust for your use case\n", "    max_tokens=45,\n", "    # highlight-end\n", "    # Most chat models expect that chat history starts with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a SystemMessage followed by a HumanMessage\n", "    start_on=\"human\",\n", "    # Most chat models expect that chat history ends with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a ToolMessage\n", "    end_on=(\"human\", \"tool\"),\n", "    # Usually, we want to keep the SystemMessage\n", "    # if it's present in the original history.\n", "    # The SystemMessage has special instructions for the model.\n", "    include_system=True,\n", ")"]}, {"cell_type": "markdown", "id": "4b2a672b-c007-47c5-9105-617944dc0a6a", "metadata": {}, "source": ["## Chaining\n", "\n", "`trim_messages` can be used imperatively (like above) or declaratively, making it easy to compose with other components in a chain"]}, {"cell_type": "code", "execution_count": 10, "id": "96aa29b2-01e0-437c-a1ab-02fb0141cb57", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='A \"polly-no-wanna-cracker\"!', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 11, 'prompt_tokens': 32, 'total_tokens': 43, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_90d33c15d4', 'finish_reason': 'stop', 'logprobs': None}, id='run-b1f8b63b-6bc2-4df4-b3b9-dfc4e3e675fe-0', usage_metadata={'input_tokens': 32, 'output_tokens': 11, 'total_tokens': 43, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["llm = ChatOpenAI(model=\"gpt-4o\")\n", "\n", "# Notice we don't pass in messages. This creates\n", "# a RunnableLambda that takes messages as input\n", "trimmer = trim_messages(\n", "    token_counter=llm,\n", "    # Keep the last <= n_count tokens of the messages.\n", "    strategy=\"last\",\n", "    # When token_counter=len, each message\n", "    # will be counted as a single token.\n", "    # Remember to adjust for your use case\n", "    max_tokens=45,\n", "    # Most chat models expect that chat history starts with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a SystemMessage followed by a HumanMessage\n", "    start_on=\"human\",\n", "    # Most chat models expect that chat history ends with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a ToolMessage\n", "    end_on=(\"human\", \"tool\"),\n", "    # Usually, we want to keep the SystemMessage\n", "    # if it's present in the original history.\n", "    # The SystemMessage has special instructions for the model.\n", "    include_system=True,\n", ")\n", "\n", "chain = trimmer | llm\n", "chain.invoke(messages)"]}, {"cell_type": "markdown", "id": "4d91d390-e7f7-467b-ad87-d100411d7a21", "metadata": {}, "source": ["Looking at [the <PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/65af12c4-c24d-4824-90f0-6547566e59bb/r) we can see that before the messages are passed to the model they are first trimmed.\n", "\n", "Looking at just the trimmer, we can see that it's a Runnable object that can be invoked like all Runnables:"]}, {"cell_type": "code", "execution_count": 11, "id": "1ff02d0a-353d-4fac-a77c-7c2c5262abd9", "metadata": {}, "outputs": [{"data": {"text/plain": ["[SystemMessage(content=\"you're a good assistant, you always respond with a joke.\", additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='what do you call a speechless parrot', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["trimmer.invoke(messages)"]}, {"cell_type": "markdown", "id": "dc4720c8-4062-4ebc-9385-58411202ce6e", "metadata": {}, "source": ["## Using with ChatMessageHistory\n", "\n", "Trimming messages is especially useful when [working with chat histories](/docs/how_to/message_history/), which can get arbitrarily long:"]}, {"cell_type": "code", "execution_count": 11, "id": "a9517858-fc2f-4dc3-898d-bf98a0e905a0", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='A \"polygon\"!', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 4, 'prompt_tokens': 32, 'total_tokens': 36, 'completion_tokens_details': {'reasoning_tokens': 0}}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_c17d3befe7', 'finish_reason': 'stop', 'logprobs': None}, id='run-71d9fce6-bb0c-4bb3-acc8-d5eaee6ae7bc-0', usage_metadata={'input_tokens': 32, 'output_tokens': 4, 'total_tokens': 36})"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.chat_history import InMemoryChatMessageHistory\n", "from langchain_core.runnables.history import RunnableWithMessageHistory\n", "\n", "chat_history = InMemoryChatMessageHistory(messages=messages[:-1])\n", "\n", "\n", "def dummy_get_session_history(session_id):\n", "    if session_id != \"1\":\n", "        return InMemoryChatMessageHistory()\n", "    return chat_history\n", "\n", "\n", "trimmer = trim_messages(\n", "    max_tokens=45,\n", "    strategy=\"last\",\n", "    token_counter=llm,\n", "    # Usually, we want to keep the SystemMessage\n", "    # if it's present in the original history.\n", "    # The SystemMessage has special instructions for the model.\n", "    include_system=True,\n", "    # Most chat models expect that chat history starts with either:\n", "    # (1) a HumanMessage or\n", "    # (2) a SystemMessage followed by a HumanMessage\n", "    # start_on=\"human\" makes sure we produce a valid chat history\n", "    start_on=\"human\",\n", ")\n", "\n", "chain = trimmer | llm\n", "chain_with_history = RunnableWithMessageHistory(chain, dummy_get_session_history)\n", "chain_with_history.invoke(\n", "    [HumanMessage(\"what do you call a speechless parrot\")],\n", "    config={\"configurable\": {\"session_id\": \"1\"}},\n", ")"]}, {"cell_type": "markdown", "id": "556b7b4c-43cb-41de-94fc-1a41f4ec4d2e", "metadata": {}, "source": ["Looking at [the <PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/17dd700b-9994-44ca-930c-116e00997315/r) we can see that we retrieve all of our messages but before the messages are passed to the model they are trimmed to be just the system message and last human message."]}, {"cell_type": "markdown", "id": "75dc7b84-b92f-44e7-8beb-ba22398e4efb", "metadata": {}, "source": ["## API reference\n", "\n", "For a complete description of all arguments head to the [API reference](https://python.langchain.com/api_reference/core/messages/langchain_core.messages.utils.trim_messages.html)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}