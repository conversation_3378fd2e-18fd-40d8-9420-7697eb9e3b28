{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "keywords: [Run<PERSON>ble, Runnables, RunnableSequence, LCEL, chain, chains, chaining]\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to chain runnables\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [LangChain Expression Language (LCEL)](/docs/concepts/lcel)\n", "- [Prompt templates](/docs/concepts/prompt_templates)\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [Output parser](/docs/concepts/output_parsers)\n", "\n", ":::\n", "\n", "One point about [LangChain Expression Language](/docs/concepts/lcel) is that any two runnables can be \"chained\" together into sequences. The output of the previous runnable's `.invoke()` call is passed as input to the next runnable. This can be done using the pipe operator (`|`), or the more explicit `.pipe()` method, which does the same thing.\n", "\n", "The resulting [`RunnableSequence`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.RunnableSequence.html) is itself a runnable, which means it can be invoked, streamed, or further chained just like any other runnable. Advantages of chaining runnables in this way are efficient streaming (the sequence will stream output as soon as it is available), and debugging and tracing with tools like [LangSmith](/docs/how_to/debugging).\n", "\n", "## The pipe operator: `|`\n", "\n", "To show off how this works, let's go through an example. We'll walk through a common pattern in LangChain: using a [prompt template](/docs/how_to#prompt-templates) to format input into a [chat model](/docs/how_to#chat-models), and finally converting the chat message output into a string with an [output parser](/docs/how_to#output-parsers).\n", "\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs\n", "  customVarName=\"model\"\n", "/>\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "import os\n", "from getpass import getpass\n", "\n", "from langchain_anthropic import ChatAnthropic\n", "\n", "if \"ANTHROPIC_API_KEY\" not in os.environ:\n", "    os.environ[\"ANTHROPIC_API_KEY\"] = getpass()\n", "\n", "model = ChatAnthropic(model=\"claude-3-7-sonnet-20250219\", temperature=0)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_template(\"tell me a joke about {topic}\")\n", "\n", "chain = prompt | model | StrOutputParser()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Prompts and models are both runnable, and the output type from the prompt call is the same as the input type of the chat model, so we can chain them together. We can then invoke the resulting sequence like any other runnable:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Why don't bears wear shoes?\\n\\nBecause they prefer to go bear-foot!\""]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke({\"topic\": \"bears\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Co<PERSON>cion\n", "\n", "We can even combine this chain with more runnables to create another chain. This may involve some input/output formatting using other types of runnables, depending on the required inputs and outputs of the chain components.\n", "\n", "For example, let's say we wanted to compose the joke generating chain with another chain that evaluates whether or not the generated joke was funny.\n", "\n", "We would need to be careful with how we format the input into the next chain. In the below example, the dict in the chain is automatically parsed and converted into a [`RunnableParallel`](/docs/how_to/parallel), which runs all of its values in parallel and returns a dict with the results.\n", "\n", "This happens to be the same format the next prompt template expects. Here it is in action:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Yes, that\\'s a funny joke! It\\'s a classic pun that plays on the homophone pair \"bare-foot\" and \"bear-foot.\" The humor comes from:\\n\\n1. The wordplay between \"barefoot\" (not wearing shoes) and \"bear-foot\" (the foot of a bear)\\n2. The logical connection to the setup (bears don\\'t wear shoes)\\n3. It\\'s family-friendly and accessible\\n4. It\\'s a simple, clean pun that creates an unexpected but satisfying punchline\\n\\nIt\\'s the kind of joke that might make you groan and smile at the same time - what people often call a \"dad joke.\"'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "\n", "analysis_prompt = ChatPromptTemplate.from_template(\"is this a funny joke? {joke}\")\n", "\n", "composed_chain = {\"joke\": chain} | analysis_prompt | model | StrOutputParser()\n", "\n", "composed_chain.invoke({\"topic\": \"bears\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Functions will also be coerced into runnables, so you can add custom logic to your chains too. The below chain results in the same logical flow as before:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Yes, that\\'s a cute and funny joke! It works well because:\\n\\n1. It plays on the double meaning of \"roots\" - both the literal roots of the beet plant and the metaphorical sense of knowing one\\'s origins or foundation\\n2. It\\'s a simple, clean pun that doesn\\'t rely on offensive content\\n3. It has a satisfying logical connection (beets are root vegetables)\\n\\nIt\\'s the kind of wholesome food pun that might make people groan a little but also smile. Perfect for sharing in casual conversation or with kids!'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["composed_chain_with_lambda = (\n", "    chain\n", "    | (lambda input: {\"joke\": input})\n", "    | analysis_prompt\n", "    | model\n", "    | StrOutputParser()\n", ")\n", "\n", "composed_chain_with_lambda.invoke({\"topic\": \"beets\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["However, keep in mind that using functions like this may interfere with operations like streaming. See [this section](/docs/how_to/functions) for more information."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The `.pipe()` method\n", "\n", "We could also compose the same sequence using the `.pipe()` method. Here's what that looks like:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"This joke is moderately funny! It plays on Battlestar Galactica lore where Cylons are robots with 12 different models trying to infiltrate human society. The humor comes from the idea of a Cylon accidentally revealing their non-human nature through a pickup line that references their artificial origins. It's a decent nerd-culture joke that would land well with fans of the show, though someone unfamiliar with Battlestar Galactica might not get the reference. The punchline effectively highlights the contradiction in a Cylon trying to blend in while simultaneously revealing their true identity.\""]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.runnables import RunnableParallel\n", "\n", "composed_chain_with_pipe = (\n", "    RunnableParallel({\"joke\": chain})\n", "    .pipe(analysis_prompt)\n", "    .pipe(model)\n", "    .pipe(StrOutputParser())\n", ")\n", "\n", "composed_chain_with_pipe.invoke({\"topic\": \"battlestar galactica\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Or the abbreviated:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["composed_chain_with_pipe = RunnableParallel({\"joke\": chain}).pipe(\n", "    analysis_prompt, model, StrOutputParser()\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Related\n", "\n", "- [Streaming](/docs/how_to/streaming/): Check out the streaming guide to understand the streaming behavior of a chain\n"]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 4}