{"cells": [{"cell_type": "raw", "id": "d35de667-0352-4bfb-a890-cebe7f676fe7", "metadata": {}, "source": ["---\n", "sidebar_position: 5\n", "keywords: [RunnablePassthrough, LCEL]\n", "---"]}, {"cell_type": "markdown", "id": "b022ab74-794d-4c54-ad47-ff9549ddb9d2", "metadata": {}, "source": ["# How to pass through arguments from one step to the next\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [LangChain Expression Language (LCEL)](/docs/concepts/lcel)\n", "- [Chaining runnables](/docs/how_to/sequence/)\n", "- [Calling runnables in parallel](/docs/how_to/parallel/)\n", "- [Custom functions](/docs/how_to/functions/)\n", "\n", ":::\n", "\n", "\n", "When composing chains with several steps, sometimes you will want to pass data from previous steps unchanged for use as input to a later step. The [`RunnablePassthrough`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.passthrough.RunnablePassthrough.html) class allows you to do just this, and is typically is used in conjunction with a [RunnableParallel](/docs/how_to/parallel/) to pass data through to a later step in your constructed chains.\n", "\n", "See the example below:"]}, {"cell_type": "code", "execution_count": null, "id": "e169b952", "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain langchain-openai\n", "\n", "import os\n", "from getpass import getpass\n", "\n", "if \"OPENAI_API_KEY\" not in os.environ:\n", "    os.environ[\"OPENAI_API_KEY\"] = getpass()"]}, {"cell_type": "code", "execution_count": 2, "id": "03988b8d-d54c-4492-8707-1594372cf093", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'passed': {'num': 1}, 'modified': 2}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.runnables import RunnableParallel, RunnablePassthrough\n", "\n", "runnable = RunnableParallel(\n", "    passed=RunnablePassthrough(),\n", "    modified=lambda x: x[\"num\"] + 1,\n", ")\n", "\n", "runnable.invoke({\"num\": 1})"]}, {"cell_type": "markdown", "id": "702c7acc-cd31-4037-9489-647df192fd7c", "metadata": {}, "source": ["As seen above, `passed` key was called with `RunnablePassthrough()` and so it simply passed on `{'num': 1}`. \n", "\n", "We also set a second key in the map with `modified`. This uses a lambda to set a single value adding 1 to the num, which resulted in `modified` key with the value of `2`."]}, {"cell_type": "markdown", "id": "15187a3b-d666-4b9b-a258-672fc51fe0e2", "metadata": {}, "source": ["## Retrieval Example\n", "\n", "In the example below, we see a more real-world use case where we use `RunnablePassthrough` along with `RunnableParallel` in a chain to properly format inputs to a prompt:"]}, {"cell_type": "code", "execution_count": 3, "id": "267d1460-53c1-4fdb-b2c3-b6a1eb7fccff", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON> worked at Kensho.'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.vectorstores import FAISS\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_openai import ChatOpenAI, OpenAIEmbeddings\n", "\n", "vectorstore = FAISS.from_texts(\n", "    [\"ha<PERSON><PERSON> worked at kensho\"], embedding=OpenAIEmbeddings()\n", ")\n", "retriever = vectorstore.as_retriever()\n", "template = \"\"\"Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "\"\"\"\n", "prompt = ChatPromptTemplate.from_template(template)\n", "model = ChatOpenAI()\n", "\n", "retrieval_chain = (\n", "    {\"context\": retriever, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | model\n", "    | StrOutputParser()\n", ")\n", "\n", "retrieval_chain.invoke(\"where did harrison work?\")"]}, {"cell_type": "markdown", "id": "392cd4c4-e7ed-4ab8-934d-f7a4eca55ee1", "metadata": {}, "source": ["Here the input to prompt is expected to be a map with keys \"context\" and \"question\". The user input is just the question. So we need to get the context using our retriever and passthrough the user input under the \"question\" key. The `RunnablePassthrough` allows us to pass on the user's question to the prompt and model. \n", "\n", "## Next steps\n", "\n", "Now you've learned how to pass data through your chains to help format the data flowing through your chains.\n", "\n", "To learn more, see the other how-to guides on runnables in this section."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 5}