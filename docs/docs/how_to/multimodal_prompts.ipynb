{"cells": [{"cell_type": "markdown", "id": "4facdf7f-680e-4d28-908b-2b8408e2a741", "metadata": {}, "source": ["# How to use multimodal prompts\n", "\n", "Here we demonstrate how to use prompt templates to format [multimodal](/docs/concepts/multimodality/) inputs to models. \n", "\n", "To use prompt templates in the context of multimodal data, we can templatize elements of the corresponding content block.\n", "For example, below we define a prompt that takes a URL for an image as a parameter:"]}, {"cell_type": "code", "execution_count": 1, "id": "2671f995", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "# Define prompt\n", "prompt = ChatPromptTemplate(\n", "    [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"Describe the image provided.\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"image\",\n", "                    \"source_type\": \"url\",\n", "                    # highlight-next-line\n", "                    \"url\": \"{image_url}\",\n", "                },\n", "            ],\n", "        },\n", "    ]\n", ")"]}, {"cell_type": "markdown", "id": "f75d2e26-5b9a-4d5f-94a7-7f98f5666f6d", "metadata": {}, "source": ["Let's use this prompt to pass an image to a [chat model](/docs/concepts/chat_models/#multimodality):"]}, {"cell_type": "code", "execution_count": 2, "id": "5df2e558-321d-4cf7-994e-2815ac37e704", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["This image shows a beautiful wooden boardwalk cutting through a lush green wetland or marsh area. The boardwalk extends straight ahead toward the horizon, creating a strong leading line through the composition. On either side, tall green grasses sway in what appears to be a summer or late spring setting. The sky is particularly striking, with wispy cirrus clouds streaking across a vibrant blue background. In the distance, you can see a tree line bordering the wetland area. The lighting suggests this may be during \"golden hour\" - either early morning or late afternoon - as there's a warm, gentle quality to the light that's illuminating the scene. The wooden planks of the boardwalk appear well-maintained and provide safe passage through what would otherwise be difficult terrain to traverse. It's the kind of scene you might find in a nature preserve or wildlife refuge designed to give visitors access to observe wetland ecosystems while protecting the natural environment.\n"]}], "source": ["from langchain.chat_models import init_chat_model\n", "\n", "llm = init_chat_model(\"anthropic:claude-3-5-sonnet-latest\")\n", "\n", "url = \"https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg\"\n", "\n", "chain = prompt | llm\n", "response = chain.invoke({\"image_url\": url})\n", "print(response.text())"]}, {"cell_type": "markdown", "id": "f4cfdc50-4a9f-4888-93b4-af697366b0f3", "metadata": {}, "source": ["Note that we can templatize arbitrary elements of the content block:"]}, {"cell_type": "code", "execution_count": 3, "id": "53c88ebb-dd57-40c8-8542-b2c916706653", "metadata": {}, "outputs": [], "source": ["prompt = ChatPromptTemplate(\n", "    [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"Describe the image provided.\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"image\",\n", "                    \"source_type\": \"base64\",\n", "                    \"mime_type\": \"{image_mime_type}\",\n", "                    \"data\": \"{image_data}\",\n", "                    \"cache_control\": {\"type\": \"{cache_type}\"},\n", "                },\n", "            ],\n", "        },\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "25e4829e-0073-49a8-9669-9f43e5778383", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["This image shows a beautiful wooden boardwalk cutting through a lush green marsh or wetland area. The boardwalk extends straight ahead toward the horizon, creating a strong leading line in the composition. The surrounding vegetation consists of tall grass and reeds in vibrant green hues, with some bushes and trees visible in the background. The sky is particularly striking, featuring a bright blue color with wispy white clouds streaked across it. The lighting suggests this photo was taken during the \"golden hour\" - either early morning or late afternoon - giving the scene a warm, peaceful quality. The raised wooden path provides accessible access through what would otherwise be difficult terrain to traverse, allowing visitors to experience and appreciate this natural environment.\n"]}], "source": ["import base64\n", "\n", "import httpx\n", "\n", "image_data = base64.b64encode(httpx.get(url).content).decode(\"utf-8\")\n", "\n", "chain = prompt | llm\n", "response = chain.invoke(\n", "    {\n", "        \"image_data\": image_data,\n", "        \"image_mime_type\": \"image/jpeg\",\n", "        \"cache_type\": \"ephemeral\",\n", "    }\n", ")\n", "print(response.text())"]}, {"cell_type": "code", "execution_count": null, "id": "424defe8-d85c-4e45-a88d-bf6f910d5ebb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}