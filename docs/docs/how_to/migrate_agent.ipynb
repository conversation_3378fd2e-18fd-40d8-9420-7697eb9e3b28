{"cells": [{"cell_type": "raw", "id": "adc7ee09", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "keywords: [create_react_agent, create_react_agent()]\n", "---"]}, {"cell_type": "markdown", "id": "457cdc67-1893-4653-8b0c-b185a5947e74", "metadata": {}, "source": ["# How to migrate from legacy LangChain agents to LangGraph\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [Agents](/docs/concepts/agents)\n", "- [LangGraph](https://langchain-ai.github.io/langgraph/)\n", "- [Tool calling](/docs/how_to/tool_calling/)\n", "\n", ":::\n", "\n", "Here we focus on how to move from legacy LangChain agents to more flexible [LangGraph](https://langchain-ai.github.io/langgraph/) agents.\n", "LangChain agents (the [AgentExecutor](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor) in particular) have multiple configuration parameters.\n", "In this notebook we will show how those parameters map to the LangGraph react agent executor using the [create_react_agent](https://langchain-ai.github.io/langgraph/reference/prebuilt/#langgraph.prebuilt.chat_agent_executor.create_react_agent) prebuilt helper method.\n", "\n", "\n", ":::note\n", "In LangGraph, the graph replaces <PERSON><PERSON><PERSON><PERSON>'s agent executor. It manages the agent's cycles and tracks the scratchpad as messages within its state. The <PERSON><PERSON>hai<PERSON> \"agent\" corresponds to the prompt and LLM you've provided.\n", ":::\n", "\n", "\n", "#### Prerequisites\n", "\n", "This how-to guide uses OpenAI as the LLM. Install the dependencies to run."]}, {"cell_type": "code", "execution_count": 1, "id": "662fac50", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph langchain langchain-openai"]}, {"cell_type": "markdown", "id": "6f8ec38f", "metadata": {}, "source": ["Then, set your OpenAI API key."]}, {"cell_type": "code", "execution_count": 2, "id": "5fca87ef", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if \"OPENAI_API_KEY\" not in os.environ:\n", "    os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"OpenAI API key:\\n\")"]}, {"cell_type": "markdown", "id": "8e50635c-1671-46e6-be65-ce95f8167c2f", "metadata": {}, "source": ["## Basic Usage\n", "\n", "For basic creation and usage of a tool-calling ReAct-style agent, the functionality is the same. First, let's define a model and tool(s), then we'll use those to create an agent."]}, {"cell_type": "code", "execution_count": 1, "id": "1e425fea-2796-4b99-bee6-9a6ffe73f756", "metadata": {}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"gpt-4o\")\n", "\n", "\n", "@tool\n", "def magic_function(input: int) -> int:\n", "    \"\"\"Applies a magic function to an input.\"\"\"\n", "    return input + 2\n", "\n", "\n", "tools = [magic_function]\n", "\n", "\n", "query = \"what is the value of magic_function(3)?\""]}, {"cell_type": "markdown", "id": "af002033-fe51-4d14-b47c-3e9b483c8395", "metadata": {}, "source": ["For the LangChain [AgentExecutor](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor), we define a prompt with a placeholder for the agent's scratchpad. The agent can be invoked as follows:"]}, {"cell_type": "code", "execution_count": 2, "id": "03ea357c-9c36-4464-b2cc-27bd150e1554", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input': 'what is the value of magic_function(3)?',\n", " 'output': 'The value of `magic_function(3)` is 5.'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.agents import AgentExecutor, create_tool_calling_agent\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant\"),\n", "        (\"human\", \"{input}\"),\n", "        # Placeholders fill up a **list** of messages\n", "        (\"placeholder\", \"{agent_scratchpad}\"),\n", "    ]\n", ")\n", "\n", "\n", "agent = create_tool_calling_agent(model, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)\n", "\n", "agent_executor.invoke({\"input\": query})"]}, {"cell_type": "markdown", "id": "94205f3b-fd2b-4fd7-af69-0a3fc313dc88", "metadata": {}, "source": ["LangGraph's [react agent executor](https://langchain-ai.github.io/langgraph/reference/prebuilt/#langgraph.prebuilt.chat_agent_executor.create_react_agent) manages a state that is defined by a list of messages. It will continue to process the list until there are no tool calls in the agent's output. To kick it off, we input a list of messages. The output will contain the entire state of the graph-- in this case, the conversation history.\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "53a3737a-d167-4255-89bf-20ac37f89a3e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input': 'what is the value of magic_function(3)?',\n", " 'output': 'The value of `magic_function(3)` is 5.'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "langgraph_agent_executor = create_react_agent(model, tools)\n", "\n", "\n", "messages = langgraph_agent_executor.invoke({\"messages\": [(\"human\", query)]})\n", "{\n", "    \"input\": query,\n", "    \"output\": messages[\"messages\"][-1].content,\n", "}"]}, {"cell_type": "code", "execution_count": 4, "id": "74ecebe3-512e-409c-a661-bdd5b0a2b782", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input': '<PERSON><PERSON>?',\n", " 'output': 'The result of applying `magic_function` to the input value 3 is 5.'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["message_history = messages[\"messages\"]\n", "\n", "new_query = \"Pardon?\"\n", "\n", "messages = langgraph_agent_executor.invoke(\n", "    {\"messages\": message_history + [(\"human\", new_query)]}\n", ")\n", "{\n", "    \"input\": new_query,\n", "    \"output\": messages[\"messages\"][-1].content,\n", "}"]}, {"cell_type": "markdown", "id": "f4466a4d-e55e-4ece-bee8-2269a0b5677b", "metadata": {}, "source": ["## Prompt Templates\n", "\n", "With legacy LangChain agents you have to pass in a prompt template. You can use this to control the agent.\n", "\n", "With LangGraph [react agent executor](https://langchain-ai.github.io/langgraph/reference/prebuilt/#langgraph.prebuilt.chat_agent_executor.create_react_agent), by default there is no prompt. You can achieve similar control over the agent in a few ways:\n", "\n", "1. Pass in a system message as input\n", "2. Initialize the agent with a system message\n", "3. Initialize the agent with a function to transform messages in the graph state before passing to the model.\n", "4. Initialize the agent with a [Runnable](/docs/concepts/lcel) to transform messages in the graph state before passing to the model. This includes passing prompt templates as well.\n", "\n", "Let's take a look at all of these below. We will pass in custom instructions to get the agent to respond in Spanish.\n", "\n", "First up, using `AgentExecutor`:"]}, {"cell_type": "code", "execution_count": 5, "id": "a9a11ccd-75e2-4c11-844d-a34870b0ff91", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input': 'what is the value of magic_function(3)?',\n", " 'output': 'El valor de magic_function(3) es 5.'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant. Respond only in Spanish.\"),\n", "        (\"human\", \"{input}\"),\n", "        # Placeholders fill up a **list** of messages\n", "        (\"placeholder\", \"{agent_scratchpad}\"),\n", "    ]\n", ")\n", "\n", "\n", "agent = create_tool_calling_agent(model, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)\n", "\n", "agent_executor.invoke({\"input\": query})"]}, {"cell_type": "markdown", "id": "bd5f5500-5ae4-4000-a9fd-8c5a2cc6404d", "metadata": {}, "source": ["Now, let's pass a custom system message to [react agent executor](https://langchain-ai.github.io/langgraph/reference/prebuilt/#langgraph.prebuilt.chat_agent_executor.create_react_agent).\n", "\n", "LangGraph's prebuilt `create_react_agent` does not take a prompt template directly as a parameter, but instead takes a [`prompt`](https://langchain-ai.github.io/langgraph/reference/prebuilt/#langgraph.prebuilt.chat_agent_executor.create_react_agent) parameter. This modifies the graph state before the llm is called, and can be one of four values:\n", "\n", "- A `SystemMessage`, which is added to the beginning of the list of messages.\n", "- A `string`, which is converted to a `SystemMessage` and added to the beginning of the list of messages.\n", "- A `Callable`, which should take in full graph state. The output is then passed to the language model.\n", "- Or a [`Runnable`](/docs/concepts/lcel), which should take in full graph state. The output is then passed to the language model.\n", "\n", "Here's how it looks in action:"]}, {"cell_type": "code", "execution_count": 6, "id": "a9486805-676a-4d19-a5c4-08b41b172989", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import SystemMessage\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "system_message = \"You are a helpful assistant. Respond only in Spanish.\"\n", "# This could also be a SystemMessage object\n", "# system_message = SystemMessage(content=\"You are a helpful assistant. Respond only in Spanish.\")\n", "\n", "langgraph_agent_executor = create_react_agent(model, tools, prompt=system_message)\n", "\n", "\n", "messages = langgraph_agent_executor.invoke({\"messages\": [(\"user\", query)]})"]}, {"cell_type": "markdown", "id": "fc6059fd-0df7-4b6f-a84c-b5874e983638", "metadata": {}, "source": ["We can also pass in an arbitrary function or a runnable. This function/runnable should take in a graph state and output a list of messages.\n", "We can do all types of arbitrary formatting of messages here. In this case, let's add a SystemMessage to the start of the list of messages and append another user message at the end."]}, {"cell_type": "code", "execution_count": 7, "id": "d369ab45-0c82-45f4-9d3e-8efb8dd47e2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'input': 'what is the value of magic_function(3)?', 'output': 'El valor de magic_function(3) es 5. ¡Pandamonium!'}\n"]}], "source": ["from langchain_core.messages import HumanMessage, SystemMessage\n", "from langgraph.prebuilt import create_react_agent\n", "from langgraph.prebuilt.chat_agent_executor import AgentState\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant. Respond only in Spanish.\"),\n", "        (\"placeholder\", \"{messages}\"),\n", "        (\"user\", \"Also say 'Pandamonium!' after the answer.\"),\n", "    ]\n", ")\n", "\n", "# alternatively, this can be passed as a function, e.g.\n", "# def prompt(state: AgentState):\n", "#     return (\n", "#         [SystemMessage(content=\"You are a helpful assistant. Respond only in Spanish.\")] +\n", "#         state[\"messages\"] +\n", "#         [HumanMessage(content=\"Also say 'Pandamonium!' after the answer.\")]\n", "#     )\n", "\n", "\n", "langgraph_agent_executor = create_react_agent(model, tools, prompt=prompt)\n", "\n", "\n", "messages = langgraph_agent_executor.invoke({\"messages\": [(\"human\", query)]})\n", "print(\n", "    {\n", "        \"input\": query,\n", "        \"output\": messages[\"messages\"][-1].content,\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "68df3a09", "metadata": {}, "source": ["## Memory"]}, {"cell_type": "markdown", "id": "96e7ffc8", "metadata": {}, "source": ["### In LangChain\n", "\n", "With <PERSON><PERSON><PERSON><PERSON>'s [AgentExecutor](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor.iter), you could add chat [Memory](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor.memory) so it can engage in a multi-turn conversation."]}, {"cell_type": "code", "execution_count": 8, "id": "b97beba5-8f74-430c-9399-91b77c8fa15c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The output of the magic function when the input is 3 is 5.\n", "---\n", "Yes, you mentioned your name is <PERSON>.\n", "---\n", "The output of the magic function when the input is 3 is 5.\n"]}], "source": ["from langchain.agents import AgentExecutor, create_tool_calling_agent\n", "from langchain_core.chat_history import InMemoryChatMessageHistory\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables.history import RunnableWithMessageHistory\n", "from langchain_core.tools import tool\n", "from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"gpt-4o\")\n", "memory = InMemoryChatMessageHistory(session_id=\"test-session\")\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant.\"),\n", "        # First put the history\n", "        (\"placeholder\", \"{chat_history}\"),\n", "        # Then the new input\n", "        (\"human\", \"{input}\"),\n", "        # Finally the scratchpad\n", "        (\"placeholder\", \"{agent_scratchpad}\"),\n", "    ]\n", ")\n", "\n", "\n", "@tool\n", "def magic_function(input: int) -> int:\n", "    \"\"\"Applies a magic function to an input.\"\"\"\n", "    return input + 2\n", "\n", "\n", "tools = [magic_function]\n", "\n", "\n", "agent = create_tool_calling_agent(model, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)\n", "\n", "agent_with_chat_history = RunnableWithMessageHistory(\n", "    agent_executor,\n", "    # This is needed because in most real world scenarios, a session id is needed\n", "    # It isn't really used here because we are using a simple in memory ChatMessageHistory\n", "    lambda session_id: memory,\n", "    input_messages_key=\"input\",\n", "    history_messages_key=\"chat_history\",\n", ")\n", "\n", "config = {\"configurable\": {\"session_id\": \"test-session\"}}\n", "print(\n", "    agent_with_chat_history.invoke(\n", "        {\"input\": \"Hi, I'm polly! What's the output of magic_function of 3?\"}, config\n", "    )[\"output\"]\n", ")\n", "print(\"---\")\n", "print(agent_with_chat_history.invoke({\"input\": \"Remember my name?\"}, config)[\"output\"])\n", "print(\"---\")\n", "print(\n", "    agent_with_chat_history.invoke({\"input\": \"what was that output again?\"}, config)[\n", "        \"output\"\n", "    ]\n", ")"]}, {"cell_type": "markdown", "id": "c2a5a32f", "metadata": {}, "source": ["### In LangGraph\n", "\n", "Memory is just [persistence](https://langchain-ai.github.io/langgraph/how-tos/persistence/), aka [checkpointing](https://langchain-ai.github.io/langgraph/reference/checkpoints/).\n", "\n", "Add a `checkpointer` to the agent and you get chat memory for free."]}, {"cell_type": "code", "execution_count": 9, "id": "baca3dc6-678b-4509-9275-2fd653102898", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The output of the magic function for the input 3 is 5.\n", "---\n", "Yes, you mentioned that your name is <PERSON>.\n", "---\n", "The output of the magic function for the input 3 was 5.\n"]}], "source": ["from langgraph.checkpoint.memory import MemorySaver  # an in-memory checkpointer\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "system_message = \"You are a helpful assistant.\"\n", "# This could also be a SystemMessage object\n", "# system_message = SystemMessage(content=\"You are a helpful assistant. Respond only in Spanish.\")\n", "\n", "memory = MemorySaver()\n", "langgraph_agent_executor = create_react_agent(\n", "    model, tools, prompt=system_message, checkpointer=memory\n", ")\n", "\n", "config = {\"configurable\": {\"thread_id\": \"test-thread\"}}\n", "print(\n", "    langgraph_agent_executor.invoke(\n", "        {\n", "            \"messages\": [\n", "                (\"user\", \"Hi, I'm polly! What's the output of magic_function of 3?\")\n", "            ]\n", "        },\n", "        config,\n", "    )[\"messages\"][-1].content\n", ")\n", "print(\"---\")\n", "print(\n", "    langgraph_agent_executor.invoke(\n", "        {\"messages\": [(\"user\", \"Remember my name?\")]}, config\n", "    )[\"messages\"][-1].content\n", ")\n", "print(\"---\")\n", "print(\n", "    langgraph_agent_executor.invoke(\n", "        {\"messages\": [(\"user\", \"what was that output again?\")]}, config\n", "    )[\"messages\"][-1].content\n", ")"]}, {"cell_type": "markdown", "id": "d7cf24a8", "metadata": {}, "source": ["## Iterating through steps\n", "\n", "### In LangChain\n", "\n", "With <PERSON><PERSON><PERSON><PERSON>'s [AgentExecutor](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor.iter), you could iterate over the steps using the [stream](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.Runnable.html#langchain_core.runnables.base.Runnable.stream) (or async `astream`) methods or the [iter](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor.iter) method. LangGraph supports stepwise iteration using [stream](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.Runnable.html#langchain_core.runnables.base.Runnable.stream) "]}, {"cell_type": "code", "execution_count": 10, "id": "e62843c4-1107-41f0-a50b-aea256e28053", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'actions': [ToolAgentAction(tool='magic_function', tool_input={'input': 3}, log=\"\\nInvoking: `magic_function` with `{'input': 3}`\\n\\n\\n\", message_log=[AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'call_yyetzabaDBRX9Ml2KyqfKzZM', 'function': {'arguments': '{\"input\":3}', 'name': 'magic_function'}, 'type': 'function'}]}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7'}, id='run-7a3a5ada-52ec-4df0-bf7d-81e5051b01b4', tool_calls=[{'name': 'magic_function', 'args': {'input': 3}, 'id': 'call_yyetzabaDBRX9Ml2KyqfKzZM', 'type': 'tool_call'}], tool_call_chunks=[{'name': 'magic_function', 'args': '{\"input\":3}', 'id': 'call_yyetzabaDBRX9Ml2KyqfKzZM', 'index': 0, 'type': 'tool_call_chunk'}])], tool_call_id='call_yyetzabaDBRX9Ml2KyqfKzZM')], 'messages': [AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'call_yyetzabaDBRX9Ml2KyqfKzZM', 'function': {'arguments': '{\"input\":3}', 'name': 'magic_function'}, 'type': 'function'}]}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7'}, id='run-7a3a5ada-52ec-4df0-bf7d-81e5051b01b4', tool_calls=[{'name': 'magic_function', 'args': {'input': 3}, 'id': 'call_yyetzabaDBRX9Ml2KyqfKzZM', 'type': 'tool_call'}], tool_call_chunks=[{'name': 'magic_function', 'args': '{\"input\":3}', 'id': 'call_yyetzabaDBRX9Ml2KyqfKzZM', 'index': 0, 'type': 'tool_call_chunk'}])]}\n", "{'steps': [AgentStep(action=ToolAgentAction(tool='magic_function', tool_input={'input': 3}, log=\"\\nInvoking: `magic_function` with `{'input': 3}`\\n\\n\\n\", message_log=[AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'call_yyetzabaDBRX9Ml2KyqfKzZM', 'function': {'arguments': '{\"input\":3}', 'name': 'magic_function'}, 'type': 'function'}]}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7'}, id='run-7a3a5ada-52ec-4df0-bf7d-81e5051b01b4', tool_calls=[{'name': 'magic_function', 'args': {'input': 3}, 'id': 'call_yyetzabaDBRX9Ml2KyqfKzZM', 'type': 'tool_call'}], tool_call_chunks=[{'name': 'magic_function', 'args': '{\"input\":3}', 'id': 'call_yyetzabaDBRX9Ml2KyqfKzZM', 'index': 0, 'type': 'tool_call_chunk'}])], tool_call_id='call_yyetzabaDBRX9Ml2KyqfKzZM'), observation=5)], 'messages': [FunctionMessage(content='5', additional_kwargs={}, response_metadata={}, name='magic_function')]}\n", "{'output': 'The value of `magic_function(3)` is 5.', 'messages': [AIMessage(content='The value of `magic_function(3)` is 5.', additional_kwargs={}, response_metadata={})]}\n"]}], "source": ["from langchain.agents import AgentExecutor, create_tool_calling_agent\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.tools import tool\n", "from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"gpt-4o\")\n", "\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant.\"),\n", "        (\"human\", \"{input}\"),\n", "        # Placeholders fill up a **list** of messages\n", "        (\"placeholder\", \"{agent_scratchpad}\"),\n", "    ]\n", ")\n", "\n", "\n", "@tool\n", "def magic_function(input: int) -> int:\n", "    \"\"\"Applies a magic function to an input.\"\"\"\n", "    return input + 2\n", "\n", "\n", "tools = [magic_function]\n", "\n", "agent = create_tool_calling_agent(model, tools, prompt=prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools)\n", "\n", "for step in agent_executor.stream({\"input\": query}):\n", "    print(step)"]}, {"cell_type": "markdown", "id": "46ccbcbf", "metadata": {}, "source": ["### In LangGraph\n", "\n", "In LangGraph, things are handled natively using [stream](https://langchain-ai.github.io/langgraph/reference/graphs/#langgraph.graph.graph.CompiledGraph.stream) or the asynchronous `astream` method."]}, {"cell_type": "code", "execution_count": 11, "id": "076ebc85-f804-4093-a25a-a16334c9898e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'agent': {'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_IHTMrjvIHn8gFOX42FstIpr9', 'function': {'arguments': '{\"input\":3}', 'name': 'magic_function'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 14, 'prompt_tokens': 61, 'total_tokens': 75, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-1a6970da-163a-4e4d-b9b7-7e73b1057f42-0', tool_calls=[{'name': 'magic_function', 'args': {'input': 3}, 'id': 'call_IHTMrjvIHn8gFOX42FstIpr9', 'type': 'tool_call'}], usage_metadata={'input_tokens': 61, 'output_tokens': 14, 'total_tokens': 75, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}})]}}\n", "{'tools': {'messages': [ToolMessage(content='5', name='magic_function', id='51a9d3e4-734d-426f-a5a1-c6597e4efe25', tool_call_id='call_IHTMrjvIHn8gFOX42FstIpr9')]}}\n", "{'agent': {'messages': [AIMessage(content='The value of `magic_function(3)` is 5.', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 14, 'prompt_tokens': 84, 'total_tokens': 98, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a20a4ee344', 'finish_reason': 'stop', 'logprobs': None}, id='run-73001576-a3dc-4552-8d81-c9ce8aec05b3-0', usage_metadata={'input_tokens': 84, 'output_tokens': 14, 'total_tokens': 98, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}})]}}\n"]}], "source": ["from langgraph.prebuilt import create_react_agent\n", "from langgraph.prebuilt.chat_agent_executor import AgentState\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant.\"),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "langgraph_agent_executor = create_react_agent(model, tools, prompt=prompt)\n", "\n", "for step in langgraph_agent_executor.stream(\n", "    {\"messages\": [(\"human\", query)]}, stream_mode=\"updates\"\n", "):\n", "    print(step)"]}, {"cell_type": "markdown", "id": "6898ccbc-42b1-4373-954a-2c7b3849fbb0", "metadata": {}, "source": ["## `return_intermediate_steps`\n", "\n", "### In LangChain\n", "\n", "Setting this parameter on AgentExecutor allows users to access intermediate_steps, which pairs agent actions (e.g., tool invocations) with their outcomes.\n"]}, {"cell_type": "code", "execution_count": 12, "id": "a2f720f3-c121-4be2-b498-92c16bb44b0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(ToolAgentAction(tool='magic_function', tool_input={'input': 3}, log=\"\\nInvoking: `magic_function` with `{'input': 3}`\\n\\n\\n\", message_log=[AIMessageChunk(content='', additional_kwargs={'tool_calls': [{'index': 0, 'id': 'call_njTvl2RsVf4q1aMUxoYnJuK1', 'function': {'arguments': '{\"input\":3}', 'name': 'magic_function'}, 'type': 'function'}]}, response_metadata={'finish_reason': 'tool_calls', 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7'}, id='run-c9dfe3ab-2db6-4592-851e-89e056aeab32', tool_calls=[{'name': 'magic_function', 'args': {'input': 3}, 'id': 'call_njTvl2RsVf4q1aMUxoYnJuK1', 'type': 'tool_call'}], tool_call_chunks=[{'name': 'magic_function', 'args': '{\"input\":3}', 'id': 'call_njTvl2RsVf4q1aMUxoYnJuK1', 'index': 0, 'type': 'tool_call_chunk'}])], tool_call_id='call_njTvl2RsVf4q1aMUxoYnJuK1'), 5)]\n"]}], "source": ["agent_executor = AgentExecutor(agent=agent, tools=tools, return_intermediate_steps=True)\n", "result = agent_executor.invoke({\"input\": query})\n", "print(result[\"intermediate_steps\"])"]}, {"cell_type": "markdown", "id": "594f7567-302f-4fa8-85bb-025ac8322162", "metadata": {}, "source": ["### In LangGraph\n", "\n", "By default the [react agent executor](https://langchain-ai.github.io/langgraph/reference/prebuilt/#langgraph.prebuilt.chat_agent_executor.create_react_agent) in LangGraph appends all messages to the central state. Therefore, it is easy to see any intermediate steps by just looking at the full state."]}, {"cell_type": "code", "execution_count": 13, "id": "ef23117a-5ccb-42ce-80c3-ea49a9d3a942", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='what is the value of magic_function(3)?', additional_kwargs={}, response_metadata={}, id='1abb52c2-4bc2-4d82-bd32-5a24c3976b0f'),\n", "  AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_XfQD6C7rAalcmicQubkhJVFq', 'function': {'arguments': '{\"input\":3}', 'name': 'magic_function'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 14, 'prompt_tokens': 55, 'total_tokens': 69, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a20a4ee344', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-34f02786-5b5c-4bb1-bd9e-406c81944a24-0', tool_calls=[{'name': 'magic_function', 'args': {'input': 3}, 'id': 'call_XfQD6C7rAalcmicQubkhJVFq', 'type': 'tool_call'}], usage_metadata={'input_tokens': 55, 'output_tokens': 14, 'total_tokens': 69, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}}),\n", "  ToolMessage(content='5', name='magic_function', id='cbc9fadf-1962-4ed7-b476-348c774652be', tool_call_id='call_XfQD6C7rAalcmicQubkhJVFq'),\n", "  AIMessage(content='The value of `magic_function(3)` is 5.', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 14, 'prompt_tokens': 78, 'total_tokens': 92, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7', 'finish_reason': 'stop', 'logprobs': None}, id='run-547e03d2-872d-4008-a38d-b7f739a77df5-0', usage_metadata={'input_tokens': 78, 'output_tokens': 14, 'total_tokens': 92, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}})]}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "langgraph_agent_executor = create_react_agent(model, tools=tools)\n", "\n", "messages = langgraph_agent_executor.invoke({\"messages\": [(\"human\", query)]})\n", "\n", "messages"]}, {"cell_type": "markdown", "id": "45b528e5-57e1-450e-8d91-513eab53b543", "metadata": {}, "source": ["## `max_iterations`\n", "\n", "### In LangChain\n", "\n", "`AgentExecutor` implements a `max_iterations` parameter, allowing users to abort a run that exceeds a specified number of iterations."]}, {"cell_type": "code", "execution_count": 14, "id": "16f189a7-fc78-4cb5-aa16-a94ca06401a6", "metadata": {}, "outputs": [], "source": ["@tool\n", "def magic_function(input: str) -> str:\n", "    \"\"\"Applies a magic function to an input.\"\"\"\n", "    return \"Sorry, there was an error. Please try again.\"\n", "\n", "\n", "tools = [magic_function]"]}, {"cell_type": "code", "execution_count": 15, "id": "c96aefd7-6f6e-4670-aca6-1ac3d4e7871f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m<PERSON><PERSON> siento, no puedo decirte directamente el valor de `magic_function(3)`. <PERSON> des<PERSON>, puedo usar la función mágica para calcularlo. ¿Te gustaría que lo hiciera?\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': 'what is the value of magic_function(3)?',\n", " 'output': 'Lo siento, no puedo decirte directamente el valor de `magic_function(3)`. <PERSON> deseas, puedo usar la función mágica para calcularlo. ¿Te gustaría que lo hiciera?'}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant. Respond only in Spanish.\"),\n", "        (\"human\", \"{input}\"),\n", "        # Placeholders fill up a **list** of messages\n", "        (\"placeholder\", \"{agent_scratchpad}\"),\n", "    ]\n", ")\n", "\n", "agent = create_tool_calling_agent(model, tools, prompt)\n", "agent_executor = AgentExecutor(\n", "    agent=agent,\n", "    tools=tools,\n", "    verbose=True,\n", "    max_iterations=3,\n", ")\n", "\n", "agent_executor.invoke({\"input\": query})"]}, {"cell_type": "markdown", "id": "dd3a933f", "metadata": {}, "source": ["### In LangGraph\n", "\n", "In LangGraph this is controlled via `recursion_limit` configuration parameter.\n", "\n", "Note that in `AgentExecutor`, an \"iteration\" includes a full turn of tool invocation and execution. In LangGraph, each step contributes to the recursion limit, so we will need to multiply by two (and add one) to get equivalent results.\n", "\n", "If the recursion limit is reached, <PERSON><PERSON><PERSON><PERSON> raises a specific exception type, that we can catch and manage similarly to AgentExecutor."]}, {"cell_type": "code", "execution_count": 16, "id": "b974a91f-6ae8-4644-83d9-73666258a6db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='what is the value of magic_function(3)?' additional_kwargs={} response_metadata={} id='c2489fe8-e69c-4163-876d-3cce26b28521'\n", "content='' additional_kwargs={'tool_calls': [{'id': 'call_OyNTcO6SDAvZcBlIEknPRrTR', 'function': {'arguments': '{\"input\":\"3\"}', 'name': 'magic_function'}, 'type': 'function'}], 'refusal': None} response_metadata={'token_usage': {'completion_tokens': 14, 'prompt_tokens': 55, 'total_tokens': 69, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7', 'finish_reason': 'tool_calls', 'logprobs': None} id='run-b65504bb-fa23-4f8a-8d6c-7edb6d16e7ff-0' tool_calls=[{'name': 'magic_function', 'args': {'input': '3'}, 'id': 'call_OyNTcO6SDAvZcBlIEknPRrTR', 'type': 'tool_call'}] usage_metadata={'input_tokens': 55, 'output_tokens': 14, 'total_tokens': 69, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}}\n", "content='Sorry, there was an error. Please try again.' name='magic_function' id='f00e0bff-54fe-4726-a1a7-127a59d8f7ed' tool_call_id='call_OyNTcO6SDAvZcBlIEknPRrTR'\n", "content=\"It seems there was an error when trying to compute the value of the magic function with input 3. Let's try again.\" additional_kwargs={'tool_calls': [{'id': 'call_Q020rQoJh4cnh8WglIMnDm4z', 'function': {'arguments': '{\"input\":\"3\"}', 'name': 'magic_function'}, 'type': 'function'}], 'refusal': None} response_metadata={'token_usage': {'completion_tokens': 40, 'prompt_tokens': 88, 'total_tokens': 128, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7', 'finish_reason': 'tool_calls', 'logprobs': None} id='run-556d8cb2-b47a-4826-b17d-b520982c2475-0' tool_calls=[{'name': 'magic_function', 'args': {'input': '3'}, 'id': 'call_Q020rQoJh4cnh8WglIMnDm4z', 'type': 'tool_call'}] usage_metadata={'input_tokens': 88, 'output_tokens': 40, 'total_tokens': 128, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}}\n", "content='Sorry, there was an error. Please try again.' name='magic_function' id='777212cd-8381-44db-9762-3f81951ea73e' tool_call_id='call_Q020rQoJh4cnh8WglIMnDm4z'\n", "content=\"It seems there is a persistent issue in computing the value of the magic function with the input 3. Unfortunately, I can't provide the value at this time. If you have any other questions or need further assistance, feel free to ask!\" additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 49, 'prompt_tokens': 150, 'total_tokens': 199, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7', 'finish_reason': 'stop', 'logprobs': None} id='run-92ec0b90-bc8e-4851-9139-f1d976145ab7-0' usage_metadata={'input_tokens': 150, 'output_tokens': 49, 'total_tokens': 199, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}}\n"]}], "source": ["from langgraph.errors import GraphRecursionError\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "RECURSION_LIMIT = 2 * 3 + 1\n", "\n", "langgraph_agent_executor = create_react_agent(model, tools=tools)\n", "\n", "try:\n", "    for chunk in langgraph_agent_executor.stream(\n", "        {\"messages\": [(\"human\", query)]},\n", "        {\"recursion_limit\": RECURSION_LIMIT},\n", "        stream_mode=\"values\",\n", "    ):\n", "        print(chunk[\"messages\"][-1])\n", "except GraphRecursionError:\n", "    print({\"input\": query, \"output\": \"Agent stopped due to max iterations.\"})"]}, {"cell_type": "markdown", "id": "3a527158-ada5-4774-a98b-8272c6b6b2c0", "metadata": {}, "source": ["## `max_execution_time`\n", "\n", "### In LangChain\n", "\n", "`AgentExecutor` implements a `max_execution_time` parameter, allowing users to abort a run that exceeds a total time limit."]}, {"cell_type": "code", "execution_count": 18, "id": "4b8498fc-a7af-4164-a401-d8714f082306", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m<PERSON><PERSON> siento, no tengo la capacidad de evaluar directamente una función llamada \"magic_function\" con el valor 3. Sin embargo, si me proporcionas más detalles sobre qué hace la función o cómo está definida, podría intentar ayudarte a comprender su comportamiento o resolverlo de otra manera.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': 'what is the value of magic_function(3)?',\n", " 'output': 'Lo siento, no tengo la capacidad de evaluar directamente una función llamada \"magic_function\" con el valor 3. Sin embargo, si me proporcionas más detalles sobre qué hace la función o cómo está definida, podría intentar ayudarte a comprender su comportamiento o resolverlo de otra manera.'}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["import time\n", "\n", "\n", "@tool\n", "def magic_function(input: str) -> str:\n", "    \"\"\"Applies a magic function to an input.\"\"\"\n", "    time.sleep(2.5)\n", "    return \"Sorry, there was an error. Please try again.\"\n", "\n", "\n", "tools = [magic_function]\n", "\n", "agent = create_tool_calling_agent(model, tools, prompt)\n", "agent_executor = AgentExecutor(\n", "    agent=agent,\n", "    tools=tools,\n", "    max_execution_time=2,\n", "    verbose=True,\n", ")\n", "\n", "agent_executor.invoke({\"input\": query})"]}, {"cell_type": "markdown", "id": "d02eb025", "metadata": {}, "source": ["### In LangGraph\n", "\n", "With LangG<PERSON><PERSON>'s react agent, you can control timeouts on two levels. \n", "\n", "You can set a `step_timeout` to bound each **step**:"]}, {"cell_type": "code", "execution_count": 19, "id": "b1d8883d-f5c4-444b-b15e-09827f1b9c57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'agent': {'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_UuxSgpGaqzX84sNlKzCVOiRO', 'function': {'arguments': '{\"input\":\"3\"}', 'name': 'magic_function'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 14, 'prompt_tokens': 55, 'total_tokens': 69, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-24c94cbd-2962-48cf-a447-af888eb6ef86-0', tool_calls=[{'name': 'magic_function', 'args': {'input': '3'}, 'id': 'call_UuxSgpGaqzX84sNlKzCVOiRO', 'type': 'tool_call'}], usage_metadata={'input_tokens': 55, 'output_tokens': 14, 'total_tokens': 69, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}})]}}\n", "------\n", "{'input': 'what is the value of magic_function(3)?', 'output': 'Agent stopped due to a step timeout.'}\n"]}], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "langgraph_agent_executor = create_react_agent(model, tools=tools)\n", "# Set the max timeout for each step here\n", "langgraph_agent_executor.step_timeout = 2\n", "\n", "try:\n", "    for chunk in langgraph_agent_executor.stream({\"messages\": [(\"human\", query)]}):\n", "        print(chunk)\n", "        print(\"------\")\n", "except TimeoutError:\n", "    print({\"input\": query, \"output\": \"Agent stopped due to a step timeout.\"})"]}, {"cell_type": "markdown", "id": "32a9db70", "metadata": {}, "source": ["The other way to set a single max timeout for an entire run is to directly use the python stdlib [asyncio](https://docs.python.org/3/library/asyncio.html) library."]}, {"cell_type": "code", "execution_count": 20, "id": "f8d5bd03-6e7e-484b-a543-c8c0ab160b69", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'agent': {'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_km17xvoY7wJ5yNnXhb5V9D3I', 'function': {'arguments': '{\"input\":\"3\"}', 'name': 'magic_function'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 14, 'prompt_tokens': 55, 'total_tokens': 69, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_45c6de4934', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-b44a04e5-9b68-4020-be36-98de1593eefc-0', tool_calls=[{'name': 'magic_function', 'args': {'input': '3'}, 'id': 'call_km17xvoY7wJ5yNnXhb5V9D3I', 'type': 'tool_call'}], usage_metadata={'input_tokens': 55, 'output_tokens': 14, 'total_tokens': 69, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}})]}}\n", "------\n", "Task Cancelled.\n"]}], "source": ["import asyncio\n", "\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "langgraph_agent_executor = create_react_agent(model, tools=tools)\n", "\n", "\n", "async def stream(langgraph_agent_executor, inputs):\n", "    async for chunk in langgraph_agent_executor.astream(\n", "        {\"messages\": [(\"human\", query)]}\n", "    ):\n", "        print(chunk)\n", "        print(\"------\")\n", "\n", "\n", "try:\n", "    task = asyncio.create_task(\n", "        stream(langgraph_agent_executor, {\"messages\": [(\"human\", query)]})\n", "    )\n", "    await asyncio.wait_for(task, timeout=3)\n", "except asyncio.TimeoutError:\n", "    print(\"Task Cancelled.\")"]}, {"cell_type": "markdown", "id": "4884ac87", "metadata": {}, "source": ["## `early_stopping_method`\n", "\n", "### In LangChain\n", "\n", "With <PERSON><PERSON><PERSON><PERSON>'s [AgentExecutor](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor.iter), you could configure an [early_stopping_method](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor.early_stopping_method) to either return a string saying \"Agent stopped due to iteration limit or time limit.\" (`\"force\"`) or prompt the LLM a final time to respond (`\"generate\"`)."]}, {"cell_type": "code", "execution_count": 21, "id": "3f6e2cf2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output with early_stopping_method='force':\n", "Agent stopped due to max iterations.\n"]}], "source": ["from langchain.agents import AgentExecutor, create_tool_calling_agent\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.tools import tool\n", "from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"gpt-4o\")\n", "\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant.\"),\n", "        (\"human\", \"{input}\"),\n", "        # Placeholders fill up a **list** of messages\n", "        (\"placeholder\", \"{agent_scratchpad}\"),\n", "    ]\n", ")\n", "\n", "\n", "@tool\n", "def magic_function(input: int) -> int:\n", "    \"\"\"Applies a magic function to an input.\"\"\"\n", "    return \"Sorry there was an error, please try again.\"\n", "\n", "\n", "tools = [magic_function]\n", "\n", "agent = create_tool_calling_agent(model, tools, prompt=prompt)\n", "agent_executor = AgentExecutor(\n", "    agent=agent, tools=tools, early_stopping_method=\"force\", max_iterations=1\n", ")\n", "\n", "result = agent_executor.invoke({\"input\": query})\n", "print(\"Output with early_stopping_method='force':\")\n", "print(result[\"output\"])"]}, {"cell_type": "markdown", "id": "706e05c4", "metadata": {}, "source": ["### In LangGraph\n", "\n", "In LangGraph, you can explicitly handle the response behavior outside the agent, since the full state can be accessed."]}, {"cell_type": "code", "execution_count": 22, "id": "73cabbc4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='what is the value of magic_function(3)?' additional_kwargs={} response_metadata={} id='81fd2e50-1e6a-4871-87aa-b7c1225913a4'\n", "content='' additional_kwargs={'tool_calls': [{'id': 'call_aaEzj3aO1RTnB0uoc9rYUIhi', 'function': {'arguments': '{\"input\":3}', 'name': 'magic_function'}, 'type': 'function'}], 'refusal': None} response_metadata={'token_usage': {'completion_tokens': 14, 'prompt_tokens': 55, 'total_tokens': 69, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7', 'finish_reason': 'tool_calls', 'logprobs': None} id='run-476bc4b1-b7bf-4607-a31c-ddf09dc814c5-0' tool_calls=[{'name': 'magic_function', 'args': {'input': 3}, 'id': 'call_aaEzj3aO1RTnB0uoc9rYUIhi', 'type': 'tool_call'}] usage_metadata={'input_tokens': 55, 'output_tokens': 14, 'total_tokens': 69, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}}\n", "content='Sorry there was an error, please try again.' name='magic_function' id='dcbe7e3e-0ed4-467d-a729-2f45916ff44f' tool_call_id='call_aaEzj3aO1RTnB0uoc9rYUIhi'\n", "content=\"It seems there was an error when trying to compute the value of `magic_function(3)`. Let's try that again.\" additional_kwargs={'tool_calls': [{'id': 'call_jr4R8uJn2pdXF5GZC2Dg3YWS', 'function': {'arguments': '{\"input\":3}', 'name': 'magic_function'}, 'type': 'function'}], 'refusal': None} response_metadata={'token_usage': {'completion_tokens': 40, 'prompt_tokens': 87, 'total_tokens': 127, 'completion_tokens_details': {'audio_tokens': None, 'reasoning_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_a7d06e42a7', 'finish_reason': 'tool_calls', 'logprobs': None} id='run-d94b8932-6e9e-4ab1-99f7-7dca89887ffe-0' tool_calls=[{'name': 'magic_function', 'args': {'input': 3}, 'id': 'call_jr4R8uJn2pdXF5GZC2Dg3YWS', 'type': 'tool_call'}] usage_metadata={'input_tokens': 87, 'output_tokens': 40, 'total_tokens': 127, 'input_token_details': {'cache_read': 0}, 'output_token_details': {'reasoning': 0}}\n", "{'input': 'what is the value of magic_function(3)?', 'output': 'Agent stopped due to max iterations.'}\n"]}], "source": ["from langgraph.errors import GraphRecursionError\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "RECURSION_LIMIT = 2 * 1 + 1\n", "\n", "langgraph_agent_executor = create_react_agent(model, tools=tools)\n", "\n", "try:\n", "    for chunk in langgraph_agent_executor.stream(\n", "        {\"messages\": [(\"human\", query)]},\n", "        {\"recursion_limit\": RECURSION_LIMIT},\n", "        stream_mode=\"values\",\n", "    ):\n", "        print(chunk[\"messages\"][-1])\n", "except GraphRecursionError:\n", "    print({\"input\": query, \"output\": \"Agent stopped due to max iterations.\"})"]}, {"cell_type": "markdown", "id": "017fe20e", "metadata": {}, "source": ["## `trim_intermediate_steps`\n", "\n", "### In LangChain\n", "\n", "With <PERSON><PERSON><PERSON><PERSON>'s [AgentExecutor](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor), you could trim the intermediate steps of long-running agents using [trim_intermediate_steps](https://python.langchain.com/api_reference/langchain/agents/langchain.agents.agent.AgentExecutor.html#langchain.agents.agent.AgentExecutor.trim_intermediate_steps), which is either an integer (indicating the agent should keep the last N steps) or a custom function.\n", "\n", "For instance, we could trim the value so the agent only sees the most recent intermediate step."]}, {"cell_type": "code", "execution_count": 23, "id": "b94bb169", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Call number: 1\n", "Call number: 2\n", "Call number: 3\n", "Call number: 4\n", "Call number: 5\n", "Call number: 6\n", "Call number: 7\n", "Call number: 8\n", "Call number: 9\n", "Call number: 10\n", "Call number: 11\n", "Call number: 12\n", "Call number: 13\n", "Call number: 14\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Stopping agent prematurely due to triggering stop condition\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Call number: 15\n"]}], "source": ["from langchain.agents import AgentExecutor, create_tool_calling_agent\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.tools import tool\n", "from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"gpt-4o\")\n", "\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"You are a helpful assistant.\"),\n", "        (\"human\", \"{input}\"),\n", "        # Placeholders fill up a **list** of messages\n", "        (\"placeholder\", \"{agent_scratchpad}\"),\n", "    ]\n", ")\n", "\n", "\n", "magic_step_num = 1\n", "\n", "\n", "@tool\n", "def magic_function(input: int) -> int:\n", "    \"\"\"Applies a magic function to an input.\"\"\"\n", "    global magic_step_num\n", "    print(f\"Call number: {magic_step_num}\")\n", "    magic_step_num += 1\n", "    return input + magic_step_num\n", "\n", "\n", "tools = [magic_function]\n", "\n", "agent = create_tool_calling_agent(model, tools, prompt=prompt)\n", "\n", "\n", "def trim_steps(steps: list):\n", "    # Let's give the agent amnesia\n", "    return []\n", "\n", "\n", "agent_executor = AgentExecutor(\n", "    agent=agent, tools=tools, trim_intermediate_steps=trim_steps\n", ")\n", "\n", "\n", "query = \"Call the magic function 4 times in sequence with the value 3. You cannot call it multiple times at once.\"\n", "\n", "for step in agent_executor.stream({\"input\": query}):\n", "    pass"]}, {"cell_type": "markdown", "id": "3d450c5a", "metadata": {}, "source": ["### In LangGraph\n", "\n", "We can use the [`prompt`](https://langchain-ai.github.io/langgraph/reference/prebuilt/#langgraph.prebuilt.chat_agent_executor.create_react_agent) just as before when passing in [prompt templates](#prompt-templates)."]}, {"cell_type": "code", "execution_count": 24, "id": "b309ba9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Call number: 1\n", "Call number: 2\n", "Call number: 3\n", "Call number: 4\n", "Call number: 5\n", "Call number: 6\n", "Call number: 7\n", "Call number: 8\n", "Call number: 9\n", "Call number: 10\n", "Call number: 11\n", "Call number: 12\n", "Stopping agent prematurely due to triggering stop condition\n"]}], "source": ["from langgraph.errors import GraphRecursionError\n", "from langgraph.prebuilt import create_react_agent\n", "from langgraph.prebuilt.chat_agent_executor import AgentState\n", "\n", "magic_step_num = 1\n", "\n", "\n", "@tool\n", "def magic_function(input: int) -> int:\n", "    \"\"\"Applies a magic function to an input.\"\"\"\n", "    global magic_step_num\n", "    print(f\"Call number: {magic_step_num}\")\n", "    magic_step_num += 1\n", "    return input + magic_step_num\n", "\n", "\n", "tools = [magic_function]\n", "\n", "\n", "def _modify_state_messages(state: AgentState):\n", "    # Give the agent amnesia, only keeping the original user query\n", "    return [(\"system\", \"You are a helpful assistant\"), state[\"messages\"][0]]\n", "\n", "\n", "langgraph_agent_executor = create_react_agent(\n", "    model, tools, prompt=_modify_state_messages\n", ")\n", "\n", "try:\n", "    for step in langgraph_agent_executor.stream(\n", "        {\"messages\": [(\"human\", query)]}, stream_mode=\"updates\"\n", "    ):\n", "        pass\n", "except GraphRecursionError as e:\n", "    print(\"Stopping agent prematurely due to triggering stop condition\")"]}, {"cell_type": "markdown", "id": "41377eb8", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now learned how to migrate your LangChain agent executors to LangGraph.\n", "\n", "Next, check out other [LangGraph how-to guides](https://langchain-ai.github.io/langgraph/how-tos/)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}