{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to bind model-specific tools\n", "\n", "Providers adopt different conventions for formatting tool schemas. \n", "For instance, OpenAI uses a format like this:\n", "\n", "- `type`: The type of the tool. At the time of writing, this is always `\"function\"`.\n", "- `function`: An object containing tool parameters.\n", "- `function.name`: The name of the schema to output.\n", "- `function.description`: A high level description of the schema to output.\n", "- `function.parameters`: The nested details of the schema you want to extract, formatted as a [JSON schema](https://json-schema.org/) dict.\n", "\n", "We can bind this model-specific format directly to the model as well if preferred. Here's an example:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_mn4ELw1NbuE0DFYhIeK0GrPe', 'function': {'arguments': '{\"a\":119,\"b\":8}', 'name': 'multiply'}, 'type': 'function'}]}, response_metadata={'token_usage': {'completion_tokens': 17, 'prompt_tokens': 62, 'total_tokens': 79}, 'model_name': 'gpt-3.5-turbo', 'system_fingerprint': 'fp_c2295e73ad', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-353e8a9a-7125-4f94-8c68-4f3da4c21120-0', tool_calls=[{'name': 'multiply', 'args': {'a': 119, 'b': 8}, 'id': 'call_mn4ELw1NbuE0DFYhIeK0GrPe'}])"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI()\n", "\n", "model_with_tools = model.bind(\n", "    tools=[\n", "        {\n", "            \"type\": \"function\",\n", "            \"function\": {\n", "                \"name\": \"multiply\",\n", "                \"description\": \"Multiply two integers together.\",\n", "                \"parameters\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"a\": {\"type\": \"number\", \"description\": \"First integer\"},\n", "                        \"b\": {\"type\": \"number\", \"description\": \"Second integer\"},\n", "                    },\n", "                    \"required\": [\"a\", \"b\"],\n", "                },\n", "            },\n", "        }\n", "    ]\n", ")\n", "\n", "model_with_tools.invoke(\"Whats 119 times 8?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is functionally equivalent to the `bind_tools()` method."]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}