{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to stream events from a tool\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [<PERSON><PERSON><PERSON><PERSON>](/docs/concepts/tools)\n", "- [Custom tools](/docs/how_to/custom_tools)\n", "- [Using stream events](/docs/how_to/streaming/#using-stream-events)\n", "- [Accessing RunnableConfig within a custom tool](/docs/how_to/tool_configure/)\n", "\n", ":::\n", "\n", "If you have [tools](/docs/concepts/tools/) that call [chat models](/docs/concepts/chat_models/), [retrievers](/docs/concepts/retrievers/), or other [runnables](/docs/concepts/runnables/), you may want to access [internal events](https://python.langchain.com/docs/how_to/streaming/#event-reference) from those runnables or configure them with additional properties. This guide shows you how to manually pass parameters properly so that you can do this using the `astream_events()` method.\n", "\n", ":::caution Compatibility\n", "\n", "Lang<PERSON><PERSON><PERSON> cannot automatically propagate configuration, including callbacks necessary for `astream_events()`, to child runnables if you are running `async` code in `python<=3.10`. This is a common reason why you may fail to see events being emitted from custom runnables or tools.\n", "\n", "If you are running `python<=3.10`, you will need to manually propagate the `RunnableConfig` object to the child runnable in async environments. For an example of how to manually propagate the config, see the implementation of the `bar` RunnableLambda below.\n", "\n", "If you are running `python>=3.11`, the `RunnableConfig` will automatically propagate to child runnables in async environment. However, it is still a good idea to propagate the `RunnableConfig` manually if your code may run in older Python versions.\n", "\n", "This guide also requires `langchain-core>=0.2.16`.\n", ":::\n", "\n", "Say you have a custom tool that calls a chain that condenses its input by prompting a chat model to return only 10 words, then reversing the output. First, define it in a naive way:\n", "\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"model\" />\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "import os\n", "from getpass import getpass\n", "\n", "from langchain_anthropic import ChatAnthropic\n", "\n", "if \"ANTHROPIC_API_KEY\" not in os.environ:\n", "    os.environ[\"ANTHROPIC_API_KEY\"] = getpass()\n", "\n", "model = ChatAnthropic(model=\"claude-3-5-sonnet-20240620\", temperature=0)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "async def special_summarization_tool(long_text: str) -> str:\n", "    \"\"\"A tool that summarizes input text using advanced techniques.\"\"\"\n", "    prompt = ChatPromptTemplate.from_template(\n", "        \"You are an expert writer. Summarize the following text in 10 words or less:\\n\\n{long_text}\"\n", "    )\n", "\n", "    def reverse(x: str):\n", "        return x[::-1]\n", "\n", "    chain = prompt | model | StrOutputParser() | reverse\n", "    summary = await chain.ainvoke({\"long_text\": long_text})\n", "    return summary"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Invoking the tool directly works just fine:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["'.yad noita<PERSON>rg rof tiftuo sesoohc yrraB ;scisy<PERSON> seifed eeB'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["LONG_TEXT = \"\"\"\n", "NARRATOR:\n", "(Black screen with text; The sound of buzzing bees can be heard)\n", "According to all known laws of aviation, there is no way a bee should be able to fly. Its wings are too small to get its fat little body off the ground. The bee, of course, flies anyway because bees don't care what humans think is impossible.\n", "BARRY BENSON:\n", "(<PERSON> is picking out a shirt)\n", "Yellow, black. Yellow, black. Yellow, black. Yellow, black. Ooh, black and yellow! Let's shake it up a little.\n", "JANET BENSON:\n", "<PERSON>! Breakfast is ready!\n", "BARRY:\n", "Coming! Hang on a second.\n", "\"\"\"\n", "\n", "await special_summarization_tool.ainvoke({\"long_text\": LONG_TEXT})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["But if you wanted to access the raw output from the chat model rather than the full tool, you might try to use the [`astream_events()`](/docs/how_to/streaming/#using-stream-events) method and look for an `on_chat_model_end` event. Here's what happens:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["stream = special_summarization_tool.astream_events({\"long_text\": LONG_TEXT})\n", "\n", "async for event in stream:\n", "    if event[\"event\"] == \"on_chat_model_end\":\n", "        # Never triggers in python<=3.10!\n", "        print(event)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You'll notice (unless you're running through this guide in `python>=3.11`) that there are no chat model events emitted from the child run!\n", "\n", "This is because the example above does not pass the tool's config object into the internal chain. To fix this, redefine your tool to take a special parameter typed as `RunnableConfig` (see [this guide](/docs/how_to/tool_configure) for more details). You'll also need to pass that parameter through into the internal chain when executing it:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnableConfig\n", "\n", "\n", "@tool\n", "async def special_summarization_tool_with_config(\n", "    long_text: str, config: RunnableConfig\n", ") -> str:\n", "    \"\"\"A tool that summarizes input text using advanced techniques.\"\"\"\n", "    prompt = ChatPromptTemplate.from_template(\n", "        \"You are an expert writer. Summarize the following text in 10 words or less:\\n\\n{long_text}\"\n", "    )\n", "\n", "    def reverse(x: str):\n", "        return x[::-1]\n", "\n", "    chain = prompt | model | StrOutputParser() | reverse\n", "    # Pass the \"config\" object as an argument to any executed runnables\n", "    summary = await chain.ainvoke({\"long_text\": long_text}, config=config)\n", "    return summary"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And now try the same `astream_events()` call as before with your new tool:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'event': 'on_chat_model_end', 'data': {'output': AIMessage(content='Bee defies physics; <PERSON> chooses outfit for graduation day.', additional_kwargs={}, response_metadata={'stop_reason': 'end_turn', 'stop_sequence': None}, id='run-337ac14e-8da8-4c6d-a69f-1573f93b651e', usage_metadata={'input_tokens': 182, 'output_tokens': 19, 'total_tokens': 201, 'input_token_details': {'cache_creation': 0, 'cache_read': 0}}), 'input': {'messages': [[HumanMessage(content=\"You are an expert writer. Summarize the following text in 10 words or less:\\n\\n\\nNARRATOR:\\n(Black screen with text; The sound of buzzing bees can be heard)\\nAccording to all known laws of aviation, there is no way a bee should be able to fly. Its wings are too small to get its fat little body off the ground. The bee, of course, flies anyway because bees don't care what humans think is impossible.\\nBARRY BENSON:\\n(<PERSON> is picking out a shirt)\\nYellow, black. Yellow, black. Yellow, black. Yellow, black. Ooh, black and yellow! Let's shake it up a little.\\nJANET BENSON:\\nBarry! Breakfast is ready!\\nBARRY:\\nComing! Hang on a second.\\n\", additional_kwargs={}, response_metadata={})]]}}, 'run_id': '337ac14e-8da8-4c6d-a69f-1573f93b651e', 'name': 'ChatAnthropic', 'tags': ['seq:step:2'], 'metadata': {'ls_provider': 'anthropic', 'ls_model_name': 'claude-3-5-sonnet-20240620', 'ls_model_type': 'chat', 'ls_temperature': 0.0, 'ls_max_tokens': 1024}, 'parent_ids': ['225beaa6-af73-4c91-b2d3-1afbbb88d53e']}\n"]}], "source": ["stream = special_summarization_tool_with_config.astream_events({\"long_text\": LONG_TEXT})\n", "\n", "async for event in stream:\n", "    if event[\"event\"] == \"on_chat_model_end\":\n", "        print(event)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Awesome! This time there's an event emitted.\n", "\n", "For streaming, `astream_events()` automatically calls internal runnables in a chain with streaming enabled if possible, so if you wanted to a stream of tokens as they are generated from the chat model, you could simply filter to look for `on_chat_model_stream` events with no other changes:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run-f5e049f7-4e98-4236-87ab-8cd1ce85a2d5', usage_metadata={'input_tokens': 182, 'output_tokens': 2, 'total_tokens': 184, 'input_token_details': {'cache_creation': 0, 'cache_read': 0}})}, 'run_id': 'f5e049f7-4e98-4236-87ab-8cd1ce85a2d5', 'name': 'ChatAnthropic', 'tags': ['seq:step:2'], 'metadata': {'ls_provider': 'anthropic', 'ls_model_name': 'claude-3-5-sonnet-20240620', 'ls_model_type': 'chat', 'ls_temperature': 0.0, 'ls_max_tokens': 1024}, 'parent_ids': ['51858043-b301-4b76-8abb-56218e405283']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='Bee', additional_kwargs={}, response_metadata={}, id='run-f5e049f7-4e98-4236-87ab-8cd1ce85a2d5')}, 'run_id': 'f5e049f7-4e98-4236-87ab-8cd1ce85a2d5', 'name': 'ChatAnthropic', 'tags': ['seq:step:2'], 'metadata': {'ls_provider': 'anthropic', 'ls_model_name': 'claude-3-5-sonnet-20240620', 'ls_model_type': 'chat', 'ls_temperature': 0.0, 'ls_max_tokens': 1024}, 'parent_ids': ['51858043-b301-4b76-8abb-56218e405283']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' defies physics;', additional_kwargs={}, response_metadata={}, id='run-f5e049f7-4e98-4236-87ab-8cd1ce85a2d5')}, 'run_id': 'f5e049f7-4e98-4236-87ab-8cd1ce85a2d5', 'name': 'ChatAnthropic', 'tags': ['seq:step:2'], 'metadata': {'ls_provider': 'anthropic', 'ls_model_name': 'claude-3-5-sonnet-20240620', 'ls_model_type': 'chat', 'ls_temperature': 0.0, 'ls_max_tokens': 1024}, 'parent_ids': ['51858043-b301-4b76-8abb-56218e405283']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' <PERSON> chooses outfit for', additional_kwargs={}, response_metadata={}, id='run-f5e049f7-4e98-4236-87ab-8cd1ce85a2d5')}, 'run_id': 'f5e049f7-4e98-4236-87ab-8cd1ce85a2d5', 'name': 'ChatAnthropic', 'tags': ['seq:step:2'], 'metadata': {'ls_provider': 'anthropic', 'ls_model_name': 'claude-3-5-sonnet-20240620', 'ls_model_type': 'chat', 'ls_temperature': 0.0, 'ls_max_tokens': 1024}, 'parent_ids': ['51858043-b301-4b76-8abb-56218e405283']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' graduation day.', additional_kwargs={}, response_metadata={}, id='run-f5e049f7-4e98-4236-87ab-8cd1ce85a2d5')}, 'run_id': 'f5e049f7-4e98-4236-87ab-8cd1ce85a2d5', 'name': 'ChatAnthropic', 'tags': ['seq:step:2'], 'metadata': {'ls_provider': 'anthropic', 'ls_model_name': 'claude-3-5-sonnet-20240620', 'ls_model_type': 'chat', 'ls_temperature': 0.0, 'ls_max_tokens': 1024}, 'parent_ids': ['51858043-b301-4b76-8abb-56218e405283']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={'stop_reason': 'end_turn', 'stop_sequence': None}, id='run-f5e049f7-4e98-4236-87ab-8cd1ce85a2d5', usage_metadata={'input_tokens': 0, 'output_tokens': 17, 'total_tokens': 17, 'input_token_details': {}})}, 'run_id': 'f5e049f7-4e98-4236-87ab-8cd1ce85a2d5', 'name': 'ChatAnthropic', 'tags': ['seq:step:2'], 'metadata': {'ls_provider': 'anthropic', 'ls_model_name': 'claude-3-5-sonnet-20240620', 'ls_model_type': 'chat', 'ls_temperature': 0.0, 'ls_max_tokens': 1024}, 'parent_ids': ['51858043-b301-4b76-8abb-56218e405283']}\n"]}], "source": ["stream = special_summarization_tool_with_config.astream_events({\"long_text\": LONG_TEXT})\n", "\n", "async for event in stream:\n", "    if event[\"event\"] == \"on_chat_model_stream\":\n", "        print(event)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now seen how to stream events from within a tool. Next, check out the following guides for more on using tools:\n", "\n", "- Pass [runtime values to tools](/docs/how_to/tool_runtime)\n", "- Pass [tool results back to a model](/docs/how_to/tool_results_pass_to_model)\n", "- [Dispatch custom callback events](/docs/how_to/callbacks_custom_events)\n", "\n", "You can also check out some more specific uses of tool calling:\n", "\n", "- Building [tool-using chains and agents](/docs/how_to#tools)\n", "- Getting [structured outputs](/docs/how_to/structured_output/) from models"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 4}