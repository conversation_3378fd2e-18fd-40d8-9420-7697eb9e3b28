{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_position: 2\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to add retrieval to chatbots\n", "\n", "[Retrieval](/docs/concepts/retrieval/) is a common technique chatbots use to augment their responses with data outside a chat model's training data. This section will cover how to implement retrieval in the context of chatbots, but it's worth noting that retrieval is a very subtle and deep topic - we encourage you to explore [other parts of the documentation](/docs/how_to#qa-with-rag) that go into greater depth!\n", "\n", "## Setup\n", "\n", "You'll need to install a few packages, and have your OpenAI API key set as an environment variable named `OPENAI_API_KEY`:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mWARNING: You are using pip version 22.0.4; however, version 23.3.2 is available.\n", "You should consider upgrading via the '/Users/<USER>/.pyenv/versions/3.10.5/bin/python -m pip install --upgrade pip' command.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["%pip install -qU langchain langchain-openai langchain-chroma beautifulsoup4\n", "\n", "# Set env var OPENAI_API_KEY or load from a .env file:\n", "import dotenv\n", "\n", "dotenv.load_dotenv()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's also set up a chat model that we'll use for the below examples."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "chat = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0.2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a retriever\n", "\n", "We'll use [the <PERSON><PERSON><PERSON> documentation](https://docs.smith.langchain.com/overview) as source material and store the content in a [vector store](/docs/concepts/vectorstores/) for later retrieval. Note that this example will gloss over some of the specifics around parsing and storing a data source - you can see more [in-depth documentation on creating retrieval systems here](/docs/how_to#qa-with-rag).\n", "\n", "Let's use a document loader to pull text from the docs:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import WebBaseLoader\n", "\n", "loader = WebBaseLoader(\"https://docs.smith.langchain.com/overview\")\n", "data = loader.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we split it into smaller chunks that the LLM's context window can handle and store it in a vector database:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=0)\n", "all_splits = text_splitter.split_documents(data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then we embed and store those chunks in a vector database:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from langchain_chroma import Chroma\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "vectorstore = Chroma.from_documents(documents=all_splits, embedding=OpenAIEmbeddings())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And finally, let's create a retriever from our initialized vectorstore:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='Skip to main content🦜️🛠️ LangSmith DocsPython DocsJS/TS DocsSearchGo to AppLangSmithOverviewTracingTesting & EvaluationOrganizationsHubLangSmith CookbookOverviewOn this pageLangSmith Overview and User GuideBuilding reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.Over the past two months, we at LangChain', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", " Document(page_content='LangSmith Overview and User Guide | 🦜️🛠️ LangSmith', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", " Document(page_content='You can also quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs.Monitoring\\u200bAfter all this, your app might finally ready to go in production. LangSmith can also be used to monitor your application in much the same way that you used for debugging. You can log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise. Each run can also be', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", " Document(page_content=\"does that affect the output?\\u200bSo you notice a bad output, and you go into LangSmith to see what's going on. You find the faulty LLM call and are now looking at the exact input. You want to try changing a word or a phrase to see what happens -- what do you do?We constantly ran into this issue. Initially, we copied the prompt to a playground of sorts. But this got annoying, so we built a playground of our own! When examining an LLM call, you can click the Open in Playground button to access this\", metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'})]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# k is the number of chunks to retrieve\n", "retriever = vectorstore.as_retriever(k=4)\n", "\n", "docs = retriever.invoke(\"Can LangSmith help test my LLM applications?\")\n", "\n", "docs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that invoking the retriever above results in some parts of the LangSmith docs that contain information about testing that our chatbot can use as context when answering questions. And now we've got a retriever that can return related data from the LangSmith docs!\n", "\n", "## Document chains\n", "\n", "Now that we have a retriever that can return <PERSON><PERSON><PERSON><PERSON> docs, let's create a chain that can use them as context to answer questions. We'll use a `create_stuff_documents_chain` helper function to \"stuff\" all of the input documents into the prompt. It will also handle formatting the docs as strings.\n", "\n", "In addition to a chat model, the function also expects a prompt that has a `context` variables, as well as a placeholder for chat history messages named `messages`. We'll create an appropriate prompt and pass it as shown below:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "\n", "SYSTEM_TEMPLATE = \"\"\"\n", "Answer the user's questions based on the below context. \n", "If the context doesn't contain any relevant information to the question, don't make something up and just say \"I don't know\":\n", "\n", "<context>\n", "{context}\n", "</context>\n", "\"\"\"\n", "\n", "question_answering_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            SYSTEM_TEMPLATE,\n", "        ),\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")\n", "\n", "document_chain = create_stuff_documents_chain(chat, question_answering_prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can invoke this `document_chain` by itself to answer questions. Let's use the docs we retrieved above and the same question, `how can langsmith help with testing?`:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Yes, LangSmith can help test and evaluate your LLM applications. It simplifies the initial setup, and you can use it to monitor your application, log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise.'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "document_chain.invoke(\n", "    {\n", "        \"context\": docs,\n", "        \"messages\": [\n", "            HumanMessage(content=\"Can LangSmith help test my LLM applications?\")\n", "        ],\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looks good! For comparison, we can try it with no context docs and compare the result:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"I don't know about LangSmith's specific capabilities for testing LLM applications. It's best to reach out to LangSmith directly to inquire about their services and how they can assist with testing your LLM applications.\""]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["document_chain.invoke(\n", "    {\n", "        \"context\": [],\n", "        \"messages\": [\n", "            HumanMessage(content=\"Can LangSmith help test my LLM applications?\")\n", "        ],\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that the LLM does not return any results.\n", "\n", "## Retrieval chains\n", "\n", "Let's combine this document chain with the retriever. Here's one way this can look:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from typing import Dict\n", "\n", "from langchain_core.runnables import RunnablePassthrough\n", "\n", "\n", "def parse_retriever_input(params: Dict):\n", "    return params[\"messages\"][-1].content\n", "\n", "\n", "retrieval_chain = RunnablePassthrough.assign(\n", "    context=parse_retriever_input | retriever,\n", ").assign(\n", "    answer=document_chain,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Given a list of input messages, we extract the content of the last message in the list and pass that to the retriever to fetch some documents. Then, we pass those documents as context to our document chain to generate a final response.\n", "\n", "Invoking this chain combines both steps outlined above:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Can LangSmith help test my LLM applications?')],\n", " 'context': [Document(page_content='Skip to main content🦜️🛠️ LangSmith DocsPython DocsJS/TS DocsSearchGo to AppLangSmithOverviewTracingTesting & EvaluationOrganizationsHubLangSmith CookbookOverviewOn this pageLangSmith Overview and User GuideBuilding reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.Over the past two months, we at LangChain', metadata={'description': 'Building reliable LLM applications can be challenging. Lang<PERSON>hain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", "  Document(page_content='LangSmith Overview and User Guide | 🦜️🛠️ LangSmith', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", "  Document(page_content='You can also quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs.Monitoring\\u200bAfter all this, your app might finally ready to go in production. LangSmith can also be used to monitor your application in much the same way that you used for debugging. You can log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise. Each run can also be', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", "  Document(page_content=\"does that affect the output?\\u200bSo you notice a bad output, and you go into LangSmith to see what's going on. You find the faulty LLM call and are now looking at the exact input. You want to try changing a word or a phrase to see what happens -- what do you do?We constantly ran into this issue. Initially, we copied the prompt to a playground of sorts. But this got annoying, so we built a playground of our own! When examining an LLM call, you can click the Open in Playground button to access this\", metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'})],\n", " 'answer': 'Yes, <PERSON><PERSON><PERSON> can help test and evaluate your LLM applications. It simplifies the initial setup, and you can use it to monitor your application, log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise.'}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["retrieval_chain.invoke(\n", "    {\n", "        \"messages\": [\n", "            HumanMessage(content=\"Can LangSmith help test my LLM applications?\")\n", "        ],\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looks good!\n", "\n", "## Query transformation\n", "\n", "Our retrieval chain is capable of answering questions about <PERSON><PERSON><PERSON>, but there's a problem - chatbots interact with users conversationally, and therefore have to deal with followup questions.\n", "\n", "The chain in its current form will struggle with this. Consider a followup question to our original question like `Tell me more!`. If we invoke our retriever with that query directly, we get documents irrelevant to LLM application testing:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='You can also quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs.Monitoring\\u200bAfter all this, your app might finally ready to go in production. LangSmith can also be used to monitor your application in much the same way that you used for debugging. You can log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise. Each run can also be', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", " Document(page_content='playground. Here, you can modify the prompt and re-run it to observe the resulting changes to the output - as many times as needed!Currently, this feature supports only OpenAI and Anthropic models and works for LLM and Chat Model calls. We plan to extend its functionality to more LLM types, chains, agents, and retrievers in the future.What is the exact sequence of events?\\u200bIn complicated chains and agents, it can often be hard to understand what is going on under the hood. What calls are being', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", " Document(page_content='however, there is still no complete substitute for human review to get the utmost quality and reliability from your application.', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", " Document(page_content='Skip to main content🦜️🛠️ LangSmith DocsPython DocsJS/TS DocsSearchGo to AppLangSmithOverviewTracingTesting & EvaluationOrganizationsHubLangSmith CookbookOverviewOn this pageLangSmith Overview and User GuideBuilding reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.Over the past two months, we at LangChain', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'})]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["retriever.invoke(\"Tell me more!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is because the retriever has no innate concept of state, and will only pull documents most similar to the query given. To solve this, we can transform the query into a standalone query without any external references an LLM.\n", "\n", "Here's an example:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='\"LangSmith LLM application testing and evaluation\"')"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import AIMessage, HumanMessage\n", "\n", "query_transform_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "        (\n", "            \"user\",\n", "            \"Given the above conversation, generate a search query to look up in order to get information relevant to the conversation. Only respond with the query, nothing else.\",\n", "        ),\n", "    ]\n", ")\n", "\n", "query_transformation_chain = query_transform_prompt | chat\n", "\n", "query_transformation_chain.invoke(\n", "    {\n", "        \"messages\": [\n", "            HumanMessage(content=\"Can LangSmith help test my LLM applications?\"),\n", "            AIMessage(\n", "                content=\"Yes, <PERSON><PERSON>mith can help test and evaluate your LLM applications. It allows you to quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs. Additionally, LangSmith can be used to monitor your application, log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise.\"\n", "            ),\n", "            HumanMessage(content=\"Tell me more!\"),\n", "        ],\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Awesome! That transformed query would pull up context documents related to LLM application testing.\n", "\n", "Let's add this to our retrieval chain. We can wrap our retriever as follows:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnableBranch\n", "\n", "query_transforming_retriever_chain = RunnableBranch(\n", "    (\n", "        lambda x: len(x.get(\"messages\", [])) == 1,\n", "        # If only one message, then we just pass that message's content to retriever\n", "        (lambda x: x[\"messages\"][-1].content) | retriever,\n", "    ),\n", "    # If messages, then we pass inputs to LLM chain to transform the query, then pass to retriever\n", "    query_transform_prompt | chat | StrOutputParser() | retriever,\n", ").with_config(run_name=\"chat_retriever_chain\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then, we can use this query transformation chain to make our retrieval chain better able to handle such followup questions:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["SYSTEM_TEMPLATE = \"\"\"\n", "Answer the user's questions based on the below context. \n", "If the context doesn't contain any relevant information to the question, don't make something up and just say \"I don't know\":\n", "\n", "<context>\n", "{context}\n", "</context>\n", "\"\"\"\n", "\n", "question_answering_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            SYSTEM_TEMPLATE,\n", "        ),\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")\n", "\n", "document_chain = create_stuff_documents_chain(chat, question_answering_prompt)\n", "\n", "conversational_retrieval_chain = RunnablePassthrough.assign(\n", "    context=query_transforming_retriever_chain,\n", ").assign(\n", "    answer=document_chain,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Awesome! Let's invoke this new chain with the same inputs as earlier:"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Can LangSmith help test my LLM applications?')],\n", " 'context': [Document(page_content='Skip to main content🦜️🛠️ LangSmith DocsPython DocsJS/TS DocsSearchGo to AppLangSmithOverviewTracingTesting & EvaluationOrganizationsHubLangSmith CookbookOverviewOn this pageLangSmith Overview and User GuideBuilding reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.Over the past two months, we at LangChain', metadata={'description': 'Building reliable LLM applications can be challenging. Lang<PERSON>hain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", "  Document(page_content='LangSmith Overview and User Guide | 🦜️🛠️ LangSmith', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", "  Document(page_content='You can also quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs.Monitoring\\u200bAfter all this, your app might finally ready to go in production. LangSmith can also be used to monitor your application in much the same way that you used for debugging. You can log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise. Each run can also be', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", "  Document(page_content=\"does that affect the output?\\u200bSo you notice a bad output, and you go into LangSmith to see what's going on. You find the faulty LLM call and are now looking at the exact input. You want to try changing a word or a phrase to see what happens -- what do you do?We constantly ran into this issue. Initially, we copied the prompt to a playground of sorts. But this got annoying, so we built a playground of our own! When examining an LLM call, you can click the Open in Playground button to access this\", metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'})],\n", " 'answer': 'Yes, <PERSON><PERSON><PERSON> can help test and evaluate LLM (Language Model) applications. It simplifies the initial setup, and you can use it to monitor your application, log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise.'}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["conversational_retrieval_chain.invoke(\n", "    {\n", "        \"messages\": [\n", "            HumanMessage(content=\"Can LangSmith help test my LLM applications?\"),\n", "        ]\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Can LangSmith help test my LLM applications?'),\n", "  AIMessage(content='Yes, <PERSON><PERSON>mith can help test and evaluate your LLM applications. It allows you to quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs. Additionally, LangSmith can be used to monitor your application, log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise.'),\n", "  HumanMessage(content='Tell me more!')],\n", " 'context': [Document(page_content='LangSmith Overview and User Guide | 🦜️🛠️ LangSmith', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", "  Document(page_content='You can also quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs.Monitoring\\u200bAfter all this, your app might finally ready to go in production. LangSmith can also be used to monitor your application in much the same way that you used for debugging. You can log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise. Each run can also be', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", "  Document(page_content='Skip to main content🦜️🛠️ LangSmith DocsPython DocsJS/TS DocsSearchGo to AppLangSmithOverviewTracingTesting & EvaluationOrganizationsHubLangSmith CookbookOverviewOn this pageLangSmith Overview and User GuideBuilding reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.Over the past two months, we at LangChain', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}),\n", "  Document(page_content='<PERSON><PERSON><PERSON> makes it easy to manually review and annotate runs through annotation queues.These queues allow you to select any runs based on criteria like model type or automatic evaluation scores, and queue them up for human review. As a reviewer, you can then quickly step through the runs, viewing the input, output, and any existing tags before adding your own feedback.We often use this for a couple of reasons:To assess subjective qualities that automatic evaluators struggle with, like', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'})],\n", " 'answer': 'LangSmith simplifies the initial setup for building reliable LLM applications, but it acknowledges that there is still work needed to bring the performance of prompts, chains, and agents up to the level where they are reliable enough to be used in production. It also provides the capability to manually review and annotate runs through annotation queues, allowing you to select runs based on criteria like model type or automatic evaluation scores for human review. This feature is particularly useful for assessing subjective qualities that automatic evaluators struggle with.'}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["conversational_retrieval_chain.invoke(\n", "    {\n", "        \"messages\": [\n", "            HumanMessage(content=\"Can LangSmith help test my LLM applications?\"),\n", "            AIMessage(\n", "                content=\"Yes, <PERSON><PERSON>mith can help test and evaluate your LLM applications. It allows you to quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs. Additionally, LangSmith can be used to monitor your application, log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise.\"\n", "            ),\n", "            HumanMessage(content=\"Tell me more!\"),\n", "        ],\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can check out [this <PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/bb329a3b-e92a-4063-ad78-43f720fbb5a2/r) to see the internal query transformation step for yourself.\n", "\n", "## Streaming\n", "\n", "Because this chain is constructed with LCEL, you can use familiar methods like `.stream()` with it:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'messages': [HumanMessage(content='Can <PERSON><PERSON><PERSON> help test my LLM applications?'), AIMessage(content='Yes, <PERSON><PERSON><PERSON> can help test and evaluate your LLM applications. It allows you to quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs. Additionally, LangSmith can be used to monitor your application, log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise.'), HumanMessage(content='Tell me more!')]}\n", "{'context': [Document(page_content='LangSmith Overview and User Guide | 🦜️🛠️ LangSmith', metadata={'description': 'Building reliable LLM applications can be challenging. <PERSON><PERSON>hain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}), Document(page_content='You can also quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs.Monitoring\\u200bAfter all this, your app might finally ready to go in production. LangSmith can also be used to monitor your application in much the same way that you used for debugging. You can log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise. Each run can also be', metadata={'description': 'Building reliable LLM applications can be challenging. Lang<PERSON>hain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}), Document(page_content='Skip to main content🦜️🛠️ LangSmith DocsPython DocsJS/TS DocsSearchGo to AppLangSmithOverviewTracingTesting & EvaluationOrganizationsHubLangSmith CookbookOverviewOn this pageLangSmith Overview and User GuideBuilding reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.Over the past two months, we at LangChain', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'}), Document(page_content='LangSmith makes it easy to manually review and annotate runs through annotation queues.These queues allow you to select any runs based on criteria like model type or automatic evaluation scores, and queue them up for human review. As a reviewer, you can then quickly step through the runs, viewing the input, output, and any existing tags before adding your own feedback.We often use this for a couple of reasons:To assess subjective qualities that automatic evaluators struggle with, like', metadata={'description': 'Building reliable LLM applications can be challenging. LangChain simplifies the initial setup, but there is still work needed to bring the performance of prompts, chains and agents up the level where they are reliable enough to be used in production.', 'language': 'en', 'source': 'https://docs.smith.langchain.com/overview', 'title': 'LangSmith Overview and User Guide | 🦜️🛠️ LangSmith'})]}\n", "{'answer': ''}\n", "{'answer': '<PERSON>'}\n", "{'answer': '<PERSON>'}\n", "{'answer': ' simpl'}\n", "{'answer': 'ifies'}\n", "{'answer': ' the'}\n", "{'answer': ' initial'}\n", "{'answer': ' setup'}\n", "{'answer': ' for'}\n", "{'answer': ' building'}\n", "{'answer': ' reliable'}\n", "{'answer': ' L'}\n", "{'answer': 'LM'}\n", "{'answer': ' applications'}\n", "{'answer': '.'}\n", "{'answer': ' It'}\n", "{'answer': ' provides'}\n", "{'answer': ' features'}\n", "{'answer': ' for'}\n", "{'answer': ' manually'}\n", "{'answer': ' reviewing'}\n", "{'answer': ' and'}\n", "{'answer': ' annot'}\n", "{'answer': 'ating'}\n", "{'answer': ' runs'}\n", "{'answer': ' through'}\n", "{'answer': ' annotation'}\n", "{'answer': ' queues'}\n", "{'answer': ','}\n", "{'answer': ' allowing'}\n", "{'answer': ' you'}\n", "{'answer': ' to'}\n", "{'answer': ' select'}\n", "{'answer': ' runs'}\n", "{'answer': ' based'}\n", "{'answer': ' on'}\n", "{'answer': ' criteria'}\n", "{'answer': ' like'}\n", "{'answer': ' model'}\n", "{'answer': ' type'}\n", "{'answer': ' or'}\n", "{'answer': ' automatic'}\n", "{'answer': ' evaluation'}\n", "{'answer': ' scores'}\n", "{'answer': ','}\n", "{'answer': ' and'}\n", "{'answer': ' queue'}\n", "{'answer': ' them'}\n", "{'answer': ' up'}\n", "{'answer': ' for'}\n", "{'answer': ' human'}\n", "{'answer': ' review'}\n", "{'answer': '.'}\n", "{'answer': ' As'}\n", "{'answer': ' a'}\n", "{'answer': ' reviewer'}\n", "{'answer': ','}\n", "{'answer': ' you'}\n", "{'answer': ' can'}\n", "{'answer': ' quickly'}\n", "{'answer': ' step'}\n", "{'answer': ' through'}\n", "{'answer': ' the'}\n", "{'answer': ' runs'}\n", "{'answer': ','}\n", "{'answer': ' view'}\n", "{'answer': ' the'}\n", "{'answer': ' input'}\n", "{'answer': ','}\n", "{'answer': ' output'}\n", "{'answer': ','}\n", "{'answer': ' and'}\n", "{'answer': ' any'}\n", "{'answer': ' existing'}\n", "{'answer': ' tags'}\n", "{'answer': ' before'}\n", "{'answer': ' adding'}\n", "{'answer': ' your'}\n", "{'answer': ' own'}\n", "{'answer': ' feedback'}\n", "{'answer': '.'}\n", "{'answer': ' This'}\n", "{'answer': ' can'}\n", "{'answer': ' be'}\n", "{'answer': ' particularly'}\n", "{'answer': ' useful'}\n", "{'answer': ' for'}\n", "{'answer': ' assessing'}\n", "{'answer': ' subjective'}\n", "{'answer': ' qualities'}\n", "{'answer': ' that'}\n", "{'answer': ' automatic'}\n", "{'answer': ' evalu'}\n", "{'answer': 'ators'}\n", "{'answer': ' struggle'}\n", "{'answer': ' with'}\n", "{'answer': '.'}\n", "{'answer': ''}\n"]}], "source": ["stream = conversational_retrieval_chain.stream(\n", "    {\n", "        \"messages\": [\n", "            HumanMessage(content=\"Can LangSmith help test my LLM applications?\"),\n", "            AIMessage(\n", "                content=\"Yes, <PERSON><PERSON>mith can help test and evaluate your LLM applications. It allows you to quickly edit examples and add them to datasets to expand the surface area of your evaluation sets or to fine-tune a model for improved quality or reduced costs. Additionally, LangSmith can be used to monitor your application, log all traces, visualize latency and token usage statistics, and troubleshoot specific issues as they arise.\"\n", "            ),\n", "            HumanMessage(content=\"Tell me more!\"),\n", "        ],\n", "    }\n", ")\n", "\n", "for chunk in stream:\n", "    print(chunk)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Further reading\n", "\n", "This guide only scratches the surface of retrieval techniques. For more on different ways of ingesting, preparing, and retrieving the most relevant data, check out the relevant how-to guides [here](/docs/how_to#document-loaders)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.1"}}, "nbformat": 4, "nbformat_minor": 2}