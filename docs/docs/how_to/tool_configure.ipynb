{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to access the RunnableConfig from a tool\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [<PERSON><PERSON><PERSON><PERSON>](/docs/concepts/tools)\n", "- [Custom tools](/docs/how_to/custom_tools)\n", "- [LangChain Expression Language (LCEL)](/docs/concepts/lcel)\n", "- [Configuring runnable behavior](/docs/how_to/configure/)\n", "\n", ":::\n", "\n", "If you have a [tool](/docs/concepts/tools/) that calls [chat models](/docs/concepts/chat_models/), [retrievers](/docs/concepts/retrievers/), or other [runnables](/docs/concepts/runnables/), you may want to access internal events from those runnables or configure them with additional properties. This guide shows you how to manually pass parameters properly so that you can do this using the `astream_events()` method.\n", "\n", "Tools are [runnables](/docs/concepts/runnables/), and you can treat them the same way as any other runnable at the interface level - you can call `invoke()`, `batch()`, and `stream()` on them as normal. However, when writing custom tools, you may want to invoke other runnables like chat models or retrievers. In order to properly trace and configure those sub-invocations, you'll need to manually access and pass in the tool's current [`RunnableConfig`](https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.config.RunnableConfig.html) object. This guide show you some examples of how to do that.\n", "\n", ":::caution Compatibility\n", "\n", "This guide requires `langchain-core>=0.2.16`.\n", "\n", ":::\n", "\n", "## Inferring by parameter type\n", "\n", "To access reference the active config object from your custom tool, you'll need to add a parameter to your tool's signature typed as `RunnableConfig`. When you invoke your tool, <PERSON><PERSON><PERSON><PERSON> will inspect your tool's signature, look for a parameter typed as `RunnableConfig`, and if it exists, populate that parameter with the correct value.\n", "\n", "**Note:** The actual name of the parameter doesn't matter, only the typing.\n", "\n", "To illustrate this, define a custom tool that takes a two parameters - one typed as a string, the other typed as `RunnableConfig`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -qU langchain_core"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnableConfig\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "async def reverse_tool(text: str, special_config_param: RunnableConfig) -> str:\n", "    \"\"\"A test tool that combines input text with a configurable parameter.\"\"\"\n", "    return (text + special_config_param[\"configurable\"][\"additional_field\"])[::-1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then, if we invoke the tool with a `config` containing a `configurable` field, we can see that `additional_field` is passed through correctly:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["'321cba'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["await reverse_tool.ainvoke(\n", "    {\"text\": \"abc\"}, config={\"configurable\": {\"additional_field\": \"123\"}}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now seen how to configure and stream events from within a tool. Next, check out the following guides for more on using tools:\n", "\n", "- [Stream events from child runs within a custom tool](/docs/how_to/tool_stream_events/)\n", "- Pass [tool results back to a model](/docs/how_to/tool_results_pass_to_model)\n", "\n", "You can also check out some more specific uses of tool calling:\n", "\n", "- Building [tool-using chains and agents](/docs/how_to#tools)\n", "- Getting [structured outputs](/docs/how_to/structured_output/) from models"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}