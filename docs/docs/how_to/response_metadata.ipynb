{"cells": [{"cell_type": "markdown", "id": "6bd1219b-f31c-41b0-95e6-3204ad894ac7", "metadata": {}, "source": ["# Response metadata\n", "\n", "Many model providers include some metadata in their chat generation [responses](/docs/concepts/messages/#aimessage). This metadata can be accessed via the `AIMessage.response_metadata: Dict` attribute. Depending on the model provider and model configuration, this can contain information like [token counts](/docs/how_to/chat_token_usage_tracking), [logprobs](/docs/how_to/logprobs), and more.\n", "\n", "Here's what the response metadata looks like for a few different providers:\n", "\n", "## OpenAI"]}, {"cell_type": "code", "execution_count": 1, "id": "161f5898-9976-4a75-943d-03eda1a40a60", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'token_usage': {'completion_tokens': 88,\n", "  'prompt_tokens': 16,\n", "  'total_tokens': 104,\n", "  'completion_tokens_details': {'accepted_prediction_tokens': 0,\n", "   'audio_tokens': 0,\n", "   'reasoning_tokens': 0,\n", "   'rejected_prediction_tokens': 0},\n", "  'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}},\n", " 'model_name': 'gpt-4o-mini-2024-07-18',\n", " 'system_fingerprint': 'fp_34a54ae93c',\n", " 'id': 'chatcmpl-ByN1Qkvqb5fAGKKzXXxZ3rBlnqkWs',\n", " 'service_tier': 'default',\n", " 'finish_reason': 'stop',\n", " 'logprobs': None}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")\n", "msg = llm.invoke(\"What's the oldest known example of cuneiform\")\n", "msg.response_metadata"]}, {"cell_type": "markdown", "id": "98eab683-df03-44a1-a034-ebbe7c6851b6", "metadata": {}, "source": ["## Anthropic"]}, {"cell_type": "code", "execution_count": 2, "id": "61c43496-83b5-4d71-bd60-3e6d46c62a5e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'id': 'msg_01NTWnqvbNKSjGfqQL7xikau',\n", " 'model': 'claude-3-7-sonnet-20250219',\n", " 'stop_reason': 'end_turn',\n", " 'stop_sequence': None,\n", " 'usage': {'cache_creation_input_tokens': 0,\n", "  'cache_read_input_tokens': 0,\n", "  'input_tokens': 17,\n", "  'output_tokens': 197,\n", "  'server_tool_use': None,\n", "  'service_tier': 'standard'},\n", " 'model_name': 'claude-3-7-sonnet-20250219'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_anthropic import ChatAnthropic\n", "\n", "llm = ChatAnthropic(model=\"claude-3-7-sonnet-20250219\")\n", "msg = llm.invoke(\"What's the oldest known example of cuneiform\")\n", "msg.response_metadata"]}, {"cell_type": "markdown", "id": "c1f24f69-18f6-43c1-8b26-3f88ec515259", "metadata": {}, "source": ["## Google Generative AI"]}, {"cell_type": "code", "execution_count": null, "id": "39549336-25f5-4839-9846-f687cd77e59b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'prompt_feedback': {'block_reason': 0, 'safety_ratings': []},\n", " 'finish_reason': 'STOP',\n", " 'model_name': 'gemini-2.5-flash',\n", " 'safety_ratings': []}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_google_genai import ChatGoogleGenerativeAI\n", "\n", "llm = ChatGoogleGenerativeAI(model=\"gemini-2.5-flash\")\n", "msg = llm.invoke(\"What's the oldest known example of cuneiform\")\n", "msg.response_metadata"]}, {"cell_type": "markdown", "id": "bc4ef8bb-eee3-4266-b530-0af9b3b79fe9", "metadata": {}, "source": ["## <PERSON><PERSON> (Anthropic)"]}, {"cell_type": "code", "execution_count": 4, "id": "1e4ac668-4c6a-48ad-9a6f-7b291477b45d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ResponseMetadata': {'RequestId': 'ea0ac2ad-3ad5-4a49-9647-274a0c73ac31',\n", "  'HTTPStatusCode': 200,\n", "  'HTTPHeaders': {'date': 'Sat, 22 Mar 2025 11:27:46 GMT',\n", "   'content-type': 'application/json',\n", "   'content-length': '1660',\n", "   'connection': 'keep-alive',\n", "   'x-amzn-requestid': 'ea0ac2ad-3ad5-4a49-9647-274a0c73ac31'},\n", "  'RetryAttempts': 0},\n", " 'stopReason': 'end_turn',\n", " 'metrics': {'latencyMs': [11044]}}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_aws import ChatBedrockConverse\n", "\n", "llm = ChatBedrockConverse(model=\"anthropic.claude-3-7-sonnet-20250219-v1:0\")\n", "msg = llm.invoke(\"What's the oldest known example of cuneiform\")\n", "msg.response_metadata"]}, {"cell_type": "markdown", "id": "ee040d15-5575-4309-a9e9-aed5a09c78e3", "metadata": {}, "source": ["## MistralAI"]}, {"cell_type": "code", "execution_count": 5, "id": "deb41321-52d0-4795-a40c-4a811a13d7b0", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'token_usage': {'prompt_tokens': 13,\n", "  'total_tokens': 306,\n", "  'completion_tokens': 293},\n", " 'model_name': 'mistral-small-latest',\n", " 'model': 'mistral-small-latest',\n", " 'finish_reason': 'stop'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_mistralai import ChatMistralAI\n", "\n", "llm = ChatMistralAI(model=\"mistral-small-latest\")\n", "msg = llm.invoke([(\"human\", \"What's the oldest known example of cuneiform\")])\n", "msg.response_metadata"]}, {"cell_type": "markdown", "id": "297c7be4-9505-48ac-96c0-4dc2047cfe7f", "metadata": {}, "source": ["## Groq"]}, {"cell_type": "code", "execution_count": 6, "id": "744e14ec-ff50-4642-9893-ff7bdf8927ff", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'token_usage': {'completion_tokens': 184,\n", "  'prompt_tokens': 45,\n", "  'total_tokens': 229,\n", "  'completion_time': 0.245333333,\n", "  'prompt_time': 0.002262803,\n", "  'queue_time': 0.19315161,\n", "  'total_time': 0.247596136},\n", " 'model_name': 'llama-3.1-8b-instant',\n", " 'system_fingerprint': 'fp_a56f6eea01',\n", " 'finish_reason': 'stop',\n", " 'logprobs': None}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_groq import ChatGroq\n", "\n", "llm = ChatGroq(model=\"llama-3.1-8b-instant\")\n", "msg = llm.invoke(\"What's the oldest known example of cuneiform\")\n", "msg.response_metadata"]}, {"cell_type": "markdown", "id": "3d5e0614-8dc2-4948-a0b5-dc76c7837a5a", "metadata": {}, "source": ["## FireworksAI"]}, {"cell_type": "code", "execution_count": 7, "id": "6ae32a93-26db-41bb-95c2-38ddd5085fbe", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'token_usage': {'prompt_tokens': 25,\n", "  'total_tokens': 352,\n", "  'completion_tokens': 327},\n", " 'model_name': 'accounts/fireworks/models/llama-v3p1-70b-instruct',\n", " 'system_fingerprint': '',\n", " 'finish_reason': 'stop',\n", " 'logprobs': None}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_fireworks import ChatFireworks\n", "\n", "llm = ChatFireworks(model=\"accounts/fireworks/models/llama-v3p1-70b-instruct\")\n", "msg = llm.invoke(\"What's the oldest known example of cuneiform\")\n", "msg.response_metadata"]}], "metadata": {"kernelspec": {"display_name": "langchain", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}