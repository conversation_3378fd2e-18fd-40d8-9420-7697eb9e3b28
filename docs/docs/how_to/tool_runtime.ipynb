{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to pass run time values to tools\n", "\n", "import Prerequisites from \"@theme/Prerequisites\";\n", "import Compatibility from \"@theme/Compatibility\";\n", "\n", "<Prerequisites titlesAndLinks={[\n", "  [\"Chat models\", \"/docs/concepts/chat_models\"],\n", "  [\"<PERSON><PERSON><PERSON><PERSON>\", \"/docs/concepts/tools\"],\n", "  [\"How to create tools\", \"/docs/how_to/custom_tools\"],\n", "  [\"How to use a model to call tools\", \"/docs/how_to/tool_calling\"],\n", "]} />\n", "\n", "\n", "<Compatibility packagesAndVersions={[\n", "  [\"langchain-core\", \"0.2.21\"],\n", "]} />\n", "\n", "You may need to bind values to a [tool](/docs/concepts/tools/) that are only known at runtime. For example, the tool logic may require using the ID of the user who made the request.\n", "\n", "Most of the time, such values should not be controlled by the LLM. In fact, allowing the LLM to control the user ID may lead to a security risk.\n", "\n", "Instead, the LLM should only control the parameters of the tool that are meant to be controlled by the LLM, while other parameters (such as user ID) should be fixed by the application logic.\n", "\n", "This how-to guide shows you how to prevent the model from generating certain tool arguments and injecting them in directly at runtime.\n", "\n", ":::info Using with LangGraph\n", "\n", "If you're using LangGraph, please refer to [this how-to guide](https://langchain-ai.github.io/langgraph/how-tos/pass-run-time-values-to-tools/)\n", "which shows how to create an agent that keeps track of a given user's favorite pets.\n", ":::"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can bind them to chat models as follows:\n", "\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs\n", "  customVarName=\"llm\"\n", "  overrideParams={{fireworks: {model: \"accounts/fireworks/models/firefunction-v1\", kwargs: \"temperature=0\"}}}\n", "/>\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:51.802901Z", "iopub.status.busy": "2024-09-11T02:52:51.802682Z", "iopub.status.idle": "2024-09-11T02:52:52.398167Z", "shell.execute_reply": "2024-09-11T02:52:52.397911Z"}}, "outputs": [], "source": ["# | output: false\n", "# | echo: false\n", "\n", "import os\n", "from getpass import getpass\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "if \"OPENAI_API_KEY\" not in os.environ:\n", "    os.environ[\"OPENAI_API_KEY\"] = getpass()\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hiding arguments from the model\n", "\n", "We can use the InjectedToolArg annotation to mark certain parameters of our Tool, like `user_id` as being injected at runtime, meaning they shouldn't be generated by the model"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:52.399922Z", "iopub.status.busy": "2024-09-11T02:52:52.399796Z", "iopub.status.idle": "2024-09-11T02:52:52.406349Z", "shell.execute_reply": "2024-09-11T02:52:52.406077Z"}}, "outputs": [], "source": ["from typing import List\n", "\n", "from langchain_core.tools import InjectedToolArg, tool\n", "from typing_extensions import Annotated\n", "\n", "user_to_pets = {}\n", "\n", "\n", "@tool(parse_docstring=True)\n", "def update_favorite_pets(\n", "    pets: List[str], user_id: Annotated[str, InjectedToolArg]\n", ") -> None:\n", "    \"\"\"Add the list of favorite pets.\n", "\n", "    Args:\n", "        pets: List of favorite pets to set.\n", "        user_id: User's ID.\n", "    \"\"\"\n", "    user_to_pets[user_id] = pets\n", "\n", "\n", "@tool(parse_docstring=True)\n", "def delete_favorite_pets(user_id: Annotated[str, InjectedToolArg]) -> None:\n", "    \"\"\"Delete the list of favorite pets.\n", "\n", "    Args:\n", "        user_id: User's ID.\n", "    \"\"\"\n", "    if user_id in user_to_pets:\n", "        del user_to_pets[user_id]\n", "\n", "\n", "@tool(parse_docstring=True)\n", "def list_favorite_pets(user_id: Annotated[str, InjectedToolArg]) -> None:\n", "    \"\"\"List favorite pets if any.\n", "\n", "    Args:\n", "        user_id: User's ID.\n", "    \"\"\"\n", "    return user_to_pets.get(user_id, [])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If we look at the input schemas for these tools, we'll see that user_id is still listed:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:52.407763Z", "iopub.status.busy": "2024-09-11T02:52:52.407691Z", "iopub.status.idle": "2024-09-11T02:52:52.411761Z", "shell.execute_reply": "2024-09-11T02:52:52.411512Z"}}, "outputs": [{"data": {"text/plain": ["{'description': 'Add the list of favorite pets.',\n", " 'properties': {'pets': {'description': 'List of favorite pets to set.',\n", "   'items': {'type': 'string'},\n", "   'title': 'Pet<PERSON>',\n", "   'type': 'array'},\n", "  'user_id': {'description': \"User's ID.\",\n", "   'title': 'User Id',\n", "   'type': 'string'}},\n", " 'required': ['pets', 'user_id'],\n", " 'title': 'update_favorite_petsSchema',\n", " 'type': 'object'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["update_favorite_pets.get_input_schema().model_json_schema()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["But if we look at the tool call schema, which is what is passed to the model for tool-calling, user_id has been removed:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:52.427826Z", "iopub.status.busy": "2024-09-11T02:52:52.427691Z", "iopub.status.idle": "2024-09-11T02:52:52.431791Z", "shell.execute_reply": "2024-09-11T02:52:52.431574Z"}}, "outputs": [{"data": {"text/plain": ["{'description': 'Add the list of favorite pets.',\n", " 'properties': {'pets': {'description': 'List of favorite pets to set.',\n", "   'items': {'type': 'string'},\n", "   'title': 'Pet<PERSON>',\n", "   'type': 'array'}},\n", " 'required': ['pets'],\n", " 'title': 'update_favorite_pets',\n", " 'type': 'object'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["update_favorite_pets.tool_call_schema.model_json_schema()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So when we invoke our tool, we need to pass in user_id:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:52.433096Z", "iopub.status.busy": "2024-09-11T02:52:52.433014Z", "iopub.status.idle": "2024-09-11T02:52:52.437499Z", "shell.execute_reply": "2024-09-11T02:52:52.437239Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'123': ['lizard', 'dog']}\n", "['lizard', 'dog']\n"]}], "source": ["user_id = \"123\"\n", "update_favorite_pets.invoke({\"pets\": [\"lizard\", \"dog\"], \"user_id\": user_id})\n", "print(user_to_pets)\n", "print(list_favorite_pets.invoke({\"user_id\": user_id}))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["But when the model calls the tool, no user_id argument will be generated:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:52.439148Z", "iopub.status.busy": "2024-09-11T02:52:52.438742Z", "iopub.status.idle": "2024-09-11T02:52:53.394524Z", "shell.execute_reply": "2024-09-11T02:52:53.394005Z"}}, "outputs": [{"data": {"text/plain": ["[{'name': 'update_favorite_pets',\n", "  'args': {'pets': ['cats', 'parrots']},\n", "  'id': 'call_pZ6XVREGh1L0BBSsiGIf1xVm',\n", "  'type': 'tool_call'}]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["tools = [\n", "    update_favorite_pets,\n", "    delete_favorite_pets,\n", "    list_favorite_pets,\n", "]\n", "llm_with_tools = llm.bind_tools(tools)\n", "ai_msg = llm_with_tools.invoke(\"my favorite animals are cats and parrots\")\n", "ai_msg.tool_calls"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Injecting arguments at runtime"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If we want to actually execute our tools using the model-generated tool call, we'll need to inject the user_id ourselves:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:53.397134Z", "iopub.status.busy": "2024-09-11T02:52:53.396972Z", "iopub.status.idle": "2024-09-11T02:52:53.403332Z", "shell.execute_reply": "2024-09-11T02:52:53.402787Z"}}, "outputs": [{"data": {"text/plain": ["[{'name': 'update_favorite_pets',\n", "  'args': {'pets': ['cats', 'parrots'], 'user_id': '123'},\n", "  'id': 'call_pZ6XVREGh1L0BBSsiGIf1xVm',\n", "  'type': 'tool_call'}]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from copy import deepcopy\n", "\n", "from langchain_core.runnables import chain\n", "\n", "\n", "@chain\n", "def inject_user_id(ai_msg):\n", "    tool_calls = []\n", "    for tool_call in ai_msg.tool_calls:\n", "        tool_call_copy = deepcopy(tool_call)\n", "        tool_call_copy[\"args\"][\"user_id\"] = user_id\n", "        tool_calls.append(tool_call_copy)\n", "    return tool_calls\n", "\n", "\n", "inject_user_id.invoke(ai_msg)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And now we can chain together our model, injection code, and the actual tools to create a tool-executing chain:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:53.405183Z", "iopub.status.busy": "2024-09-11T02:52:53.405048Z", "iopub.status.idle": "2024-09-11T02:52:54.248576Z", "shell.execute_reply": "2024-09-11T02:52:54.248107Z"}}, "outputs": [{"data": {"text/plain": ["[ToolMessage(content='null', name='update_favorite_pets', tool_call_id='call_oYCD0THSedHTbwNAY3NW6uUj')]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["tool_map = {tool.name: tool for tool in tools}\n", "\n", "\n", "@chain\n", "def tool_router(tool_call):\n", "    return tool_map[tool_call[\"name\"]]\n", "\n", "\n", "chain = llm_with_tools | inject_user_id | tool_router.map()\n", "chain.invoke(\"my favorite animals are cats and parrots\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looking at the user_to_pets dict, we can see that it's been updated to include cats and parrots:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:54.251169Z", "iopub.status.busy": "2024-09-11T02:52:54.250948Z", "iopub.status.idle": "2024-09-11T02:52:54.254279Z", "shell.execute_reply": "2024-09-11T02:52:54.253889Z"}}, "outputs": [{"data": {"text/plain": ["{'123': ['cats', 'parrots']}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["user_to_pets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Other ways of annotating args\n", "\n", "Here are a few other ways of annotating our tool args:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:54.256425Z", "iopub.status.busy": "2024-09-11T02:52:54.256279Z", "iopub.status.idle": "2024-09-11T02:52:54.262533Z", "shell.execute_reply": "2024-09-11T02:52:54.262228Z"}}, "outputs": [{"data": {"text/plain": ["{'description': 'Update list of favorite pets',\n", " 'properties': {'pets': {'description': 'List of favorite pets to set.',\n", "   'items': {'type': 'string'},\n", "   'title': 'Pet<PERSON>',\n", "   'type': 'array'},\n", "  'user_id': {'description': \"User's ID.\",\n", "   'title': 'User Id',\n", "   'type': 'string'}},\n", " 'required': ['pets', 'user_id'],\n", " 'title': 'UpdateFavoritePetsSchema',\n", " 'type': 'object'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.tools import BaseTool\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class UpdateFavoritePetsSchema(BaseModel):\n", "    \"\"\"Update list of favorite pets\"\"\"\n", "\n", "    pets: List[str] = Field(..., description=\"List of favorite pets to set.\")\n", "    user_id: Annotated[str, InjectedToolArg] = Field(..., description=\"User's ID.\")\n", "\n", "\n", "@tool(args_schema=UpdateFavoritePetsSchema)\n", "def update_favorite_pets(pets, user_id):\n", "    user_to_pets[user_id] = pets\n", "\n", "\n", "update_favorite_pets.get_input_schema().model_json_schema()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:54.264192Z", "iopub.status.busy": "2024-09-11T02:52:54.264074Z", "iopub.status.idle": "2024-09-11T02:52:54.267400Z", "shell.execute_reply": "2024-09-11T02:52:54.267113Z"}}, "outputs": [{"data": {"text/plain": ["{'description': 'Update list of favorite pets',\n", " 'properties': {'pets': {'description': 'List of favorite pets to set.',\n", "   'items': {'type': 'string'},\n", "   'title': 'Pet<PERSON>',\n", "   'type': 'array'}},\n", " 'required': ['pets'],\n", " 'title': 'update_favorite_pets',\n", " 'type': 'object'}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["update_favorite_pets.tool_call_schema.model_json_schema()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:54.269027Z", "iopub.status.busy": "2024-09-11T02:52:54.268905Z", "iopub.status.idle": "2024-09-11T02:52:54.276123Z", "shell.execute_reply": "2024-09-11T02:52:54.275876Z"}}, "outputs": [{"data": {"text/plain": ["{'description': 'Update list of favorite pets',\n", " 'properties': {'pets': {'description': 'List of favorite pets to set.',\n", "   'items': {'type': 'string'},\n", "   'title': 'Pet<PERSON>',\n", "   'type': 'array'},\n", "  'user_id': {'description': \"User's ID.\",\n", "   'title': 'User Id',\n", "   'type': 'string'}},\n", " 'required': ['pets', 'user_id'],\n", " 'title': 'UpdateFavoritePetsSchema',\n", " 'type': 'object'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import Optional, Type\n", "\n", "\n", "class UpdateFavoritePets(BaseTool):\n", "    name: str = \"update_favorite_pets\"\n", "    description: str = \"Update list of favorite pets\"\n", "    args_schema: Optional[Type[BaseModel]] = UpdateFavoritePetsSchema\n", "\n", "    def _run(self, pets, user_id):\n", "        user_to_pets[user_id] = pets\n", "\n", "\n", "UpdateFavoritePets().get_input_schema().model_json_schema()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:54.277497Z", "iopub.status.busy": "2024-09-11T02:52:54.277400Z", "iopub.status.idle": "2024-09-11T02:52:54.280323Z", "shell.execute_reply": "2024-09-11T02:52:54.280072Z"}}, "outputs": [{"data": {"text/plain": ["{'description': 'Update list of favorite pets',\n", " 'properties': {'pets': {'description': 'List of favorite pets to set.',\n", "   'items': {'type': 'string'},\n", "   'title': 'Pet<PERSON>',\n", "   'type': 'array'}},\n", " 'required': ['pets'],\n", " 'title': 'update_favorite_pets',\n", " 'type': 'object'}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["UpdateFavoritePets().tool_call_schema.model_json_schema()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:54.281741Z", "iopub.status.busy": "2024-09-11T02:52:54.281642Z", "iopub.status.idle": "2024-09-11T02:52:54.288857Z", "shell.execute_reply": "2024-09-11T02:52:54.288632Z"}}, "outputs": [{"data": {"text/plain": ["{'description': 'Use the tool.\\n\\nAdd run_manager: Optional[CallbackManagerForToolRun] = None\\nto child implementations to enable tracing.',\n", " 'properties': {'pets': {'items': {'type': 'string'},\n", "   'title': 'Pet<PERSON>',\n", "   'type': 'array'},\n", "  'user_id': {'title': 'User Id', 'type': 'string'}},\n", " 'required': ['pets', 'user_id'],\n", " 'title': 'update_favorite_petsSchema',\n", " 'type': 'object'}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["class UpdateFavoritePets2(BaseTool):\n", "    name: str = \"update_favorite_pets\"\n", "    description: str = \"Update list of favorite pets\"\n", "\n", "    def _run(self, pets: List[str], user_id: Annotated[str, InjectedToolArg]) -> None:\n", "        user_to_pets[user_id] = pets\n", "\n", "\n", "UpdateFavoritePets2().get_input_schema().model_json_schema()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2024-09-11T02:52:54.290237Z", "iopub.status.busy": "2024-09-11T02:52:54.290145Z", "iopub.status.idle": "2024-09-11T02:52:54.294273Z", "shell.execute_reply": "2024-09-11T02:52:54.294053Z"}}, "outputs": [{"data": {"text/plain": ["{'description': 'Update list of favorite pets',\n", " 'properties': {'pets': {'items': {'type': 'string'},\n", "   'title': 'Pet<PERSON>',\n", "   'type': 'array'}},\n", " 'required': ['pets'],\n", " 'title': 'update_favorite_pets',\n", " 'type': 'object'}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["UpdateFavoritePets2().tool_call_schema.model_json_schema()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}