{"cells": [{"cell_type": "raw", "id": "45e0127d", "metadata": {}, "source": ["---\n", "sidebar_position: 4\n", "---"]}, {"cell_type": "markdown", "id": "d8ca736e", "metadata": {}, "source": ["# How to partially format prompt templates\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [Prompt templates](/docs/concepts/prompt_templates)\n", "\n", ":::\n", "\n", "Like partially binding arguments to a function, it can make sense to \"partial\" a [prompt template](/docs/concepts/prompt_templates/) - e.g. pass in a subset of the required values, as to create a new prompt template which expects only the remaining subset of values.\n", "\n", "<PERSON><PERSON><PERSON><PERSON> supports this in two ways:\n", "\n", "1. Partial formatting with string values.\n", "2. Partial formatting with functions that return string values.\n", "\n", "In the examples below, we go over the motivations for both use cases as well as how to do it in LangChain.\n", "\n", "## Partial with strings\n", "\n", "One common use case for wanting to partial a prompt template is if you get access to some of the variables in a prompt before others. For example, suppose you have a prompt template that requires two variables, `foo` and `baz`. If you get the `foo` value early on in your chain, but the `baz` value later, it can be inconvenient to pass both variables all the way through the chain. Instead, you can partial the prompt template with the `foo` value, and then pass the partialed prompt template along and just use that. Below is an example of doing this:\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5f1942bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["foobaz\n"]}], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "prompt = PromptTemplate.from_template(\"{foo}{bar}\")\n", "partial_prompt = prompt.partial(foo=\"foo\")\n", "print(partial_prompt.format(bar=\"baz\"))"]}, {"cell_type": "markdown", "id": "79af4cea", "metadata": {}, "source": ["You can also just initialize the prompt with the partialed variables.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "572fa26f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["foobaz\n"]}], "source": ["prompt = PromptTemplate(\n", "    template=\"{foo}{bar}\", input_variables=[\"bar\"], partial_variables={\"foo\": \"foo\"}\n", ")\n", "print(prompt.format(bar=\"baz\"))"]}, {"cell_type": "markdown", "id": "ab12d50d", "metadata": {}, "source": ["## Partial with functions\n", "\n", "The other common use is to partial with a function. The use case for this is when you have a variable you know that you always want to fetch in a common way. A prime example of this is with date or time. Imagine you have a prompt which you always want to have the current date. You can't hard code it in the prompt, and passing it along with the other input variables is inconvenient. In this case, it's handy to be able to partial the prompt with a function that always returns the current date.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "c538703a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tell me a funny joke about the day 04/21/2024, 19:43:57\n"]}], "source": ["from datetime import datetime\n", "\n", "\n", "def _get_datetime():\n", "    now = datetime.now()\n", "    return now.strftime(\"%m/%d/%Y, %H:%M:%S\")\n", "\n", "\n", "prompt = PromptTemplate(\n", "    template=\"Tell me a {adjective} joke about the day {date}\",\n", "    input_variables=[\"adjective\", \"date\"],\n", ")\n", "partial_prompt = prompt.partial(date=_get_datetime)\n", "print(partial_prompt.format(adjective=\"funny\"))"]}, {"cell_type": "markdown", "id": "da80290e", "metadata": {}, "source": ["You can also just initialize the prompt with the partialed variables, which often makes more sense in this workflow.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "f86fce6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tell me a funny joke about the day 04/21/2024, 19:43:57\n"]}], "source": ["prompt = PromptTemplate(\n", "    template=\"Tell me a {adjective} joke about the day {date}\",\n", "    input_variables=[\"adjective\"],\n", "    partial_variables={\"date\": _get_datetime},\n", ")\n", "print(prompt.format(adjective=\"funny\"))"]}, {"cell_type": "markdown", "id": "3895b210", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now learned how to partially apply variables to your prompt templates.\n", "\n", "Next, check out the other how-to guides on prompt templates in this section, like [adding few-shot examples to your prompt templates](/docs/how_to/few_shot_examples_chat)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 5}