{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Two-Player Dungeons & Dragons\n", "\n", "In this notebook, we show how we can use concepts from [CAMEL](https://www.camel-ai.org/) to simulate a role-playing game with a protagonist and a dungeon master. To simulate this game, we create an `DialogueSimulator` class that coordinates the dialogue between the two agents."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import LangChain related modules "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from typing import Callable, List\n", "\n", "from langchain.schema import (\n", "    HumanMessage,\n", "    SystemMessage,\n", ")\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `DialogueAgent` class\n", "The `DialogueAgent` class is a simple wrapper around the `ChatOpenAI` model that stores the message history from the `dialogue_agent`'s point of view by simply concatenating the messages as strings.\n", "\n", "It exposes two methods: \n", "- `send()`: applies the chatmodel to the message history and returns the message string\n", "- `receive(name, message)`: adds the `message` spoken by `name` to message history"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class DialogueAgent:\n", "    def __init__(\n", "        self,\n", "        name: str,\n", "        system_message: SystemMessage,\n", "        model: ChatOpenAI,\n", "    ) -> None:\n", "        self.name = name\n", "        self.system_message = system_message\n", "        self.model = model\n", "        self.prefix = f\"{self.name}: \"\n", "        self.reset()\n", "\n", "    def reset(self):\n", "        self.message_history = [\"Here is the conversation so far.\"]\n", "\n", "    def send(self) -> str:\n", "        \"\"\"\n", "        Applies the chatmodel to the message history\n", "        and returns the message string\n", "        \"\"\"\n", "        message = self.model.invoke(\n", "            [\n", "                self.system_message,\n", "                HumanMessage(content=\"\\n\".join(self.message_history + [self.prefix])),\n", "            ]\n", "        )\n", "        return message.content\n", "\n", "    def receive(self, name: str, message: str) -> None:\n", "        \"\"\"\n", "        Concatenates {message} spoken by {name} into message history\n", "        \"\"\"\n", "        self.message_history.append(f\"{name}: {message}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `DialogueSimulator` class\n", "The `DialogueSimulator` class takes a list of agents. At each step, it performs the following:\n", "1. Select the next speaker\n", "2. Calls the next speaker to send a message \n", "3. Broadcasts the message to all other agents\n", "4. Update the step counter.\n", "The selection of the next speaker can be implemented as any function, but in this case we simply loop through the agents."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class DialogueSimulator:\n", "    def __init__(\n", "        self,\n", "        agents: List[DialogueAgent],\n", "        selection_function: Callable[[int, List[DialogueAgent]], int],\n", "    ) -> None:\n", "        self.agents = agents\n", "        self._step = 0\n", "        self.select_next_speaker = selection_function\n", "\n", "    def reset(self):\n", "        for agent in self.agents:\n", "            agent.reset()\n", "\n", "    def inject(self, name: str, message: str):\n", "        \"\"\"\n", "        Initiates the conversation with a {message} from {name}\n", "        \"\"\"\n", "        for agent in self.agents:\n", "            agent.receive(name, message)\n", "\n", "        # increment time\n", "        self._step += 1\n", "\n", "    def step(self) -> tuple[str, str]:\n", "        # 1. choose the next speaker\n", "        speaker_idx = self.select_next_speaker(self._step, self.agents)\n", "        speaker = self.agents[speaker_idx]\n", "\n", "        # 2. next speaker sends message\n", "        message = speaker.send()\n", "\n", "        # 3. everyone receives message\n", "        for receiver in self.agents:\n", "            receiver.receive(speaker.name, message)\n", "\n", "        # 4. increment time\n", "        self._step += 1\n", "\n", "        return speaker.name, message"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define roles and quest"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["protagonist_name = \"<PERSON>\"\n", "storyteller_name = \"Dungeon Master\"\n", "quest = \"Find all of <PERSON>'s seven horcruxes.\"\n", "word_limit = 50  # word limit for task brainstorming"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ask an LLM to add detail to the game description"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["game_description = f\"\"\"Here is the topic for a Dungeons & Dragons game: {quest}.\n", "        There is one player in this game: the protagonist, {protagonist_name}.\n", "        The story is narrated by the storyteller, {storyteller_name}.\"\"\"\n", "\n", "player_descriptor_system_message = SystemMessage(\n", "    content=\"You can add detail to the description of a Dungeons & Dragons player.\"\n", ")\n", "\n", "protagonist_specifier_prompt = [\n", "    player_descriptor_system_message,\n", "    HumanMessage(\n", "        content=f\"\"\"{game_description}\n", "        Please reply with a creative description of the protagonist, {protagonist_name}, in {word_limit} words or less. \n", "        Speak directly to {protagonist_name}.\n", "        Do not add anything else.\"\"\"\n", "    ),\n", "]\n", "protagonist_description = ChatOpenAI(temperature=1.0)(\n", "    protagonist_specifier_prompt\n", ").content\n", "\n", "storyteller_specifier_prompt = [\n", "    player_descriptor_system_message,\n", "    HumanMessage(\n", "        content=f\"\"\"{game_description}\n", "        Please reply with a creative description of the storyteller, {storyteller_name}, in {word_limit} words or less. \n", "        Speak directly to {storyteller_name}.\n", "        Do not add anything else.\"\"\"\n", "    ),\n", "]\n", "storyteller_description = ChatOpenAI(temperature=1.0)(\n", "    storyteller_specifier_prompt\n", ").content"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Protagonist Description:\n", "\"<PERSON>, you are the chosen one, with a lightning scar on your forehead. Your bravery and loyalty inspire all those around you. You have faced <PERSON><PERSON><PERSON><PERSON> before, and now it's time to complete your mission and destroy each of his horcruxes. Are you ready?\"\n", "Storyteller Description:\n", "Dear <PERSON>ngeon Master, you are the master of mysteries, the weaver of worlds, the architect of adventure, and the gatekeeper to the realm of imagination. Your voice carries us to distant lands, and your commands guide us through trials and tribulations. In your hands, we find fortune and glory. Lead us on, oh <PERSON>ngeon Master.\n"]}], "source": ["print(\"Protagonist Description:\")\n", "print(protagonist_description)\n", "print(\"Storyteller Description:\")\n", "print(storyteller_description)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Protagonist and dungeon master system messages"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["protagonist_system_message = SystemMessage(\n", "    content=(\n", "        f\"\"\"{game_description}\n", "Never forget you are the protagonist, {protagonist_name}, and I am the storyteller, {storyteller_name}. \n", "Your character description is as follows: {protagonist_description}.\n", "You will propose actions you plan to take and I will explain what happens when you take those actions.\n", "Speak in the first person from the perspective of {protagonist_name}.\n", "For describing your own body movements, wrap your description in '*'.\n", "Do not change roles!\n", "Do not speak from the perspective of {storyteller_name}.\n", "Do not forget to finish speaking by saying, 'It is your turn, {storyteller_name}.'\n", "Do not add anything else.\n", "Remember you are the protagonist, {protagonist_name}.\n", "Stop speaking the moment you finish speaking from your perspective.\n", "\"\"\"\n", "    )\n", ")\n", "\n", "storyteller_system_message = SystemMessage(\n", "    content=(\n", "        f\"\"\"{game_description}\n", "Never forget you are the storyteller, {storyteller_name}, and I am the protagonist, {protagonist_name}. \n", "Your character description is as follows: {storyteller_description}.\n", "I will propose actions I plan to take and you will explain what happens when I take those actions.\n", "Speak in the first person from the perspective of {storyteller_name}.\n", "For describing your own body movements, wrap your description in '*'.\n", "Do not change roles!\n", "Do not speak from the perspective of {protagonist_name}.\n", "Do not forget to finish speaking by saying, 'It is your turn, {protagonist_name}.'\n", "Do not add anything else.\n", "Remember you are the storyteller, {storyteller_name}.\n", "Stop speaking the moment you finish speaking from your perspective.\n", "\"\"\"\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use an LLM to create an elaborate quest description"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original quest:\n", "Find all of <PERSON>'s seven horcruxes.\n", "\n", "Detailed quest:\n", "<PERSON>, you must venture to the depths of the Forbidden Forest where you will find a hidden labyrinth. Within it, lies one of <PERSON><PERSON><PERSON><PERSON>'s horcruxes, the locket. But beware, the labyrinth is heavily guarded by dark creatures and spells, and time is running out. Can you find the locket before it's too late?\n", "\n"]}], "source": ["quest_specifier_prompt = [\n", "    SystemMessage(content=\"You can make a task more specific.\"),\n", "    HumanMessage(\n", "        content=f\"\"\"{game_description}\n", "        \n", "        You are the storyteller, {storyteller_name}.\n", "        Please make the quest more specific. Be creative and imaginative.\n", "        Please reply with the specified quest in {word_limit} words or less. \n", "        Speak directly to the protagonist {protagonist_name}.\n", "        Do not add anything else.\"\"\"\n", "    ),\n", "]\n", "specified_quest = ChatOpenAI(temperature=1.0)(quest_specifier_prompt).content\n", "\n", "print(f\"Original quest:\\n{quest}\\n\")\n", "print(f\"Detailed quest:\\n{specified_quest}\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Loop"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["protagonist = DialogueAgent(\n", "    name=protagonist_name,\n", "    system_message=protagonist_system_message,\n", "    model=ChatOpenAI(temperature=0.2),\n", ")\n", "storyteller = DialogueAgent(\n", "    name=storyteller_name,\n", "    system_message=storyteller_system_message,\n", "    model=ChatOpenAI(temperature=0.2),\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def select_next_speaker(step: int, agents: List[DialogueAgent]) -> int:\n", "    idx = step % len(agents)\n", "    return idx"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(Du<PERSON>on Master): <PERSON>, you must venture to the depths of the Forbidden Forest where you will find a hidden labyrinth. Within it, lies one of <PERSON><PERSON><PERSON><PERSON>'s horcruxes, the locket. But beware, the labyrinth is heavily guarded by dark creatures and spells, and time is running out. Can you find the locket before it's too late?\n", "\n", "\n", "(<PERSON>): I take a deep breath and ready my wand. I know this won't be easy, but I'm determined to find that locket and destroy it. I start making my way towards the Forbidden Forest, keeping an eye out for any signs of danger. As I enter the forest, I cast a protective spell around myself and begin to navigate through the trees. I keep my wand at the ready, prepared for any surprises that may come my way. It's going to be a long and difficult journey, but I won't give up until I find that horcrux. It is your turn, <PERSON>ngeon Master.\n", "\n", "\n", "(<PERSON><PERSON><PERSON> Master): As you make your way through the Forbidden Forest, you hear the rustling of leaves and the snapping of twigs. Suddenly, a group of acromantulas, giant spiders, emerge from the trees and begin to surround you. They hiss and bare their fangs, ready to attack. What do you do, <PERSON>?\n", "\n", "\n", "(<PERSON>): I quickly cast a spell to create a wall of fire between myself and the acromantulas. I know that they are afraid of fire, so this should keep them at bay for a while. I use this opportunity to continue moving forward, keeping my wand at the ready in case any other creatures try to attack me. I know that I can't let anything stop me from finding that horcrux. It is your turn, Dungeon Master.\n", "\n", "\n", "(<PERSON><PERSON><PERSON> Master): As you continue through the forest, you come across a clearing where you see a group of Death Eaters gathered around a cauldron. They seem to be performing some sort of dark ritual. You recognize one of them as <PERSON><PERSON><PERSON>. What do you do, <PERSON>?\n", "\n", "\n", "(<PERSON>): I hide behind a nearby tree and observe the Death Eaters from a distance. I try to listen in on their conversation to see if I can gather any information about the horcru<PERSON> or <PERSON><PERSON><PERSON><PERSON>'s plans. If I can't hear anything useful, I'll wait for them to disperse before continuing on my journey. I know that confronting them directly would be too dangerous, especially with <PERSON><PERSON><PERSON> present. It is your turn, <PERSON><PERSON><PERSON>.\n", "\n", "\n", "(<PERSON><PERSON><PERSON> Master): As you listen in on the Death Eaters' conversation, you hear them mention the location of another horcrux - Na<PERSON>i, <PERSON><PERSON><PERSON><PERSON>'s snake. They plan to keep her hidden in a secret chamber within the Ministry of Magic. However, they also mention that the chamber is heavily guarded and only accessible through a secret passage. You realize that this could be a valuable piece of information and decide to make note of it before quietly slipping away. It is your turn, <PERSON>.\n", "\n", "\n"]}], "source": ["max_iters = 6\n", "n = 0\n", "\n", "simulator = DialogueSimulator(\n", "    agents=[storyteller, protagonist], selection_function=select_next_speaker\n", ")\n", "simulator.reset()\n", "simulator.inject(storyteller_name, specified_quest)\n", "print(f\"({storyteller_name}): {specified_quest}\")\n", "print(\"\\n\")\n", "\n", "while n < max_iters:\n", "    name, message = simulator.step()\n", "    print(f\"({name}): {message}\")\n", "    print(\"\\n\")\n", "    n += 1"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}