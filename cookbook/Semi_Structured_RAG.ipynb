{"cells": [{"attachments": {"7b5c5a30-393c-4b27-8fa1-688306ef2aef.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "b6d466cc-aa8b-4baf-a80a-fef01921ca8d", "metadata": {}, "source": ["## Semi-structured RAG\n", "\n", "Many documents contain a mixture of content types, including text and tables. \n", "\n", "Semi-structured data can be challenging for conventional RAG for at least two reasons: \n", "\n", "* Text splitting may break up tables, corrupting the data in retrieval\n", "* Embedding tables may pose challenges for semantic similarity search \n", "\n", "This cookbook shows how to perform RAG on documents with semi-structured data: \n", "\n", "* We will use [Unstructured](https://unstructured.io/) to parse both text and tables from documents (PDFs).\n", "* We will use the [multi-vector retriever](https://python.langchain.com/docs/modules/data_connection/retrievers/multi_vector) to store raw tables, text along with table summaries better suited for retrieval.\n", "* We will use [LCEL](https://python.langchain.com/docs/expression_language/) to implement the chains used.\n", "\n", "The overall flow is here:\n", "\n", "![MVR.png](attachment:7b5c5a30-393c-4b27-8fa1-688306ef2aef.png)\n", "\n", "## Packages"]}, {"cell_type": "code", "execution_count": null, "id": "5740fc70-c513-4ff4-9d72-cfc098f85fef", "metadata": {}, "outputs": [], "source": ["! pip install langchain langchain-chroma \"unstructured[all-docs]\" pydantic lxml langchainhub"]}, {"cell_type": "markdown", "id": "44349a83-e1dc-4eed-ba75-587f309d8c88", "metadata": {}, "source": ["The PDF partitioning used by Unstructured will use: \n", "\n", "* `tesseract` for Optical Character Recognition (OCR)\n", "*  `poppler` for PDF rendering and processing"]}, {"cell_type": "code", "execution_count": null, "id": "f7880871-4949-4ea2-aed8-540a09188a41", "metadata": {}, "outputs": [], "source": ["! brew install tesseract\n", "! brew install poppler"]}, {"cell_type": "markdown", "id": "7c24efa9-b6f6-4dc2-bfe3-70819ba3ef75", "metadata": {}, "source": ["## Data Loading\n", "\n", "### Partition PDF tables and text\n", "\n", "Apply to the [`LLaMA2`](https://arxiv.org/pdf/2307.09288.pdf) paper. \n", "\n", "We use the Unstructured [`partition_pdf`](https://unstructured-io.github.io/unstructured/core/partition.html#partition-pdf), which segments a PDF document by using a layout model. \n", "\n", "This layout model makes it possible to extract elements, such as tables, from pdfs. \n", "\n", "We also can use `Unstructured` chunking, which:\n", "\n", "* Tries to identify document sections (e.g., Introduction, etc)\n", "* Then, builds text blocks that maintain sections while also honoring user-defined chunk sizes"]}, {"cell_type": "code", "execution_count": 1, "id": "62cf502b-407d-4645-a72c-24498fd55130", "metadata": {}, "outputs": [], "source": ["path = \"/Users/<USER>/Desktop/Papers/LLaMA2/\""]}, {"cell_type": "code", "execution_count": null, "id": "3867a654-61ba-4759-9a64-de953a429ced", "metadata": {}, "outputs": [], "source": ["from typing import Any\n", "\n", "from pydantic import BaseModel\n", "from unstructured.partition.pdf import partition_pdf\n", "\n", "# Get elements\n", "raw_pdf_elements = partition_pdf(\n", "    filename=path + \"LLaMA2.pdf\",\n", "    # Unstructured first finds embedded image blocks\n", "    extract_images_in_pdf=False,\n", "    # Use layout model (YOLOX) to get bounding boxes (for tables) and find titles\n", "    # Titles are any sub-section of the document\n", "    infer_table_structure=True,\n", "    # Post processing to aggregate text once we have the title\n", "    chunking_strategy=\"by_title\",\n", "    # Chunking params to aggregate text blocks\n", "    # Attempt to create a new chunk 3800 chars\n", "    # Attempt to keep chunks > 2000 chars\n", "    max_characters=4000,\n", "    new_after_n_chars=3800,\n", "    combine_text_under_n_chars=2000,\n", "    image_output_dir_path=path,\n", ")"]}, {"cell_type": "markdown", "id": "b09cd727-aeab-49af-8a51-0dc377321e7c", "metadata": {}, "source": ["We can examine the elements extracted by `partition_pdf`.\n", "\n", "`CompositeElement` are aggregated chunks."]}, {"cell_type": "code", "execution_count": 13, "id": "628abfc6-4057-434b-b880-d88e3ba44657", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\"<class 'unstructured.documents.elements.CompositeElement'>\": 184,\n", " \"<class 'unstructured.documents.elements.Table'>\": 47,\n", " \"<class 'unstructured.documents.elements.TableChunk'>\": 2}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a dictionary to store counts of each type\n", "category_counts = {}\n", "\n", "for element in raw_pdf_elements:\n", "    category = str(type(element))\n", "    if category in category_counts:\n", "        category_counts[category] += 1\n", "    else:\n", "        category_counts[category] = 1\n", "\n", "# Unique_categories will have unique elements\n", "unique_categories = set(category_counts.keys())\n", "category_counts"]}, {"cell_type": "code", "execution_count": 14, "id": "5462f29e-fd59-4e0e-9493-ea3b560e523e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["49\n", "184\n"]}], "source": ["class Element(BaseModel):\n", "    type: str\n", "    text: Any\n", "\n", "\n", "# Categorize by type\n", "categorized_elements = []\n", "for element in raw_pdf_elements:\n", "    if \"unstructured.documents.elements.Table\" in str(type(element)):\n", "        categorized_elements.append(Element(type=\"table\", text=str(element)))\n", "    elif \"unstructured.documents.elements.CompositeElement\" in str(type(element)):\n", "        categorized_elements.append(Element(type=\"text\", text=str(element)))\n", "\n", "# Tables\n", "table_elements = [e for e in categorized_elements if e.type == \"table\"]\n", "print(len(table_elements))\n", "\n", "# Text\n", "text_elements = [e for e in categorized_elements if e.type == \"text\"]\n", "print(len(text_elements))"]}, {"cell_type": "markdown", "id": "731b3dfc-7ddf-4a11-9a30-9a79b7c66e16", "metadata": {}, "source": ["## Multi-vector retriever\n", "\n", "Use [multi-vector-retriever](https://python.langchain.com/docs/modules/data_connection/retrievers/multi_vector#summary) to produce summaries of tables and, optionally, text. \n", "\n", "With the summary, we will also store the raw table elements.\n", "\n", "The summaries are used to improve the quality of retrieval, [as explained in the multi vector retriever docs](https://python.langchain.com/docs/modules/data_connection/retrievers/multi_vector).\n", "\n", "The raw tables are passed to the LLM, providing the full table context for the LLM to generate the answer.  \n", "\n", "### Summaries"]}, {"cell_type": "code", "execution_count": 16, "id": "8e275736-3408-4d7a-990e-4362c88e81f8", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "markdown", "id": "37b65677-aeb4-44fd-b06d-4539341ede97", "metadata": {}, "source": ["We create a simple summarize chain for each element.\n", "\n", "You can also see, re-use, or modify the prompt in the Hub [here](https://smith.langchain.com/hub/rlm/multi-vector-retriever-summarization).\n", "\n", "```\n", "from langchain import hub\n", "obj = hub.pull(\"rlm/multi-vector-retriever-summarization\")\n", "```"]}, {"cell_type": "code", "execution_count": 17, "id": "1b12536a-1303-41ad-9948-4eb5a5f32614", "metadata": {}, "outputs": [], "source": ["# Prompt\n", "prompt_text = \"\"\"You are an assistant tasked with summarizing tables and text. \\ \n", "Give a concise summary of the table or text. Table or text chunk: {element} \"\"\"\n", "prompt = ChatPromptTemplate.from_template(prompt_text)\n", "\n", "# Summary chain\n", "model = ChatOpenAI(temperature=0, model=\"gpt-4\")\n", "summarize_chain = {\"element\": lambda x: x} | prompt | model | StrOutputParser()"]}, {"cell_type": "code", "execution_count": null, "id": "8d8b567c-b442-4bf0-b639-04bd89effc62", "metadata": {}, "outputs": [], "source": ["# Apply to tables\n", "tables = [i.text for i in table_elements]\n", "table_summaries = summarize_chain.batch(tables, {\"max_concurrency\": 5})"]}, {"cell_type": "code", "execution_count": 26, "id": "3e9c176c-3d46-4034-b169-0d7305d42d27", "metadata": {}, "outputs": [], "source": ["# Apply to texts\n", "texts = [i.text for i in text_elements]\n", "text_summaries = summarize_chain.batch(texts, {\"max_concurrency\": 5})"]}, {"cell_type": "markdown", "id": "60524010-754f-4924-ad75-78cb54ca7257", "metadata": {}, "source": ["### Add to vectorstore\n", "\n", "Use [Multi Vector Retriever](https://python.langchain.com/docs/modules/data_connection/retrievers/multi_vector#summary) with summaries: \n", "\n", "* `InMemoryStore` stores the raw text, tables\n", "* `vectorstore` stores the embedded summaries"]}, {"cell_type": "code", "execution_count": 27, "id": "346c3a02-8fea-4f75-a69e-fc9542b99dbc", "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "from langchain.retrievers.multi_vector import MultiVectorRetriever\n", "from langchain.storage import InMemoryStore\n", "from langchain_chroma import Chroma\n", "from langchain_core.documents import Document\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "# The vectorstore to use to index the child chunks\n", "vectorstore = Chroma(collection_name=\"summaries\", embedding_function=OpenAIEmbeddings())\n", "\n", "# The storage layer for the parent documents\n", "store = InMemoryStore()\n", "id_key = \"doc_id\"\n", "\n", "# The retriever (empty to start)\n", "retriever = MultiVectorRetriever(\n", "    vectorstore=vectorstore,\n", "    docstore=store,\n", "    id_key=id_key,\n", ")\n", "\n", "# Add texts\n", "doc_ids = [str(uuid.uuid4()) for _ in texts]\n", "summary_texts = [\n", "    Document(page_content=s, metadata={id_key: doc_ids[i]})\n", "    for i, s in enumerate(text_summaries)\n", "]\n", "retriever.vectorstore.add_documents(summary_texts)\n", "retriever.docstore.mset(list(zip(doc_ids, texts)))\n", "\n", "# Add tables\n", "table_ids = [str(uuid.uuid4()) for _ in tables]\n", "summary_tables = [\n", "    Document(page_content=s, metadata={id_key: table_ids[i]})\n", "    for i, s in enumerate(table_summaries)\n", "]\n", "retriever.vectorstore.add_documents(summary_tables)\n", "retriever.docstore.mset(list(zip(table_ids, tables)))"]}, {"cell_type": "markdown", "id": "1d8bbbd9-009b-4b34-a206-5874a60adbda", "metadata": {}, "source": ["## RAG\n", "\n", "Run [RAG pipeline](https://python.langchain.com/docs/expression_language/cookbook/retrieval)."]}, {"cell_type": "code", "execution_count": 28, "id": "f2489de4-51e3-48b4-bbcd-ed9171deadf3", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnablePassthrough\n", "\n", "# Prompt template\n", "template = \"\"\"Answer the question based only on the following context, which can include text and tables:\n", "{context}\n", "Question: {question}\n", "\"\"\"\n", "prompt = ChatPromptTemplate.from_template(template)\n", "\n", "# LLM\n", "model = ChatOpenAI(temperature=0, model=\"gpt-4\")\n", "\n", "# RAG pipeline\n", "chain = (\n", "    {\"context\": retriever, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | model\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "code", "execution_count": 29, "id": "90e3d100-10e8-4ee6-ae46-2480b1524ec8", "metadata": {}, "outputs": [{"data": {"text/plain": ["'The number of training tokens for LLaMA2 is 2.0T.'"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke(\"What is the number of training tokens for LLaMA2?\")"]}, {"cell_type": "markdown", "id": "37f46054-e239-4ba8-af81-22d0d6a9bc32", "metadata": {}, "source": ["We can check the [trace](https://smith.langchain.com/public/4739ae7c-1a13-406d-bc4e-3462670ebc01/r) to see what chunks were retrieved:\n", "\n", "This includes Table 1 of the paper, showing the Tokens used for training.\n", "\n", "```\n", "Training Data Params Context GQA Tokens LR Length 7B 2k 1.0T 3.0x 10-4 See <PERSON><PERSON><PERSON><PERSON> et al. 13B 2k 1.0T 3.0 x 10-4 LiaMa 1 (2023) 33B 2k 14T 1.5 x 10-4 65B 2k 1.4T 1.5 x 10-4 7B 4k 2.0T 3.0x 10-4 Liama 2 A new mix of publicly 13B 4k 2.0T 3.0 x 10-4 available online data 34B 4k v 2.0T 1.5 x 10-4 70B 4k v 2.0T 1.5 x 10-4\n", "```"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}