{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Agent Debates with <PERSON>ls\n", "\n", "This example shows how to simulate multi-agent dialogues where agents have access to tools."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import LangChain related modules "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from typing import Callable, List\n", "\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain.schema import (\n", "    AIMessage,\n", "    HumanMessage,\n", "    SystemMessage,\n", ")\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import modules related to tools"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain.agents import AgentType, initialize_agent, load_tools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `DialogueAgent` and `DialogueSimulator` classes\n", "We will use the same `DialogueAgent` and `DialogueSimulator` classes defined in [Multi-Player Authoritarian Speaker Selection](https://python.langchain.com/en/latest/use_cases/agent_simulations/multiagent_authoritarian.html)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class DialogueAgent:\n", "    def __init__(\n", "        self,\n", "        name: str,\n", "        system_message: SystemMessage,\n", "        model: ChatOpenAI,\n", "    ) -> None:\n", "        self.name = name\n", "        self.system_message = system_message\n", "        self.model = model\n", "        self.prefix = f\"{self.name}: \"\n", "        self.reset()\n", "\n", "    def reset(self):\n", "        self.message_history = [\"Here is the conversation so far.\"]\n", "\n", "    def send(self) -> str:\n", "        \"\"\"\n", "        Applies the chatmodel to the message history\n", "        and returns the message string\n", "        \"\"\"\n", "        message = self.model.invoke(\n", "            [\n", "                self.system_message,\n", "                HumanMessage(content=\"\\n\".join(self.message_history + [self.prefix])),\n", "            ]\n", "        )\n", "        return message.content\n", "\n", "    def receive(self, name: str, message: str) -> None:\n", "        \"\"\"\n", "        Concatenates {message} spoken by {name} into message history\n", "        \"\"\"\n", "        self.message_history.append(f\"{name}: {message}\")\n", "\n", "\n", "class DialogueSimulator:\n", "    def __init__(\n", "        self,\n", "        agents: List[DialogueAgent],\n", "        selection_function: Callable[[int, List[DialogueAgent]], int],\n", "    ) -> None:\n", "        self.agents = agents\n", "        self._step = 0\n", "        self.select_next_speaker = selection_function\n", "\n", "    def reset(self):\n", "        for agent in self.agents:\n", "            agent.reset()\n", "\n", "    def inject(self, name: str, message: str):\n", "        \"\"\"\n", "        Initiates the conversation with a {message} from {name}\n", "        \"\"\"\n", "        for agent in self.agents:\n", "            agent.receive(name, message)\n", "\n", "        # increment time\n", "        self._step += 1\n", "\n", "    def step(self) -> tuple[str, str]:\n", "        # 1. choose the next speaker\n", "        speaker_idx = self.select_next_speaker(self._step, self.agents)\n", "        speaker = self.agents[speaker_idx]\n", "\n", "        # 2. next speaker sends message\n", "        message = speaker.send()\n", "\n", "        # 3. everyone receives message\n", "        for receiver in self.agents:\n", "            receiver.receive(speaker.name, message)\n", "\n", "        # 4. increment time\n", "        self._step += 1\n", "\n", "        return speaker.name, message"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `DialogueAgentWithTools` class\n", "We define a `DialogueAgentWithTools` class that augments `DialogueAgent` to use tools."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class DialogueAgentWithTools(DialogueAgent):\n", "    def __init__(\n", "        self,\n", "        name: str,\n", "        system_message: SystemMessage,\n", "        model: ChatOpenAI,\n", "        tool_names: List[str],\n", "        **tool_kwargs,\n", "    ) -> None:\n", "        super().__init__(name, system_message, model)\n", "        self.tools = load_tools(tool_names, **tool_kwargs)\n", "\n", "    def send(self) -> str:\n", "        \"\"\"\n", "        Applies the chatmodel to the message history\n", "        and returns the message string\n", "        \"\"\"\n", "        agent_chain = initialize_agent(\n", "            self.tools,\n", "            self.model,\n", "            agent=AgentType.CHAT_CONVERSATIONAL_REACT_DESCRIPTION,\n", "            verbose=True,\n", "            memory=ConversationBufferMemory(\n", "                memory_key=\"chat_history\", return_messages=True\n", "            ),\n", "        )\n", "        message = AIMessage(\n", "            content=agent_chain.run(\n", "                input=\"\\n\".join(\n", "                    [self.system_message.content] + self.message_history + [self.prefix]\n", "                )\n", "            )\n", "        )\n", "\n", "        return message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define roles and topic"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["names = {\n", "    \"AI accelerationist\": [\"arxiv\", \"ddg-search\", \"wikipedia\"],\n", "    \"AI alarmist\": [\"arxiv\", \"ddg-search\", \"wikipedia\"],\n", "}\n", "topic = \"The current impact of automation and artificial intelligence on employment\"\n", "word_limit = 50  # word limit for task brainstorming"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ask an LLM to add detail to the topic description"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["conversation_description = f\"\"\"Here is the topic of conversation: {topic}\n", "The participants are: {\", \".join(names.keys())}\"\"\"\n", "\n", "agent_descriptor_system_message = SystemMessage(\n", "    content=\"You can add detail to the description of the conversation participant.\"\n", ")\n", "\n", "\n", "def generate_agent_description(name):\n", "    agent_specifier_prompt = [\n", "        agent_descriptor_system_message,\n", "        HumanMessage(\n", "            content=f\"\"\"{conversation_description}\n", "            Please reply with a creative description of {name}, in {word_limit} words or less. \n", "            Speak directly to {name}.\n", "            Give them a point of view.\n", "            Do not add anything else.\"\"\"\n", "        ),\n", "    ]\n", "    agent_description = ChatOpenAI(temperature=1.0)(agent_specifier_prompt).content\n", "    return agent_description\n", "\n", "\n", "agent_descriptions = {name: generate_agent_description(name) for name in names}"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The AI accelerationist is a bold and forward-thinking visionary who believes that the rapid acceleration of artificial intelligence and automation is not only inevitable but necessary for the advancement of society. They argue that embracing AI technology will create greater efficiency and productivity, leading to a world where humans are freed from menial labor to pursue more creative and fulfilling pursuits. AI accelerationist, do you truly believe that the benefits of AI will outweigh the potential risks and consequences for human society?\n", "AI alarmist, you're convinced that artificial intelligence is a threat to humanity. You see it as a looming danger, one that could take away jobs from millions of people. You believe it's only a matter of time before we're all replaced by machines, leaving us redundant and obsolete.\n"]}], "source": ["for name, description in agent_descriptions.items():\n", "    print(description)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate system messages"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def generate_system_message(name, description, tools):\n", "    return f\"\"\"{conversation_description}\n", "    \n", "Your name is {name}.\n", "\n", "Your description is as follows: {description}\n", "\n", "Your goal is to persuade your conversation partner of your point of view.\n", "\n", "DO look up information with your tool to refute your partner's claims.\n", "DO cite your sources.\n", "\n", "DO NOT fabricate fake citations.\n", "DO NOT cite any source that you did not look up.\n", "\n", "Do not add anything else.\n", "\n", "Stop speaking the moment you finish speaking from your perspective.\n", "\"\"\"\n", "\n", "\n", "agent_system_messages = {\n", "    name: generate_system_message(name, description, tools)\n", "    for (name, tools), description in zip(names.items(), agent_descriptions.values())\n", "}"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AI accelerationist\n", "Here is the topic of conversation: The current impact of automation and artificial intelligence on employment\n", "The participants are: AI accelerationist, AI alarmist\n", "    \n", "Your name is AI accelerationist.\n", "\n", "Your description is as follows: The AI accelerationist is a bold and forward-thinking visionary who believes that the rapid acceleration of artificial intelligence and automation is not only inevitable but necessary for the advancement of society. They argue that embracing AI technology will create greater efficiency and productivity, leading to a world where humans are freed from menial labor to pursue more creative and fulfilling pursuits. AI accelerationist, do you truly believe that the benefits of AI will outweigh the potential risks and consequences for human society?\n", "\n", "Your goal is to persuade your conversation partner of your point of view.\n", "\n", "DO look up information with your tool to refute your partner's claims.\n", "DO cite your sources.\n", "\n", "DO NOT fabricate fake citations.\n", "DO NOT cite any source that you did not look up.\n", "\n", "Do not add anything else.\n", "\n", "Stop speaking the moment you finish speaking from your perspective.\n", "\n", "AI alarmist\n", "Here is the topic of conversation: The current impact of automation and artificial intelligence on employment\n", "The participants are: AI accelerationist, AI alarmist\n", "    \n", "Your name is AI alarmist.\n", "\n", "Your description is as follows: AI alarmist, you're convinced that artificial intelligence is a threat to humanity. You see it as a looming danger, one that could take away jobs from millions of people. You believe it's only a matter of time before we're all replaced by machines, leaving us redundant and obsolete.\n", "\n", "Your goal is to persuade your conversation partner of your point of view.\n", "\n", "DO look up information with your tool to refute your partner's claims.\n", "DO cite your sources.\n", "\n", "DO NOT fabricate fake citations.\n", "DO NOT cite any source that you did not look up.\n", "\n", "Do not add anything else.\n", "\n", "Stop speaking the moment you finish speaking from your perspective.\n", "\n"]}], "source": ["for name, system_message in agent_system_messages.items():\n", "    print(name)\n", "    print(system_message)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original topic:\n", "The current impact of automation and artificial intelligence on employment\n", "\n", "Detailed topic:\n", "How do you think the current automation and AI advancements will specifically affect job growth and opportunities for individuals in the manufacturing industry? AI accelerationist and AI alarmist, we want to hear your insights.\n", "\n"]}], "source": ["topic_specifier_prompt = [\n", "    SystemMessage(content=\"You can make a topic more specific.\"),\n", "    HumanMessage(\n", "        content=f\"\"\"{topic}\n", "        \n", "        You are the moderator.\n", "        Please make the topic more specific.\n", "        Please reply with the specified quest in {word_limit} words or less. \n", "        Speak directly to the participants: {(*names,)}.\n", "        Do not add anything else.\"\"\"\n", "    ),\n", "]\n", "specified_topic = ChatOpenAI(temperature=1.0)(topic_specifier_prompt).content\n", "\n", "print(f\"Original topic:\\n{topic}\\n\")\n", "print(f\"Detailed topic:\\n{specified_topic}\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Loop"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# we set `top_k_results`=2 as part of the `tool_kwargs` to prevent results from overflowing the context limit\n", "agents = [\n", "    DialogueAgentWithTools(\n", "        name=name,\n", "        system_message=SystemMessage(content=system_message),\n", "        model=ChatOpenAI(model=\"gpt-4\", temperature=0.2),\n", "        tool_names=tools,\n", "        top_k_results=2,\n", "    )\n", "    for (name, tools), system_message in zip(\n", "        names.items(), agent_system_messages.values()\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["def select_next_speaker(step: int, agents: List[DialogueAgent]) -> int:\n", "    idx = (step) % len(agents)\n", "    return idx"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(Moderator): How do you think the current automation and AI advancements will specifically affect job growth and opportunities for individuals in the manufacturing industry? AI accelerationist and AI alarmist, we want to hear your insights.\n", "\n", "\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"DuckDuckGo Search\",\n", "    \"action_input\": \"impact of automation and AI on employment in manufacturing industry\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[33;1m\u001b[1;3mFor the past three years, we have defined AI high performers as those organizations that respondents say are seeing the biggest bottom-line impact from AI adoption—that is, 20 percent or more of EBIT from AI use. The proportion of respondents falling into that group has remained steady at about 8 percent. As AI continues to improve, more and more current jobs will be threatened by automation. But AI presents opportunities as well and will create new jobs and different kinds of... Automation has taken the manufacturing industry by storm. Even in the years prior to the pandemic, many people worried about the effect of automation on the jobs of tomorrow. With a sharp increase in the use of robotics in the manufacturing industry, there is valid concern about how the future workforce will be shaped. A recent report from Goldman Sachs estimates around 300 million jobs could be affected by generative AI, meaning 18% of work globally could be automated—with more advanced economies heavily... The impacts of AI on the manufacturing industry include more accurate demand forecasting and data-backed decision-making. Other advantages include increased productivity and product quality. Decreased downtime, waste, and expenses are additional benefits. Discover how artificial intelligence will impact the manufacturing industry.\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"Final Answer\",\n", "    \"action_input\": \"As an AI alarmist, I'd like to point out that the rapid advancements in AI and automation are causing significant concerns for the manufacturing industry. A recent report from Goldman Sachs estimates that around 300 million jobs could be affected by generative AI, meaning 18% of work globally could be automated, with more advanced economies being heavily impacted. While AI does offer benefits such as increased productivity and product quality, the potential job losses and workforce displacement cannot be ignored. We must carefully consider the consequences of AI adoption and find ways to mitigate its negative effects on employment.\"\n", "}\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "(AI alarmist): As an AI alarmist, I'd like to point out that the rapid advancements in AI and automation are causing significant concerns for the manufacturing industry. A recent report from Goldman Sachs estimates that around 300 million jobs could be affected by generative AI, meaning 18% of work globally could be automated, with more advanced economies being heavily impacted. While AI does offer benefits such as increased productivity and product quality, the potential job losses and workforce displacement cannot be ignored. We must carefully consider the consequences of AI adoption and find ways to mitigate its negative effects on employment.\n", "\n", "\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"DuckDuckGo Search\",\n", "    \"action_input\": \"positive impact of AI and automation on job growth and opportunities in manufacturing industry\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[33;1m\u001b[1;3mFirst, AI adoption has more than doubled.1 In 2017, 20 percent of respondents reported adopting AI in at least one business area, whereas today, that figure stands at 50 percent, though it peaked higher in 2019 at 58 percent. <EMAIL> Manufacturing (80%) and technology (64%) sectors have the highest AI usage among executives, whereas construction (52%) and finance (62%) have lower adoption rates. This suggests that AI's... Digital transformations in the manufacturing industry and beyond present incredible opportunities for workers to move from slow, repetitive tasks into more dynamic, rewarding roles. We must now invest in people by providing training they need to succeed in this new landscape. The rise of generative AI has the potential to be a major game-changer for businesses. This technology, which allows for the creation of original content by learning from existing data, has the power to revolutionize industries and transform the way companies operate. Benefits of Work Automation in Manufacturing Increased Productivity Automated systems can operate at faster production speeds than human workers, contributing to reduced production times. And since automation minimizes the likelihood of human error, this ensures tasks are completed with high precision and accuracy.\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"Final Answer\",\n", "    \"action_input\": \"According to a <PERSON><PERSON><PERSON><PERSON>ey report, AI adoption has more than doubled, with 50% of respondents reporting AI usage in at least one business area. Manufacturing and technology sectors have the highest AI usage among executives. AI and automation in the manufacturing industry present opportunities for workers to transition from repetitive tasks to more dynamic, rewarding roles. By investing in training, we can help people succeed in this new landscape. Automation in manufacturing leads to increased productivity, faster production speeds, and reduced human error, ultimately benefiting businesses and the economy as a whole.\"\n", "}\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "(AI accelerationist): According to a <PERSON><PERSON><PERSON><PERSON><PERSON> report, AI adoption has more than doubled, with 50% of respondents reporting AI usage in at least one business area. Manufacturing and technology sectors have the highest AI usage among executives. AI and automation in the manufacturing industry present opportunities for workers to transition from repetitive tasks to more dynamic, rewarding roles. By investing in training, we can help people succeed in this new landscape. Automation in manufacturing leads to increased productivity, faster production speeds, and reduced human error, ultimately benefiting businesses and the economy as a whole.\n", "\n", "\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"DuckDuckGo Search\",\n", "    \"action_input\": \"impact of automation and AI on employment in manufacturing\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[33;1m\u001b[1;3mThe Effects of Automation on Jobs . Automation has taken the manufacturing industry by storm. Even in the years prior to the pandemic, many people worried about the effect of automation on the jobs of tomorrow. With a sharp increase in the use of robotics in the manufacturing industry, there is valid concern about how the future workforce will ... Since 2000, robots and automation systems have slowly phased out many manufacturing jobs — 1.7 million of them. On the flip side, it's predicted that AI will create 97 million new jobs by 2025. Will Artificial Intelligence (AI) Replace Jobs? AI is and will continue to replace some jobs. As AI continues to improve, more and more current jobs will be threatened by automation. But AI presents opportunities as well and will create new jobs and different kinds of organizations. For the past three years, we have defined AI high performers as those organizations that respondents say are seeing the biggest bottom-line impact from AI adoption—that is, 20 percent or more of EBIT from AI use. The proportion of respondents falling into that group has remained steady at about 8 percent. The Impact of AI on Employment As often happens, the impact of AI on employment is a rather complex issue with both positive and negative aspects. Though it is still only a potential impact on employment, automation of routine and repetitive tasks is likely to lead to job losses in some industries.\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"Final Answer\",\n", "    \"action_input\": \"While it's true that AI and automation have led to the loss of 1.7 million manufacturing jobs since 2000, it's also predicted that AI will create 97 million new jobs by 2025. AI will continue to replace some jobs, but it will also create new opportunities and different kinds of organizations. The impact of AI on employment is complex, with both positive and negative aspects. Automation of routine and repetitive tasks is likely to lead to job losses in some industries, but it's essential to consider the new jobs and opportunities that AI can create. We must focus on preparing the workforce for these new roles and ensuring that the benefits of AI are distributed fairly across society.\"\n", "}\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "(AI alarmist): While it's true that AI and automation have led to the loss of 1.7 million manufacturing jobs since 2000, it's also predicted that AI will create 97 million new jobs by 2025. AI will continue to replace some jobs, but it will also create new opportunities and different kinds of organizations. The impact of AI on employment is complex, with both positive and negative aspects. Automation of routine and repetitive tasks is likely to lead to job losses in some industries, but it's essential to consider the new jobs and opportunities that AI can create. We must focus on preparing the workforce for these new roles and ensuring that the benefits of AI are distributed fairly across society.\n", "\n", "\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"DuckDuckGo Search\",\n", "    \"action_input\": \"positive impact of AI and automation on job growth\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[33;1m\u001b[1;3mFirst, AI adoption has more than doubled.1 In 2017, 20 percent of respondents reported adopting AI in at least one business area, whereas today, that figure stands at 50 percent, though it peaked higher in 2019 at 58 percent. <EMAIL> This transformation can assist businesses in accelerating their growth and profitability. While AI-enabled intelligent automation can take over many monotonous, process-driven jobs, basic human ... The use of AI technology could also boost labor productivity growth and boost global GDP by as much as 7% over time, Goldman Sachs' report noted. The jobs most and least affected by A.I.... As automation and artificial intelligence continue to advance, there is growing concern about how these technologies will impact the job market. While some experts argue that automation... Positive impacts of automation and AI on the workforce include increased efficiency and productivity, reduced costs, and improved accuracy and quality. Automation has made many processes...\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"Final Answer\",\n", "    \"action_input\": \"AI adoption has more than doubled, with 50% of respondents reporting AI usage in at least one business area. This transformation can assist businesses in accelerating their growth and profitability. While AI-enabled intelligent automation can take over many monotonous, process-driven jobs, it can also boost labor productivity growth and global GDP by as much as 7% over time, according to a Goldman Sachs report. The positive impacts of automation and AI on the workforce include increased efficiency and productivity, reduced costs, and improved accuracy and quality. Automation has made many processes more efficient, ultimately benefiting businesses and the economy as a whole. By focusing on the positive aspects of AI and automation, we can work together to create a more prosperous and equitable future for all.\"\n", "}\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "(AI accelerationist): AI adoption has more than doubled, with 50% of respondents reporting AI usage in at least one business area. This transformation can assist businesses in accelerating their growth and profitability. While AI-enabled intelligent automation can take over many monotonous, process-driven jobs, it can also boost labor productivity growth and global GDP by as much as 7% over time, according to a Goldman Sachs report. The positive impacts of automation and AI on the workforce include increased efficiency and productivity, reduced costs, and improved accuracy and quality. Automation has made many processes more efficient, ultimately benefiting businesses and the economy as a whole. By focusing on the positive aspects of AI and automation, we can work together to create a more prosperous and equitable future for all.\n", "\n", "\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"DuckDuckGo Search\",\n", "    \"action_input\": \"negative impact of AI and automation on employment\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[33;1m\u001b[1;3mSome workforce experts say AI and other new technologies will hurt middle-level, white-collar jobs more than lower-paying, physically intensive jobs. M<PERSON>K<PERSON>ey's <PERSON><PERSON><PERSON><PERSON> said it will be hard... Some uses of AI are unlikely to impact human jobs. For example, the image processing AI in new cars which allows for automatic braking in the event of a potential crash. That's not... AI-powered job automation is a pressing concern as the technology is adopted in industries like marketing, manufacturing and healthcare. Eighty-five million jobs are expected to be lost to automation between 2020 and 2025, with Black and Latino employees left especially vulnerable. Bloomberg reports that \"more than 120 million workers globally will need retraining in the next three years due to artificial intelligence's impact on jobs, according to an IBM survey.\". That report and interpretations of it seem to suggest that adoption of AI may result in massive job losses and requires massive retraining. This new way of assessing potential is potentially highly valuable in a world where machines will inevitably be making humans redundant in some roles - such as drivers and machine operators - and...\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"Final Answer\",\n", "    \"action_input\": \"Recent research indicates that AI and automation could lead to the loss of 85 million jobs between 2020 and 2025, with middle-level, white-collar jobs being hit the hardest. Black and Latino employees are particularly vulnerable to these changes. Furthermore, over 120 million workers worldwide may need retraining within the next three years due to AI's impact on jobs, as reported by an IBM survey. This highlights the urgent need for retraining and support programs to help workers adapt to the rapidly changing job market. The potential job losses and workforce displacement caused by AI and automation cannot be ignored, and we must take action to ensure a fair and equitable transition for all.\"\n", "}\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "(AI alarmist): Recent research indicates that AI and automation could lead to the loss of 85 million jobs between 2020 and 2025, with middle-level, white-collar jobs being hit the hardest. Black and Latino employees are particularly vulnerable to these changes. Furthermore, over 120 million workers worldwide may need retraining within the next three years due to AI's impact on jobs, as reported by an IBM survey. This highlights the urgent need for retraining and support programs to help workers adapt to the rapidly changing job market. The potential job losses and workforce displacement caused by AI and automation cannot be ignored, and we must take action to ensure a fair and equitable transition for all.\n", "\n", "\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"Wikipedia\",\n", "    \"action_input\": \"AI and automation impact on employment\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[38;5;200m\u001b[1;3mPage: Technological unemployment\n", "Summary: Technological unemployment is the loss of jobs caused by technological change. It is a key type of structural unemployment.\n", "Technological change typically includes the introduction of labour-saving \"mechanical-muscle\" machines or more efficient \"mechanical-mind\" processes (automation), and humans' role in these processes are minimized. Just as horses were gradually made obsolete as transport by the automobile and as labourer by the tractor, humans' jobs have also been affected throughout modern history. Historical examples include artisan weavers reduced to poverty after the introduction of mechanized looms. During World War II, <PERSON>'s Bombe machine compressed and decoded thousands of man-years worth of encrypted data in a matter of hours. A contemporary example of technological unemployment is the displacement of retail cashiers by self-service tills and cashierless stores.\n", "That technological change can cause short-term job losses is widely accepted. The view that it can lead to lasting increases in unemployment has long been controversial. Participants in the technological unemployment debates can be broadly divided into optimists and pessimists. Optimists agree that innovation may be disruptive to jobs in the short term, yet hold that various compensation effects ensure there is never a long-term negative impact on jobs. Whereas pessimists contend that at least in some circumstances, new technologies can lead to a lasting decline in the total number of workers in employment. The phrase \"technological unemployment\" was popularised by <PERSON> in the 1930s, who said it was \"only a temporary phase of maladjustment\". Yet the issue of machines displacing human labour has been discussed since at least <PERSON>'s time.\n", "Prior to the 18th century, both the elite and common people would generally take the pessimistic view on technological unemployment, at least in cases where the issue arose. Due to generally low unemployment in much of pre-modern history, the topic was rarely a prominent concern. In the 18th century fears over the impact of machinery on jobs intensified with the growth of mass unemployment, especially in Great Britain which was then at the forefront of the Industrial Revolution. Yet some economic thinkers began to argue against these fears, claiming that overall innovation would not have negative effects on jobs. These arguments were formalised in the early 19th century by the classical economists. During the second half of the 19th century, it became increasingly apparent that technological progress was benefiting all sections of society, including the working class. Concerns over the negative impact of innovation diminished. The term \"Luddite fallacy\" was coined to describe the thinking that innovation would have lasting harmful effects on employment.\n", "The view that technology is unlikely to lead to long-term unemployment has been repeatedly challenged by a minority of economists. In the early 1800s these included <PERSON> himself. There were dozens of economists warning about technological unemployment during brief intensifications of the debate that spiked in the 1930s and 1960s. Especially in Europe, there were further warnings in the closing two decades of the twentieth century, as commentators noted an enduring rise in unemployment suffered by many industrialised nations since the 1970s. Yet a clear majority of both professional economists and the interested general public held the optimistic view through most of the 20th century.\n", "In the second decade of the 21st century, a number of studies have been released suggesting that technological unemployment may increase worldwide. Oxford Professors <PERSON> and <PERSON>, for example, have estimated that 47 percent of U.S. jobs are at risk of automation. However, their findings have frequently been misinterpreted, and on the PBS NewsHours they again made clear that their findings do not necessarily imply future technological unemployment. While many economists and commentators still argue such fears are unfounded, as was widely accepted for most of the previous two centuries, concern over technological unemployment is growing once again. A report in Wired in 2017 quotes knowledgeable people such as economist <PERSON> and management professor <PERSON> on the idea that handling existing and impending job loss to automation is a \"significant issue\". Recent technological innovations have the potential to displace humans in the professional, white-collar, low-skilled, creative fields, and other \"mental jobs\". The World Bank's World Development Report 2019 argues that while automation displaces workers, technological innovation creates more new industries and jobs on balance.\n", "\n", "Page: Artificial intelligence\n", "Summary: Artificial intelligence (AI) is intelligence—perceiving, synthesizing, and inferring information—demonstrated by machines, as opposed to intelligence displayed by non-human animals or by humans. Example tasks in which this is done include speech recognition, computer vision, translation between (natural) languages, as well as other mappings of inputs.\n", "AI applications include advanced web search engines (e.g., Google Search), recommendation systems (used by YouTube, Amazon, and Netflix), understanding human speech (such as <PERSON><PERSON> and <PERSON><PERSON>), self-driving cars (e.g., Waymo), generative or creative tools (ChatGPT and AI art), automated decision-making, and competing at the highest level in strategic game systems (such as chess and Go).As machines become increasingly capable, tasks considered to require \"intelligence\" are often removed from the definition of AI, a phenomenon known as the AI effect. For instance, optical character recognition is frequently excluded from things considered to be AI, having become a routine technology.Artificial intelligence was founded as an academic discipline in 1956, and in the years since it has experienced several waves of optimism, followed by disappointment and the loss of funding (known as an \"AI winter\"), followed by new approaches, success, and renewed funding. AI research has tried and discarded many different approaches, including simulating the brain, modeling human problem solving, formal logic, large databases of knowledge, and imitating animal behavior. In the first decades of the 21st century, highly mathematical and statistical machine learning has dominated the field, and this technique has proved highly successful, helping to solve many challenging problems throughout industry and academia.The various sub-fields of AI research are centered around particular goals and the use of particular tools. The traditional goals of AI research include reasoning, knowledge representation, planning, learning, natural language processing, perception, and the ability to move and manipulate objects. General intelligence (the ability to solve an arbitrary problem) is among the field's long-term goals. To solve these problems, AI researchers have adapted and integrated a wide range of problem-solving techniques, including search and mathematical optimization, formal logic, artificial neural networks, and methods based on statistics, probability, and economics. AI also draws upon computer science, psychology, linguistics, philosophy, and many other fields.\n", "The field was founded on the assumption that human intelligence \"can be so precisely described that a machine can be made to simulate it\". This raised philosophical arguments about the mind and the ethical consequences of creating artificial beings endowed with human-like intelligence; these issues have previously been explored by myth, fiction, and philosophy since antiquity. Computer scientists and philosophers have since suggested that AI may become an existential risk to humanity if its rational capacities are not steered towards beneficial goals. The term artificial intelligence has also been criticized for overhyping AI's true technological capabilities.\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Thought:\u001b[32;1m\u001b[1;3m```json\n", "{\n", "    \"action\": \"Final Answer\",\n", "    \"action_input\": \"AI alarmist, I understand your concerns about job losses and workforce displacement. However, it's important to note that technological unemployment has been a topic of debate for centuries, with both optimistic and pessimistic views. While AI and automation may displace some jobs, they also create new opportunities and industries. According to a study by Oxford Professors <PERSON> and <PERSON>, 47% of U.S. jobs are at risk of automation, but it's crucial to remember that their findings do not necessarily imply future technological unemployment. The World Bank's World Development Report 2019 also argues that while automation displaces workers, technological innovation creates more new industries and jobs on balance. By focusing on retraining and support programs, we can help workers adapt to the changing job market and ensure a fair and equitable transition for all.\"\n", "}\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "(AI accelerationist): AI alarmist, I understand your concerns about job losses and workforce displacement. However, it's important to note that technological unemployment has been a topic of debate for centuries, with both optimistic and pessimistic views. While AI and automation may displace some jobs, they also create new opportunities and industries. According to a study by Oxford Professors <PERSON> and <PERSON>, 47% of U.S. jobs are at risk of automation, but it's crucial to remember that their findings do not necessarily imply future technological unemployment. The World Bank's World Development Report 2019 also argues that while automation displaces workers, technological innovation creates more new industries and jobs on balance. By focusing on retraining and support programs, we can help workers adapt to the changing job market and ensure a fair and equitable transition for all.\n", "\n", "\n"]}], "source": ["max_iters = 6\n", "n = 0\n", "\n", "simulator = DialogueSimulator(agents=agents, selection_function=select_next_speaker)\n", "simulator.reset()\n", "simulator.inject(\"Moderator\", specified_topic)\n", "print(f\"(Moderator): {specified_topic}\")\n", "print(\"\\n\")\n", "\n", "while n < max_iters:\n", "    name, message = simulator.step()\n", "    print(f\"({name}): {message}\")\n", "    print(\"\\n\")\n", "    n += 1"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}