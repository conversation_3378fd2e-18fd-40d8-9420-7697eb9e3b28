{"cells": [{"cell_type": "markdown", "id": "0ddfef23-3c74-444c-81dd-6753722997fa", "metadata": {}, "source": ["# Plan-and-execute\n", "\n", "Plan-and-execute agents accomplish an objective by first planning what to do, then executing the sub tasks. This idea is largely inspired by [BabyAGI](https://github.com/yoheinakajima/babyagi) and then the [\"Plan-and-Solve\" paper](https://arxiv.org/abs/2305.04091).\n", "\n", "The planning is almost always done by an LLM.\n", "\n", "The execution is usually done by a separate agent (equipped with tools)."]}, {"cell_type": "markdown", "id": "a7ecb22a-7009-48ec-b14e-f0fa5aac1cd0", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 3, "id": "5fbbd4ee-bfe8-4a25-afe4-8d1a552a3d2e", "metadata": {}, "outputs": [], "source": ["from langchain.chains import LLMMathChain\n", "from langchain_community.utilities import DuckDuckGoSearchAPIWrapper\n", "from langchain_core.tools import Tool\n", "from langchain_experimental.plan_and_execute import (\n", "    PlanAndExecute,\n", "    load_agent_executor,\n", "    load_chat_planner,\n", ")\n", "from langchain_openai import ChatOpenAI, OpenAI"]}, {"cell_type": "markdown", "id": "e0e995e5-af9d-4988-bcd0-467a2a2e18cd", "metadata": {}, "source": ["## Tools"]}, {"cell_type": "code", "execution_count": 4, "id": "1d789f4e-54e3-4602-891a-f076e0ab9594", "metadata": {}, "outputs": [], "source": ["search = DuckDuckGoSearchAPIWrapper()\n", "llm = OpenAI(temperature=0)\n", "llm_math_chain = LLMMathChain.from_llm(llm=llm, verbose=True)\n", "tools = [\n", "    Tool(\n", "        name=\"Search\",\n", "        func=search.run,\n", "        description=\"useful for when you need to answer questions about current events\",\n", "    ),\n", "    Tool(\n", "        name=\"Calculator\",\n", "        func=llm_math_chain.run,\n", "        description=\"useful for when you need to answer questions about math\",\n", "    ),\n", "]"]}, {"cell_type": "markdown", "id": "04dc6452-a07f-49f9-be12-95be1e2afccc", "metadata": {}, "source": ["## Planner, Executor, and Agent\n"]}, {"cell_type": "code", "execution_count": 5, "id": "d8f49c03-c804-458b-8122-c92b26c7b7dd", "metadata": {}, "outputs": [], "source": ["model = ChatOpenAI(temperature=0)\n", "planner = load_chat_planner(model)\n", "executor = load_agent_executor(model, tools, verbose=True)\n", "agent = PlanAndExecute(planner=planner, executor=executor)"]}, {"cell_type": "markdown", "id": "78ba03dd-0322-4927-b58d-a7e2027fdbb3", "metadata": {}, "source": ["## Run example"]}, {"cell_type": "code", "execution_count": 6, "id": "a57f7efe-7866-47a7-bce5-9c7b1047964e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction:\n", "{\n", "  \"action\": \"Search\",\n", "  \"action_input\": \"current prime minister of the UK\"\n", "}\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction:\n", "```\n", "{\n", "  \"action\": \"Search\",\n", "  \"action_input\": \"current prime minister of the UK\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mBottom right: <PERSON><PERSON> is the current prime minister and the first non-white prime minister. The prime minister of the United Kingdom is the principal minister of the crown of His Majesty's Government, and the head of the British Cabinet. 3 min. British Prime Minister <PERSON><PERSON> asserted his stance on gender identity in a speech Wednesday, stating it was \"common sense\" that \"a man is a man and a woman is a woman\" — a ... The former chancellor <PERSON><PERSON> is the UK's new prime minister. Here's what you need to know about him. He won after running for the second time this year He lost to <PERSON> in September,... Isaeli Prime Minister <PERSON> spoke with US President <PERSON> on Wednesday, the prime minister's office said in a statement. <PERSON><PERSON><PERSON> \"thanked the President for the powerful words of ... By <PERSON><PERSON><PERSON>/London Updated: October 25, 2022 12:56 PM EDT | Originally published: October 24, 2022 9:17 AM EDT S top me if you've heard this one before: After a tumultuous period of political...\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3mThe search results indicate that <PERSON><PERSON> is the current prime minister of the UK. However, it's important to note that this information may not be accurate or up to date.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction:\n", "```\n", "{\n", "  \"action\": \"Search\",\n", "  \"action_input\": \"current age of the prime minister of the UK\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mHow old is <PERSON><PERSON>? Mr <PERSON><PERSON> was born on 12 May, 1980, making him 42 years old. He first became an MP in 2015, aged 34, and has served the constituency of Richmond in Yorkshire ever since. He... Prime Ministers' ages when they took office From oldest to youngest, the ages of the PMs were as follows: <PERSON> - 65 years old <PERSON> - 64 years old <PERSON> - 62 years... <PERSON> USA TODAY Just a few days after <PERSON> resigned as prime minister, the UK has a new prime minister. <PERSON><PERSON>, who lasted a mere 45 days in office, will be replaced by <PERSON><PERSON>... Advertisement <PERSON><PERSON> is the youngest British prime minister of modern times. Mr<PERSON> is 42 and started out in Parliament in 2015. <PERSON><PERSON> was appointed as chancellor of the Exchequer... The first prime minister of the current United Kingdom of Great Britain and Northern Ireland upon its effective creation in 1922 (when 26 Irish counties seceded and created the Irish Free State) was Bonar Law, [10] although the country was not renamed officially until 1927, when <PERSON> was the serving prime minister. [11]\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3mBased on the search results, it seems that <PERSON><PERSON> is the current prime minister of the UK. However, I couldn't find any specific information about his age. Would you like me to search again for the current age of the prime minister?\n", "\n", "Action:\n", "```\n", "{\n", "  \"action\": \"Search\",\n", "  \"action_input\": \"age of R<PERSON>\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3m<PERSON><PERSON> is 42 years old, making him the youngest person to hold the office of prime minister in modern times. How tall is <PERSON><PERSON>? How Old Is <PERSON><PERSON>? <PERSON><PERSON> was born on May 12, 1980, in Southampton, England. Parents and <PERSON><PERSON> <PERSON>'s parents were born to Indian-origin families in East Africa before... Born on May 12, 1980, <PERSON><PERSON> is currently 42 years old. He has been a member of parliament since 2015 where he was an MP for Richmond and has served in roles including Chief Secretary to the Treasury and the Chancellor of Exchequer while <PERSON> was PM. <PERSON>, 42, is the daughter of the Indian billionaire NR <PERSON>, often described as the <PERSON> of India, who founded the software company Infosys. According to reports, his... <PERSON><PERSON> became the first non-White person to lead the country and, at age 42, the youngest to take on the role in more than a century. Like most politicians, <PERSON><PERSON> is revered by some and...\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3mBased on the search results, <PERSON><PERSON> is currently 42 years old. He was born on May 12, 1980.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: To calculate the age raised to the power of 0.43, I can use the calculator tool.\n", "\n", "Action:\n", "```json\n", "{\n", "  \"action\": \"Calculator\",\n", "  \"action_input\": \"42^0.43\"\n", "}\n", "```\u001b[0m\n", "\n", "\u001b[1m> Entering new LLMMathChain chain...\u001b[0m\n", "42^0.43\u001b[32;1m\u001b[1;3m```text\n", "42**0.43\n", "```\n", "...numexpr.evaluate(\"42**0.43\")...\n", "\u001b[0m\n", "Answer: \u001b[33;1m\u001b[1;3m4.9888126515157\u001b[0m\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "Observation: \u001b[33;1m\u001b[1;3mAnswer: 4.9888126515157\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3mThe age raised to the power of 0.43 is approximately 4.9888126515157.\n", "\n", "Final Answer:\n", "```json\n", "{\n", "  \"action\": \"Final Answer\",\n", "  \"action_input\": \"The age raised to the power of 0.43 is approximately 4.9888126515157.\"\n", "}\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction:\n", "```\n", "{\n", "  \"action\": \"Final Answer\",\n", "  \"action_input\": \"The current prime minister of the UK is <PERSON><PERSON>. His age raised to the power of 0.43 is approximately 4.9888126515157.\"\n", "}\n", "```\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'The current prime minister of the UK is <PERSON><PERSON>. His age raised to the power of 0.43 is approximately 4.9888126515157.'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["agent.run(\n", "    \"Who is the current prime minister of the UK? What is their current age raised to the 0.43 power?\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0ef78a07-1a2a-46f8-9bc9-ae45f9bd706c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "poetry-venv", "language": "python", "name": "poetry-venv"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 5}