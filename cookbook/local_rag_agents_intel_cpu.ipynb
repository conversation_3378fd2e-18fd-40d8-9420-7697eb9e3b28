{"cells": [{"cell_type": "markdown", "id": "9e7a7c86", "metadata": {}, "source": ["### Custom RAG Agent Workflow with Open Source LLMs Running Locally on Intel CPU"]}, {"cell_type": "markdown", "id": "f309f56d-1db4-4e03-870e-a2a6f5ee4dc5", "metadata": {}, "source": ["Author - <PERSON><PERSON><PERSON> (<EMAIL>)"]}, {"cell_type": "markdown", "id": "0af01c3c-c42a-4ba5-95fa-4b83fd77fe9d", "metadata": {}, "source": ["This notebook demonstrates a Retrieval-Augmented Generation (RAG) agent that routes questions through two paths to find answers. The agent generates answers based on documents retrieved from either the vector database or web search. If the vector database lacks relevant information, the agent opts for web search. Open-source models for LLM and embeddings are used locally on an Intel Xeon CPU to execute this pipeline."]}, {"cell_type": "markdown", "id": "8b50e68f", "metadata": {}, "source": ["<figure style=\"text-align: center;\">\n", "<figcaption style=\"text-align: center;\">Flow chart for the Custom RAG Agent Workflow</figcaption>\n", "<img src=\"data:image/png;base64,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\" />"]}, {"cell_type": "markdown", "id": "24f76969", "metadata": {}, "source": ["Install required libraries in a conda or venv environment"]}, {"cell_type": "code", "execution_count": 1, "id": "746ae008", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip available: \u001b[0m\u001b[31;49m22.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m24.3.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["!pip install --upgrade --quiet  tiktoken scikit-learn gpt4all langchain langchain-community langchain-core langchain_nomic langchain_ollama langgraph "]}, {"cell_type": "markdown", "id": "399f7e2e", "metadata": {}, "source": ["In Linux systems, use following commands to install Ollama and download Llama 3.1 model locally.\n", "```\n", "curl -fsSL https://ollama.com/install.sh | sh\n", "ollama run llama3.1\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "c7ea62fe-7ea0-4e98-95e5-df79599b1545", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This cell asks you to set up environment variables for a local RAG (Retrieval-Augmented Generation) agent.\n", "\n", "Environment Variables:\n", "- USER_AGENT: Specifies the user agent string to be used.\n", "- LANGSMITH_TRACING: Enables or disables tracing for LangChain.\n", "- LANGSMITH_API_KEY: API key for accessing LangChain services.\n", "- TAVILY_API_KEY: API key for accessing Tavily services.\n", "\"\"\"\n", "import os\n", "\n", "os.environ[\"USER_AGENT\"] = \"myagent\"\n", "os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "os.environ[\"LANGSMITH_API_KEY\"] = \"xxxx\"\n", "os.environ[\"TAVILY_API_KEY\"] = \"tvly-xxxx\""]}, {"cell_type": "markdown", "id": "f4fe714b", "metadata": {}, "source": ["Use local embedding model to store documents in vector database"]}, {"cell_type": "code", "execution_count": 3, "id": "8d1b3be3-b150-4e39-aecf-f4a51a5eb358", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Failed to load libllamamodel-mainline-cuda-avxonly.so: dlopen: libcudart.so.11.0: cannot open shared object file: No such file or directory\n", "Failed to load libllamamodel-mainline-cuda.so: dlopen: libcudart.so.11.0: cannot open shared object file: No such file or directory\n"]}], "source": ["\"\"\"\n", "This cell performs the following tasks:\n", "\n", "1. Imports necessary modules and classes from langchain and related libraries.\n", "2. Defines a list of URLs from IRS to load tax related documents from.\n", "3. Loads documents from the specified URLs using the WebBaseLoader.\n", "4. Flattens the list of loaded documents.\n", "5. Initializes a RecursiveCharacterTextSplitter with a specified chunk size and overlap.\n", "6. Splits the loaded documents into chunks using the text splitter.\n", "7. Initializes an SKLearnVectorStore with the document chunks embedded using  local embeddings model \"nomic-embed-text-v1.5\" from NomicEmbeddings.\n", "8. Converts the vector store into a retriever with a specified number of nearest neighbors (k=4).\n", "\n", "Modules and Classes:\n", "- RecursiveCharacterTextSplitter: Splits text into chunks based on character count.\n", "- WebBaseLoader: Loads documents from web URLs.\n", "- SKLearnVectorStore: Stores document vectors for retrieval.\n", "- NomicEmbeddings: Generates embeddings for documents.\n", "- tool: Utility for defining tools.\n", "\n", "Variables:\n", "- urls: List of URLs to load documents from.\n", "- docs: List of loaded documents from the URLs.\n", "- docs_list: Flattened list of loaded documents.\n", "- text_splitter: Instance of RecursiveCharacterTextSplitter.\n", "- doc_splits: List of document chunks.\n", "- vectorstore: Instance of SKLearnVectorStore.\n", "- retriever: Retriever instance for querying the vector store.\n", "\"\"\"\n", "\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_community.vectorstores import SKLearnVectorStore\n", "from langchain_core.tools import tool\n", "from langchain_nomic.embeddings import NomicEmbeddings\n", "\n", "# List of URLs to load documents from\n", "urls = [\n", "    \"https://www.irs.gov/newsroom/irs-releases-tax-inflation-adjustments-for-tax-year-2025\",\n", "    \"https://www.irs.gov/newsroom/401k-limit-increases-to-23500-for-2025-ira-limit-remains-7000\",\n", "    \"https://www.irs.gov/newsroom/tax-basics-understanding-the-difference-between-standard-and-itemized-deductions\",\n", "]\n", "\n", "# Load documents from the URLs\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "# Initialize a text splitter with specified chunk size and overlap\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=250, chunk_overlap=0\n", ")\n", "\n", "# Split the documents into chunks\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Add the document chunks to the \"vector store\" using NomicEmbeddings\n", "vectorstore = SKLearnVectorStore.from_documents(\n", "    documents=doc_splits,\n", "    embedding=NomicEmbeddings(\n", "        model=\"nomic-embed-text-v1.5\", inference_mode=\"local\", device=\"cpu\"\n", "    ),\n", "    # embedding=OpenAIEmbeddings(),\n", ")\n", "retriever = vectorstore.as_retriever(k=4)"]}, {"cell_type": "code", "execution_count": 4, "id": "f8d54464-37b9-4b48-877e-38fc7620c1ff", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This cell imports the necessary modules and initializes the web search tool for the LLM.\n", "\n", "Modules:\n", "- `Document` from `langchain.schema`: Represents a document schema.\n", "- `TavilySearchResults` from `langchain_community.tools.tavily_search`: Provides functionality to perform web search by LLM if required.\n", "\n", "Initialization:\n", "- `web_search_tool`: An instance of `TavilySearchResults` used to perform web searches.\n", "\"\"\"\n", "from langchain.schema import Document\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "\n", "web_search_tool = TavilySearchResults()"]}, {"cell_type": "code", "execution_count": 5, "id": "36dad7e6-3752-4939-be70-f87d23d90d6f", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This cell sets up a question-answering assistant using the LangChain library. \n", "1. It imports necessary modules: `ChatOllama` for the language model, `PromptTemplate` for creating prompts, and `StrOutputParser` for parsing the output.\n", "2. It defines a prompt template that instructs the assistant to answer questions concisely using provided documents.\n", "3. It initializes the `ChatOllama` language model with specific parameters.\n", "4. It creates a chain (`rag_chain`) that combines the prompt template, language model, and output parser to process and generate answers.\n", "This setup is essential for enabling the assistant to handle question-answering tasks effectively.\n", "\"\"\"\n", "from langchain.prompts import PromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_ollama import ChatOllama\n", "\n", "prompt = PromptTemplate(\n", "    template=\"\"\"You are an assistant for question-answering tasks. \n", "    \n", "    Use the following documents to answer the question. \n", "    \n", "    If you don't know the answer, just say that you don't know. \n", "    \n", "    Use three sentences maximum and keep the answer concise:\n", "    Question: {question} \n", "    Documents: {documents} \n", "    Answer: \n", "    \"\"\",\n", "    input_variables=[\"question\", \"documents\"],\n", ")\n", "\n", "llm = ChatOllama(\n", "    model=\"llama3.1\",\n", "    temperature=0,\n", ")\n", "\n", "rag_chain = prompt | llm | StrOutputParser()"]}, {"cell_type": "code", "execution_count": 6, "id": "0affbee8-30c4-4dd0-a95a-d8ab571b55c6", "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This cell sets up a prompt template and a retrieval grader for assessing the relevance of a retrieved document to a user question.\n", "\n", "Functionality:\n", "- Imports the necessary JsonOutputParser from langchain_core.output_parsers.\n", "- Defines a PromptTemplate that instructs a grader to assess the relevance of a document to a user question.\n", "- The grader uses a simple binary scoring system ('yes' or 'no') to indicate relevance.\n", "- The result is provided as a JSON object with a single key 'score'.\n", "- Combines the prompt template with a language model (llm) and the JsonOutputParser to create the retrieval_grader.\n", "\n", "The retrieval_grader can be used in the workflow to filter out erroneous document retrievals based on their relevance to user questions.\n", "\"\"\"\n", "from langchain_core.output_parsers import JsonOutputParser\n", "\n", "prompt = PromptTemplate(\n", "    template=\"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n \n", "    Here is the retrieved document: \\n\\n {document} \\n\\n\n", "    Here is the user question: {question} \\n\n", "    If the document contains keywords related to the user question, grade it as relevant. \\n\n", "    It does not need to be a stringent test. The goal is to filter out erroneous retrievals. \\n\n", "    Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question. \\n\n", "    Provide the binary score as a JSON with a single key 'score' and no premable or explanation.\"\"\",\n", "    input_variables=[\"question\", \"document\"],\n", ")\n", "\n", "retrieval_grader = prompt | llm | JsonOutputParser()"]}, {"cell_type": "code", "execution_count": 7, "id": "d672ffdf", "metadata": {}, "outputs": [], "source": ["# This cell defines the state of the graph and imports necessary modules for graph visualization.\n", "# It includes a TypedDict class `GraphState` that represents the state of the graph with attributes\n", "# such as question, generation, search, documents, and steps. This state will be used to manage\n", "# the workflow of the RAG agent.\n", "\n", "from IPython.display import Image, display\n", "from langgraph.graph import END, START, StateGraph\n", "from typing_extensions import List, TypedDict\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of our graph.\n", "\n", "    Attributes:\n", "        question: question\n", "        generation: LLM generation\n", "        search: whether to add search\n", "        documents: list of documents\n", "    \"\"\"\n", "\n", "    question: str\n", "    generation: str\n", "    search: str\n", "    documents: List[str]\n", "    steps: List[str]"]}, {"cell_type": "code", "execution_count": 8, "id": "2f26efee", "metadata": {}, "outputs": [], "source": ["# This cell contains the core functions for the document retrieval and answer generation pipeline.\n", "# The functions are designed to work with a state dictionary that maintains the current state of the process.\n", "\n", "\n", "def retrieve(state):\n", "    \"\"\"\n", "    Retrieve documents\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, documents, that contains retrieved documents\n", "    \"\"\"\n", "    question = state[\"question\"]\n", "    documents = retriever.invoke(question)\n", "    steps = state[\"steps\"]\n", "    steps.append(\"retrieve_documents\")\n", "    return {\"documents\": documents, \"question\": question, \"steps\": steps}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation, that contains LLM generation\n", "    \"\"\"\n", "\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "    generation = rag_chain.invoke({\"documents\": documents, \"question\": question})\n", "    steps = state[\"steps\"]\n", "    steps.append(\"generate_answer\")\n", "    return {\n", "        \"documents\": documents,\n", "        \"question\": question,\n", "        \"generation\": generation,\n", "        \"steps\": steps,\n", "    }\n", "\n", "\n", "def grade_documents(state):\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with only filtered relevant documents\n", "    \"\"\"\n", "\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "    steps = state[\"steps\"]\n", "    steps.append(\"grade_document_retrieval\")\n", "    filtered_docs = []\n", "    search = \"No\"\n", "    for d in documents:\n", "        score = retrieval_grader.invoke(\n", "            {\"question\": question, \"document\": d.page_content}\n", "        )\n", "        grade = score[\"score\"]\n", "        if grade == \"yes\":\n", "            filtered_docs.append(d)\n", "        else:\n", "            search = \"Yes\"\n", "            continue\n", "    return {\n", "        \"documents\": filtered_docs,\n", "        \"question\": question,\n", "        \"search\": search,\n", "        \"steps\": steps,\n", "    }\n", "\n", "\n", "def web_search(state):\n", "    \"\"\"\n", "    Web search based on the re-phrased question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with appended web results\n", "    \"\"\"\n", "\n", "    question = state[\"question\"]\n", "    documents = state.get(\"documents\", [])\n", "    steps = state[\"steps\"]\n", "    steps.append(\"web_search\")\n", "    web_results = web_search_tool.invoke({\"query\": question})\n", "    documents.extend(\n", "        [\n", "            Document(page_content=d[\"content\"], metadata={\"url\": d[\"url\"]})\n", "            for d in web_results\n", "        ]\n", "    )\n", "    return {\"documents\": documents, \"question\": question, \"steps\": steps}\n", "\n", "\n", "def decide_to_generate(state):\n", "    \"\"\"\n", "    Determines whether to generate an answer, or re-generate a question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Binary decision for next node to call\n", "    \"\"\"\n", "    search = state[\"search\"]\n", "    if search == \"Yes\":\n", "        return \"search\"\n", "    else:\n", "        return \"generate\""]}, {"cell_type": "code", "execution_count": 11, "id": "e056c4c8-fb62-4524-bb38-11b8c2a20326", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Graph\n", "\"\"\"\n", "This cell defines and builds a state graph workflow for the agent pipeline described earlier.\n", "\n", "The workflow consists of the following nodes:\n", "- \"retrieve\": Retrieves documents from the vector database.\n", "- \"grade_documents\": Grades the retrieved documents.\n", "- \"generate\": Generates output based on the graded documents.\n", "- \"web_search\": Performs a web search if needed.\n", "\n", "The workflow is constructed as follows:\n", "1. The entry point is set to the \"retrieve\" node. so the first step is to retrieve similar documents from the vector database.\n", "2. An edge is added from \"retrieve\" to \"grade_documents\".\n", "3. Conditional edges are added from \"grade_documents\" to either \"web_search\" or \"generate\" based on the decision function `decide_to_generate`.\n", "4. An edge is added from \"web_search\" to \"generate\".\n", "5. An edge is added from \"generate\" to the end of the workflow.\n", "\n", "Finally, the workflow is compiled into a custom graph and displayed as a Mermaid diagram.\n", "\"\"\"\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # generate\n", "workflow.add_node(\"web_search\", web_search)  # web search\n", "\n", "# Build graph\n", "workflow.set_entry_point(\"retrieve\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\"search\": \"web_search\", \"generate\": \"generate\"},\n", ")\n", "workflow.add_edge(\"web_search\", \"generate\")\n", "workflow.add_edge(\"generate\", END)\n", "\n", "custom_graph = workflow.compile()\n", "\n", "display(Image(custom_graph.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 12, "id": "f26919fb-85ac-4afc-aaf7-cbb222dcd737", "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "\n", "def predict_custom_agent_answer(example: dict):\n", "    # This cell defines a function to predict the answer from a custom agent based on the provided example input.\n", "    \"\"\"\n", "    Predicts the answer from a custom agent based on the provided example input.\n", "\n", "    Args:\n", "        example (dict): A dictionary containing the input question under the key \"input\".\n", "\n", "    Returns:\n", "        dict: A dictionary containing the response generated by the custom agent under the key \"response\",\n", "              and the steps taken during the generation process under the key \"steps\".\n", "\n", "    The `config` dictionary is used to pass configuration settings to the custom graph.\n", "    In this case, it includes a unique `thread_id` generated using `uuid.uuid4()`.\n", "    The `thread_id` ensures that each invocation of the function is uniquely identifiable,\n", "    which can be useful for tracing and debugging purposes.\n", "    \"\"\"\n", "\n", "    config = {\"configurable\": {\"thread_id\": str(uuid.uuid4())}}\n", "\n", "    state_dict = custom_graph.invoke(\n", "        {\"question\": example[\"input\"], \"steps\": []}, config\n", "    )\n", "\n", "    return {\"response\": state_dict[\"generation\"], \"steps\": state_dict[\"steps\"]}"]}, {"cell_type": "code", "execution_count": 13, "id": "5261f17e-3b6a-43df-ad5d-17ad9639e8dd", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'response': 'The standard deduction is a fixed amount that most taxpayers can claim, while itemized deductions are specific expenses like mortgage interest, charitable donations, and medical expenses that can be deducted from taxable income. Taxpayers choose the option that gives them the lowest overall tax.',\n", " 'steps': ['retrieve_documents',\n", "  'grade_document_retrieval',\n", "  'generate_answer']}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\"\n", "# Here we define an example input question about the difference between standard deduction and itemized deduction,\n", "# and then uses the `predict_custom_agent_answer` function to generate a response based on the input and show it.\n", "# Since, this question is related to tax deductions, the agent should provide an answer based on the loaded tax documents.\n", "\"\"\"\n", "example = {\n", "    \"input\": \"What is the difference between standard deduction and itemized deduction?\"\n", "}\n", "response = predict_custom_agent_answer(example)\n", "response"]}, {"cell_type": "code", "execution_count": 14, "id": "627e38d9-3e0a-4094-b1fd-917fb89cc5bb", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'response': 'India won the 2024 cricket world cup and <PERSON><PERSON><PERSON> was named Player of the Match for. The final match was played between India and South Africa on June 29, 2024. India defeated South Africa by 7 runs to win their second T20 World Cup title.',\n", " 'steps': ['retrieve_documents',\n", "  'grade_document_retrieval',\n", "  'web_search',\n", "  'generate_answer']}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\"\n", "# Here we define another example input question about the sports event,\n", "# and then uses the `predict_custom_agent_answer` function to generate a response based on the input and show it.\n", "# Since, this question is NOT related to tax deductions, the agent should provide an answer based on the documents returned from web search.\n", "\"\"\"\n", "example = {\"input\": \"Who won the 2024 cricket world cup and who was the MVP in final?\"}\n", "response = predict_custom_agent_answer(example)\n", "response"]}, {"cell_type": "markdown", "id": "2caa78d6-f2aa-41eb-9298-f16ba6e467ba", "metadata": {}, "source": ["As demonstrated in the previous examples, the RAG agent routes the control flow through web search to generate answers for non-TAX related questions. For TAX related queries, it uses documents retrieved from the vector database."]}, {"cell_type": "code", "execution_count": null, "id": "87a59300-c8ab-4281-9a31-25d37a5149f3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "test-env-langchain", "language": "python", "name": "test-env-langchain"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}