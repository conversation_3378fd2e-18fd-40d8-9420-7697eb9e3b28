{"cells": [{"cell_type": "markdown", "id": "62ee82e4-2ad8-498b-8438-fac388afe1a2", "metadata": {}, "source": ["Press Releases Data\n", "=\n", "\n", "Press Releases data powered by [Kay.ai](https://kay.ai).\n", "\n", ">Press releases are used by companies to announce something noteworthy, including product launches, financial performance reports, partnerships, and other significant news. They are widely used by analysts to track corporate strategy, operational updates and financial performance.\n", "Kay.ai obtains press releases of all US public companies from a variety of sources, which include the company's official press room and partnerships with various data API providers. \n", "This data is updated till Sept 30th for free access, if you want to access the real-time feed, reach out to <NAME_EMAIL> or [tweet at us](https://twitter.com/vishal<PERSON><PERSON>_)"]}, {"cell_type": "markdown", "id": "8183d85d-365f-4672-a963-52b533547de0", "metadata": {}, "source": ["Setup\n", "=\n", "\n", "First you will need to install the `kay` package. You will also need an API key: you can get one for free at [https://kay.ai](https://kay.ai/). Once you have an API key, you must set it as an environment variable `KAY_API_KEY`.\n", "\n", "In this example we're going to use the `KayAiRetriever`. Take a look at the [kay notebook](/docs/integrations/retrievers/kay) for more detailed information for the parmeters that it accepts."]}, {"cell_type": "markdown", "id": "02ec21c7-49fe-4844-b58a-bf064ad40b2a", "metadata": {}, "source": ["Examples\n", "="]}, {"cell_type": "code", "execution_count": 1, "id": "bf0395f7-6ebe-4136-8b0d-00b9dea3becd", "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": [" ········\n", " ········\n"]}], "source": ["# Setup API keys for Kay and OpenAI\n", "from getpass import getpass\n", "\n", "KAY_API_KEY = getpass()\n", "OPENAI_API_KEY = getpass()"]}, {"cell_type": "code", "execution_count": 2, "id": "f7fcaf70-29a4-444b-8f07-9784f808c300", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"KAY_API_KEY\"] = KAY_API_KEY\n", "os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_KEY"]}, {"cell_type": "code", "execution_count": 3, "id": "ac00bf93-3635-4ffe-b9a6-a8b4f35c0c85", "metadata": {}, "outputs": [], "source": ["from langchain.chains import ConversationalRetrievalChain\n", "from langchain.retrievers import KayAiRetriever\n", "from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"gpt-3.5-turbo\")\n", "retriever = KayAiRetriever.create(\n", "    dataset_id=\"company\", data_types=[\"PressRelease\"], num_contexts=6\n", ")\n", "qa = ConversationalRetrievalChain.from_llm(model, retriever=retriever)"]}, {"cell_type": "code", "execution_count": 5, "id": "8d9d927c-35b2-4a7b-8ea7-4d0350797941", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-> **Question**: How is the healthcare industry adopting generative AI tools? \n", "\n", "**Answer**: The healthcare industry is adopting generative AI tools to improve various aspects of patient care and administrative tasks. Companies like HCA Healthcare Inc, Amazon Com Inc, and Mayo Clinic have collaborated with technology providers like Google Cloud, AWS, and Microsoft to implement generative AI solutions.\n", "\n", "HCA Healthcare is testing a nurse handoff tool that generates draft reports quickly and accurately, which nurses have shown interest in using. They are also exploring the use of Google's medically-tuned Med-PaLM 2 LLM to support caregivers in asking complex medical questions.\n", "\n", "Amazon Web Services (AWS) has introduced AWS HealthScribe, a generative AI-powered service that automatically creates clinical documentation. However, integrating multiple AI systems into a cohesive solution requires significant engineering resources, including access to AI experts, healthcare data, and compute capacity.\n", "\n", "Mayo Clinic is among the first healthcare organizations to deploy Microsoft 365 Copilot, a generative AI service that combines large language models with organizational data from Microsoft 365. This tool has the potential to automate tasks like form-filling, relieving administrative burdens on healthcare providers and allowing them to focus more on patient care.\n", "\n", "Overall, the healthcare industry is recognizing the potential benefits of generative AI tools in improving efficiency, automating tasks, and enhancing patient care. \n", "\n"]}], "source": ["# More sample questions in the Playground on https://kay.ai\n", "questions = [\n", "    \"How is the healthcare industry adopting generative AI tools?\",\n", "    # \"What are some recent challenges faced by the renewable energy sector?\",\n", "]\n", "chat_history = []\n", "\n", "for question in questions:\n", "    result = qa({\"question\": question, \"chat_history\": chat_history})\n", "    chat_history.append((question, result[\"answer\"]))\n", "    print(f\"-> **Question**: {question} \\n\")\n", "    print(f\"**Answer**: {result['answer']} \\n\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 5}