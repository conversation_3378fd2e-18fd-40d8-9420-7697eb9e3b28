{"cells": [{"cell_type": "markdown", "id": "052dfe58", "metadata": {}, "source": ["# Fake LLM\n", "Lang<PERSON>hai<PERSON> provides a fake LLM class that can be used for testing. This allows you to mock out calls to the LLM and simulate what would happen if the LLM responded in a certain way.\n", "\n", "In this notebook we go over how to use this.\n", "\n", "We start this with using the FakeLLM in an agent."]}, {"cell_type": "code", "execution_count": 1, "id": "ef97ac4d", "metadata": {}, "outputs": [], "source": ["from langchain_community.llms.fake import FakeListLLM"]}, {"cell_type": "code", "execution_count": 2, "id": "9a0a160f", "metadata": {}, "outputs": [], "source": ["from langchain.agents import AgentType, initialize_agent, load_tools"]}, {"cell_type": "code", "execution_count": 3, "id": "b272258c", "metadata": {}, "outputs": [], "source": ["tools = load_tools([\"python_repl\"])"]}, {"cell_type": "code", "execution_count": 16, "id": "94096c4c", "metadata": {}, "outputs": [], "source": ["responses = [\"Action: Python REPL\\nAction Input: print(2 + 2)\", \"Final Answer: 4\"]\n", "llm = FakeListLLM(responses=responses)"]}, {"cell_type": "code", "execution_count": 17, "id": "da226d02", "metadata": {}, "outputs": [], "source": ["agent = initialize_agent(\n", "    tools, llm, agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION, verbose=True\n", ")"]}, {"cell_type": "code", "execution_count": 18, "id": "44c13426", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mAction: Python REPL\n", "Action Input: print(2 + 2)\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3m4\n", "\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3mFinal Answer: 4\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'4'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["agent.invoke(\"whats 2 + 2\")"]}, {"cell_type": "code", "execution_count": null, "id": "814c2858", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}