{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Self-checking chain\n", "This notebook showcases how to use LLMCheckerChain."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new LLMCheckerChain chain...\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new SequentialChain chain...\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["' No mammal lays the biggest eggs. The Elephant Bird, which was a species of giant bird, laid the largest eggs of any bird.'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chains import LLMCheckerChain\n", "from langchain_openai import OpenAI\n", "\n", "llm = OpenAI(temperature=0.7)\n", "\n", "text = \"What type of mammal lays the biggest eggs?\"\n", "\n", "checker_chain = LLMCheckerChain.from_llm(llm, verbose=True)\n", "\n", "checker_chain.invoke(text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 4}