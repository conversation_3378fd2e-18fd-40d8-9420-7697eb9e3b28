{"cells": [{"cell_type": "code", "execution_count": null, "id": "459d0bcf-7c60-495e-91c3-85b0b8c67552", "metadata": {}, "outputs": [], "source": ["! pip install langchain-chroma langchain_community tiktoken langchain-openai langchainhub langchain langgraph tavily-python"]}, {"attachments": {"5bfa38a2-78a1-4e99-80a2-d98c8a440ea2.png": {"image/png": "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******************************************+YMWPy5quCkKLz5XuQj/iuJGGYK4zvVz66/fbbi85XhS35sk09+OCDReerjGXefJ966qmi82U+k4+YF+XCMimO+Vc+Yh6XdG+uMBVy5Ms2pcxy0fmqMCJvviqgKzrfG264IW++v/32W9H5XnnllXnz/eOPP4rOVwX/efOdOHFi0fmq8CJvvrNnzy463xNOOCFvviRQgVtReR922GEF5VvsnEcVOwXlqwLeouq7yy67FJTv8ssvnzffgjIqUyI3u9XRz8kRcAQcAUfAEXAEHAFHwBFwBBwBR6C8CNQKs9tcZn5LLLFEXgSWW245Ofzww/OmiyZQyXv0MvGcNEceeWRinAXOmDGjgtltPvMe7luiUyc56KCDLIvEY6NGjSqYTGGul486duxYkGljNB/MC/NRhw4dBPPNYqhnz555k2O6hUlJMbT22mvnTd6yZcu8Zh/xTDbYYIN4UMY1pne5zG4zbtCAQkxWGjRoIJibJhFxSbThhhsmBWeEYZI6Z86cjPBs+eYzy7KMwGH69Ol2mT5my1c1hOk0uU4wzSrGtKVHjx65skvHqUZKxo0bl77Od5LPnM7ux1xapc52mfdYyPhAJow7xZhRFTJOkq9KeQWT7UIJ89hCCLOwfOb70XzymW1a2v3331969epll3mPqqHMm4YEqlGVlVdeuaC0JGqsyyAKIUy4MIEuhrK9M9E8VNotmEAXQ/lMbskL0zDVFheTrbRu3Tpv+q222koKfRaWGd+ZfETfxSy1GCrk3cCkrhBTtmi5hbzLLDe4+eabo7flPS9k7GG5QSFm69HCCvnWY+6rWuDobenzbP2UuuQjljjdeeedicmy5bveeuslpo8Gdu3aVe6+++5oUPq8YcNk3QrPOh8tvfTSghl4MVRIfZmjPfDAA8Vkm3e5FZmpVk7UOq6ofAv5JjOXwsy+GCrkm8z8tpBlDNFyC5kDk56lBNGlcNE8ks7pQ4UQz001toUkDWkKGR9ISP+FjyiUCv0O8B4XszSq0PJLla4BGtVSZbYw5jNu7NiiOmShGDXXSdQiiyxSaHJP5wg4Ao6AI+AIOAKOgCPgCDgCjkCtRiBZNFSrq+yVcwQcAUfAEXAEHAFHwBFwBBwBR8ARqGsIOPNZ156Y19cRcAQcAUfAEXAEHAFHwBFwBByBOoiAM5918KF5lR0BR8ARcAQcAUfAEXAEHAFHwBGoawg481nXnpjX1xFwBBwBR8ARcAQcAUfAEXAEHIE6iEBhLvzqYMO8yo6AI+AIVAaBKdNmyoBBo2T0uCkyZuwkGTd+qjRt2kRat2wqbVo3lyUXbyu9enSWNq2aVyZ7v8cRcAQcAUfAEXAEHIGFFgFnPhfaR+8NdwTqDgKzZs+REaMnyPBRE2SmnjfULWiaNGkkbZUBXEKZwcU7tJFsLvsLaeXocZPkzQ8Hykdf/yb9fhwuc+fldwK+YpfFZe3Vl5Xdt15DunQubEuSbHWZPHWGTJsxR1o2ayzNmzUJbcuW1sMdAUfAEXAEHAFHwBGoqwj4VitVfHK+1UoVAfTbHYEEBMaqtvGtjwfKJ1/9JkNGjpfRf06UXJtCtWvTXLp1XVxWWr6TbLxON1ljpaWkYcPkPVKjxY35a4o88NSn8twb/WT2nHnpqMUXbSOdl1pEOnVoK4u2byWz586TKVNmyCT9/TL0T/ljzMR0Wk42XKurHLDrurL2asvkZYLnKWP73U8j5d3PfpZvfvg9MNVTps6skF+njm1l1e5LyRr626LPSspc599bsUIGfuEIOAKOgCPgCDgCjkAtRMCZzyo+FGc+qwig3+4ILEBg+oxZ8sp7/eUN1UB+8+PvGbg0a9pIll6yvbRs3lQ3kU7JrFlzZJIybZjGxgnmbeuNVpadt1hNlls6UyvJ/fc99Ync9+SnMmv23HB7jxWXkK36rCx91l5e7+mQk4kcrWV+oxrS1z7oL598PThdPEzo2cduI50Wa5sOs5M5ysC+8Ob3cs+Tn8if4yZbcN5jI2WiN1m/u+y749pq7rt03vSewBFwBBwBR8ARcAQcgdqKgDOfVXwyznxWEUC/faFHAEbw1ff7y60PvS9/qibSqMcKS6jWr7v0WGFJWUa1kB3bt0nUZsK0Dho6Vn4aPFq+7T9CPvjiF5k5c07IRq1zZYfNVpOj9ttQlujYLoShZbzg+hfloy9/C9cwncccsJGs17NLTobT6hU/Dh4+Vh594St58a3vg7lu65bN5IKTd1AN7IrppP0GDJdLb35Nfh/5Vwhr3ryJbLj2CrJJ7xWk6zKLytKdFpFWuqZ0hjLUU6fRnjHyvWpHP1bN74BBf6Tz2WqjHnLGkVvKIm1bpMP8xBFwBBwBR8ARcAQcgbqCgDOfVXxSznxWEUC/faFGAAbr2rvekgG/zmewMHfdY9uespVqLTsv0b5S2MCMfvDlr/Li2z/I598OCXk0adxQ9tphbdl1q9XljCue0bWj44Ww047YSnbbZo1KMZ3xyv0yZIxccuMr8vPgMSGq797ryzH7bSQvvP29/Pu2N4LpbutWzeSwvTaQPbdbU9d2FrbkHmwef/Fr1Qr/GPJddJFWcunpO8uaqy4Tr0L6OqU2yqPUNHjMX1NlwqSpMnHSjMDctm/bUjq0bynLLNlBGjdyZ+dpwPzEEXAEHAFHwBFwBKoFAWc+qwizM59VBNBvX2gRePq1b+U/d74ZTGibKSN2yO7r6brJdaSFOtwpFX07YITc/OB78v3AERWy7KhrKK84c1dZTdeGlpJwjHTdPe8IbYNaqoZz2ozZ4XyTdbvJ+SduFzzmhoAi/8GoX3TDy4Fxbta0sVz/r72l1yqd07lQ9nufDZJPvxksn/cbUkGLnE604KRVi6bSa9XOsq5qezE1hqGtKzR59jTpP2GIjJn+l8yZN99kuq7U3evpCDgCjoAj4AhUBoFGasrVpmlrWWWRrtKpReWE85Uptxz3OPNZRVSd+awigH77QocAZrY3PvCePPL8F6Htm23QXTWQW5bNqQ5awPe/GCRX3vqGjJ84NZR56hFbhDWU5QL/gWc+k1sefD+d/UG7ryvHH7hJotlwOlEBJ2h1z7jyGfnqu2GC6e6NF+wj3dTr7vNvfScPPvN5hbWkrBVdTDXJHVTb2UYdMk2bPkvbP03GqmnzTDXvNWrcuJHspGtjD9iltyy7VAcLrlXH6XNmyPPDPpRXB78tf00cVqvq5pVxBBwBR8ARcASqE4EWzdvLhsttJHt33UqWbJnp16I661KZspz5rAxqkXuc+YyA4aeOQB4E5qrTnX9e+4K8+8nPIeXh+2wgR+3bpyRmr3mKlnETpsrBZzwgfy1YV3rcgRvLoXuun++2SsWPHT9Z9jj2zuDMiLWf/z5390rlk3QTDOgplzwp/VSrC6EFNWayfbtWso2aLK+/VpewF2kLdc4UJxwfDdR1pJ9/N0Te/fSXtJkw62MPUu3z0fo82MamttBrwz+Tu769Vx1M/b0euHnzdtK+1eLSuGFjaaB/To6AI+AIOAKOQH1GYG5qrkyeMUEmTxktthlcg4YNZevuO8vRK+8mTRqWzmqs3Dg681lFhJ35rCKAfvtChcDN6lTowac/kya63vCcE7aVHdUZUHUSjBda18de+DIU+39ah122XKPkVbj+vneDZhdz1iduPlJaqplrKekP3Xpm35PuVqZzvtnpYmpGfLBqV3fdqmfBa0mpD1rhr3S7lwef/lQ+6zc0VJHtai4+dcdEL8GlbEO+vObph/aSr+6Sr4d9EJK2bNFBtl5+K9lh2T6yRIu6J+nN116PdwQcAUfAEXAE8iHA0pP3Rn0tz/36howZ/2tIvmi75eTqjc6SxZrNd6yYL4+ajm90oVJNV6Iulz992jRds/b3/oClakvjJk3UrK55qbLzfByBGkeAfS1xLgSdd9L21c54Ui57f26wZleZOn22/KBrKEePnSw7b7VGlc1hyTtKI0dPCBrFM4/ZOpjFRuOqej556gw54fzHgyaXvDbfsHswv11zlWWksTpRKoYaqLpzqU7tZPvNVpVldXsZ1oqOGjNJXtUtb9ZeY1k1hW5TTHYlSwvjef4Xt0m/3z8Kes1Num0vl69/qqzTcRVp3aRlycrxjBwBR8ARcAQcgbqEQLNGTaR7u2Vl5y6bSctWHeWHP3+UKdPHyft/fCsbd15XWjau/byDaz6r2ONc81lFAP32hQKB39W7bF81eZ2q6w733K6XnHn01jXabtadPvjcZ7LnNmsKHmjLQTgAatyoUUkZW+p95pVPh21i0KqeomtXt1aHQaUiNKrnXv188D7M1i+3X3aArLhcx1JlX3A+tw14Wl7RH8zxkWsfJzstu2HB93pCR8ARcAQcAUdgYUFg0KTh8n8fXKZbzE2WzoutLDdu/H/SsEFxgujqxqp216660fDyHAFHoCwIYG4L47lKtyXllMO2KEsZxWSKBvTQ3dcvG+NJXZo2aVxSxtPa162rrnVUJ0FXnb1bSRlP8mcv1Bsv2ic8J/YbPe3Sp4R9UauTBk4YKq/99Gwocv+ehzrjWZ3ge1mOgCPgCDgCdQqBFdt2lks3OlcaNWosw8cOlEfUHLe2kzOftf0Jef0cgTqOwODfx6YdDJ1z3Da1yplNXYMWpvnY/TeWp249suTbxBgWrVs2k/+et6d06thWxoybLNff/45FVcvxroFPhaUMXRZfXfbpumW1lOmFOAKOgCPgCDgCdRUBzHB36bF3qP5zA5+WmXNn1eqmOPNZqx+PV84RqPsI3Pfkp6ERfXovL911WxCnqiPQadG2Vc8kRw7t2rTQPUm3DyleePN7+frH33OkLl3UiGljZdCob0KGx662b7V4QS5d7T0nR8ARcAQcAUegZhA4aMVtpZk6HJo5a6q88vsnNVOJAkt15rNAoDyZI+AIFI/A6LGT5PUPB4QbD9vb1+0Vj2DN3dF79WV1/8/VQwUe0n1Lq4PeHvFF8MDbqf0K0mORLtVRpJfhCDgCjoAj4AjUeQTYemzTrpuFdrw34vNa3R5nPmv14/HKOQJ1G4HPvxuqzITIyit0ktV0vadT3ULgoN3WCRX++OvBMnTEuLJXfuD430IZqy++atnL8gIcAUfAEXAEHIH6hMA6Hed/O0dMGFKrm+XMZ61+PF45R6BuI9Cv/4jQgDVXXbZuN2QhrX2XzotKb91yBXr301/KjsLIyfP7y8qu9Sw71l6AI+AIOAKOQP1CwCyGZs6cKONnTaq1jWtca2vmFXMEHIE6j8C3A4aHNvRaZek635aFtQHr9eoiX343TL4dOFIOLTMIs+fMCCW0bdq6zCXV7uxvuukmmTp1qpxyyim6hqd0WwENHz5cnnrqKenRo4dss802tRuEStRuzpw58thjj8n3338vTZs2lb322kvWWGONSuTktzgCjoAjUPcQaKP7YDfQbVZSqXkybfZMad+0drbBmc/a+Vy8Vo5AnUdg4uTpMlz394R69ehc59uzsDZgjZXnP7v+P8/XSpYTh5SojbZSg0oU8sILL0j//v0Dw7HCCitUyOHJJ5+UQYMGyS677CKrrLJKhbjaeHH22WfLtGnT5KCDDpKlly6d4ObDDz8MDO1WW21VI8zn7Nmz5eGHH5bp06enYW/evLmsuOKKssEGG+hWAY3S4ZU52XHHHeX1119P3/rTTz/J//73Pzn00ENl7Nix8tBDD0nHjtW/b226Qn7iCDgCjkCZEdDtscNyp/lf0zIXVsnsnfmsJHB+myNQmxAol6aksm2cM3eejPpzvslHy+ZNBO+pTnUTgaU7LRIqPnHyDLn+vnflsD3Xl7Ztmte6xlx77bXy7rvvymKLLSZR5hOG4+CDDw717dWrV51gPmsduCWq0Keffip9+/ZNzG3JJZeUe+65R7bbbrvE+HyBH330UWA8W7ZsKXfddZe0bt1a0IQOGTIkMLzc/91338mWW26ZL6uFIv75558P7UQg4+QIOAKOQHUi4MxndaLtZRWFQLml5EVVJpIYDct1110nu+22m5x00kmRmJo7LZempNAW/TrsT3n1/QHymx6HjZwgI/8YL3PnzZe7NW7sS8sLxbE2pmvX+m9G85Hnv5BX3+sv5xy3tWy6brfaWN0Kdfrmm2/kmGOOCWFnnXVWpRmbCpn6RaURmDdvXri3Xbt2ctVVV4X9XDEFvvnmm2XUqFGy5557BpPZ5Zdfvugy0HpDmBPvv//+6ftT6vGMcbqBqgN69+6dDl+YT2bMmCG77rprgAAtNNpnJ0fAEXAEqgsBZz6rC2kvp2gEyikl//XXX+XLL78Mk5GolqSQSrKm6O233xaY49rCfBZS71KnQbv5wlvfy/NvfCcDfv0ja/YzZ83JGldMBHifccYZAfeLLrqoZOZzkydPlv/85z+Cid4666wjp59+ejHVKlla6tGmTZuS5VdsRl9//bXce++9csEFFwTtod0/fcZsOxW0oCNGT5Czr3xWtt64h5x19FbSplXtnLiOGzdO9thjj2C+inbniiuuSLfDT2oWAfq5CQWoyXHHHScrrbRSeFaPP/64nHPOOUVXcMyYMeGeTp06VbgXpvOGG26oELawX4CJkyPgCDgCNYWAqyRqCnkvNy8CUSn5bbfdJrfccov83//9nyA1Nyn5b7/N35ohb2axBHfeeafst99+wrFYOvzww+WQQw6R0047rdhb6036wcPHylHn/E+uuu31wHg2bNhA+vReXk49Ygu57vw95elbj5I7rzxgfnt1ooP2wei+++6TlVdeOUw2zz//fAvOe8QBC5PIW2+9VX7//fe86ZMS8Mw23XTTClFMfGFMYP7eeOONCnFVvYi2O1de9O22bduWvPxcZcbjvv32W8F8+48/KgoSmjZtLFeevaucctgW8tB1fWW/XeZrj974YICceMHjMknNcWsbzZ07V/bdd99gconDGdb9NWz49+fu5ZdfFtYH8rzRjh511FGy2mqryfbbby+MNUkEM0t/3WmnnYLDHsxDYZIIN5o1a5bsvvvu4Td+/Pz1zhZHv6dM1jxGCaHKPvvsE+7hPBcx7iGA2XzzzWX11VcPGr5HH3008RaEd4xVPXv2lK233jpoF7P1R/C6/fbbQ/1WXXVV2XDDDQN+jJH8XnvttQplUCbaRTCjLghs4v2mwg15Ljp37ixrr712SIUmNE65yhsxYkSoo+Hw0ksvhesDDjggvNPkxZpPngvvuBHraQ877LBgmnvNNdcEc9w111xTjjjiCBk8eLAlq3AsFH/Lm+d55ZVXyiabbBKew/HHHy9//vlnyJM+yHMBb/rqxx9/XKEsuyi2TEyNc7UHQRv1M8IknWe8MH/PDAs/OgKOQDUhoB8jpyogMPbPP1OjRo4s+U8nLlWoVf24VddvwbGkdGJSoUHKeKR0XU+IU6ahQlyhFzppDPeruWqht9TqdIaHTtzKXs/HX/461Wfva1Lr7X51avP9/5u694mPU3/+NSWj3GnTZ6Y22PPfqV2PuT01eeqMdHyvnj0D9jxb1YCk1OwrHZfrhHeCe/h99dVXuZJmjVNT6ZQ6HKkQv/jii6dUQ1YhrBQXqkFMqcfNlE5A82b31ltvpZQxSan2NW/aciW4++67A7bqKTRvEd/2/z219cHXhz5w4Kn3pcZPnJb3nkISHPDqKaldnjow9dmYHwtJXiHNZpttFuqv6/1SyqCFc57t0KFDK6Tj4uqrrw7xymSlxxLrWxzPO++8Cvd89tlnqaWWWircQ7x6oE2fL7HEEinVGqfTM16RRhnedBgnylyF8PXWW69COM+e9OSjArcQl/Q+K3MS+i5piY/WRy0wKuT59NNPp1q0aJGuI/fw433jqA6HKqRX5ieE0y7qYent+O9//zukp37KuKTjaavVlft+/DH3c8s2ps+cOTPVtWvXkC/PxqiQ8lR4kK6P1deONh5aHe2a/C1M14Bm3K/rhlPKTFs1wrEY/C1vFXRl5L3WWmul/vnPf2aE87zUGqfKZeZrD8/e8Ikel1122Qpl+4Uj4AjUTQR2e+bg8B39fcqYWtuAv0XBOgo5OQJ1AYFcUvJ8UmKcTyDlffbZZ0NTn3vuuXBNmEmeo1qRBx54QJCGI+HHHAx69dVXg4YA5xhxyiWhJy0mjWg/4poE4n755ZfgjfPkk0/mMmgLdRIpBx54oKypjlK6d+8ueKlEM4Omoibooee+kGvufFO1BXPD/o8PX3+Y9N1rA1msfauM6rRo3lTef/RUefa2o6V1y2YhfsCAAfJtv37ptGgiXnzxxfS1nYwcOVLwzPnDDz9U0JpavB1Zr4SmhDVMlaUJEybk9CiKJgEtPEc07Wi3okTZ1CFOaD1Iy71Rsvvxvmnaoi222EJ41jzjONGn0fpGSSfr0ctwTjlxzVm2utnN9CPqrl8oCyro2FO9F9908X7qSKq5DBoyRk677CnBDLs20COPPBLMqHUyH95znVRnrRbvPN5k8ZBKPzjhhBNC2uuvv16mTJkSzsEVDSJ9Ei0ffYB+h6YYz7k8Q3tnuQHNKIQGzgjtKM5uIMybefZGjDfQzjvvHNYlWnj0yHM6+uijg9bsxBNPDHVF46eMa9hS5MYbbwzLCLiHfsoWLdSRdek8X7YeiWv+LP/PP/88bE/Svn37kI7+xrtnW7x88sknaa3YE088ETzG4hyIdmCBgCaPuoMDa88LIdqDmSxteO+990I90TZijhvVyhVSHk6kKJs1vRBaTp4V+RfiKRgMuRecVMggKrAIzyeqAS8G/2j7adu//vUvGT16dMAYb77gdtlll8n6668fnpkKR4IWm+eFLwGjypaZrz3PPPNM6MNWDriDl/VPC/ejI+AIOAJlQ6DWssV1pGKu+SzfgypWSl6IZFo9IiZKffUFS+l6t9AY04qouV6FtKYltfgjjzwy3XidoBakEVCzq5BnXPNARiYNV9O/kK9pZ6kbGgs1y0zXRyfJ6bI5MUm7TqAqhJfy4rUP+gdNFxrPWx56P62lKaYMNVsMbUDLt/HGG4fzqNZRJ84pZbbT7aTtOuEPRcQ1nzpxTWtpDA91BpXSiXBWDWJU8/nmm2+mdDKYLotzNVfLaA6aWjRDah4X0qrJdUijDERKHaQE7Sb1VHPwlE5YQ9y6666bzpc48lbGO6UCjBCuE9L0fbr+OKWT7BCOFseIMJ3kh/AmTZoEHHi+1Js81VTXkoYjWhZdNxfOc9XNblLvsKlWrVqFvNBa6Z6I4bwQzaflMWjomKD9pk/c9dhHFlzpYyk0n2DDj/6VjewdVsc2KfqRkW5vksZEJ+MhWAVVIT/eQWVQLWk4gpWV169fvxD2yiuvhDC0Z/RnSNeJp9OR/sEHHwzh/FMGNsQps5oOi7/PKqAJaXR9eoYmXU06Q5wuSQj38w5QBvVVBjqdJ3VR894QFx1/1Iw9hEXHM24iDfnccccd6TzUYU8Is7HSInQNfAinn6vAw4IzjjamG2bRI+9XXPNXTHmXXHJJqIOa0WeUG8eTBBYWt57R9eQhn+i4VAz+0byjWlzCTTuPFnzixIkEBUJLDhaMNUaVLbOQ9vCMDHtleq1IPzoCjkA9QMA1nzq6OTkCVUWgECl5oVJi1jIh2T/22GNDtdB0IPXlZ9sxWH2RBHfp0kXeeecdef/994U1RNmoEAk996LFhMjPHGSEAP1nmlUrp3HjxiE9kvJJkyaJTlbCmjzSs1aVfQCri8aOnypX3jJ/3dde268pxx6wUVYtTa462bqsvffeW/hBaJJpH4QmmvV5HTp0EJ24BYdOPNs4EQaWaDy6desmF154YUiCpgFvxGh68hFrzHAepRPm4CGTc7xtxmmGahktHVs4oOWBdLIe1gyylk8ZwuCsiH6FJok1Vzi7gWgzz1bNCtNa0IsvvljOPPNMUXNGQeNkbURrBbEOEW08ex9+8cUXcvnllwctCdqvjTbaKKx7RsNnhAYJbZVtU5GrbtxD/2ONF94/0biijWcvzGJphWU7yqlHzt+64p7HP87peKrYvCubHs2ZMuvywQcf5HU0g8ZZzVfTRaEtXW655cK1vZ/Wl1gPynrzKGERQf+DsFyA0I6iwUO7iSYNon9A1idM24nWCy+tpM+1BQgWA5AKC0SZwbD+nXXC/Mx5jK1/t3qQH+mN6OdoP+Nknk7R5EZJ52DhctFFF00HWz3YM9XK56iMd0hDP6ZNhRBOoOh/PCsIHFmfGqVSlhfNN3oeH/d79OgRom1tJhdWj0Lwj+ZtY7mFsdYdQjvOGm8js3gYG1k/XNkyC2mPletHR8ARcARqAgH3dlsTqHuZRSEAsxj3YIiThvvvv18w/4JgYDDRxHPtf//7X4Fxg5hcYt7EZBtzIyY7quWRRRaZv3che8FZHuGGyD+VjAfTuUI2pYeJgGASMNOFuP/UU08NzJBqQwRTSZhfthFgoshkHwcUEMwGk0YmNzZBVEl+iIv+g1mG0WJii7kUOFQH3fbIBzJNvZ5277q4nHL4FukJbzFl412YNjLZxc0/zDP4cOTZYC4HMwmRBmccmNQlEcybalGCx1uwZW9HCO/D3LPtttsm3VYhjD4As4kjGiacSYyn3UD+mGZaPxw2bFgw6cTrrm3rgIkf+cDM4WSEekE8T137aVmFo64plKTna4lw/EP/weybPkG/BT/6DGagMO6qfQqmgpihI/xg4g/jla9uqpUVGGbqBFNNm6gj5VTGK+hOm68mH3z+q7z/+S+iGnG58YJ9rBk1clTtUhgHeDaYgfLOgV+hxJgAmUDAmDpwTiJdQxz6NaaVEOaqCAF4JpjeUj6OjWBscWxF/8DsnvyNCWV7EDNzTSrD6oBAjDEgiWAuIcxHoWzjWoiM/INZ5n2jfzEeIVxhvMR8kz6BAASCGTfzb8xGs1HUsVO2NGCJoAlCYARjz1IItlwxE+ZSlpetHknhtBkyQRDnxeBP+mxkjH483p4977ZRqcpMao+V4UdHwBFwBGoCAWc+awJ1L7NoBJCSo51Ews66triUPC4ljhYQ1wxE43KdM8EvhPEkDyvfNAKWr63vM40AEm6k4ZdeemnQiBnzCRMAsUbLJguWB+si2RoGJpw1fDaBsYmgpSvXcdjIv+TFN+drEk87Yktp3KhyS8VN67niiisGbR71RevEujE8gMJ8Mmmn/TxrmHgm7moeKuqkpULzbJ0o3iqjW+Wwvs+0yxVuqOIF5RvjSVZsywKhgYZxDrRAU8Rzyke2x162dAMHDgzCCrScRjAV9COwgclEC4tGFQ0mR7aqgMkyj7256sbEFiFItE1xrZOVm+/I+/WPvpsG5vOLfkNl8O9jpesy84UB+e4tVzxeac1iAU+yWA+YwKnYMk1DZWtA4/dPW7Ae1wQgxDNewXy+rMwn60VZa44mkvcfL7VoU9GKGvPJe5+LrO4IVXgfkgjNOgSTCxU6PuCV15gePEnzg9DG0oeMiQUHGFz6IIKVpLW0MO5J4SHDLP/wPo23XMZEPLHCXCM8LFd5WaqRM7gY/HNmVERkTZRZRPU8qSPgCDgClUbAmc9KQ+c3VhcChUjJSyUljrbJzMGiYUnnxUroMYtiooWzE8wl0ZiZyW3U2QaTXbaAQCMA01lT9M5nP4ei11p9Gem1SrL2J1/dMOEzBhsNNZqeKKHFBEcYIsxW0WzSbjBCA4qW1zQ73AczyOQdDSGmqFEGNJpvuc6ZgEM77LBDYJCj5RSjZYveFz0nfyafOI6JEtpKmAxwWmaZZQKmulYzYIZGHCqkbghF4hqq+HXIrMB/nZdoH7ba+ejL3+Txl7+Rs4/ZusA7y5OMvoJAAy04fQchRVpIUGSRxtRh1hwn+uxPP89/P8z8ljSYMfOscK6FhhrSNZThSJ+B+UQDyn7BpCN9LrL+jcVDPoGYOdkxk99ovmjF44QFCf0BRhOGkzEJBhKmEK2uEUIvGFGEIGgF89XD7ivkiMUC1iPkjUYUK4RylldInaJpisE/el9VzqurTIQUJtCsSn39XkfAEXAECkWgciqMQnP3dI5AiREwKTnZIiU306iolFjd/UvSj8lVOcgk9OSNRiCpbNZBofGD0H7AoMAkwHSypo8JMubAMFpGMKloC5nw4X0T02JM1KJr1CxtOY/vfTooZL/Zet0rXQzr78wckHWRaDf4wVxDTH7RFOFxlEkX7WaCDhOB5pd1cVHCfBEcMNm1PCze1qrZdTmONjFkbSoa2+jPTKFN427rWYupB8wlwgc0aNG8MfGFSSRvNOgw6pj40pdM41tI3WAu0NL/9ddf6WrF1/ylIwo8YS0w9M4nPxXtPbfAIopKBhNmjB+CjKgn0WIyQtsIg0gfxCzUiLEHrTNeSmFyoybirOPt06dPSGomqvZuw3xCrAum/5KO9LkIU2JMM3WLoeBtNp6Wd8U0nTC5vDdo502oRXqEP3hejZPdh3dXtOq2xpP7eR+jZEIjTJrRmEaJfs7YVxnCw6x5CY56mS1XecXWsRj8i807W/pylklfwqwfwtzayRFwBByB6kTAmc/qRNvLKgkCSMn5eJqUnExtwm2aAaTy8Z+Zo0UrYROvaFix5yah5z7TCMTLjpuiGaPAhNA0gmw0bto9JqW2ZhCm8x//+H/2rgI8iqSJ1kEIwd3dg7u7Oxxyh8vhcLg7wQnB3d3dD7fDJXiCE9whwf3/6/Veh8myu7HdrKSLb5ndmdaaycy8rqpXnUUcI5KVS6Ad3HGGpDyA3I3buli2QrlSh6QJUUeS4yDeEG59sFjiA/IUZnj0L4N9IHkCQEVcJEAVXvy1ViUUBvGLjE+EeyVcUCEgKcK1cZAtqZYUjAfnAmNAmgsAN1ix4D4oLU7SXRFAGWBa/2Xd1PgAqHENwPUb80P7nIczQCoLeQ1Bn3BPlha6oIwN7QLww9KOWFYsBGCBIzSSi9OvMCYm3zcf6ekLHYFUaNozR10AGmk9ZvZoAdaD2y5IYmA5hSDNCazuIIPC3zjIsXBfQZy5XGyQ7WPhQAoWKWQsOM6VlrgoMJdbtIH7G84RBItSiGXHfbBly5aE8TEzNzGTtziO8y9JsZiZWbj7YtEOY5b3F1Hwv/8AoHEPA7DF9+bNm4vrjtmChVcGFjekIE4Z1lEAFiyy4PpBjCYst7je9ReCZL2gbEGSBcH1joURiCX7Ex0E8b/g6D+ITQZazNJ9ysUSkGAZ8uAIdICqgNKA0oDSQAg1oMBnCBWnqllPA4ZWyYO7SiwtDXjhMocEd4UeL4IAVdJ1FGPQutyCnEjmcoRrnxS87EoXY7nPktu37z7T1//yNyaIFyPEXUkwiPg7fanP1jwILMBgDwXomj17trDaII4OABVWYbw4Q2cQbOGaJ9lDAToh0BligvXzXYqD/B/a0H8BhyXRlMspjunXQXvLli0TABSAE8ACMX0gmJGLHFhMgJUbMXqwXoKtWPaj357+fpDAYN6IcQbQQPtYgJDXBPpH7CAnrMdX0b748t9/gY0NesO4cV5g2UOcqIwllGPRtheU78jrmjq5LtbT+6aOOCoo9cxVRpKM6bvLu7u7C8AI3cFbAiLLyK12DIbaAaMr2sH1KPNiwoUVZGbIg4n7j74AVGIhBAJCHalXtC+tfLiODcX/Yly4RuRY0AbGjoUO3P8A0LBQg1zDiJkGYZQEEygLl2Ncf+gTiyIAi/h7kQBVO29YR+HWDwALV2AsFMHCi2sEf4v9+vXzt47B2wDkaLjfwWqO+xEWYBC7ivAIuMCbEtmv3GrLok0soGDBCR4ekOD0J9uUW23b2KevT0P7UEfWl1vZTnD0b6xteT7125a/5f3NnH3KtuVWto0FE5wzudCpvbfIMmqrNKA0oDRgCQ38xpYNHZ+6JVoPB22+5BgcYy+6oZm+C6+mh6WFKzRjtVRdvIDgpQ4PSLxgaQXWQLzQ4YUC5CxwacXLtHyBxss7VubxYMULF1zIwDApQSJetuV3vHThoY/UFwBHiD1C0nGsxBty1TV0HO5qsEDARRQWAAAGuK9hBR8v+CBzQfyiVvDSD6sTBGQxmIdWkAoERCkAXrBk4GUXc5AC4ApLIgQkPZgrrMEy5kuWC80WZEN/dpxPzpEi0uHV3UPclASE0tVLvyGMHecSL+soK2M8YTHWvjTBWodblnypRzuoi/OHFzscwzmQJDH6/cAyDXIV7UseXrzxW4ID/TroE8fki6P+cby0Qe+wbslFDVkG40EMHaxF0tqF8RrSg7H9YADGGPAirj8GvKijf0PtYQymxiaPw11SxvbBhVSCZzmH4GwHjN9K+45epfZNilOzWoWCU1WUbbSrG717/5wGFO9PBRJkCVZ9nFvo0NC5x3nAPPF3Iq8T/AZBDq47rUDX0BvOmSHB+YCHBRYWtNeRobJoB9cXxqS1jMrx4Do2FG+HOji3+uRjsg/0j3sirin8jehfF7Ic+kc5xAfLvxm4c+M7/q4wDrSBv5nrHLuq72GAeyjcjWF1B3GSVuTfqbw2cf0HRdA/9GZId2gT1yB0r9UX2g2sP6lT1NX/WzakT0P75PhxbeDvQHvvkcewDUz/xtrGvUfen/TnB72gP3metP2Fpk/UNTYfXGO4PqA7XEf6fwv6Y1C/lQaUBmxfA7U2NRXvOdMr8gJTtJ9x+7Y0cidbGowai9KAVgPywS+32mNylRwgBSAV4BMr01g5h6sWLAP4QPBirm8ZQPwVwCXcNZG+AA9d+RIo+5Nbbb/4LvfLLfbJFXqw12I8sAhIwUuqIYsAXuYk+JRufbIOtrBAwMUOlhaAYMwDY8ZL9qJFiwK8cGIsmIOxl1Btu8H5HiuGjjnzy9fv9PHTF4J1KySC8Wn1pd+GFjyhHHRmSAy9sGrr4oXOEPiQbRnSjzzvsoz+1lCf2jJ4WZRu39r9+I7xYPFEK9rxBmU/Fh+MCc65sfZQx9TY5HEJPPE7NMAT9SNEYL9bbOF/G8aCc2vs3OM8SPCPYen/1g7VGDCSZXA+TJ0TWQ5b6N8QmDDVP+oFdk3CAqtl10UdQ4K+Zay5PC5TyeA37iUAjxAs0mnBJ+I3sXACkXHM4sd//5n6O9WW0/+u7V//mKn7RGD9mdKpIX0a2ifHY+w6kscD07+xtgGKtdehbA9bU3rB8ZD2ibrG5oP7R+rUqVFEidKA0oDSQJhpQFk+Q6lqZfkMpQIDqR7SVfLAVqZltyDUQDJxrNpLog25gm7IKoJ6gR0PbIVe9o0tLDVYfTZmZUEZWFowRlhH8WKMF0ZpHcBxiLGVdt3RkP+PuZZqOIktQd9o5eS/rJ5CI+QzUTXDQgP92fK5ny2fnZqVpEY1dVb54PQbGstncPpRZX9qAO65cL0FEIH1C7lqcV+Eqy6AKUIEZMz2z1rqm9KA0oDSgNKALWpAWT5t8ayoMdmVBkytBptaJQ9slVgqAa7N+u7NplbQUS+w44Gt0Mu+sTVltZLl9C0tsPDog1VjK+2yjZBuMde0HMfnfesJnb1yX4HPkCoynNR7/+6jmGnkyJHCyYztf5pItQL3fQBMuN7u27dP5H8FMRJIrRCvrERpQGlAaUBpQGnAXBpQbrfm0qRqR2nAQTVQqkgGAT73/OtNdSvp0mk46FTVtEKhge9MTHX5+mPRQpb0SULRkqoalhrAwhXi5fFRojSgNKA0oDSgNGBpDSi2W0trWLWvNGDnGihf1FXM4IL3Q7pyUwcu7HxKavgW0MCt+y/o/ccvHOPoRBnTJLRAD6pJpQGlAaUBpQGlAaUBe9eAAp/2fgbV+JUGLKyBpAljU7limUUv4+fuYxY1RZBtYZXbZfMHTlwX486WiVl5I6pHi12eRDVopQGlAaUBpQGlAQtrQL0hWFjBqnmlAUfQQKdmJSiysxN53XhMi9Yfd4QpqTmYUQNgQt6487xosXrp7GZsWTWlNKA0oDSgNKA0oDTgSBpQ4NORzqaai9KAhTSQKF5M6tislGh9zsqjtHX/JbP3hBQ5S5cuNXu7qkHLa2D55tPk++YjJYwfk8oWyWT5DlUPSgNKA0oDSgNKA0oDdqkBBT7t8rSpQSsNhL0G/qicm+rXyCc6HjltJ81ffYQuX3tktoEgl+nmzZvN1p6tN4Q0No4gSzedpPlrjomp/FW3EOdzjegI01JzUBpQGlAaUBpQGlAasIAGFPi0gFJVk0oDjqqBzk1LUVQXXRqNuauPU6t+y2na0kP04vV7R52yRebl5uZGYBlFzlZ7lgveD2jeqmOc+1Y3izdvdalW7HlOauxKA0oDSgNKA0oDSgOW04ACn5bTrWpZacDhNLD/xDX68OmrmFfM6JHFdtnGU1Sj9UzqOWojHTx5nQHV9yDP+9WrV/TmzRuT5QHQ7t69axKovX//nu7fv88g6Kc1EfV+/PgRoG3s+/795/i+fPkijqMc+vj6VTc37PTz86OnT58GqK/98enTJ3rw4IF2l/932S7Gg3HpjwP9oIz+fjSAY3fu3CHMyZRgHvfu3aOHDx/+UkzOHdvbt2+LvrSFTI1dW87U9/3Hr9HfQ9bQ5y/fKGWyuKLoko0n6dNn+wbUpuasjikNKA0oDSgNKA0oDYROAwp8hk5/qrbSQLjRwMkLd2joxO1ivlVKZaVt8zvQ8O7VyTVdIsGAe+TMTerrvpnKNZ1KTXsspkOnbhjVDUBToUKFKF68eBQ/fnxq0KABvXv3LkB5gD8kuY8VKxalTp2aokePTg0bNiRfX1//cgB/5cuXF2VSpkxJyZMnp127donjGTJkEOX9C/OXjBkzijaxb+3atZwWJDLNmTOHEiRIIPpA/WPHjlHv3r0pUaJElDhxYsqXL18AEPry5UuqW7eu6DNFihQUO3Zsmj17tn83st358+eLNjCuuHHj0qZNm0SZggUL0qhRo8R39O/k5ERXr14Vv93d3UW7adOmFXWmTp3q3672S79+/YTuUqVKJeaMeZ0/ryP8Qbn8PGboKleuXJQuXTpq3bq1qB7Y2LV9GPsOtuPlm09Rf48tYqGhcJ40tGBMY0oQNzq9//CFjnneMlZV7VcaUBpQGlAaUBpQGgjnGlDgM5xfAGr6PzVw+fJlGjFiBB08ePDnTvVNaODN2080YNxW+vr9B5UokIH6/12JnCM5UflirrTIoyktm9ic6lXPR7FiuNAntoxev/OMfnBZY9K8eXMBliZOnEizZs0SgO/169cBiqPMunXraMCAAXTgwAHq3LkzrVy5krp06eJfDmWOHDlC7du3p3/++YeKFi1KGzZsEMdhGYTlTyv4LfdJq2Pbtm1F2/PmzRMAuHTp0gTg6OHhQXCPPXv2LI0dO9a/mVatWtGePXto0aJFtHfvXsqfPz+1a9eOTp06JcrIdlEOoG/NmjUUNWpU8R3WzvHjx1Pt2rVF2VWrVonjadKkERbKvn37UosWLejGjRu0ePFiihkzpn+/2i8A2IMGDaIzZ84IHcH62adPH/8inz5/ptWrV1PEiBEJ86pevbo4FtjY/Rsw8sWP3Wrhaj118SFRokb5HOTRrzZFjxbZPx3PrkPeRmqr3UoDSgNKA0oDSgNKA+FdA07hXQFq/ralAbgcrlixgj5+NBw7BotQhQoVLDLoffv2iRd6vKCXKlXKIn3Ya6MglHn3/jOlThaPrZ3VfsnjmD5VAur2V2nq2LgE+Tx8SQ+f+lK2jMkMThdWT4DJjh07UteuXUWZsmXLUvbsP1N0wFUUlkKU6d+/vyiDc+Ll5SUAKAArABfOWdOmTUlaCCtVquQPLg12bmAn2gIAhezevVuAQWzz5s0r9i1ZskQAUPzA2DGuoUOHCmst9iVLlowyZ84sQG+BAgWwS8jMmTMFKMWPW7duEayVcO0tVqyYAMrYX6tWLXJ2dsZXf4surKTp06cXH3HAwH+enp7+ezFO/M2cPn3afx++wKKMecCCCwnO2EUFA//FjO7C5/43kXanTYOi1LBGfvrtt99EyYolMtPKLafpqOdtevv+E8WI5mKgBbVLacA8GsDfABaJHFFq1qxpseecI+pLzUlpQGnAvjSgwKd9nS+HH+2JEycI1ixjApdFS4FPGadnrG9z7wcggeUKbp1wjbRVefjEl9b9owM7nZqXFMDD2FjBdJohdULxMVYGwBICECYF7qNwX5Ui3VBhhdQK6mzfvl2AOMRFQooXL64tItxYA+wI5IcEmSgGt1uIdh/ApXQJvnbtmjgOZt6NGzeK75Jt5/Hjx7rf//2P8yoFrrcQuBIbk5w5c1LJkiUJKWd27NghrLFNmjTxB3faeohFxYv3pUuX6MWLFwSrvbS4ynL4W5HAE/uCM3bZhv4WQHNgx8rsruxESL+jlUxpElLyJHHowePXdPriXSpT2H5SruCa9D+f2kmF8XcstsAVW0ngGsD9c8aMGYEXtMMSuF9Y6jlnh+pQQ1YaUBpwMA0o8OlgJ9TepyNfoBHnN3r06F+mA8unowgADGL84C45ZswYm50WSIa+c5xfdtdkVCRP6PUvQb60mMmJa3/LMhEiRJCHxVZeH3AnBWkOBPGaxkSWN3Zcf79+f/rHJVlRlSpVqEiRIgEOa8FmgAP8A+MNTFAG7rzLly+nKVOmULNmzejff/8lXCdaefv2rXAvBpCF1RcgE6RNPj4+2mK/fA/p2PUbSplURy6kvx/nLyMDUIBPWL7tSbDY0bNnT6sPGV4XCnwG/zS079gj+JVssMbMaePDbFQgQ3vx/LlYrIsdJ47BRS79weAe4sOLfjE4HAD3Xe09W7+s/C0J1FAeC4xBqSPrqq3SgNKAY2pAgU/HPK92P6sYMWKIOD67n4iJCdjLQ/j4OZ2FsVTB9GZ5cZBWwJMnT9Kff/4pNARGWG3Mp7QEA3z9/vvv/lo8dOiQiJ9EjKRkg4W1HG5qUkBIhJccfNCuFFhKtWRFcn9wtnJccI00kggFAABAAElEQVQFOAypyHMP0Aj3WCmRIkWi5mz5R9uwfIC8SB98Hj16VLjxLl26lBo3biyqIt40MPBprrHLsRraJk6gs4Y+eWaawdhQXVvZB/KrsBSEGEgyqrDs11H6ypO3APXoM8QhpvP44QPatHG1xeeCRbmRzG+wjEMKQHjWnRdeWrRsafL+jsW+Th060H4OmXCJHJkGu7lRvfr1TY4VC2TNeYHs4sWLgjBu4qTJVKZsGZN11EGlAaUBx9eAAp+Of44dbobXr1+n7t27i5f2hQsX/vLABGnQ8ePHafDgwcIqBAAC0hUAF7hGRo0ShXLlzi1iCcGIakpAXgP3RjCHgnlVKxgDiGFgpQIYgmA1Ge5769evJ68rV5j98wMBbIEdFeQzsG4BJIBY5sKFC6LO5s2bxT78AKmO1qK2ZcsWwnE8vPHCkC1bNhEnqAVkqAcQAuACCyraOHfuHMFKvG3btmC7oaI9KV++fqNL3rpUHoVzh97qiXZdXV3FPBBrie9xeNW9V69eBIueFMR/wqI3ffp0f8ZZ6AExjLAU44UJbqooB/0nTZpUsNUuWLBAvOQAmOXJk0eQ9oAsKGHChOJ60PYh+wrOFtdLiRIl/PuEyy/SxcAVGEAaYw6KJEmSRBSDdR+ERRUrVhRWT1yjsGbiXCNGUwJ1bZvwCoDgGoP+QLQEIibM0ZSYa+ym+kiaSDe2R89/nktT5W3tGNiBET8blvKB7xHRokULyy5VX+FcA3BZXsqEZt/5PvONrZmTJ02iOvyM0oY+6KsIi3wAnhAQmk1isri6f/xh0qtjOz9/8OyCIHRh+rSpVKp0KQrMw0RUUP8pDSgNOKwGFPh02FPruBMDAyjcE+GaCUAHhlMpABd4occL3ZAhQwipJUAGAysZwBmADsDfeQZ+YCH19vY2+IIv2wOIQ/wdXtz1wSf2I44OQFKCT5DjSBdaWG9h4QJABTEO4vKmTZtGjx49Ekyksg+4/MkYRxDmAHwCxIIER1q9QEoDlyeQbIAAB8cA3qQA8ILFFRYwkPJAEMcGlycAtZDK6zcf6Os3HWtt6uTxQtrML/XA8gqynTZt2ghLJix90KX2pQRsrX/99Zdgu8U8oE+4Rg4bNky0B92inUa8MADADUmdOrV/2hOQAoGpFmDVxcWF4NIIwC9dYGVf8jfqYx+uE63guLbMsmXLxLh79Ojhnxc0U6ZM/pZQQ+3K+vJYvXr1BICdMGGCIBzC3BFbisUOGceGxQMw3upL4cKFBZERrg1YzDDnGjVqBCAcQj+yT239wMauLRuS73FjRRXVfP1M5ygNSduqjtKA0oB5NBDht9/0Gvrtl0VcvQJ8n4ocYBfcw6UHR4ADmh+472rFmesoURpQGlAaCPlbqdKd0oAFNQAQqQVX6Ap5HmHhQ+5FWJ+Q5gLgQws+YSkE8ARYBPPoc45pwXekwoD1EgDmyZMnIjckwCAYSQ3FloZ0agB6AKkAJrnZugqB9Q5EIgALsMIBXMICC3CEOf79998CYKGstF5Jd0uk6AAYgVUNYBvfAXCRV/IPXnUGS6wUHAfwRN8t2YUK84vCVt7QyKdPX0V1F5dIDMz0X1hC3nLWrFkJFmyQ5eCc4EUGAFP7MgMSov379wvmY7jkwlqoPY7es2TJQuc4vyUWGXDekXdTCupLQh7oER9tH9AfFiWwXwquBZwXrcDaqhX0AWvjZ179R55RuOBiUUOKoXZh+db2BVdbLDiAsRfzl7lMsTCBfQDAuBb05yv7wHWLawlubSBJgqUUc5OCRRMAUCxiaNsIbOyyfki3fm90cbixY4Tuugtp/6qe0oDSQOAaSMMLWy154XYhe4rgXuPsHIlO88Jl2XLlAtwvZEu4vxw7eoQS8H3rNd9zYvI9C264F/jeCy8i7T1G1sHzCNZS3Mde8n0ei2t9mfFbLsDJcmqrNKA0EP40oMBn+DvndjFjvFQjd6NWYMmRMW5gAQX4RB7IyZMn+z/QYM2EAGhCQHKAF3GtALzCctipUye6efOm9lCov4OpVF8ALt3c3ATQQtwhgBfGIF2cAKqlG6asKwExgKSMLQSQRLoOWPPgcglQqwWfqAuXzXHjxolmYPENrURyiiia+PqF82Ny3k6niAEJgELbvn68o6H2MO/AQHS8ePEIH0Niqg8t8ERdvBjpr9brW0JlHwDMMo5S7pNb/XaxX38fXtgku66sZ2ifPKa/BWjFB4Jxa4lqYCk/fu42Ldt8mgZ3qvwLM62psev3E5zfL33fieJxYys30uDoTZVVGghLDeB+0Zs9Qvw45jwVh4XA3bwr50/+zvf4ChUrBACT8LiZMG48HTx4gDbx4u4AXvyEiy7uVX9zDOhkDnvIx6EDWgAK4NmPPYKwyLqLF+/q8+LpKA4JQaiEEqUBpQGlAQU+1TVgkxqAJWkix5RoRfvyDpdNgFNYMQ8ePEhlypQRViBppZIgVVsfbqiwKsFKJtN0SNIabTlzfIflFnE1ePhihVgCmqD0h4e9dMM1RH5Su3ZtAT7hzqsv+lY7/ePB/Z0ofkxOrRKRPn/5Tnc5f2e6lMaZZYPbtipvOQ1gocB99l568syPGnVdJHKzmitm19SoX/l+EIfjxlHg05Se1DGlAWtrAGARnjr4FOBY9Wm8mNmJPXQQvlGlahUBJvEscmfQCA6FhRwCACsmgCs+FTlEBNsunTrTxMmTqGChQmJKeN71ZWD7mmPhZ7GHDsJkfvstAv3GZZUoDSgNKA1AAwp8quvAJjUAAg5p8TM0QFh8EOcGt1vEBgJ8Iv4NDz6QvqRPn96/GohqQA4k80v6H7DAF5AqIA5VjiUkXcAVVbpQ6lvG0J5MLYJcj/pizEqnXy6ovyOypTN96kR05foj8rr5RIHPoCrOyuVgoZ44sDYNmrCdbvo8oz5jNtG0YfUoR6ZkFh3Z1du6azJpAh3xkEU7U40rDYShBk4d/5fcR7uRx8SZlDZdxjDs2fJdAYjm5bzEAKAIEfnx4ztV5nRSo0eNIk/2tJnP7rkyJESOBnUqMFFahIhOwmo6nuPX8+TNS704Lv8dL77O4JASRaQltaW2SgNKA1oNqKUorTbUd7vSgLRugqQFq7XS5Vbux2RAwANyFwDP5s2bi9QVB5ixb8CAAUGaq3QlQvtBEbgDAxDDjRbuwDt37hQsu2BjDarAJRIrzhCAWX2R1lMJQvWPm/t37qzJRZP/HLpi7qZVexbUQJrk8WmBeyMqmDMVffn6nXqM3CCs15bq8tmrd+R147Fovki+NJbqRrWrNGBWDXzmBcv3H369z+p34uv7mu7cvm7wnqxf1l5/52aG8JkMGseMHkMtmeztPIeszDMAPOX88HwsV66scKntwQu8LfgZ+4lTB01jbgIFPKWW1FZpQGlAXwMKfOprRP22Gw0gDyLi+WApBPAEoyxi3QA2pYCACMQw2Ie0LCB+KVWqFOXIkUMWMbmVcZlwn9UKYlLx0QoIb0BEAwHoBAMrUmiAHEm2oy0vv0swKX+jrEyngTyX+nL48GGxS2vd1S9jzt+1K+Zktykiz0v36QZb0ZTYjwacIznRqN6/U8Y0Centu0/UZdh6evHaMky0/57WxU9nTpf4lxhT+9GYGml408DihbPoxFHdPdXU3CtUrkGel+9Tjpx5TBWz62MAk+AkyJUrJx0/doyqVa8uCNVMTQp14G0EMrOzZ86IeFBtiIypuuqY0oDSQPjUgAKf4fO8O8Ss4WIqgSbYXQEyy5cv7++WiklKyyFYb6UgLyNSngRFZJ7FXbt2CWZT1AHzKoAv4k21gv7xgTx79hOkLV++3KDLr2RIBYGQvlStWlXsQloRbVuw5CJnKQRpSMJCkiaMTcXz6/Khjpu7V5BShEW/qg/zaCBaFGeaMLAuJWZX2CfP/aj7iHWCPMo8retaAavu7sO6FD8lCv50eTdnH6otpQFza+DGdW+aPnmsuZu12/ZAFDSQvYLAi7CYcyWDDXcNh7Xg79uYYPG0M7vqxo+fgGZwjOcwt6GCpdxUHWNtqf1KA0oD4UMDCnyGj/Nsd7ME4IJ1Uv8Dt1atyNybiPWEaF1u8VuywSJlB76D5RaMe4YsiiivLwCBsK7iAZuHXZIqV64sGEoBGGFl1QrAJMpAkG4DOSyRtxNjkuPTlpepWEDmgDHlZ8ZA6ToM1lysHl+5ckWsRINgCcAaVluQGcGi+vvvv2ubs+j3jk1LMPGQE13wfkjLtpyyaF+qcfNrID4TAE0eXIcARK/feUZ7j1w1aydHzt4S10YkjjWtWDz0LMtmHZxqLNxrYP++ndShVSOqVbUENW/0Oy2aP5NOnzxG9etU4nv7O5o0fhS1blaXU4I8p+WL51L1SsVoy8a11L5VQ6pSriDnIPaiOTMnUYWSecnrykX2Ingjyv1euTjHRJ6kPj06UPECmalV8z/o8aMHdqlv8Az0Zzb1uz4+NJvTghXmlGBzeKFzOi/UruAFVENgEou7nZjNHWzkEyZNFM/Y8bwdyIy4YKNXojSgNKA0YEgDCnwa0oraZzUNIBcYQBdWYJGjUf+D3JVaKVy4sMj1iH3ItwgSIq1UZ7chCeQAQOfMmSMelIjLhIDpT4ok65Fb7MdDdfv27ZQmTRqRMxTutOgHIBGssxBt+ZUrV4r8orCKIq8nQG6rVq1EvCnKavsDmMQxyMWLF0VKGMmKmzZtWvG7ePHi9Ibp8EFghIc5iJYGDhxI27ZtE/XkfxgDUtFo25fHzLFNmTQudf6rtGhq5tJ/aev+S+ZoVrVhZg1gYQJpeMAUDSIurXU+VbJ4VK96XtHj4g0nmVTEuDUjOMP69u07TVl0SFSpUzUPJU0UOzjVVVmlAYtqAPGcvbq2pVFjp9CazftESMPHj+8pZ668NG7yHNF3m3ZdacLU+XTvvg/HOZ4mbwaYI4f1ZZKdxPSRwykunj9Lp04eZQ+WG+LZdO7MKdq3dyfnUr5A/Xp25GeAM8VgVteD+3ZRhzaNDQI1i04ylI1jcbR3r14EErs5/NySYR+ZMmUSZENz+bm5ZMkSkU9YdoV7TQdezEXZ8Xy/kc+uQsx6O5WJi4YMGkQ7+NlpCLTKNtRWacBWNeDr6ys8AOAFYAufoPKO2Ko+9cf1881b/4j6rTRgBQ0gjlE/BjKwYQC4YQUWoFULBGU9gLVe/GAF6RAelJL8p1q1agGsl104zxlcWZF3UysFChQQdZGmBQ9pmdsRQBdgVuZaRJ2MGTPSyZMnxUs/XH3x8IaFFDeOKZwPTVsW5QFQPTw8BLAFqNXmqkRbiO8EEEdaFRzXzweKNiAAGSApsgTJA3K/Xbr2iHPCfaD4caPTCyaWGTltJ63edpaqlc5OVctkpRjRXHQDCaf/4wVLklMZU0FQyhirG9T9uDYRi4zrHNcfrre1a9cKSznaqFc1Ly3n3J937r+gI2dvUon/3KmD2r6hcqu3e9L9R68oRnQXavlHYUNF1D6lAatp4PpVL/JjsqA1q5dS63ZdqFP3/nT21HFyZmK3qFF0KYGiRovK4DEW5c6dn758+kwb16+iFq07UtsO3fzH7cSsrgCXkBKlytGL58/o8ME91KZDV6rzRyMBzEoXzUGXLngKAJs7TwH/urb4Bc+M61ev0ivmTACxEBY5Z8ycGeAZhXtaBn4OzV+wkFq1+IvechnwHyD8Yym75SJf9ajRowM8R1EnL7PeTmWLKVK3PHjwgF69fkXHjhwRXjyWWiC1RR2rMdmvBrDwr2/ssOZs4G0nPeusOQ5z9a3Ap7k0qdqxmgZg8ZMrtcYGAQbZzJkDugPqAzU8NE21A6usVvAQ1QeT8jgeyvhIMVUWBEOmCIkAXkECYUrkqrOpMiE59vTFGxoyaTud93rwS/WbPs9p0sL9tGj9cerZuhyVLZIpUAD2SyMOsMPNzY1G8wsYFk2MvVhh8QHXWz92a0N5S8mQIUOofv36YhEDD06QXSEfrkwzFCtGFKpdMTet3HKaFq07ScXzpQ/VOTt+7jZNX6qzerZpUCzcL0JY6ryqdkOugWw5clOKlKlo7KjBtHXTWurVbyjVa9jcaINOTNIFSZMmXYAyTk6RDP7OkFH3XEHOyxKlK9DKpfPo9q0bBPAJJt1VKxaRa+asVLBw8QD1rfkD96qGfJ+4e/euGEbcePFoLxP2GXuepU2XlqYwmKzD4R/ff/wgb29vSpU6NS3i3J+GFnzxLEXalb79+lO3rl1EH+PGjWN35XfUm3OAhpUgzZqhlGRh1b8l+2nRooXR540l+1VtKw2YQwO6u6w5WlJtKA0oDTiUBg6evE4j2ML57v1nfshFpCJ50hLSrmRMnZB2HLxCSL0C103fNx9p4PittH7XOZoy6E9+GYloNj3AWvjo0SPxUoRk5foCdxiUQVyuVgD28DL4g1+U7t27J+J0pQXa0H5ZF6v66Ed/YUIex0sbCKuQfxUvWBDESsE6jb6MCcaIMvgYEowXlnUscBgDsKiHcrBYwB1cWsk/cmoD6AELGH9z/JWUbNmyCSA6k60Z0hqKYw1q5KN128+KtCh37r+ktCkD6k7WD2yLnJ59x24W10DZoq5Up2KuwKqo40oDYa6BSAwa1205QOPch9LalYupRZPa1Lv/MGrTvmuwxiL/3k1VKliwqACfLpGjiGInTxzh2NA51KFzb1PVwvwYPGkk8ETnb9jF8Mt/ZHmGBoO54x4C4CnlKd8rcT8zBD5RBnWePXsqi4vtAQ596cF5QLFgHBYCr6KjR4+GRVdh3kfTpk1NPivCfEAO2uGchaupWPEyVptd4XwZheeG1QZgoY4V+LSQYlWzSgP2rAGkVOnvsUUAi/QMNkd0r0apk8fzn1Le7Cmpca38NHzqTv/cjucuP6BKf00TrpxZMyURBEX7jl7lWCBnevbiLVUulZX+qJzbv43AvsAluQ+vkoNdGC84IFgCczC+b926VaSy8WFyDAho/sFgLGN+83PC9EyursJtBqRNeFAv5lV6Y/vXrVsn2gP4RPvFihUTbmXS2g3XMbhkI0cs3Jvhur2AmSAHDx4sXNAwBljX8VIFi6Mr9y1lx44d/uOChXTs2LGCkArxwXih69Chg4jpRaoetIFY4hmcJ8+QNXzFihXUrFkz4WKOFX0A31y5cgmADfdyxDdrBWAZoBtgVUpCdp1Oz6lXvG8+oRt3n4UIfF7n66PHiPXM7vyNcmVJTkM6V2awrwPjsp/wsMWCA5iw4X6P+HJYI5TYlga8vC7Rg3s+NHrsVPqzQVPq3K4pzZo+nlq06eQ/UFMLR/6FjHz5+vXngtKtW9dEqYyuOmso3HPTZfh5LzDSRJjvTpUqFcVna+cLXrSCgCwvKntmmBKUAaDEQhoEi1+BedzI+6dsN2fu3GJRUP4Oy22pshXDsjuL9PWG3cc9zyrCP4so10ijiOeGi761xJnfRxxRFPh0xLOq5qQ0EAoNwJo5euZuATwL5kxFHv1rE/JF6kua5PFp/phGguV08qIDAsy8//BFWERhFdWXtKmCbmEDURPYgrNnzy5AJeKRhg4dKnK6wvIH9l/EFU1nYgukt+nbt68AdCCoQqzsJ94Hwh2wJSM1jXSnNrT/HMc7wVW1Zs2awi324MGDhBQ36B9kU5DmzZuLFXS4sIIBGcBzw4YNNH78eEHug+8gsQJwBTmVVkDAAdCMPgAsGzZs6B83jHYBTuEuW4TZJfEdq/VoB2BZX9A3ACqAKlzKQEQFyy50MYgJPiB4ObzKsVwAyiDGqlKlSoCYLJRJlzKBOF+37r4gCoY3INretPsijZ+/j62w38WChEffWgavD/Tj6AIXapwnnAPJXu3oc7a3+b3lBZ5xo4dQmfJVREzn77Xr0+aNaygCAymQBEE8z5zghSBfXnQqTd94QQfymuMUtfKBiYsgnz5+0O4mr8sXKW++QmLfwf27qXSZChwnqQOf2BmBY0VtTQAk53EcZ98+vSkmx4d/YwvmCF64GsYf3Hv0BZ4VSMFSpUpVOnv2DKXn2PLHfB+eNnUqdeJ81vAm0QruE7CswtW2Ht/3djFRX2W+D+E+HRQLsrYtc3yfOmMxVa5eyxxNWbUN31cvKV/OgM8Xqw5Ida40EEIN2N5dMYQTUdWUBpQGzKOBTXsuCGtm5MhO1K9DRZPAAi8SsHwtHNuEY0O30a7D3oJ4Jm2KeAKUYkS1KuaknK7JKVO6hEEe4GzOFwcBuALzLwTMwHBJxQsMtjgmCZhg+USqmoULF4r4S5SHK+7u3bspUaJE+Okv+vuRIxZkVWBzhLttPraanuFk6bCGwqUV7rD7OB4K1tOp/LIFQQoduMBiHP/884/YB0AMK6O+gChKMiODgKpOnTqiCOIwMYeOTMrRn1MTQEqVKkVeXl4Eq+isWbMCWCxxHNYGpNnZsmWLsNTCBRhuwgCZsu8TJ04IIIv4rbp169KkSZNQNYCk+28h4PY9neUjwEEjP959+Ezus/fQnn+9RQksTLh1rSbOt5EqDr8bixS4PsMq567DK9RCE7x//y6VL5GLSpQsz8zinjRizGQBmNKly0jpM7qK1CuNm7VmK2UmGjNigBjF9CkefF+ITtV/r0vHjx7iMjPE/gljh9HwMVP8Rzplwmi6fOm8SMESxcWFRrjr7hH+BWz0S/Yc2Skv3zNTpUxJdTk1WHtmru3PMZojR430v5dg6PCwaM33XiyiwROlHZerw/cVeFy0YG8QeF906949gCvtnTt3mKCohVhoa8F1L5w7T3/8+SdF53uSEqUBpQGlAQU+1TWgNKA0EEADu/8DF83rFKLECWIFOGbqR9e/ygjw+fbdJxrZsybNXXWENu+5yMyqL6l3m/LBWvFGTBLiKiXwRL8yFhJWPVg3JfDEMQBGuJbipUdKwYIFfwGeOKa/H+3BegpXWylws4V7LeJNMRYI2O+0Isej3Rec7+gXUrp06QDVMA5YXGE50LrvykJ/8kscwCeAJwRAXasn6RanZbmVdeU2UXzdS+Arv/dyl9Htx09faMOuC7RkA1uH3n7i80gEcqFmtQuFS1dbfUUZikXWL6N+W08DefIVoHNXHjBbtx+9evmc3DKP978XRWawuGPPCXr39g1bAHUpgjZuP/zLYAsXLUk79p4MsP+at867Y9aClbxoFZ2iRY/B7v+pApSxlx/wDJnFruMAoP14cW/0mDECgD5j4NmSAWZRvieBKEgbq5k4SRKaz4t9rXnxDozoPXv1FID+1q1b1LpFS2rCXh3Nmzf7xSpqLzpR41QaUBqwnAYU+LScblXLSgM2rQG8MEAiRvzpMgWXW68bj8X+4vnTi21Q/4sTKyqlSRFfpPE4d+U+NWdwsmP/ZcGUu2H3hWAR0iCljT6JkBwHiDEi6rl54RhAl/blSJYPbAuQifjKrl0DEpDAkggXWqTygSRIkCCwpoJ1XJIP6busyfgzQ3PBsUWLFgXoRz82VFpA4Z5rTH6wriDG4jShS58HL2nT3ku0bd9Fgjs1JGmiWDSoY2UmnkohfofX/3DNwMUbsb/65y+86sRW542YLXiSuvDiVKJEPxnI5Xhx/iTwlPuCsv3f/3T3T7DgumbOZrTK//hvVv5NGy1kAwewiDKbASg8MXoxKVC3bt2oA4cZlC1Xjrryd0P3IywALmS3cwBUWEDr1K1D7du0pZatW1HjJk38Qb4NTE8NQWlAacCGNKDApw2dDDWU4GsAL8mIB3zErpFwAU3KbKFwNzR3XAn6ecH50K5fv8ExQV8EzTxcPQ09kIM/i5810A9IaG5cv05vOIk3+oBVK7RWNtkDwCXcag+evEEXrz5ki99XSpowNhXJm5Za1ysq2Ay/fP0uiqdMGkdWC/I2h2tSAT6v3HxM5Yu5UquGRWnm0n9p4vz9lCNTUsrA5EVBEcwZLrMgG0J8EgTnGTlY03K8EWIjcT4kQD1+/LjIwZolS5agNB+gDPoCkABZkexLW0DGcMKdFXGhUpCEGsBPXmsYnxyPLCO32jJyn8wXi/hWkClJOXTokHADlv3K/dgi7nXv3r1inOgblt5ly5YJN2BZLmfOnIQ4VlO6+MREQVL83n5kcPmZwF7rdeMJed9+QtduPiW42UqBBbxZ7YJUvWw2vhYjyt3hcgv3bLygg00YhE5wzVYS/jRw/76PmPQ9nzuUM1degwo4y7GkV70vintE0RJlDIJfgxWttBNusYijb8sAsjrnwW7K5GZwqTW1wIJFOVhA/+Kya1avol69eyvgaaXzp7pVGrAXDSjwaS9nSo3zFw1gpXXD+nW0kMlfHj9+Io7DVbM5r8LW5rg6cwFDWKg2rFtPc+bOIdDLw2oUi1/8Qf4CsgV9y9MvAw3iDlhT9nNs4aSJEwWBCWIKEYtYomRJ4fKEvKESxASxyQDFXrx+T0OnbKfTF+4G2P/wqS+t3eFJe454099NS/of+/btB8d7+v8M0hedPY2tDBwvCmlSsyB5XrxHJ7nPAZyOZbFHE4rC7LeBSaNGjUQ8JFxMu3TpIvJUgskVABCxnyD4AWBDrCTId3rzCw8WHUASFFxp3bq1iLH8g+OeBjCpBgAoEjpf5wUAd3d3ApgD8dGUKVOEpSt16tSCcAhAGInWpfsvmGwRd4qYTH0Qi8UDAFOA5o0bN4rY0goVKggXYLzs4dzCdRgkQgDdiK3SX3DAfvQBgastgCfKIW4UIEimh0E56G0iX0fG9HGVmW4hl689oorNponv+v9FYot4Tk6t80eVPFQsbzr+e/ppIdcvG15+gzgKbMM4740bNxbAE7lUlYQvDWzdtE6kPKrfuCWdPHmUErJFtWDhn277UhsgItp/ROc5IffZ+hYLfs85RUoivifdvnVbhCRo2bINjR/uuW940RRA9MaNmyIe3hBxkaG6ap/SgNJA+NOAAp/h75w7xIwB1BYvXEQeY93pG3+Xgji6Af36CStYI345NLViK+uY2sJdaj0Tzwx1cxNuRbLs82fPaBGv9sLaNXzEiEAp52U9Y1tYPI8eOUrd2b0JYEoKgO8WBiQvnz+nyZxKRB/UyHKBbWHx7OexmS6xtROWq6a1CxDcamNHj0JXbj2mWcuP0IPHr8lj9h6KHTOKyN156fpDKhhMZr277KoJSRJfFysKt87BXapS0+7MCPrwFY2csZuGdqkSKJABKQ/SmIAtEZY+uJICHMISDIshiH+QWgQLAFhkyJo1qyDdka6xOO+GFh8M7UfMJVJlAMiWKVNGjB+gH6AUAsAPJttGzFLbmRcbIACgkhSpXr16AphOmDBBjPPatWsGzxOALUAyyIdAcgTwCUZekNXgGBZTAKB7sssbiGy0goWIJuzGhjIA39AFYkKhAwDPw4cPU+XKlUUVWOJwDWmvI21b+P7560/LJ37zFCkN5/vMki4JuaZPRJnTJ6YMqRKYJJtCvfAkcAUH2RUWCQA8IXgpR3oVEFQpsY4GkHpi0/pVYd55vnwF/ft8/OiBWcawaeNq/zat8eWuj4+4N9Xl+28TXmTpyVbPrnzPm8CkZXJxSzsuPLcus9dI+3bthIW0NN8/2/EC4BC+dw/le1h4A6DdOrWgxEmSUZ/+w7VqUt+VBpQG9DSgwKeeQtRP+9AAXrznz58XAHjKkSMR9gy2JpXjWJUkHJMVGnnGIBOWVbz0G5ItbHUC+MjLL6ShsUoCZE6bOsUoYDjGbqX7GYTVZpbBkPSzee9FATwjO0ekOaMaUaa0ifynk4Tj+IrnS08d3dboykTW2S9PX7gXLPD59OUb8rquixfNzi62UuLFjsasqFWok9ta2svW1a/s1jusG6cMcTZ++8Ec4WKK9CH3798XK+qwNEqB2yM+YGIEANAnfYHbqaGFB2P7ATTxQRoXnAvE8mktj3BhPXf+PCHlAEAdQLAUWDSx6AFWXIBHmdZFHpdbxJQCdMKyAAs9BPn29nPidQBG7IcV1dD5xViQzgOLIdIKgbrv3r0TxEhyH9oEwy3IiIxZ5PHCCAIWyCDOz1m+qKvQlZOybAqdGPsPrt0gogLTsVak5Vu7T37HeQDbsRLLaqBn1+B7PFh2RPbXug8DT7DX1ufnWSsGkLh/AnQCgHbme+0UXvzUAlDcRy7wPfFvzlPckxfV4ImCe9dszs8MMDpo4CBO3TIsAHOu/WlFN+LXnOIkTtx4gQ7f5/ZN+qFZDA+0giqgNBBONWD87S+cKkRN2z40cOXyZXrO1kBj8pLjAUEUE1rw+fDBQ7rLL/3GBFbX06dOC/BprExQ9r/ml1TJfmqoPB70p9m6UpPTeWhBkaGyhvat33le7G5et3AA4CnLAggOZYBYu91cAlstZN3Oc1SzfA5KkSRosZ8T5u6nr0xi5Joukcj/KNvGNn+O1DS8e3UaNnk7HTp5nbqN/Ehj+9Si6FEja4v98h1zNRT7KAvqp1GR+yXpjvwtt8b2y+NwfzUlSHWCj77gpUsCSv1j2t8Ap/joC8CjFkDqH8dvQwRCxuZjykLuefm+sHJjIaIELzoYyuFqqP/wvs/bW5dixtB50NcNFkxGjRol0vfAMq3E/BqAl4M+U7T5e7FOiyk5/YmlBc8U+bl58ya1YY8KkAT9xZZ8uQCGe9J4dt/v06s3dWSQCQAq65zz9KSOf3ekfv37UbXq1f3r4N4zhwFoO44blcy5so6l52SJ9j/xwmC3zq1o0bKNgTZviCk50EqqgNJAONSAAp/h8KQ7wpRhfcIDzZggLtOU26Gxevr7v3//Rv8LZCXzCxMQhVZgrf3OrpWmBNZXU3PWr/v5yzdhXQSr7e17OqBeodjP5Of65UE8lJ5dLW/efU7JGXDCDXfQhK00a0RD/xhO/Try99b9l+jQqRu8Wv4b9WtfUWzlMbkFAVHMGJGp95hN5HnpPnUYtIrG9K7JDKq6FAeynLm2ANHvP34OVroYc/Vty+2s26VbiChfIku4ztEZ3HMEV2wIUkkYEv2/zRw5chj1mDBUX+0LngZKlSpF+CgJmQb27tlDB5hjAB4lyziGuTmDTsQzS+ApW3XhdDQe4zxEyEADtoo+ZOs/UlDBU2PwkCFUid399evAE2XWnNnCKgpL6t27PjSG49XHenhQWABrOXZzbN0G9+IUPS/M0ZRqQ2lAaeA/DURQmlAasEcNpGSXQxcTqSQiI01G2nShnlrixEkoQaKfLqqGGsRLZmgFq8WBPZSzsutnYFZPvACvYfKgmm1nU8n6E6lG61m07p/z7K6pA+pRo5gm+4kfJ5qYSrUyWShaVGe6euspNe+1hC5469w09ef5kdlyx8zaTSOn7RSH6lXLZ9CyKushhnT6sPoC9Fy/84wadFlAi9efYIKKn3G7smxotmv/OUcVm0+j6cv+DU0zYVJXprwJi87uPHhBh09cF13VrZQ7LLp0mD7AbguBmzQsRfqidc2HW7YptmH9uuq30kBYagDhBeAXeMwEeshjjHhmkJTpg0g5Jmd+1jZp2lR454CFHXHt0TmvqSHgKesAgCIkBa65nzg91ulTp2g4h1LoL9LI8tbc+jGQHj28PzX6syrVqVGWalcvLYbTsV0zWrdqCfncuUktm9altfz9wvmz1LdXR2pavxpt2biWypXIQ7OmjacTxw5Tp/bMDsxxnxBRruff1Lt7e9q7ezvVqlqCKpTMS0sWzhbH1X+BawCM9kocUwMKfDrmeXX4WeVkwFe4SBGj88SDL27cOKF60OEhGdklMiUykd8R1pC4BtwwjQ7MyAE89FOkMO5qlTFjRqpUpYrRlwPZ7JJNJ2nCvH309PkbsevZy7c0ccE+ihXDRfy+eO2hLPrL9htbSG/46CykmdMnoeHdqgmQiHyPbQespJ6jNtKKLWdo//FrtJ6tZwMnbKNaDHI3cQ5PSL2qeal9o2K/tKu/I1sGTk4+phHlzJyMPn/5TjOX/0tNevBDncmQzCVpk8cTgPu45236okeuY64+zNEOrrFGXRdRp6Fr6MGT1+Zo0mgbiLUdMnE7p9P5H8fypiJXTdyv0UrqgL8GQDQEkijEBFfjNBRgXgYjMliFISB92rZtm3/5wBaK/AuqL0oDYawBEOZpPYNec9oo7W9Dw0EoixY4vuI4SBD/mZIXzwOCB4BdW8x5unwZ80ew59HyNdtp+OiJnOpM52I/2mMq5StQhBeG09CkafOpxu9/0nnP07Rv13Y6e+Yk7di2XoRh3Lt/ly5dPEf7GGT6+fpxDP8HOnbkAG3dtIaO/nuAZk6bQBkyZWVegHs0jC2p+/fpFmtN6U4dI8EMD0Z3JY6nAeV263jnNFzMKGq0aDTEzY2ZPj+Q5zlPzr2pIwQCux5isp5xPGgfJkFwHztWkMMYW9E1piw8ZEEuM5Tdii5w7Chck2A7fM8ELxBYXaMw8ITrUXcmkpk+YwZlcnUNFByKynr/ISbMg92R9h/Yz2lIXBjwupAv09bzk56ceD5ZMmemIbxibIrYBE3CzXbB6mOi9Sack7Fu5Vy0YtMZWr39LD8MdfpZtPYEFc2DvKG/5mpcve0svfTllB1s8cyWMRlFYyvp2mmtaMqiA7Tj4BU6cuam+OgNX7DjDu5UmXOFBt3SnDJpXJo5vAFtO3CJpi0+KHKDtu2/gvLmSEm1yuekkgUyMFPir2PU79vYbwDbOLGi0Wu/97TzsBfVKBt667SxvkKzH8De5+FLesjAM3ZMnVtnaNozVXfu6qMEazPibPt3DEiaY6qeOvZTA0ixUr9+fTpw4AAVLlxYxO9W53g3yGe27oBgSEnYaADAf/v27WHTWRj3UqxYMZG2yVLd4lmFxRTJ0gym7MCIsbDYC2u+l5cXP+ciCBfdwBZYKlWpTAvmz6eHj5hlnZ9lyBtqiIXcUvMMarunTh6jZ08e0T2fW5Qlaw5q2FhnvYwRI6YYbwRmVI8RM5ZorlmLdrR751a6cc2Lxk2ZS9Gj/YzhX7F0nigTJUpUat+xJ61ZsZicXaLQstVbOaY/KhUoWIT6sjV0yYJZVKasugcHdn5AqDeW3+GwyD+E38WUOI4GFPh0nHMZ7maSnF3bZnNcCfIcunM8SUQmp+nH6TJSp0lDfThdxamTJ6kvr5qNHjNGuLQGB4D68kowUrbs45iYhAkTMmvfcGHhbM1xMR/ZRakPH8uTJ4+glD/PbkVdu3SmcRMmisTzwTkRAJ5j3cfSqhXLxQ22L7ebO3dubq+LcO1r3ao1tWzdSjCXBjb+R898hSUxIsddtm9YXMRdtm9cQoDPL+zWGtUlEnnfekJ9x26hvu0rkHSxhUsuAOqMpYfE0Ds0KSmAJ34g7crgzkxEVDk3nWArIuq/5HyhkSI5Ue4sySlPthSUI1MyCsyd15BOEB8KUAim3SmLDtI/h67QWc4Jig/6rVomG5XnGNX0nALEEFg21KbcB10VL5COtuy5SCu3nrFZ8AmXaEju7CkDJV+ScwvJFud3yYaTomrvduUpUbyYIWnGbHVOPfcSbeWJl5GcItjPYwj3ArjdIscqmLCRAxZ/w1idN4f7vdkUHA4aunDhAnVnJlZHlDH8zELOYEsJiMrmc6qwQwcP8WKtMxXnXLWG2MG1/YMobQWnnDrF7rNx48SlHDkDX9ADgdumrVvozOnThOd1Zl5IlQILaGB9yrKW3latVov6sStt1YpF6c96TahDl94mu8TzKEGCRAGAJypEcgqYGDsi/07JHk0AnpASpSuI7R1mxZXy4P49WjhvGvUdNPKX+rJMeN1i0R+Le2vWrBEqUADUca4E+3nqO47O1UzMpAEAjJixYlEpztM4mSnh8UDFQzRZsmQ0ccoUQREvAejYsR788EseJMskrJl9e/cRwBOMiiOYtVISW8Aq+ZXdc7Jy/Bc+4zi3Yw+2fMI62rtHDxrLeSmxPzCgCBVgVc+DV/VW8wMdMTUDOGdjHU6TgdXkOPxwh2TJmsVgzkhxUO+/xPF5lZYBHdwqDzKjbJnCmWjf8auiVCSnCNSrbXkaOnmHsF7Wae9DORk8OnNflzmfp++bj6Iccn/C8qgvcJXFxxISJxavanapQi3rFaZNDBa37bskxrN802nCx5ktoBnZRTQruwIj/2Sc2FHIxTmSAMBgbI3A18GjZ35079Frusu5RO89fkU37jz3Z+29c++lsKymSRHfEsMPcZtvmBBp0x4d+GxYPW+I2zFVERb8heuO05yVR0WxP6vlIVOkU6baMuexe++e0tJz8/laj0yp4memfIlyUJGEgb/MmnMMoWkLDMyShRmELIYYkEPTvqPUnc9WL6QXsrTUqVHF0l2ESfvrt+wIk37QCV7sq1arGqz+AEDLli0brDqwqFaoWPGXOsidjJRW8tn6S4Ew3PFH/aYUK3YcGj2sP+cPn027d22jtRv3UuKkyYI5Ck6YbEISJkxEadNmoN8i6CLevjJZ4Z5dW2kxW0L79B9GbB42UTt8HgKj/VxmT5Y5uBUAdYzrQIFPxziPahYaDQD4wXrowUCw138W0J49utPkqVMpsFQacJsbyNbTffv2Ujx+aErgCVch/fgW9IMX0ImTJ9Pff/9N3uyOhP4ASOGehOPGBNaScexqiwewM7sj9R8wgOr+8UeoXJKiuDhTFbYWbt17ifp7bCHk14QbLaRulbxUuWRWSpksLo2evksw2p467yOO4T+s5P7dpISI24RF0hqSPHEc6shW17b1i9G/7OKLeZxjoqNPn77S5WuPxCc444L78PsPOiZij3l7aergP1m/thPmvoatkd+/64igTrG1N2+2VKFyNdbXDc79hPn7ad9R3QJEU3bFbt+ouH6xMP/95ftXuvP2gej327fPdOvJefFZzXsiRjRNiBXmgw1FhyAgwseWLDyhmE6Iqrbi9B3IT4ucvZaSQvly0bJZEyzVfJi2G5k9Slas3xKmfVqrM+RobtSokXBhtzYAnTppDP3duTeVKFmOxrm70aL5M2kzx2u27dBNqOd/bKUNqWjZ8JG25dHjB1SqjM7lNlIkZ2rcvA2NHNovpM2Hi3oAoPA4UQDUcU63Ap+Ocy7VTDQagDtPXo5pmQDLJANCxLZ0bN+eps2cSbiR6QNDWIjArDaIrY97du8Wbq7jmEgEsTf6ZTXdiK+pUqem6dOnC1dZ5Bbt0rETTZk+TbgYGaqrc7V1p+XLloncjoMGDabadeuECnjKMXVvUYZTtvwQMZoSeCJXZ9sGOiIgWA8XjWtK3jef0HUfJp3gVCRZ0iWhrBmTBppORfZh6S1iPWG1xQdMsPcevaLLNx6T140ndO32Ux7zF0Ei9PnzN/rMZEJf2aU4SfxYlCIpMwYn40+SuJxnNC5l4bk+fOpLjbsvFqldYAFsVa+opYcfpPYfP/WjpRtP+Zddsfk05z+9QfWq5aVqpbOFyI1ZNgbiqO37L9NUjqV99+Gz2N2+SXFqVquQLGKV7efvX2jFrd204/p2Ju15+8sYsiUvTHde3eAFg4AkJb8UtIMdYBDFPQHSnxezBg8eLNzq7WDoZh/iVF70g5WtV69eZm9bNWi/GsA1kTZtWvqDF13Xrl1L1gSgJ479y9wQqen3OvWpZx83Wr5kHsfA6vI5w9p76aInXb/mTWdOHaOGTVqK54+f368Ece8/vBdkQ9qzcvvmDfrMoTrwmjp27BABgLZu19m/iFNE9RrurwwTXxQANaEcOzykrno7PGlqyEHTAIBfLo7LFACUY4POcWxm544dhWUSqRAkMATwfM4ERUMGDRLAM378+OQxfnyQgKccCQDoRHb97cmut+fOnaNObAmdwi9dWbJm9e8HZT+wxRP5zlauWMFMstE5RnWAAJ6BETfIfgLbwvqJGM22zDr76IkfwdUU8ZNacWLrX/ZMScVHu98Wv8NSiTngU71M9mAPMTWz3vZqXY5Gz9xF85iMKbtrUmZ6TRPsdsxZ4T2D555jNgqCqCzsytygej5y53Q1D5/4CqbiOSuOUA1eMPizSu5g5Sh98tyPNrPL8ja2GD9/pSPGSsd5W/u1q0DZ+HxbUw489qQ55xfxwsErMQwXl9hs0fYV3yNEiEh/Zm9EDdJVoEa7dJYGa47VHH1nyJBBvFCboy17bgMul7BYAGBAFAC157Np/rHDktWgQQOrA1A4+wzs14VdYLcz6+0XKlexGtX6o5GYcPlK1Tkf6i5q2aQ2LV65mcaNcaMLnqcE63WXDs2YHXcyh//E5vrd6CmTFvn5vhKpV9p17CHqv379kurXrUQZM2URLrbDRk2knLnyml+Z4aBFBUAd5yQr8Ok451LNxIAGADBz5spF49mK2Y1JfM6ePUu9eQV+jLs7pWbACHnFrLaD2O117969wio6ksFhUCyeorLmPwBQxHz24fY9PT2pS+fONGnyFMqWPZsoBYunOxNJrF65kqIxW29fMwNPzVAEoYy1SWW047Hm9xrlstNFTuOy/cBlGjBuK00aWNdqYAyW3METt9Ktu89F+psR3atR0kSxqXCeNBzreplWsSvuE45fhSV05ZbTlC5lArZKJxExr8kTx6KY0aMIy/Y3jutFblQw5V69+ZTzsT4RrtRSz9GjcV6+3wtQo5r5g03WJNswx/bjt080+vxCunBPF3Pq4hKL6rjWotqpS9GocwvJ+9kl6lewE+WIm8Ec3ak2bFADyZMnD+AypwCoDZ4kKw4JAHT16tVWBaAz5q3k8Bdn8vG5TfETJOQY7p/8AHUYhFZgMBqN85rCo6pnXzfx0VfZCE7Rgo++FC5SgkaNmyHYdIeOGE8uUQIuBuuXV79Na0ABUNP6sZejCnzay5lS4wyxBgQAZVZKxGZ2Y0AI5j0QCrl7jBWpEvpySpYDBw8K4Dl85EgqEQTmP0ODQT+IAUWsac//LK09OJH32PHjKF26dGzxHMPWkDX88IkqyIVq16kjyIUMtaX2mU8DOC8925Sj2/deCLbev91Wk0ff2lSAc12GpYBVeOKC/XT0zG1mNYxA7jwGAE8I0p/UZ9KhP9jaiXjXlZxP9YL3QwEobzJQDarkYhKpGuVyCJdll8jWvb37vH1Mg4+PJ793T4T1v3DactQp658U1Un38lU9dUnqlL0exXGOGdTpqXJ2qgEFQO30xIXRsK0NQJFSBZLJNYvBGcs0KwYPmtj5438/iG/7zIybUHwMFYXnFeS/jaEiZt03gBfa7969a5YwH7MOzEBje/bsIaQB0hcFQPU1Yn+/rft2Yn/6UiO2Uw1IADqBXWO7MyA8ffoUARhG4/xRR44eFelURjKrbclSpUJF/45+kOoFllakS0EMKPrJlCkT7eW0LZGYuGjg4EFUh4GnLeY7s9PTG+iwo0SORFOH/km9Rm+kc1fuU4+R66g3u6MivhLnzNLylplth0zaRsc874iu+nWoRLk4F6m+wM24VMGM4oPUOYhzRbyrDwPnBxwn+pFddiNwGZBCOfEqfFK2hmZiJmDXdMwGzLG7SRLpctHptxvWv8++uEajj49jwp2PnMohFnXP34EKJcwaYBi5Oc2KkvCjAQVAw8+5DslMrQ1AQzJmU3XevX/LucKfCxb+b9+/kxM/+w3J0kVzxO5lS+ZSyzadDBUx675lzDUBN/js2YMfxmLWgQShMVwTCGEwJAqAGtKK/exT4NN+zpUaaSg1AJCRi1lwBQDl9CiIzYTEZor1scw8W6x4cbMBEbjgTpk2jTowyZHXlSsiL2AUJhwY4jaUYPFUwDOUJzME1WFdnDSornC9PcLWxZHTdtKBY9eoT/tK7KYcPQQtBq3KDSZ26uO+iR4xeITFs2fr8lSlVEAgZqilpAljEz7liroaOmyz+/59eoEmnpjEZFFfKUHsNDSqSHdK6BLHZserBhZ2GrBnAHr42CkaNGo8zWVm1Izp04Sd0sJRT44CQP04XduUSaOpes0/xdkbzwy6ffoPN3gmm7VoR/iElcSMGZOaNWtmF+AzMJ0oABqYhmz3uAKftntu1MgspAHkFkuWLLlIAYAuEiaITwCL5pY4ceKIVTuAT0gMvulnYAuorSTWNvd87aG9yM5ONKZPTVrEzLcL1x4XlsiGXeZT24bFqSa7q+K4ueTpy3e0mPvZuvcifeVYz4TxYtBo7huMw44qx59dZuA5kYHnN0qVMDuNLdSNXJwcJ32Ktc8b4sZfcoy6vcjbt29/GaqtAdBPzPj943/fA2Ujfu33hq7fukNv+RzYquzatYv8/PxsdXgBxrVp0yaDqc+0AHTdunVUsmTJAPVC8uPqtSuUJr1hC1pI2gtKHeQO1cpV78vanyH6/sb3V4bdEDXkQJUUALXPk2m+Ny37nL8adTjSgGS1BbnQ8ePHKAGz2jpxjs3rnBahF7PUurP1M7WZQCheEkezG++WzZsFuVCChAnJ584ddsHtKmJPc+TIEY40b1tTBdsvUq6UKJiBRkz9h67feSZYZhevO0G1K+WimuVzUvw40UI8aKRRWbXDk9b/c06QAqGhArlS09AuVSlOrKghbtfWK3r7+tC4/4BnmkQ5yaNwV4oUIZKtD9uuxreZ7yfIjQi2bnuQSHx/NbTYZksAdMbCpZQpXVqqWrGMSZXWrFyO8LFladOmDccWJhApTGx5nBhb5MiRqUCBAgaHKQFo3bp1yRwAdPrksYSPEsfUgAKg9ndeFfi0v3OmRhxCDbx69Yr6MdHQocOHeMU1CY0cNZLixotHXTp1EnlAwVI7ZuxYAUBDEwf47t07GsnERRs4dxnSqfTn3KG5OeVL/759Bdtu967dOCZ0AuVkEiQl1tNAxtQJaYF7Y1q/67zIufmC05PMXXWUFqw5Rlk472merCkoT7bklCNTMkIKG2PyifONXrvzlI6cuUVHz96i23d/5qlEKpW2DYtSgRypzebSbWwc1tz/8rMfDTs+gcH2Z0rKsZzuhboo4GmhE4LUFCs4VZO9iwSgpTjOHmINFlzv6zdo1MSZtHiahxiDvf8Hl8qFCxc6hEulOQAo9IF0P0ocXwPWAKBfv36hvTu308rl86l5645Upmwlx1e0mWaowKeZFKmasW0NvOYYjD49e9HBQzpW2xEMPEuwOw9ApmDBZXKgM2fOiDQp4yZMEJaFkABQAE/k8dywfj0588ruQM4dWrNWLRHjCRbcHmDB5VjTXrwFKVE2DvoPST+2rW37GZ2TU0SqVzUv/c7Wzt1HvIW18uqtp3SJU7Pgs3g9UUQm94nPLrMxornwJzLF5O23H9/p6Yt3/HlDIBPSl+yuyahZnYJUNE9ahz+/31kXAzjGEzk8o0dNQKMLd6fIEY2DdX1dqd/hVwMAoAcPHiRLA9B/9hykRSvX0YNHjylO7NhUuVxJypU9C/3RoiPBS2X4+Kk0b9kqmjNxDG3ctpMWrFhL3Tu0pHWbd9ItHx9aMnMC7dp/mPM8rqclMyZQ2jQpaOW6LaLNCSMG04KVa+jA4WOUI4srTR49hJInc1zX+rC8WkMLQHfs2BGWw1V9WVkDYQ1AL186Tzv/2UTHjh6mP+o3t/Ls7at7BT7t63yp0QZTA3C1ffHiBfXr04fTqRwQrLbubN0sWqyYvzsYXGAnTZkiWGmRBxSW0GkzZhBiQ4MKDNGPDnhyOpXVq8iFyYXchg2j3xl4SrczxJVOnjqVOnXsSBfOnxdsuCA/AuucLBPM6aniZtIAYj2rl8kuPnc5d+bZy/fJ88oDOnf5Hr30fU9Pn78RH2PdRXWJRAVyp6Hi+dNRkdxpHdq9Vl8Hs65uoMcvb3DaoMg0hIFnbGfLkTfp923u3+f577Jdu7Aj/8D4P378aO5p2FV7lgagHz58oJZd+9KVIzs5tVZ0at6xN71nnefPnYMWTB5DtZq2Y6DZmiqWKU7Xbt6mU57n6cJlb+o5eAzVrlqeLnl509nzl+joiTMi5vPLt6908sw52r7nAJ275EXtevSnYoXyU0xue8feg/Ts+Qv6d8da4oeHXZ0HWx1saAGorc5LjcsyGghLAJo7TwH6/td32r51o2Um48CtKvDpwCc3vE8NgPDp06c0qH9/OnDggIjx9RheSwAAQABJREFUnMDWxkKFCwcAlQB+AKCwRPbm2E+kRwFL7XQGoMmSJQtQ1phOQawBi+caTpYNV9shbm7MdFfzF1CJ9qYwAO3GllZPT0+xBQCFC25Qga6xMaj95tFAqmTxCJ/aFXNx7rX/CZba56/fM9HIR7ZyfqZ3Hz6LjhLFj0GJ4sdkptwYFDtmlHB5/i6+ukF7rm8V+mie6y/KGMs+YhFNXSmzZ882dVgds4AGLAlAL3nfoNevfWkhWz67t29Jg3r8TcdOeYoUGNGi6WKwo0VxoVgxY1CBPDnp8+fPtJytml3aNqMef7f2ny28JAAuIeVKFaenz17S7gP/Uk8u07heLfofp9NwLVKBzly4TCc9L1LBvCqsQijLDP8pAGoGJYajJiQAzZIli3gHG8QeaJYSJyfFaxAS3SrwGRKtqTo2rwF/4MnkQvsZeCZkwp9x48f/AjzlRAD8AEDHchkA0MuXLlG3zp3Jg38jb6cpgdvWKI7xXMcxnjFjxBCuttVq1DCaTgUAFK69vXv2FK6+3TntC0Ax0sAosS0N4LpIlji2+NjWyKw/mi+cSsXjzCz6348f5Jo0P1VPWcz6gwrhCNKnT0+WfEEJ6rBAwhJexVIANG/OrJQmZXIaMGIcrdm0nUb0705/NfrDqJqdIupei9KnDXjfd9JjbY70HzN2Zlcdi+pvnMexUukSNGfJSrrBFlSAz2279tGWf/ZSqWIFqW6NKgLwGu1YHTCpAQVATapHHdTTAFjBv/OCUJpA3t/0qoX658J50+nI4QPkEiUqVahYlWrWrhfqNh2xAQU+HfGsqjnR82fPBcHP4cOHKXGSJDR6zBgqXKSISesUgAZcYMezJbIrA09PxGYyQAQLrrEbGFxtRwwbzjGe6yhWrFg0YMBAqvF7TXZBNP6nhX5Spkwpcov2YXfg06dOUfdu3UT+0Vy5cqmzpzRgFxpYdGM7vXn3lFkrY1C/PC3tYszGBpkxY0Yaxm7ySqyrAUsAUNyL4QY7kHN0Llqxjqo3bE0jB/ZkV9tWwZpshN/+F2j54oXzCfAZJUpkOssW0K2791PuHFmp3/BxHJbxgdo0bxhoG6qAcQ0oAGpcN+rITw14eXlR+fLlaRrnWm/cuPHPA2Hw7a7PLZFqzI0XuxIkSBgGPdpnFxHsc9hq1EoDOg3AwvmeAeAPXuH69u0bIb4HLla9evYgAE+4X4waNZqKaWI8TekOLrhZs2alyRwDCoAI11iw4N69e1esosHKA4Fr1jt2tYXFc+OG9RQlalTqz1bWwICn7FsA0FSpCPGnefPmFe0DgF64cF7EgH398kUUBbj98V+fsq7aKg1YWwNPP76mXf+529bPWt+u4zytrUvVf0ANSAAK92cPXvgLrVzk+M2jp87SzHEj6NC21ZQiaRLymDqHfnz77t/0D36OhFS+fNbdq1H/Kls8IVnYGvqN25/NfbZv0Zg6tGxMew8dE8fUf6HTgASgtWvXpkOHDoWuMVXb4TQggae7u3uYAs8fTLzXt1dHevPGj+YuXquAZyBXlnHzTCAV1WGlAWtr4AsDtFUrV9H8+fPoDQNBALq2rdtQtOjRyJtXvgA8Ae6KFC36S+ylqbGjnSwMQKfyqlmX/2IzO/3dkeIniM/A9jV9Y6A7sP8AkU/tPINTZ5fINHT4cKrBrrbBIQ5CP6kYgCLmU8aAtm/XnhJxnjavq1fFEN3ZYnuF59KZLbGgjEcdJUoD1tbAdK/VIq1KgthpqFaqEtYejurfwTQAAAqrReXKlal69erk6uoa4hn68bNh4MhxVK1caRHT2aBuDVq5YSvfS4liRo8h2j1+2pN8ff2obIki9JUJhSCvXr0WW/nfu/cfxFcscGoF5ESFC+QRu3btPUSVypagzBl1rriy3J27D6h08ULyp9qGUgOFChUSLsyrVq2iksxar0RpABqwFvBE34P6dREkY0dOelMkFQcKlZgUZfk0qR510FY1AF/++fPmscvrUHpw/74YJqyg9+7dFcAzNtPpI8ZTy2obnLkA5GXNlo0mT54sAKKX1xU6zKusAJ6Qu3d9OF7zNPJwhAh4aseCF60p/KKVOXNmevrkCV3keNNvX3UvQL6+vrRsyRIa0K+fsLRq66nvSgPW0MCD98/p4n2dFad9jsZqQcQaJ8HB+7x8+TL99ddftHLlylABT6kmn3sPKVuxitS131Daf+gozRg7lBCjmSl9Ws7pm56mzVtCl7yv0YMnT6nfsLGi2ujJMzlGVJeq4+CR4zR17mKx3819El25el02LdK0tOnenwpVqEVOkZy47eH+x/AFC6MPHz+htk0bBNivfoRMAwD/WJCoUKGCWKAIWSuqlqNpwJrAE7osUboifWD+j17d2gqiQq1+69QoS/fYHVfJTw0oy+dPXahvdqQBnzt3aDEn0/5uxCXViV8sYFUMjaUQddOkTSvagdutQeH+EzGZUWj6Qbvx4sWjWAyYjcmB/ftFPrzqbF1VojRgTQ0svLpJkAwlj+9KeeNnsuZQVN8OqAEAT8RrTWQStvr164d6hoXy5aanV0+T35u39PzFS5o0arB/GhQXjs08s28z+b17T7FjxRR9Hd3JyX31pFSxwnT2wLYAeyUAXbdwBqdwiUbRmeU8dYpkAcqAAbfPUHeaMsaNIjBbrpLQaUACTyzYLliwwCipX+h6UbXtTQPWBp7QV8VK1TlUKxXNmTmJpk4aQ5279fNXY79BIyhJshT+v9UXIgU+1VVglxq4fOUKvWA2M2MC99hLbEFMxg+p0AhSteDGZkxgCUUaF7j2hkYwXjDsGhP0c/z4capcpYpJMiNj9dV+pQFzaODFZz/yvH9ENNXEtZY5mrSJNhAf3p7TK1lbTpw4wUBG5wpq7bFYo39zA0/MIVKkSOIThdOpJE6U4JdpwQIqgecvB03skLH4kZwjUbbMvy7CAHj2Y9KRds0aCbbdoyfPCCtrnDjGFxlNdBfuDyngGe4vAYMKsDbw/PjhvRjXp08fqHufIZzD/SxNmTCaMqR3pcrVdc/IWLFiC06SiMykfdXrEqVLn4kuXz5P2bLlosicEz48igKf4fGsO8CcP3OScLjZGhMQSHziMqEVtBFYEni4xoZW4GaLGFZTgrHIFx5T5dQxpQFLaWCzzyG+Br9T/NipqFDCrJbqJszbBbGXqUWmsBpQeP77tgTwtOR5u3PvgWj+ts99ypcr+y9ddeo3jOYvW01zl64WfzNJkySmK0d3/VJO7QhcAwp4Bq6joJZ48+YNubm5iTCfoNaxVjm84+XPn59+//13g0OwNvA8deIojffQudkvmjeDEiZKQpmzZKOTx/+lHl1b07//HqCChYpS7+7tOCfwaVowdwZt3byW8uYryMaTF5TJNSuNGTfd4NwcfacCn45+hh10fnCHjcoMs3goGRIXzpeXjnP3hVbiMMlP/PjxCbk8jUmG9AHJJYyVM7UfLrcp2Ep767aOLdFQ2YyZMokVfEPH1D6lgbDQwAGfg6KbCqlLh0V3Vulj7969Ydov7mEgKwvPYm/AE7Ggjx8/o9ZN6tOR46coSaL4VLxwgQCncBrHleKjJHQaUMAzdPrTr92/f396/vy5/m6b/L1+/Xp6wjwYhsCntYEnFFaAgeW6TQGfFyVLl6eBbu4B9DliaF/xu0GTFrR711aaMW8FeZ45RUMH9QhQLjz9UOAzPJ1tB5prNs7HifQpu3fvNjiruBxDGZ9ZY7FyFtJ4TNR1dnamhBzTaSzmE23nyaNjOjQ4kCDuRC66tOnSGQWfqVKlFi63IZ1LEIehiikNGNXAqede9Pb9M46zcqaqKULnZm60EysfQJ7dsmXLhukojC2ghekgrNhZaIHn6XOX6OTZC2E6g1Qc25kqRUC3c3OM4ZTnxTCdh613FlrgeYXDc94y4ZMjSoECBYLFri910LZtW/nV5rfIWHCK86Driy0AT/0xmfot2W+dOATA2TkyGxGcKQbnhf/yRUcsaaquox5T4NNRz6yDzytKlCg0cPBg+vDxEx0/esSfeAhEQ85s9Xz48CENHjiQRowaJcBjcEEbgCfcU8aMHsOstmcIllTkEZVst1Av+sLvYcOH0dSpUylV6tQhArrIGTpj2nQCqRBuTohR+qix6GbIkJ4GDR4i8o46+GlV07NhDex5cEKMLmuyfBQ9UhQbHqljDw33JnzsSYzdf0MLPKEDMJ+Xql7PntRhsbHCffzYsWN2A7iwsJveiIdSaIEnlAygdfToUYvp25oNIxzIJRzGC9ob8MQ1wnds/sdbJqjU3ru13615LVmjbwU+raF11adZNJA0aVLOxTmVdv7zDw1zc6OIbD0cwlvkw+zTq5cgAhrE+TiHjhhOiRMnDhYwfM8P8aFuQ2nbls2EYPH+A/pTVAa8ffr0oc8cm9mgQUORxmXi+HHkxaur3bt2pbGc2sXYg9TYhD99+kTTGLgibQyAJ/KK5smbl/r27kN3fO5Qc0430KpVK0qcJEmwxm+sP7VfaSCkGrj42FNULZOsYEibUPXMoAHkNsTHXuTFixeCzVt/vKEFnliATJHCMRkkQ0o6VaRIEZo1axZBN7YuZ8+epcaNG3Oe7vm/DNUcwFO/0SRJAjIR6x+3h99+fq+NhhrZw/hDO0Z7BJ7HjhwgP9/X/C65nj7z+96rVy/oCMeCHuPP82eP6fTJY5S/YJHQqsbu6ivwaXenTA1YagCr6TFjxqRChQsLxjC4yBYoWJCSJUtGHhMmCAC6/8B++t/A/9HI0aMpAbvhGluBl21ii/hOtyFuAnjGiBFTWFiRV4y7o2HDhomVq+o1qlOe3LkpWdIk1I2B54WLF0V/Y8eNo7QcjxqUfgA8p3N+Tzx8sQLWtWs3avZXc+HqC5dhgM+8DESTMMhWojRgTQ1c9b1Lnz75ssttJCqS8FdyFWuOLTz13bBhQ8LHXgQppAxJaIEn2qxWrZr4GGo/vO5bvny53Ux97ty5Bl0qLQE8p85Y7M88ajcKMjBQ31cvKV/ONAaOOP4uewSeOCtFipUmr1sv/E9QD2bEhRQrXpp69x/mvz+8fYkQ3ias5uv4GgDwQzzoWA8P4XK7n91Z+/TsRa9fvQrg8qCvCQDAt5wLzm3IENq0aZPI2zZ4qBuTgVTn9Ca/5mj7LUIEZjbLQlOnT6fU7HJ7/vx56tm9O93hHKSm3ClwDMBzBtebO2eOcMXo3qMH/dWyhQCe+uNSv5UGrK2Bk8+viCEki5uRInPMpxKlgZBqwBzAM6R9q3q2rQFLAE/bnrEaXVA0YK/AMyhzC69lFPgMr2fewecdgYFhseLFBQBNwi6rh/89LCyUr4wAUABCpEwZymBz44YNFC1aVGEtBQtlRI7tNCYAuhkzZqTpM2eK7UW2gHbp2InuMGutMQAqLZ6zZswQcaO9evemFi1bqvydxpSs9ltdA5dfXBVjyJYgs9XHogZgvxpQwNN+z52lR66Ap6U1bJ/tK+Bpn+ctsFGHG/CJ/GkgJjD3x1K0DwhMNvdY0Z4xQBTYhWKPxwEMi7IFdIy7u4iZPHLkCPXu2ZOePn0aQA864OlHI0eMoE0bN1JMTvI+momGKlWuHCT3WegmE6dBmTBxIrm6upKXtxd16dyZbt+6FaAflAO50LQpU2kOx+UguXA3tng2adpUAU8oR4nNauD+6ztibDniprfZMaqB2bYGFPC07fNjzdEp4GlN7dtu35JAy53f4RAfrMRxNBBuYj4BMF4y8YG9JPEGSHn+7JlZrzSk80AKkqDEI5q1Yys2hrkWKVqUPDgWEy6xhw4dooGc52r4iJEcS5lEjAystsOHMrnQ1i0Um/NtgrSoYuVKwdIT+nHNnJkQ8wmyI6zWIRZ04qRJ/vlGYfGcMnkyLeAYz0jMntuNx9OIb6iIVVWiNGCrGvD78p4+fnothpc1TlpbHaYalw1rQAFPGz45Vh6aAp5WPgE23D04PZYsWaKApw2fo5AOLdxYPuE6GTtOnJDqyf7rMTjC/OGOGt4Ecy5UqBCNGz+B4sePTwcPHqSBAwZw8uKnBFbbIZyyBcAzJuddGsxEQ5WrVDHpamtMfwCgmRmAAujCFRc5xnp26063bt4UMZ5Tp0yhhQsWUEQeT1cGpo2bNKHIDEKVKA3YsgZuv30ohufiEotiRopqy0NVY7NBDSjgaYMnxUaGpICnjZwIGx1Go0aNqAm/JylxPA2EKyQCC1MMXkkJjwKLHiyf4VUEAC1ciMYzCy5Ybw8ePEC9evWkXuyGu23rVnFduHFqlWrVq4VKT+gHFtDJnD4FaVcuXr4kXHBBYoR0KmyAp+7cJ1KoKItneL0a7Wve9989FQOOEy2hfQ1cjdbqGkCMfcWKFWkihyTUr1/f6uMJ6gBev34tWM9RHq5/4AOAfOE0W8/M7JEkGg6H/wF4rlixgpL/n73rAI+i6qKX9JBKIIRQktB7b6JIrwJiF2z4IwoqgigIFlR6ERBFBFERFEVBBZUqINKl9xZaQkIgEFJIDwn533nLWyab3dTNZje59/s2O+XVM5uZOe+2qlVpMRZlc4itYCvwjHpjMM2YOt5Whmv14yyNeUyt/qKYaYClinwCs7Jly5KLDeTAMtP1lc24u7uXymTEhhiCGMIEFwQUeT93Cx/QjRs3kpvAZ8qUKdRHEE+UKaxAA6qCENWqWZNOnz5NK1eskGa8Y8e+I4lnaV4IKCy+XN+yCFxPiZYd+rj4WLZj7q1EIPClCKxmS8QzJCSE/Pz8COm10tPTJTmCTz/kQRHEDvmlmYAW/qcJYgE8bYF4xogUJ3mRkIvnKSL8cl6KchlGoFQjUPg3bRuDD8TAS5hXOjg62tjICzZcmHWCXLHcQwBBgerUrqM/UE2svDZr0UK/b64NrOg2a9FS35yPj4/MSWoOgqtvlDcYgSJGIC4tQfbg6VI6rUaKGN4S3fzatWtp8ODBNjVHvCPg4yoWqfENjRwWrSF4nuJTErR0xX1RunXrRlu3brV6LFOSk2nUiCF5gmvV2u30mcgpysIIMAI5I1DqyCfgwAOlnPB/RJ7Gkix4QHoJc1vMl4Vk5FmYT02aNIl27dopsQEhhGbyvXHj6OrVq2aDCcGFPp3zqcgXuopcxQovNK3Xrl0TZr5jpA+o2TrihhiBIkYgMS1J9uDu6FbEPXHzJQ2Bh4T/vK1JYGAg3bx5k9asWSOJUXh4OJ07d05OA/ECYEpcXgTuYykcAjWFVZAtkPiPPxxD0TejCjdZrs0IMAJZECjZ7CvLVLPu4KYHP8gSKyDYglixlu3eFYbvzsciuNBa4eOJ4EITJ06kz4VvZkXhA7p9+3YZBRcEsbDpaEA85wriuXTJd+QofmdvvT2aFn71lUjDUl8QXUTBHUXnRRAiFkbAFhBIz0yXw3S0K70+47ZwnXiM5kMAripq0RYaUOUmgecpB4kzH87W0lKc8PGdNuk9evapPvT4w13psX6d5dCGDxtEv/78PYVcOk8vvfAErRTbR48cpHFjhtMLA/rSn6tWUrcOLWjhF7Ppv93b6Y1XBxH8PiGy3OjX6Z23XqXNf6+lR/t0oB4dW9L3330lz1v6D95rCvtuY+kxc38lF4FSSz5xSfEQKakBiEp7gCHtvyxuuCCeiHC7Vqxmg3hOmDCRHurTR5rBzhHpT3xFFFwQ0HFjx8oUNwW5SaNOSnIKfSbSqywRxBMvL2+LtCsvvDiIGjVuTJ9+Nlf6gp46dZLeFHlALxjJA6odN28zAtaAQHqGjnw62dlbw3CsagxI3bV+/Xp69NFHpe+aVQ2OB8MIMAJ5QuDHZd9I/94fV6ylSdM+pXPBp2W9aZ/Mo1Zt7qeAgOo094tv6eFHnqIjh/bTlo1r6eCBvbRuzW9SC345LJSOHztMWwTJjIuNo+TkJBFTYiv9tXoF7dqxlRZ8MYdq121IV65cpolCk/rPlg15Gpc5CkFTD7NxLJyoxZP777+f9uzZY47muQ1GoEAIlGryCcRKYgAi+HhylDDd/wMIYVxcHL0vcnsiqi3SzSC40EN9+8gbMQgi0rDMFWlQKvv70w5BQJEGJerGjXytEqKfZBG9b+7cT2mxiGprL17Ux737Lg168UW5ao5+VBAifMPUd8Tw4dKcqyBEt0D/7VyJESgAArfv3Ja1HO1Kh598fiD6WOQEHjZsmDCvX00wzywqwT2C7xNFhS63W9oR2Ld3N+3ZtY0uh1ygBg2b0DPP6bSXHh6e0jTYTlgweXh6kbNwoRk0eBjVqdeA3NzcadbnX9MvqzbR1Bmf08vDRpJfJV3ucFfXsvTq8NFUsWIlchdtLPvlL5o5ZwFNmDJHQv394oUWgxwL78nCbxWxTt555x1q27atJJ79+/eXhNtiA+GOGAENAqWefIIUyABEJSQNCbS5MBli0fl4YtUPaU6g8YQ2eNr06dRdhP7HdVeC7TbihjxD5OesVq0a7f3vP0lAb4iQ+nl94cPN/TOhQf3u22/JUVyDse+Oo2efey6b2XP16tVp3hdfyHygZ86cobcE0Q0+ezbP/agx8zcjYCkEktOTZVduDq6W6tJm+oHp/oQJE4p0vPCbVFoLpGdC5NWxwkIjPj6+SPvlxhmB0oJAn76PiufwaerT8wGaJDSTr7w2KsepOzjYi5RtfuTu5pGlnKND1gU6e7EfUC1ABK/SBazq0LmHLH9JRMWNvHaVxr79Gn02Z2qWNopqx18srs+YMUMGeYLS5YZYYI+IiJDd/fjjj9SpUyf5XjJw4EAKERGflRw7dkxaduC+0717dzp69Kg8tWrVKurVq5dcVEfwqF9//VVVIeTnHC1Syk2ePJkaC6uvDh060P79++ldsSCv2sECPOQbsVjfpUsXWb9du3bUtGlTmi7e05RcvHiRnhPvUo0aNZLE+X1hwYb3LcilS5foySeflGMAqYYVCgRpfMYIq7PmzZtTq1at6EPhboU0SSzWgwA78YhrAfIBjVhUlHAqFyvMtiocYOjelQNpBPGcJF4O1wniiQUGEM+u4iaJFzlDwW8AN69p02fQu+PG0n/CJAWrhFOnTZOh9Q3La/fh4zln9hz64Yfv5coo8ngOEDdw5SekLYt+aor8n3OEae7bo94imOC+/dZbMv1LXRGFl4URsDYEElJ10W69nbO+aFnbOItrPJ5FnDsa9zEIXsDwAoWgNzNnzqS///5bvtAZu88UFxaW7HfLli2EF9GSKK+//jo9//zzFpma4QIrnlHBwcG0efNmeuKJJ4T2riK9JZ5RCMK0dOlSi4zJ0p08OeAFEYCwHE2b+B4tFT6Zf29cQytXbaZKlavkcyj3FrWNVaxY0Y9q1Kgtg11W9KsksPWjW3G6HLLGypvz2O3btwlEDhGGU1NTqYJwNUIgRJBG3FuaCdLXpEkT+v333wmEEx/kuwVxhPUYyN+RI0dkebTz2GOPyfcqEEa4LOH/EanrevToIfdhCYLFMgR1PHHiBD0g0tzht4V9/LZgMbJt2zZpBYYx4QOrsNDQUElSkScdpBbvZXg3B0FFrIypU6fKVEdff/01vfLKK7Ie2kLAyP+E4qB37970P5FHfYVIb/fUU09J9yYEmcTvHN8s1oEAk8+71wEPcETAjbn7oLeOy5OPUYh/aozfGLHKRyslpihulhOESdyGdetkVNuJ4qZjiniqSQO7tve1pZmffCKCAr1JO3fsoPfESh0IKFYNjYmOeM6WxNNJpO8B8XzmmWfkTddYeRzDDRg31k9mfSKi344WBPSU0ICOos/mfU61atc2VY2PMwLFgkDC3TyflVw5z6e6ABkZGXT8+HG5MGWpe+44EZG7YcOG8kUMPlt4EVyyZAkNGTJErvR/JCw88FKHRUhoSz/44AN5H7p165Zc+VdpLV599VV6+eWX5YsoNAJox83NTb7ooQ4C7EAbAW3IG2+8IS068PI3QvipI/8l3BYwf+y/KNwKEAkWYwBZ2rBhg3yZbNasGc0TwdzwookXXdTZtGkTYSzQhOAlsLa416Gd8ePHy8iyeDmG7yxeLiHoHy+YkZGRhPamiftwUFCQPIc/IOV79+7V75ekDeBgCcHvRGmLVH9/CfeUlStX0vfffy/fJ/Bij32QCZBPEBKk0MFvEZqlkiDz5k6n10e8Qx06dqNZMz6mJd8uoD+Ev+bQuxrQTOHbXVBJu31P44a0LRFXw6lTl17yPcBVaCCTkhIpTfyPXBPHIf5VA8hQg1rQvrX1EGMCEYYhIJ64piCHCxYskMcQfBGWWSB/+L9CefzP4l0KeXqXL18ut7H/wgsvyDq//fYbde3alRYtWkRDhw4V70E/SPIpT4o/qI//XZBctXCG/3/s476jFdxPYD32iXj/wsL/H3/8If/3ce8ByfxKBG3EvQD3gJ9++kn2Cc0t7r+tW7eWY4TlHxZJQDwbNGhAs2fPlvsYA9pj8qlFvHi3mXxq8JcBiDw8bNKcyVto9kpL7lLNJZOrWXiBwcMBH2zD5OJ9QRqxCgdT2wniRaeHMLXNS1h3eSNr04Y+Ez6gw197TRJQBCECIYX2FAFGILiRwvRj7qciqq14IDuIGyCCC2EFMS+aCPQDTedsUX/UiJF05uwZGiFe9OZ9MZ8CAgPojpgHJDU1TW+SC9LKwghYEoFbt5Podlqi7LKKW0VLdm21feGlfLjw14YmIDExUW8CZqkB48URhODtt9+Wmk8Qv5xW+gcNGiR9Un1FVG9osfBCBlO1nDQKMImDhhUf5CsG8UCfuIeiHUQFx8smtBx42YPmAx+8VKanp8uXQ5BJvASCoP78888i2nc9uUD6yy+/yLLQYoBgglRCwwGtBzQhkJy0Mcbu4/MX/WAp+Iu0nzFvDpXPryLtRNO40qqDhOK6QpBqBtca16Nfv36a0rrNw4cPy4UG/OYWL16c7bwtHvhv9w7hchNEjzw+gEaP/Zh+/P4bsXCiS6fjId4Jjx87JM1yD+zbTc88/xKl3U4XRCwm21QTBZFEsCGtXDx/jlKFdRT8RXfv3iaCEiYL/9ARsoh6psfERNMrLw2kUaPfp0r+VYmK4M0c/5swhQUBwyKQepeBFhMCDadWQDJBQCEdO3aU33gHwkcdR7wMSMuWLeW3MuOVO+KPahPvYbhPtG/fXp7CPtrXyoMPPih324j3LwhIp+oH2lUIFr8qV64sF86wCALTWtyHcI/B4hzIr7JEwaI+3KiUsJuCQsI6vovgJ24dEyvoKMqKFeA08dDEzcJWRAYYEqvVpU1ANPeJFbr5wocSTvW4kX8kVvJB0fAi5CNysWHFvbt4QVI3+bxghLIthZ/AvPnzadSoUbRr5056Tdzg7IV2HJrxDEFAEcAoMCCAdu/aJcnmu8L86xnh5wBSmVdBP3jAo583xMvsmTOnhUZiCFUT7R4/cVw2M3PGdLp8OZT+JxK14yGYn3nkdRxcjhEwhcD5W7rVeGdnL/J01PktmSpbGo6DLIHM4SUHC01YgFIvWJacv7LEuC780nNa6Yc2AcGQ8EIG/yhoOKGxxTxy0iioucD88wtxf0Vwkj///FOS7rnCZaBv375S+wVzOvWyB3IJszcQGmg1oR3D+EA8MV6QFgTCg98YNLT4XLlyRXZVpUoVqalV88pJG4N7plZatGxDPXv31x6y2e1N69fQ6lW/WHz8WACAyaUSkEqYdWNRANdGCcxx4W8Mgca0p1jUhUYKixqmNO/w/wOxBVlAhFVow0BwrUnsxEvDB++OpE0iim16ehp169mXHn3yWTnE7r360dYtG+ml5x+jpcv/oFnTP6ajh/bJBeiRrw0S0XE/ExH0vUX9UcKPM0JEu42WqVeGDX9b1o+JuUkDnhC+kXUbiPb/oolTP6WmzXRkDQWwkDPxo7G09Mc/9AGLZEUz/wHhw4IVLBvwfw0rh927dxMUFxAs/OA6QrCADuss/A9DcK9QAhKHtrAghf9f/D9iGwJyaEzw3pLbuwu0mhBoXiHIpYt+IOo+ASsK+KriPoJzIJ2dO3cWgR7nyg/mp3xP8XvGQpcSWAayWA8CTD4NrgX+QbCyEy1Wb7GCa+3iJMwMSmOAIdjvrxM3RpjFQtOpBNFqIV7iZQvmWzC1ze2mp+pqv1GnlTDlmD17Dr016k06KvwftBIqHPLxgbZ5rBjDQGFqmx/iqW2reo3q9MX8L2iYMC05L1Ya0a4SvLzNE1rY0NDLNGnyJPnyqM7xNyNQ1AicitGtilfyureCXJg+M9JTxSKOc2GaKLa6MLHH6joCWIB4QvAiN1gsDB04cMCi4woLC5P9BQUF6bUDxlb6leYAYwbxhMDs7bvvvpPbpjQK8qT400kEIYHAHA9iuI+XQSV42VOaEbw44t4FvzEIzN5UBHZoSUA8oSWB+d63IkgbrEdg1gffQhChnLQxqj/+Nh8CWJiACSYEAWFOnjwpF3DxO4OGXAm02YqIYOEFpAMatJw071gIVuQEgW60z2vVbnF/f/nNcnJydBKBdi5SBd+KgvhU0A/pcUFCewgy6ubuIZ/xo8d9TPgYymSRogUfQ2l3fweaOutLui6I6YTJs8nFQFFw/OhBug3TXPHOYQnB4hkC/2Ah4ODBg9RJkLcjIogQ/u/w/4jFLBzHPQL/7wioBlNYXLd9+/ZJLSdMbbHwhLbgF/zll1/KocMPtKACU1v87rBYBoGPJ36LWNRA/7B4wJhBfuHLifetp59+Wi5mwfwb50Gaca/CfRFtLVu2TN7vsA1iDYsVFutAgMmnkeuAH7UtBCDCPxtWfwpCroxM26YOwbkcJq+mHmTQYDcTN6SCEkKAAVxr1qopTMV85OqkMYDwuMDLlDFTMGPljR1DP1WEaZt/ZX9JPg3L6Ij2GurZq6dcaTY8z/uMQFEhcDrmvGy6drkaZuni5uUtdHzzVPKoUJk8K9YjT98m5OHbmNzLiSAcZezM0kdRNYIXH7xEI8KjVpS2TnsM2yBmWMBUpM/wfEH38fK/cKEuVQN8ndSKvrGVfrxIQhRhwLbSXGDblEYB53ISY88ckE0ITPrwATGHiRxE9YNtRUSgJalRo4YMbgMfLhB7RLkEkclJG4M2WMyLwCOPPKJvENpNUwI/TywWgHyAaEBDmpPmXetjB3N1aEEL80w2Na7CHvfw8JRN1BUpVIwJ0qwURO5k3qE7IoalryC0+BgKnu3dezwkAxC9MfQ5kZJlLUGhYE4BIcP7iaNYKIfgfgT/7PnC4gpm8fD7xmIPtJzw2YYoE1iQTwT1AvkDOYX1FXzGQQyxYIE6sHbAogIif4OIQtAX3NiUqDFo9w1/ByCM8B2FRRvcArDAhwURBFf7+OOPJWFGuzARhz85JDY2VvqvYxtmxSiLvmDZ8aLQisLyDYKFL+1vUR7kP8WKAJNPE/DjB2zVAYgEYQFBNvwHNjGdEnf4uFhRD7+7+m9sckiTclKszGFVrzCCF6VLF3V+D8bawUowbr4NxEPZ2AuZsTrGjuEmevz4CWOn5DH0s3PHTmkGVRiia7IDPsEIGEHgwo2z8mizCnWNnM3/IZ+qHSg9bRLFRITJjwhJIRtx9XCmNo8vz3+DFqyhUgNoX6pMdY+XJbwg4f8Wq/Mgi4X9v4UGAi+r8MMEmUT0SAQCgeYJL27GVvoRqAM+m9BAQkuBl0QEHgKByEmjYGpeOR3HuGCOCxcIaIlB0mF+i7Ghf/gRYrEUZnEIRIQX20/FAiLMMUGiQUYxLzx7c9LG5DQGPlcwBOALiEUUuRCqMbPNS2tKu25M866tb6novdo+i3M7ITFeEPMbkkClC0LlIAigVq5FXKFdO/4VC+iJQmM6l37+aQm9/sqzNH7CDAoIqqktWqhtLABhkV57/4EpPQglSBmuOYLxoAwW9bXmrugY6VJAULGABBNspSFXdWAGC99K7bsoFjBwr1KC34h2H8RVu49yuL+B9IJg4v6gBH6dsIjAuxjMt3EPU4K4HjBbxkIf7nNqjgjIhjFgbFhsw7jzct9W7fJ30SPA5DMHjPFjdRcrPQlWmE8NK8NqJSuHKZTYU/Arwg3dlMAvU636myqTl+Pw/U3RmJYZq2PoOG+sTG7HEMDIlBZX1Y2PvyVXBdUNVh3nb0agKBCAv2dySox4qbCnVuULTz7jbhyni/uym6WBeLZ8+FtycTceUboo5laQNtVLj3rZNmxDvUyBpOJFDSZq8Jt7TQQug58jTMUKIggwBEF0Spi1VhdkDr6c8G/CCx8+plb68aIIk1ZonKB5wr0DJBTENSeNAgggRH2rZ43aN/xGWZDMHSJCOLSeiKoJYol6GBvM+eAbCIFZHDRmWNyFdhQaT9z/8Lx9T/jSQxuakzZGNsJ/zIoAiKHW5zMvjWNhBZKT5j0v7ZTEMnFiQeXzudOoX3/d//xsEUF37HuTskwVaVx+XKHzqcSJE8E6n8cshcy0owijtjn872kF9zcVDVd7HNuoj/9bQ0EdY/676v6gyue2r8qZ8hnFfctYP6gHjacpAVlVgbRMlcnt+PChz1P7Dl1zK1Zk52/c0FmUFFkHxdQwk89cgIeJAm6y1hSACGMy9BvIZRol7jRWsvBiox6AhhPEza6qJtKZ4fm87uPFr4LwX4q6a75mrB78EgoreKnEyrNytjfWHh4M6iXQ2Hk+xgiYE4HtVw/J5qqUr0OuDi6FavrSwXl07r/l2Va73X28qFX/peTsZjxQRaE6NXNlRLeF/PPPPzLfHAJyaEXdi0C+oFkAmYKJGojWThG0rKDkc43IU5yb5LTSDxM5+FeCECM+gArikZNGAeav0EQoP00QVZitqX1EzIVPPfZh+QFBlEr4iUF7GSCCpinB2BDZVpnlqqAmOA9tKMzrEGwE9z9F8EFolGbFmDZGtW3J7317dtCMaR/TJ58uoBo1swY9suQ4irsvRTahdYJGCul4sPBgTPNeWn3svMTvd/yEmcV9qWyi/8cff1wSRFg/WKMkJibQxvV/WOPQbHpMTD5zuXwwSbCmAERYgYI2trRLE5EQuXGjxnTosO4F2RCPpk2bSUdzw+P53QeB7SYi+v0sfJKMSf369and/brkycbO5/UYFhSefOppmi1yfyoNirZuFWE6A1+Hwpj2atvjbUYgNwR2Xdkni7Tzb5VbUZPn74jIkcc2DafIC7rozb5BdcivZm86seUzKudfhVr0/Y4cnG3jfoagPUgtAm0mzEuXLFkizcOg4YMgsAqIIs5pBQthyodKe7wotk2t9OO+AbM0QzGlUUB5RTRRJ7d91S7Io5Z4quP41pJO7XEQYlNB83LSxmjbKMw2FpbT7wjf3LLuOTYTGxsjXDCCpVlxjgVt6KTSiBkuaipNlfrGeWW22FQ8e2Ey/a8ws/5cBMPDAoIpzTug0NYtLmjeEFFpCR8Wq0MAi1P4WJvAIsMc1nPmmpex+7e52i6Odph85gF1mDVZQwAivChgHExARDRboSl8/8Px9I4wPbtwN0+VupTQEE6YOEGadKljBf3GAxdpUKKF5vMfEaFRmfriGkDjOVFoA8qV04UDL2gfqIff2LPPPSv9Glb//lsWU9+qQsv7/vgPqbowR2NhBCyBwLm4cIqKDRH3GjvqUfVetMv89J0ucoQe+muQ8O0Ml/eseg8OpoAmQ+TiSmzkYarffhLZOTjlp8liL4sUK/CzhN8kIsUiAIfKhQi/I5U3UQ0U6UWQikQF4lDHS9I3/DqhKUXESVuUpd8tFIHl6lDX7g/lOPwevR8mfEqSgEAiKJYioWpuhppvrQ8fnlX4/UMjjQUKaEKRbsOUj522rmqfvxkBa0cArgosRYcAk888YosVwHIiWAKCIxSXlOYAQ4aYg/xhBfYbYea1+vdVIljPMfmCiwi3eBmExtJcJN1P+BRMnzFD5vtExEuY1yHqXxcR8U8FaTAcX0H28SL7/gfvC+1Kd0LKmDhhvldbmPZ1E/5iQcLfAg99FkbAEgj8HrJFdlO9YmPydcn/4gpSqijiae9gR80emkIVAjrJNvF/2bDTDEtMw+x9QHsHs1sEzIAZKe5BiYmJMvehoc8cUlFg9Rxmt+a6F5l9QmZoEAFKVO5HMzRn0SbOBZ+m+Z/NpDnzvrFov9bSGZ4phsQTY8PvVav5VhpQ7bjx7DMUY5p3Y3UN6xXV/qpVq6Q/cVG1X5ztKk10cY6B+2YECooAk898IOcsVvlgHoRofpYWL0F8DU1jLD0Ga+sPD0iYeL0xcoTe9xMYmftFD+1B09r+vubkkRpM6fZlqNPDzxdJPzAz6yhMmjp07CijWOLlwNzzyet1TM1Io4vxERQvNFj34tbltTaXMxcCdvj9ObpRLc8qZFfG3lzNmmwnLeM27Q/bLc/3q9HFZDlTJzJFeoFjf78qNZ4gni37zxUmttbpz2NqDrkdR/ANfCB4SQcB0woi0MLfc/z48TLSovYcb1segX+2bKBfl/8gtHVh5OUtIu126UkNGzWlYUMGisWDBJo7eyr9vGwxTZ+9gDasXU0/L19KLw8dSevXrhJ5ly/Qp/O/o23//E2//vwDzf3yO6oWGER//vYLrfz5e/pw8iz65aeltHvHVqrboBFNmvqpSJuV3cTZ8rPmHgsbbIYRZAQYgaJBgMlnPnF1E+RTBiDKJQJqPpvNsThyVhpGJsuxQik7CXJmbPXW3DCc2PoTRb87m+44lSF6eKS5m8/SHuYEM+vikCtJUbTy4t+089I28VtPLI4hcJ9GECjr6kPda3Sjx6p3IW+nnP3TjFTP86FfQ7bS7bREcnUpR50qNctzPVXw3J4pdP3iablo0rzvjBJHPNU8TX0jTx2iu8KPCWkOEIgFgXhARLGIxWJZBBKTEmjMm0Npy7ZD5CZMoEePGELJyYnUtFlLmvXZInp50JP0yrA3BSHtLvIsB9ORw/vp9MljNGXiOOrVuz+dOXWcjh05SPv27hL5EM9JTdrhA/toy+YNdOLEUXp39HBq3fYB8hBt/7tlI732ynP0+19bi23R0LLocm+MACPACOQfASaf+cQMpABaSDgiZwhfiaIWkCqYY7IUPwIV3X3pBoYh8lcdj7lATXxqFf+gzDyCfTdO0fTds0RKlzTZsr29IzkJrRt+9yzFgwASlaelxVNScjT9cXIFbbywiWZ2eJ8C3U2HmC/oSBF45a9gXXTV3rV751vTeu3CGrp0aJ3svmHXkVS+avuCDsVm60Hj+ZMIUIaPkq7CRJ+Jp0LDst/BZ05RnAgWtOKXH+jlYSPpjbfeo4P79pCT8Ocv6+omB1PWrawgj17Cb7U1paWk0qrffqbBLw+noa+N0g/Wwd5Bkksc6NCpG0WJFAjb/91Er7z2Jj3+5LPSUqXzA03o+NFDksA2b9FGX5c3GAFGgBFgBO4hwOTzHhZ53oIpJJzsEVIeRKSoxI4DDBUVtAVq19Hxbl4sccl/OPsnfdLurQK1Y62VtMTTy70i9Q7sQA9UrE8OdsWjgbVWnIpjXMliMWDL1WO0OWQ7pYjcm+9sn1IkBHR16A5Jcp2cPOjp6t3yNdVkkRf05Jbpsk5Ak65Upd7T+apfUgovWrSI8GGxDgQaNWlO1QICaebUD+mv1StpzLsT6OlnXjQ5OAdH3WtR9eo1s5RxcHA0ul+7Tn15HO8FHTr3oOU/fEMXL5wjJp9Z4OIdRoARYAT0CHAEEz0U+duAE73Kl5a/mnkvDYKLBxqLdSBgryJzCvIZLHIgrg3bYx0DM8Mo7mRm0KcHFkqNp693IH3cagh1rNSIiacZsDVHE672TtS3ait6X1yXsq7lJAGdc2SpOZrWt5GUnkwrTv0q93sJraeL+r3rS5jeQHqg45vfovS0DPKs4EN1H/jIdGE+wwhYEAFHQRp//XMrPTlwEJ0WJrSDn3+MFi2Ym+8R5MX6o60wv4W4OLvSV/Pn0MAnc46gm+9BcAVGgBFgBEoAAsxsCnEREWjCVH6yQjQrq8JEiwMMFRZF89a3u6v5VBao3x5aRAejzpq3k2JqbevVw1Lj5eDgTO80GUguguywWB8Cvi6e9HojnUYx5PpxCom/ZrZBLjqzilJT40S+w/L0XK1e+Wo3/NQPIsBQmFwsa9JzLtkJc20WRsAaEDglCOfB/Xto2sx5tPLPLSIYUBVaOH+2Pm0WxogAUQWV27d1Lgqof+GC7nlQp159erBjVwq9eL6gzXI9RoARYARKLAJMPgt5aRGAyNwhrxHx1FV8WKwLAQeXu763QvMZWL6O0BLepqm7Z9KOyKPWNdACjGbNpa2yVmP/puTu6FKAFriKpRCo4VmJ/MoFye5Wh/5rlm4v3LpC285vlG39r/Gz5JyPxYdUEaAqeJfOzLTWfQPJrVxts4yJG2EEzIFAfFwczZr2kSSb8Ol85LEBYtHYU/gzl5FBgtDHoQP/0a8rltG1iCuULlJpQWJiouW3+pMkAhdBUpKT1CH5ferEMf3+vyIibucuPQimuG4iUKAStHtZRM29FRerDvE3I8AIMAKlFgEmn4W89CoAkbkikzoiwJCImsdifQg4Cq2TFEE+JzYfTFUr1BMJulNp9u7ZtFj4gML00FYlOkmGUqLG5WrY6hRK1bjr++j80a4n6q5bYSafcSeDph74Umh/MijAtyF1r5K/QCnn/5smzW3dfTwpsNmwwgyF6zICRYJAWFgode/QjD567y3aKVKiTJ7+mdTS16xZh2rVqUdLvl1AZ0+foKuRETR98vtyDPM//0T4iOrM0Pfs2ibKfCmPz5k5kc6KIEZKPp8zjca+/Rr169WeHIU7zuQZ89Qp/ff4d0fSmr9+p+SUFP0x3mAEGAFGoLQiwAGHzHDl9QGIRATcwgQgQjvwI82Lb4kZhs1N5BMBO6e7GkHBMR0z0+jTB8bShIOL6ET4HhmF9GDkMXq35VCq6uabz5aLv3j6Hd1qv4sdm9sW/9XIfQTwAYWkZKTmXjiXEgvP/E5RsaHixdmF3mv5Si6ls56Ov3mawk/rfJ/rd/hQvNDzIyUrQrxX3Ai0aNWGDp8Mp7hbcRR98wZ9XH+2/hmL3N3rNv1HCfG3yNPLWw511drt2Ybc7oGOtG7z3izHz54+KfcXLl4utJzu5ObuQdWqBWYpg4WdieNH08DnX6Iu3XpnOcc7jAAjwAiUVgRY82mmK+/g6FjoAEQIMGQuDaqZpsXNaBBwcL5LzAT5TE+OISfh1zalzes0oOkL4ro5UnjUGRqx+R1aePp3Sr2bqkRT3SY2eeHDJi6T2QYJk/FNZ/+S7Q1q9iL5C3/P/Mi5PdPlglvF6vXIp6ou2Ep+6nNZRqCoEXB0dCIXV1fy86tE9Rs01hNP1S8WfRXxVMfy8p0pUiBBEAW3Xv1G2YgnziUmJtD6taspOSkZuyyMACPACDACAgEmn2b8GSAAEXxACyKeCDAkTG5ZrBcBO3F9Ich4mXzrmtzGn4E1e9DsLlPIX+T9hB/oekE+B218m1Ze2kLIm8jCCFgjApfiI+izvfOEscYdahrwAPULyF9OztjIw3QjJFhOrVa7cdY4RR4TI1BkCISFhci2L4dcMtqHWKMUObo9ae78JQSzW62prtEKfJARYAQYgVKCAJNPM19oRL9F8ur8CAIM4cNi3QjYO981uxXDTIm7kmWw1T0q08JOH9NgYbbo6lJO+PbE0LLD39HzG9+iZec3UHI6+/pkAYx3ihWBmyKq7Xjhq3xb/C4rCj/fD5oPyfd4Lh34XNapVLs5efjUzXd9rsAI2CoC8AW9du0aDXjuJdq7dxft3bMz21RW/bqc4mJjKC0tlZq3bCtTvGzbuilbOT7ACDACjEBpQ8ChtE24qOcLs0X4bd6MihJasIxcu0M6FQ4wlCtMVlHA4a7mE4NJjrlqdEz9AztQz6r30fILG2ld8DqZvmTlsWW0+vRv1CbwQXqyejcCUWVhBIoLgbi0RBq9cwbFJ0bKhZKp7d6SJuT5GU9izDm6fumMrFKz1Yj8VLXqskeOHDF79PLcJpyWdi9VR25l+bx1INDvkScIn5xk1JjxhA8EaVdYGAFGgBGwBAKwurB2YfJZBFdIBg4S/ps3cwlApMqxn10RXIQiaNJOYxadEnfP7NawK+TI/F+dfjSwRk/6NeQfWic0n4kiHcWu83/LTyVhnttNENEeVe4jL6d74fgN2+F9RsDcCEQJjecYQTyj4y6Ts/jtTWk/jnxddIFW8tNX6LHvZPEKgTXJvXy9/FTNsaxdGZ0xTsZdf7ocCxfRSSaDRQRsETV76OA+mjKhZJh9r171SxGhxM0yAoxAaUAAWRcy7+Yttr/7PLXGeTP5LKKrAo2mt/DjjI01ndfLmwMMFRH6RddsGXt7yhQa7eToiFw7cXFwoudq9aJnhE/ov9cO0+oLm+nyjRN0Lfo8LROfn45+T4EitcWDIrVFl8otqJwTp9jJFVQuUGAEzsWF00d7ZsmFECcnd5r84HtU07NKvttLv51EEWe2y3rVGg/Kd/2cKpR18qBbFEkRYrHGktK5c2cKDQ21ZJdG+/LwuJtL2OhZPmgKge++0aVBMXWejzMCjAAjUBoQuJocLacJpVY58Ty1VmHyWYRXBhH23ETC6sTExGy9IMCQk0aTlq0AH7BKBOzs7KU5dXLM9TyPD9qcLv4t5edq0k368/IO2hW2i+Lir9IlkZ4Fnx8Ol6GK3jWoRaWmdL9fY2pUrqZIgl56XLK3bt5Gi7/6gU6dOEPxtxKoTv1a9PKrL1L/x/vmGWdLFsTqYmpyKrmUvecHbMn+89vXmsu76bsj38q8tGVdfWiq0HgW1Pz76tmVlHE7g1w9nMk30LzmhNXLVZeLM2diLhIFdcrvNAtc3lXcqwMCAgpcnytaHoE6derQ6NGjLd+xBXps0yZ/uXYtMCTughFgBGwAgZMxF+QovdwrERQg1ipMPov4yriLlezbgoBqTblcOcBQEaNedM3bC7/PjNtplBqTKKKEZmYL259bz0hlMbTeI/JzRuRW3BKxj/Zd2U+xIvJopLhprMdHRMtFzsXKPrWpUYW61Fx8mparlW+/vNzGYi3nZ037nD41SMx++MBReu2lUdSzdzerJHgvPfcabVy7mUaOeZ3eef9Na4Ey2zgQWGj2ke/p5BVdjkJEZJ7cbhRVcPbKVjavB8JPrpRFqzV+Qvz+zbtA0tinDu25sImOXT1EGU0Hkb1Y7GFhBIwh0LRpU8KHhRFgBBgBRkCHwL/ifRJSQ7wzWrMw+SziqyMDEMH/824AIpjjenqyeWURw15kzTu4ulOaSEiemZxOqYlXycW94MGD6nkHEj6vN3iSriTeoG3CNPeA0IKGRp2WUUhDrx8nfNaK2UDj6uNZlQJE+TpeQVTfuzrV8wrM98rWHeFL90foDuovfE6tQbN66sRp+mLOQnm9mjZvQtNmf0R+/pXo3y3bad6shVTG/h65SUu7TRfPh1BqSjJVr1VD/B/dS2uUmppG0VHR5OvnSzeuR1HY5XBq1rwxwfHe2HGnuzlbkxKT6dzZc1TW3Y2CqgeSo2PWWyIWGC6HhNP16zeodu0a5O3jTQkJiZSUmCTHfCvuFsXGxMoUS4Z1ZYFi+pOSnkbLL26ktWf+kL8l3Ic61e5Db4jfWmEIXWLcRbp146bIN1SGqtR9yuyz61q5JX0nfFFTUmJp45V99FC1dmbvgxtkBBgBRoARYARKGgLXRZaFU0KhAelfvYtVTy/rm5ZVD9V2B6cCC8XGxBD8PPEiyGKbCNiX1dnQZ6beoYTo4EKRTy0CVdx8pW8o/EMz7mTQydhLdCjqDJ0QfYRGBQvNeQJFCU0pPodou6yKX5GLSOtSwaMS+bv7UzVhZoFPJWFW6Sc+Ps7ZFznSMtJpyaGvafW5tfRiowHU2b+FdhgW3/56wVJpGeDi4kzfLV9IfpV85RgGPPeESGNwL5rkV18spk+mzs2SrP3ZQU/RxBkfCgycadqEWfT1l99Rr77daeum7ZSamkqLf1pAe3buM3q8R++uNPeT+fT5rAV6q4QKvuVp3tdzqFwmHeMAAEAASURBVEOn++UYTh4/Q68PGSXI6Xm5DzP5yZ98KOuEh+lS7Xy36AfCZ+L08fTSsBcsjp9hh8EikNDayztpV+g2ui2i2kLKCb/ON0UqlWblaxsWz/f+1eBVso5P5Wrk5FYx3/Vzq4BgXe2DOtPW4DX0/fGfqL0wQ/d05DRUueHG5xkBRoARYARKLwJYKP9EpPe7I94fK3gHUVNh5WTNwuTTQlcHGs8Kvr5MPC2Ed1F14+Cm07ZlpmZQYkwwVQjoZPauoJlqIm4c+CgJEf6hp2JD6KwgpRcEAY2MC5WEFPlEw/C5cZp0612qBpG9vQO5OHuTuzCxdBEv8G5OrpSRqUv/AzPfuXvm0IrydWhYo4H3Kll4SxG7Nu1a6Ymn4RD++G0NTfxgmjzcvuP9Yl72tO2fHfTj0hVUuWplelOYvqrobhvWbJLnkTcXOJo6vvrXv2jW1M/Izc2Nho0YQnt37RO5+g7Qa4PfpD1H/xGRYJ3olUHDKeRiqPTNbte+LR06cIQO7j8icvY1pZjoWOnL7VO+HFWtVoWqBeY/cI/hPAuyHy18iBeKND6Xb0XQpehzMrWPage+nY/V60+PB3USWm7zmK9eC94gm/ev2091Y/bvIXX7096w3XIuEw8upBltRhZKW2v2AXKDjAAjwAgwAoyAFSHwo0jvFyzcVeAKM6r5S1bPNZh8WvDHwxpPC4JdRF3Zu98jnwkiYq2lJMjDn/DRmiHeSImjkIQICo2/RqHi+4ogqNFJNyhBmCxC65UhtJxI8YKPKYm4GUwfbptADg7OpooU6fHQS5dl+5UqVzLZz6L5urQeDz/ahxZ8N1eWG/X6OFrx42+0cvkqST5VZWdnZ/rp9yXUqm1zQULtaMe/u+Qpw+MP99CZjA5/exiNeGuYMPNMpYZBLQWpjJEkM+N2uiSeqPzbuh+pRatmFCXMeqOESW+9BnXo2Sdeon83b6ennn2cxk8cq7q3+HfMrTBaLz5KYGUR6NuIeol8sz2qtDYb6UT78WKxJTEmQT7U/GoWHfl0d3SlUa2G0dRd0+lcxCF6b988mtDqtXybmCtM+JsRYAQYAUaAESiJCEDj+f359fS7sBSC9Kj7sAhYWd3qp8rk0+ovEQ/QmhBwcNeZ3VJaJiXc1EUVK67x+bp4iRyNXtS6Qv1sQ0jNSKOrSdF0PSWaolNvUcLtZPFJpHNCa3oifI++vJMIxd21ZnfaLsw009NT9cctteHp4UnRN2MoTvhNmpILwTqcO3XvoC/SsfMDknxei8iab7V9x3Z03wOt9OXUhuHxC8J3FPLZzC9owWeL5DYIKORqRCTFx8XLba9y3lLTiZ0KFXzkR56wkj+4fvUqNSb/shWpoU9N8VuoR2UdXItkdFEhm2W73pWrkmMhAhblZXBtfBvQa61epy8PzKczEQdoyJZxNFys5rYVx3kRLy8IchlGgBFgBBiBkozA5YRrIqDgUgoRsUEg7cS73Kv1H7eJKTP5tInLxIO0FgQc7mo+KTWT4m9GFijirSXm4ix854KELyg+Wjl/K5zeFuTTXfiY9qv1ED0a1JFQdodI/1IcElgjgEJCQqU5K8gf/DcNJeMOwgZllXSRaxUC02KtQPNnTAyP3xERqCHNWjWlOnVq6qvYCZPeB4SJ7R+rEOaJZFAjayY71QThnCS0gpaQm6E6X2PfwI6W6I56VG1Lro4uNO/AVxSfeJ2m7Zwm/FerUceA+6mhSEUUKPycHYVpNbvQW+RycCeMACPACDACxYhA+p07dD05hpBOZc/VwyJN31EZVBHvNw/Ve5yGCK2nNb+vaKHL+uamPcPbjAAjkA0BR8+7KSqE5hO5DpNiL5CblYe01k7CTWjFhrV+nXqKF3triHbbtUcn6b8ZJSKovvv2RzTtk49lapVzQtu54PNvaOrMj6haUFU6eyqY/l63mZ5+5jE5nfXCtxNSu+494igP5PFPgCC9J4+dpvoN6tLkmR/qayFqrrOIhBsYVE0eCwsNp6OHjlHTFk0IYwo+fY769O9FDoKkQiKvRsrvkv4n43YSRUeEyWmaO7dnTtg96NeUGnWfTp8fW05HwncTzIxXn/iFVudUic8xAowAI8AIMAKlAIGqwtrpjSbPy8wJtjRdJp+2dLV4rMWOgJOIVgyxv6NL3nvrxjGbIp/IM+pf1nrSVzw/eCD98N1yGVEWPpyrV/5F7h5u0hQXOH8o/CmfGzSAxo+dSAgm1LNjf6ltBnGEvDbyZfmd3z8Dn3+SPhgzUUaqvXDuIlUNqEwwxT28/yidCTtMnbt1FAHCyhNI8RN9nxepXQLp3JkL1Ltfd0k+q4ggQ5BVYryHDx6jV0e8TM+9+HR+h2Ez5aOv7JHBm1zKOpJ7+XoWHXc5J0/6qNVQutl4AP0Rso2OR52lKyLwVqowJ2dhBBgBRoARYARKCwKIz1FRpNmrJaye+gV0EKn3dAvltjZ/Jp9WdsXgPLxq1SqKjIwkd2Hi+fjjjxMid5pbkIpi7dq1FB8fTxUrVqTevXubu4sS2Z4in3a3df86t64fJf86Om1ciZxwEU/KycmR/ti4giZ8MJXWrF5PiSKHZvTNNPIQeTefH/KszKsJgnrzZjR99cW3dOLoKTmicj7l6N2P3pZEEAcc7+btRDoUrZg6/vz/nhH5QG/SovmLaftWXVAi1GvdtiU5iFyf0H5+s+xLenPoaGEWHCa1pPD/RCoXyEtDn6e/12+mq1euycBEzi5Z+5WFStCf2Gu6xNXeVRoV26zKCz/TwcKsiOrqhoCctekirDwLI8AIMAKMACNQ0hGwF5FsC5On25rwKSPITnaHKmsaoRhLSkoKHThwQKY28BXpSlq0yJqbMC0tjf777z9KTk4mLy8vatu2rc3YPRtCfe3aNfL399cfXrNmDfXp00e/b66NLVu2ULdu3fTNXb16lSpVyuofqD/JG3oErqxbSwdGDiFnEXymzLOe5OXnR/c9aftGgM9uHEUJiTdoaPNB1MyneCKl4VZ0Jfwq3RH+nEih4uCgM21V4GcIf4ew0CuSGPpX9lOH5TfqJiUmS5Nde43fp6njqjLavHL5irjHpJF/FT/y8NBFM1bn8R157YYgxQkUEBSQZUzp6RkUHhYh/VQr+Zs/56V2DIbbf4btpfXB66lmpWY05/7RhqfNvn9g9QC6GR5K9R4cTIFNC6ZtNvuguEFGgBFgBBgBRoARsDkEbELzuXTpUho2bJge3PDwcKpSRWf2hoMDBgyQ2kJVAEQUBJTFNAKGaw6G+6Zrlu4zjj4+EoDMlAwqI7Zu3bhBd9LTyM6hZGu+LHHV4ShftVplk12BVAZVN25igrpu7tktBEwdV52gzYC7/p3qmOG3XyVfcQifrAJybGo8WUva9h7uDbGR4XIS3v6tbXsyPHpGgBFgBBgBRoARKFYEjIeGLNYhZe/8jtBOaEW7D2IKM1UlzZs3p1atsqdaUOf5mxEoDALOPuVl9fSUZHJ0cZB+cLGRBwvTJNdlBKwagQSR3xPBtRBRz6NC8ZndWjVIPDhGgBFgBBgBRoARyBMCNkE+Tc3k8uXLNGLECP1pT09P+uWXX0T6hazmevoCvMEIFBIB57sBh6Dt9KqgM0+Nubq3kK1ydUbAehGIv6HLIeZRobwgoDZhLGO9YPLIGAFGgBFgBBiBUo6Azb5JwBTsxRdfpFu37kU8/Pbbb6l27dr6S3rlyhWKjY2VPpQ+wlwSdU6dOkVHjhyhmjVrUtOmTcnV1XRC9tsiF2BwcLCs4+joSPXq1ZP1sK2VkJAQShA+YQgQFBQUpD0lj4MkQ2tQo0YN0gZEwXguXrwofVrLly+fZ59L1Lt06RIdPSpy/IjtRo0ayXGZIt0oc+7cOTp48KA0V27dOm+mc/ChPXv2rOzLxcVFYqsNfoRARQ4O935C+R1XFqBsZMdJmt3C4DZTXO8GFEXn6GbYbqrZ6k0bmQEPkxHIHwIJN8/ICh7l791b89cCl2YEGAFGgBFgBBgBRkCHwD3mYGOIfPbZZ7R161b9qEeOHElPPPGEfj8qKoqqVq0q9xs3bkwgpk899ZSIXBmiL1OhQgVasWIFde7cWX8MGyCdkyZNok8++UQSQ+1JaFdnzZpFQ4YMkUGNDh8+nCUA0saNG6lHjx76Kp06dZKkDwdeeOEFgpmwksWLF8t2sI9ASSDKucn27dtp0KBBWeaBOggWhPYMo9aeOXOGHn74YUk+VduY9zPPPKN2jX5/+eWX9NFHHxFwNCXA6IMPPpCn8zsuU21a+3E7oVV3cHal9NQkKuuIlBN/UNzVK5SRnkr2IgQ2CyNQ0hBIFAmtIWWZfJa0S8vzYQQYAUaAEWAELI6ATZrdQnv5/vvv68G67777JFHUHxAb6enp+t3jx49Tx44dsxE2EKv+/ftTTEyMvmyGiLQJwghihSi7hgJN6yuvvELvvPOOPFWrVi0RffMe6Vi/fr2+ys2bN6WWVR34+++/paZS7W/atEltEtrJTVauXCkj1GoJtKqDKLkPPfSQTJ+ijiGCLbCB1lMrmPfnn3+uPZRl+/vvv6fXX389R+KJCtD2QvI7LlnJhv84eeuCDtnfdiMnVweCD3JMxB4bnhEPnREwjUBibLg86eZV3XQhPsMIMAKMACPACDACjEAeELBJ8gkNYlJSkpye0l4amsIazh0mpDAdRXoRbUoR5LlctmyZvvjXX39Nu3fv1u+3bNmSZs6cSaNHjyY3Nzf98Xnz5tGFCxdEagYPuv/++/XHtYQS2kCQWSUgiMeOHZO7MFHVam5BHHMSEOG3335bamVRrkOHDrRz505auHBhFlNjkGIVkAna4bi4OH2zdevWpccee0yOWX/QyMbUqVP1R6Hh3b9/P40fP15/DBuvvvoqPf3005Kg53dcWRqywR2nu0GHUiKvUflqTeQMboZts8GZ3BuyncgfBbnNeRPvgWLFW+mZuvsK8n4VtSTf0t1r3bxrFHVX3D4jwAgwAowAI8AIlHAEiv7NpQgAvH79ur7V9u3bU7Vq1fT7pjbgn4hcoSCHSMUCIqrk/PnzapOmTZum34YvJQjemDFjpGYVGj4lqamp9OOPP8pdLXE8efIkRUREyONacqnqKXJ64sQJ0s4jt1yeMA8OCwuTzcDv8rfffqMHHniAhg4dSjNmzFDNS/9UVQ5muErgr4r5ox58RQMCAtSpLN+JiYnSDxUHQei/+OILGT144sSJWfxZMV5EFi7IuLJ0aIM7ThV0OR1Txe+wQmAnOYMbITttcCb3huziqMtveTX55r2DvGW1CFxN0l0nd8d7C2JFMdi05GiRd1UXbdzF/V56q6Loi9tkBBgBRoARYAQYgZKPgE2ST/hHKlm9ejWtW7dO7Zr8hqayYcOG8nxgYKAMNqQKQyMJgUktggMpgXmtlqTCn1IbUAiaT4ghcdy8ebM8nhP5/Oeff2QZ/AExbtOmjX7f2Mbp06f1h+F3ChL51VdfyQ/8OrUCMg2N7g2Rg1IJ5oKASJDq1avT/Pnz1aks3yCcyI0Ige8ryLTalht3/3h7e8ut/I5L24atbjtX9JNDT7keKchnNxKAUWJMAiXdCrHVKVFr/+Zy7LsjDmYxDbfZCZXggSdnpNGZyBNyhg9WKdq0UqmJuoU0Byd7snfMnke1BMPMU2MEGAFGgBFgBBiBIkDAoQjaLPImYRr77LPP6k1Qof2DJlFLSg0HoTWZxTlEl1WiTGMNfSPr1Kmjiui/oWVVPpfqu379+pLQIQItBNpNEFVFzKB1VAQRmlRoTbXkE2UV4dN3ZLChHRvI8rBhwwxK3NuFqa2WROOMIt6qlJZUq2P4RjTeFi1aSO0w9kGsEbAI5sJqvtC8IlIwJL/jkpVs/I+zr9J8RpKTSzny9vOn2GsRdP3iRgpqNtQmZ/d49c60/swqiku4Tmuu7Kd+VXNeDLHJSZaAQcNcf8n5v4VPeyqVdfWhTpWaFemsUpN0Acecy96zFCnSDrlxRoARYAQYAUaAESjRCNik5hNBdLTkKzw8XPpD5udKGSN70PRpBS96hqL8KXFcS2i12k9oPqH1VKT2ueeek+lQUAe+qtu2bZMf7EP69u2r28jhr+HYYDZr7ANTWOCTlpaWpTVjc8lSQLMDv1AlILqLFi3Sk1EcR7RfpUXN77hUu7b87eqn03ymRuk0yxVrdJbTuX7hXgApW5tfeWcval+zhxz2urPraGXoLuH/eS9ol63NpySONzE9hRacXUPHwg/I6T1V/3GyK1O0OY1vp0TLvhxdPEsipDwnRoARYAQYAUaAEbAwAjap+QRGU6ZMIZjcKv9GlUpFm+Ykv1giD6dWEFW3V69e+kOIoKtMbXFQm1MU5BP+kRAQNrWNfaRyQURdaGch06dP1wcCQqTcvIwZprJK4IuKCL45iWHOT2gue/bsqa8SHa17qdQfuLsB02NE+oVAwwlMkIsUpsHQ8ML/VZuaJr/jutuNTX+5VPKX40+7ESm//Wr1peDdP1KMSLmSlnidnNx0mlFbm+SoRgMpOT2Z9l/6l/45v4l2hO6mhn4NqJyzp9DM2+Q6la1dAqPjzRBBoCKF7+W56yfFgpZugezJJs/Ro0EdjZY358H0tFuyOUcXncm+OdvmthgBRoARYAQYAUag9CFgs+QTUWbhtwiTUCUvv/yyJHg4VxABwYI/JQgYBOa9L730kt6c95tvvpHEUrUNLaMSpGcBWVNReHfs2CFPQTvaunVrQiCf2bNny2NaX1AEDUKfuYk2FQtI7K5du2TAIW09aGXRD+aPueAbvp8QzAXmyejryJEjNHz4cG1V/faePXv0dRAVGAQffq6GZFZVyO+4VD1b/i5bVRfgKiUmSk6jrGcQeVTwofioaLp24U8KaDLEJqeHiLfvNRtM37r60qYLG4R5eDwdCd9vk3MpqYP2EAsbT9V7hB4O7GCRKaanJch+7B3Y39MigHMnjAAjwAgwAoxACUfAZsknrku/fv3oqaeekhFXsQ8/R2jmkH6koAJSplKNwE8TxLF79+6EnJl//vmnvlloPZ955hn9Pnwou3TpQmvWrNEfwwai8SKIz4MPPpiFnKpCWnNddczYN9KafPDBB3qNKbSlIJPwJwXhRQRbBF6CphIEEmbF8ItVWMA3E2UbN25MSAFjLIcp+vW7a1KKbWg8FbnE/GBqi31oUN966y1JZPM7LrRr6+JWtaqcwp30NEoT/rVOIgCWf+2egnwup6vB622WfGJSIKAv1+tPL9Z5iDaE76VjN4MpMS2RMstkN0G39etoK+PHNfEQ0YjvF/6d7f2ayGtkqbFnCFNfiJ0D+3xaCnPuhxFgBBgBRoARKMkI2DT5xIVBLksE+IFZKwTayRdffFFq6+SBfP4ZN24cLV++nFTwIJA2bVAdNKe0rgjOoxUQSUPyqUxUQd5AQDdu3Kitkid/T1Tw9fWlyZMn0xtvvCHrg3B++umnWdrCjjbtzKhRo+RcVK5PEGh8INDSwg8U+U+10qxZMzkmw3mArOITFRUl/T/Xr19P0O4WZFza/mxx21GQcHtHZ8q4nUqJV8Il+axU5xEK3rNcBh5Kjr9Crh62nZbC0c6R+gW0lx9bvEY8ZvMgkHnX79fOrmh9S80zWm6FEWAEGAFGgBFgBKwdAZtw5PLx8dGbfYI0ubq66nGFaSgIqCKCCPIDs1L4UqpyMBk1NMXVRsbVmr2iHLSIr776qiRo+o7EBtrr1q2bbB/aUEOBCbC2XZTX5gAdMGCAfh6o26pVKzKMqJvTuKGVBdE2jFyrxlG3bl0aMWKE2pVtI7gRNJ5KgAWi2e7evVv2j+PATuEHranKRYpzMGUeO3as/Ib5rZJ9+/bJoErYz++4VBu2/O1croIcfpIIdgVx9Qgg77u+oBFnfpHH+A8jYOsIZGbqcnySWIxgYQQYAUaAEWAEGAFGoLAIlBHaL5uwp4PWDVo6kEMHh+wKW0RdTUhIkHk5FelUx7BvLLUINIIwT9WSTy2ggCY0NFSan1auXFkGGDLl+6jqgfwqP0uYqRqOFWlWoLVEO6b6zW3c6AtzhUYWmkjk3ETgnwoVdIRIjUX7ff36dUIAJZjdqjQzGCv8W7X4PProo9LPE3Vhtrts2TJ9MzBrRo5UJQsWLMgSdRjH8zsu1ZatfW9/tB/FnDhADd+fQrVeHCyHH3byBzq19Usq6+VKDz5/L4+rrc2Nx8sIKATO7ppAIYc3ULUmXahBhynqMH8zAowAI8AIMAKMACNQIASys7gCNVP0lUAejRFI1TP8KsuVK6d25bexY9oCWi2l9rjaBjENEto+fPIqIJUgg6YEmk18cpLcxo26ILbagEc5tYdzCECEj1YwVkPMlFkuykE7CsIKjSqIv/IfVW0Y6z+/41Jt2dq3a+Uqknwm39V8Yvz+tR+lM9sXUlJcsoh8u4/K+XOuTFu7rjzerAgos1t7O5t5VGSdAO8xAowAI8AIMAKMgFUhwG8UVnU5in8w8Evdu3evHAj8XmHiC5Ncw7yhSEEDs+HSKi5VdEGHksLD9BA4OLmTX83mIujQQQo7sYzJpx4Z3rBVBDLSkuTQ7R3dbHUKPG5GgBFgBBgBRoARsCIEbMLn04rwKvFDQVAjmNsqH1BMWEs8YaL7v//9T0YYzs0EuSSD5R5UXU4vOSw0yzSrNXpO7keeO0hpKbogWFkK8A4jYEMIpN9OlKNl8mlDF42HyggwAowAI8AIWDECrPm04otTHEODSTD8PJGTFMGHIiMj6ebNm9JkF/6eMLXNybe0OMZcHH263TXFTrl2JUv35SrfR+4+npQQfYuunF5O1Zu/luU87zACtoTA7ZRYOVxHF9OuBLY0Hx4rI8AIMAKMACPACBQvAkw+ixd/q+0d+T4feeQRqx1fcQ9MaT5T42Pojgh2ZSd8jpVUazyATm9bRGHHV1JQs2EiqBUbGChs+Nu2EEhLjpMDdnItb1sD59EyAowAI8AIMAKMgFUiwG/FVnlZeFDWjoCrvz+VsRdrNyIicuLlrKa3/nWfIgdHe0q+lUKRF9dZ+1R4fIyASQRSkhLkOaeyfibL8AlGgBFgBBgBRoARYATyigCTz7wixeUYAQ0CiITsUs5XHokPCdGcIXJ0cqOqjbrLYyGHvs1yjncYAVtB4E5GGt1OTpfDdXWvbCvD5nEyAowAI8AIMAKMgBUjwGa3VnxxeGjWjUDZKtUoOeoqJYaGZBtoYNOXKfTIRoqLvEZx1w6QV6XSGxk4Gzh8wCYQSE4Il+MsY2dHTmWzpmkqzgncEfmJ486eoVunT1NGsojGaxupqosTMu6bEWAEGAFGwMYRKOPkTG6BQeTTrCk5uLja9GyYfNr05ePBFycCroEi4u3RfZRw4Xy2YbgITVGl2i1k2pULB+ZRi75Ls5XhA4yANSOQGHNRDq+sV1nht1ymWIeaKQjm9R3b6fyiBRR9cDfdSb9drOPhzhkBRoARYAQYgWJBQMQR8axel4IGvUSBjz9BdiJQqK0Jk09bu2I8XqtBwL1GLTmWJCPkEyeqt3pDkM8X6UZIMMXdOE5evo2tZuw8EEYgNwQSY3WLKmW9q+RWtEjPxwUH06HhQ+nWpTP6fuBv7V6lOtm7u1MZO3v9cd5gBBgBRoARYARKHAJiATYjNZmSwkIoPTWJbl08Tcc+Gk1nZk6iJlNnU5WH+tjUlJl82tTl4sFaEwLutWvL4SSG6jREhmPz8KlLfjUbUeSFE3Rx/6fU/KHFhkV4nxGwWgQSonRkz92nTrGN8eIPS+nElA8oMyNdBvjy79pHrPYOpvItWpCdAz++iu3CcMeMACPACDACFkcAVkAJly5S6PKf6PKKHygtMY4OjBxCVzc+QS3mzCU7e9tYjOWAQxb/6XCHJQUBzzp15VRSoq8T/NCMSc3Wb8rD18UqVXzUCWNF+BgjYJUIJNzUaT49ytcrlvGdX7KYjk8cJ4mnd72m1GXjTmo9fyH5tmnDxLNYrgh3yggwAowAI1CcCMAFxqNGTWr0/njq8d9RqvbIQDmcK+t+pX1Dh5h8Fy3OMRvr2+bJ5507dyhYmGWtWLGCZs2aRUuWLKETJ/glX13s5ORk6ty5M7m5uVGVKlXo5MmT6hR/FxIB94AAqY3JvJNBiSGXjLbmUaGh1H7iZPCe6UbL8EFGwNoQyEhPFYsl0XJYnn7NLD68iM2b6OSU92W/Vfo8SR3+WEfugYEWHwd3yAgwAowAI8AIWCMCDq6u1OKTOdR02uck/E8octsGOj7hI2scarYx2Sz5hOp5/vz55O3tTXXr1qWnn36axowZQ//73/+ocePG1Lx5czpw4EC2CZfEA+np6RQXF0epqanZprdjxw76999/KSkpiSIiImj9+vXZyvCBgiGAKKCu5XX5D2+dO2eykdr3jZUBW6JCL1D0ld0my/EJRsBaELh1/bAIIptJji4O5OZVw6LDSouLpaNjRsg+K3V+iFoKUyL8r7EwAowAI8AIMAKMQFYEgp54kpp8PFMeDPl5Md3YuzdrASvcs8knemxsLPXu3ZuGDx9O8fHxRmE9cuQIjRihe4ExWqCEHExMTCQvLy9JwqHdBNHUCrSdWqlY0XpSJmjHZavbZQN1L+bxOZBPt3K1qEr9dnKKwbtY+2mr17o0jTv22kE5Xe9KIqKzheX07FmUlhBLzl7lqflsJp4Whp+7YwQYAUaAEbAxBKo/8yxVbN9Nph47/t4Yqx+9TZLPDz/8kDZu3KgH11442LYQASig9ezVqxe5ClU0BNrAki5paWlSq4l5ZiD/ncGcGzZsSN9++y29++679NNPP9GTTz5Z0iGx6Pzca+mCsSScC86x35ptRgtHcDuKu36DIs6uzLEsn2QEihuB2GuH5BC8LGxym5GSQuG/L5d91xv3ETl5eBQ3FNw/I8AIMAKMACNg9Qg0mz5LRn+Pv3yObuzbZ9XjtblwgadFYvGvvvpKD2rZsmXphx9+oMcee0x/LDIyksaNG0fwBzUmN27ckH6hV65coaCgIKpXrx5VqFAhW9EU8SJ06dIlArmtLSKbguht2LBBals7dOhAAcLnLy9lVMMwY0N7R48elSZtjRo1opo1a8r2VRnD75iYGDnWy5cvk5+fH7Vr1076b6IctL4wpdUKyuOYg4gEqbScffv2JczZXaQlUMRcWwfbt2/flr6zp06dIkdHR4kJxoZtQwFu0D77+/uTj4+PnAvqQduMOk2bNjXZj2Fbtr7vWb+BnEJ88Kkcp+Li7k/VWz5KF/b9Rmd3zqOKNfqQg2PZHOvwSUagOBDIFPfN6PCzsutyle+z6BDC162VYeSd3L0poP8jFu2bO2MEGAFGgBFgBGwVAVfBESo+0JUid/xNocuWyuB8VjsXQYhsSl544YVMAab+M2PGjDyP//Dhw5nNmjbV19W207Nnz8zQ0NAsbb3zzjv6sl988UWm0CLq9/v06SPL5qUMCm7bti1TEF19fdV3pUqVMtetW5elX+wIEpk5aNCgbOU9PDwyP/vsM1m+VatW2c6rdvEdEhKSGRUVlSnIs77cpk2bsvQlCHXm+PHjM11cXPRlVBuenp6ZixYtEu+id/R1BInVlxO+tZn79u3LNi9B5DP/+ecffZ2SvBElflOra1XK/KtBUK7TTL+dkrlt8YOZG+bdl3l29+Rcy3MBRqA4EIiO2Ct/o5sWPpCZkXHbokM4NG6M/H/a/8brFu2XO2MEGAFGgBFgBGwdgZDffpXP0E0PtrXqqdic2e3x48cFN9IJfByHDh2qdnP8/uOPP+i+++6jI0LraExgxotARVpNotaEFf6l2kixMHGF5KXMypUrqVu3biTIoKyj/XPt2jV66KGHaO3atfrD0LBirEuXLtUfUxvQdk6aNEnuJiQkqMNGv3EeGk01VhTSBiXC8U6dOsn2oME1lFu3btErr7xCgmDrTyG4kRJci44dO2ablyC81L9/f4IWtqSLVz2RbqVMGcpIS6EkAy204dztHZypzoM6W/yQQ2spPlqnXTIsx/uMQHEiEBX6j+y+fLW6ZGdnWeOYuGNHZN/ewo2ChRFgBBgBRoARYATyjkD5lq1k4cRrYZSekpz3ihYuaXPk8/x5Xe454FS/fn0ZbCc3zECs3nzzTT3xcnZ2lsGIPvnkE4L5rBKQLaEFVLtGvxHcp1y5cgRzX1OiLYO+3377bUkCUR797dy5kxYuXChNeVUbIHjKTHju3Ll09uw9YiI0nHJciOjr5OQkXgh1l+35558nobFVTcjvBg0aEMxs4dtZvXrOwUK+/vpr2r37XvTVli1b0syZM2n06NF60140Om/ePLpw4UKWftQOUrkIrakk10KLqw5Lk+Bly5bp90vqhoOLK7mU85XTiz2ds+ktClWq1Y8qVKuhM1X+R+QwFKbYLIyANSEQFbJDDqdCYCeLDystOkr26RYQaPG+uUNGgBFgBBgBRsCWEXAT7oBSMu9QWrT1KoAsu6xdyCsKX0NtdNtq1arlqcWff/45i3YOAXieffZZWRekFNo7RcJAmISpqVE/zK5du9KqVask8YR20pgYlkH+0bCwMFkUhPW3336T/qUPPPCA9MlUvqrwmUS5QJHLDmRPCQIp7dq1S5JOHINfpUoh895779Grr74q/S5V+alTp0qto9oHoTYl06ZN05+C/ylIMYgkpEuXLlIji21oS3/88UdCoCdDgV+pMLElBDYSZsvSV1RpUbULBYb1StK+e826lBJ9nW6dOkmVu3bLdWoNukyjncsGUqzQeoefXErVGr2Yax0uwAhYAoHk+Mt0625+T9/A3H/L5h4TcuZC7Iz4mpu7L26PEWAEGAFGgBEoSQiUEZZ4yPlJgnzeEZaP1io2pfnUmowCUK0JaE4Aa011fX19acCAAfriCMzz8ssv6/dBKhHcx1BQD0RS+FxKYmoscI+xMgiQpET4UEryiYBJ+Jw5c0adkt8ga0idEh4erj8Os1doO5U0a9aMhgwZonYL/A1Sqp0n+lHEE40ilY3wUdW3b0rzCaIM4gkBcUawISUwKS4N4lG3vpxm/Jl71zqnebt6BFCtts/IImd3fk144WdhBKwBgYizv8thePtXJgTJKjbBA5SFEWAEGAFGgBFgBPKFgCSg+aph+cIOlu+y4D0iuiyImNI6hhkhicZaDw6+lwbDWHRZtKsVaFgNTVZBrBDZNScxVuacJv8jyNiwYcNMNgH/UUNtIcZbFKIdF9qvU0eXMkTbFzTLIXf9VNW39jy24XerlfLly+t3tb6m+oMlcMOzYSM5q/gzuZvdqukHNXuVIs//LVKvXKdjf4+iNo+tFK6jNrUWpKbC3yUIgWvBG+RsKtftV4JmxVNhBBgBRoARYAQYAWtBwKbedqGlBMFTEiyIHYLb5CYIuqPEmI+d8rVUZbSaRnWsoN/avtEGiK6xT/PmzWWQIWg+tVJUBM5wXLnhYkgytWPUbtvCiot2vObYLte4iWwm4coluqMJyJRT22WE327jnp/K3J+xVyMo5PCCnIrzOUagyBFAAKyE6DixCFKG/Go+XOT9cQemERDR1UlEctfHKTBdMn9nYFUjoqXT33//nb+KXFqPwIkTJ2jy5Mn077//6o/xBiPACDACjEDeEbAp8olpaTV0SUlJNHv2bJOzVb6HNWrU0JeBxs/QXNfQ/BU5Pc0lWg0q/CrhF2nsc+jQIapcuXI2jas2wm5exmRIKk3V0WKCMvA51Qow0pramhMTbT8lYdtT/F7sHBwpMyOd4s5mNaXOaX5uXjWo7gODZZFze36kuMhDORXnc4xAkSIQdnyJbL9CYC1ycs3ZyqNIB1LAxv/66y9J2LT3LdXUr7/+StOnT892n1Pnre177NixMld1XhZX8zN2+PUjzgGC7RWXYKET0d0//vhjGU39gw8+kHEDjC2AFtcYc+p3y5YtMgAg4iDkRfC7RCwIbSyHvNTjMowAI8AIlFQEbI58InKsVkA+J0yYkGWFeM+ePdS9e3dq3bq1LFqrVi19lejoaELAISWxsbGEVWYl0Eoimq25RNs3VkwRPMhQoHlVgZQQMVYbSffLL7/Mot1dvny5nJtqA5F7tYKARHkRBAqCD6oSRL7Vpo355ptvSOuzCc0si3EEoMV0q6pb4Ig9dsx4IRNHA5q8RL7V68qot0c3vEXpqaYDRJlogg8zAoVGIP12EkWc0UW5rdrohUK3VxwNzJkzRxI2Q40Ugsgh+ve7776bxc+9OMZY2vvEc65du3YyIjue23juTJkyRZKzZ57R+cFbO0bK7Sev4/zll18kuUbKNRZGgBFgBBgBIpvy+cQF69y5Mz3yyCO0evVqef2g6cMKKqK8IkAOyKVaLVb+ki+88AJNnDhRT67eeOMNaXYEordhwwa6ePGi/reAB6I5BelRsLKriF2PHj1kbtJ69eoRNLdHRd7RdevWETSRIM0weUNAoc8//1wOA6v4yD+KvJ/wBwWB1QY7AlGFLyrmDUGqFKxuQ7u6Y8cOgqmyKUHuUuAGgfYXZB2k/erVq/Tnn3/qq0HraSsvBvpBW3jDs2ETig85S7HHRR7Zgfl7iWrcdS7tXv6wCDyUSsc3j6RmDy2WvwMLTyFLd5liQeRG6N8UE7FP5DCN55QwWdCx8I6dPTk6e5JvUHfyrtS6SH4bV8+uoIzbGeTq6UK+gV0tPMGi6+7w4cP6XNBIZ9WrV6+i64xbzhWBt956i/bu3Ut49uK5jWc0rHuQeswwDkGujdlIgcGDB8sghY8++qiNjJiHyQgwAoxA0SJgmpkUbb+Fah2aSpCt7du369vBaqQ2sBBOqLyTFSpUoEmTJsncnjgOwvr7779jM4vgxQRE1ZyCCLjwDwHhhYBwfvrpp9m60KaNAVlFepjrIhgNBBpIRbaxb0go+/XrR0uXLsUpObdt27bJ7YSEBPL29pbbxv6MGzeOoEm9dOmSPI2Hv+ELAKL7zp8/P0vEXWNtlfZjXk2a0JW1K+nWCUE+8ymOLt7UrPcc2vvbCLp+6Qxd2PeJiIb7Tj5bMU9xmL6FHf9W+KAuk2TYPK1yK+ZA4NLBNeRR3ptqthkufDL7mKNJ2QaueehRnQlhtUZPFAm5Ndtg89HQzZs3CamscM99+OGHSZtaKh/NcFEzIrBx40bZGvxOn3rqKbndrVs3wkKoNvq6Gbss9qaQtgwfFkaAEWAEGAEdAjZJPqtUqUJbt26V2kGsmII8aU1hYDY7cODALHkpQf4aNGggCaihfyN8LUHE8ADUBszx8vLS/0602/qDYkN7XLutLYN2oemEr40xH866devqiTHqgbBCI4oUMAgMoZ0b5gDfJa1gHw9uYKIE2lB/f3+CSS80pcnJyXL1FWRSCbbRD/yLQF7xkqYEdZCLFClhtP6hMPM11R7qajHQmvWqdkvqt/fdFDMJF+9FVs7PXL0qtaL6HYfSqX8X0oX9q8i9Qn2qVNOyEUdBQoJ3TxTEUxfx1N7BjioK/z/nsj5Z/i/yMy8uW3gEkPsyOT6Srov/8fibsXRk/WRq2CWWqjbQ5SoubA83Lm2gxJgEwvU2V5uFHVNh6yNQG6xOEKW7iVgYgn+enTCPVwJrEyyq4Z6MxUm4N8DyBIuA/fv3NxqVHGR27ty5BG0qLFIQ/A6pr8aMGUMqyjfu1egXsnjx4iwuHEuWLCGYXiLHtNaSBIuhOIZvpPNyzCHHKaxS4Gpy8OBBaeGDOAIYrzZ9mJrjf//9J3NWoyzcLGAxZCpiO/CCqwUsXoAZ7uPAQj0P//e//1HPnj1V03Jx9I8//iCkMcPzCvmogYNa8NUXNNgAhhDttcC+vb19tngHOJ7X+SJHNsaPhVfUKSueX82EqwhyYWvjFWivO8phIRi4I4e1IsOwMALGeDbCTBipxF577TXq0KEDhpRFUAZWStDmmvrtwLoK/p6PP/44QQsK0Y4jr78/LAzDsumYcO3AsxqWXioQIJ7lMGFmYQQYAUbAFhAoI144M21hoDmNEfk/YTYKTZ+fn58kS4YPN219lEN5BCQCmTP1QEYdPHxA4ECk1INY21Zey6g66BsPEZgGQyuJgER4+JgSzA25QmG2i4eNNtqvYZ2IiAhJxEEO8cBU/qB4uKJfHNfm8tTWl9oPYaoLE2SQcTyw8UJgTHJrD2MFVqWJfGaI39LaprVEXt8M6rJpD3mIa1UQOfnvWAo/sV1GwW3z2Dzy8mtRkGYKVOe80Lhe2KezCKje5H4KavQk2Tu6FqgtrmR+BNKSYyn4wLd07eI52XiTnu+Qf+3Cm/Lt+aUv3bpxk4Ka9aC67SeYf+D5aHFj26aUEn2d7vtuBfm1fzAfNXUuGfD3BAnB/X3WrFmSdO3fv19GGNc2hoA7MMO9//77CX7y2oU3lIP1CaxllOzbt49gNol7LAT3VpV3GoQLZEL5xYOEIKosCK+WZLZq1UqSxrZt2xKIoZJ//vlH+jyiHbSPeydIBcaEdrDYCgE5BtG8ceOGjAuA54caDxZXlasGyq5atUoSWiw6agUkBc80aBs3bdqkPwXyCt9EzAuLt1p/fxQCXqNHj5bm97AOgh8tpGrVqtIKCWPF+BGMB89UUwJ/T8wdzzJoQbUBBA3r5HW+ILR4LiJSPIg7xo9YDlgIgFsKnp8qnZq67liQAIlTggVYLOLiOsI/WP0e0B6ed3gWwo0F41dt4NkdGRmpL6vaMvztqPJwp1EEUR3L6+8Pv1GUxfXEQgKuofba4roidzcLI8AIMAJ/1q0q30W7bv6P3DUZQqwJmXtLwdY0qnyOBQ/MpkLzBE0dAvzkRDzRtLu7O+FFoH379jkST5TFTR2rwKaIZ17LoBwEfeMlRQVEyol4ojzmhtX1jh075kg8URakERhgFRr1lKgHsiniiXKYH14IYB4ELa0p4omyubUHvEoT8QQm9i4u5FY5AJt088B++V2QP/U7TCGfKgF0J+MOHfxrJCXG3fNHLkh7ea2TlhJDlw7o/Khrt+xKNZu/wMQzr+BZqJyTqzc1aj+K/GvWkT2e/2+eIAN3CtX79YvrJPG0s7ej6s1fL1Rb1lIZrgQgnlhsg7uCIh7Gxrd7925J7mBhAsLy+us6DGAWigU7CBYfobEC0UPMASzQ4cUfhABEC0Rt5MiR+ub79u0rtxHRVQkIkiI7iGyu4hLgPAgPBO4Tpp4z0Ey+8sorknjCkgZjRT5qkD2kBoNm7cCBA7IdRCqHRhdjhLYTBBYaSpBnkBZDAbEG8QRpQzloBBE3QD1DQALhqwmB5hbEE1Y1mAc0jiDDGDtwAInLSRBTAc+WEKFdxTMbpNmYuW1+5ovrg8VSWEEBZxBCxDyAVhgkcsGC7GmscC3wvIO1ENx3sEiAxWhcf9TBPq4R8Prpp5+kdheYawUWV1gYUL8dXBcIfjuGKdO09bTbefn9oTw0uLiewAs447eoFjb69Okj56xtl7cZAUaAEbBmBEoE+bRmgHlspQcBr8Y6LWXMwYKTTzs7B2r+0DfSt+92SjodXD2YUhMjixzEK6d+ki/Zbl7OFNjwiSLvjzsoIAJl7Khem6HiBd6OkuKS6eble6b2+W3xzp10YWY9W1YLaNKDnNwq5rcJqywPQgbBAiM0VTkJXAqgLcViIBbNoJGC1hGkQ/nCI1UG3CWwEAmNIjReIIkgTyBtEGjFFLmEdhICUgISBcGYoEGD4BummErWr18vN0EUTQnKI9gcAvTAVBQLgBAsFiIYHQRjg6AsCB3GC6IIggQiBuKoTEtlwbt/QCIhMAtVJqpYxHzwQZ3mGYRULegCHwgC1SlNL7SLo0aNkscxF6URlgcM/iDgHuItQHsHsof4DXA7AbnV1svPfGH2qwJLYc4QaGGHDh0qt2FGaygYMxYHOnXqJOcJTSiuM0gx2oPJNEypQcDhwoPjhsGqDH87MIlVvx1tEEPDvrX7hm0Y+/2hPOYHAbnHbw9ab7VQgnNYaGFhBBgBRsBWEGDyaStXisdp9Qh4t2wlxxh3RPcyV9ABOzh7UMuHl5Crm6MM+rN/1TOUlqgLPlXQNnOrF3lhsyxStU4nqMFzK87nixEBeyc38qtRT44g8oJOa1aQ4YQdX0yJsUnk4GxPNVq9WZAmrLLOc889J8kZCKHWFNXYYEHeYDGiBC/xyrVBBXwD+YL07t07i087joHUKcKmgrVBOwoSBM0ZfAEhmzfr/r8QAAmitJ3Q0CEGAcojF6QpgekoBORm0aJF0kcVfqr4KG2pIjxqHGgP5ZVA42gs4qqyiDFM06U8cpQ/K9pR4wChU/3jG76PEJBtzCknQfAn+MzCzBUkESQUhBrkDlpMiOonL/PV9gUMcN2h8VaLB8a0kDCtNTQPVgELcZ2V1le1jX2FszqWl9+OKmvqO69tQLsN0V4jhZX2+pjqh48zAowAI2BNCNhkwCFrApDHwggoBMoLTQskPiRYmM1mCL9N4z6zqnxO385uftTq0aW07/dBkiDsW/0stX70JxH8xzenagU+p/KLlvWuVuA2uKLlEPCQ1+kUpaVkNQXM6wiwmHHuv+9l8dptXxSpXLzyWtXqy0GbBQ0hTDyhKYKvHLSgeRW4RkCU1lKROvg3GhNoykD4YO4JAVEBkYKmEdo19A//ShBbRNyF5g/+jmhfkVBoBA0Jj7YvNQZoV5XGS3se28pVAma2EJjG5kVAlkFAYbaLwDowoYUpKrS1IH9Ke/x/9q4DPIqqix5IpYYACSRA6L333jsiVaQISpMmimKliaCIqKAiKoIIAj9dKVIE6b333nsNHUIggfz3vM3EzWbTd5NN8i7fZGZn3rvvvTMbMmduIxk3iBxrc0YmhpU0sus8T4x5b7gWugiz9jYt0MSMCZtis17qY/IjWk+NfjwXlRiWY/M2Rl/mjYirWH534qLHmg5+n/jSgXG3vL+cI5MkUhi/q0UjoBHQCCQlBDT5TEp3S8/VoRHIVLQYUju74kXwM9wXa4an1GeNj6TNlBcV207DzgVdVTbSnX+2R4VWYhHNYIotjY9uy76s60lJndrkzmd5XX92MATEPZsSEmJy64zt7I5sGKzqembI4olcJbrHtrvDtx82bFhYPB9dTelaGlXZqagWZMSvGzGglm0DJNENxTx+n9Y9EqnlQj4ZL0q3TVoimWCHdZtpTaVV1CCfUbncUrcxd2acHTduHE9FELoDUwwXTIMoRmhoceLu3bthFkfGRxoxkrTGMkGOQWKJAwkuSTPda63F0pI4WTtvMWTYR7bnGMSCbsV0ISX5jM16GbPKPnTb7dq1KxgDyXtBa3NUJDlsEqEHhpXYERP3GHW8mUirT58+YVOn5dQ8MVbYBX2gEdAIaAQcGAFNPh345uipJS0EUkkphwz5CuP+yUOSdGhnvMknV5/OIx8qtfkduxf3UDF+2+e9hgotJyJD1uJJCxw9W4dB4NrJv3Dz7BHlRli8/pfg9za5CUkSE8UwWRvdL1kqxIiJjO1aDVLHJDyWQmvgiZOm8kqG+y3bkADRVXK/uKNOnTpVdTMsVM2aNVPkkxZQZrplO7aPSmjJpdCV19Jd1LKfkR3XcPk1v24tuQ/LbDEzLEkgCScTGZFAMskdrbqG0DpKIkrLG5MaRTcPo190e7qz0v2W5NNwL43NelkehsSTBNTAmmMabtPRjW9cN+4zsyM7kvDlAL+7TATIGF7eV1qXGadKq7oWjYBGQCOQ1BBIfk8dSe0O6PkmKwQ8y1dS67m9479SCvFdYDrPAqjUdhbSeabHsyfB2LGgF25KbUYtGoHYIvDk4UUcWWeynOUp1xwe3mViqyLJtCcJM8gIYwBZozMuQmsjSRFjM1kb1BASMLp6MgspSS43Q5g5lkl7KIb1jUmNKCSfFNaSZGZVtmP7qISuxHTLZc1Oo8yJeXtaUg1LJ0kuyfeJEydU3VCjHZMjsZ6lpRj9mC2WWVSNGEL2t8zwSvdgCl2aSYrMhRZDa3WsjTZ0qyWWliSeyYXo5kthGRpKbNZrWKSZddcQWgqZzCg2wjEptJKzTI4hdKdmZlkjkZVxPqH2/I7Q2ky3YM6L30UmxyL+RmxsQs1Fj6MR0AhoBGyBgCaftkBR69AIhCKQtYopu+a9fTttikmaDDmEgM5Dpuw+eB78AvuWjcCZ3d+puns2HUgrS7YIPA9+in1L+yh324xeWVGg8kfJdq3Gwlj2hDGFFMbI0UUztkKLEy2nFJbTIEFiXUxa/kgG6ObKhDmWCWnoemsIa0kb2WFprSJ5MCQ6l1u2oyXQiPXs0qWLynLLshs9evRQFjFawbZs2aJU0gLLuE0Ka3LS3ZdWTM7ZiAtVF0N/kEDTqkliy2O6rjIhD7PdksAzMZAhdPGkdZTxoawlzeROLDNDyy2tom+++abRNMKe9Z+ZAZh6meWVcYyMxWWCH1peOWeDmMdmvUaiJqNeKrPcMhMxEw/FRnh/mPGX8sYbb6BWrVpqbpwLy/dYku3Y6I5PW94D3kdixO8ZMSYZ5neb30HOm27dWjQCGgGNQFJBQJPPpHKn9DyTBAIm8pkKgXdu4skt22aodXX3lKRDc5GzmMnV6vT2edj7d2c8DfjvjX+SAElPMsERYObSw6vfwcPbd1V22zLNfhLXveQVdeHsbFqPZTKZMWPGKMJI10zW/6QYbYy9+Q2xpocZXamHsYRGXUy6sLLMCetgGlYzcz0klUYSIRIsIxEP9Ru1QGnFMkqzmPflvEgUjbnwGufOep4sU8L6lLTssSQIa20yi6255ZUux3RD5ZgkZSSLJFYGQTVf97x581TGWRJYugKTaNHCy8y8tLoNGjQorIYoMwMzLpMWUFoXSb6ZUZixq0zIxFqkkQmJJS2mbEdXaCZdIuElEadFluOaE/iYrpeklaSY5VO4Vibm4QuBOXPmqKmYY2is29hbzpVW5b59+6r7RvJKqyzvA5MjGS8TjL7G3lyHMZb5NePY2LO9cWzso9JByytJO+fxxRdfYMGCBZgxY4a6L7ly5VIZcI0ao+Z69LFGQCOgEXBUBFLJQ0mIo05Oz0sjkBQR+LdaeQTcuoqy3/4Cv5at7LKEi4em4sSm31SiEJc0zihRdzC88zWN81ibptdFwINAlG/cF57ZS8dZj+6YMAhcOv43TuxYBq+8hVHupWnRDnp80zBcOLBaPdyXf3k0svjVjrZPYjRYWbm0enFTZeo8ZKthqjMZ03nQDZZkyUgQZN6Pf+boFsqkMiQIxmcmvLG0BtLCRKJKC581uX79uoq9ZPIgI0bRWjueox6WEuGczImVMT7JqVHqxFwH+9DV0kiCY36Nx4z9JOkkcWN8pkF6LNtxfLYjSTGIMN1UeUziw3lQB+uastSIedwqddG6SXdjZqNl4iRzYb1SkkjiRVJK625MhXGldJNlTCn7mmNjTUdM1su10jWV6zHK59ClmPfIIHkG7tbuu/m4xN4oWUPLp9GfbaLSYe27Y629tXPG+JY6SMI//PBDZe0k2TcXo3Yr76+1eF7ztvrYsRHg/eP/YclN+MLO2v/JyW2djrSeJYVzIuTFc9RfvR3pc+d2pKmFzSV5vfoOW5Y+0AgkHgKZylVCwMpFuL1jm93Ip1/JbvD0qYQDK99VmXD3LR8p5HM+itYaBff0MSuxkHgIxX7kFSs3YfHSdahRtQw6d/rPndFc090797F15wEULZRP3Pqsl8Uwb59Sjk9sGaGIJ9dbrF5/hyWe8b0fJGCRPeSQ3JCUGGL52TjPPclKVKSSyXG4xURI8gzSZ94+qvHZzhohNe/PBzpu0QnHLlCgQLhmJF6G8GGXZIdCq6g5+WT8JkkihSTUUkjISMDjInQl5RZTicl6udaiRYuGU2lJ3qPD3ejMFxJ0t7YmUemw9t2x1t7aOWMsSx1GTCutxHwpYXw3eO9oAadYuz+GPr1PGgiw5JFR7idpzDhms2QsOV3htWgEzBHQ5NMcDX2sEbABAlkk7vOqkM+7u0wF5m2g0qoKZryt2n4JTm+xyTtuAABAAElEQVT/CucP/CsZTI/h9sVXkLvMy8hbrj+cXf97yLSqwE4n/5ixCGO+nyoWAuDV1o0wYthb8R7p37Xb8Nsff+H6Tf9Iyed7n4zBzDnLUb1KWWxYNTXeYyZ1BS+eP5PkQh/i6nFTnGPhml2Rs2inpL4sPX8bIkACSbdfut727NkTI0eOVASOiYboqktiylhRIxGQDYfWqmKIAO/PN998oxJI0eW6RIkSqhQNk0wx8zDdjb/88ssYatPNkgIC7uI2ntQlUBKxadEIRIaAJp+RIaPPawTiiIBXNVOWy0cXTyNY3L6cxdXPXuLknAaFa4xA9sKv4OjawXhwyx9ndy/GpUPL4FemldRw7Aq3NFnCDX/78mZk9q1mtxIb43+ZhZOnLqgxeTz4w55wc3cLNwd7fMjhkx0uYrEoXDCPPdQnKZ3Mantgxdu4L6VAKMXq9FbfhSS1CD3ZBEGApVYqVaqkYi7pesusrtmyZVNlPF577TWVfCdBJqIHsYoAEwqtl0zBTDbEOFTG3NI6midPHpW0iUmiLC2+VhXpk2EI0O3ZJCF4JhbkI8eu4cSZW7h+PQCBT4PEFR/I7JkW+XJnQqniOZDF8z8XfFqt7Smz5q9ApSqmZwh7jmNv3T27tsP6NSvtPYzWn0QR0OQzid44PW3HRSBDvvxwTZ8Jzx7dw81tW+HboKHdJ+vhVRJVXl2C66cW4vSOCaom6JkdC3Bu11/IXrA8fAq3RpactRXhPLlljJpPiUZjkcEzbm5zkS3o+ImzOHD4ZNjlh48eY+k/G9C2VaOwc7du3VYxVJkyZZS4r9s4feYSihUtIG6RJkvtwcMnVNviRfPLQ0DE/6KePn2G/QePI2tmT8kCmitM72dD+qLPm6/CK0umsHM8ePwoAKfOXBD3R1f4ZDfVLUyTxl218fe/iyyZPeAe+jnwSSBui/tu1qyeqr2hiC5uJ0+dl7IaT1GoQG5kyPifVZnziYketjtx6hweyXzoGuwp49paaO28cPA3nNnxP5UV2cnFCaUaDYN33sa2HkrrSyYI0I3z/fffV1syWVKyW0bFihVV3dpkt7BEWlBIyHPsP3QFs//cj383XRQL/3MUyJsFOXzSSpy1G54FBWP/4ZuYPP0O7t5/htIlvdDupSJo3qQE0siLVHsT0ESCRQ+rEUgwBCI+2SXY0HogjUDyRcCzQhXcWP8Pbm3ckCDkk0jyD6JPoTbIVqAFrp38Exf2TZPspvdw9cQutbm4O0ucaEFlHWX77bO7SbmNDshTLv5usdRHmTPfVH+0WZOaeHD/ETZv26fOGeTz4YNH8MlfH9m8sqBl87qYMnUBnks/xmb9On4IJvw6G9t3HlK6CgrJW7N0siQP8Vaf+WP/gRMoULIZrl33V+eqSYKaJQsmIJNHBgz+bDy+mzADLV+qhz9nm2pZfvfjdAz/4mcECKk0l7YtGwjpzIRJMn7nDs0wbZLJbe2NXkPx5+LVePetzvh29Aeqyw+icxh1BJjciOSlOLp3bYvvv/5IWXTf++jraPVs3Lwbnbp+LG7Dt5VOWi6W/TUBdWuZ6sKazy0uxwEPzuPaib/E4r1Ish8HKRUe4qJXqskPSJsxT1xU6j4aAY2ARiBZIRASEowz52/hi+82Yvuu62hcPy++/6IhKpbLg7TuripUZNzEtXi1RSnkyuGFFyEvcObcDaxYcwbjp+zE+N934f2+VdGicQnJJO2kSWiy+nboxSQkArrUSkKircdKMQh41ayt1np7W+xqzdkCIJbQyFGkPap1XCGlWcZKnF9Vif90QlBgMG6eOxY2xIsXL3By2yzsWNAGL4JJAeMvc/80kc92LRuiXRuTtXPl6q0g6aQ8D3V3uiEWTxI/vzw54C6JQpiVsnOPwdglxLNQwdyq7anTF/D9TzPVsfHj8tUbeBzwFKVLFVantu44gK/H/q6OmaGS8vyFKWPg0WOn8ckQIaFCyod+1AuVKpZU1z0zZUBNyUj8/PmL0PZqp34QE4pxbd6fK/D+4LGKeNarXQlNGlRTZHnytD8xdvwf4dqGqlPnLPUM+OArRTxJer8aOQCVyhfHvXsPVNv4/PA/fxL//lIdm6Z3FIv3fEU8ea8L1eiKyu0WauIZH3B1X42ARiBZIEA3W/6fPGfhPrTuMg8Z07pj5fwOaN+yKCqUyYV0ypqZWpHJRStO4fylB+rYSUoVFcyXHZXKZcPi6a+hR6eyGPHNRvT7aCHuyt+0ECGnWjQCGoHYI6DJZ+wxC9cjQB6ab0odLltvt0JjtcINpj8kGQSy1a6r5vrwwmk8kzIGiSWZc1RD8frjULfHOlRoOUYso+UjTOXxPX8EBcX/j+ievUfEvfWiIpMvi1WzTYv6oJWQVsdFf6+NMO57/bvg1MFlGDm0X9i1GVO/wtE9i0GSRjl05FTYNR5k986CfVvnYs/mucrCyXMLl0bUzfP7DxxXRLFe7Yr4TMb4dtRAnpZYHg/079tRHUf347sJptIGr7ZpjFV/T8LSv35G184tVbcZs5dG1z3s+vUbJkttbj9f9O/dEetXTkXrFqY1hjWKw4F6qDJnvaIjWFzITm6ehk3T6kg25H5gWR7GgGrRCGgENAIpDQFGd/KF5Jgf1+CrH7bhi2F18N2XzXD9xiP0GLAUV67dDwdJSEgq8J9JuE+F737dhW9+3ow3Xi2PxTM64tbtR3it15+4dvOeJqDh0NMfNAIxQ0CTz5jhFGkr440a36rZcjOsOJEOrC84NAKsreSWUereyZvRW1s2J/pcUzu5IEuuWhL/2VzNxck5tRyXRdmXPkPd7uskKZFLvOc4909TcoH8+XJh957DksThDHxyZFN6mUTBUjq92kydKlGsgNq7iSvqq22bhDsXEBDeXbaCWAxJ4ChNG1VT+6vXTMROfTD7kTePqdzKuo178N346bLNUFcL5vczaxX14YkT51SDpg3/SwDRsF5lde7ylRtRdza7ahDNceIG7FuwIYaN+BFBz0zusWbNYn3oltYFOYtVR65S9dSLhUw+vmDdV8qTx0ESA7wPxzZMwsY/2mPLrEZiHf1a6rmeV9f1D42ARkAjkLwRCJE/wS+EPG7CX3+fwPSfW+HlhiXx4nkqjPx2E7q0K6ksm/SOiSBkrSIMZxn+QR0sWX4a+w5fhV+OrJjxcwf4+qRB97cX485dsYDKP/ljr9rrHxoBjUD0CJieUqJvp1toBDQCsUTAs1J1XF/9N25uWIccTZrGsrd9mju7ZpQENB9KApomcHJJa7tBxK1pXij5PCLurk1a9Q2ne8Om3Sq5kIsQTEtJJa5NFPMkDqlDz1m2Nf8cHGrxcxM3U2tCkppWEgnRpffDoaYY0Bw+Xhg98l3V3HjeiIoEPn8R8YHiebDJSpw6NBlSTPRM+G4QihXJjwmTZqkES6PHTlFz+Hz429amHuNzGbPlQ/F630Zo//TJbdy/sQ/3ru3EnUvbJevtLTy681C2hTiza6HE/uaQkjzd5HvQ1G5ZjyNMSp/QCGgENAIJiEDIi+cSr3kUf8w+iO9HNcSR4zdx+OhNXLh6DyfP3kfzxvkxa+FepBJrpxL5O/bkidRP3XwaF6/eDT2XSlk3/fwyYsz4LWjRlC87U6F7h/L4YfJ2fDziH0wc1xpOEu5i/C0wdUzcnxcunMOcmVNw8sRxTJm+IHEno0fXCFggoMmnBSD6o0bAVghkq1tfkU//TettpTLeeuiGaw/ZtHUvGI9J6d39FaR2MhHKoGfBqj7ns6AgLFi0Gh1DrZ1xncPWHQdx5OgpReT+XPivUpMv1MJpqfOP/y1WLr8vN6uNbq+3gpdkxy1XpmhY2Rcjuy6z85KAkjRv2LI3nJp8eXxx6OhpLFm2Hl1ea6GuLQx1IS5WJK/6HBM9tySrLl19+/fpgG59hoEuu9t3HQw3li0/sLyOd54GakNVSCyoP26d/0dqfi7G3auXcffaFdm+QFqPsZJ0qr9YwVvJg5N2hLHlPdC6NAIagcRDgF5p/nceYfhXG/HBO1Xh450ek2ceRMjzEBw67o/cOdNh3ZZL/xFPmWpIqhAEBj7Htt1XcPikKTkciSnPZ5QsuHsP38DDR0+QPoMb6lb3w3dfNEOrLnMwf/EBtG9dzsxdN/HWbYy8asUSLJgzU7KqiweWFo2AgyGgyaeD3RA9neSDgE+9+jggf44CblzC4ytXkC5HjuSzOIuVzAl1q61Qrjh++n5ouKu7xAWXBG/2vBXxJp937txDxVqvwVtKoRhk943QGMxwg8qHnL4ml9+/l28AN7r1entnRvOmtfDtlx+gTGjSIiY28ivcUBIAPURQaNIiQ9ebQqTfkWRBjCutWLODyobIMi+UD999Q+1joidvsWaoVb0cShYviHUbd6l+RQqbyKv6YOcfbmmzintuZ7Ux/vPCgd9w+chaVZLn4KpvcG7Pbyhe93N4ZI8YE2znqWn1GgGNgEbApgjQDZbkc7xYJvPmzYTOQgyd5IXonIntcUUyjtdrORO/fNsMBfKY/kYYgzPes+bLv2LQuzVQs0p+47TaM/Ntp95zUb1yTrzdvQZddeR8KnzQvzK+/Wk7XmpYDBnSpwnnwRNOQQJ/eLPPAGzesBrXrl5J4JH1cBqB6BHQr7qjx0i30AjECQG3rFmRPpeJYFxfY7LSxUlREuhEt1pKe0nMYykd2pniOHdLQqKgZ8/g6mKKL3V1Nb37cnUz7Z2c/3sX5uYWvo2Li+na651eRmbJVkvi6SKVwHu83gZvdmurhiS5pBj7tOlMtTyZ3ZblV8qVKYyrl6/jl8nzMOGXWZKNtzFaN6+n+tySkjQFC+bBhwO6qs/G3Hr3eAXDPu6FtGnTYJ8kMDLVF5USLT9+ijaS0ZcSEz0knqvXbVelYBgr2qBuFXw+rL/qn9A/0mTwQ5EaI1Gn63Lkq9ASjP99ePsuti/oj2Mbh0rm42cJPSU9nkZAI6ARsCkCt+48kJeGJzCwT1VFPFOlkvAM4YubN19E3jwZkc/PS4giy6WYb9JASGUqSZUX/rwTUku7RrXzY8PmS0JtJTMuN/EWafNSSSn15YZ5S/aL6TRimIZNFxVLZU7OEcNcYqlCN9cI2AWB/5727KJeK9UIpGwEvGrVx6P/ncXNtauR//WuyRaM/TsWSMbcYKSRGEtL+fC97uJu2knqoqWGm5sr7l/bKvXTQtQx29auURGPbmyHcyjB5LkP3u2Gfr06qILe/PyVxGl+NqSf0k8X2dNnL0odtuzi/pSOl5WMlhImwwb1DuszasxkdX7wB2/ivXdeV8dV63YGLbEXhYQ6C9mdP2sc7gjxfCwZeXPlzK7afDq4T5gOJ4nrHC7jfjqoD85fuKrmbF53lB1iouefxRNVzOvNW3fgk80LmbNkUmMl5g9nt4woWOUT+JXshuObhuP66QO4eHAN7l7Zg7LNfwVJqhaNgEZAI5DkEBAOuHjFUSGZnqhUNrciklwDS6PsOnQZVcvnDDsX07WRbFaukAvf/rxNXHOfST4BEjuhqfJ3pPMrxTFn8Ql061BZZXiPqc6EbHdNwi0+H/4Jnj4NhG9OPwwYOAhZs3ol5BT0WBqBMAQ0+QyDQh9oBGyPQLYGDXHuf5NxZ/c2lXXPSK5j+5ESVyMJGLfIxJyUuriarJrmbd2tkFZaGw0hboYO9i8qyXsiiLyxNu+TJbOJ4I36ZjIYk3pX6mqSeNJi2qqFyeJJHSSC5lExxjjm+jl+3rym7Lnm582Po9Pj5ZUF3BxN3NJlQ+kmE+FzdjkOrf5SrKD3sG3eayj/8o/w8C7jaNPV89EIaAQ0AlEiQJfbdZvOoUn9/Mo6ad74qMR79uxSXs6LlTOWUqRAdnz3eV0g8DoC7t1GKmcnOKX3lSREJeDhIX+v4qAzllOIc/NHjx/h0ME9eP+j4WjVtkOc9eiOGgFbIKDdbm2BotahEYgEAa/KVSSrrCuCnwbAf7fJNTWSpvq0jRGYMnEkOrRrChd5QPhn9VacPHUBdWtWxILZ36FurUo2Hi3pq/PO1wzVOsxGOs/0CJKMj7sX9sdD/yNJf2F6BRoBjUCKQoBeOAeO3EIVsVTS1dYQJki/fPUR8vp5xIp8hoQ8R1BwAB4dm4UKD77G43/64MmWz/F4zTA8WPQGQnYMQsOSj2WoFyrW1BjPUfYH9u9Bn+4d8f1PUzXxdJSbksLnEbmpIoUDo5evEbAFAqklvtGzbBX479yIayuWwauSJj22wDUmOvxy+WDmlNExaarbhCKQJmMuVH5lAXYveg0Pbt3G7sV9hZDOB62jWjQCGgGNQFJA4OIVfyGLz1G4QDYhhCyVYorFfPj4CZ48DYa3l0fYOfP1GO1CpMSWOiZxla7PHl/Dww3DkSooAK4lOsA9Z3U4uXnIxed4du8SAs/+g4AtoxGcpzbSVxggISYusSK35nOw9fHZs6fRpUNzdO3eF+UrVLG1+mSp77kkHrx27ZrDrI1W+hzJLGGlJp8O8/XSE0muCPg0aabI5801/wDDRyTXZep1JRMEXOShqnyL6dix4BWVDffw2g9Qrvl0h3mYSiYw62VoBDQCdkLg1p0AeKR3g7NkuD1z/poioBzqxs17QiqBe3cf4mng0wij81qw1HG+JqWxrl7zh0/2LOK1dB8P1w1FqnReyFD/W/D/R6Ms1ZObRxF4cQPSFGmDF3nq4cmGz/Ag9S/IWP4tOKVyjMfrHBLfmTlLVkyZ9CPq1G+McuUrh1v35UsXMfW3Cfhk2CjxEooYEhOucQr5cPv2beTKJVZzBxEPDw/Jxn/PQWZjm2k4xm+HbdaitWgEHBIBXyGfhz4fhMfXLuKx/EefLpdO5OKQN0pPKgwB1zSZUabpeGyb2wv+F07LthZeeeqHXdcHGgGNgEbAURF4FhgCV9fUEvd5BO8MWaNy03Ku4nUrWWtd0LrrAtmb+eOGLkScZiU3Q2oM+2Iz3N03YvvKfnh66HekSu2EjDU+k6R4acNewtEymso5LULunsbj5W/BrcYnSFtrGAJWDUJgrupIl71CqNbE3TH7+8+TZqJFk5ro36sLFq3YJCXHTJ4sQUHP8O/Kv/HH7xPx8eCRkj1Pk8/EvVspZ3RNPlPOvdYrTSQE3L28kDFvETw4ewxXli1DoT59E2km0Q8rf3qjb6RbOAAC9r9PGbKWQM4SdXDp0Dqc2/erJp8OcNf1FDQCGoHoEXB3d0LgsxdoUKc49q0rKpZPSgiuXL+LJu1nYvs/vZAujVuoolDf2tA29dtNwxcf1ZF6nnmROuQhnp9ZB/c6Q5DaJXwNT7pCunkWgEu9sXh4eJrEgI5Bhma/IHW+mgg89ifSZCunLK5xSWwUOrF47wICHuPJkyfw8c2JH37+Ha93bIm+PTvhf3OXwj1NGrhIPorOXXth1IhB8R4ruSo4fv6uWNClTE8iyKmTx9C0fnhLdSJMwy5D6oRDdoFVK9UIhEfAu6Gp1uWNVSvCX3CQT6lD33iGBAc6yIz0NKJC4EXofXJyMh6gomod92u5y/RUne9euYRngXfjrkj31AhoBDQCCYRAdq90ePjomZREeY70ad2QLq2rbG7wzious2IHffr0mfrMc8Y1HqcVQuokpNJVMqq7S63pZzf2IrV7eqTNXjHMemq+BBLL1Kmdkb7EG+KW64PA08vhmq8RQm4cxvPgx+ZNE/x49OeDcfjgXoldvIKhg96Dd3ZfZBT3zQP7dqFtizpYu9r0LOIs5cS0aAQSGgH9rUtoxPV4KRIB3+Yv4/Sv3+Hukb14dv8+XOWPgCOJewZvPLrzEHeuH0aWnDopgSPdG2tzuXP9qDrtnt7H2mWbnUvnkQ/u6V0RKA9yzHybJWcNm+nWipIvAkePHEFAQECEBfr6+sJXEmfcvHEDFy9ejHDdRVwES5cujRcvXmDvnj0RrvNEgYIFkSlTJpw7exaMzbIUT7mWX9o8evgQx48ft7ysPpcpW1aVhjpy6BCeBEZ84cbkHj4y1+vXr+PypUsRdLi5uaFkqVJgYpJ9e/dGuM4ThQoXRsaMGXH2zBncuXMnQpvMmTMjX/78ePjgAU6cOBHhOk+ULVcOTmJ1OXTwoBCmiDGKOSUuLXv27LguyVEuX74cQUcad3cUL1lS4hiDsX/fvgjXeaJwkSLIkCEDzpw+jbt3I75gypIlC/Lmy4cH8nfr5MmTVnWUK19e1XE+cOAAgp49i9Aml58fsmXLhmtXr+LKlSsRrqcVK1yxEiVUX+qwJkWKFkX69Olx+tQpq/Fvxj1lXx9fT6Rxc8LRE1dQqVy+MFfZ9OlckT6dM67fCEAu34hut8a4KkkR7aWPZK4eUmIrlVOYDqONsScBdZIEQ645yiH49jG4FG6DwOdBCAq4B+eMrEMd+TiGDnvsBw37EtzMZe/hiN9l8+v6WCOQUAho8plQSOtxUjQCnkWKIk1WHzzxv4YrK5Yjb4eODoWHb5F2Etf3Fa6c3Id8pQOlPIy7Q81PT+Y/BAIeXMbtK9fVCd9i9v8euabJKOTTH8+eRHzQ/29W+kgjYELghBC+aVOmWIWjUZMminyeEQKx8K+/IrTxEOJI8sl4unmzZ0e4zhM9evVS5HPXjh3YvWtXhDYlhBSSfPr7+0eqo1ixYop8rli+XBFhSyVNm7+kyOcpIYV/L15seVkSuGQJI5+RzbNX376KfG7ftg37rRDU0kKAST5v3rwZ6TxJcEk+ly9dituyHkt5uWVLRT6PHzum2lhe9/L2VuQzKCgo0jH69u+vyOfWzZsVybXUUa5CBUU+r8sLg8jWSuKXWmohL1uyRJL5RCSwLVu3VuSTLyVWrojo/ZPdx0eRTxLsyMZ4a8AART43b9wI6rEU457yvKuTC8qWyI4tuy6iYrm8yv2V5+nqlyunB85euI0KZfwiJZRsq0SSD0nAZ4zoo4tPRYRIDGhqSTTExEWppDyLDGBo0nuNgEbADAFNPs3A0IcaAXsikL1xc5z732RcXbLQ4chntvwvwS3tWDwNCMKhjWNQus4gednrak84tO44IPAs8D4OrhurembO4YcMmQvHQUvsuril8wRu+ePpYxPhjV1v3TqlIbDwzz/VkkmaaDEzF1r7KCSZtGBaSjqxbBli7TqvpUtHaxLgLZY0a218hMhQGNNm7TqvpQ6N4cqTJ48iiDxnLp6epnlm8vS0qoPuixRavSIbI42MT8km87HWJrtYVilsZ+06rxlJcThPT5mLpRBHSibB1ZoOow+JobXr7GvMk5ZexgdaSjaxrFJonYxMhxHXmCdvXjzKmtVSBYgjhfffmg5aVym8L9au8xqtuBRaz5+FWldp0T1/7pw6H+6HcL76tfNgxvxDePvNGqI3NMJMiGSxgt44eOQG2rV4Lvcv6kfgVBmyI0Sy2bKkimTjCTeE+Qeu392rtGwl8fT2KYTIOM6StE0lJZJrjiyqpIxMkIRZi0YgoRCI/LcpoWagx9EIpBAEcrRqrcjn7b3bEPToEVzMHrQSGwLGrZSoPxx7l30K/8vXsOufYchfph2y+JZTb34Te34pffwQceO6fmETzu1fhICHz+Di7oyitUYkCCwZvUrg1vlTuH1pE/KU6Z0gY+pBkj4Cb/bpoyx71lZCF0tukQmJKy2HUUmdevXALTKhO2p0Ol5p3z6y7uo8LY/cIhMXqeMc3Rj169cHt8iEbsjR6WjfqVNk3dX5MmXKgFtkQjfh6MZo0KhRZN3Vebr4RqejU+fOUeooK+653CKTtGnTRjtGo6ZNw7rTPZvuwhS3UHJqXGzeuCjG/rwDm7aeQd0ahYRoigVTiGCV8j6Y8PuuGJAtif30LosnO39CoP8puGctrHQY+i331P1C6oM+vbgWqb0LIrVYQem+6+gyY9okNcWZ0yeLV8Hbjj5dPb9kgoAmn8nkRuplOD4CWcqUhXumrAi8548r/6xAnlfaOdSks+auj7LNgH3LP8UD//vYt/o3qWnmDHdJ1iAM1KHmmpImExLyAoGPnyE4iG/foYhnpdaTkT5LkQSBIXuBFjiza6G4ZZ/Bw7snkcGzUIKMqwfRCGgENAJRIUCLbsFC1v8/8vRIh07tiuPbiTtRvVIeSSLkKvluU6F61bwYNGo9jp66juKFc4RTTyug2oREMjuuc7psSJ2jIh4fmAzXul+Ja7GLIrDhOqkPIUI8n+PZ46sIOrUCbpXflbaRx4lG7J94Z97o3gfctGgEEhIBTT4TEm09VopHwFtcby/OnYYri/9yOPLJm8NajlU75MTFg7/j6rHNCHoarLYUf+McAABaO3OVeEm2NyQJkMm1MCGmRZKbNXcBVe/z2LohqNh6bpQWgISYkx5DI6AR0Agw4dO8OXMUEG1eeQW08FJMbsBO6P1GZSxZMRO/ztiOmpVz471ha4QkyvWQ1Oj6zmIpt2Jy5VWd+EPIp//tp/hw5BrRtR6tX8qPvh174dG/7+L+np/gUeEtiRsVAsqmYZ1MB8+e3MTjDSMQnLkYMuWqqU4ql1ZpHBsLaKd2TcU92eSGbDFEkvp4547OEZCkblgCT1aTzwQGXA+XshHI2bqNIp93dm/BM8nG6CpZBh1NGEdYvM4YFK76QCygRxD87L4OCEnMmyTuYi7umeEhMUWpnWmFTngpUuNTbL3UFXevXsapbV+iULWhCT8JPaJGQCOgETBDgORuX2hW5JatWonvrYl8Gk080qfFV0Protf7y5HPzxNDBlQXfpkKFy7fwTc/7cD7vSsjq2c6RQ5NNa5TiVV0LTq1LoIihbOhUD4PuEnc54taw/Fk4wjcv38ObqW6wjVLCWXZJAV9ERyAgAtrEHRwFk7fTYuPF/rgpRObJZtuBjSqVwS0wJrIsDGr6PeauEWPkW6RtBHQ5DNp3z89+ySGgFf5inDP7I3AOzdxecki5Huti8OuwNktIzLnqOqw89MTSzgE0nkWRJHafXB03c84t3eZuGNnQN7yAxJuAnqkJINAK8ls+lQSwjAhkBaNQGIiQItj9cr58fGAavjk8/WYMKYJalaRJFgh+fHvpnM4ePQ6Rg1pFpbYiUl3Phu3HuVK5USNqv8ly7oT7INUFUcjzcU5CFg7DIGuaeCUPgtCxPL64t51Ib1p4VK0DcoVbAnXlXOxdddlPHocjLFCcL8cWh8NaksMqORViEqWSKZgI5FSVO2S2rVcEiusRSNgiUDUvw2WrfVnjYBGIN4I+L7cFmf/+AWXF8xzaPIZ74VqBckKgVzFu0jJlas4u2sRTm6bgyePb6JIjRHRPlQlKxD0YqJFoIiUMdGiEXAIBCQJkFThxOvtyktN1hfo98EKDOxXEV07VMSn79dBux7z0KLJGVStkD80lCBEiKjYQOkqK30Zbx/0PBivv7UQHdsUQfeOHyO43D0E3Zb6sY9uSnpeZ6T2zAO3zEUlO7yL8sXNnMkFDWrmRbdOlfC/+XswYOg/GPvZczSpX0yNIWqtSvHixa2e1yc1AskRAU0+k+Nd1WtyaAT8OnRS5PPukb14cvMG0nhrC4FD3zA9uTAEClT6SMimC07vmI9LB9fi4c2jKNXoR6TJKIXYtWgEBIHVq1bhccBjVK9eA1m9vDQmGoFERcCI/+wmhNPP1wODv1yHfzdewMdvV8Wkbxsjr5+pNAzdbunG+4LmT7xQx0xQ5CyJgwb2rYhyJf3EVXcjtmy/hMUzOwuRND0+m7vUvpCSLHWr58bsv06gbYtS6NK+kmThdcIHn60WQvsCTeszwzMJcSQMNFGR0oNrBBIOAU0+Ew5rPZJGQCHgUaAAMvgVxMOLp3Bh3jwU6a/Tm+uvRtJAgA9a+SsORFqP3Di8ZhzuXb+OLbPaI3+VLshdqqfDWEHPnT2LP37/3Sqog4cNg6vEhv0wbhzu3rkToU3jZs1QtVo1bNm0Cf+uXBnhupe3N9565x1VE3HMqFERrvNEtzffRO7cuTFv9mwcPXIkQpsyZcuiVdu2OH3qFGb+8UfY9U+GDoW7RcmIsItJ5GD3rl24c/s2SpYspclnErlnyX2a/H+LZLFB7cIoXdwX4yZuxmu9FqFsqexo0eypSkbk451RwdC7c1kUyp9VyGcwnkjCvT37L2HrzqsYMmoD/HJ6YNB7NZVV1GTBDE8iU0k6oteFcP678Rx6DFiI38e3xqstywqhBT78dC2cnZzRqHYRFnBN7pDr9WkEokRAk88o4dEXNQL2QcC3dTuc+OFLXPlrriaf9oFYa7UjAj6F2iKjJEA6uOo9PLjlj5Ob/8DlQ/NQoHJ/ZC/YSrmX2XH4KFWfOnkSkydOjLINLz558gQBAQER2gUHBalzz2Rv7Tr7UWglsXZdXWNKTZHAp0+ttnkq5ynM1mmuQ2XHVFf0D42ARiA6BEgqS5YurZo5OUf/OMtan15ZPTB6aFP07V4ZcxYdxOTpe4VYroZX5vTwze6BdGlTYeXa87ju/wRXrtxDunROqF7FDz+MaizxogXkBZuphMqVK5eRI0d4jw/OJ427C34b1xo9hXz2GLAAv33fFh1alcXTwCAMG71Wao4WhKuUiNFiWwTWr1+PokWLIpuONbctsHbSFv1vq50G1mo1AikZgdxS3PzE+K/w6NIZ3D16BJ7FdLxHSv4+JMW1p/MsgCrtFuPS4ak4tX0qAu4/ETL6jbjkTkDu0p3gW7g9nCUxkTUJUfUOaI2wvQUgSJLdUFgDcMDAgRGGd3aR2CyR7j17KvJn2SBDRpMFpGLFiihSRKwUFuIc+pDLsg7vffCBxVXTxyxZTKUSXm7RAg0bNYrQxj1NGnUuT548yor60/jxEdrY+wSTmxw4cABcpxaNQFJEwMnJCV3eeCMWU+f/OQzNdBIX3Kz4qF9dfNg3BNdu3sfJM7dw7cYjBMjLJRcnV3hKFtz8uT1RIJ8XXJyd6C1rcpgN/T9r8OAhqFmzJnrK/yPmQutnJo+0mPyDENCBC9Hz3YWY8kMrlC3pi7v3t+FZ0Au4mv4LMu+mj+OJwN69e1G3bl1cF28cTUDjCWYCdNfkMwFA1kNoBCwRSOPljazlq8F/92acn/EHPEd/bdlEf9YIODwCqYTg+ZXqAZ/Cr0gW3J9x8cBSRUKPbZyCE5unwjtfSfgUaoHMuerA2SVt2HoCpGTB4TUfonjdL8E6ovYQH19fcItMosvGml7KIHGLTPjgG5V+9vPMnDmy7uq8m7jYRqcjSgXxuPhCXgBUqlQJO3fu1AQ0HjjqromHAL/DF86fVxPwEzd3/k7GRNQrL0UiTWQ0R7bM8JVNZQxSChj3GfXLsSPiTj99+nRkypQJr0iN0TAhSQ1xUiVWaAF9Uwhoo1f+J6QzGG2bF0ZasYxaym1xUz937pzl6WTxmdluE4IMpk+fXuFVr149rF27NkHGTBY3KJEWoclnIgGvh9UI+EmZFZLPa0v/QukRXyC1a+LUcNR3QiMQXwRc3DxQqOog5CvXH5eO/Q+XD85FwINAXD99QG2pUo9CZl8/ZMpRAZ7ZK+JZ4G2JF72GrXO6I0/ZphJH+j6czMhpfOej+8ccgebNm2Pp0qWagMYcMt3SQRAg+fxlwgQ1m88+/xxp06WL28yUVZNdFS0120euji6ederUwdtvm3I2RCCgYgFljc9Zv7THpm2n4ZbGBVXL51OWV0uty5cvx+uvv255Oll8/uGHH/COxMgnhLwpsfY5cuSAJqAJgXb8xtDkM3746d4agTgjkKNJMxwakgFBAQ9x6e8lyN3W7O1pnLXqjhqBxEOAbrZ5y/RBntK9hVzuxtUTC3Dr7DY8DQjC7cvn1QYsCJsgYxzP7V2O6yf+RZE6Q8LOp6QDuvF+NGiQWjJdeRNS0oj772+//QZbEtA8efMis1h8qVuLRiA5I1Ba4k27dOmCJk2aqGWGI6ByhjGm/J1uUCfmYTVeyST7/S3J5J8YMnz4cDWsJqCJgX7Mx9TkM+ZY6ZYaAZsikFoeOnO0ao/zs37D+em/a/JpU3S1ssREgLGcnj4V1RZSOwQP/A/jzuXNuHdtNx5cP4VAIaPm8uRxEPYt+wwv8km8ZsQEtOZNoz3OnDUrakvsj4eHR7RtHaEBY1MTsyTJyy+/bFMC2qFTJ0eAVc9BI5AgCJCA/vPPP5ES0NhMolr1Wpg+Z2lsujhs27d6v46Vyxclyvw0AU0U2GM1qCafsYJLN9YI2BaBfN16CPmcgntH9+O+lF3wKFjQtgNobRqBREaARNTDq6TaUNY0mZNbvxCL5zL1wTWNvIQp1gQ5i7+GTTNaxnu22bNnx0tCqJKKBElWXSM7b49evZSlJKHnbksC6n/rFoIli29mT09V0iah1nL//n1cuHAhbDhalPkCwlfifu2R2MoY6KRkV2a8GcexhTx69Ahbt24FvxcNGjSI8/eBLqmHDx8ONyXOM69Ypu2JR7gBU8gHWxLQFAKZ3ZepCajdIY7XADrfc7zg0501AvFDIEOePMhcqoIoCcGZKZPip0z31ggkEQSCnj1C1twFULbZMNTutk7iRYcgbcY8Npn93bt3sXf3bhw/etQm+uythK7H5yXZCDcShsQScwK6S2p1xlV+mzQJ477+GpcvX46RCpKt4ODgGLWNqtGqVatAEmBsxYsXR86cOVGmTBnwmq1kk9R/fcMsw2pbqdc6ZswYm6hnBuLKlSujcePGaNeunSrnE1fFxNXAwtjnz59fYbJt27YYq7Vcb4w7prCGxJgWUMaALljwX2hBCoPBoZZLAvrqq6+qGNAbNxLHDdihAHGgyWjy6UA3Q08lZSKQr0dvtfCrfy9A8OPHKRMEveoUhUCRGiNQ/uUZkg23mZREsa0DzrUrVzBn1iyslAdBLbFDwFYENCajkhC+9NJL8PLyUjGiJHSzZ8+Gj48PmCGTRIxC6x3PcVuyZEm0qpctW4aj8uJhx44dSt+9e/fQpk0bnD59Otq+MWkwc+ZMHD9+PCZNY93m7Nmzau5TpkxR9V/dJRtyfGWQxBMTj3379oHYsAxQq1atYvyiw57rje/aHK2/JqBR3xHWN+bvo702ay/vNAGN+p4k1lXb/tVPrFXocTUCSRgB3yZN4Z4pKwLv+ePc7P+hYM9eSXg1euoagegRcHJO2MQ60c9ItzAQMCeg9sqCe/DgQbRu3VoRLBepu0o3UJatoMUoICAAxYoVg2to9m+SSNbuoxQoUMCYZqR7tilUqJC6zlIyrMVIK+j777+PxYsXh/VbsWIFNm/erBIjkQSXLRvqEy4tLl26hL///lsR1rRp06q5li9fXpWlYYmNmzdv4pdffkGfPn3C9P3111/Yvn27cmultcWo9RrWIPSAlm7Og23pBtusWTOUK1cOFy9exNSpU1WrUxKCsX79epVN1bI/58zt6tWr8PPzQ7du3SIdi33phs7MrBRagTl39jl//jzy5cunzkemk2V4zNfbXupT8wVBy5YtsWjRIly7dk29QKhatSrmz5+vCG7Dhg3VvBnLTIkMS16bM2cOSpQoAa6X1li6LvMlhKe4bFM4/m7xYugl7uhGfV11IfQHS6uMGDVKfbKWrIsW6Q0bNoDfMVsLX4Q0slLD1yCgkSUhsvU8kpK+X3/9FQMGDLBLPD7d7iPLqqtdcB3vW6LJp+PdEz2jFIaAqpXYpQdO/jgG53//FQV6vKljclLYd0AvVyNgDQFaqpiJltZBWwsti0+ePLGq1pyAcg4VKjA0wDbCcZkVlCSTxGjLli3ImDGjStiyZ88eNYj5eN7e3sqVkWSmSJEisZ4ESy/QlZUkxpC+ffti4sSJal0kY59LmQ5aGzt37owDBw6gWrVqam6lSpVS8/ta3IjXrFmjXIlJpujaTfJKUkRhX5IykjyWlpgkD9l7pOi9QcCMcbnv2LEj5s6dG0YEOfaPP/4IklvDHZaurrmlbmWdOnXMu+LLL7/EZ599puqzMpvw5MmT8dNPP4FxpzEhWCTxHJuklbGflKh0njlzRpFHY721atVC//79MWLECGWdJnZjx45VsakksMTrq6++CiPmUWHJlwKjhDj6+/vjwYMHYTVnuaZDhw4p7PjdGzlyJF577TWrhIUvLaLKqkyCz98dWr5tLd27d1cvDazp1QTUGiqmc3RLHj9+fOQN7HRFE1A7ARtHtZp8xhE43U0jYEsE8r/RDad/+Q4Bt67i6qqVyNG4iS3Va10aAY1AEkSAbmokICQMCS0GAaVV0JYElISEli4KiYthfWPJCoN8VqxYMWy5nAe3+AgtoHTzpXWELqgknt9++62yhjLelJbA9957T5HiP//8U7n4cp7ppG4kXQRJkv/9919FhGiR3L9/fzgXYFrq9grZJNH5+eef8dZbb6nkRwbBM+bOeo68l99//72yAJGI05o4cOBAZfnkeZJQEkoSGHOhxZTWRZIezp8ybdo0NXe6AZcsWdK8edjxBx98gE8++QTPJQmU4cpMokjiFp1OEmXz9Rruxk2bNsUff/yhSCPXTNJMSyytxLxXtIrSKhwVliSfFFo0r4irfKZMmcLWQ2sr10MrGb8XGTJkCFuP+QHv3fdyHylvSVtLIkrdfHlDS21CS2IT0AsXzmHOzCk4eeI4pkzXMai8/5qAJvRvQeTjafIZOTb6ikYgwRBwlayMOV5+BZcWzsLpn77X5DPBkNcDaQQSFwFax1gahuIsboSWwpqZtP4lhtiDgJonMyKJMYTkyBCSRVuKEQtGV14SLwqtgLQ6UphZlhY4WhBpafvwww+VxZMWOJJKundGZiVmf1oEDet07dq1eUpZDC3JJ91IaaGkhZXC+dCqR7JGV2RagCMTkkXGbdJdliSU5NhYS1Rzo5WZCYyIL9c8S+KheV9pXaU7clx0Gi8DOF9aaGlZJvGk0O3ZSPAUEyxZj5HEk0L3Y8rDhw/Vnt99blEJra+UkERM1hXZ/BKTgK5asQQL5syEZzT4RTb35HpeE1DHuLOafDrGfdCz0Aig0Fvv4NKiObh37ABu79uLLGVNf4g1NBoBjUDMESCZY6IWazFgMddiavnFF18ol0p+IgFh/BjdOCm03jFxC91HKSQSjBM04g3VyRj8oHXGkUvDxJaA9u7XTxGBDJEQKcN6RrJiTrZoYaPwvlla/dSFePw4duyYsmbSMnZLSsFQLBMQGUl4Vq5cqTJk0kpIN1ISq+hcWrNKbVlDjFhVWhUthQSX5V/M40ENyy+/R+Z4WPbl53fffRcTJkxQMZGM3yS55XcyKqlSpUq47Lx0OaYll4mERo8eHSed7G8If9/MP/P3xJCYYGkQT/aJCjtDZ1LbJxYBfbPPAGzesBrXrl5JapDZfb62IKDashy/22SKCI+fDt1bI6ARsAEC6eUNcrYaDZSm42O/toFGrUIjkPIQKCLJakZKbFwfcX2Mr3To0EG5E9LaxPg3klEK0/YzoQhdOHmNcYCfffZZrIknddEixdIw3GxRcoQ6bS3mBNQ8dtLaOHRBzSwZVSMjbMxaSyHZosWPMm/ePEXcecwENMaLA9a7ZNIYbnRBjoswWy7dhkkuKYY18qOPPsLChQvVRgtoHXFvZpbdwYMHq9hSEkUmO6J7bmBgoLpPcRnfvE8eKa1FvbRaGrJ69Wp1GB3hZiZcxpMOHTpUJQ2iG7BhOTYsu4bOqPYkeCSILMUSV520whpijWQb1+yJpTFGUtgbBDShy7A4ObsmBXgSZY4koPEpw2JYli9dOJso80/qg2rymdTvoJ5/skKg8MAPZD2p4L9zI+7ZKZ1/sgJML0YjYEcE6EJoXtOR2UgZj8Y4Mj64G8KENIyPi4uQfLI0DDe6fzqqxJSAzpA4xAlCkq5KHJ81IWk3rGN0CWXyG8Y9GmLucsv4ScYrkgAahNRoF9me1mdmUWUSIJYZoRssXTeph8Isu7QwMhZy48aNyq2W49MdlVY4tmUCHJJEZqDt2rWrIr6Gayvb8Py6detiXK7EmCtrd9Liy8y7jJMk6WbWXGbaNSzqRlvLPTPj0qLPeXF+JMYff/yxambMzbIPPzOOlnhwmyQ1WJmhlVZdJuGJic74rDc6LK3N1/wcCTatvST/SV0Si4AauF27ehn93uyMHq+/gmGDB8r3yOQBYFxPifv4EFBalouVsB5nnRKxjO2aNfmMLWK6vUbAjgh4yn9mWctXkwCWEBz/erQdR9KqNQLJE4FjYo0cLA/lP9koo+KwYcPCEpnQ+saEMObWPyaL4ZYSJCYElMljLl64EClhINEimTRqWDJRTRmz5Drm5JPxiBTzBETR4UxSyRcB/cT9l66lJFt0TSXJpdBFlGVROC6JKTPb0hr7+++/K1L86aefKqstrZR8+UBrdIMGDVTZD/Zn/CTJH2MV+QKCbqfcYiKMj2S85okTJ9S4r7/+OvLnzw+6p1IMi6KxN9fJuF9+F0lYSQhZ0qRnz56KzJKIWoqhgyVsiAc3WntJVEnM60qccUx0mq/38uXLlsNE+GyMywvRYRkddoxppbU3rlbvCJNL5BOJSUAfPX6EQwf34OWWr+DzL8cha1bbZ9BOZHjjNHx8CKi2LMcJctXJOe5ddU+NgEbAHggU+egTbG7/Mm5sXo37kgDDI7RmnT3G0jo1AskNASYeCRYLoq1cWOmKyZIafAimmD8I0y2XRColiTkBjWsWXFqzevfurVyZiS9JIkt4UMyJJq3MMRVaFaNyATXXU79+fZw7d04l4GFGW/NsqszCyuQ/JNG03FlmUCXpvH37tiLXJIHmLrQco2DBglHOg3U96aZtZHjl+IaQmEe1Brqx0tpJEkjcSN6GDBlidA+355qi0mU0jk6n5XotdZJIm8s333wDbpTosLTEjqV0zPVzbvQ2ML8/5mOR6DYVLwRKZG7e5u0d4dggoIzZpdu34Q5uz7kd2L8HA9/uie9/moryFarYc6gkqZsvSZg5mm7RfLkTF6Fl+fPhn8jfh0D45vTDgIGDNMGPAsiYva6LQoG+pBHQCNgWgSzlKiBLGfkDEfICR78caVvlWptGQCMQawT4EGxY6ozOfChnuQlzS49xLbnvSUBphSNRNBL4xHbNJHWM72QCHiMDLkkGa2UmlNAKGhmxoRusJfE05sXvAolnfIT6zYlnTHXRZZkW1JhaW2OiNzqd8V1vVFhGNT+WlKF1N7K1ct515feQm4vEsiYVoSs0syMXk/h0e8vZs6fRpUNzSWrWWhNPK2DzZQfj9emCzmRecRVtWY4dctryGTu8dGuNQIIgUHTIcGxu1ww3t6zFXUn37xlJDbcEmYweRCOQwhFYvHhxBDdSJsExsnOmNHguiFvtuHHjlDXYKDESHwwM8knLX2REIz76dd+kiQDrfEYljJfeGlo6p4q4TycF6yfdu+nqSZfi2GbGjgqLyK7lECtc5ixZMWXSj6hTvzHKla8c1vT06RNSC/R3ZPTIhPYduyJbdp+waynhwCCedMNfs2ZNnEtaacty7L8t2vIZe8x0D42A3RHIIi45WauwXlwIjowYavfx9AAaAY2AdQRIPA2XUPMWY8eONf8Yr2Naliwtq/FSaMfOJJ6MF6Tr7DvvvGOTkVjy488//8R3331nE31aScpAgOThb/n95BYkSZQcXQziSaKTEMSTeLiJRfjnSTMlNjg9+vfqIpmSbyiYmCH5+2+/RKHCxbBt6yYMfKeno8Nn0/nZinhqy3Lcbou2fMYNN91LI2B3BEp8OhLrX6qD2wd24tr6dfCpU9fuY+oBNAIagf8Q2LJlCzp16qSyg/531nTExDHMoMoYrvgIracsDZMUJCbEs0rVqqqMSmzcUlu0aJEUlq/nqBGIMwKJQTwDAh6rBFM+vjnxw8+/4/WOLdG3Zyf8b+5S3L9/D58M+Rw5c/mhcpXqaFS3Ip5JvKKrm3uc15hUOtqKeHK9UVmWkwoeiTFPbflMDNT1mBqBGCDgIYkrcjRrq1oe+XSQKtweg266iUYgRSPgmzMnOkkWUSMRSVzBYLIbxjayHiWFJTJYxoMxihS6/EWW7EU1SGY/YkI8ueQ6En/XTJLAsNanFo2ARgAqkzJdbRPS4jn688E4fHAvrl27gqGD3oN3dl9xr/XAgX270LZFHRw5vF8RT96fixcvoKy442riGftva2SW5dhrSlk9NPlMWfdbrzaJIVBiyDA4ubjh8bULOPPH1CQ2ez1djUDCI0CLGzNJxsetjdlIWY/y7t27agGMJZsxY4aq78lSHoYw2+vWrVuNj3Has+biYCmBwS2qeo1xUm6jTjElnhzu4IED2L1zJx5KORItGoGUjkBiWDyJ+aBhX+LwqZs4fekBvhj9nZQNKoy9hy+pz8v+3YF6DZqG3ZrFf87BF2NM2bzDTibDA1taPAmPpWX55s3ryrIcKOWMDHkm5bkYE8qxaVm+eP6M2h4HPFL7W7duGk1T1F6TzxR1u/VikxoC7pIRL3/vAWraJ8Z9iad37iS1Jej5agQSFIHr165h8aKF2LBuXZzGDZIyLSSe5jUNGd/Zpk0bpY9JULJmzRqm+/PPPw87jusBy8LYqjRMXOcQWb/YEE/qWL50KebNmRPnLLiRzUOf1wgkNQQSi3jGBqe/5s9ClRq1FTmNTb+k1tbWxDM6y/La1SskGdY6TPxpLHZu34wm9Soh4PFjTJr4I5o1qgb/W7fQtUtbXLl8MalBaZP56phPm8ColWgE7IdA4X79cXnuTATcuopDn32KCuPjng48JrPkf9K3pGj5mUk/4/6BPQh+8jgm3XQbOyGQSmr5OadJh6w166FA737wKFzYTiMlD7V3pAbjlo2bJBYnJ2pLYpzYCq2cUdWXZGmOuJYXie1cErt9bIlnYs/XEcY/ydrM4t6YLVs2u07n6tWruCMvI1muxlz43WUJEtbMNITJZQ4fPoyc8jvBlxz+/v6qzAdr1rJOZ/78+SOUfbl582ZYO0OPLffHjx9X5WpY7iY+mBnu3fx/0lK4VpbR2LBhg+WleH/m38k33ngDlSpVsqorKRDPVf/8jQcP7qFrj364d+c2Tp06gYqVq1ldj61O/vjjj6qmpq30GXoePnyoEsNRv6XYmnhSPy3L3MyFlmVz6d29PUqXraAyeAcFB+HM2VP4bNRYnDx+GH17SI3o739FGbmeEkWTz5R41/WakxQCqeVhuMRoeXvWsyOurPgLfts6wbuqff5APJUH962vtcODM8eSFEbJfbLBT5/g8t/z1OZdsyEqT/wNqZNQXbvkfn/suT6WAShfvrzNh3ghMauUffv3W9WtiadVWKI92a5dO9SuXRvjx49X5K13796qfqCPj23LWMybNw/vvfcerly5Al9fXzUvHpOMMokVyaMRn7xf7jG/Q8wofPToUfz0008SC3gNvMdMmLVx40bUrFkz3NomTZoU1i7cBRt9eOmll9CxY0d88cUXMMcsNuqdnZ0lac6QSLv06tULZ86cifR6fC6MHDkSVapUsUo+kwLx3LRhDfq9+ZqKZR/39UgV275kxab4QBJt3z59+qBbt27RtotLA35f+ULDUgziye/+2rVr41xOxVJvTD7v3b0TH0tSp3z5C6FHr7cRLP/nOsuLoU5demLIx29LJmK3mKhJlm00+UyWt1UvKrkh4FO7DrLXbYbr65bjwPtvo/66rUjtZtv/uEg8N7V+ScWX8i2yV6mKyNGgMdyzeiGVU8S3yskNY0ddz4vg5wi4ehmXVyzFndNHcXPTv9jWpSOqzpitCaij3jQbzYslTXbv3m0jbeHVBAYGokaNGuFPhn7SxNMqLDE6SUubYfUk6ePLA3uUkGnQoIGaDzMyk7xR/vnnH6RJk0ZZN5ncxnAVp+WPhLR+/frImzcv8uXLp9pH9YPkMCbtotIR02vmmMW0D9uRWBglVlxkfalSpQrXvWnT/+Iaw12wwYcd4h1kTZIC8eS8a9aur+I/ra3BXuf4HeRmD6FHiuX9T0ziyTVWrlZL3G6/w7ARY7BtywZ4Z/OBb45c2LljKwaL1XTggF5YvGwD3OV3NqWJJp8p7Y7ru7SlSAAAQABJREFU9SZZBMqM/hpr6m4wud+OGonSI0fZdC3bu3dRxNPZ1R0VPhsDjwIFbKpfK4s7Ah7iFudTszaub9uCgz98Bf+9W7F/2GCUG/Nt3JXqng6PgKenp12snlw4ySeJiqXYm3gySdPmzZtB9+bKlSuDJMeQe/fuKQK1b98+VK9eHQ0bNlRz3CkJjK5fv67Ixp49e9C+fXsUL15cuWsuWrQIt+XFWcWKFdG2bdtwD6ArVqxQY3GdHKds2bJqqF27dkmGz4soKBnFWceVmYtbtWqlElWdOnUKq1atUi6V6dOnV0Tu119/VRYujkEhyaMrK2N/LedFV1YKXWKpm0JC8rpkYCaZo4uptTmz/ezZs1GtWjXQqkmrGq2RPMeXEMWKFVO6jB+0cNJllViak0/GKzNhFtdukE9aNitUqKAsoVwrk1xZkxs3bmDBggXKZZdWU6MdE2/NmjVLxUIvXLgQjyV2jXhSpyE8xza0PpHgskRR5syZjcsKpyVLlihrK7NIm4uBmXGOa+JG12I/Pz9lLctiJXsy1zJ00CDV7TOJvU6bLp2hIlH28SWeW7dsRLH8/8WTJ8oibDSo8d2xkbpYq0ls4skJ9+jdH4M+eAvVKxZGu1c7o9+Aj9Gtc2v07TcQpcTd9qsvh2HAW13x9diJ8JD/61OSaPKZku62XmuSRsBN/viWGPkN9n3YD+fnTINP05ds5n5799Ah3Du6T/BJhfLyRk4TT8f8qmSvWh3PH72Nw5N/wJUl81BiyKdwzZjRMSerZxUjBBif95oQE4q9rAIxmog0sgXxJNFLI2VpUluJwfvll1/w7rvvhllcx4wZgw8//FC5Xj6Q7Lh169TB0WPHUKtWLfz222/IkycPSBRJWJn0icmg6GqZI0cOnD9/Hq+++qoiVCQ7X3/9tSJbdK+j9O3bFxMnTlQEiS6oTAw1ZcoUdO7cWRGz77//HiQ9JUuWVK6rX331FWhFJOnq37+/GoOEdPv27eoziZyhm+6chvug5bx+/vln5XbLrMiG1ZpEigTymKwtsjmTaHFcvnBgrCLXXUBeAPLc5MmTI5BPrpGWTM6ZQiL277//gpiSWE+fPl2d50M4x6ceCokz3W67du2qPhs/iFE9KZPDsWk1nTZtWlg7klL2p3WJ7sPu7u6gtZJkk66zJM5Vpb4rx+VLA+LOeXBuJI/sT+LOdnTz/fbbb9V8jbFHjx6tMCPx/lJq3lI3Yyn5XeLaOV+Sdr6wcFSJL/E01pXYpM2YR1LeOwLxJH5ly1bEitU78EBeqhnk0ty1+dCJ60kZ5njNXZPPeMGnO2sEEhYBv1atcfXvRbixcRX2vt0L9dZsgas8LMVXzkyeqFRkKVwSmXRCm/jCadf+vvUb4Myc6Xjy8C7Oz5yBQv3esut4SU05rR8FxKLlJZmik4KQfJaW0jCJLbYgnlzDgIEDI10KXVBJTkhuKCNGjFAWMR6TgBySpDgkaLRIHpIXYrSssbYqhZa1pZJJt1GjRsrqyFI6tGSuX79eEVLqpuVz+fLlisCRAJHkvP/++4pQkSwyRvKVV15R+mjNY1u6ZtJySpJESyVj+WhlpPWT5JN7CsehtZPzomWR12jBNJ8X25F8UqiPhJfWWxITJvshSY5szmxPadGiBaZOnapi8NwktIKWWFo4rQldb+fPn6/mQBff+/fvo3HjxooEksQxyRCFCYaIW2TCBFr9+vVTl+miG9l4fClAjPhigZZNxrPS6sq4Tb4M4L2jdZdEluWOPv30U0Vihw0bpsoWnTt3TsXczZw5E126dIkwHZIGrqd79+6KwLIBSTDvHS2qfFHgiBJf4tm6dWuFnyOuLb5z4suUhBRHIZ7GmukKbBBP45zeA5p86m+BRiCJIVBu3HisrV8dT+/fxq63eqP6zDnxXsH9Q7R6Aj516sdbl1ZgXwQYj+tdrTYurFyEu/v32newJKg9jzzg9xKrV1IRWpqWLTURrGbNXgJj1xJabEU8o5s33UiHDx+uSCVdL5s3bx7mVkwrIYkZiSeFROPRo0fK2rV3715lbSNRJPHhfGllIwElaaWQGNIytm3bNmQM9QagS6xRCodWU5IwWtAoacU6a8QE0qUzV65cYMZMCgkgXV8ptCbSZZaWxANSw5TurCSKnCvJJ62AxrxUh0h+kLBGNWeDfLZs2VK5DqcLdSGl9TMyoeWTljJaZ0mOiQetxbQM00pJd1sKrbmRZWXldVpBuXa2j4x4sh2JO1+WUEiY5khJHRJOWjjpYvu///1PXeMPYsp7QeG95b339vZWn9mXxNpS+KBOl2sSWZJQ4k2rLcVRa+DGl3hybXTv5qYlfgg4GvGM32qSd29NPpP3/dWrS4YI0NJZ/qcp2PZ6W/jv2IDjP/6AIm+baoHGdbnPA0zlVFwT+C1lXOeb0vsZ9yn4kelhOaXjYb5+xjIydtDF2RlZzOpxmrdxpGOSJpaGoTRq3AQJ7Vhoa+L5jbiv3haS11ssaXnFCmYutIAxMyvjGOnmSvdKuqHOnTtXxfdZlg0xd7NkfKXhymuUuqEbJ4mpIYxDJHnimiinT582Lqk9rZXEm2JkglUf5AfH4sMrheST8yNpon666DJTJjdaX6nHEPN5Gees7aObs9EnNllxSZhJOLdu3aostIz3pHAtJKZMikPXYh4bpNEYx3zPUiv8nfnoo48UkTRwNm/D48JmXjG05FJI6GnppJjfC5JmEnwK124eH0pibfRXDcx+0C17woQJynpN6ymtxfYolWI2ZJwPSTz5HaMln/dBS+IhwN9duuozFjyhs9om3qqT7siafCbde6dnnoIR8JJEHYX6f4QT40fjxI9fw6NESfjUrRdnREyPXNJd3jxrcXwEwmrahT4sO/6ME26GZ+VhcJo8FFLSmVkTSIReFwvPLXlQ/lkebq3JO/Lg6ykWnKnyEEMrlaXUEOtNfXGj3C/WmcWSeMVSGKP2UWgClC/EpZRxeJbyaocOKCpuncuFxOySRDpGtk7LdgnxmdYkxiLygf+dd96xyZBcMwmeQeTMldKdNHfu3OphnWRniJTJoJsqiR7Pb9pkIuHsQ4swLYqGe6Z5JksSEgrdQI1MsiRZjA2khc8gTyRTjEWk0AWVJIGELTIx5sxESCSxg+Re8p6SODEekpY+WuNGjRoVpsJ8XmEnLQ6oN7o5W3SJ8Ue63q5evVpZDGlVNoTuviTNfBlDnKMSI/6VLruMYWUcrjWhFdLAc926daqJQRBJwhgTa2DPxEVG/GKpUqWUZdbQyTIv1kqgnD17Fj/88IOyjnMtxJYW6GnTpoW9NDB0JPae3ye6OrNOqiaeiX03TG75nIUmnol/L2Iyg9QxaaTbaAQ0Ao6HQJG330G22vKmO+QF9vTvgXtWalw53qz1jDQCCYfAY3HbNLYnAQFq4OdCjIxzlvsXoWQ+QNpaXuNnIysniZG164z/M+SRuDFaa8O+FOrideMB3eiX0HtbEs/o5k5yyXg+7hlzySRDtI7RHfO1115TtSdJlOgaS6soyQ5rZloKXTqbNWumXGFpfSJpIXlmX7ov0q2TrrdM+kNXUlrkmCGXCXIyZcpkqS7CZxIoWlFJ6kjOaTUkyaMllO6sljUxIygIPWHEuzFhEu97VHO2poNuw7w/dKuNTDgvrpGJmOrUqRPWjESS8aJ0940q3pMduF62eeONNxTu1uolsh2z/tLtmHGftDLR0so10i2Z4wyUeF/eO5J03k/jBQ6x51wY43vw4EHVjvoshfeObsy0pvK7Qcvtxx9/rJpZc7vlvOliz81N+iWkEC9NPBMS8ajHYh1bJrYyXLujbq2vJjYC2vKZ2HdAj68RiAcCFSf8go0tmuLBuePY/lpb1FyyEulymNyh4qFWd9UIJFkE8ooL4buSZMZSjEyyJC7WrrO94YrZXjJ4WiOFRlwWE9JY02FuBXtHkttYE1pWKXXFFbKylNMwF2txcObXbX1MK5OtLJ4xmRvjL5nVlaSFBJ9JoRgnSDdMJrDhw/w333yjMp7SxfPHH39UFlGSDHNsORYz15LIMvMsra28J8ymW7RoUTUVJiDq2bOnIq/ElTVNud6o3E/N10DXW45Bl1UKSR6FcaokehRr8+I5Q2gRK1KkCN566y3l1hrVnI8ePWp0C9vTrZhzpjsyS69YE5JjfreZKdZwc2U7utJy43wMq6vR3xJLY87jxo3DypUrVYIfkm/LdqxfapRJ4bh0n6aQtNLVmRmHOV+6ItNiTfJPITmlpZNW6s/kpQItoZyTod8Yn8SBrtm0xNKKTaJP8snkSSSiHNNc2I/JxRJDmGTJ3C08Meagx/wPAWsvqf67qo8cDYFU4g4S5nHnaJNLCvPhm2sjSYGt55tdUppr0QhEh0CgxNNsbN4AT+7cRJqsPqi5aAXSyENCbGRl5dIIlP7lBn0Or7LlYtNVt00EBM4t+gsnZ01B1go1UH32/ESYgX2GNL6HVabOQ7YaNe0ziNZqVwS+EpfUO5I9to8QrnxCfqwJXUFp3bIW98fEQHTLNBLwWOtvfo7WZmZ5ZSypNaH1kOSWRCYxhI9YzKbLhDwGyYpuzubzJImjFdGI5zS/llDHtISS1NMSTSJMt2rDqms+B74EoAWU985Yq/l13ltap6zdd/N21HP58mXlIm1Nj9GW7eaGEuC2knU3oV/eGPNw9D1fpPFlD63+vHf8nTB+X65cuaKOjRcBjr6WhJgfY5j5ooVy/PxdOIcm2UqIsc3HOHXyGJrWr6xeijJMIaaypHBOcYh7jvqrtyO9hDM4ovz3is4RZ6fnpBHQCESLgLtYD6rP/xuu6TPhif81bGrVFI/lD4oWjYBGQCPgiAjQtTIyAkJrUkyJJ9dGYmk8SFtbK2M3E4t4cj58qLdMShTdnI110G2VFkVabR1F6B1gjXhyfrQq58mTxyrx5HXe28juO68bQj2MAY6KeLItif1+canm9jzUnd3Qoff/IWBkGiap6tGjh8qGzCROLEXE+8GyRFo0AgmJgCafCYm2HksjYCcE0smb5hoLzAhoi0Y6BtROWGu1GgGNQOQItG7TBp3FDdM7lt4XkWtMuVdIwhnPabh7JxYSdOel229ikvjEWntyGJdu4iT+JPXc6KbNzXAfZ1ItLRqBhERAk8+ERFuPpRGwIwIZ8hdAjYXicpvZG08f3MHmNk1wZfkyO46oVWsENAIagfAIFBb3zFKlSyc6YQo/K/0pPgiQBLNmJ2M1k6LQQkq3U6PMjrEGnjMSgBnnmAiM7sFRCRMrMc41qch6qQHL+GHGu0+aNEkl+2LWYiazYthYV8kCrkUjkJAIaPKZkGjrsTQCdkYgg7g81Vq6GhnzF8XzoKfYPeBN7Bv8MZ5LjJUhAZLw40U0f1yNtrbcN+ndG6XbtMUy+UOYXOWpxNYEafev5Hp77b4uPiQbm90HCx2A2UlZ7sSo1Rjfcf/95x8skpIb/hKLrkUj4AgIkHDR1ZkJqgxhciqeKy0vSgxhUiSeiy6+dv78+SrTstHP0fe0ehrxsHRlNk9MldhWdUfHTs/PPgho8mkfXLVWjUCiIcAY0NpL/kHO5q/KHEJwcf50rKlbDVfXrFZzOvvHVGxs2QxMVJSQsu/oMZw8fx6XbpgKkifk2Akx1gHJ1JmpSlV4SaIcklAt1hF4obGxCsw0qWXIB0NjY3IQJpuhxcKewpqVzMbK7LCUJUuWYNGiRXEecs+ePdi6ZYsqlRFnJbqjRsCGCLAMB8W8jizrQVKYZZgZmCm8ziRGRnt1Uv+wKwJMxsUasdFZm41JJDWrszFvvQ+PgC61Eh4P/UkjkCwQSC3xHOW/+wHeUhj90JAPVCKiXX26IFOR0hILekCtcX3j2qj4+/+QpUzZRFkzCdotqfXnI2n5mZTjkNSBSyexJ/mlCLxl5r2HktHy5LnzUvDdHUXz5Qt3nZbGU5KUIzDwKQrm9kMGqRVniPkYN+SP3Hl5yKhQvLiyLhljm593lTfElEuSDfCKkORCeXIjsyTYsBTL+dCd63ZoNjqOyeOMMo/0EiulJTwC6xrXgW/rV1Hwzd5w1rFGYeDwIYxSvXp1VKpUSdVKnDlzprJSsL6ivaRPnz5gSRCjhEa3bt3UgyDrLMZHZvzxB/LJ72qJkiVRVh7+z8gD5lbJlmoptMi8KqVtKP+bMQMv5OHfUmpJiQ0moNkh9S5PWqln7JsjB+o3bKiI+jIhz9akzSuvIJ38Ti5fuhS3JdOupZQUC1iZsmVxSizB27dutbws//ekwStSr5IyQ14UWBOWz8kp/39tk/6nRY+l5PTLhbr16iur8Aqp/WlNOAbHWirruGvlxUNpmSPdmk8cO4adUn7EUmi5ayOZX0miZgme1oRYETPeD94XS/ETrGsL5jclM+1KqetpTXjPeO+WyIuK+1YycZarUAHFJTPuMSF3u3fujKAig9RhbSXxwSQdc6TcjjVpKLVKmfV/s8S9npNarpbCeZYqU0addgotf2PZhp/LlSunkiSxHiwzvTIJFTP3GsLkOx1lPVvkpQmFZXXokjt8+HBV65VxkqzROnToUBUrafTj9blz56qEV6xhGtP6r0Z/Y8+at8zwnBzFR+6ftQRVzHrcTr6nxgsBxhMvWLAg2pq0tDozE/If8v9LXIRYs+5ucXkOKCu/S1oSBwFNPhMHdz2qRiBBEMj1ckt416yNI6NG4vKSeWHEk4M/fXgXWzu2RInhYxJkLpaDfPrjBIyXh47u8gCySh7WLstDAaVWhfJYLPX93OXBhi6Iwyf8hB/kIfxZqKtwMXmg3TRzBtJKxszxcn7ELxMR8OSJ6uskP98QfWM//ED1N8ZoUacOVm3bisCnz7Bg3Fhs3LNXjW15Pr88zLz+ySBFhJVC+dGxaVP8/OmwKOfTVx5c3hYLkiH5mzSFpzxcXV2/zjhll32IEO9gIeZJSZ7cvIaTP47Bud8mIG/P/pqEWty8evLCaOTIkao+ZatWrbB//37VIrKHYT6wMVslLQJszzqNtGbSxZCxXPv27VOxXe2F1DDJCOtqsh3rKH700UfK0rpq1Sr1wP3JJ5+AZUD4e9dYHvxpee3cubPqM3nyZFUmo4w87I8ePVplNbWYeriPLEN26OBBZJGXS3zE40MfP1tKGrMXNIcOHIgQl8f2ZYQ8UK5KFm9rOoyarIHy/4C16+zbvGVLpJP9aXnJdVnKgViKUVqBLwGs6UhvVqrF2nXqq1CxolJ7VR6OrbUhIaQ8iWKerdq2VW1Igq+FWuTUidAfRmbfyOZJi7kh1ubAa5WlJijlkmTTtdbGqL/H75y16+zb9lV61kC9DLDmsp1T4kSLy3W6X1vTwe8FJURe3Fm7zmvV5EUMhRl/rbVJLaSws9QQjU7oTVBH/gYsXLgQW+VvDcvFkIQyM+958cYxyCevMRMzswvzJcy8efPwqqyT7risUcvfC+4NYV3TXPKyYd26daDFn2VL4uLGyt9h1qtNjsL/m3pLyI2lsH4riSfr5taqVQt///23soA2atTIsqlNP/P/Q/6/xvv7+++/21S3VhZzBDT5jDlWuqVGIEki4CYPI+W+GYfCb7+L/R8NhP8e09tdLuZFcBAODhsIJ1f3BF/bC6lDRfld3P3SCNHMJynfz8pD28bdezBn+Qp0bd0KM5b8jW+mTlXtCsmDgoe81d915AjuyJv2pfsP4ONx36lrdcRS5CoPIqskKQb15crmjU/efFMeZE1jLFm/Hi5yPZ086PKBxdp5Maeiw/sf4IQ8jNStXBklCxTAdLE+zJY3/8UK5McH8scqsvlkEL0F5CHkdOhDbUkpfJ5T3q7bW27v345lZQrYexi76A96ItZsCxJql4GSmFK62R4TqxYffCl8UKZE9jBcQSxMfIjzEnd7kso5c+aEWRO6CvmkCy0zpnbq1EnpZfIRboz74sMyLZy8zvqMJBF8wCaZo3WBRJQPxiSgZcTaxoQzdM89KCSSGy1CltJAHh5JAg2hJZCSS8hIC5mfpTiHehvw/MtCEDm+pdB6QuH4/2fvPOCjKJ8+PqQTCCEJPaGF3kGkg1IVpUlRwAYWQBQVQV4FsfwVsYEUEZUiIKAIiIJ0ERCQ3pv0TggdkpBGQt6ZJ9njcrnc5ZJc7vbuN/lcbm/32Xnm+T63eztPmUfKaSpBvIamSCEepWAuDznmn9bL/hA7ITEcZMVUxD4RcUjM6fBmx10Tc8flWPG0a15647Rt7Rx5D+aALyKFg4LM5iHHtLl5LbnnUfibSmluIBMpz41w5uzQzpfRI+aOy7lF0xy/evzdCeX7rqnI0jAisjZpZjpkHqFI67ReQvXB6F+58uXVJ1nr1ZwOv7T6kB5Lc8fl5JC0upbvuPR8m4q574JpGu2zNOpozqc0hIhIT+bAgQPV9SDX3VGeOiG9l9I4INdf9erVaezYsWqdVml0Wbx4cTrnc82aNdSUHXkJ3rONe6HFCdVGEGj52vpeMKCQrac4ZfqY6CiLdh04cEAd78e/0507d6Zhw4YZ0mfW0CaNZ6Yi90qpx0P8XCANM2+++SZ14fuIiNyjpHdahlZL0KxBgwbRqFGj1LEV/LsuDWwyqkSueUjeEoDzmbe8kRsIOIyALMdS6onuBudTHM6gWvUppNlDdHrG95SceD8oUV4aWZQfxNZMn0YV2b6GvZ+mQ9wzcejEcWXCNH7wFXmUW6IXjR+nemk2cgtzqWLFaFLaUK0e/LA7+/PPVLoBH/1POYxzly5Tzqfayf/8fH1oKQdVacwPr9IKvpaH74kY71+7fZtyPAtxD8dv476m/NwCHlDAnz6dMpWWbdionM/M7GnGw3cq8ENci7QIiOtnzVQ9syoT/LNIQJwN6cFNMTPc0uKJLnpQeiTlJSIPwl988YV6+M3sYVh6EKS35u+//1ZDLcWxFJGHYYlkKftFOnToQBJcSKRr167q4VocFOn91ER6TSUipgyFlIc5kTY8lFRkIo9GKM8OhfRCiW7pDZLhuqai9f6Z7peeRa130fSY9rkZl9eSVOBGHXllJtLr1Jx7USyJDK21JDLMU16WxFoelZiLvDITGWJoTYcMVbYkJflBW16ZidznrOUh9WeuDjWd4sxb0yHDay2JOLfmHFztHGnAsJaHRFCWV05E+x7L0NpracOuxemZNGmSGl0gjqn0TEs6+W6LiNMiPZuayPVkLDJEXkSGycs1cTWHcRQebvUITf8p9TfPOB89br/c90la//eqTE2XIa+7eX1WGe786quv0tChQ9VwaDkhs4Y2415nSSeNCNJLLRGLZWSHNLR155EDO3fuVOuXSo/q7du3VQOejCARh1Ore2lgkAY20wjIohdifwJwPu3PGDmAgFMRqPzq21SsZSsKrlOX8vEDisjZOdMdZmMDnhMmvZoiNSuEK+czOja15+QEDwsTactrzMnDlEiLtIcy6aEUeSTtAUC2W3OPpfRWXuQeHGNp2bARiYNoKsb7j59JzSuKHzDCH22vksYnJqj3CJ6fImLJHpUgj/8F12lEjX78KY9zzVl2qxvX4kjMieTl50/lXxhIlfq/Qt5G83Rzpl3/Z0vPSSjPx5PhavJwJPP/NEfQ3MOwcrj4AUx6YWQ+lAwjrMQO2nFuxFnK8xvlQUx6LWUxeU2kF1Rb40/bl9m7zJMTkQc5Y5GHOggI6IVANXZepQddnBBpQKlatarqRW/FPcyyb/To0aooMt9Tm6MoPe0yp1MTbb/2WZxN6X2VUQMiWo+xdhzvmRP4/PPPSe4t0ls8ZswY1QMpzqNEH86soc3U+ZS6kR5r6TV94403VO/m+++/r4bwSq+93KN69epFv/zyi9qWzzKMWhoYuvH0HAy7zbx+7H0Ezqe9CUM/CDgRgfK9ejuRNRlN8ciX6mBqRxK4RVMkyGi+lXYs2czwPK0V08Mk+IQH9/CYE+P9ScmpeUnAjid5OI6x1E7rxbBkj3H6vNr24KFvPjy3VE/iV7QUlerSHU5nJpUmw/tkzucRDqwjvZgy11J6LUUyexiWeVPifI4YMUKlkwc76QGQIWeybuHjaeerg1n4Jz1A0iMtPaOF0wJuyby4YjziQEQc14o8LB0CAnoiIENvZc6zBPeRa0ZEnM9x48YpR6gQ30sb8Lxd+e6X4wZRafSRoF+1uIFUtuX7L0M3NZGez5YtWypnR4axy/BbSNYIiKMuEYdlZIY4jzIXcyjPW/+Bl8URMdfQZqpZ66H+6quvSF6aSA+1NrT64YcfVrsD+T4mL4msC3E8gfRPeo63BxaAAAiAgIFA2bQ5VIv4B0oeCOSheOqChRTNwUzKce+QyFJuOdVkcVr4/OrZmMMhw2ZFZPjn8H4v08QRw9VrwvB36fkundUxS/YYz3+7dOWqSo9/GQm0Xr2eqg95G72dGdGk2/P666+rzzIsUOa7GT8M7+PAPD///LPq6ZREmnN6+vRp1fsiw2or8Fw76f0U0R601Qcr/6R3R+ZcSZAQWXZF5h6KSJAheUAUJ1cCGkFAQG8EtOtE7G7HEX9FpEdfW/fyMQ4uJ/dxaVwRJ1WiNct3XXrPxLmJT1svW45XqVKFZN6rNA7JPFsZKm/LHFSVuRv/k2i3ItILOTstKvMV7knWepeloU3mc2qvrWlTZYyRaWll6oCWTt4/+ugjkoYEEW2YrWwbD5vO6tIuch4k9wmg5zP3mUIjCIBALhF4rnMnGjFhIi3ngCi1n+hKsdyLE8FDals2bEAv8QP2EI42KMGEmjzzDEdPSqG9HDBC5K20uZe2mNGah/aWZmdXlllp+syz9AgHkojj/P7leSnPcCv5x68PIkv2hJUoThJ+JZlfLfv2oWLBIbRr4QJbTHCLtB78oAbJSEAL4KIF1ZD5aDJUVlr3JTiHPAz37dvX4PjJPE9tGJr0QsrSAdI7I8MGpcdSHM4JEyaooYaNeDi6iDbUVns3t0+cXumJkIdqcXg/+OAD1SskyxOsXLlS6cnukhLqZPwDAQcRkPmFMiJARLvOpDdMgm6JMyLXlCYSSEjmSMvQWnFaZCi8FsxJ9ss0ELnOZAivHNP0aefj3TIBcezlXiXL4Pz1118qsTQEyLxyue9Y63WWE6TXWUQa4uQeKPUjS+jIHFI5JsvfSKOBNKZt5+V+ZP1WaUgQkfntEiH8rbfeSjevVx3EP7sTgPNpd8TIAARAQAj4SqQ6jt7oJ+8sPj6pToivT2rURNmn/bh7c8uyyGs8N02cwem//2GIJNuGH6TDOHhJvx4cPOnmDRo/Zy7t/e+ISl+kcCA7iW9Q17apQVLM5SEJze2XAEPzObLha9zSvZsDTcjcUREZ8luHW7lFLNkj57/BYf/H/fQTXbt1m9cbDVDn4B8IZIWAzFmS5Ra0B2BxEM+cOZPuoTizh2HRL5EdZYitdv748eNVb6U8FGvzpWU4rjyQaWnkPNN9Ei1SnFx54NbmiUqUT3mAk+GKEpDIeDkP0QEBAb0QMOckSm+n8cgV47JIb6Zpj6bWUCTpxFmC2E6gB6+7K5G5ZXSFiMx1l1Eect+z1NAm7LXnBGkgkIBsck/TlqqR4bzSgCbO53vvvaecTxm1IUG+JLKxzCmVY+u50XrixIkk6xxbkn4cOMnLw8NSErsdO3E8tTHdbhk4UHE+HsqW4sD8dZ+1rGVm3JWfmwWyFnEvN/OCLvcmsKpRHYq/cYUeGP4JFa33gF1gyJBZWatTnDQRufXE8jAmWWZFezjW9skantKqrImcd4aXhQjmZWOKGK1jJ8dlnufZixH8g+SjouBq58i7ps84D0v7tXNlWO95HhYUwPM/Q3mej2afdtySPTe5Ff0aR+Erw8EtlMOtnZSL76f/WETHfp5ORR5sTs1+cZ3eVe172HjGfCrevEUuEoMqEAABEHBOAjLKQJyn3Ix2e/bsaZrHgQSPHT3ikAi6WrTbzNb5lJqQpYRkvdUSPOJIYi2YirleZ4lsK7/rxg0A8mwhwdnEcZXlVtI9O/CyUbKklGnvtDSkSSOcNnTXOG9ZcspaZG7j9Pbelt75W7y8XFZlSZUwXj+XIzev2UoFzSxRlFU99kyHnk970oVuEAABAwFpWc7PL03kB6JA2lpvlvbJMR9u7dQi4mpptXdxDMuXTp2vqe3T3s3lIccy26+dF8DRV6tbiMBqyZ4gnmsiLwgIgAAIgAAIOILA6hVLaOG8OaStgesIG6zlKQ6nzE3PTMz1OhtPGdDOk2cLc+vAynHp6TbXO62tHazpMH6XXtIpaYGPjPc7attcb72jbMmtfOF85hZJ6AEBEAABEAABEAABEAABBxPo98qbtOmfNXQp4qKDLdFf9rK8Vb9+/fRnuI4sdsxAZh0BgqkgAAIgAAIgAAIgAAIgoCcCnl6p8RX0ZDNsdQ8CcD7do55RShAAARAAARAAARBwGwLXr19X6zrm5bIa586do549e9IPP/zgVJwvRVygV/s9Sy8934PeHzGElyDBcmBOVUFuZgycTzercBQXBEAABEAABEAABFyVgKwhKct2SOTTSpUqqQjNq1evzpPiRkRE0Pz582lt2prTeZJpFjKJuRNDB/bvok5detAno79mNkWzcBaSgIB9CMD5tA9XaAUBXREwxJVF8Gtd1FsKR/hVYhQRWBeGw0gQAAEQsDOBd955hzZu3KjWuv2S14KuW7eu6gG1c7ZOq37f3l30you9afy3M+iJ7qnrXDqtsTDMLQjA+XSLakYhQcAyAU//1DDniTaE87asEUftSSCRl3IR8cJaovbEDN0gAAI6JHDgwAFltQSNGTZsGG3YsIFeffVVtU/Wq5V99erVowcffJA++OADSuTlOER+/fVXateunYrAKutByhq4mjzzzDM0ePBg6tOnD1WuXJnOnj1LUbysluyTtA888ADJkimayFIeck7VqlXVObYslaHpyI33U6dO0HO9OlKHTl2p/oONc0MldIBAjgnA+cwxQigAAf0TKFSzripE5Lo1+i+Mi5dAej2vbNmgSlm4Tj0XLy2KBwIgAAK2ERDHUqR3797K0ZS1JDV54YUXaMyYMcqBlH2ffPKJesm2OJLXrl6l5s2bk6z1OHz4cFq1apUcUg7shAkT6KefflLrRooTK46o7BNHUxzYsWPHqrTyT3pely1bpo7JOcaOrCFRHmyEhpWhChWr0PQp39DuXdvyIEdkAQLWCcD5tM4IKUDA5QlU7PeKKuO1owfo9vHjLl9ePRcwYv06iou6QR5e3lT+2ef0XBTYDgIgAAK5TkAcvVatWpE4iJqjKXMwJQCRzMesXr26chSnT5+u8l68eLF637ZtG+3Zu5emTZtGb731ltp38ODBdPaJIxkdHa3mk/7xxx9UiNd0Pn36NEm6BQsWGNJK7+ipU6dI0ojs2bPHcCwvN3x5ncvJU+aQv39BGtT/OXaqLxuyP3P6BJclig4f3EeXL9930A0JsAECdiIA59NOYKEWBPREIKh2bSpcrQ6bnEK7Ph5OUfyjCXE+Ale2baVDUyYow0p16E4+gYHOZyQsAgEQAAEHEpBAQ+JsrlmzRg2vFWdx6JAhdPLkSWXV4cOHqXTp0mouqOyQ4yK//fYbhYaGkg87bDJvVCQpKUm9a/+ee+458vDwMOiSobsFCqROW6lVq5aWTOkODg5WQ3hlZ1xcnOFYXm3Ext5R+ZYsFUYTJv/IjmckDXz5aYpnW9avW02d2jenV17qTRPHfUadHmmi9ueVbcjHvQl4uXfxUXoQAAGNQKPps2lj1w4Ue/k8bR3xJhWr04hCH2lPfvxDzr+2WjK85zGBlKRkusNh8i8uX0rXj6e2wodw3dT79P58pDw2CdmBAAiAgNMSkGi3xYsXpzZt2tDs2bOpZs2adIWH0wYFBSmba3Njq8zv1ET2H+cRP0PYQZVe0Tlz5qhhsx9++KGWJMO79HiKXLt2zXBMc2INO3gjn4OCwn32yQg6uH83xcfH08jhb1Hfl16hQtxYuW/PDureuSUNfecjKlmiFL0++B1q3PQhalinPF24eI4q8hBdCAjYmwCcT3sThn4Q0AkBv6JFqcXvy2jz009S9JmjdHnPFvXSifluY2bRJq2o8dQZ5OHr6zZlRkFBAARAIKsEwsPDqW3btioI0F9//aVOk6VXypcvT+XKlaNDhw4pB1N6KmW7WLFi1LRpU4P6mxzQbcmSJYbP5jYqVqxIYWFhtH//fjX309/fn9atW0czZ840lzzP9w1/fzTJy1h2Hzxv/JHGfP4R5U8LNlgoMIjupgVeSpcIH0DADgTgfNoBKlSCgF4JiAPaavU6urplM538YTLd4pbTpPhYLk6KXouke7ul5dwrf0Eq0qINVeg/gIKq19B9mVAAEAABELAXgR49etC8efMMDmSnTp1o0qRJ5OXlRXPnzqW+ffvSp59+qrL38/NTAYdkOZbu3burobfy3qJFC3Xc09NTvXt7e5OvUYOfDM2dNWuWimgr80AlnQQgkjxEjN/lmKZHHXSSf/d4abWUtOXV5B2/8k5SMW5gBpxPN6hkFBEEbCEgzk6xps3Uy5bzkBYEQAAEQAAEHE1AnMLJkyeTRLktUaKEYU6m2CU9nMeOHaOrPAxXhsnKHE/NqVy4cCEH3rmsHMeQkBBKSEhQ8z/lPDlHc9Tks0jr1q0pIiJCRb8tWLAgFS5cWO2Xoa7irIqULFlSBT5yNudTIt9GXDhHSxcvoIT4OLp65RItX/IbDzu+P29VFQD/QMAOBOB82gEqVIIACIAACIAACIAACDiGgAQBqlChQqaZF+VRPvIyFZkrqonmlMpnrSdTO6a9S2OtDL81FuPzZL/0kjqbPFC/Ee0/ej/CrfG2s9kKe1yPAKKIuF6dokQgAAIgAAIgAAIgAAIgAAIg4HQE0PPpdFUCg0AABEAABEAABEAABNyJwD+8/End6ul7UfVa/hhePxQCApkRgPOZGRnsBwEQAAEQAAEQAAEQAIE8IgCnLY9AIxuHEoDz6VD8yBwEQAAEQAAEQAAEQMBdCUh03ubNm7tk8SXgEwQETAnA+TQlgs8gAAIgAAIgAAIgAAIgkAcEgoKCSF4QEHAXAgg45C41jXKCAAiAAAiAAAiAAAiAAAiAgAMJwPl0IHxkDQIgAAIgAAIgAAIgAAIgAALuQgDOp7vUNMoJAiAAAiAAAiAAAiAAAiAAAg4kAOfTgfCRNQiAAAiAAAiAAAiAAAiAAAi4CwE4n+5S0ygnCIAACIAACIAACIAACIAACDiQAJxPB8JH1iAAAiAAAiAAAiAAAiAAAiDgLgTgfLpLTaOcIAACIAACIAACIAACIAACIOBAAnA+HQgfWYMACIAACIAACIAACIAACICAuxCA8+kuNY1yggAIgAAIgAAIgAAIgAAIgIADCcD5dCB8ZA0CIAACIGCGQEqKmZ3YBQIgAAIgAAIgYIlAig5+P+F8WqpBHAMBEAABEMgzAh5e3iqvpLi4PMsTGYEACIAACICAKxC4l5RElHJPFcXTz89piwTn02mrBoaBAAiAgHsRyF+qtCpw9NEj7lVwlBYEQAAEQAAEckjg9n//KQ3SkOtbpEgOtdnvdDif9mMLzSAAAiAAAjYQCKxTT6W+uWuHDWchKQiAAAiAAAiAwPW0386C5SqRh6en0wKB8+m0VQPDQAAEQMC9CBR76GFV4GvbNlDi7VvuVXiUFgRAAARAAARyQODCgnnq7JDGzXOgxf6nwvm0P2PkAAIgAAIgkAUCxVo8RPmLlKR7SXfpxPRpWTgDSUAABEAABEAABK7v3UO3jx0gypePKrzwklMDgfPp1NUD40AABEDAfQjk4x/N8v1eVQU+OXUiRZ866T6FR0lBAARAAARAIBsE7iUm0t7Bqb+dRRu3pAJlymRDS96dAucz71gjJxAAARAAASsEKvR5gQpVrK56P7e/9ByG31rhhcMgAAIgAALuS0CWVtk9bCjFXDxDnj5+VPfzMU4PA86n01cRDAQBEAAB9yEgQRIenPQDefnmp5gLp2lTt84Ud+Wy+wBASUEABEAABEAgCwSkx3PX4Dfo4vKFKnXN/31J/qVKZeFMxyaB8+lY/sgdBEAABEDAhEBAhYrUeM5vygGNPnec1rZuSmfm/8rLl6WuX2aSHB9BAARAAARAwK0IXN+zm9a2e9jgeFYf/gmV6/GkLhjk4+7aFF1Y6qRG3omJoejoaLtYV6JkSbvohVIQAAEQ0AOBmwcO0M6BL1Ls5QvKXL+gohT25DNUpGkzCqpdh7wLFuTYCvn0UBTYCAIgAAIgAALZJiC9nLePHCFZTkWi2t4+flDpkqG20uOpF8dTjIbzme2vQeqJcD5zCBCngwAIgIAFAvcSEujg56Pp3K8zKfluYvqUyvGE85keCj6BAAiAAAi4HIEUk5E//PsnwYVkjqcehtoa1wecT2Ma2diG85kNaDgFBEAABGwkkMgjTM7O+5kur15JUUcP0N24OzZqQHIQAAEQAAEQ0C8BDy9vCihXmYKbNOflVF6kAqWdO6ptZqThfGZGJov74XxmERSSgQAIgEAuEZDZIok3blBSXCwRZo7kElWoAQEQAAEQcFYCHr6+5BtShCQon97FS+8FgP0gAAIgAALuRUDmefqGhJAvhbhXwVFaEAABEAABENA5AUS71XkFwnwQAAEQAAEQAAEQAAEQAAEQ0AMBOJ96qCXYCAIgAAIgAAIgAAIgAAIgAAI6JwDnU+cVCPNBAARAAARAAARAAARAAARAQA8E4HzqoZZgIwiAAAiAAAiAAAiAAAiAAAjonACcT51XIMwHARAAARAAARAAARAAARAAAT0QgPOph1qCjSAAAiAAAiAAAiAAAiAAAiCgcwJwPnVegTAfBEAABEAABEAABEAABEAABPRAAM6nHmoJNoIACIAACIAACIAACIAACICAzgnA+dR5BcJ8EAABEAABEAABEAABEAABENADATifeqgl2AgCIAACIAACIAACIAACIAACOicA51PnFQjzQQAEQAAEQAAEQAAEQAAEQEAPBOB86qGWYCMIgAAIgAAIgAAIgAAIgAAI6JwAnE+dVyDMBwEQAAEQAAEQAAEQAAEQAAE9EIDzqYdago0gAAIgAAIgAAIgAAIgAAIgoHMCcD51XoEwHwRAAARAAARAAARAAARAAAT0QADOpx5qCTaCAAiAAAiAAAiAAAiAAAiAgM4JeOncfpgPAiBghUBKSoqVFPcP58uX7/4HC1v37t0jW/R6eHhQVnQnJSXZrNfT09OCpamH7t69S2JzVkXs9fb2tpo8MTGRkpOTrabTEoitPj4+2sdM3+Pj423W6+fnl6k+7UBcXBwJ46yKcChQoIDV5LGxsSSMsyqiNyAgwGpy0ZuQkGA2nbnvn+gtXLiw2fTGO+/cuUPC2JyY0yvf3ZCQEHPJ0+2LiYkhYWxOzOmVdMWKFTOXPN0+0Ss2m5PM9BYvXtzqNRcdHU3yskVKlChBwtmSiL23bt2ylCTDsZIlS5K1a1n03rhxI8O5lnaUKlWKvLwsP+qI3mvXrllSk+GY6LV2LYveK1euZDjX0o7Q0FDy9fW1lER9Fy5dumQxjelB0Zs/f37T3ek+y/V24cKFdPusfRC91u4Rck2cPXvWmqp0x0WvtXuEXMOnTp1Kd561D1Jv1u4Rcs85fvy4NVXpjove4ODgdPtMP8g98r///jPdbfGzXBdFixa1mEbu6QcPHrSYxvSgXMfysiTym7l3715LSTIcE53Cwprs3LnTWpJ0x+U+WaZMmXT7zH0Qvbb81gvb8uXLm1OVbp/ozey309w9uEiRIlSpUqV0Osx92LVrl02/cfI7VLVqVXOq0u0TvdZ+i1q0aJHunDz9wNAcLnzBpmT22rx5s1X7li1blhIUFGT2FRgYmGLutWbNGqt6165da/ZcTV+hQoVS+OaY7vXTrFkplyIiLL7+XLIkxd/f3+rLWPevv/5q1d49e/ZY1Wma74wZM6zqPXr0qM16J02aZFUv/xil8AOzTa+vvvrKql7+sU/hhwKbXh999JFVvfwgkcIOiU2vYcOGWdXLN0rxDm16vfrqq1b1SgJ+2LBJb58+fbKkV64BW2x+6qmnsqSXf7Rs0tuxY8cs6eUfAZv0tm7dOkt6a9eubZPexo0bZ0mvpLOFb906dbKkt1WrVjbpFW5ZkQ4dOtikNywsLCtqU5588kmb9MrvQFZEvue28JXrKCsycOBAm/SKDXL9W5MhQ4bYrFfuV9Zk5MiRNuuV+6s1GT16tM16z5w5Y01tyvjx423We/jwYat6f/jhB5v18sOdVb2zZ8+2We/GjRut6v3tt99s1rtq1SqrelesWGGz3t9//92q3n/++cdmvQMGDEiR5xNL34vt27fbrHfatGlW7WVHzma9EydOtKqXHWWb9X7++edW9UZGRtqs94MPPrCq9/bt2zbrzcozDzvhNut97bXXrNorCbjhxibdL7zwQpb02vrM07Nnzyzp5cYFm+zt3LlzlvSGh4db1ZslRXZKZLk5kH8Z80IstWRmpTVdeh9u3rxpk6lZ0Stp+OLLfb3cSiUtjLZIVuyVHhhn0ZtZC5Fxmfk7nWnvg3E64+2s6pXvhC2Sld4rsTcr9WCcb1Za4ESvrZKdc2zNA+ldg4C02J44cUIVxtZeLtcggFKAAAjojQA3CJC85s+fT2XLltWb+bAXBEDAAgGncD4t2IdDIAACIAACOSAwZcoUmjp1qtJQunTpHGjCqSAAAiBgOwEZtm5tSLWx1qw0Bkt6Gf5tbeizsV7ZzoodYq+1oc/Z1Wtt6LOpXmtDxiW92Muj2kxPtfg5K9yEr7Uh1aaZZEWv2GttSLWp3qxy4xGJZEvnQ1a58agaq9MNjG0uWLCg8cdMt2Woti2dGlK+rIgM+3XmxuZ83INie7dLVkpuQxoeWptp6po1a5I12NLryUNDM9Vh7kC1atWIu9HNHTLsi4qKoiNHjhg+m9uI4x5M495GGTtubT6BzN05ntYTYU6ntk++PJpwF7rV+QRiR2b2ysVuTmQMvbW5TDL/IbN5Cpnplfkaxvaby1suOFv1ynwCa/Mf5IcrMw7m7JB9Mp/Aml65VGzVK2yzMqfr2LFjmZlmdr98x7KiV3q8bLnE5VqTuWLW5PTp0zbNq5AbcVb0nj9/PtN5FeZskh+OrOiNiIiw6QYvP3RZ4cvDnWzSKw80WdF79epVm35A5Qc/s+9v//79Dc4nD9cmHmppDqXZffLgk5le4xNkfl9mczON02nb8gBo7f4gaeUebIteeViydj8TvTIXzxa9cp+zNp9L9Mo92Ba9co481FgTmdtmq15rv2+SpzykmdOb2X1dzsnKQ5Xc2809AFrSK9ecpeOSt4x8MfegZuk8ueYsHRe9MkLFVr0yJ9yaXrn3ZtWREjtEsuJsSLqsjKqRdJpYm6erpXOG9zp16tD+/fuVKdLzycPvncEs2AACIJBLBJzC+cylsjhEzR1+iLFX60IJnmgOAQEQAIGcEDB2PocOHUpjxozJiTqcCwIgAAJ2JQDn0654oRwEHE7Acsg6h5sHA0AABEAABEAABEAABEAABEAABFyBAJxPV6hFlAEEQAAEQAAEQAAEQAAEQAAEnJwAnE8nryCYBwIgAAIgAAIgAAIgAAIgAAKuQADOpyvUIsoAAiAAAiAAAiAAAiAAAiAAAk5OAEutOHkFwTzXIXAv5R7tun6ULt25Ron3klynYDosia+nN4UWKEb1gitZjVipw+LBZCclcI+v+5sXN1P8nUi6l5zgpFa6h1le3gXIv3A4FSpaB/cA96hypyrlPY4Iff7PJXTl778o8fYtCV/sVPa5mzFehQKpSNNmVPapnuTll9/dip/n5YXzmefIkaG7EYhPTqT5p9bQan5F37nibsV36vIGBpSkx8LbUfdyrciHHVIICNiDQFJCNJ09MJ0uHPyd4mMS7ZEFdGaTQECRYCpT5xkKrdKL8vFSPRAQsDeB41N/oBOTx1NiDDudEKchEPn3Ujry5cdU5ukXqea7I3A/sGPNwPm0I1yoBoH4pEQauvlLunAtdb1YT08vCipYirz5PR//QfKeQAql0F1uELgRfYlu82vevp9oe+Q++qLxYDigeV8dLp/j3fhbtPOPZyjq2g1VVi9vTwoICiQPbx+XL7vTFpDX37ybGE/R16Momuvl0N/f0I0LW6lWm/F44HSCSmvUqJFhHeCsrOPsBCZn2YSDn4+mk9O/Uem9fHypRMOHKH+pUkS89jHEMQTy8drw8by2duTWfygx7g6dmvEtJUReovrjv8H9wE5VgnU+cwgW63zmEKALn56YfJfe+vcL5XiK09mqfCtqH1qfCnj5uXCp9VO0qMQ4Whmxk/45vU4t2B5evA6NaTKEPD1c6yEgMjKSbt68qSomJCSEihUrpp9K0rml0uO54/deyvEUp7PSgx2oRHgb8vTy1XnJXMP8xLgbdP7In3R6/xZVoJJVGlCtthMwDNc1qtfpSnHs++/ov7EfK7vKtOlIlfu+SJ6+uBc4S0XdS0qiM38souPzZ6XWUc++VG/UZ85inkvZAeczh9UJ5zOHAF349EVn/qFZu6eSBzsz/eo8S3WDy7twafVbtO1Xj9Gsg/OUA/pqw9fp0bBG+i0MLHcqAqd3TaBjW+aROJ712w+mAJ5jDHE+ApdOraVDG+crwxp0HUvBoU2dz0hYpGsC9xITaWWDmnQ3NprKtO1E1fq/ouvyuLLxZ5b8QUfnTKV83Gnw6L97yZcbbSG5SwATHHKXJ7SBgCKQwsO6/jy5Sm03LdsCjqcTfy8aFq1M9dIczj/S6syJzYVpOiGQwgFEzh1YpKytWP9xOJ5OXG8lw1tTyfDKysJz+2c4saUwTa8Ezi3+Qzme3r5+qsdTr+VwB7vLdupCBYKLUUpyEp2chfuBPeoczqc9qEKn2xM4EXWRbtw+p+YLPBb6oNvzcHYAHcIaKhMjrh+ji7HXnN1c2KcDAjciOKotBxfy9PKgkhXa6sBi9zaxTPVOCsDlU4coKTHGvWGg9LlOIHLVCqWzRJNW5OmD+d65DjgXFebLl49KtX1MabyyJrUTIRfVQxUTgPOJrwEI2IHAlfjUOXb+fkEU7FvQDjlAZW4SKJk/iHx8Uuvpalxq3eWmfuhyPwIJvJyKSEEOLoQ5ns5f/wHBFVPnevKolcR4NEA5f43py8KkqNvKYBVcSF+mu6W1/qFhqtxJ0an15pYQ7FhoOJ92hAvV7ksg8d5dVXgvDwSU1su3wCttqRWt7vRiN+x0TgLaOp6e3ljCxzlryMQq7u3w8EyNQJ6chDVYTejgY04J3EtWGjy88EyQU5R5cb5nWj3J9AlI7hOA85n7TKERBEAABEAABEAABEAABEAABEDAhACcTxMg+AgCIAACIAACIAACIAACIAACIJD7BOB85j5TaAQBEAABEAABEAABEAABEAABEDAhgMHnJkDwEQRAAARcicCmTZvoyJEjqki1a9emhg1TI/u6UhlRFhAAAdch0LNnTzp29Kgq0JdffUXt2rVzncKhJCAAAgTnE18CEAABEHBhAj/99BNNnTpVlXDo0KFwPl24rlE0EHAFAtJYtn//flWUW7duuUKRUAYQAAEjAhh2awQDmyAAAiAAAiDgDgSSkpLorf/7kga99Sldu3Yj0yLv3HWQVq3eRPfSonVmmhAHQAAEQAAEQCALBOB8ZgESkoCA3ghERcXQRyNG02Mtu1KlUnWofvXm9Pbr79HlyKtOW5SEhERKTExdosZpjYRhIOAAAnJtPNSuD1Wr15nqN32Kzp2/lGMrYu7E0Tff/0zfT19A5y9cNqvv1KkL1LjVs9ShxyBav3Gn2TTYCQIg4BwE2g8YQHW6dadl69c7h0F2sCIhMZHucsMZRN8E4Hzqu/5gPQhkIHD+7AVq16wjTZ08g/bvPUixsbEUGXGZfpk9n6Z9PzNDemfYcXDfYQovXoOqhNUledCGgCcJHwUAAEAASURBVAAI3Cew8q9NtHnbPjp+8hztO3iMfv512f2DdtwqHFiQChQoQEGFA6hoSJAdc4JqEACBnBLYc/g/OnbmDJ2/fCWnqpzy/H08D7hw4yZUtHkLEicUol8CcD71W3ewHATMEvjk/S/owvmL5M2L238wajjtPLyJVqz7nbp078APkv7pzpHhdju27aazp89TssliylevXqdbN29RUlIy7dy+h86dOa/OzWy/pvjC+QiV/tYN83N1oqNjaO/u/XTk8HFKSUlR+m9wPiKJ/INy4/pNiom5o6nDOwi4PYF5C1YoBp5pJH6en/pZAyMNNhcvXlZDY2V47J69/9F/R05qh+nmjdu0eesekms3Mzl7LoK2sYMbFxdvSBIcUphOHVxGB3Ysolo1Kxv234mJpb37UvO4dSuK5KU1GkkeV0zyuXzlmtmhvXL/2bJ1L505c5FMF3PPqp5Ll67Qv1v20CG+n/ANxWAjNkDA3QmIg3bh8mVKTk7me8M9EuftxLlz6nfXlE30nTu06+AhOnzyZIbj0tMo+3cfOkzRMTHpTjXOI+LKFdq8dy8l3r2rnEMtb+P92snnIyNp6779dOP2bW1XundTe6QM19Pm/0qesh3DDesQfRJAwCF91husBgGzBMTxW7ZkpTrW56WnacCgF9V2yVLFafL08YZzTp86R4NfeVs5idrOsNKhNG7yF9S0RSOSYbt1KzWmIiHBVKl6ZdqycSvVqF2NFi792ez+1RuW0DF+2H3t5cF0+GBqZFXR2+2pLvTVxE/Jz89X/aB9/vFY+mHSj3SXf5xEKletSC8OeJ7efesD9Vn+PchDhAODCtPh0zsM+7ABAu5KQBy95av+VcX/9OM36d0PJtBhvtYOHTpONWpUUvvf+2gijf92Dr3cpxutWL2RLl5KHV7f8bGH6NG2zeidkeMolp1KcV5Fx9uDX0iH81We97mD53aK+Pr40LTJH1LvpzpQNN8Hipdvqfbv3vwr1a5ZhcZ98xN9OGqy0qcOpP3r3qUtTRz7LoVWbKv2HNu7lMLDw+jwfyeodqMeat/******************************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"}}, "cell_type": "markdown", "id": "8889a307-fa3f-4d38-9127-d41e4686ae47", "metadata": {}, "source": ["# CRAG\n", "\n", "Corrective-RAG is a recent paper that introduces an interesting approach for active RAG. \n", "\n", "The framework grades retrieved documents relative to the question:\n", "\n", "1. Correct documents -\n", "\n", "* If at least one document exceeds the threshold for relevance, then it proceeds to generation\n", "* Before generation, it performns knowledge refinement\n", "* This paritions the document into \"knowledge strips\"\n", "* It grades each strip, and filters our irrelevant ones                                           \n", "\n", "2. Ambiguous or incorrect documents -\n", "\n", "* If all documents fall below the relevance threshold or if the grader is unsure, then the framework seeks an additional datasource\n", "* It will use web search to supplement retrieval\n", "* The diagrams in the paper also suggest that query re-writing is used here \n", "\n", "![Screenshot 2024-02-04 at 2.50.32 PM.png](attachment:5bfa38a2-78a1-4e99-80a2-d98c8a440ea2.png)\n", "\n", "Paper -\n", "\n", "https://arxiv.org/pdf/2401.15884.pdf\n", "\n", "---\n", "\n", "Let's implement this from scratch using [LangGraph](https://python.langchain.com/docs/langgraph).\n", "\n", "We can make some simplifications:\n", "\n", "* Let's skip the knowledge refinement phase as a first pass. This can be added back as a node, if desired. \n", "* If *any* document is irrelevant, let's opt to supplement retrieval with web search. \n", "* We'll use [Tavily Search](https://python.langchain.com/docs/integrations/tools/tavily_search) for web search.\n", "* Let's use query re-writing to optimize the query for web search.\n", "\n", "Set the `TAVILY_API_KEY`."]}, {"cell_type": "markdown", "id": "a21f32d2-92ce-4995-b309-99347bafe3be", "metadata": {}, "source": ["## Retriever\n", " \n", "Let's index 3 blog posts."]}, {"cell_type": "code", "execution_count": null, "id": "3a566a30-cf0e-4330-ad4d-9bf994bdfa86", "metadata": {}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_chroma import Chroma\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n", "]\n", "\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=250, chunk_overlap=0\n", ")\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Add to vectorDB\n", "vectorstore = Chroma.from_documents(\n", "    documents=doc_splits,\n", "    collection_name=\"rag-chroma\",\n", "    embedding=OpenAIEmbeddings(),\n", ")\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "87194a1b-535a-4593-ab95-5736fae176d1", "metadata": {}, "source": ["## State\n", " \n", "We will define a graph.\n", "\n", "Our state will be a `dict`.\n", "\n", "We can access this from any graph node as `state['keys']`."]}, {"cell_type": "code", "execution_count": null, "id": "94b3945f-ef0f-458d-a443-f763903550b0", "metadata": {}, "outputs": [], "source": ["from typing import Dict, TypedDict\n", "\n", "from langchain_core.messages import BaseMessage\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of an agent in the conversation.\n", "\n", "    Attributes:\n", "        keys: A dictionary where each key is a string and the value is expected to be a list or another structure\n", "              that supports addition with `operator.add`. This could be used, for instance, to accumulate messages\n", "              or other pieces of data throughout the graph.\n", "    \"\"\"\n", "\n", "    keys: Dict[str, any]"]}, {"attachments": {"3b65f495-5fc4-497b-83e2-73844a97f6cc.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "f81239f2-314d-41fe-9af9-d19b5b193b53", "metadata": {}, "source": ["## Nodes and Edges\n", "\n", "Each `node` will simply modify the `state`.\n", "\n", "Each `edge` will choose which `node` to call next.\n", "\n", "It will follow the graph diagram shown above.\n", "\n", "![Screenshot 2024-02-04 at 1.32.52 PM.png](attachment:3b65f495-5fc4-497b-83e2-73844a97f6cc.png)"]}, {"cell_type": "code", "execution_count": null, "id": "efd639c5-82e2-45e6-a94a-6a4039646ef5", "metadata": {}, "outputs": [], "source": ["import json\n", "import operator\n", "from typing import Annotated, Sequence, TypedDict\n", "\n", "from langchain import hub\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.output_parsers.openai_tools import PydanticToolsParser\n", "from langchain.prompts import PromptTemplate\n", "from langchain.schema import Document\n", "from langchain_chroma import Chroma\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "from langchain_core.messages import BaseMessage, FunctionMessage\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.utils.function_calling import convert_to_openai_tool\n", "from langchain_openai import ChatOpenAI, OpenAIEmbeddings\n", "from langgraph.prebuilt import ToolInvocation\n", "\n", "### Nodes ###\n", "\n", "\n", "def retrieve(state):\n", "    \"\"\"\n", "    Retrieve documents\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New key added to state, documents, that contains documents.\n", "    \"\"\"\n", "    print(\"---RETRIEVE---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = retriever.invoke(question)\n", "    return {\"keys\": {\"documents\": documents, \"question\": question}}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New key added to state, generation, that contains generation.\n", "    \"\"\"\n", "    print(\"---GENERATE---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "\n", "    # Prompt\n", "    prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "    # LLM\n", "    llm = ChatOpenAI(model=\"gpt-3.5-turbo\", temperature=0, streaming=True)\n", "\n", "    # Post-processing\n", "    def format_docs(docs):\n", "        return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "    # Chain\n", "    rag_chain = prompt | llm | StrOutputParser()\n", "\n", "    # Run\n", "    generation = rag_chain.invoke({\"context\": documents, \"question\": question})\n", "    return {\n", "        \"keys\": {\"documents\": documents, \"question\": question, \"generation\": generation}\n", "    }\n", "\n", "\n", "def grade_documents(state):\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New key added to state, filtered_documents, that contains relevant documents.\n", "    \"\"\"\n", "\n", "    print(\"---<PERSON><PERSON><PERSON> RELEVANCE---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "\n", "    # Data model\n", "    class grade(BaseModel):\n", "        \"\"\"Binary score for relevance check.\"\"\"\n", "\n", "        binary_score: str = Field(description=\"Relevance score 'yes' or 'no'\")\n", "\n", "    # LLM\n", "    model = ChatOpenAI(temperature=0, model=\"gpt-4-0125-preview\", streaming=True)\n", "\n", "    # Tool\n", "    grade_tool_oai = convert_to_openai_tool(grade)\n", "\n", "    # LLM with tool and enforce invocation\n", "    llm_with_tool = model.bind(\n", "        tools=[convert_to_openai_tool(grade_tool_oai)],\n", "        tool_choice={\"type\": \"function\", \"function\": {\"name\": \"grade\"}},\n", "    )\n", "\n", "    # Parser\n", "    parser_tool = PydanticToolsParser(tools=[grade])\n", "\n", "    # Prompt\n", "    prompt = PromptTemplate(\n", "        template=\"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n \n", "        Here is the retrieved document: \\n\\n {context} \\n\\n\n", "        Here is the user question: {question} \\n\n", "        If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \\n\n", "        Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\"\",\n", "        input_variables=[\"context\", \"question\"],\n", "    )\n", "\n", "    # Chain\n", "    chain = prompt | llm_with_tool | parser_tool\n", "\n", "    # Score\n", "    filtered_docs = []\n", "    search = \"No\"  # <PERSON><PERSON><PERSON> do not opt for web search to supplement retrieval\n", "    for d in documents:\n", "        score = chain.invoke({\"question\": question, \"context\": d.page_content})\n", "        grade = score[0].binary_score\n", "        if grade == \"yes\":\n", "            print(\"---GRADE: DOCUMENT RELEVANT---\")\n", "            filtered_docs.append(d)\n", "        else:\n", "            print(\"---GRADE: DOCUMENT NOT RELEVANT---\")\n", "            search = \"Yes\"  # Perform web search\n", "            continue\n", "\n", "    return {\n", "        \"keys\": {\n", "            \"documents\": filtered_docs,\n", "            \"question\": question,\n", "            \"run_web_search\": search,\n", "        }\n", "    }\n", "\n", "\n", "def transform_query(state):\n", "    \"\"\"\n", "    Transform the query to produce a better question.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New value saved to question.\n", "    \"\"\"\n", "\n", "    print(\"---TRANSFORM QUERY---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "\n", "    # Create a prompt template with format instructions and the query\n", "    prompt = PromptTemplate(\n", "        template=\"\"\"You are generating questions that is well optimized for retrieval. \\n \n", "        Look at the input and try to reason about the underlying sematic intent / meaning. \\n \n", "        Here is the initial question:\n", "        \\n ------- \\n\n", "        {question} \n", "        \\n ------- \\n\n", "        Formulate an improved question: \"\"\",\n", "        input_variables=[\"question\"],\n", "    )\n", "\n", "    # Grader\n", "    model = ChatOpenAI(temperature=0, model=\"gpt-4-0125-preview\", streaming=True)\n", "\n", "    # Prompt\n", "    chain = prompt | model | StrOutputParser()\n", "    better_question = chain.invoke({\"question\": question})\n", "\n", "    return {\"keys\": {\"documents\": documents, \"question\": better_question}}\n", "\n", "\n", "def web_search(state):\n", "    \"\"\"\n", "    Web search using <PERSON><PERSON>.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        state (dict): Web results appended to documents.\n", "    \"\"\"\n", "\n", "    print(\"---WEB SEARCH---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "\n", "    tool = TavilySearchResults()\n", "    docs = tool.invoke({\"query\": question})\n", "    web_results = \"\\n\".join([d[\"content\"] for d in docs])\n", "    web_results = Document(page_content=web_results)\n", "    documents.append(web_results)\n", "\n", "    return {\"keys\": {\"documents\": documents, \"question\": question}}\n", "\n", "\n", "### Edges\n", "\n", "\n", "def decide_to_generate(state):\n", "    \"\"\"\n", "    Determines whether to generate an answer, or re-generate a question.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New key added to state, filtered_documents, that contains relevant documents.\n", "    \"\"\"\n", "\n", "    print(\"---DECIDE TO GENERATE---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    filtered_documents = state_dict[\"documents\"]\n", "    search = state_dict[\"run_web_search\"]\n", "\n", "    if search == \"Yes\":\n", "        # All documents have been filtered check_relevance\n", "        # We will re-generate a new query\n", "        print(\"---DECISION: TRANSFORM QUERY and RUN WEB SEARCH---\")\n", "        return \"transform_query\"\n", "    else:\n", "        # We have relevant documents, so generate answer\n", "        print(\"---DECISION: GENERATE---\")\n", "        return \"generate\""]}, {"cell_type": "code", "execution_count": null, "id": "dedae17a-98c6-474d-90a7-9234b7c8cea0", "metadata": {}, "outputs": [], "source": ["import pprint\n", "\n", "from langgraph.graph import END, StateGraph\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # generatae\n", "workflow.add_node(\"transform_query\", transform_query)  # transform_query\n", "workflow.add_node(\"web_search\", web_search)  # web search\n", "\n", "# Build graph\n", "workflow.set_entry_point(\"retrieve\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\n", "        \"transform_query\": \"transform_query\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_edge(\"transform_query\", \"web_search\")\n", "workflow.add_edge(\"web_search\", \"generate\")\n", "workflow.add_edge(\"generate\", END)\n", "\n", "# Compile\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": null, "id": "f5b7c2fe-1fc7-4b76-bf93-ba701a40aa6b", "metadata": {}, "outputs": [], "source": ["# Run\n", "inputs = {\"keys\": {\"question\": \"Explain how the different types of agent memory work?\"}}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        pprint.pprint(f\"Output from node '{key}':\")\n", "        pprint.pprint(\"---\")\n", "        pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint.pprint(\"\\n---\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "2bee03de-a32c-4bbe-b37a-a13bb825e4cb", "metadata": {}, "outputs": [], "source": ["# Correction for question not present in context\n", "inputs = {\"keys\": {\"question\": \"What is the approach taken in the AlphaCodium paper?\"}}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        pprint.pprint(f\"Output from node '{key}':\")\n", "        pprint.pprint(\"---\")\n", "        pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint.pprint(\"\\n---\\n\")"]}, {"cell_type": "markdown", "id": "a7e44593-1959-4abf-8405-5e23aa9398f5", "metadata": {}, "source": ["Traces -\n", " \n", "[Trace](https://smith.langchain.com/public/7e0b9569-abfe-4337-b34b-842b1f93df63/r) and [<PERSON>](https://smith.langchain.com/public/b40c5813-7caf-4cc8-b279-ee66060b2040/r)"]}, {"cell_type": "code", "execution_count": null, "id": "69eddb3e-57f4-4eea-8e40-4822fc50c729", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}