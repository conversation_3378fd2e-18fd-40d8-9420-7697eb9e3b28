{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Learned Prompt Variable Injection via RL\n", "\n", "LLM prompts can be enhanced by injecting specific terms into template sentences. Selecting the right terms is crucial for obtaining high-quality responses. This notebook introduces automated prompt engineering through term injection using Reinforcement Learning with VowpalWabbit.\n", "\n", "The rl_chain (reinforcement learning chain) provides a way to automatically determine the best terms to inject without the need for fine-tuning the underlying foundational model.\n", "\n", "For illustration, consider the scenario of a meal delivery service. We use Lang<PERSON>hain to ask customers, like <PERSON>, about their dietary preferences and recommend suitable meals from our extensive menu. The rl_chain selects a meal based on user preferences, injects it into a prompt template, and forwards the prompt to an LLM. The LLM's response, which is a personalized recommendation, is then returned to the user.\n", "\n", "The example laid out below is a toy example to demonstrate the applicability of the concept. Advanced options and explanations are provided at the end."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install necessary packages\n", "# ! pip install langchain langchain-experimental matplotlib vowpal_wabbit_next sentence-transformers pandas"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# four meals defined, some vegetarian some not\n", "\n", "meals = [\n", "    \"Beef Enchiladas with Feta cheese. Mexican-Greek fusion\",\n", "    \"Chicken Flatbreads with red sauce. Italian-Mexican fusion\",\n", "    \"Veggie sweet potato quesadillas with vegan cheese\",\n", "    \"One-Pan <PERSON> bake with peppers and onions\",\n", "]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# pick and configure the LLM of your choice\n", "\n", "from langchain_openai import OpenAI\n", "\n", "llm = OpenAI(model=\"gpt-3.5-turbo-instruct\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Initialize the RL chain with provided defaults\n", "\n", "The prompt template which will be used to query the LLM needs to be defined.\n", "It can be anything, but here `{meal}` is being used and is going to be replaced by one of the meals above, the RL chain will try to pick and inject the best meal\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain.prompts import PromptTemplate\n", "\n", "# here I am using the variable meal which will be replaced by one of the meals above\n", "# and some variables like user, preference, and text_to_personalize which I will provide at chain run time\n", "\n", "PROMPT_TEMPLATE = \"\"\"Here is the description of a meal: \"{meal}\".\n", "\n", "Embed the meal into the given text: \"{text_to_personalize}\".\n", "\n", "Prepend a personalized message including the user's name \"{user}\" \n", "    and their preference \"{preference}\".\n", "\n", "Make it sound good.\n", "\"\"\"\n", "\n", "PROMPT = PromptTemplate(\n", "    input_variables=[\"meal\", \"text_to_personalize\", \"user\", \"preference\"],\n", "    template=PROMPT_TEMPLATE,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next the RL chain's PickBest chain is being initialized. We must provide the llm of choice and the defined prompt. As the name indicates, the chain's goal is to Pick the Best of the meals that will be provided, based on some criteria. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import langchain_experimental.rl_chain as rl_chain\n", "\n", "chain = rl_chain.PickBest.from_llm(llm=llm, prompt=PROMPT)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once the chain is setup I am going to call it with the meals I want to be selected from, and some context based on which the chain will select a meal."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["response = chain.run(\n", "    meal=rl_chain.ToSelectFrom(meals),\n", "    user=rl_chain.BasedOn(\"<PERSON>\"),\n", "    preference=rl_chain.BasedOn([\"Vegetarian\", \"regular dairy is ok\"]),\n", "    text_to_personalize=\"This is the weeks specialty dish, our master chefs \\\n", "        believe you will love it!\",\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hey <PERSON>! We've got a special treat for you this week - our master chefs have cooked up a delicious One-Pan Tortelonni Bake with peppers and onions, perfect for any Vegetarian who is ok with regular dairy! We know you'll love it!\n"]}], "source": ["print(response[\"response\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## What is the chain doing\n", "\n", "Here's a step-by-step breakdown of the RL chain's operations:\n", "\n", "1. Accept the list of meals.\n", "2. Consider the user and their dietary preferences.\n", "3. Based on this context, select an appropriate meal.\n", "4. Automatically evaluate the appropriateness of the meal choice.\n", "5. Inject the selected meal into the prompt and submit it to the LLM.\n", "6. Return the LLM's response to the user.\n", "\n", "Technically, the chain achieves this by employing a contextual bandit reinforcement learning model, specifically utilizing the [VowpalWabbit](https://github.com/VowpalWabbit/vowpal_wabbit) ML library.\n", "\n", "Initially, since the RL model is untrained, it might opt for random selections that don't necessarily align with a user's preferences. However, as it gains more exposure to the user's choices and feedback, it should start to make better selections (or quickly learn a good one and just pick that!).\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hey <PERSON>! We know you love vegetarian dishes and that regular dairy is ok, so this week's specialty dish is perfect for you! Our master chefs have created a delicious Chicken Flatbread with red sauce - a unique Italian-Mexican fusion that we know you'll love. Enjoy!\n", "\n", "Hey Tom, this week's specialty dish is a delicious Mexican-Greek fusion of Beef Enchiladas with Feta cheese to suit your preference of 'Vegetarian' with 'regular dairy is ok'. Our master chefs believe you will love it!\n", "\n", "Hey <PERSON>! Our master chefs have cooked up something special this week - a Mexican-Greek fusion of Beef Enchiladas with Feta cheese - and we know you'll love it as a vegetarian-friendly option with regular dairy included. Enjoy!\n", "\n", "Hey <PERSON>! We've got the perfect meal for you this week - our delicious veggie sweet potato quesadillas with vegan cheese, made with the freshest ingredients. Even if you usually opt for regular dairy, we think you'll love this vegetarian dish!\n", "\n", "Hey <PERSON>! Our master chefs have outdone themselves this week with a special dish just for you - Chicken Flatbreads with red sauce. It's an Italian-Mexican fusion that's sure to tantalize your taste buds, and it's totally vegetarian friendly with regular dairy is ok. Enjoy!\n", "\n"]}], "source": ["for _ in range(5):\n", "    try:\n", "        response = chain.run(\n", "            meal=rl_chain.ToSelectFrom(meals),\n", "            user=rl_chain.BasedOn(\"<PERSON>\"),\n", "            preference=rl_chain.BasedOn([\"Vegetarian\", \"regular dairy is ok\"]),\n", "            text_to_personalize=\"This is the weeks specialty dish, our master chefs believe you will love it!\",\n", "        )\n", "    except Exception as e:\n", "        print(e)\n", "    print(response[\"response\"])\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How is the chain learning\n", "\n", "It's important to note that while the RL model can make sophisticated selections, it doesn't inherently recognize concepts like \"vegetarian\" or understand that \"beef enchiladas\" aren't vegetarian-friendly. Instead, it leverages the LLM to ground its choices in common sense.\n", "\n", "The way the chain is learning that <PERSON> prefers vegetarian meals is via an AutoSelectionScorer that is built into the chain. The scorer will call the LLM again and ask it to evaluate the selection (`ToSelectFrom`) using the information wrapped in (`BasedOn`).\n", "\n", "You can set `set_debug(True)` if you want to see the details of the auto-scorer, but you can also define the scoring prompt yourself."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["scoring_criteria_template = (\n", "    \"Given {preference} rank how good or bad this selection is {meal}\"\n", ")\n", "\n", "chain = rl_chain.PickBest.from_llm(\n", "    llm=llm,\n", "    prompt=PROMPT,\n", "    selection_scorer=rl_chain.AutoSelectionScorer(\n", "        llm=llm, scoring_criteria_template_str=scoring_criteria_template\n", "    ),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you want to examine the score and other selection metadata you can by examining the metadata object returned by the chain"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hey <PERSON>, this week's meal is something special! Our chefs have prepared a delicious One-Pan Tortelonni Bake with peppers and onions - vegetarian friendly and made with regular dairy, so you can enjoy it without worry. We know you'll love it!\n", "selected index: 3, score: 0.5\n"]}], "source": ["response = chain.run(\n", "    meal=rl_chain.ToSelectFrom(meals),\n", "    user=rl_chain.BasedOn(\"<PERSON>\"),\n", "    preference=rl_chain.BasedOn([\"Vegetarian\", \"regular dairy is ok\"]),\n", "    text_to_personalize=\"This is the weeks specialty dish, our master chefs believe you will love it!\",\n", ")\n", "print(response[\"response\"])\n", "selection_metadata = response[\"selection_metadata\"]\n", "print(\n", "    f\"selected index: {selection_metadata.selected.index}, score: {selection_metadata.selected.score}\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In a more realistic scenario it is likely that you have a well defined scoring function for what was selected. For example, you might be doing few-shot prompting and want to select prompt examples for a natural language to sql translation task. In that case the scorer could be: did the sql that was generated run in an sql engine? In that case you want to plugin a scoring function. In the example below I will just check if the meal picked was vegetarian or not."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["class CustomSelectionScorer(rl_chain.SelectionScorer):\n", "    def score_response(\n", "        self, inputs, llm_response: str, event: rl_chain.PickBestEvent\n", "    ) -> float:\n", "        print(event.based_on)\n", "        print(event.to_select_from)\n", "\n", "        # you can build a complex scoring function here\n", "        # it is preferable that the score ranges between 0 and 1 but it is not enforced\n", "\n", "        selected_meal = event.to_select_from[\"meal\"][event.selected.index]\n", "        print(f\"selected meal: {selected_meal}\")\n", "\n", "        if \"<PERSON>\" in event.based_on[\"user\"]:\n", "            if \"Vegetarian\" in event.based_on[\"preference\"]:\n", "                if \"Chicken\" in selected_meal or \"Beef\" in selected_meal:\n", "                    return 0.0\n", "                else:\n", "                    return 1.0\n", "            else:\n", "                if \"Chicken\" in selected_meal or \"Beef\" in selected_meal:\n", "                    return 1.0\n", "                else:\n", "                    return 0.0\n", "        else:\n", "            raise NotImplementedError(\"I don't know how to score this user\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["chain = rl_chain.PickBest.from_llm(\n", "    llm=llm,\n", "    prompt=PROMPT,\n", "    selection_scorer=CustomSelectionScorer(),\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'user': ['<PERSON>'], 'preference': ['Vegetarian', 'regular dairy is ok']}\n", "{'meal': ['Beef Enchiladas with Feta cheese. Mexican-Greek fusion', 'Chicken Flatbreads with red sauce. Italian-Mexican fusion', 'Veggie sweet potato quesadillas with vegan cheese', 'One-Pan Tortelonni bake with peppers and onions']}\n", "selected meal: Veggie sweet potato quesadillas with vegan cheese\n"]}], "source": ["response = chain.run(\n", "    meal=rl_chain.ToSelectFrom(meals),\n", "    user=rl_chain.BasedOn(\"<PERSON>\"),\n", "    preference=rl_chain.BasedOn([\"Vegetarian\", \"regular dairy is ok\"]),\n", "    text_to_personalize=\"This is the weeks specialty dish, our master chefs believe you will love it!\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How can I track the chains progress\n", "\n", "You can track the chains progress by using the metrics mechanism provided. I am going to expand the users to <PERSON> and <PERSON>, and extend the scoring function. I am going to initialize two chains, one with the default learning policy and one with a built-in random policy (i.e. selects a meal randomly), and plot their scoring progress."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["class CustomSelectionScorer(rl_chain.SelectionScorer):\n", "    def score_preference(self, preference, selected_meal):\n", "        if \"Vegetarian\" in preference:\n", "            if \"Chicken\" in selected_meal or \"Beef\" in selected_meal:\n", "                return 0.0\n", "            else:\n", "                return 1.0\n", "        else:\n", "            if \"Chicken\" in selected_meal or \"Beef\" in selected_meal:\n", "                return 1.0\n", "            else:\n", "                return 0.0\n", "\n", "    def score_response(\n", "        self, inputs, llm_response: str, event: rl_chain.PickBestEvent\n", "    ) -> float:\n", "        selected_meal = event.to_select_from[\"meal\"][event.selected.index]\n", "\n", "        if \"<PERSON>\" in event.based_on[\"user\"]:\n", "            return self.score_preference(event.based_on[\"preference\"], selected_meal)\n", "        elif \"<PERSON>\" in event.based_on[\"user\"]:\n", "            return self.score_preference(event.based_on[\"preference\"], selected_meal)\n", "        else:\n", "            raise NotImplementedError(\"I don't know how to score this user\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["chain = rl_chain.PickBest.from_llm(\n", "    llm=llm,\n", "    prompt=PROMPT,\n", "    selection_scorer=CustomSelectionScorer(),\n", "    metrics_step=5,\n", "    metrics_window_size=5,  # rolling window average\n", ")\n", "\n", "random_chain = rl_chain.PickBest.from_llm(\n", "    llm=llm,\n", "    prompt=PROMPT,\n", "    selection_scorer=CustomSelectionScorer(),\n", "    metrics_step=5,\n", "    metrics_window_size=5,  # rolling window average\n", "    policy=rl_chain.PickBestRandomPolicy,  # set the random policy instead of default\n", ")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["for _ in range(20):\n", "    try:\n", "        chain.run(\n", "            meal=rl_chain.ToSelectFrom(meals),\n", "            user=rl_chain.BasedOn(\"<PERSON>\"),\n", "            preference=rl_chain.BasedOn([\"Vegetarian\", \"regular dairy is ok\"]),\n", "            text_to_personalize=\"This is the weeks specialty dish, our master chefs believe you will love it!\",\n", "        )\n", "        random_chain.run(\n", "            meal=rl_chain.ToSelectFrom(meals),\n", "            user=rl_chain.BasedOn(\"<PERSON>\"),\n", "            preference=rl_chain.BasedOn([\"Vegetarian\", \"regular dairy is ok\"]),\n", "            text_to_personalize=\"This is the weeks specialty dish, our master chefs believe you will love it!\",\n", "        )\n", "\n", "        chain.run(\n", "            meal=rl_chain.ToSelectFrom(meals),\n", "            user=rl_chain.BasedOn(\"<PERSON>\"),\n", "            preference=rl_chain.BasedOn([\"Loves meat\", \"especially beef\"]),\n", "            text_to_personalize=\"This is the weeks specialty dish, our master chefs believe you will love it!\",\n", "        )\n", "        random_chain.run(\n", "            meal=rl_chain.ToSelectFrom(meals),\n", "            user=rl_chain.BasedOn(\"<PERSON>\"),\n", "            preference=rl_chain.BasedOn([\"Loves meat\", \"especially beef\"]),\n", "            text_to_personalize=\"This is the weeks specialty dish, our master chefs believe you will love it!\",\n", "        )\n", "    except Exception as e:\n", "        print(e)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The RL chain converges to the fact that <PERSON> prefers beef and <PERSON> is vegetarian. The random chain picks at random, and so will send beef to vegetarians half the time."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The final average score for the default policy, calculated over a rolling window, is: 1.0\n", "The final average score for the random policy, calculated over a rolling window, is: 0.6\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from matplotlib import pyplot as plt\n", "\n", "chain.metrics.to_pandas()[\"score\"].plot(label=\"default learning policy\")\n", "random_chain.metrics.to_pandas()[\"score\"].plot(label=\"random selection policy\")\n", "plt.legend()\n", "\n", "print(\n", "    f\"The final average score for the default policy, calculated over a rolling window, is: {chain.metrics.to_pandas()['score'].iloc[-1]}\"\n", ")\n", "print(\n", "    f\"The final average score for the random policy, calculated over a rolling window, is: {random_chain.metrics.to_pandas()['score'].iloc[-1]}\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is a bit of randomness involved in the rl_chain's selection since the chain explores the selection space in order to learn the world as best as it can (see details of default exploration algorithm used [here](https://github.com/VowpalWabbit/vowpal_wabbit/wiki/Contextual-Bandit-Exploration-with-SquareCB)), but overall, default chain policy should be doing better than random as it learns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced options\n", "\n", "The RL chain is highly configurable in order to be able to adjust to various selection scenarios. If you want to learn more about the ML library that powers it please take a look at tutorials [here](https://vowpalwabbit.org/)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| Section | Description | Example / Usage |\n", "|---------|-------------|-----------------|\n", "| [**Change Chain Logging Level**](#change-chain-logging-level) | Change the logging level for the RL chain. | `logger.setLevel(logging.INFO)` |\n", "| [**Featurization**](#featurization) | Adjusts the input to the RL chain. Can set auto-embeddings ON for more complex embeddings. | `chain = rl_chain.PickBest.from_llm(auto_embed=True, [...])` |\n", "| [**Learned Policy to Learn Asynchronously**](#learned-policy-to-learn-asynchronously) | Score asynchronously if user input is needed for scoring. | `chain.update_with_delayed_score(score=<the score>, chain_response=response)` |\n", "| [**Store Progress of Learned Policy**](#store-progress-of-learned-policy) | Option to store the progress of the variable injection learned policy. | `chain.save_progress()` |\n", "| [**Stop Learning of Learned Policy**](#stop-learning-of-learned-policy) | Toggle the RL chain's learned policy updates ON/OFF. | `chain.deactivate_selection_scorer()` |\n", "| [**Set a Different Policy**](#set-a-different-policy) | Choose between different policies: default, random, or custom. | Custom policy creation at chain creation time. |\n", "| [**Different Exploration Algorithms and Options for Default Learned Policy**](#different-exploration-algorithms-and-options-for-the-default-learned-policy) | Set different exploration algorithms and hyperparameters for `VwPolicy`. | `vw_cmd = [\"--cb_explore_adf\", \"--quiet\", \"--squarecb\", \"--interactions=::\"]` |\n", "| [**Learn Policy's Data Logs**](#learned-policys-data-logs) | Store and examine `VwPolicy`'s data logs. | `chain = rl_chain.PickBest.from_llm(vw_logs=<path to log FILE>, [...])` |\n", "| [**Other Advanced Featurization Options**](#other-advanced-featurization-options) | Specify advanced featurization options for the RL chain. | `age = rl_chain.BasedOn(\"age:32\")` |\n", "| [**More Info on Auto or Custom SelectionScorer**](#more-info-on-auto-or-custom-selectionscorer) | Dive deeper into how selection scoring is determined. | `selection_scorer=rl_chain.AutoSelectionScorer(llm=llm, scoring_criteria_template_str=scoring_criteria_template)` |\n", "\n", "### change chain logging level\n", "\n", "```\n", "import logging\n", "logger = logging.getLogger(\"rl_chain\")\n", "logger.setLevel(logging.INFO)\n", "```\n", "\n", "### featurization\n", "\n", "#### auto_embed\n", "\n", "By default the input to the rl chain (`ToSelectFrom`, `BasedOn`) is not tampered with. This might  not be sufficient featurization, so based on how complex the scenario is you can set auto-embeddings to ON\n", "\n", "`chain = rl_chain.PickBest.from_llm(auto_embed=True, [...])`\n", "\n", "This will produce more complex embeddings and featurizations of the inputs, likely accelerating RL chain learning, albeit at the cost of increased runtime.\n", "\n", "By default, [sbert.net's sentence_transformers's ](https://www.sbert.net/docs/pretrained_models.html#model-overview) `all-mpnet-base-v2` model will be used for these embeddings but you can set a different embeddings model by initializing the chain with it as shown in this example. You could also set an entirely different embeddings encoding object, as long as it has an `encode()` function that returns a list of the encodings.\n", "\n", "```\n", "from sentence_transformers import SentenceTransformer\n", "\n", "chain = rl_chain.PickBest.from_llm(\n", "    [...]\n", "    feature_embedder=rl_chain.PickBestFeatureEmbedder(\n", "        auto_embed=True,\n", "        model=SentenceTransformer(\"all-mpnet-base-v2\")\n", "    )\n", ")\n", "```\n", "\n", "#### explicitly defined embeddings\n", "\n", "Another option is to define what inputs you think should be embedded manually:\n", "- `auto_embed = False`\n", "- Can wrap individual variables in `rl_chain.Embed()` or `rl_chain.EmbedAndKeep()` e.g. `user = rl_chain.BasedOn(rl_chain.Embed(\"Tom\"))`\n", "\n", "#### custom featurization\n", "\n", "Another final option is to define and set a custom featurization/embedder class that returns a valid input for the learned policy.\n", "\n", "## learned policy to learn asynchronously\n", "\n", "If to score the result you need input from the user (e.g. my application showed <PERSON> the selected meal and <PERSON> clicked on it, but <PERSON> did not), then the scoring can be done asynchronously. The way to do that is:\n", "\n", "- set `selection_scorer=None` on the chain creation OR call `chain.deactivate_selection_scorer()`\n", "- call the chain for a specific input\n", "- keep the chain's response (`response = chain.run([...])`)\n", "- once you have determined the score of the response/chain selection call the chain with it: `chain.update_with_delayed_score(score=<the score>, chain_response=response)`\n", "\n", "### store progress of learned policy\n", "\n", "Since the variable injection learned policy evolves over time, there is the option to store its progress and continue learning. This can be done by calling:\n", "\n", "`chain.save_progress()`\n", "\n", "which will store the rl chain's learned policy in a file called `latest.vw`. It will also store it in a file with a timestamp. That way, if `save_progress()` is called more than once, multiple checkpoints will be created, but the latest one will always be in `latest.vw`\n", "\n", "Next time the chain is loaded, the chain will look for a file called `latest.vw` and if the file exists it will be loaded into the chain and the learning will continue from there.\n", "\n", "By default the rl chain model checkpoints will be stored in the current directory but you can specify the save/load location at chain creation time:\n", "\n", "`chain = rl_chain.PickBest.from_llm(model_save_dir=<path to dir>, [...])`\n", "\n", "### stop learning of learned policy\n", "\n", "If you want the rl chain's learned policy to stop updating you can turn it off/on:\n", "\n", "`chain.deactivate_selection_scorer()` and `chain.activate_selection_scorer()`\n", "\n", "### set a different policy\n", "\n", "There are two policies currently available:\n", "\n", "- default policy: `VwPolicy` which learns a [Vowpal Wabbit](https://github.com/VowpalWabbit/vowpal_wabbit) [Contextual Bandit](https://github.com/VowpalWabbit/vowpal_wabbit/wiki/Contextual-Bandit-algorithms) model\n", "\n", "- random policy: `RandomPolicy` which doesn't learn anything and just selects a value randomly. this policy can be used to compare other policies with a random baseline one.\n", "\n", "- custom policies: a custom policy could be created and set at chain creation time\n", "\n", "### different exploration algorithms and options for the default learned policy\n", "\n", "The default `VwPolicy` is initialized with some default arguments. The default exploration algorithm is [SquareCB](https://github.com/VowpalWabbit/vowpal_wabbit/wiki/Contextual-Bandit-Exploration-with-SquareCB) but other Contextual Bandit exploration algorithms can be set, and other hyper parameters can be tuned (see [here](https://vowpalwabbit.org/docs/vowpal_wabbit/python/9.6.0/command_line_args.html) for available options).\n", "\n", "`vw_cmd = [\"--cb_explore_adf\", \"--quiet\", \"--squarecb\", \"--interactions=::\"]`\n", "\n", "`chain = rl_chain.PickBest.from_llm(vw_cmd = vw_cmd, [...])`\n", "\n", "### learned policy's data logs\n", "\n", "The `VwPolicy`'s data files can be stored and examined or used to do [off policy evaluation](https://vowpalwabbit.org/docs/vowpal_wabbit/python/latest/tutorials/off_policy_evaluation.html) for hyper parameter tuning.\n", "\n", "The way to do this is to set a log file path to `vw_logs` on chain creation:\n", "\n", "`chain = rl_chain.PickBest.from_llm(vw_logs=<path to log FILE>, [...])`\n", "\n", "### other advanced featurization options\n", "\n", "Explicitly numerical features can be provided with a colon separator:\n", "`age = rl_chain.BasedOn(\"age:32\")`\n", "\n", "`ToSelectFrom` can be a bit more complex if the scenario demands it, instead of being a list of strings it can be:\n", "- a list of list of strings:\n", "    ```\n", "    meal = rl_chain.ToSelectFrom([\n", "        [\"meal 1 name\", \"meal 1 description\"],\n", "        [\"meal 2 name\", \"meal 2 description\"]\n", "    ])\n", "    ```\n", "- a list of dictionaries:\n", "    ```\n", "    meal = rl_chain.ToSelectFrom([\n", "        {\"name\":\"meal 1 name\", \"description\" : \"meal 1 description\"},\n", "        {\"name\":\"meal 2 name\", \"description\" : \"meal 2 description\"}\n", "    ])\n", "    ```\n", "- a list of dictionaries containing lists:\n", "    ```\n", "    meal = rl_chain.ToSelectFrom([\n", "        {\"name\":[\"meal 1\", \"complex name\"], \"description\" : \"meal 1 description\"},\n", "        {\"name\":[\"meal 2\", \"complex name\"], \"description\" : \"meal 2 description\"}\n", "    ])\n", "    ```\n", "\n", "`BasedOn` can also take a list of strings:\n", "```\n", "user = rl_chain.BasedOn([\"Tom Joe\", \"age:32\", \"state of california\"])\n", "```\n", "\n", "there is no dictionary provided since multiple variables can be supplied wrapped in `BasedOn`\n", "\n", "Storing the data logs into a file allows the examination of what different inputs do to the data format.\n", "\n", "### More info on Auto or Custom SelectionScorer\n", "\n", "It is very important to get the selection scorer right since the policy uses it to learn. It determines what is called the reward in reinforcement learning, and more specifically in our Contextual Bandits setting.\n", "\n", "The general advice is to keep the score between [0, 1], 0 being the worst selection, 1 being the best selection from the available `ToSelectFrom` variables, based on the `BasedOn` variables, but should be adjusted if the need arises.\n", "\n", "In the examples provided above, the AutoSelectionScorer is set mostly to get users started but in real world scenarios it will most likely not be an adequate scorer function.\n", "\n", "The example also provided the option to change part of the scoring prompt template that the AutoSelectionScorer used to determine whether a selection was good or not:\n", "\n", "```\n", "scoring_criteria_template = \"Given {preference} rank how good or bad this selection is {meal}\"\n", "chain = rl_chain.PickBest.from_llm(\n", "    llm=llm,\n", "    prompt=PROMPT,\n", "    selection_scorer=rl_chain.AutoSelectionScorer(llm=llm, scoring_criteria_template_str=scoring_criteria_template),\n", ")\n", "\n", "```\n", "\n", "Internally the AutoSelectionScorer adjusted the scoring prompt to make sure that the llm scoring returned a single float.\n", "\n", "However, if needed, a FULL scoring prompt can also be provided:\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[1:chain:PickBest] Entering Chain run with input:\n", "\u001b[0m[inputs]\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[1:chain:PickBest > 2:chain:<PERSON><PERSON><PERSON>n] Entering Chain run with input:\n", "\u001b[0m[inputs]\n", "\u001b[32;1m\u001b[1;3m[llm/start]\u001b[0m \u001b[1m[1:chain:PickBest > 2:chain:LLMChain > 3:llm:OpenAI] Entering LLM run with input:\n", "\u001b[0m{\n", "  \"prompts\": [\n", "    \"Here is the description of a meal: \\\"Chicken Flatbreads with red sauce. Italian-Mexican fusion\\\".\\n\\nEmbed the meal into the given text: \\\"This is the weeks specialty dish, our master chefs believe you will love it!\\\".\\n\\nPrepend a personalized message including the user's name \\\"<PERSON>\\\" \\n    and their preference \\\"['Vegetarian', 'regular dairy is ok']\\\".\\n\\nMake it sound good.\"\n", "  ]\n", "}\n", "\u001b[36;1m\u001b[1;3m[llm/end]\u001b[0m \u001b[1m[1:chain:PickBest > 2:chain:LLMChain > 3:llm:OpenAI] [1.12s] Exiting LLM run with output:\n", "\u001b[0m{\n", "  \"generations\": [\n", "    [\n", "      {\n", "        \"text\": \"\\nHey <PERSON>, we have something special for you this week! Our master chefs have created a delicious Italian-Mexican fusion Chicken Flatbreads with red sauce just for you. Our chefs have also taken into account your preference of vegetarian options with regular dairy - this one is sure to be a hit!\",\n", "        \"generation_info\": {\n", "          \"finish_reason\": \"stop\",\n", "          \"logprobs\": null\n", "        }\n", "      }\n", "    ]\n", "  ],\n", "  \"llm_output\": {\n", "    \"token_usage\": {\n", "      \"total_tokens\": 154,\n", "      \"completion_tokens\": 61,\n", "      \"prompt_tokens\": 93\n", "    },\n", "    \"model_name\": \"text-davinci-003\"\n", "  },\n", "  \"run\": null\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[1:chain:PickBest > 2:chain:<PERSON><PERSON><PERSON><PERSON>] [1.12s] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"text\": \"\\nHey <PERSON>, we have something special for you this week! Our master chefs have created a delicious Italian-Mexican fusion Chicken Flatbreads with red sauce just for you. Our chefs have also taken into account your preference of vegetarian options with regular dairy - this one is sure to be a hit!\"\n", "}\n", "\u001b[32;1m\u001b[1;3m[chain/start]\u001b[0m \u001b[1m[1:chain:<PERSON>MChain] Entering Chain run with input:\n", "\u001b[0m[inputs]\n", "\u001b[32;1m\u001b[1;3m[llm/start]\u001b[0m \u001b[1m[1:chain:LLMChain > 2:llm:OpenAI] Entering LLM run with input:\n", "\u001b[0m{\n", "  \"prompts\": [\n", "    \"Given ['Vegetarian', 'regular dairy is ok'] rank how good or bad this selection is ['Beef Enchiladas with Feta cheese. Mexican-Greek fusion', 'Chicken Flatbreads with red sauce. Italian-Mexican fusion', 'Veggie sweet potato quesadillas with vegan cheese', 'One-Pan Tortelonni bake with peppers and onions']\\n\\nIMPORTANT: you MUST return a single number between -1 and 1, -1 being bad, 1 being good\"\n", "  ]\n", "}\n", "\u001b[36;1m\u001b[1;3m[llm/end]\u001b[0m \u001b[1m[1:chain:LLMChain > 2:llm:OpenAI] [274ms] Exiting LLM run with output:\n", "\u001b[0m{\n", "  \"generations\": [\n", "    [\n", "      {\n", "        \"text\": \"\\n0.625\",\n", "        \"generation_info\": {\n", "          \"finish_reason\": \"stop\",\n", "          \"logprobs\": null\n", "        }\n", "      }\n", "    ]\n", "  ],\n", "  \"llm_output\": {\n", "    \"token_usage\": {\n", "      \"total_tokens\": 112,\n", "      \"completion_tokens\": 4,\n", "      \"prompt_tokens\": 108\n", "    },\n", "    \"model_name\": \"text-davinci-003\"\n", "  },\n", "  \"run\": null\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[1:chain:<PERSON>MChain] [275ms] Exiting Chain run with output:\n", "\u001b[0m{\n", "  \"text\": \"\\n0.625\"\n", "}\n", "\u001b[36;1m\u001b[1;3m[chain/end]\u001b[0m \u001b[1m[1:chain:PickBest] [1.40s] Exiting Chain run with output:\n", "\u001b[0m[outputs]\n"]}, {"data": {"text/plain": ["{'response': 'Hey <PERSON>, we have something special for you this week! Our master chefs have created a delicious Italian-Mexican fusion Chicken Flatbreads with red sauce just for you. Our chefs have also taken into account your preference of vegetarian options with regular dairy - this one is sure to be a hit!',\n", " 'selection_metadata': <langchain_experimental.rl_chain.pick_best_chain.PickBestEvent at 0x289764220>}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.globals import set_debug\n", "from langchain.prompts.prompt import PromptTemplate\n", "\n", "set_debug(True)\n", "\n", "REWARD_PROMPT_TEMPLATE = \"\"\"\n", "\n", "Given {preference} rank how good or bad this selection is {meal}\n", "\n", "IMPORTANT: you MUST return a single number between -1 and 1, -1 being bad, 1 being good\n", "\n", "\"\"\"\n", "\n", "\n", "REWARD_PROMPT = PromptTemplate(\n", "    input_variables=[\"preference\", \"meal\"],\n", "    template=REWARD_PROMPT_TEMPLATE,\n", ")\n", "\n", "chain = rl_chain.PickBest.from_llm(\n", "    llm=llm,\n", "    prompt=PROMPT,\n", "    selection_scorer=rl_chain.AutoSelectionScorer(llm=llm, prompt=REWARD_PROMPT),\n", ")\n", "\n", "chain.run(\n", "    meal=rl_chain.ToSelectFrom(meals),\n", "    user=rl_chain.BasedOn(\"<PERSON>\"),\n", "    preference=rl_chain.BasedOn([\"Vegetarian\", \"regular dairy is ok\"]),\n", "    text_to_personalize=\"This is the weeks specialty dish, our master chefs believe you will love it!\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.1"}}, "nbformat": 4, "nbformat_minor": 4}