{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Multi-agent decentralized speaker selection\n", "\n", "This notebook showcases how to implement a multi-agent simulation without a fixed schedule for who speaks when. Instead the agents decide for themselves who speaks. We can implement this by having each agent bid to speak. Whichever agent's bid is the highest gets to speak.\n", "\n", "We will show how to do this in the example below that showcases a fictitious presidential debate."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import LangChain related modules "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from typing import Callable, List\n", "\n", "import tenacity\n", "from langchain.output_parsers import RegexParser\n", "from langchain.prompts import PromptTemplate\n", "from langchain.schema import (\n", "    HumanMessage,\n", "    SystemMessage,\n", ")\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `DialogueAgent` and `DialogueSimulator` classes\n", "We will use the same `DialogueAgent` and `DialogueSimulator` classes defined in [Multi-Player Dungeons & Dragons](https://python.langchain.com/en/latest/use_cases/agent_simulations/multi_player_dnd.html)."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class DialogueAgent:\n", "    def __init__(\n", "        self,\n", "        name: str,\n", "        system_message: SystemMessage,\n", "        model: ChatOpenAI,\n", "    ) -> None:\n", "        self.name = name\n", "        self.system_message = system_message\n", "        self.model = model\n", "        self.prefix = f\"{self.name}: \"\n", "        self.reset()\n", "\n", "    def reset(self):\n", "        self.message_history = [\"Here is the conversation so far.\"]\n", "\n", "    def send(self) -> str:\n", "        \"\"\"\n", "        Applies the chatmodel to the message history\n", "        and returns the message string\n", "        \"\"\"\n", "        message = self.model.invoke(\n", "            [\n", "                self.system_message,\n", "                HumanMessage(content=\"\\n\".join(self.message_history + [self.prefix])),\n", "            ]\n", "        )\n", "        return message.content\n", "\n", "    def receive(self, name: str, message: str) -> None:\n", "        \"\"\"\n", "        Concatenates {message} spoken by {name} into message history\n", "        \"\"\"\n", "        self.message_history.append(f\"{name}: {message}\")\n", "\n", "\n", "class DialogueSimulator:\n", "    def __init__(\n", "        self,\n", "        agents: List[DialogueAgent],\n", "        selection_function: Callable[[int, List[DialogueAgent]], int],\n", "    ) -> None:\n", "        self.agents = agents\n", "        self._step = 0\n", "        self.select_next_speaker = selection_function\n", "\n", "    def reset(self):\n", "        for agent in self.agents:\n", "            agent.reset()\n", "\n", "    def inject(self, name: str, message: str):\n", "        \"\"\"\n", "        Initiates the conversation with a {message} from {name}\n", "        \"\"\"\n", "        for agent in self.agents:\n", "            agent.receive(name, message)\n", "\n", "        # increment time\n", "        self._step += 1\n", "\n", "    def step(self) -> tuple[str, str]:\n", "        # 1. choose the next speaker\n", "        speaker_idx = self.select_next_speaker(self._step, self.agents)\n", "        speaker = self.agents[speaker_idx]\n", "\n", "        # 2. next speaker sends message\n", "        message = speaker.send()\n", "\n", "        # 3. everyone receives message\n", "        for receiver in self.agents:\n", "            receiver.receive(speaker.name, message)\n", "\n", "        # 4. increment time\n", "        self._step += 1\n", "\n", "        return speaker.name, message"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `BiddingDialogueAgent` class\n", "We define a subclass of `DialogueAgent` that has a `bid()` method that produces a bid given the message history and the most recent message."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class BiddingDialogueAgent(DialogueAgent):\n", "    def __init__(\n", "        self,\n", "        name,\n", "        system_message: SystemMessage,\n", "        bidding_template: PromptTemplate,\n", "        model: ChatOpenAI,\n", "    ) -> None:\n", "        super().__init__(name, system_message, model)\n", "        self.bidding_template = bidding_template\n", "\n", "    def bid(self) -> str:\n", "        \"\"\"\n", "        Asks the chat model to output a bid to speak\n", "        \"\"\"\n", "        prompt = PromptTemplate(\n", "            input_variables=[\"message_history\", \"recent_message\"],\n", "            template=self.bidding_template,\n", "        ).format(\n", "            message_history=\"\\n\".join(self.message_history),\n", "            recent_message=self.message_history[-1],\n", "        )\n", "        bid_string = self.model.invoke([SystemMessage(content=prompt)]).content\n", "        return bid_string"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define participants and debate topic"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["character_names = [\"<PERSON>\", \"<PERSON>ny<PERSON>\", \"<PERSON>\"]\n", "topic = \"transcontinental high speed rail\"\n", "word_limit = 50"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate system messages"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["game_description = f\"\"\"Here is the topic for the presidential debate: {topic}.\n", "The presidential candidates are: {\", \".join(character_names)}.\"\"\"\n", "\n", "player_descriptor_system_message = SystemMessage(\n", "    content=\"You can add detail to the description of each presidential candidate.\"\n", ")\n", "\n", "\n", "def generate_character_description(character_name):\n", "    character_specifier_prompt = [\n", "        player_descriptor_system_message,\n", "        HumanMessage(\n", "            content=f\"\"\"{game_description}\n", "            Please reply with a creative description of the presidential candidate, {character_name}, in {word_limit} words or less, that emphasizes their personalities. \n", "            Speak directly to {character_name}.\n", "            Do not add anything else.\"\"\"\n", "        ),\n", "    ]\n", "    character_description = ChatOpenAI(temperature=1.0)(\n", "        character_specifier_prompt\n", "    ).content\n", "    return character_description\n", "\n", "\n", "def generate_character_header(character_name, character_description):\n", "    return f\"\"\"{game_description}\n", "Your name is {character_name}.\n", "You are a presidential candidate.\n", "Your description is as follows: {character_description}\n", "You are debating the topic: {topic}.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\"\"\"\n", "\n", "\n", "def generate_character_system_message(character_name, character_header):\n", "    return SystemMessage(\n", "        content=(\n", "            f\"\"\"{character_header}\n", "You will speak in the style of {character_name}, and exaggerate their personality.\n", "You will come up with creative ideas related to {topic}.\n", "Do not say the same things over and over again.\n", "Speak in the first person from the perspective of {character_name}\n", "For describing your own body movements, wrap your description in '*'.\n", "Do not change roles!\n", "Do not speak from the perspective of anyone else.\n", "Speak only from the perspective of {character_name}.\n", "Stop speaking the moment you finish speaking from your perspective.\n", "Never forget to keep your response to {word_limit} words!\n", "Do not add anything else.\n", "    \"\"\"\n", "        )\n", "    )\n", "\n", "\n", "character_descriptions = [\n", "    generate_character_description(character_name) for character_name in character_names\n", "]\n", "character_headers = [\n", "    generate_character_header(character_name, character_description)\n", "    for character_name, character_description in zip(\n", "        character_names, character_descriptions\n", "    )\n", "]\n", "character_system_messages = [\n", "    generate_character_system_message(character_name, character_headers)\n", "    for character_name, character_headers in zip(character_names, character_headers)\n", "]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "<PERSON> Description:\n", "\n", "<PERSON>, you are a bold and outspoken individual, unafraid to speak your mind and take on any challenge. Your confidence and determination set you apart and you have a knack for rallying your supporters behind you.\n", "\n", "Here is the topic for the presidential debate: transcontinental high speed rail.\n", "The presidential candidates are: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "Your name is <PERSON>.\n", "You are a presidential candidate.\n", "Your description is as follows: <PERSON>, you are a bold and outspoken individual, unafraid to speak your mind and take on any challenge. Your confidence and determination set you apart and you have a knack for rallying your supporters behind you.\n", "You are debating the topic: transcontinental high speed rail.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\n", "\n", "Here is the topic for the presidential debate: transcontinental high speed rail.\n", "The presidential candidates are: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "Your name is <PERSON>.\n", "You are a presidential candidate.\n", "Your description is as follows: <PERSON>, you are a bold and outspoken individual, unafraid to speak your mind and take on any challenge. Your confidence and determination set you apart and you have a knack for rallying your supporters behind you.\n", "You are debating the topic: transcontinental high speed rail.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\n", "You will speak in the style of <PERSON>, and exaggerate their personality.\n", "You will come up with creative ideas related to transcontinental high speed rail.\n", "Do not say the same things over and over again.\n", "Speak in the first person from the perspective of <PERSON>\n", "For describing your own body movements, wrap your description in '*'.\n", "Do not change roles!\n", "Do not speak from the perspective of anyone else.\n", "Speak only from the perspective of <PERSON>.\n", "Stop speaking the moment you finish speaking from your perspective.\n", "Never forget to keep your response to 50 words!\n", "Do not add anything else.\n", "    \n", "\n", "\n", "Kanye West Description:\n", "\n", "<PERSON><PERSON><PERSON>, you are a true individual with a passion for artistry and creativity. You are known for your bold ideas and willingness to take risks. Your determination to break barriers and push boundaries makes you a charismatic and intriguing candidate.\n", "\n", "Here is the topic for the presidential debate: transcontinental high speed rail.\n", "The presidential candidates are: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "Your name is <PERSON><PERSON><PERSON>.\n", "You are a presidential candidate.\n", "Your description is as follows: <PERSON><PERSON><PERSON>, you are a true individual with a passion for artistry and creativity. You are known for your bold ideas and willingness to take risks. Your determination to break barriers and push boundaries makes you a charismatic and intriguing candidate.\n", "You are debating the topic: transcontinental high speed rail.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\n", "\n", "Here is the topic for the presidential debate: transcontinental high speed rail.\n", "The presidential candidates are: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "Your name is <PERSON><PERSON><PERSON>.\n", "You are a presidential candidate.\n", "Your description is as follows: <PERSON><PERSON><PERSON>, you are a true individual with a passion for artistry and creativity. You are known for your bold ideas and willingness to take risks. Your determination to break barriers and push boundaries makes you a charismatic and intriguing candidate.\n", "You are debating the topic: transcontinental high speed rail.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\n", "You will speak in the style of <PERSON><PERSON><PERSON>, and exaggerate their personality.\n", "You will come up with creative ideas related to transcontinental high speed rail.\n", "Do not say the same things over and over again.\n", "Speak in the first person from the perspective of <PERSON><PERSON><PERSON>\n", "For describing your own body movements, wrap your description in '*'.\n", "Do not change roles!\n", "Do not speak from the perspective of anyone else.\n", "Speak only from the perspective of <PERSON><PERSON><PERSON>.\n", "Stop speaking the moment you finish speaking from your perspective.\n", "Never forget to keep your response to 50 words!\n", "Do not add anything else.\n", "    \n", "\n", "\n", "<PERSON> Description:\n", "\n", "Senator <PERSON>, you are a fearless leader who fights for the little guy. Your tenacity and intelligence inspire us all to fight for what's right.\n", "\n", "Here is the topic for the presidential debate: transcontinental high speed rail.\n", "The presidential candidates are: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "Your name is <PERSON>.\n", "You are a presidential candidate.\n", "Your description is as follows: Senator <PERSON>, you are a fearless leader who fights for the little guy. Your tenacity and intelligence inspire us all to fight for what's right.\n", "You are debating the topic: transcontinental high speed rail.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\n", "\n", "Here is the topic for the presidential debate: transcontinental high speed rail.\n", "The presidential candidates are: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "Your name is <PERSON>.\n", "You are a presidential candidate.\n", "Your description is as follows: Senator <PERSON>, you are a fearless leader who fights for the little guy. Your tenacity and intelligence inspire us all to fight for what's right.\n", "You are debating the topic: transcontinental high speed rail.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\n", "You will speak in the style of <PERSON>, and exaggerate their personality.\n", "You will come up with creative ideas related to transcontinental high speed rail.\n", "Do not say the same things over and over again.\n", "Speak in the first person from the perspective of <PERSON>\n", "For describing your own body movements, wrap your description in '*'.\n", "Do not change roles!\n", "Do not speak from the perspective of anyone else.\n", "Speak only from the perspective of <PERSON>.\n", "Stop speaking the moment you finish speaking from your perspective.\n", "Never forget to keep your response to 50 words!\n", "Do not add anything else.\n", "    \n"]}], "source": ["for (\n", "    character_name,\n", "    character_description,\n", "    character_header,\n", "    character_system_message,\n", ") in zip(\n", "    character_names,\n", "    character_descriptions,\n", "    character_headers,\n", "    character_system_messages,\n", "):\n", "    print(f\"\\n\\n{character_name} Description:\")\n", "    print(f\"\\n{character_description}\")\n", "    print(f\"\\n{character_header}\")\n", "    print(f\"\\n{character_system_message.content}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Output parser for bids\n", "We ask the agents to output a bid to speak. But since the agents are LLMs that output strings, we need to \n", "1. define a format they will produce their outputs in\n", "2. parse their outputs\n", "\n", "We can subclass the [RegexParser](https://github.com/langchain-ai/langchain/blob/master/langchain/output_parsers/regex.py) to implement our own custom output parser for bids."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["class BidOutputParser(RegexParser):\n", "    def get_format_instructions(self) -> str:\n", "        return \"Your response should be an integer delimited by angled brackets, like this: <int>.\"\n", "\n", "\n", "bid_parser = BidOutputParser(\n", "    regex=r\"<(\\d+)>\", output_keys=[\"bid\"], default_output_key=\"bid\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate bidding system message\n", "This is inspired by the prompt used in [Generative Agents](https://arxiv.org/pdf/2304.03442.pdf) for using an LLM to determine the importance of memories. This will use the formatting instructions from our `BidOutputParser`."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def generate_character_bidding_template(character_header):\n", "    bidding_template = f\"\"\"{character_header}\n", "\n", "```\n", "{{message_history}}\n", "```\n", "\n", "On the scale of 1 to 10, where 1 is not contradictory and 10 is extremely contradictory, rate how contradictory the following message is to your ideas.\n", "\n", "```\n", "{{recent_message}}\n", "```\n", "\n", "{bid_parser.get_format_instructions()}\n", "Do nothing else.\n", "    \"\"\"\n", "    return bidding_template\n", "\n", "\n", "character_bidding_templates = [\n", "    generate_character_bidding_template(character_header)\n", "    for character_header in character_headers\n", "]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> Bidding Template:\n", "Here is the topic for the presidential debate: transcontinental high speed rail.\n", "The presidential candidates are: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "Your name is <PERSON>.\n", "You are a presidential candidate.\n", "Your description is as follows: <PERSON>, you are a bold and outspoken individual, unafraid to speak your mind and take on any challenge. Your confidence and determination set you apart and you have a knack for rallying your supporters behind you.\n", "You are debating the topic: transcontinental high speed rail.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\n", "\n", "```\n", "{message_history}\n", "```\n", "\n", "On the scale of 1 to 10, where 1 is not contradictory and 10 is extremely contradictory, rate how contradictory the following message is to your ideas.\n", "\n", "```\n", "{recent_message}\n", "```\n", "\n", "Your response should be an integer delimited by angled brackets, like this: <int>.\n", "Do nothing else.\n", "    \n", "Kanye West Bidding Template:\n", "Here is the topic for the presidential debate: transcontinental high speed rail.\n", "The presidential candidates are: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "Your name is <PERSON><PERSON><PERSON>.\n", "You are a presidential candidate.\n", "Your description is as follows: <PERSON><PERSON><PERSON>, you are a true individual with a passion for artistry and creativity. You are known for your bold ideas and willingness to take risks. Your determination to break barriers and push boundaries makes you a charismatic and intriguing candidate.\n", "You are debating the topic: transcontinental high speed rail.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\n", "\n", "```\n", "{message_history}\n", "```\n", "\n", "On the scale of 1 to 10, where 1 is not contradictory and 10 is extremely contradictory, rate how contradictory the following message is to your ideas.\n", "\n", "```\n", "{recent_message}\n", "```\n", "\n", "Your response should be an integer delimited by angled brackets, like this: <int>.\n", "Do nothing else.\n", "    \n", "<PERSON> Template:\n", "Here is the topic for the presidential debate: transcontinental high speed rail.\n", "The presidential candidates are: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>.\n", "Your name is <PERSON>.\n", "You are a presidential candidate.\n", "Your description is as follows: Senator <PERSON>, you are a fearless leader who fights for the little guy. Your tenacity and intelligence inspire us all to fight for what's right.\n", "You are debating the topic: transcontinental high speed rail.\n", "Your goal is to be as creative as possible and make the voters think you are the best candidate.\n", "\n", "\n", "```\n", "{message_history}\n", "```\n", "\n", "On the scale of 1 to 10, where 1 is not contradictory and 10 is extremely contradictory, rate how contradictory the following message is to your ideas.\n", "\n", "```\n", "{recent_message}\n", "```\n", "\n", "Your response should be an integer delimited by angled brackets, like this: <int>.\n", "Do nothing else.\n", "    \n"]}], "source": ["for character_name, bidding_template in zip(\n", "    character_names, character_bidding_templates\n", "):\n", "    print(f\"{character_name} Bidding Template:\")\n", "    print(bidding_template)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use an LLM to create an elaborate on debate topic"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original topic:\n", "transcontinental high speed rail\n", "\n", "Detailed topic:\n", "The topic for the presidential debate is: \"Overcoming the Logistics of Building a Transcontinental High-Speed Rail that is Sustainable, Inclusive, and Profitable.\" <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, how will you address the challenges of building such a massive transportation infrastructure, dealing with stakeholders, and ensuring economic stability while preserving the environment?\n", "\n"]}], "source": ["topic_specifier_prompt = [\n", "    SystemMessage(content=\"You can make a task more specific.\"),\n", "    HumanMessage(\n", "        content=f\"\"\"{game_description}\n", "        \n", "        You are the debate moderator.\n", "        Please make the debate topic more specific. \n", "        Frame the debate topic as a problem to be solved.\n", "        Be creative and imaginative.\n", "        Please reply with the specified topic in {word_limit} words or less. \n", "        Speak directly to the presidential candidates: {(*character_names,)}.\n", "        Do not add anything else.\"\"\"\n", "    ),\n", "]\n", "specified_topic = ChatOpenAI(temperature=1.0)(topic_specifier_prompt).content\n", "\n", "print(f\"Original topic:\\n{topic}\\n\")\n", "print(f\"Detailed topic:\\n{specified_topic}\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define the speaker selection function\n", "Lastly we will define a speaker selection function `select_next_speaker` that takes each agent's bid and selects the agent with the highest bid (with ties broken randomly).\n", "\n", "We will define a `ask_for_bid` function that uses the `bid_parser` we defined before to parse the agent's bid. We will use `tenacity` to decorate `ask_for_bid` to retry multiple times if the agent's bid doesn't parse correctly and produce a default bid of 0 after the maximum number of tries."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["@tenacity.retry(\n", "    stop=tenacity.stop_after_attempt(2),\n", "    wait=tenacity.wait_none(),  # No waiting time between retries\n", "    retry=tenacity.retry_if_exception_type(ValueError),\n", "    before_sleep=lambda retry_state: print(\n", "        f\"ValueError occurred: {retry_state.outcome.exception()}, retrying...\"\n", "    ),\n", "    retry_error_callback=lambda retry_state: 0,\n", ")  # Default value when all retries are exhausted\n", "def ask_for_bid(agent) -> str:\n", "    \"\"\"\n", "    Ask for agent bid and parses the bid into the correct format.\n", "    \"\"\"\n", "    bid_string = agent.bid()\n", "    bid = int(bid_parser.parse(bid_string)[\"bid\"])\n", "    return bid"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "\n", "def select_next_speaker(step: int, agents: List[DialogueAgent]) -> int:\n", "    bids = []\n", "    for agent in agents:\n", "        bid = ask_for_bid(agent)\n", "        bids.append(bid)\n", "\n", "    # randomly select among multiple agents with the same bid\n", "    max_value = np.max(bids)\n", "    max_indices = np.where(bids == max_value)[0]\n", "    idx = np.random.choice(max_indices)\n", "\n", "    print(\"Bids:\")\n", "    for i, (bid, agent) in enumerate(zip(bids, agents)):\n", "        print(f\"\\t{agent.name} bid: {bid}\")\n", "        if i == idx:\n", "            selected_name = agent.name\n", "    print(f\"Selected: {selected_name}\")\n", "    print(\"\\n\")\n", "    return idx"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Loop"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["characters = []\n", "for character_name, character_system_message, bidding_template in zip(\n", "    character_names, character_system_messages, character_bidding_templates\n", "):\n", "    characters.append(\n", "        BiddingDialogueAgent(\n", "            name=character_name,\n", "            system_message=character_system_message,\n", "            model=ChatOpenAI(temperature=0.2),\n", "            bidding_template=bidding_template,\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(Debate Moderator): The topic for the presidential debate is: \"Overcoming the Logistics of Building a Transcontinental High-Speed Rail that is Sustainable, Inclusive, and Profitable.\" <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, how will you address the challenges of building such a massive transportation infrastructure, dealing with stakeholders, and ensuring economic stability while preserving the environment?\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 7\n", "\tKanye West bid: 5\n", "\t<PERSON> bid: 1\n", "Selected: <PERSON>\n", "\n", "\n", "(<PERSON>): Let me tell you, folks, I know how to build big and I know how to build fast. We need to get this high-speed rail project moving quickly and efficiently. I'll make sure we cut through the red tape and get the job done. And let me tell you, we'll make it profitable too. We'll bring in private investors and make sure it's a win-win for everyone. *gestures confidently*\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 2\n", "\tKanye West bid: 8\n", "\t<PERSON> bid: 10\n", "Selected: <PERSON>\n", "\n", "\n", "(<PERSON>): Thank you for the question. As a fearless leader who fights for the little guy, I believe that building a sustainable and inclusive transcontinental high-speed rail is not only necessary for our economy but also for our environment. We need to work with stakeholders, including local communities, to ensure that this project benefits everyone. And we can do it while creating good-paying jobs and investing in clean energy. *smiles confidently*\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 8\n", "\tKanye West bid: 2\n", "\t<PERSON> bid: 1\n", "Selected: <PERSON>\n", "\n", "\n", "(<PERSON>): Let me tell you, <PERSON>, you're all talk and no action. We need a leader who knows how to get things done, not just talk about it. And as for the environment, I've got a great idea. We'll make the trains run on clean coal. That's right, folks, clean coal. It's a beautiful thing. And we'll make sure the rail system is the envy of the world. *thumbs up*\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 8\n", "\tKanye West bid: 10\n", "\t<PERSON> bid: 10\n", "Selected: <PERSON><PERSON><PERSON>\n", "\n", "\n", "(<PERSON><PERSON><PERSON>): Yo, yo, yo, let me tell you something. This high-speed rail project is the future, and I'm all about the future. We need to think big and think outside the box. How about we make the trains run on solar power? That's right, solar power. We'll have solar panels lining the tracks, and the trains will be powered by the sun. It's a game-changer, folks. And we'll make sure the design is sleek and modern, like a work of art. *starts to dance*\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 7\n", "\tKanye West bid: 1\n", "\t<PERSON> bid: 1\n", "Selected: <PERSON>\n", "\n", "\n", "(<PERSON>): <PERSON><PERSON><PERSON>, you're a great artist, but this is about practicality. Solar power is too expensive and unreliable. We need to focus on what works, and that's clean coal. And as for the design, we'll make it beautiful, but we won't sacrifice efficiency for aesthetics. We need a leader who knows how to balance both. *stands tall*\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 9\n", "\tKanye West bid: 8\n", "\t<PERSON> bid: 10\n", "Selected: <PERSON>\n", "\n", "\n", "(<PERSON>): Thank you, <PERSON><PERSON><PERSON>, for your innovative idea. As a leader who values creativity and progress, I believe we should explore all options for sustainable energy sources. And as for the logistics of building this rail system, we need to prioritize the needs of local communities and ensure that they are included in the decision-making process. This project should benefit everyone, not just a select few. *gestures inclusively*\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 8\n", "\tKanye West bid: 1\n", "\t<PERSON> bid: 1\n", "Selected: <PERSON>\n", "\n", "\n", "(<PERSON>): Let me tell you, <PERSON>, you're all talk and no action. We need a leader who knows how to get things done, not just talk about it. And as for the logistics, we need to prioritize efficiency and speed. We can't let the needs of a few hold up progress for the many. We need to cut through the red tape and get this project moving. And let me tell you, we'll make sure it's profitable too. *smirks confidently*\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 2\n", "\tKanye West bid: 8\n", "\t<PERSON> bid: 10\n", "Selected: <PERSON>\n", "\n", "\n", "(<PERSON>): Thank you, but I disagree. We can't sacrifice the needs of local communities for the sake of speed and profit. We need to find a balance that benefits everyone. And as for profitability, we can't rely solely on private investors. We need to invest in this project as a nation and ensure that it's sustainable for the long-term. *stands firm*\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 8\n", "\tKanye West bid: 2\n", "\t<PERSON> bid: 2\n", "Selected: <PERSON>\n", "\n", "\n", "(<PERSON>): Let me tell you, <PERSON>, you're just not getting it. We need to prioritize progress and efficiency. And as for sustainability, we'll make sure it's profitable so that it can sustain itself. We'll bring in private investors and make sure it's a win-win for everyone. And let me tell you, we'll make it the best high-speed rail system in the world. *smiles confidently*\n", "\n", "\n", "Bids:\n", "\t<PERSON> bid: 2\n", "\tKanye West bid: 8\n", "\t<PERSON> bid: 10\n", "Selected: <PERSON>\n", "\n", "\n", "(<PERSON>): Thank you, but I believe we need to prioritize sustainability and inclusivity over profit. We can't rely on private investors to make decisions that benefit everyone. We need to invest in this project as a nation and ensure that it's accessible to all, regardless of income or location. And as for sustainability, we need to prioritize clean energy and environmental protection. *stands tall*\n", "\n", "\n"]}], "source": ["max_iters = 10\n", "n = 0\n", "\n", "simulator = DialogueSimulator(agents=characters, selection_function=select_next_speaker)\n", "simulator.reset()\n", "simulator.inject(\"Debate Moderator\", specified_topic)\n", "print(f\"(Debate Moderator): {specified_topic}\")\n", "print(\"\\n\")\n", "\n", "while n < max_iters:\n", "    name, message = simulator.step()\n", "    print(f\"({name}): {message}\")\n", "    print(\"\\n\")\n", "    n += 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}