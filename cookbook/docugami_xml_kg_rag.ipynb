{"cells": [{"attachments": {"image.png": {"image/png": "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**********************************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****************************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"}}, "cell_type": "markdown", "id": "b6d466cc-aa8b-4baf-a80a-fef01921ca8d", "metadata": {}, "source": ["## Docugami RAG over XML Knowledge Graphs (KG-RAG)\n", "\n", "Many documents contain a mixture of content types, including text and tables. \n", "\n", "Semi-structured data can be challenging for conventional RAG for a few reasons since semantics may be lost by text-only chunking techniques, e.g.: \n", "\n", "* Text splitting may break up tables, corrupting the data in retrieval\n", "* Embedding tables may pose challenges for semantic similarity search \n", "\n", "Docugami deconstructs documents into XML Knowledge Graphs consisting of hierarchical semantic chunks using the XML data model. This cookbook shows how to perform RAG using XML Knowledge Graphs as input (**KG-RAG**):\n", "\n", "* We will use [Docugami](http://docugami.com/) to segment out text and table chunks from documents (PDF \\[scanned or digital\\], DOC or DOCX) including semantic XML markup in the chunks.\n", "* We will use the [multi-vector retriever](https://python.langchain.com/docs/modules/data_connection/retrievers/multi_vector) to store raw tables and text (including semantic XML markup) along with table summaries better suited for retrieval.\n", "* We will use [LCEL](https://python.langchain.com/docs/expression_language/) to implement the chains used.\n", "\n", "The overall flow is here:\n", "\n", "![image.png](attachment:image.png)\n", "\n", "## Packages"]}, {"cell_type": "code", "execution_count": 16, "id": "5740fc70-c513-4ff4-9d72-cfc098f85fef", "metadata": {}, "outputs": [], "source": ["! pip install langchain docugami==0.0.8 dgml-utils==0.3.0 pydantic langchainhub langchain-chroma hnswlib --upgrade --quiet"]}, {"cell_type": "markdown", "id": "44349a83-e1dc-4eed-ba75-587f309d8c88", "metadata": {}, "source": ["Docugami processes documents in the cloud, so you don't need to install any additional local dependencies. "]}, {"cell_type": "markdown", "id": "c6fb4903-f845-4907-ae14-df305891b0ff", "metadata": {}, "source": ["## Data Loading\n", "\n", "Let's use Docugami to process some documents. Here's what you need to get started:\n", "\n", "1. Create a [Docugami workspace](http://www.docugami.com) (free trials available)\n", "1. Create an access token via the Developer Playground for your workspace. [Detailed instructions](https://help.docugami.com/home/<USER>\n", "1. Add your documents (PDF \\[scanned or digital\\], DOC or DOCX) to Docugami for processing. There are two ways to do this:\n", "    1. Use the simple Docugami web experience. [Detailed instructions](https://help.docugami.com/home/<USER>\n", "    1. Use the [Docugami API](https://api-docs.docugami.com), specifically the [documents](https://api-docs.docugami.com/#tag/documents/operation/upload-document) endpoint. You can also use the [docugami python library](https://pypi.org/project/docugami/) as a convenient wrapper.\n", "\n", "Once your documents are in Docugami, they are processed and organized into sets of similar documents, e.g. NDAs, Lease Agreements, and Service Agreements. Docugami is not limited to any particular types of documents, and the clusters created depend on your particular documents. You can [change the docset assignments](https://help.docugami.com/home/<USER>//api-docs.docugami.com/#tag/webhooks) to be informed when your documents are done processing.\n", "\n", "You can also use the [Docugami API](https://api-docs.docugami.com) or the  [docugami](https://pypi.org/project/docugami/) python library to do all the file processing without visiting the Docugami webapp except to get the API key.\n", "\n", "> You can get an API key as documented here: https://help.docugami.com/home/<USER>\n", "\n", "First, let's define two simple helper methods to upload files and wait for them to finish processing."]}, {"cell_type": "code", "execution_count": 3, "id": "ce0b2b21-7623-46e7-ae2c-3a9f67e8b9b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Report_CEN23LA277_192541.pdf': '/tmp/tmpa0c77x46',\n", " 'Report_CEN23LA338_192753.pdf': '/tmp/tmpaftfld2w',\n", " 'Report_CEN23LA363_192876.pdf': '/tmp/tmpn7gp6be2',\n", " 'Report_CEN23LA394_192995.pdf': '/tmp/tmp9udymprf',\n", " 'Report_ERA23LA114_106615.pdf': '/tmp/tmpxdjbh4r_',\n", " 'Report_WPR23LA254_192532.pdf': '/tmp/tmpz6h75a0h'}\n"]}], "source": ["from pprint import pprint\n", "\n", "from docugami import Docugami\n", "from docugami.lib.upload import upload_to_named_docset, wait_for_dgml\n", "\n", "#### START DOCSET INFO (please change this values as needed)\n", "DOCSET_NAME = \"NTSB Aviation Incident Reports\"\n", "FILE_PATHS = [\n", "    \"/Users/<USER>/ntsb/Report_CEN23LA277_192541.pdf\",\n", "    \"/Users/<USER>/ntsb/Report_CEN23LA338_192753.pdf\",\n", "    \"/Users/<USER>/ntsb/Report_CEN23LA363_192876.pdf\",\n", "    \"/Users/<USER>/ntsb/Report_CEN23LA394_192995.pdf\",\n", "    \"/Users/<USER>/ntsb/Report_ERA23LA114_106615.pdf\",\n", "    \"/Users/<USER>/ntsb/Report_WPR23LA254_192532.pdf\",\n", "]\n", "\n", "# Note: Please specify ~6 (or more!) similar files to process together as a document set\n", "#       This is currently a requirement for Doc<PERSON><PERSON> to automatically detect motifs\n", "#       across the document set to generate a semantic XML Knowledge Graph.\n", "assert len(FILE_PATHS) > 5, \"Please provide at least 6 files\"\n", "#### END DOCSET INFO\n", "\n", "dg_client = Docugami()\n", "dg_docs = upload_to_named_docset(dg_client, FILE_PATHS, DOCSET_NAME)\n", "dgml_paths = wait_for_dgml(dg_client, dg_docs)\n", "\n", "pprint(dgml_paths)"]}, {"cell_type": "markdown", "id": "01f035e5-c3f8-4d23-9d1b-8d2babdea8e9", "metadata": {}, "source": ["If you are on the free Docugami tier, your files should be done in ~15 minutes or less depending on the number of pages uploaded and available resources (please contact Docugami for paid plans for faster processing). You can re-run the code above without reprocessing your files to continue waiting if your notebook is not continuously running (it does not re-upload)."]}, {"cell_type": "markdown", "id": "7c24efa9-b6f6-4dc2-bfe3-70819ba3ef75", "metadata": {}, "source": ["### Partition PDF tables and text\n", "\n", "You can use the [Docugami Loader](https://python.langchain.com/docs/integrations/document_loaders/docugami) to very easily get chunks for your documents, including semantic and structural metadata. This is the simpler and recommended approach for most use cases but in this notebook let's explore using the `dgml-utils` library to explore the segmented output for this file in more detail by processing the XML we just downloaded above."]}, {"cell_type": "code", "execution_count": 4, "id": "05fcdd57-090f-44bf-a1fb-2c3609c80e34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 30 chunks, here are the first few\n", "<AviationInvestigationFinalReport-section>Aviation </AviationInvestigationFinalReport-section>Investigation Final Report\n", "<table><tbody><tr><td>Location: </td> <td><Location><TownName>Elbert</TownName>, <USState>Colorado </USState></Location></td> <td>Accident Number: </td> <td><AccidentNumber>CEN23LA277 </AccidentNumber></td></tr> <tr><td><LocationDateTime>Date &amp; Time: </LocationDateTime></td> <td><DateTime><EventDate>June 26, 2023</EventDate>, <EventTime>11:00 Local </EventTime></DateTime></td> <td><DateTimeAccidentNumber>Registration: </DateTimeAccidentNumber></td> <td><Registration>N23161 </Registration></td></tr> <tr><td><LocationAircraft>Aircraft: </LocationAircraft></td> <td><AircraftType>Piper <AircraftType>J3C-50 </AircraftType></AircraftType></td> <td><AircraftAccidentNumber>Aircraft Damage: </AircraftAccidentNumber></td> <td><AircraftDamage>Substantial </AircraftDamage></td></tr> <tr><td><LocationDefiningEvent>Defining Event: </LocationDefiningEvent></td> <td><DefiningEvent>Nose over/nose down </DefiningEvent></td> <td><DefiningEventAccidentNumber>Injuries: </DefiningEventAccidentNumber></td> <td><Injuries><Minor>1 </Minor>Minor </Injuries></td></tr> <tr><td><LocationFlightConductedUnder>Flight Conducted Under: </LocationFlightConductedUnder></td> <td><FlightConductedUnder><Part91-cell>Part <RegulationPart>91</RegulationPart>: General aviation - Personal </Part91-cell></FlightConductedUnder></td><td/><td><FlightConductedUnderCEN23LA277/></td></tr></tbody></table>\n", "Analysis\n", "<TakeoffAccident> <Analysis>The pilot reported that, as the tail lifted during takeoff, the airplane veered left. He attempted to correct with full right rudder and full brakes. However, the airplane subsequently nosed over resulting in substantial damage to the fuselage, lift struts, rudder, and vertical stabilizer. </Analysis></TakeoffAccident>\n", "<AircraftCondition> The pilot reported that there were no preaccident mechanical malfunctions or anomalies with the airplane that would have precluded normal operation. </AircraftCondition>\n", "<WindConditions> At about the time of the accident, wind was from <WindDirection>180</WindDirection>° at <WindConditions>5 </WindConditions>knots. The pilot decided to depart on runway <Runway>35 </Runway>due to the prevailing airport traffic. He stated that departing with “more favorable wind conditions” may have prevented the accident. </WindConditions>\n", "<ProbableCauseAndFindings-section>Probable Cause and Findings </ProbableCauseAndFindings-section>\n", "<ProbableCause> The <ProbableCause>National Transportation Safety Board </ProbableCause>determines the probable cause(s) of this accident to be: </ProbableCause>\n", "<AccidentCause> The pilot's loss of directional control during takeoff and subsequent excessive use of brakes which resulted in a nose-over. Contributing to the accident was his decision to takeoff downwind. </AccidentCause>\n", "Page 1 of <PageNumber>5 </PageNumber>\n"]}], "source": ["from pathlib import Path\n", "\n", "from dgml_utils.segmentation import get_chunks_str\n", "\n", "# Here we just read the first file, you can do the same for others\n", "dgml_path = dgml_paths[Path(FILE_PATHS[0]).name]\n", "\n", "with open(dgml_path, \"r\") as file:\n", "    contents = file.read().encode(\"utf-8\")\n", "\n", "    chunks = get_chunks_str(\n", "        contents,\n", "        include_xml_tags=True,  # Ensures Docugami XML semantic tags are included in the chunked output (set to False for text-only chunks and tables as Markdown)\n", "        max_text_length=1024 * 8,  # 8k chars are ~2k tokens for OpenAI.\n", "        # Ref: https://help.openai.com/en/articles/4936856-what-are-tokens-and-how-to-count-them\n", "    )\n", "\n", "    print(f\"found {len(chunks)} chunks, here are the first few\")\n", "    for chunk in chunks[:10]:\n", "        print(chunk.text)"]}, {"cell_type": "markdown", "id": "bfc1f2c9-e6d4-4d98-a799-6bc30bc61661", "metadata": {}, "source": ["The file processed by <PERSON><PERSON><PERSON> in the example above was [this one](https://data.ntsb.gov/carol-repgen/api/Aviation/ReportMain/GenerateNewestReport/192541/pdf) from the NTSB and you can look at the PDF side by side to compare the XML chunks above. \n", "\n", "If you want text based chunks instead, Docugami also supports those and renders tables as markdown:"]}, {"cell_type": "code", "execution_count": 5, "id": "8a4b49e0-de78-4790-a930-ad7cf324697a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["found 30 chunks, here are the first few\n", "Aviation Investigation Final Report\n", "+-------------------------+---------------------------------------+-------------------+-------------+\n", "| Location:               | Elbert , Colorado                     | Accident Number:  | CEN23LA277  |\n", "+-------------------------+---------------------------------------+-------------------+-------------+\n", "| Date & Time:            | June 26, 2023 , 11:00 Local           | Registration:     | N23161      |\n", "+-------------------------+---------------------------------------+-------------------+-------------+\n", "| Aircraft:               | Piper J3C-50                          | Aircraft Damage : | Substantial |\n", "+-------------------------+---------------------------------------+-------------------+-------------+\n", "| Defining Event:         | Nose over/nose down                   | Injuries:         | 1 Minor     |\n", "+-------------------------+---------------------------------------+-------------------+-------------+\n", "| Flight Conducted Under: | Part 91 : General aviation - Personal |                   |             |\n", "+-------------------------+---------------------------------------+-------------------+-------------+\n", "Analysis\n", "The pilot reported that, as the tail lifted during takeoff, the airplane veered left. He attempted to correct with full right rudder and full brakes. However, the airplane subsequently nosed over resulting in substantial damage to the fuselage, lift struts, rudder, and vertical stabilizer.\n", "The pilot reported that there were no preaccident mechanical malfunctions or anomalies with the airplane that would have precluded normal operation.\n", "At about the time of the accident, wind was from 180 ° at 5 knots. The pilot decided to depart on runway 35 due to the prevailing airport traffic. He stated that departing with “more favorable wind conditions” may have prevented the accident.\n", "Probable Cause and Findings\n", "The National Transportation Safety Board determines the probable cause(s) of this accident to be:\n", "The pilot's loss of directional control during takeoff and subsequent excessive use of brakes which resulted in a nose-over. Contributing to the accident was his decision to takeoff downwind.\n", "Page 1 of 5\n"]}], "source": ["with open(dgml_path, \"r\") as file:\n", "    contents = file.read().encode(\"utf-8\")\n", "\n", "    chunks = get_chunks_str(\n", "        contents,\n", "        include_xml_tags=False,  # text-only chunks and tables as Markdown\n", "        max_text_length=1024\n", "        * 8,  # 8k chars are ~2k tokens for OpenAI. Ref: https://help.openai.com/en/articles/4936856-what-are-tokens-and-how-to-count-them\n", "    )\n", "\n", "    print(f\"found {len(chunks)} chunks, here are the first few\")\n", "    for chunk in chunks[:10]:\n", "        print(chunk.text)"]}, {"cell_type": "markdown", "id": "1cfc06bc-67d2-46dd-b04d-95efa3619d0a", "metadata": {}, "source": ["## Docugami XML Deep Dive: <PERSON> NDA Example\n", "\n", "Let's explore the Docugami XML output for a different example PDF file (a long form contract): [<PERSON>DA](https://github.com/docugami/dgml-utils/blob/main/python/tests/test_data/article/Jane%20Doe%20NDA.pdf). We have provided processed Docugami XML output for this PDF here: https://github.com/docugami/dgml-utils/blob/main/python/tests/test_data/article/Jane%20Doe.xml so you can follow along without processing your own documents."]}, {"cell_type": "code", "execution_count": 6, "id": "7b697d30-1e94-47f0-87e8-f81d4b180da2", "metadata": {}, "outputs": [{"data": {"text/plain": ["39"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "\n", "# Download XML from known URL\n", "dgml = requests.get(\n", "    \"https://raw.githubusercontent.com/docugami/dgml-utils/main/python/tests/test_data/article/Jane%20Doe.xml\"\n", ").text\n", "chunks = get_chunks_str(dgml, include_xml_tags=True)\n", "len(chunks)"]}, {"cell_type": "code", "execution_count": 7, "id": "14714576-6e1d-499b-bcc8-39140bb2fd78", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'h1': 9, 'div': 12, 'p': 3, 'lim h1': 9, 'lim': 1, 'table': 1, 'h1 div': 4}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Count all the different structure categories\n", "category_counts = {}\n", "\n", "for element in chunks:\n", "    category = element.structure\n", "    if category in category_counts:\n", "        category_counts[category] += 1\n", "    else:\n", "        category_counts[category] = 1\n", "\n", "category_counts"]}, {"cell_type": "code", "execution_count": 8, "id": "5462f29e-fd59-4e0e-9493-ea3b560e523e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1 tables\n", "There are 38 text elements\n"]}], "source": ["# Tables\n", "table_elements = [c for c in chunks if \"table\" in c.structure.split()]\n", "print(f\"There are {len(table_elements)} tables\")\n", "\n", "# Text\n", "text_elements = [c for c in chunks if \"table\" not in c.structure.split()]\n", "print(f\"There are {len(text_elements)} text elements\")"]}, {"cell_type": "markdown", "id": "dc09ba64-4973-4471-9501-54294c1143fc", "metadata": {}, "source": ["The Docugami XML contains extremely detailed semantics and visual bounding boxes for all elements. The `dgml-utils` library parses text and non-text elements into formats appropriate to pass into LLMs (chunked text with XML semantic labels)"]}, {"cell_type": "code", "execution_count": 9, "id": "2b4ece00-2e43-4254-adc9-66dbb79139a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NON-DISCLOSURE AGREEMENT\n", "<MUTUALNON-DISCLOSUREAGREEMENT> This Non-Disclosure Agreement (\"Agreement\") is entered into as of <EffectiveDate>November 4, 2023 </EffectiveDate>(\"Effective Date\"), by and between: </MUTUALNON-DISCLOSUREAGREEMENT>\n", "Disclosing Party:\n", "<DisclosingParty><PrincipalPlaceofBusiness>Widget Corp.</PrincipalPlaceofBusiness>, a <USState>Delaware </USState>corporation with its principal place of business at <PrincipalPlaceofBusiness><PrincipalPlaceofBusiness> <WidgetCorpAddress>123 </WidgetCorpAddress> <PrincipalPlaceofBusiness>Innovation Drive</PrincipalPlaceofBusiness> </PrincipalPlaceofBusiness> , <PrincipalPlaceofBusiness>Techville</PrincipalPlaceofBusiness>, <USState> Delaware</USState>, <PrincipalPlaceofBusiness>12345 </PrincipalPlaceofBusiness></PrincipalPlaceofBusiness> (\"<Org> <CompanyName>Widget </CompanyName> <CorporateName>Corp.</CorporateName> </Org>\") </DisclosingParty>\n", "Receiving Party:\n", "<RecipientName><PERSON></RecipientName>, an individual residing at <RecipientAddress><RecipientAddress> <RecipientAddress>456 </RecipientAddress> <RecipientAddress>Privacy Lane</RecipientAddress> </RecipientAddress> , <RecipientAddress>Safetown</RecipientAddress>, <USState> California</USState>, <RecipientAddress>67890 </RecipientAddress></RecipientAddress> (\"Recipient\")\n", "(collectively referred to as the \"Parties\").\n", "1. Definition of Confidential Information\n", "<DefinitionofConfidentialInformation>For purposes of this Agreement, \"Confidential Information\" shall include all information or material that has or could have commercial value or other utility in the business in which Disclosing Party is engaged. If Confidential Information is in written form, the Disclosing Party shall label or stamp the materials with the word \"Confidential\" or some similar warning. If Confidential Information is transmitted orally, the Disclosing Party shall promptly provide writing indicating that such oral communication constituted Confidential Information . </DefinitionofConfidentialInformation>\n", "2. Exclusions from Confidential Information\n", "<ExclusionsFromConfidentialInformation>Recipient's obligations under this Agreement do not extend to information that is: (a) publicly known at the time of disclosure or subsequently becomes publicly known through no fault of the Recipient; (b) discovered or created by the Recipient before disclosure by Disclosing Party; (c) learned by the Recipient through legitimate means other than from the Disclosing Party or Disclosing Party's representatives; or (d) is disclosed by Recipient with Disclosing Party's prior written approval. </ExclusionsFromConfidentialInformation>\n", "3. Obligations of Receiving Party\n", "<ObligationsofReceivingParty>Recipient shall hold and maintain the Confidential Information in strictest confidence for the sole and exclusive benefit of the Disclosing Party. Recipient shall carefully restrict access to Confidential Information to employees, contractors, and third parties as is reasonably required and shall require those persons to sign nondisclosure restrictions at least as protective as those in this Agreement. </ObligationsofReceivingParty>\n", "4. Time Periods\n", "<TimePeriods>The nondisclosure provisions of this Agreement shall survive the termination of this Agreement and Recipient's duty to hold Confidential Information in confidence shall remain in effect until the Confidential Information no longer qualifies as a trade secret or until Disclosing Party sends Recipient written notice releasing <PERSON><PERSON><PERSON><PERSON> from this Agreement, whichever occurs first. </TimePeriods>\n", "5. Relationships\n", "<Relationships>Nothing contained in this Agreement shall be deemed to constitute either party a partner, joint venture, or employee of the other party for any purpose. </Relationships>\n", "6. Sever<PERSON>\n", "<Severability>If a court finds any provision of this Agreement invalid or unenforceable, the remainder of this Agreement shall be interpreted so as best to effect the intent of the parties. </Severability>\n", "7. Integration\n"]}], "source": ["for element in text_elements[:20]:\n", "    print(element.text)"]}, {"cell_type": "code", "execution_count": 10, "id": "08350119-aa22-4ec1-8f65-b1316a0d4123", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<table> <tbody> <tr> <td> Authorized Individual </td> <td> Role </td> <td>Purpose of Disclosure </td> </tr> <tr> <td> <AuthorizedIndividualJohn<PERSON><PERSON>> <Name><PERSON> </Name> </AuthorizedIndividualJohn<PERSON>mith> </td> <td> <JohnSmithRole> <ProjectManagerName>Project Manager </ProjectManagerName> </JohnSmithRole> </td> <td> <JohnSmithPurposeofDisclosure> Oversee project to which the NDA relates </JohnSmithPurposeofDisclosure> </td> </tr> <tr> <td> <AuthorizedIndividualLisaWhite> <Author><PERSON> </Author> </AuthorizedIndividualLisaWhite> </td> <td> <LisaWhiteRole> Lead Developer </LisaWhiteRole> </td> <td> <LisaWhitePurposeofDisclosure>Software development and analysis </LisaWhitePurposeofDisclosure> </td> </tr> <tr> <td> <AuthorizedIndividualMichaelBrown> <Name><PERSON> </Name> </AuthorizedIndividualMichaelBrown> </td> <td> <MichaelBrownRole> Financial <FinancialAnalyst> Analyst </FinancialAnalyst> </MichaelBrownRole> </td> <td> <MichaelBrownPurposeofDisclosure>Financial analysis and reporting </MichaelBrownPurposeofDisclosure> </td> </tr> </tbody> </table>\n"]}], "source": ["print(table_elements[0].text)"]}, {"cell_type": "markdown", "id": "dca87b46-c0c2-4973-94ec-689c18075653", "metadata": {}, "source": ["The XML markup contains structural as well as semantic tags, which provide additional semantics to the LLM for improved retrieval and generation.\n", "\n", "If you prefer, you can set `include_xml_tags=False` in the `get_chunks_str` call above to not include XML markup. The text-only Docugami chunks are still very good since they follow the structural and semantic contours of the document rather than whitespace-only chunking. Tables are rendered as markdown in this case, so that some structural context is maintained even without the XML markup."]}, {"cell_type": "code", "execution_count": 11, "id": "bcac8294-c54a-4b6e-af9d-3911a69620b2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+-----------------------+-------------------+------------------------------------------+\n", "| Authorized Individual | Role              | Purpose of Disclosure                    |\n", "+-----------------------+-------------------+------------------------------------------+\n", "| <PERSON>            | Project Manager   | Oversee project to which the NDA relates |\n", "+-----------------------+-------------------+------------------------------------------+\n", "| <PERSON>            | Lead Developer    | Software development and analysis        |\n", "+-----------------------+-------------------+------------------------------------------+\n", "| <PERSON>         | Financial Analyst | Financial analysis and reporting         |\n", "+-----------------------+-------------------+------------------------------------------+\n"]}], "source": ["chunks_as_text = get_chunks_str(dgml, include_xml_tags=False)\n", "table_elements_as_text = [c for c in chunks_as_text if \"table\" in c.structure.split()]\n", "\n", "print(table_elements_as_text[0].text)"]}, {"cell_type": "markdown", "id": "731b3dfc-7ddf-4a11-9a30-9a79b7c66e16", "metadata": {}, "source": ["## Multi-vector retriever\n", "\n", "Use [multi-vector-retriever](https://python.langchain.com/docs/modules/data_connection/retrievers/multi_vector#summary) to produce summaries of tables and, optionally, text. \n", "\n", "With the summary, we will also store the raw table elements.\n", "\n", "The summaries are used to improve the quality of retrieval, [as explained in the multi vector retriever docs](https://python.langchain.com/docs/modules/data_connection/retrievers/multi_vector).\n", "\n", "The raw tables are passed to the LLM, providing the full table context for the LLM to generate the answer.  \n", "\n", "### Summaries"]}, {"cell_type": "code", "execution_count": 12, "id": "8e275736-3408-4d7a-990e-4362c88e81f8", "metadata": {}, "outputs": [], "source": ["from langchain.prompts import (\n", "    ChatPromptTemplate,\n", "    HumanMessagePromptTemplate,\n", "    SystemMessagePromptTemplate,\n", ")\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "markdown", "id": "37b65677-aeb4-44fd-b06d-4539341ede97", "metadata": {}, "source": ["We create a simple summarize chain for each element.\n", "\n", "You can also see, re-use, or modify the prompt in the Hub [here](https://smith.langchain.com/hub/rlm/multi-vector-retriever-summarization).\n", "\n", "```\n", "from langchain import hub\n", "obj = hub.pull(\"rlm/multi-vector-retriever-summarization\")\n", "```"]}, {"cell_type": "code", "execution_count": 13, "id": "1b12536a-1303-41ad-9948-4eb5a5f32614", "metadata": {}, "outputs": [], "source": ["# Prompt\n", "prompt_text = \"\"\"You are an assistant tasked with summarizing tables and text. \\ \n", "Give a concise summary of the table or text. Table or text chunk: {element} \"\"\"\n", "prompt = ChatPromptTemplate.from_template(prompt_text)\n", "\n", "# Summary chain\n", "model = ChatOpenAI(temperature=0, model=\"gpt-4\")\n", "summarize_chain = {\"element\": lambda x: x} | prompt | model | StrOutputParser()"]}, {"cell_type": "code", "execution_count": 14, "id": "8d8b567c-b442-4bf0-b639-04bd89effc62", "metadata": {}, "outputs": [], "source": ["# Apply summarizer to tables\n", "tables = [i.text for i in table_elements]\n", "table_summaries = summarize_chain.batch(tables, {\"max_concurrency\": 5})"]}, {"cell_type": "markdown", "id": "60524010-754f-4924-ad75-78cb54ca7257", "metadata": {}, "source": ["### Add to vectorstore\n", "\n", "Use [Multi Vector Retriever](https://python.langchain.com/docs/modules/data_connection/retrievers/multi_vector#summary) with summaries: \n", "\n", "* `InMemoryStore` stores the raw text, tables\n", "* `vectorstore` stores the embedded summaries"]}, {"cell_type": "code", "execution_count": 17, "id": "346c3a02-8fea-4f75-a69e-fc9542b99dbc", "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "from langchain.retrievers.multi_vector import MultiVectorRetriever\n", "from langchain.storage import InMemoryStore\n", "from langchain_chroma import Chroma\n", "from langchain_core.documents import Document\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "\n", "def build_retriever(text_elements, tables, table_summaries):\n", "    # The vectorstore to use to index the child chunks\n", "    vectorstore = Chroma(\n", "        collection_name=\"summaries\", embedding_function=OpenAIEmbeddings()\n", "    )\n", "\n", "    # The storage layer for the parent documents\n", "    store = InMemoryStore()\n", "    id_key = \"doc_id\"\n", "\n", "    # The retriever (empty to start)\n", "    retriever = MultiVectorRetriever(\n", "        vectorstore=vectorstore,\n", "        docstore=store,\n", "        id_key=id_key,\n", "    )\n", "\n", "    # Add texts\n", "    texts = [i.text for i in text_elements]\n", "    doc_ids = [str(uuid.uuid4()) for _ in texts]\n", "    retriever.docstore.mset(list(zip(doc_ids, texts)))\n", "\n", "    # Add tables and summaries\n", "    table_ids = [str(uuid.uuid4()) for _ in tables]\n", "    summary_tables = [\n", "        Document(page_content=s, metadata={id_key: table_ids[i]})\n", "        for i, s in enumerate(table_summaries)\n", "    ]\n", "    retriever.vectorstore.add_documents(summary_tables)\n", "    retriever.docstore.mset(list(zip(table_ids, tables)))\n", "    return retriever\n", "\n", "\n", "retriever = build_retriever(text_elements, tables, table_summaries)"]}, {"cell_type": "markdown", "id": "1d8bbbd9-009b-4b34-a206-5874a60adbda", "metadata": {}, "source": ["## RAG\n", "\n", "Run [RAG pipeline](https://python.langchain.com/docs/expression_language/cookbook/retrieval)."]}, {"cell_type": "code", "execution_count": 18, "id": "f2489de4-51e3-48b4-bbcd-ed9171deadf3", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnablePassthrough\n", "\n", "system_prompt = SystemMessagePromptTemplate.from_template(\n", "    \"You are a helpful assistant that answers questions based on provided context. Your provided context can include text or tables, \"\n", "    \"and may also contain semantic XML markup. Pay attention the semantic XML markup to understand more about the context semantics as \"\n", "    \"well as structure (e.g. lists and tabular layouts expressed with HTML-like tags)\"\n", ")\n", "\n", "human_prompt = HumanMessagePromptTemplate.from_template(\n", "    \"\"\"Context:\n", "\n", "    {context}\n", "\n", "    Question: {question}\"\"\"\n", ")\n", "\n", "\n", "def build_chain(retriever, model):\n", "    prompt = ChatPromptTemplate.from_messages([system_prompt, human_prompt])\n", "\n", "    # LLM\n", "    model = ChatOpenAI(temperature=0, model=\"gpt-4\")\n", "\n", "    # RAG pipeline\n", "    chain = (\n", "        {\"context\": retriever, \"question\": RunnablePassthrough()}\n", "        | prompt\n", "        | model\n", "        | StrOutputParser()\n", "    )\n", "\n", "    return chain\n", "\n", "\n", "chain = build_chain(retriever, model)"]}, {"cell_type": "code", "execution_count": 19, "id": "636e992f-823b-496b-a082-8b4fcd479de5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 4 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The people authorized to receive confidential information and their roles are:\n", "\n", "1. <PERSON> - Project Manager\n", "2. <PERSON> - Lead Developer\n", "3. <PERSON> - Financial Analyst\n"]}], "source": ["result = chain.invoke(\n", "    \"Name all the people authorized to receive confidential information, and their roles\"\n", ")\n", "print(result)"]}, {"cell_type": "markdown", "id": "37f46054-e239-4ba8-af81-22d0d6a9bc32", "metadata": {}, "source": ["We can check the [trace](https://smith.langchain.com/public/21b3aa16-4ef3-40c3-92f6-3f0ceab2aedb/r) to see what chunks were retrieved.\n", "\n", "This includes Table 1 in the doc, showing the disclosures table as XML markup (same one as above)"]}, {"cell_type": "markdown", "id": "86cad5db-81fe-4ae6-a20e-550b85fcbe96", "metadata": {}, "source": ["# RAG on Llama2 paper\n", "\n", "Let's run the same Llama2 paper example from the [Semi_Structured_RAG.ipynb](./Semi_Structured_RAG.ipynb) notebook to see if we get the same results, and to contrast the table chunk returned by <PERSON><PERSON><PERSON> with the ones returned from Unstructured."]}, {"cell_type": "code", "execution_count": 20, "id": "0e4a2f43-dd48-4ae3-8e27-7e87d169965f", "metadata": {}, "outputs": [{"data": {"text/plain": ["669"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["dgml = requests.get(\n", "    \"https://raw.githubusercontent.com/docugami/dgml-utils/main/python/tests/test_data/arxiv/2307.09288.xml\"\n", ").text\n", "llama2_chunks = get_chunks_str(dgml, include_xml_tags=True)\n", "len(llama2_chunks)"]}, {"cell_type": "code", "execution_count": 21, "id": "56b78fb3-603d-4343-ae72-be54a3c5dd72", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 33 tables\n", "There are 636 text elements\n"]}], "source": ["# Tables\n", "llama2_table_elements = [c for c in llama2_chunks if \"table\" in c.structure.split()]\n", "print(f\"There are {len(llama2_table_elements)} tables\")\n", "\n", "# Text\n", "llama2_text_elements = [c for c in llama2_chunks if \"table\" not in c.structure.split()]\n", "print(f\"There are {len(llama2_text_elements)} text elements\")"]}, {"cell_type": "code", "execution_count": 22, "id": "d3cc5ba9-8553-4eda-a5d1-b799751186af", "metadata": {}, "outputs": [], "source": ["# Apply summarizer to tables\n", "llama2_tables = [i.text for i in llama2_table_elements]\n", "llama2_table_summaries = summarize_chain.batch(llama2_tables, {\"max_concurrency\": 5})"]}, {"cell_type": "code", "execution_count": 23, "id": "d7c73faf-74cb-400d-8059-b69e2493de38", "metadata": {}, "outputs": [], "source": ["llama2_retriever = build_retriever(\n", "    llama2_text_elements, llama2_tables, llama2_table_summaries\n", ")"]}, {"cell_type": "code", "execution_count": 24, "id": "4c553722-be42-42ce-83b8-76a17f323f1c", "metadata": {}, "outputs": [], "source": ["llama2_chain = build_chain(llama2_retriever, model)"]}, {"cell_type": "code", "execution_count": 25, "id": "65dce40b-f1c3-494a-949e-69a9c9544ddb", "metadata": {}, "outputs": [{"data": {"text/plain": ["'The number of training tokens for LLaMA2 is 2.0T for all parameter sizes.'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["llama2_chain.invoke(\"What is the number of training tokens for LLaMA2?\")"]}, {"cell_type": "markdown", "id": "59877edf-9a02-45db-95cb-b7f4234abfa3", "metadata": {}, "source": ["We can check the [trace](https://smith.langchain.com/public/5de100c3-bb40-4234-bf02-64bc708686a1/r) to see what chunks were retrieved.\n", "\n", "This includes Table 1 in the doc, showing the tokens used for training table as semantic XML markup:\n", "\n", "```xml\n", "<table>\n", "    <tbody>\n", "        <tr>\n", "            <td />\n", "            <td>Training Data </td>\n", "            <td>Params </td>\n", "            <td>Context Length </td>\n", "            <td>\n", "                <Org>GQA </Org>\n", "            </td>\n", "            <td>Tokens </td>\n", "            <td>LR </td>\n", "        </tr>\n", "        <tr>\n", "            <td>Llama <Number>1 </Number></td>\n", "            <td>\n", "                <Llama1TrainingData>See <Person>Touvron </Person>et al. (<Number>2023</Number>) </Llama1TrainingData>\n", "            </td>\n", "            <td>\n", "                <Llama1Params>\n", "                    <Number>7B </Number>\n", "                    <Number>13B </Number>\n", "                    <Number>33B </Number>\n", "                    <Number>65B </Number>\n", "                </Llama1Params>\n", "            </td>\n", "            <td>\n", "                <Llama1ContextLength>\n", "                    <Number>2k </Number>\n", "                    <Number>2k </Number>\n", "                    <Number>2k </Number>\n", "                    <Number>2k </Number>\n", "                </Llama1ContextLength>\n", "            </td>\n", "            <td>\n", "                <Llama1GQA>✗ ✗ ✗ ✗ </Llama1GQA>\n", "            </td>\n", "            <td>\n", "                <Llama1Tokens><Number>1.0</Number>T <Number>1.0</Number>T <Number>1.4</Number>T <Number>\n", "                    1.4</Number>T </Llama1Tokens>\n", "            </td>\n", "            <td>\n", "                <Llama1LR> 3.0 × <Number>10−4 </Number> 3.0 × <Number>10−4 </Number> 1.5 × <Number>\n", "                    10−4 </Number> 1.5 × <Number>10−4 </Number></Llama1LR>\n", "            </td>\n", "        </tr>\n", "        <tr>\n", "            <td>Llama <Number>2 </Number></td>\n", "            <td>\n", "                <Llama2TrainingData>A new mix of publicly available online data </Llama2TrainingData>\n", "            </td>\n", "            <td>\n", "                <Llama2Params><Number>7B </Number>13B <Number>34B </Number><Number>70B </Number></Llama2Params>\n", "            </td>\n", "            <td>\n", "                <Llama2ContextLength>\n", "                    <Number>4k </Number>\n", "                    <Number>4k </Number>\n", "                    <Number>4k </Number>\n", "                    <Number>4k </Number>\n", "                </Llama2ContextLength>\n", "            </td>\n", "            <td>\n", "                <Llama2GQA>✗ ✗ ✓ ✓ </Llama2GQA>\n", "            </td>\n", "            <td>\n", "                <Llama2Tokens><Number>2.0</Number>T <Number>2.0</Number>T <Number>2.0</Number>T <Number>\n", "                    2.0</Number>T </Llama2Tokens>\n", "            </td>\n", "            <td>\n", "                <Llama2LR> 3.0 × <Number>10−4 </Number> 3.0 × <Number>10−4 </Number> 1.5 × <Number>\n", "                    10−4 </Number> 1.5 × <Number>10−4 </Number></Llama2LR>\n", "            </td>\n", "        </tr>\n", "    </tbody>\n", "</table>\n", "```"]}, {"cell_type": "markdown", "id": "867f8e11-384c-4aa1-8b3e-c59fb8d5fd7d", "metadata": {}, "source": ["Finally, you can ask other questions that rely on more subtle parsing of the table, e.g.:"]}, {"cell_type": "code", "execution_count": 26, "id": "d38f1459-7d2b-40df-8dcd-e747f85eb144", "metadata": {}, "outputs": [{"data": {"text/plain": ["'The learning rate for LLaMA2 was 3.0 × 10−4 for the 7B and 13B models, and 1.5 × 10−4 for the 34B and 70B models.'"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["llama2_chain.invoke(\"What was the learning rate for LLaMA2?\")"]}, {"cell_type": "markdown", "id": "94826165", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> KG-RAG Template\n", "\n", "Docugami also provides a [langchain template](https://github.com/docugami/langchain-template-docugami-kg-rag) that you can integrate into your langchain projects.\n", "\n", "Here's a walkthrough of how you can do this.\n", "\n", "[![<PERSON><PERSON><PERSON>G-RAG Walkthrough](https://img.youtube.com/vi/xOHOmL1NFMg/0.jpg)](https://www.youtube.com/watch?v=xOHOmL1NFMg)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}