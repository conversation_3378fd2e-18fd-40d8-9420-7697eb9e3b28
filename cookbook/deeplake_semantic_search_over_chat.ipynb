{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# QA using Activeloop's DeepLake\n", "In this tutorial, we are going to use Langchain + Activeloop's Deep Lake with GPT4 to semantically search and ask questions over a group chat.\n", "\n", "View a working demo [here](https://twitter.com/thisissukh_/status/1647223328363679745)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 1. Install required packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python3 -m pip install --upgrade langchain 'deeplake[enterprise]' openai tiktoken"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 2. Add API keys"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "from langchain.chains import RetrievalQA\n", "from langchain_community.vectorstores import DeepLake\n", "from langchain_openai import OpenAI, OpenAIEmbeddings\n", "from langchain_text_splitters import (\n", "    CharacterTextSplitter,\n", "    RecursiveCharacterTextSplitter,\n", ")\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"OpenAI API Key:\")\n", "activeloop_token = getpass.getpass(\"Activeloop Token:\")\n", "os.environ[\"ACTIVELOOP_TOKEN\"] = activeloop_token\n", "os.environ[\"ACTIVELOOP_ORG\"] = getpass.getpass(\"Activeloop Org:\")\n", "\n", "org_id = os.environ[\"ACTIVELOOP_ORG\"]\n", "embeddings = OpenAIEmbeddings()\n", "\n", "dataset_path = \"hub://\" + org_id + \"/data\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "## 2. Create sample data"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["You can generate a sample group chat conversation using ChatGPT with this prompt:\n", "\n", "```\n", "Generate a group chat conversation with three friends talking about their day, referencing real places and fictional names. Make it funny and as detailed as possible.\n", "```\n", "\n", "I've already generated such a chat in `messages.txt`. We can keep it simple and use this for our example.\n", "\n", "## 3. Ingest chat embeddings\n", "\n", "We load the messages in the text file, chunk and upload to ActiveLoop Vector store."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Document(page_content='Participants:\\n\\nJerry: Loves movies and is a bit of a klutz.\\nSamantha: Enthusiastic about food and always trying new restaurants.\\nB<PERSON>ry: A nature lover, but always manages to get lost.\\nJerry: Hey, guys! You won\\'t believe what happened to me at the Times Square AMC theater. I tripped over my own feet and spilled popcorn everywhere! 🍿💥\\n\\nSamantha: <PERSON><PERSON>, that\\'s so you, <PERSON>! Was the floor buttery enough for you to ice skate on after that? 😂\\n\\nBarry: Sounds like a regular Tuesday for you, <PERSON>. Meanwhile, I tried to find that new hiking trail in Central Park. You know, the one that\\'s supposed to be impossible to get lost on? Well, guess what...\\n\\nJerry: You found a hidden treasure?\\n\\nBarry: No, I got lost. AGAIN. 🧭🙄\\n\\nSamantha: <PERSON>, you\\'d get lost in your own backyard! But speaking of treasures, I found this new sushi place in Little Tokyo. \"Samantha\\'s Sushi Symphony\" it\\'s called. Coincidence? I think not!\\n\\nJerry: Maybe they named it after your ability to eat your body weight in sushi. 🍣', metadata={}), Document(page_content='<PERSON>: How do you even FIND all these places, <PERSON>?\\n\\nSamantha: Simple, I don\\'t rely on <PERSON>\\'s navigation skills. 😉 But seriously, the wasabi there was hotter than <PERSON>\\'s love for Marvel movies!\\n\\nJerry: Hey, nothing wrong with a little superhero action. By the way, did you guys see the new \"Captain Crunch: Breakfast Avenger\" trailer?\\n\\nSamantha: Captain Crunch? Are you sure you didn\\'t get that from one of your Saturday morning cereal binges?\\n\\nBarry: Yeah, and did he defeat his arch-enemy, General Mills? 😆\\n\\nJerry: Ha-ha, very funny. Anyway, that sushi place sounds awesome, Samantha. Next time, let\\'s go together, and maybe Barry can guide us... if we want a city-wide tour first.\\n\\nBarry: As long as we\\'re not hiking, I\\'ll get us there... eventually. 😅\\n\\nSamantha: It\\'s a date! But Jerry, you\\'re banned from carrying any food items.\\n\\nJerry: Deal! Just promise me no wasabi challenges. I don\\'t want to end up like the time I tried Sriracha ice cream.', metadata={}), Document(page_content=\"Barry: Wait, what happened with Sriracha ice cream?\\n\\nJerry: Let's just say it was a hot situation. Literally. 🔥\\n\\nSamantha: 🤣 I still have the video!\\n\\nJerry: Samantha, if you value our friendship, that video will never see the light of day.\\n\\nSamantha: No promises, Jerry. No promises. 🤐😈\\n\\nBarry: I foresee a fun weekend ahead! 🎉\", metadata={})]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Your Deep Lake dataset has been successfully created!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\\"]}, {"name": "stdout", "output_type": "stream", "text": ["Dataset(path='hub://adilkhan/data', tensors=['embedding', 'id', 'metadata', 'text'])\n", "\n", "  tensor      htype      shape     dtype  compression\n", "  -------    -------    -------   -------  ------- \n", " embedding  embedding  (3, 1536)  float32   None   \n", "    id        text      (3, 1)      str     None   \n", " metadata     json      (3, 1)      str     None   \n", "   text       text      (3, 1)      str     None   \n"]}, {"name": "stderr", "output_type": "stream", "text": [" \r"]}], "source": ["with open(\"messages.txt\") as f:\n", "    state_of_the_union = f.read()\n", "text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)\n", "pages = text_splitter.split_text(state_of_the_union)\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=100)\n", "texts = text_splitter.create_documents(pages)\n", "\n", "print(texts)\n", "\n", "dataset_path = \"hub://\" + org_id + \"/data\"\n", "embeddings = OpenAIEmbeddings()\n", "db = DeepLake.from_documents(\n", "    texts, embeddings, dataset_path=dataset_path, overwrite=True\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["`Optional`: You can also use Deep Lake's Managed Tensor Database as a hosting service and run queries there. In order to do so, it is necessary to specify the runtime parameter as {'tensor_db': True} during the creation of the vector store. This configuration enables the execution of queries on the Managed Tensor Database, rather than on the client side. It should be noted that this functionality is not applicable to datasets stored locally or in-memory. In the event that a vector store has already been created outside of the Managed Tensor Database, it is possible to transfer it to the Managed Tensor Database by following the prescribed steps."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# with open(\"messages.txt\") as f:\n", "#     state_of_the_union = f.read()\n", "# text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)\n", "# pages = text_splitter.split_text(state_of_the_union)\n", "\n", "# text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=100)\n", "# texts = text_splitter.create_documents(pages)\n", "\n", "# print(texts)\n", "\n", "# dataset_path = \"hub://\" + org + \"/data\"\n", "# embeddings = OpenAIEmbeddings()\n", "# db = DeepLake.from_documents(\n", "#     texts, embeddings, dataset_path=dataset_path, overwrite=True, runtime={\"tensor_db\": True}\n", "# )"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 4. Ask questions\n", "\n", "Now we can ask a question and get an answer back with a semantic search:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["db = DeepLake(dataset_path=dataset_path, read_only=True, embedding=embeddings)\n", "\n", "retriever = db.as_retriever()\n", "retriever.search_kwargs[\"distance_metric\"] = \"cos\"\n", "retriever.search_kwargs[\"k\"] = 4\n", "\n", "qa = RetrievalQA.from_chain_type(\n", "    llm=OpenAI(), chain_type=\"stuff\", retriever=retriever, return_source_documents=False\n", ")\n", "\n", "# What was the restaurant the group was talking about called?\n", "query = input(\"Enter query:\")\n", "\n", "# The Hungry Lobster\n", "ans = qa({\"query\": query})\n", "\n", "print(ans)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}