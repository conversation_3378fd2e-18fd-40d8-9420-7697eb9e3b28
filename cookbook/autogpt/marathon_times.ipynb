{"cells": [{"cell_type": "markdown", "id": "14f8b67b", "metadata": {}, "source": ["## AutoGPT example finding Winning Marathon Times\n", "\n", "* Implementation of https://github.com/Significant-Gravitas/Auto-GPT \n", "* With Lang<PERSON>hain primitives (LLMs, PromptTemplates, VectorStores, Embeddings, Tools)"]}, {"cell_type": "code", "execution_count": 1, "id": "ef972313-c05a-4c49-8fd1-03e599e21033", "metadata": {"tags": []}, "outputs": [], "source": ["# !pip install bs4\n", "# !pip install nest_asyncio"]}, {"cell_type": "code", "execution_count": 2, "id": "1cff42fd", "metadata": {"tags": []}, "outputs": [], "source": ["# General\n", "import asyncio\n", "import os\n", "\n", "import nest_asyncio\n", "import pandas as pd\n", "from langchain.docstore.document import Document\n", "from langchain_experimental.agents.agent_toolkits.pandas.base import (\n", "    create_pandas_dataframe_agent,\n", ")\n", "from langchain_experimental.autonomous_agents import AutoGPT\n", "from langchain_openai import ChatOpenAI\n", "\n", "# Needed since j<PERSON><PERSON><PERSON> runs an async eventloop\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 3, "id": "01283ac7-1da0-41ba-8011-bd455d21dd82", "metadata": {"tags": []}, "outputs": [], "source": ["llm = ChatOpenAI(model=\"gpt-4\", temperature=1.0)"]}, {"cell_type": "markdown", "id": "192496a7", "metadata": {}, "source": ["### Set up tools\n", "\n", "* We'll set up an AutoGPT with a `search` tool, and `write-file` tool, and a `read-file` tool, a web browsing tool, and a tool to interact with a CSV file via a python REPL"]}, {"cell_type": "markdown", "id": "708a426f", "metadata": {}, "source": ["Define any other `tools` you want to use below:"]}, {"cell_type": "code", "execution_count": 4, "id": "cef4c150-0ef1-4a33-836b-01062fec134e", "metadata": {"tags": []}, "outputs": [], "source": ["# Tools\n", "import os\n", "from contextlib import contextmanager\n", "from typing import Optional\n", "\n", "from langchain.agents import tool\n", "from langchain_community.tools.file_management.read import ReadFileTool\n", "from langchain_community.tools.file_management.write import WriteFileTool\n", "\n", "ROOT_DIR = \"./data/\"\n", "\n", "\n", "@contextmanager\n", "def pushd(new_dir):\n", "    \"\"\"Context manager for changing the current working directory.\"\"\"\n", "    prev_dir = os.getcwd()\n", "    os.ch<PERSON>(new_dir)\n", "    try:\n", "        yield\n", "    finally:\n", "        os.chdir(prev_dir)\n", "\n", "\n", "@tool\n", "def process_csv(\n", "    csv_file_path: str, instructions: str, output_path: Optional[str] = None\n", ") -> str:\n", "    \"\"\"Process a CSV by with pandas in a limited REPL.\\\n", " Only use this after writing data to disk as a csv file.\\\n", " Any figures must be saved to disk to be viewed by the human.\\\n", " Instructions should be written in natural language, not code. Assume the dataframe is already loaded.\"\"\"\n", "    with pushd(ROOT_DIR):\n", "        try:\n", "            df = pd.read_csv(csv_file_path)\n", "        except Exception as e:\n", "            return f\"Error: {e}\"\n", "        agent = create_pandas_dataframe_agent(llm, df, max_iterations=30, verbose=True)\n", "        if output_path is not None:\n", "            instructions += f\" Save output to disk at {output_path}\"\n", "        try:\n", "            result = agent.run(instructions)\n", "            return result\n", "        except Exception as e:\n", "            return f\"Error: {e}\""]}, {"cell_type": "markdown", "id": "69975008-654a-4cbb-bdf6-63c8bae07eaa", "metadata": {"tags": []}, "source": ["**Browse a web page with PlayWright**"]}, {"cell_type": "code", "execution_count": 5, "id": "6bb5e47b-0f54-4faa-ae42-49a28fa5497b", "metadata": {"tags": []}, "outputs": [], "source": ["# !pip install playwright\n", "# !playwright install"]}, {"cell_type": "code", "execution_count": 6, "id": "26b497d7-8e52-4c7f-8e7e-da0a48820a3c", "metadata": {"tags": []}, "outputs": [], "source": ["async def async_load_playwright(url: str) -> str:\n", "    \"\"\"Load the specified URLs using <PERSON><PERSON> and parse using BeautifulSoup.\"\"\"\n", "    from bs4 import BeautifulSoup\n", "    from playwright.async_api import async_playwright\n", "\n", "    results = \"\"\n", "    async with async_playwright() as p:\n", "        browser = await p.chromium.launch(headless=True)\n", "        try:\n", "            page = await browser.new_page()\n", "            await page.goto(url)\n", "\n", "            page_source = await page.content()\n", "            soup = BeautifulSoup(page_source, \"html.parser\")\n", "\n", "            for script in soup([\"script\", \"style\"]):\n", "                script.extract()\n", "\n", "            text = soup.get_text()\n", "            lines = (line.strip() for line in text.splitlines())\n", "            chunks = (phrase.strip() for line in lines for phrase in line.split(\"  \"))\n", "            results = \"\\n\".join(chunk for chunk in chunks if chunk)\n", "        except Exception as e:\n", "            results = f\"Error: {e}\"\n", "        await browser.close()\n", "    return results\n", "\n", "\n", "def run_async(coro):\n", "    event_loop = asyncio.get_event_loop()\n", "    return event_loop.run_until_complete(coro)\n", "\n", "\n", "@tool\n", "def browse_web_page(url: str) -> str:\n", "    \"\"\"Verbose way to scrape a whole webpage. Likely to cause issues parsing.\"\"\"\n", "    return run_async(async_load_playwright(url))"]}, {"cell_type": "markdown", "id": "5ea71762-67ca-4e75-8c4d-00563064be71", "metadata": {}, "source": ["**Q&A Over a webpage**\n", "\n", "Help the model ask more directed questions of web pages to avoid cluttering its memory"]}, {"cell_type": "code", "execution_count": 7, "id": "1842929d-f18d-4edc-9fdd-82c929181141", "metadata": {"tags": []}, "outputs": [], "source": ["from langchain.chains.qa_with_sources.loading import (\n", "    BaseCombineDocumentsChain,\n", "    load_qa_with_sources_chain,\n", ")\n", "from langchain.tools import BaseTool, DuckDuckGoSearchRun\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from pydantic import Field\n", "\n", "\n", "def _get_text_splitter():\n", "    return RecursiveCharacterTextSplitter(\n", "        # Set a really small chunk size, just to show.\n", "        chunk_size=500,\n", "        chunk_overlap=20,\n", "        length_function=len,\n", "    )\n", "\n", "\n", "class WebpageQATool(BaseTool):\n", "    name = \"query_webpage\"\n", "    description = (\n", "        \"Browse a webpage and retrieve the information relevant to the question.\"\n", "    )\n", "    text_splitter: RecursiveCharacterTextSplitter = Field(\n", "        default_factory=_get_text_splitter\n", "    )\n", "    qa_chain: BaseCombineDocumentsChain\n", "\n", "    def _run(self, url: str, question: str) -> str:\n", "        \"\"\"Useful for browsing websites and scraping the text information.\"\"\"\n", "        result = browse_web_page.run(url)\n", "        docs = [Document(page_content=result, metadata={\"source\": url})]\n", "        web_docs = self.text_splitter.split_documents(docs)\n", "        results = []\n", "        # TODO: Handle this with a MapReduceChain\n", "        for i in range(0, len(web_docs), 4):\n", "            input_docs = web_docs[i : i + 4]\n", "            window_result = self.qa_chain(\n", "                {\"input_documents\": input_docs, \"question\": question},\n", "                return_only_outputs=True,\n", "            )\n", "            results.append(f\"Response from window {i} - {window_result}\")\n", "        results_docs = [\n", "            Document(page_content=\"\\n\".join(results), metadata={\"source\": url})\n", "        ]\n", "        return self.qa_chain(\n", "            {\"input_documents\": results_docs, \"question\": question},\n", "            return_only_outputs=True,\n", "        )\n", "\n", "    async def _arun(self, url: str, question: str) -> str:\n", "        raise NotImplementedError"]}, {"cell_type": "code", "execution_count": 8, "id": "e6f72bd0", "metadata": {"tags": []}, "outputs": [], "source": ["query_website_tool = WebpageQATool(qa_chain=load_qa_with_sources_chain(llm))"]}, {"cell_type": "markdown", "id": "8e39ee28", "metadata": {}, "source": ["### Set up memory\n", "\n", "* The memory here is used for the agents intermediate steps"]}, {"cell_type": "code", "execution_count": 9, "id": "1df7b724", "metadata": {"tags": []}, "outputs": [], "source": ["# Memory\n", "import faiss\n", "from langchain.docstore import InMemoryDocstore\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "embeddings_model = OpenAIEmbeddings()\n", "embedding_size = 1536\n", "index = faiss.IndexFlatL2(embedding_size)\n", "vectorstore = FAISS(embeddings_model.embed_query, index, InMemoryDocstore({}), {})"]}, {"cell_type": "markdown", "id": "e40fd657", "metadata": {}, "source": ["### Setup model and AutoGPT\n", "\n", "`Model set-up`"]}, {"cell_type": "code", "execution_count": 10, "id": "1233caf3-fbc9-4acb-9faa-01008200633d", "metadata": {}, "outputs": [], "source": ["# !pip install duckduckgo_search\n", "web_search = DuckDuckGoSearchRun()"]}, {"cell_type": "code", "execution_count": 11, "id": "88c8b184-67d7-4c35-84ae-9b14bef8c4e3", "metadata": {"tags": []}, "outputs": [], "source": ["tools = [\n", "    web_search,\n", "    WriteFileTool(root_dir=\"./data\"),\n", "    ReadFileTool(root_dir=\"./data\"),\n", "    process_csv,\n", "    query_website_tool,\n", "    # HumanInputRun(), # Activate if you want the permit asking for help from the human\n", "]"]}, {"cell_type": "code", "execution_count": 12, "id": "709c08c2", "metadata": {"tags": []}, "outputs": [], "source": ["agent = AutoGPT.from_llm_and_tools(\n", "    ai_name=\"<PERSON>\",\n", "    ai_role=\"Assistant\",\n", "    tools=tools,\n", "    llm=llm,\n", "    memory=vectorstore.as_retriever(search_kwargs={\"k\": 8}),\n", "    # human_in_the_loop=True, # Set to True if you want to add feedback at each step.\n", ")\n", "# agent.chain.verbose = True"]}, {"cell_type": "markdown", "id": "fc9b51ba", "metadata": {}, "source": ["### AutoGPT for Querying the Web\n", " \n", "  \n", "I've spent a lot of time over the years crawling data sources and cleaning data. Let's see if AutoGPT can help with this!\n", "\n", "Here is the prompt for looking up recent boston marathon times and converting them to tabular form."]}, {"cell_type": "code", "execution_count": 13, "id": "64455d70-a134-4d11-826a-33e34c2ce287", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"thoughts\": {\n", "        \"text\": \"I need to find the winning Boston Marathon times for the past 5 years. I can use the DuckDuckGo Search command to search for this information.\",\n", "        \"reasoning\": \"Using DuckDuckGo Search will help me gather information on the winning times without complications.\",\n", "        \"plan\": \"- Use DuckDuckGo Search to find the winning Boston Marathon times\\n- Generate a table with the year, name, country of origin, and times\\n- Ensure there are no legal complications\",\n", "        \"criticism\": \"None\",\n", "        \"speak\": \"I will use the DuckDuckGo Search command to find the winning Boston Marathon times for the past 5 years.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"DuckDuckGo Search\",\n", "        \"args\": {\n", "            \"query\": \"winning Boston Marathon times for the past 5 years ending in 2022\"\n", "        }\n", "    }\n", "}\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"The DuckDuckGo Search command did not provide the specific information I need. I must switch my approach and use query_webpage command to browse a webpage containing the Boston Marathon winning times for the past 5 years.\",\n", "        \"reasoning\": \"The query_webpage command may give me more accurate and comprehensive results compared to the search command.\",\n", "        \"plan\": \"- Use query_webpage command to find the winning Boston Marathon times\\n- Generate a table with the year, name, country of origin, and times\\n- Ensure there are no legal complications\",\n", "        \"criticism\": \"I may face difficulty in finding the right webpage with the desired information.\",\n", "        \"speak\": \"I will use the query_webpage command to find the winning Boston Marathon times for the past 5 years.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"DuckDuckGo Search\",\n", "        \"args\": {\n", "            \"query\": \"site with winning Boston Marathon times for the past 5 years ending in 2022\"\n", "        }\n", "    }\n", "}\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I need to use the query_webpage command to find the information about the winning Boston Marathon times for the past 5 years.\",\n", "        \"reasoning\": \"The previous DuckDuckGo Search command did not provide specific enough results. The query_webpage command might give more accurate and comprehensive results.\",\n", "        \"plan\": \"- Use query_webpage command to find the winning Boston Marathon times\\\\n- Generate a table with the year, name, country of origin, and times\\\\n- Ensure there are no legal complications\",\n", "        \"criticism\": \"I may face difficulty in finding the right webpage with the desired information.\",\n", "        \"speak\": \"I will use the query_webpage command to find the winning Boston Marathon times for the past 5 years.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"query_webpage\",\n", "        \"args\": {\n", "            \"url\": \"https://en.wikipedia.org/wiki/List_of_winners_of_the_Boston_Marathon\",\n", "            \"question\": \"What were the winning Boston Marathon times for the past 5 years ending in 2022?\"\n", "        }\n", "    }\n", "}\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I have already found the winning Boston Marathon times for the past 5 years. Now, I need to generate a table with the information.\",\n", "        \"reasoning\": \"Using the information I already have, I can create a table containing year, name, country of origin, and times.\",\n", "        \"plan\": \"- Write the marathon data to a CSV file\\n- Process the CSV file to display the table\",\n", "        \"criticism\": \"None\",\n", "        \"speak\": \"I will generate a table with the year, name, country of origin, and times for the winning Boston Marathon times for the past 5 years.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"write_file\",\n", "        \"args\": {\n", "            \"file_path\": \"boston_marathon_winners.csv\",\n", "            \"text\": \"Year,Name,Country,Time\\n2022,<PERSON>,<PERSON><PERSON>,2:06:51\\n2021,<PERSON>,<PERSON><PERSON>,2:09:51\\n2019,<PERSON>,<PERSON><PERSON>,2:07:57\\n2018,<PERSON><PERSON>,<PERSON><PERSON>,2:15:58\"\n", "        }\n", "    }\n", "}\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I have retrieved the winning Boston Marathon times for the past 5 years. Now, I need to generate a table with the year, name, country of origin, and times.\",\n", "        \"reasoning\": \"Creating a table will help organize the data in a clear and accessible format.\",\n", "        \"plan\": \"- Write the data to a CSV file\\n- Process the CSV file to generate the table\\n- Complete the task\",\n", "        \"criticism\": \"None\",\n", "        \"speak\": \"I will generate a table with the year, name, country of origin, and winning times using the recently retrieved data.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"write_file\",\n", "        \"args\": {\n", "            \"file_path\": \"winning_boston_marathon_data.csv\",\n", "            \"text\": \"Year,Name,Country,Time\\n2022,<PERSON>,<PERSON><PERSON>,2:06:51\\n2021,<PERSON>,<PERSON><PERSON>,2:09:51\\n2019,<PERSON>,<PERSON><PERSON>,2:07:57\\n2018,<PERSON><PERSON>,<PERSON><PERSON>,2:15:58\\n\"\n", "        }\n", "    }\n", "}\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I have found the winning Boston Marathon times for the past five years ending in 2022. Next, I need to create a table with the year, name, country of origin, and times.\",\n", "        \"reasoning\": \"Generating a table will help organize the information in a structured format.\",\n", "        \"plan\": \"- Create a table with the year, name, country of origin, and times\\n- Ensure there are no legal complications\",\n", "        \"criticism\": \"None\",\n", "        \"speak\": \"I will generate a table with the winning Boston Marathon times for the past 5 years ending in 2022.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"write_file\",\n", "        \"args\": {\n", "            \"file_path\": \"winning_times.csv\",\n", "            \"text\": \"Year,Name,Country,Time\\n2022,<PERSON>,Kenya,2:06:51\\n2021,<PERSON>,Kenya,2:09:51\\n2020,Canceled due to COVID-19 pandemic,,\\n2019,<PERSON>,Kenya,2:07:57\\n2018,<PERSON><PERSON>,Japan,2:15:58\"\n", "        }\n", "    }\n", "}\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I need to process the CSV file to generate the table with the year, name, country of origin, and winning times.\",\n", "        \"reasoning\": \"I have already written the data to a file named 'winning_times.csv'. Now, I need to process this CSV file to properly display the data as a table.\",\n", "        \"plan\": \"- Use the process_csv command to read the 'winning_times.csv' file and generate the table\",\n", "        \"criticism\": \"None\",\n", "        \"speak\": \"I will process the 'winning_times.csv' file to display the table with the winning Boston Marathon times for the past 5 years.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"process_csv\",\n", "        \"args\": {\n", "            \"csv_file_path\": \"winning_times.csv\",\n", "            \"instructions\": \"Read the CSV file and display the data as a table\"\n", "        }\n", "    }\n", "}\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: The CSV file has already been read and saved into a pandas dataframe called `df`. Hence, I can simply display the data by printing the whole dataframe. Since `df.head()` returns the first 5 rows, I can use that to showcase the contents.\n", "\n", "Action: python_repl_ast\n", "Action Input: print(df.head())\u001b[0m   Year                               Name Country     Time\n", "0  2022                       <PERSON>  2:06:51\n", "1  2021                     <PERSON>  2:09:51\n", "2  2020  Canceled due to COVID-19 pandemic     NaN      NaN\n", "3  2019                   <PERSON>  2:07:57\n", "4  2018                      <PERSON><PERSON>  2:15:58\n", "\n", "Observation: \u001b[36;1m\u001b[1;3mNone\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3mI used the wrong tool to perform the action. I should have used the given data and not interacted with the Python shell. I can now provide the displayed data as the answer since the information in the printed dataframe would look like a table when typed as text.\n", "\n", "Final Answer: \n", "   Year                               Name Country     Time\n", "0  2022                       <PERSON>  2:06:51\n", "1  2021                     <PERSON>  2:09:51\n", "2  2020  Canceled due to COVID-19 pandemic     NaN      NaN\n", "3  2019                   <PERSON>  2:07:57\n", "4  2018                      <PERSON><PERSON>  2:15:58\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I already have the winning Boston Marathon times for the past 5 years saved in the file 'winning_times.csv'. Now, I need to process the CSV and display the table.\",\n", "        \"reasoning\": \"I am choosing the process_csv command because I already have the required data saved as a CSV file, and I can use this command to read and display the data as a table.\",\n", "        \"plan\": \"- Use the process_csv command to read the 'winning_times.csv' file and generate the table\",\n", "        \"criticism\": \"None\",\n", "        \"speak\": \"I will process the 'winning_times.csv' file to display the table with the winning Boston Marathon times for the past 5 years.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"process_csv\",\n", "        \"args\": {\n", "            \"csv_file_path\": \"winning_times.csv\",\n", "            \"instructions\": \"Read the CSV file and display the data as a table\"\n", "        }\n", "    }\n", "}\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: Since the data is already loaded in a pandas dataframe, I just need to display the top rows of the dataframe.\n", "Action: python_repl_ast\n", "Action Input: df.head()\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3m   Year                               Name Country     Time\n", "0  2022                       <PERSON>  2:06:51\n", "1  2021                     <PERSON>  2:09:51\n", "2  2020  Canceled due to COVID-19 pandemic     NaN      NaN\n", "3  2019                   <PERSON>  2:07:57\n", "4  2018                      <PERSON><PERSON>  2:15:58\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3mI now know the final answer.\n", "Final Answer: \n", "   Year                               Name Country     Time\n", "0  2022                       <PERSON>  2:06:51\n", "1  2021                     <PERSON>  2:09:51\n", "2  2020  Canceled due to COVID-19 pandemic     NaN      NaN\n", "3  2019                   <PERSON>  2:07:57\n", "4  2018                      <PERSON><PERSON>  2:15:58\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I have already generated a table with the winning Boston Marathon times for the past 5 years. Now, I can finish the task.\",\n", "        \"reasoning\": \"I have completed the required actions and obtained the desired data. The task is complete.\",\n", "        \"plan\": \"- Use the finish command\",\n", "        \"criticism\": \"None\",\n", "        \"speak\": \"I have generated the table with the winning Boston Marathon times for the past 5 years. Task complete.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"finish\",\n", "        \"args\": {\n", "            \"response\": \"I have generated the table with the winning Boston Marathon times for the past 5 years. Task complete.\"\n", "        }\n", "    }\n", "}\n"]}, {"data": {"text/plain": ["'I have generated the table with the winning Boston Marathon times for the past 5 years. Task complete.'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["agent.run(\n", "    [\n", "        \"What were the winning boston marathon times for the past 5 years (ending in 2022)? Generate a table of the year, name, country of origin, and times.\"\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a6b4f96e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 5}