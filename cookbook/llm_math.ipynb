{"cells": [{"cell_type": "markdown", "id": "e71e720f", "metadata": {}, "source": ["# Math chain\n", "\n", "This notebook showcases using LLMs and Python REPLs to do complex word math problems."]}, {"cell_type": "code", "execution_count": 4, "id": "44e9ba31", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new LLMMathChain chain...\u001b[0m\n", "What is 13 raised to the .3432 power?\u001b[32;1m\u001b[1;3m\n", "```text\n", "13 ** .3432\n", "```\n", "...numexpr.evaluate(\"13 ** .3432\")...\n", "\u001b[0m\n", "Answer: \u001b[33;1m\u001b[1;3m2.4116004626599237\u001b[0m\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'Answer: 2.4116004626599237'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chains import LLMMathChain\n", "from langchain_openai import OpenAI\n", "\n", "llm = OpenAI(temperature=0)\n", "llm_math = LLMMathChain.from_llm(llm, verbose=True)\n", "\n", "llm_math.invoke(\"What is 13 raised to the .3432 power?\")"]}, {"cell_type": "code", "execution_count": null, "id": "e978bb8e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}