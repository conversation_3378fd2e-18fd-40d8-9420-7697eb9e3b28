{"cells": [{"cell_type": "markdown", "id": "4153b116-206b-40f8-a684-bf082c5ebcea", "metadata": {}, "source": ["# Building a data analyst agent with LangGraph and Azure Container Apps dynamic sessions\n", "\n", "In this example we'll build an agent that can query a Postgres database and run Python code to analyze the retrieved data. We'll use [LangGraph](https://langchain-ai.github.io/langgraph/) for agent orchestration and [Azure Container Apps dynamic sessions](https://python.langchain.com/v0.2/docs/integrations/tools/azure_dynamic_sessions/) for safe Python code execution.\n", "\n", "**NOTE**: Building LLM systems that interact with SQL databases requires executing model-generated SQL queries. There are inherent risks in doing this. Make sure that your database connection permissions are always scoped as narrowly as possible for your agent's needs. This will mitigate though not eliminate the risks of building a model-driven system. For more on general security best practices, see our [security guidelines](https://python.langchain.com/v0.2/docs/security/)."]}, {"cell_type": "markdown", "id": "3b70c2be-1141-4107-80db-787f7935102f", "metadata": {}, "source": ["## Setup\n", "\n", "Let's get set up by installing our Python dependencies and setting our OpenAI credentials, Azure Container Apps sessions pool endpoint, and our SQL database connection string.\n", "\n", "### Install dependencies"]}, {"cell_type": "code", "execution_count": 20, "id": "302f827f-062c-4b83-8239-07b28bfc9651", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install -qU langgraph langchain-azure-dynamic-sessions langchain-openai langchain-community pandas matplotlib"]}, {"cell_type": "markdown", "id": "7621655b-605c-4690-8ee1-77a4bab8b383", "metadata": {}, "source": ["### Set credentials\n", "\n", "By default this demo uses:\n", "- Azure OpenAI for the model: https://learn.microsoft.com/en-us/azure/ai-services/openai/how-to/create-resource\n", "- Azure PostgreSQL for the db: https://learn.microsoft.com/en-us/cli/azure/postgres/server?view=azure-cli-latest#az-postgres-server-create\n", "- Azure Container Apps dynamic sessions for code execution: https://learn.microsoft.com/en-us/azure/container-apps/sessions-code-interpreter?\n", "\n", "This LangGraph architecture can also be used with any other [tool-calling LLM](https://python.langchain.com/v0.2/docs/how_to/tool_calling) and any SQL database."]}, {"cell_type": "code", "execution_count": 21, "id": "be7c74d8-485b-4c51-aded-07e8af838efe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Azure OpenAI API key ········\n", "Azure OpenAI endpoint ········\n", "Azure OpenAI deployment name ········\n", "Azure Container Apps dynamic sessions pool management endpoint ········\n", "PostgreSQL connection string ········\n"]}], "source": ["import getpass\n", "import os\n", "\n", "os.environ[\"AZURE_OPENAI_API_KEY\"] = getpass.getpass(\"Azure OpenAI API key\")\n", "os.environ[\"AZURE_OPENAI_ENDPOINT\"] = getpass.getpass(\"Azure OpenAI endpoint\")\n", "\n", "AZURE_OPENAI_DEPLOYMENT_NAME = getpass.getpass(\"Azure OpenAI deployment name\")\n", "SESSIONS_POOL_MANAGEMENT_ENDPOINT = getpass.getpass(\n", "    \"Azure Container Apps dynamic sessions pool management endpoint\"\n", ")\n", "SQL_DB_CONNECTION_STRING = getpass.getpass(\"PostgreSQL connection string\")"]}, {"cell_type": "markdown", "id": "3712a7b0-3f7d-4d90-9319-febf7b046aa6", "metadata": {}, "source": ["### Imports"]}, {"cell_type": "code", "execution_count": 22, "id": "09c0a46e-a8b4-44e3-8d90-2e5d0f66c1ad", "metadata": {}, "outputs": [], "source": ["import ast\n", "import base64\n", "import io\n", "import json\n", "import operator\n", "from functools import partial\n", "from typing import Annotated, List, Literal, Optional, Sequence, TypedDict\n", "\n", "import pandas as pd\n", "from IPython.display import display\n", "from langchain_azure_dynamic_sessions import SessionsPythonREPLTool\n", "from langchain_community.utilities import SQLDatabase\n", "from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, ToolMessage\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.tools import tool\n", "from langchain_openai import AzureChatOpenAI\n", "from langgraph.graph import END, StateGraph\n", "from langgraph.prebuilt import ToolNode\n", "from matplotlib.pyplot import imshow\n", "from PIL import Image"]}, {"cell_type": "markdown", "id": "5cc14582-313c-4a61-be5e-a7a1ba26a6e0", "metadata": {}, "source": ["## Instantiate model, DB, code interpreter\n", "\n", "We'll use the LangChain [SQLDatabase](https://python.langchain.com/v0.2/api_reference/community/utilities/langchain_community.utilities.sql_database.SQLDatabase.html#langchain_community.utilities.sql_database.SQLDatabase) interface to connect to our DB and query it. This works with any SQL database supported by [SQLAlchemy](https://www.sqlalchemy.org/)."]}, {"cell_type": "code", "execution_count": 23, "id": "9262ea34-c6ac-407c-96c3-aa5eaa1a8039", "metadata": {}, "outputs": [], "source": ["db = SQLDatabase.from_uri(SQL_DB_CONNECTION_STRING)"]}, {"cell_type": "markdown", "id": "1982c6f2-aa4e-4842-83f2-951205aa0854", "metadata": {}, "source": ["For our LLM we need to make sure that we use a model that supports [tool-calling](https://python.langchain.com/v0.2/docs/how_to/tool_calling)."]}, {"cell_type": "code", "execution_count": 24, "id": "ba6201a1-d760-45f1-b14a-bf8d85ceb775", "metadata": {}, "outputs": [], "source": ["llm = AzureChatOpenAI(\n", "    deployment_name=AZURE_OPENAI_DEPLOYMENT_NAME, openai_api_version=\"2024-02-01\"\n", ")"]}, {"cell_type": "markdown", "id": "92e2fcc7-812a-4d18-852f-2f814559b415", "metadata": {}, "source": ["And the [dynamic sessions tool](https://python.langchain.com/v0.2/docs/integrations/tools/azure_container_apps_dynamic_sessions/) is what we'll use for code execution."]}, {"cell_type": "code", "execution_count": 25, "id": "89e5a315-c964-493d-84fb-1f453909caae", "metadata": {}, "outputs": [], "source": ["repl = SessionsPythonREPLTool(\n", "    pool_management_endpoint=SESSIONS_POOL_MANAGEMENT_ENDPOINT\n", ")"]}, {"cell_type": "markdown", "id": "ee084fbd-10d3-4328-9d8c-75ffa9437b31", "metadata": {}, "source": ["## Define graph\n", "\n", "Now we're ready to define our application logic. The core elements are the [agent State, Nodes, and Edges](https://langchain-ai.github.io/langgraph/concepts/#core-design).\n", "\n", "### Define State\n", "We'll use a simple agent State which is just a list of messages that every Node can append to:"]}, {"cell_type": "code", "execution_count": 31, "id": "7feef65d-bf11-41bb-9164-5249953eb02e", "metadata": {}, "outputs": [], "source": ["class AgentState(TypedDict):\n", "    messages: Annotated[Sequence[BaseMessage], operator.add]"]}, {"cell_type": "markdown", "id": "58fe92a3-9a30-464b-bcf3-972af5b92e40", "metadata": {}, "source": ["Since our code interpreter can return results like base64-encoded images which we don't want to pass back to the model, we'll create a custom Tool message that allows us to track raw Tool outputs without sending them back to the model."]}, {"cell_type": "code", "execution_count": 32, "id": "36e2d8a2-8881-40bc-81da-b40e8a152d9d", "metadata": {}, "outputs": [], "source": ["class RawToolMessage(ToolMessage):\n", "    \"\"\"\n", "    Customized Tool message that lets us pass around the raw tool outputs (along with string contents for passing back to the model).\n", "    \"\"\"\n", "\n", "    raw: dict\n", "    \"\"\"Arbitrary (non-string) tool outputs. Won't be sent to model.\"\"\"\n", "    tool_name: str\n", "    \"\"\"Name of tool that generated output.\"\"\""]}, {"cell_type": "markdown", "id": "ad1b681c-c918-4dfe-b671-9d6eee457a51", "metadata": {}, "source": ["### Define Nodes"]}, {"cell_type": "markdown", "id": "966aeec1-b930-442c-9ba3-d8ad3800d2a4", "metadata": {}, "source": ["First we'll define a node for calling our model. We need to make sure to bind our tools to the model so that it knows to call them. We'll also specify in our prompt the schema of the SQL tables the model has access to, so that it can write relevant SQL queries."]}, {"cell_type": "markdown", "id": "88f15581-11f6-4421-aa17-5762a84c8032", "metadata": {}, "source": ["We'll use our models tool-calling abilities to reliably generate our SQL queries and Python code. To do this we need to define schemas for our tools that the model can use for structuring its tool calls.\n", "\n", "Note that the class names, docstrings, and attribute typing and descriptions are crucial here, as they're actually passed in to the model (you can effectively think of them as part of the prompt)."]}, {"cell_type": "code", "execution_count": 33, "id": "390f170b-ba13-41fc-8c9b-ee0efdb13b98", "metadata": {}, "outputs": [], "source": ["# Tool schema for querying SQL db\n", "class create_df_from_sql(BaseModel):\n", "    \"\"\"Execute a PostgreSQL SELECT statement and use the results to create a DataFrame with the given column names.\"\"\"\n", "\n", "    select_query: str = Field(..., description=\"A PostgreSQL SELECT statement.\")\n", "    # We're going to convert the results to a Pandas DataFrame that we pass\n", "    # to the code intepreter, so we also have the model generate useful column and\n", "    # variable names for this DataFrame that the model will refer to when writing\n", "    # python code.\n", "    df_columns: List[str] = Field(\n", "        ..., description=\"Ordered names to give the DataFrame columns.\"\n", "    )\n", "    df_name: str = Field(\n", "        ..., description=\"The name to give the DataFrame variable in downstream code.\"\n", "    )\n", "\n", "\n", "# Tool schema for writing Python code\n", "class python_shell(BaseModel):\n", "    \"\"\"Execute Python code that analyzes the DataFrames that have been generated. Make sure to print any important results.\"\"\"\n", "\n", "    code: str = Field(\n", "        ...,\n", "        description=\"The code to execute. Make sure to print any important results.\",\n", "    )"]}, {"cell_type": "code", "execution_count": 34, "id": "a98cf69a-e25b-4016-a565-aa16e43e417a", "metadata": {}, "outputs": [], "source": ["system_prompt = f\"\"\"\\\n", "You are an expert at PostgreSQL and Python. You have access to a PostgreSQL database \\\n", "with the following tables\n", "\n", "{db.table_info}\n", "\n", "Given a user question related to the data in the database, \\\n", "first get the relevant data from the table as a DataFrame using the create_df_from_sql tool. Then use the \\\n", "python_shell to do any analysis required to answer the user question.\"\"\"\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system_prompt),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "\n", "def call_model(state: AgentState) -> dict:\n", "    \"\"\"Call model with tools passed in.\"\"\"\n", "    messages = []\n", "\n", "    chain = prompt | llm.bind_tools([create_df_from_sql, python_shell])\n", "    messages.append(chain.invoke({\"messages\": state[\"messages\"]}))\n", "\n", "    return {\"messages\": messages}"]}, {"cell_type": "markdown", "id": "4e87c72e-7f9e-4377-94c9-abd9fb869866", "metadata": {}, "source": ["Now we can define the node for executing any SQL queries that were generated by the model. Notice that after we run the query we convert the results into Pandas DataFrames — these will be uploaded the the code interpreter tool in the next step so that it can use the retrieved data."]}, {"cell_type": "code", "execution_count": 35, "id": "a229efba-e981-4403-a37c-ab030c929ea4", "metadata": {}, "outputs": [], "source": ["def execute_sql_query(state: AgentState) -> dict:\n", "    \"\"\"Execute the latest SQL queries.\"\"\"\n", "    messages = []\n", "\n", "    for tool_call in state[\"messages\"][-1].tool_calls:\n", "        if tool_call[\"name\"] != \"create_df_from_sql\":\n", "            continue\n", "\n", "        # Execute SQL query\n", "        res = db.run(tool_call[\"args\"][\"select_query\"], fetch=\"cursor\").fetchall()\n", "\n", "        # Convert result to Pandas DataFrame\n", "        df_columns = tool_call[\"args\"][\"df_columns\"]\n", "        df = pd.DataFrame(res, columns=df_columns)\n", "        df_name = tool_call[\"args\"][\"df_name\"]\n", "\n", "        # Add tool output message\n", "        messages.append(\n", "            RawToolMessage(\n", "                f\"Generated dataframe {df_name} with columns {df_columns}\",  # What's sent to model.\n", "                raw={df_name: df},\n", "                tool_call_id=tool_call[\"id\"],\n", "                tool_name=tool_call[\"name\"],\n", "            )\n", "        )\n", "\n", "    return {\"messages\": messages}"]}, {"cell_type": "markdown", "id": "7a67eaaf-1587-4f32-ab5c-e1a04d273c3e", "metadata": {}, "source": ["Now we need a node for executing any model-generated Python code. The key steps here are:\n", "- Uploading queried data to the code intepreter\n", "- Executing model generated code\n", "- Parsing results so that images are displayed and not passed in to future model calls\n", "\n", "To upload the queried data to the model we can take our DataFrames we generated by executing the SQL queries and upload them as CSVs to our code intepreter."]}, {"cell_type": "code", "execution_count": 36, "id": "450c1dd0-4fe4-4ab7-b1d7-e012c3cf0102", "metadata": {}, "outputs": [], "source": ["def _upload_dfs_to_repl(state: AgentState) -> str:\n", "    \"\"\"\n", "    Upload generated dfs to code intepreter and return code for loading them.\n", "\n", "    Note that code intepreter sessions are short-lived so this needs to be done\n", "    every agent cycle, even if the dfs were previously uploaded.\n", "    \"\"\"\n", "    df_dicts = [\n", "        msg.raw\n", "        for msg in state[\"messages\"]\n", "        if isinstance(msg, RawToolMessage) and msg.tool_name == \"create_df_from_sql\"\n", "    ]\n", "    name_df_map = {name: df for df_dict in df_dicts for name, df in df_dict.items()}\n", "\n", "    # Data should be uploaded as a BinaryIO.\n", "    # Files will be uploaded to the \"/mnt/data/\" directory on the container.\n", "    for name, df in name_df_map.items():\n", "        buffer = io.StringIO()\n", "        df.to_csv(buffer)\n", "        buffer.seek(0)\n", "        repl.upload_file(data=buffer, remote_file_path=name + \".csv\")\n", "\n", "    # Code for loading the uploaded files.\n", "    df_code = \"import pandas as pd\\n\" + \"\\n\".join(\n", "        f\"{name} = pd.read_csv('/mnt/data/{name}.csv')\" for name in name_df_map\n", "    )\n", "    return df_code\n", "\n", "\n", "def _repl_result_to_msg_content(repl_result: dict) -> str:\n", "    \"\"\"\n", "    Display images with including them in tool message content.\n", "    \"\"\"\n", "    content = {}\n", "    for k, v in repl_result.items():\n", "        # Any image results are returned as a dict of the form:\n", "        # {\"type\": \"image\", \"base64_data\": \"...\"}\n", "        if isinstance(repl_result[k], dict) and repl_result[k][\"type\"] == \"image\":\n", "            # Decode and display image\n", "            base64_str = repl_result[k][\"base64_data\"]\n", "            img = Image.open(io.BytesIO(base64.decodebytes(bytes(base64_str, \"utf-8\"))))\n", "            display(img)\n", "        else:\n", "            content[k] = repl_result[k]\n", "    return json.dumps(content, indent=2)\n", "\n", "\n", "def execute_python(state: AgentState) -> dict:\n", "    \"\"\"\n", "    Execute the latest generated Python code.\n", "    \"\"\"\n", "    messages = []\n", "\n", "    df_code = _upload_dfs_to_repl(state)\n", "    last_ai_msg = [msg for msg in state[\"messages\"] if isinstance(msg, AIMessage)][-1]\n", "    for tool_call in last_ai_msg.tool_calls:\n", "        if tool_call[\"name\"] != \"python_shell\":\n", "            continue\n", "\n", "        generated_code = tool_call[\"args\"][\"code\"]\n", "        repl_result = repl.execute(df_code + \"\\n\" + generated_code)\n", "\n", "        messages.append(\n", "            RawToolMessage(\n", "                _repl_result_to_msg_content(repl_result),\n", "                raw=repl_result,\n", "                tool_call_id=tool_call[\"id\"],\n", "                tool_name=tool_call[\"name\"],\n", "            )\n", "        )\n", "    return {\"messages\": messages}"]}, {"cell_type": "markdown", "id": "dd530250-60b6-40fb-b1f8-2ff32967ecc8", "metadata": {}, "source": ["### Define Edges\n", "\n", "Now we're ready to put all the pieces together into a graph."]}, {"cell_type": "code", "execution_count": 37, "id": "a04e0a82-1c3e-46d3-95ea-2461c21202ef", "metadata": {}, "outputs": [], "source": ["def should_continue(state: AgentState) -> str:\n", "    \"\"\"\n", "    If any Tool messages were generated in the last cycle that means we need to call the model again to interpret the latest results.\n", "    \"\"\"\n", "    return \"execute_sql_query\" if state[\"messages\"][-1].tool_calls else END"]}, {"cell_type": "code", "execution_count": 38, "id": "b2857ba9-da80-443f-8217-ac0523f90593", "metadata": {}, "outputs": [], "source": ["workflow = StateGraph(AgentState)\n", "\n", "workflow.add_node(\"call_model\", call_model)\n", "workflow.add_node(\"execute_sql_query\", execute_sql_query)\n", "workflow.add_node(\"execute_python\", execute_python)\n", "\n", "workflow.set_entry_point(\"call_model\")\n", "workflow.add_edge(\"execute_sql_query\", \"execute_python\")\n", "workflow.add_edge(\"execute_python\", \"call_model\")\n", "workflow.add_conditional_edges(\"call_model\", should_continue)\n", "\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": 39, "id": "74dc8c6c-b520-4f17-88ec-fa789ed911e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                       +-----------+                                    \n", "                                       | __start__ |                                    \n", "                                       +-----------+                                    \n", "                                              *                                         \n", "                                              *                                         \n", "                                              *                                         \n", "                                       +------------+                                   \n", "                                    ...| call_model |***                                \n", "                             .......   +------------+   *******                         \n", "                     ........          ..           ...        *******                  \n", "              .......                ..                ...            ******            \n", "          ....                     ..                     ..                *******     \n", "+---------+           +-------------------+                 ..                     **** \n", "| __end__ |           | execute_sql_query |                  .                  ****    \n", "+---------+           +-------------------+*                 .              ****        \n", "                                            *****           .          *****            \n", "                                                 ****       .      ****                 \n", "                                                     ***    .   ***                     \n", "                                                  +----------------+                    \n", "                                                  | execute_python |                    \n", "                                                  +----------------+                    \n"]}], "source": ["print(app.get_graph().draw_ascii())"]}, {"cell_type": "markdown", "id": "6d4e079b-0cf8-4f9d-a52b-6a8f980eee4b", "metadata": {}, "source": ["## Test it out\n", "\n", "Replace these examples with questions related to the database you've connected your agent to."]}, {"cell_type": "code", "execution_count": 40, "id": "2c173d6d-a212-448e-b309-299e87f205b8", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=RGBA size=989x590>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["The graph of the average latency by model has been generated successfully. However, it seems that the output is not displayed here directly. To view the graph, you would typically run the provided Python code in an environment where graphical output is supported, such as a Jupyter notebook or a Python script executed in a local environment with access to a display server.\n"]}], "source": ["output = app.invoke({\"messages\": [(\"human\", \"graph the average latency by model\")]})\n", "print(output[\"messages\"][-1].content)"]}, {"cell_type": "markdown", "id": "a67fbc65-2161-4518-9eea-f0cdd99b5f59", "metadata": {}, "source": ["**Lang<PERSON>mith Trace**: https://smith.langchain.com/public/9c8afcce-0ed1-4fb1-b719-767e6432bd8e/r"]}, {"cell_type": "code", "execution_count": 41, "id": "1d512f95-7490-483e-a748-abf708fbd20c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=RGBA size=571x453>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["The correlation coefficient between the number of prompt tokens and latency is approximately 0.305, indicating a positive but relatively weak relationship. This suggests that as the number of input tokens increases, there tends to be an increase in latency, but the relationship is not strong and other factors may also influence latency.\n", "\n", "Here is the scatter plot showing the relationship visually:\n", "\n", "![Scatter Plot of Prompt Tokens and Latency](sandbox:/2)\n"]}], "source": ["output = app.invoke(\n", "    {\n", "        \"messages\": [\n", "            (\"human\", \"what's the relationship between latency and input tokens?\")\n", "        ]\n", "    }\n", ")\n", "print(output[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": 43, "id": "10071b83-19c6-468d-b5fc-600b42cd57ac", "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=RGBA size=670x453>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Continue the conversation\n", "output = app.invoke(\n", "    {\"messages\": output[\"messages\"] + [(\"human\", \"now control for model\")]}\n", ")"]}, {"cell_type": "code", "execution_count": 44, "id": "81fb6102-c427-41c1-97cf-54e5944d1c79", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["After controlling for each model, here are the individual correlations between prompt tokens and latency:\n", "\n", "- `anthropic_claude_3_sonnet`: Correlation = 0.7659\n", "- `openai_gpt_3_5_turbo`: Correlation = 0.2833\n", "- `fireworks_mixtral`: Correlation = 0.1673\n", "- `cohere_command`: Correlation = 0.1434\n", "- `google_gemini_pro`: Correlation = 0.4928\n", "\n", "These correlations indicate that the `anthropic_claude_3_sonnet` model has the strongest positive correlation between the number of prompt tokens and latency, while the `cohere_command` model has the weakest positive correlation.\n", "\n", "Scatter plots were generated for each model individually to illustrate the relationship between prompt tokens and latency. Below are the plots for each model:\n", "\n", "1. Model: anthropic_claude_3_sonnet\n", "![Scatter Plot for anthropic_claude_3_sonnet](sandbox:/2)\n", "\n", "2. Model: openai_gpt_3_5_turbo\n", "![Scatter Plot for openai_gpt_3_5_turbo](sandbox:/2)\n", "\n", "3. Model: fireworks_mixtral\n", "![Scatter Plot for fireworks_mixtral](sandbox:/2)\n", "\n", "4. Model: cohere_command\n", "![Scatter Plot for cohere_command](sandbox:/2)\n", "\n", "5. Model: google_gemini_pro\n", "![Scatter Plot for google_gemini_pro](sandbox:/2)\n", "\n", "The plots and correlations together provide an understanding of how latency changes with the number of prompt tokens for each model.\n"]}], "source": ["print(output[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": 46, "id": "09167fa6-132a-4696-a4ee-eda80a41d3dd", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=RGBA size=703x510>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["output = app.invoke(\n", "    {\n", "        \"messages\": output[\"messages\"]\n", "        + [(\"human\", \"what about latency vs output tokens\")]\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 47, "id": "f0c48828-07ae-43df-b27f-14fdfbd835f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The correlation between the number of output tokens (completion_tokens) and latency varies by model, as shown below:\n", "\n", "- `anthropic_claude_3_sonnet`: Correlation = 0.910274\n", "- `cohere_command`: Correlation = 0.910292\n", "- `fireworks_mixtral`: Correlation = 0.681286\n", "- `google_gemini_pro`: Correlation = 0.151549\n", "- `openai_gpt_3_5_turbo`: Correlation = 0.449127\n", "\n", "The `anthropic_claude_3_sonnet` and `cohere_command` models show a very strong positive correlation, indicating that an increase in the number of output tokens is associated with a substantial increase in latency for these models. The `fireworks_mixtral` model also shows a strong positive correlation, but less strong than the first two. The `google_gemini_pro` model shows a weak positive correlation, and the `openai_gpt_3_5_turbo` model shows a moderate positive correlation.\n", "\n", "Below is the scatter plot with a regression line showing the relationship between output tokens and latency for each model:\n", "\n", "![Scatter Plot with Regression Line for Each Model](sandbox:/2)\n"]}], "source": ["print(output[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": 48, "id": "4114c16d-c727-49c2-beb1-27c5982b0948", "metadata": {}, "outputs": [], "source": ["output = app.invoke(\n", "    {\n", "        \"messages\": [\n", "            (\n", "                \"human\",\n", "                \"what's the better explanatory variable for latency: input or output tokens?\",\n", "            )\n", "        ]\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 49, "id": "7f983c4a-60b6-4dd6-ab22-2b59971e2fcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The correlation between input tokens and latency is 0.305, while the correlation between output tokens and latency is 0.487. Therefore, the better explanatory variable for latency is output tokens.\n"]}], "source": ["print(output[\"messages\"][-1].content)"]}], "metadata": {"kernelspec": {"display_name": "poetry-venv-2", "language": "python", "name": "poetry-venv-2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 5}