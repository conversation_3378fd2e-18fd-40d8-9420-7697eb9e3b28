{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Analysis of Twitter the-algorithm source code with LangChain, GPT4 and Activeloop's Deep Lake\n", "In this tutorial, we are going to use Langchain + Activeloop's Deep Lake with GPT4 to analyze the code base of the twitter algorithm. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!python3 -m pip install --upgrade langchain 'deeplake[enterprise]' openai tiktoken"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Define OpenAI embeddings, Deep Lake multi-modal vector store api and authenticate. For full documentation of Deep Lake please follow [docs](https://docs.activeloop.ai/) and [API reference](https://docs.deeplake.ai/en/latest/).\n", "\n", "Authenticate into Deep Lake if you want to create your own dataset and publish it. You can get an API key from the [platform](https://app.activeloop.ai)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "from langchain_community.vectorstores import DeepLake\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"OpenAI API Key:\")\n", "activeloop_token = getpass.getpass(\"Activeloop Token:\")\n", "os.environ[\"ACTIVELOOP_TOKEN\"] = activeloop_token"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["embeddings = OpenAIEmbeddings(disallowed_special=())"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["disallowed_special=() is required to avoid `Exception: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte` from tiktoken for some repositories"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 1. Index the code base (optional)\n", "You can directly skip this part and directly jump into using already indexed dataset. To begin with, first we will clone the repository, then parse and chunk the code base and use OpenAI indexing."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cloning into 'the-algorithm'...\n", "remote: Enumerating objects: 9142, done.\u001b[K\n", "remote: Counting objects: 100% (2438/2438), done.\u001b[K\n", "remote: Compressing objects: 100% (1662/1662), done.\u001b[K\n", "remote: Total 9142 (delta 597), reused 2349 (delta 593), pack-reused 6704\u001b[K\n", "Receiving objects: 100% (9142/9142), 7.67 MiB | 33.29 MiB/s, done.\n", "Resolving deltas: 100% (2818/2818), done.\n"]}], "source": ["!git clone https://github.com/twitter/the-algorithm # replace any repository of your choice"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Load all files inside the repository"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from langchain_community.document_loaders import TextLoader\n", "\n", "root_dir = \"./the-algorithm\"\n", "docs = []\n", "for dirpath, dirnames, filenames in os.walk(root_dir):\n", "    for file in filenames:\n", "        try:\n", "            loader = TextLoader(os.path.join(dirpath, file), encoding=\"utf-8\")\n", "            docs.extend(loader.load_and_split())\n", "        except Exception:\n", "            pass"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Then, chunk the files"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Created a chunk of size 2549, which is longer than the specified 1000\n", "Created a chunk of size 2095, which is longer than the specified 1000\n", "Created a chunk of size 1983, which is longer than the specified 1000\n", "Created a chunk of size 1531, which is longer than the specified 1000\n", "Created a chunk of size 1102, which is longer than the specified 1000\n", "Created a chunk of size 1012, which is longer than the specified 1000\n", "Created a chunk of size 1981, which is longer than the specified 1000\n", "Created a chunk of size 1022, which is longer than the specified 1000\n", "Created a chunk of size 1134, which is longer than the specified 1000\n", "Created a chunk of size 1532, which is longer than the specified 1000\n", "Created a chunk of size 1056, which is longer than the specified 1000\n", "Created a chunk of size 1515, which is longer than the specified 1000\n", "Created a chunk of size 2591, which is longer than the specified 1000\n", "Created a chunk of size 1957, which is longer than the specified 1000\n", "Created a chunk of size 2249, which is longer than the specified 1000\n", "Created a chunk of size 1275, which is longer than the specified 1000\n", "Created a chunk of size 2207, which is longer than the specified 1000\n", "Created a chunk of size 2405, which is longer than the specified 1000\n", "Created a chunk of size 1059, which is longer than the specified 1000\n", "Created a chunk of size 1726, which is longer than the specified 1000\n", "Created a chunk of size 1131, which is longer than the specified 1000\n", "Created a chunk of size 1575, which is longer than the specified 1000\n", "Created a chunk of size 1235, which is longer than the specified 1000\n", "Created a chunk of size 1857, which is longer than the specified 1000\n", "Created a chunk of size 3036, which is longer than the specified 1000\n", "Created a chunk of size 1977, which is longer than the specified 1000\n", "Created a chunk of size 1389, which is longer than the specified 1000\n", "Created a chunk of size 1282, which is longer than the specified 1000\n", "Created a chunk of size 3065, which is longer than the specified 1000\n", "Created a chunk of size 1095, which is longer than the specified 1000\n", "Created a chunk of size 1063, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 1178, which is longer than the specified 1000\n", "Created a chunk of size 1019, which is longer than the specified 1000\n", "Created a chunk of size 1130, which is longer than the specified 1000\n", "Created a chunk of size 1620, which is longer than the specified 1000\n", "Created a chunk of size 1111, which is longer than the specified 1000\n", "Created a chunk of size 1037, which is longer than the specified 1000\n", "Created a chunk of size 1913, which is longer than the specified 1000\n", "Created a chunk of size 1007, which is longer than the specified 1000\n", "Created a chunk of size 2160, which is longer than the specified 1000\n", "Created a chunk of size 1594, which is longer than the specified 1000\n", "Created a chunk of size 2181, which is longer than the specified 1000\n", "Created a chunk of size 1160, which is longer than the specified 1000\n", "Created a chunk of size 2029, which is longer than the specified 1000\n", "Created a chunk of size 1083, which is longer than the specified 1000\n", "Created a chunk of size 1076, which is longer than the specified 1000\n", "Created a chunk of size 1022, which is longer than the specified 1000\n", "Created a chunk of size 1021, which is longer than the specified 1000\n", "Created a chunk of size 3489, which is longer than the specified 1000\n", "Created a chunk of size 1543, which is longer than the specified 1000\n", "Created a chunk of size 1885, which is longer than the specified 1000\n", "Created a chunk of size 1141, which is longer than the specified 1000\n", "Created a chunk of size 2165, which is longer than the specified 1000\n", "Created a chunk of size 2142, which is longer than the specified 1000\n", "Created a chunk of size 3294, which is longer than the specified 1000\n", "Created a chunk of size 1166, which is longer than the specified 1000\n", "Created a chunk of size 1540, which is longer than the specified 1000\n", "Created a chunk of size 1020, which is longer than the specified 1000\n", "Created a chunk of size 1259, which is longer than the specified 1000\n", "Created a chunk of size 1790, which is longer than the specified 1000\n", "Created a chunk of size 1135, which is longer than the specified 1000\n", "Created a chunk of size 1193, which is longer than the specified 1000\n", "Created a chunk of size 1230, which is longer than the specified 1000\n", "Created a chunk of size 2611, which is longer than the specified 1000\n", "Created a chunk of size 1110, which is longer than the specified 1000\n", "Created a chunk of size 1097, which is longer than the specified 1000\n", "Created a chunk of size 1516, which is longer than the specified 1000\n", "Created a chunk of size 1552, which is longer than the specified 1000\n", "Created a chunk of size 1417, which is longer than the specified 1000\n", "Created a chunk of size 1416, which is longer than the specified 1000\n", "Created a chunk of size 2833, which is longer than the specified 1000\n", "Created a chunk of size 1437, which is longer than the specified 1000\n", "Created a chunk of size 1194, which is longer than the specified 1000\n", "Created a chunk of size 1939, which is longer than the specified 1000\n", "Created a chunk of size 1130, which is longer than the specified 1000\n", "Created a chunk of size 1004, which is longer than the specified 1000\n", "Created a chunk of size 1255, which is longer than the specified 1000\n", "Created a chunk of size 1139, which is longer than the specified 1000\n", "Created a chunk of size 1204, which is longer than the specified 1000\n", "Created a chunk of size 1202, which is longer than the specified 1000\n", "Created a chunk of size 1035, which is longer than the specified 1000\n", "Created a chunk of size 1044, which is longer than the specified 1000\n", "Created a chunk of size 1351, which is longer than the specified 1000\n", "Created a chunk of size 1269, which is longer than the specified 1000\n", "Created a chunk of size 1358, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1151, which is longer than the specified 1000\n", "Created a chunk of size 1088, which is longer than the specified 1000\n", "Created a chunk of size 1024, which is longer than the specified 1000\n", "Created a chunk of size 1031, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 1991, which is longer than the specified 1000\n", "Created a chunk of size 1829, which is longer than the specified 1000\n", "Created a chunk of size 1850, which is longer than the specified 1000\n", "Created a chunk of size 1099, which is longer than the specified 1000\n", "Created a chunk of size 1219, which is longer than the specified 1000\n", "Created a chunk of size 1063, which is longer than the specified 1000\n", "Created a chunk of size 1057, which is longer than the specified 1000\n", "Created a chunk of size 2343, which is longer than the specified 1000\n", "Created a chunk of size 1065, which is longer than the specified 1000\n", "Created a chunk of size 1058, which is longer than the specified 1000\n", "Created a chunk of size 1341, which is longer than the specified 1000\n", "Created a chunk of size 1017, which is longer than the specified 1000\n", "Created a chunk of size 1563, which is longer than the specified 1000\n", "Created a chunk of size 1225, which is longer than the specified 1000\n", "Created a chunk of size 1718, which is longer than the specified 1000\n", "Created a chunk of size 1548, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1121, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1240, which is longer than the specified 1000\n", "Created a chunk of size 2660, which is longer than the specified 1000\n", "Created a chunk of size 2514, which is longer than the specified 1000\n", "Created a chunk of size 1137, which is longer than the specified 1000\n", "Created a chunk of size 1892, which is longer than the specified 1000\n", "Created a chunk of size 1274, which is longer than the specified 1000\n", "Created a chunk of size 1261, which is longer than the specified 1000\n", "Created a chunk of size 1228, which is longer than the specified 1000\n", "Created a chunk of size 1992, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 2246, which is longer than the specified 1000\n", "Created a chunk of size 1008, which is longer than the specified 1000\n", "Created a chunk of size 1408, which is longer than the specified 1000\n", "Created a chunk of size 1629, which is longer than the specified 1000\n", "Created a chunk of size 2249, which is longer than the specified 1000\n", "Created a chunk of size 1664, which is longer than the specified 1000\n", "Created a chunk of size 2328, which is longer than the specified 1000\n", "Created a chunk of size 1206, which is longer than the specified 1000\n", "Created a chunk of size 1330, which is longer than the specified 1000\n", "Created a chunk of size 1842, which is longer than the specified 1000\n", "Created a chunk of size 1568, which is longer than the specified 1000\n", "Created a chunk of size 1182, which is longer than the specified 1000\n", "Created a chunk of size 1159, which is longer than the specified 1000\n", "Created a chunk of size 1067, which is longer than the specified 1000\n", "Created a chunk of size 1353, which is longer than the specified 1000\n", "Created a chunk of size 1770, which is longer than the specified 1000\n", "Created a chunk of size 1009, which is longer than the specified 1000\n", "Created a chunk of size 1286, which is longer than the specified 1000\n", "Created a chunk of size 1001, which is longer than the specified 1000\n", "Created a chunk of size 1220, which is longer than the specified 1000\n", "Created a chunk of size 1395, which is longer than the specified 1000\n", "Created a chunk of size 1068, which is longer than the specified 1000\n", "Created a chunk of size 2452, which is longer than the specified 1000\n", "Created a chunk of size 1277, which is longer than the specified 1000\n", "Created a chunk of size 1216, which is longer than the specified 1000\n", "Created a chunk of size 1557, which is longer than the specified 1000\n", "Created a chunk of size 1275, which is longer than the specified 1000\n", "Created a chunk of size 1161, which is longer than the specified 1000\n", "Created a chunk of size 1440, which is longer than the specified 1000\n", "Created a chunk of size 1430, which is longer than the specified 1000\n", "Created a chunk of size 1259, which is longer than the specified 1000\n", "Created a chunk of size 1064, which is longer than the specified 1000\n", "Created a chunk of size 1101, which is longer than the specified 1000\n", "Created a chunk of size 1108, which is longer than the specified 1000\n", "Created a chunk of size 1886, which is longer than the specified 1000\n", "Created a chunk of size 1629, which is longer than the specified 1000\n", "Created a chunk of size 1213, which is longer than the specified 1000\n", "Created a chunk of size 2095, which is longer than the specified 1000\n", "Created a chunk of size 1099, which is longer than the specified 1000\n", "Created a chunk of size 1034, which is longer than the specified 1000\n", "Created a chunk of size 1213, which is longer than the specified 1000\n", "Created a chunk of size 1223, which is longer than the specified 1000\n", "Created a chunk of size 1149, which is longer than the specified 1000\n", "Created a chunk of size 1319, which is longer than the specified 1000\n", "Created a chunk of size 1403, which is longer than the specified 1000\n", "Created a chunk of size 1358, which is longer than the specified 1000\n", "Created a chunk of size 2079, which is longer than the specified 1000\n", "Created a chunk of size 2414, which is longer than the specified 1000\n", "Created a chunk of size 1578, which is longer than the specified 1000\n", "Created a chunk of size 1253, which is longer than the specified 1000\n", "Created a chunk of size 1235, which is longer than the specified 1000\n", "Created a chunk of size 1043, which is longer than the specified 1000\n", "Created a chunk of size 1049, which is longer than the specified 1000\n", "Created a chunk of size 1126, which is longer than the specified 1000\n", "Created a chunk of size 1309, which is longer than the specified 1000\n", "Created a chunk of size 1967, which is longer than the specified 1000\n", "Created a chunk of size 1243, which is longer than the specified 1000\n", "Created a chunk of size 1156, which is longer than the specified 1000\n", "Created a chunk of size 1056, which is longer than the specified 1000\n", "Created a chunk of size 1615, which is longer than the specified 1000\n", "Created a chunk of size 1672, which is longer than the specified 1000\n", "Created a chunk of size 1432, which is longer than the specified 1000\n", "Created a chunk of size 1423, which is longer than the specified 1000\n", "Created a chunk of size 1519, which is longer than the specified 1000\n", "Created a chunk of size 1027, which is longer than the specified 1000\n", "Created a chunk of size 1050, which is longer than the specified 1000\n", "Created a chunk of size 1041, which is longer than the specified 1000\n", "Created a chunk of size 1125, which is longer than the specified 1000\n", "Created a chunk of size 1074, which is longer than the specified 1000\n", "Created a chunk of size 1416, which is longer than the specified 1000\n", "Created a chunk of size 1353, which is longer than the specified 1000\n", "Created a chunk of size 1372, which is longer than the specified 1000\n", "Created a chunk of size 1799, which is longer than the specified 1000\n", "Created a chunk of size 1712, which is longer than the specified 1000\n", "Created a chunk of size 1259, which is longer than the specified 1000\n", "Created a chunk of size 1550, which is longer than the specified 1000\n", "Created a chunk of size 1643, which is longer than the specified 1000\n", "Created a chunk of size 1658, which is longer than the specified 1000\n", "Created a chunk of size 1299, which is longer than the specified 1000\n", "Created a chunk of size 1229, which is longer than the specified 1000\n", "Created a chunk of size 1296, which is longer than the specified 1000\n", "Created a chunk of size 1041, which is longer than the specified 1000\n", "Created a chunk of size 1193, which is longer than the specified 1000\n", "Created a chunk of size 1011, which is longer than the specified 1000\n", "Created a chunk of size 2208, which is longer than the specified 1000\n", "Created a chunk of size 1101, which is longer than the specified 1000\n", "Created a chunk of size 2014, which is longer than the specified 1000\n", "Created a chunk of size 1771, which is longer than the specified 1000\n", "Created a chunk of size 1089, which is longer than the specified 1000\n", "Created a chunk of size 1364, which is longer than the specified 1000\n", "Created a chunk of size 1550, which is longer than the specified 1000\n", "Created a chunk of size 2202, which is longer than the specified 1000\n", "Created a chunk of size 1161, which is longer than the specified 1000\n", "Created a chunk of size 1559, which is longer than the specified 1000\n", "Created a chunk of size 1292, which is longer than the specified 1000\n", "Created a chunk of size 1383, which is longer than the specified 1000\n", "Created a chunk of size 1055, which is longer than the specified 1000\n", "Created a chunk of size 1036, which is longer than the specified 1000\n", "Created a chunk of size 1814, which is longer than the specified 1000\n", "Created a chunk of size 1702, which is longer than the specified 1000\n", "Created a chunk of size 1986, which is longer than the specified 1000\n", "Created a chunk of size 2261, which is longer than the specified 1000\n", "Created a chunk of size 1263, which is longer than the specified 1000\n", "Created a chunk of size 1049, which is longer than the specified 1000\n", "Created a chunk of size 1097, which is longer than the specified 1000\n", "Created a chunk of size 1519, which is longer than the specified 1000\n", "Created a chunk of size 1881, which is longer than the specified 1000\n", "Created a chunk of size 1585, which is longer than the specified 1000\n", "Created a chunk of size 1894, which is longer than the specified 1000\n", "Created a chunk of size 1114, which is longer than the specified 1000\n", "Created a chunk of size 2217, which is longer than the specified 1000\n", "Created a chunk of size 1090, which is longer than the specified 1000\n", "Created a chunk of size 1039, which is longer than the specified 1000\n", "Created a chunk of size 1568, which is longer than the specified 1000\n", "Created a chunk of size 1092, which is longer than the specified 1000\n", "Created a chunk of size 1508, which is longer than the specified 1000\n", "Created a chunk of size 1308, which is longer than the specified 1000\n", "Created a chunk of size 2633, which is longer than the specified 1000\n", "Created a chunk of size 1029, which is longer than the specified 1000\n", "Created a chunk of size 1377, which is longer than the specified 1000\n", "Created a chunk of size 1683, which is longer than the specified 1000\n", "Created a chunk of size 1443, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1110, which is longer than the specified 1000\n", "Created a chunk of size 1038, which is longer than the specified 1000\n", "Created a chunk of size 1287, which is longer than the specified 1000\n", "Created a chunk of size 1067, which is longer than the specified 1000\n", "Created a chunk of size 1673, which is longer than the specified 1000\n", "Created a chunk of size 1019, which is longer than the specified 1000\n", "Created a chunk of size 2514, which is longer than the specified 1000\n", "Created a chunk of size 1056, which is longer than the specified 1000\n", "Created a chunk of size 1575, which is longer than the specified 1000\n", "Created a chunk of size 1078, which is longer than the specified 1000\n", "Created a chunk of size 1171, which is longer than the specified 1000\n", "Created a chunk of size 1364, which is longer than the specified 1000\n", "Created a chunk of size 1595, which is longer than the specified 1000\n", "Created a chunk of size 2231, which is longer than the specified 1000\n", "Created a chunk of size 1271, which is longer than the specified 1000\n", "Created a chunk of size 2133, which is longer than the specified 1000\n", "Created a chunk of size 2272, which is longer than the specified 1000\n", "Created a chunk of size 2573, which is longer than the specified 1000\n", "Created a chunk of size 1005, which is longer than the specified 1000\n", "Created a chunk of size 2544, which is longer than the specified 1000\n", "Created a chunk of size 1102, which is longer than the specified 1000\n", "Created a chunk of size 1075, which is longer than the specified 1000\n", "Created a chunk of size 1382, which is longer than the specified 1000\n", "Created a chunk of size 1280, which is longer than the specified 1000\n", "Created a chunk of size 1452, which is longer than the specified 1000\n", "Created a chunk of size 1120, which is longer than the specified 1000\n", "Created a chunk of size 1016, which is longer than the specified 1000\n", "Created a chunk of size 1484, which is longer than the specified 1000\n", "Created a chunk of size 1536, which is longer than the specified 1000\n", "Created a chunk of size 3331, which is longer than the specified 1000\n", "Created a chunk of size 1205, which is longer than the specified 1000\n", "Created a chunk of size 1110, which is longer than the specified 1000\n", "Created a chunk of size 1056, which is longer than the specified 1000\n", "Created a chunk of size 1700, which is longer than the specified 1000\n", "Created a chunk of size 1101, which is longer than the specified 1000\n", "Created a chunk of size 1914, which is longer than the specified 1000\n", "Created a chunk of size 2808, which is longer than the specified 1000\n", "Created a chunk of size 2879, which is longer than the specified 1000\n", "Created a chunk of size 1690, which is longer than the specified 1000\n", "Created a chunk of size 1196, which is longer than the specified 1000\n", "Created a chunk of size 1221, which is longer than the specified 1000\n", "Created a chunk of size 1070, which is longer than the specified 1000\n", "Created a chunk of size 1215, which is longer than the specified 1000\n", "Created a chunk of size 1583, which is longer than the specified 1000\n", "Created a chunk of size 1207, which is longer than the specified 1000\n", "Created a chunk of size 1114, which is longer than the specified 1000\n", "Created a chunk of size 1169, which is longer than the specified 1000\n", "Created a chunk of size 1454, which is longer than the specified 1000\n", "Created a chunk of size 1083, which is longer than the specified 1000\n", "Created a chunk of size 1972, which is longer than the specified 1000\n", "Created a chunk of size 2506, which is longer than the specified 1000\n", "Created a chunk of size 2204, which is longer than the specified 1000\n", "Created a chunk of size 1464, which is longer than the specified 1000\n", "Created a chunk of size 1485, which is longer than the specified 1000\n", "Created a chunk of size 1389, which is longer than the specified 1000\n", "Created a chunk of size 1700, which is longer than the specified 1000\n", "Created a chunk of size 1063, which is longer than the specified 1000\n", "Created a chunk of size 1066, which is longer than the specified 1000\n", "Created a chunk of size 1127, which is longer than the specified 1000\n", "Created a chunk of size 3009, which is longer than the specified 1000\n", "Created a chunk of size 1217, which is longer than the specified 1000\n", "Created a chunk of size 1400, which is longer than the specified 1000\n", "Created a chunk of size 1323, which is longer than the specified 1000\n", "Created a chunk of size 2093, which is longer than the specified 1000\n", "Created a chunk of size 1486, which is longer than the specified 1000\n", "Created a chunk of size 1302, which is longer than the specified 1000\n", "Created a chunk of size 2178, which is longer than the specified 1000\n", "Created a chunk of size 1572, which is longer than the specified 1000\n", "Created a chunk of size 1327, which is longer than the specified 1000\n", "Created a chunk of size 2288, which is longer than the specified 1000\n", "Created a chunk of size 3163, which is longer than the specified 1000\n", "Created a chunk of size 1125, which is longer than the specified 1000\n", "Created a chunk of size 2009, which is longer than the specified 1000\n", "Created a chunk of size 1019, which is longer than the specified 1000\n", "Created a chunk of size 2491, which is longer than the specified 1000\n", "Created a chunk of size 2457, which is longer than the specified 1000\n", "Created a chunk of size 2462, which is longer than the specified 1000\n", "Created a chunk of size 2533, which is longer than the specified 1000\n", "Created a chunk of size 2543, which is longer than the specified 1000\n", "Created a chunk of size 2481, which is longer than the specified 1000\n", "Created a chunk of size 2574, which is longer than the specified 1000\n", "Created a chunk of size 2500, which is longer than the specified 1000\n", "Created a chunk of size 2739, which is longer than the specified 1000\n", "Created a chunk of size 1288, which is longer than the specified 1000\n", "Created a chunk of size 1375, which is longer than the specified 1000\n", "Created a chunk of size 1388, which is longer than the specified 1000\n", "Created a chunk of size 2344, which is longer than the specified 1000\n", "Created a chunk of size 1854, which is longer than the specified 1000\n", "Created a chunk of size 1659, which is longer than the specified 1000\n", "Created a chunk of size 2631, which is longer than the specified 1000\n", "Created a chunk of size 2853, which is longer than the specified 1000\n", "Created a chunk of size 1424, which is longer than the specified 1000\n", "Created a chunk of size 2364, which is longer than the specified 1000\n", "Created a chunk of size 1482, which is longer than the specified 1000\n", "Created a chunk of size 2761, which is longer than the specified 1000\n", "Created a chunk of size 2010, which is longer than the specified 1000\n", "Created a chunk of size 1716, which is longer than the specified 1000\n", "Created a chunk of size 2323, which is longer than the specified 1000\n", "Created a chunk of size 1717, which is longer than the specified 1000\n", "Created a chunk of size 1302, which is longer than the specified 1000\n", "Created a chunk of size 1641, which is longer than the specified 1000\n", "Created a chunk of size 1419, which is longer than the specified 1000\n", "Created a chunk of size 1232, which is longer than the specified 1000\n", "Created a chunk of size 1084, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1035, which is longer than the specified 1000\n", "Created a chunk of size 1502, which is longer than the specified 1000\n", "Created a chunk of size 1707, which is longer than the specified 1000\n", "Created a chunk of size 1128, which is longer than the specified 1000\n", "Created a chunk of size 1577, which is longer than the specified 1000\n", "Created a chunk of size 1149, which is longer than the specified 1000\n", "Created a chunk of size 1288, which is longer than the specified 1000\n", "Created a chunk of size 1182, which is longer than the specified 1000\n", "Created a chunk of size 1692, which is longer than the specified 1000\n", "Created a chunk of size 1653, which is longer than the specified 1000\n", "Created a chunk of size 1037, which is longer than the specified 1000\n", "Created a chunk of size 2164, which is longer than the specified 1000\n", "Created a chunk of size 1371, which is longer than the specified 1000\n", "Created a chunk of size 1348, which is longer than the specified 1000\n", "Created a chunk of size 1271, which is longer than the specified 1000\n", "Created a chunk of size 1015, which is longer than the specified 1000\n", "Created a chunk of size 1137, which is longer than the specified 1000\n", "Created a chunk of size 1759, which is longer than the specified 1000\n", "Created a chunk of size 1644, which is longer than the specified 1000\n", "Created a chunk of size 1104, which is longer than the specified 1000\n", "Created a chunk of size 1279, which is longer than the specified 1000\n", "Created a chunk of size 2328, which is longer than the specified 1000\n", "Created a chunk of size 3164, which is longer than the specified 1000\n", "Created a chunk of size 2565, which is longer than the specified 1000\n", "Created a chunk of size 1002, which is longer than the specified 1000\n", "Created a chunk of size 1261, which is longer than the specified 1000\n", "Created a chunk of size 1111, which is longer than the specified 1000\n", "Created a chunk of size 1732, which is longer than the specified 1000\n", "Created a chunk of size 1702, which is longer than the specified 1000\n", "Created a chunk of size 1029, which is longer than the specified 1000\n", "Created a chunk of size 1041, which is longer than the specified 1000\n", "Created a chunk of size 1605, which is longer than the specified 1000\n", "Created a chunk of size 1616, which is longer than the specified 1000\n", "Created a chunk of size 1224, which is longer than the specified 1000\n", "Created a chunk of size 2556, which is longer than the specified 1000\n", "Created a chunk of size 2092, which is longer than the specified 1000\n", "Created a chunk of size 1045, which is longer than the specified 1000\n", "Created a chunk of size 1172, which is longer than the specified 1000\n", "Created a chunk of size 1456, which is longer than the specified 1000\n", "Created a chunk of size 1353, which is longer than the specified 1000\n", "Created a chunk of size 1179, which is longer than the specified 1000\n", "Created a chunk of size 1060, which is longer than the specified 1000\n", "Created a chunk of size 1031, which is longer than the specified 1000\n", "Created a chunk of size 2216, which is longer than the specified 1000\n", "Created a chunk of size 1316, which is longer than the specified 1000\n", "Created a chunk of size 1485, which is longer than the specified 1000\n", "Created a chunk of size 1123, which is longer than the specified 1000\n", "Created a chunk of size 1288, which is longer than the specified 1000\n", "Created a chunk of size 1685, which is longer than the specified 1000\n", "Created a chunk of size 1577, which is longer than the specified 1000\n", "Created a chunk of size 1076, which is longer than the specified 1000\n", "Created a chunk of size 1006, which is longer than the specified 1000\n", "Created a chunk of size 1136, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1306, which is longer than the specified 1000\n", "Created a chunk of size 1306, which is longer than the specified 1000\n", "Created a chunk of size 1200, which is longer than the specified 1000\n", "Created a chunk of size 1311, which is longer than the specified 1000\n", "Created a chunk of size 1317, which is longer than the specified 1000\n", "Created a chunk of size 1528, which is longer than the specified 1000\n", "Created a chunk of size 1610, which is longer than the specified 1000\n", "Created a chunk of size 1517, which is longer than the specified 1000\n", "Created a chunk of size 1163, which is longer than the specified 1000\n", "Created a chunk of size 2573, which is longer than the specified 1000\n", "Created a chunk of size 1299, which is longer than the specified 1000\n", "Created a chunk of size 1042, which is longer than the specified 1000\n", "Created a chunk of size 1200, which is longer than the specified 1000\n", "Created a chunk of size 1047, which is longer than the specified 1000\n", "Created a chunk of size 1138, which is longer than the specified 1000\n", "Created a chunk of size 1130, which is longer than the specified 1000\n", "Created a chunk of size 1228, which is longer than the specified 1000\n", "Created a chunk of size 1124, which is longer than the specified 1000\n", "Created a chunk of size 1713, which is longer than the specified 1000\n", "Created a chunk of size 1156, which is longer than the specified 1000\n", "Created a chunk of size 1400, which is longer than the specified 1000\n", "Created a chunk of size 1050, which is longer than the specified 1000\n", "Created a chunk of size 1565, which is longer than the specified 1000\n", "Created a chunk of size 1136, which is longer than the specified 1000\n", "Created a chunk of size 1145, which is longer than the specified 1000\n", "Created a chunk of size 1182, which is longer than the specified 1000\n", "Created a chunk of size 1065, which is longer than the specified 1000\n", "Created a chunk of size 1428, which is longer than the specified 1000\n", "Created a chunk of size 2143, which is longer than the specified 1000\n", "Created a chunk of size 1887, which is longer than the specified 1000\n", "Created a chunk of size 2115, which is longer than the specified 1000\n", "Created a chunk of size 1067, which is longer than the specified 1000\n", "Created a chunk of size 1056, which is longer than the specified 1000\n", "Created a chunk of size 1534, which is longer than the specified 1000\n", "Created a chunk of size 1357, which is longer than the specified 1000\n", "Created a chunk of size 1343, which is longer than the specified 1000\n", "Created a chunk of size 1042, which is longer than the specified 1000\n", "Created a chunk of size 1468, which is longer than the specified 1000\n", "Created a chunk of size 1905, which is longer than the specified 1000\n", "Created a chunk of size 1077, which is longer than the specified 1000\n", "Created a chunk of size 3231, which is longer than the specified 1000\n", "Created a chunk of size 1821, which is longer than the specified 1000\n", "Created a chunk of size 2236, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1902, which is longer than the specified 1000\n", "Created a chunk of size 1782, which is longer than the specified 1000\n", "Created a chunk of size 1087, which is longer than the specified 1000\n", "Created a chunk of size 2570, which is longer than the specified 1000\n", "Created a chunk of size 1265, which is longer than the specified 1000\n", "Created a chunk of size 1096, which is longer than the specified 1000\n", "Created a chunk of size 1121, which is longer than the specified 1000\n", "Created a chunk of size 1061, which is longer than the specified 1000\n", "Created a chunk of size 1567, which is longer than the specified 1000\n", "Created a chunk of size 1790, which is longer than the specified 1000\n", "Created a chunk of size 1307, which is longer than the specified 1000\n", "Created a chunk of size 1386, which is longer than the specified 1000\n", "Created a chunk of size 1617, which is longer than the specified 1000\n", "Created a chunk of size 1031, which is longer than the specified 1000\n", "Created a chunk of size 1756, which is longer than the specified 1000\n", "Created a chunk of size 1796, which is longer than the specified 1000\n", "Created a chunk of size 1914, which is longer than the specified 1000\n", "Created a chunk of size 1150, which is longer than the specified 1000\n", "Created a chunk of size 1292, which is longer than the specified 1000\n", "Created a chunk of size 1100, which is longer than the specified 1000\n", "Created a chunk of size 1068, which is longer than the specified 1000\n", "Created a chunk of size 1188, which is longer than the specified 1000\n", "Created a chunk of size 1622, which is longer than the specified 1000\n", "Created a chunk of size 1078, which is longer than the specified 1000\n", "Created a chunk of size 1036, which is longer than the specified 1000\n", "Created a chunk of size 1204, which is longer than the specified 1000\n", "Created a chunk of size 1846, which is longer than the specified 1000\n", "Created a chunk of size 1309, which is longer than the specified 1000\n", "Created a chunk of size 1261, which is longer than the specified 1000\n", "Created a chunk of size 2102, which is longer than the specified 1000\n", "Created a chunk of size 1117, which is longer than the specified 1000\n", "Created a chunk of size 1055, which is longer than the specified 1000\n", "Created a chunk of size 1418, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1166, which is longer than the specified 1000\n", "Created a chunk of size 1082, which is longer than the specified 1000\n", "Created a chunk of size 1121, which is longer than the specified 1000\n", "Created a chunk of size 2550, which is longer than the specified 1000\n", "Created a chunk of size 1148, which is longer than the specified 1000\n", "Created a chunk of size 1581, which is longer than the specified 1000\n", "Created a chunk of size 1140, which is longer than the specified 1000\n", "Created a chunk of size 1064, which is longer than the specified 1000\n", "Created a chunk of size 1116, which is longer than the specified 1000\n", "Created a chunk of size 1888, which is longer than the specified 1000\n", "Created a chunk of size 3318, which is longer than the specified 1000\n", "Created a chunk of size 1540, which is longer than the specified 1000\n", "Created a chunk of size 1002, which is longer than the specified 1000\n", "Created a chunk of size 3319, which is longer than the specified 1000\n", "Created a chunk of size 1632, which is longer than the specified 1000\n", "Created a chunk of size 1553, which is longer than the specified 1000\n", "Created a chunk of size 1336, which is longer than the specified 1000\n", "Created a chunk of size 1379, which is longer than the specified 1000\n", "Created a chunk of size 1039, which is longer than the specified 1000\n", "Created a chunk of size 1096, which is longer than the specified 1000\n", "Created a chunk of size 1405, which is longer than the specified 1000\n", "Created a chunk of size 1652, which is longer than the specified 1000\n", "Created a chunk of size 1978, which is longer than the specified 1000\n", "Created a chunk of size 1416, which is longer than the specified 1000\n", "Created a chunk of size 1129, which is longer than the specified 1000\n", "Created a chunk of size 1053, which is longer than the specified 1000\n", "Created a chunk of size 1195, which is longer than the specified 1000\n", "Created a chunk of size 1511, which is longer than the specified 1000\n", "Created a chunk of size 1016, which is longer than the specified 1000\n", "Created a chunk of size 1448, which is longer than the specified 1000\n", "Created a chunk of size 1823, which is longer than the specified 1000\n", "Created a chunk of size 1475, which is longer than the specified 1000\n", "Created a chunk of size 1224, which is longer than the specified 1000\n", "Created a chunk of size 1358, which is longer than the specified 1000\n", "Created a chunk of size 1188, which is longer than the specified 1000\n", "Created a chunk of size 1044, which is longer than the specified 1000\n", "Created a chunk of size 2210, which is longer than the specified 1000\n", "Created a chunk of size 1404, which is longer than the specified 1000\n", "Created a chunk of size 1240, which is longer than the specified 1000\n", "Created a chunk of size 1021, which is longer than the specified 1000\n", "Created a chunk of size 1064, which is longer than the specified 1000\n", "Created a chunk of size 1459, which is longer than the specified 1000\n", "Created a chunk of size 1736, which is longer than the specified 1000\n", "Created a chunk of size 1261, which is longer than the specified 1000\n", "Created a chunk of size 1399, which is longer than the specified 1000\n", "Created a chunk of size 1208, which is longer than the specified 1000\n", "Created a chunk of size 1327, which is longer than the specified 1000\n", "Created a chunk of size 2257, which is longer than the specified 1000\n", "Created a chunk of size 1271, which is longer than the specified 1000\n", "Created a chunk of size 1635, which is longer than the specified 1000\n", "Created a chunk of size 1598, which is longer than the specified 1000\n", "Created a chunk of size 1423, which is longer than the specified 1000\n", "Created a chunk of size 1051, which is longer than the specified 1000\n", "Created a chunk of size 1130, which is longer than the specified 1000\n", "Created a chunk of size 1883, which is longer than the specified 1000\n", "Created a chunk of size 2315, which is longer than the specified 1000\n", "Created a chunk of size 1283, which is longer than the specified 1000\n", "Created a chunk of size 2139, which is longer than the specified 1000\n", "Created a chunk of size 1083, which is longer than the specified 1000\n", "Created a chunk of size 1417, which is longer than the specified 1000\n", "Created a chunk of size 3163, which is longer than the specified 1000\n", "Created a chunk of size 1098, which is longer than the specified 1000\n", "Created a chunk of size 1172, which is longer than the specified 1000\n", "Created a chunk of size 1174, which is longer than the specified 1000\n", "Created a chunk of size 1047, which is longer than the specified 1000\n", "Created a chunk of size 1287, which is longer than the specified 1000\n", "Created a chunk of size 1032, which is longer than the specified 1000\n", "Created a chunk of size 1088, which is longer than the specified 1000\n", "Created a chunk of size 1029, which is longer than the specified 1000\n", "Created a chunk of size 1161, which is longer than the specified 1000\n", "Created a chunk of size 1288, which is longer than the specified 1000\n", "Created a chunk of size 1203, which is longer than the specified 1000\n", "Created a chunk of size 1117, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1828, which is longer than the specified 1000\n", "Created a chunk of size 1357, which is longer than the specified 1000\n", "Created a chunk of size 1233, which is longer than the specified 1000\n", "Created a chunk of size 1211, which is longer than the specified 1000\n", "Created a chunk of size 2272, which is longer than the specified 1000\n", "Created a chunk of size 1120, which is longer than the specified 1000\n", "Created a chunk of size 1419, which is longer than the specified 1000\n", "Created a chunk of size 1175, which is longer than the specified 1000\n", "Created a chunk of size 2094, which is longer than the specified 1000\n", "Created a chunk of size 1294, which is longer than the specified 1000\n", "Created a chunk of size 1209, which is longer than the specified 1000\n", "Created a chunk of size 1033, which is longer than the specified 1000\n", "Created a chunk of size 1300, which is longer than the specified 1000\n", "Created a chunk of size 1096, which is longer than the specified 1000\n", "Created a chunk of size 1250, which is longer than the specified 1000\n", "Created a chunk of size 1029, which is longer than the specified 1000\n", "Created a chunk of size 1293, which is longer than the specified 1000\n", "Created a chunk of size 1630, which is longer than the specified 1000\n", "Created a chunk of size 1105, which is longer than the specified 1000\n", "Created a chunk of size 1147, which is longer than the specified 1000\n", "Created a chunk of size 1040, which is longer than the specified 1000\n", "Created a chunk of size 1279, which is longer than the specified 1000\n", "Created a chunk of size 1530, which is longer than the specified 1000\n", "Created a chunk of size 1031, which is longer than the specified 1000\n", "Created a chunk of size 1498, which is longer than the specified 1000\n", "Created a chunk of size 1131, which is longer than the specified 1000\n", "Created a chunk of size 1423, which is longer than the specified 1000\n", "Created a chunk of size 1054, which is longer than the specified 1000\n", "Created a chunk of size 1027, which is longer than the specified 1000\n", "Created a chunk of size 1382, which is longer than the specified 1000\n", "Created a chunk of size 1427, which is longer than the specified 1000\n", "Created a chunk of size 1049, which is longer than the specified 1000\n", "Created a chunk of size 1580, which is longer than the specified 1000\n", "Created a chunk of size 1565, which is longer than the specified 1000\n", "Created a chunk of size 1888, which is longer than the specified 1000\n", "Created a chunk of size 1475, which is longer than the specified 1000\n", "Created a chunk of size 1652, which is longer than the specified 1000\n", "Created a chunk of size 1891, which is longer than the specified 1000\n", "Created a chunk of size 2559, which is longer than the specified 1000\n", "Created a chunk of size 1028, which is longer than the specified 1000\n", "Created a chunk of size 1899, which is longer than the specified 1000\n", "Created a chunk of size 1021, which is longer than the specified 1000\n", "Created a chunk of size 1085, which is longer than the specified 1000\n", "Created a chunk of size 1854, which is longer than the specified 1000\n", "Created a chunk of size 1672, which is longer than the specified 1000\n", "Created a chunk of size 2537, which is longer than the specified 1000\n", "Created a chunk of size 1251, which is longer than the specified 1000\n", "Created a chunk of size 1734, which is longer than the specified 1000\n", "Created a chunk of size 1642, which is longer than the specified 1000\n", "Created a chunk of size 1376, which is longer than the specified 1000\n", "Created a chunk of size 1253, which is longer than the specified 1000\n", "Created a chunk of size 1642, which is longer than the specified 1000\n", "Created a chunk of size 1419, which is longer than the specified 1000\n", "Created a chunk of size 1427, which is longer than the specified 1000\n", "Created a chunk of size 1684, which is longer than the specified 1000\n", "Created a chunk of size 1760, which is longer than the specified 1000\n", "Created a chunk of size 1157, which is longer than the specified 1000\n", "Created a chunk of size 2504, which is longer than the specified 1000\n", "Created a chunk of size 1438, which is longer than the specified 1000\n", "Created a chunk of size 1082, which is longer than the specified 1000\n", "Created a chunk of size 1206, which is longer than the specified 1000\n", "Created a chunk of size 1102, which is longer than the specified 1000\n", "Created a chunk of size 1311, which is longer than the specified 1000\n", "Created a chunk of size 2972, which is longer than the specified 1000\n", "Created a chunk of size 1144, which is longer than the specified 1000\n", "Created a chunk of size 1825, which is longer than the specified 1000\n", "Created a chunk of size 1508, which is longer than the specified 1000\n", "Created a chunk of size 2268, which is longer than the specified 1000\n", "Created a chunk of size 1784, which is longer than the specified 1000\n", "Created a chunk of size 1754, which is longer than the specified 1000\n", "Created a chunk of size 2413, which is longer than the specified 1000\n", "Created a chunk of size 2054, which is longer than the specified 1000\n", "Created a chunk of size 2000, which is longer than the specified 1000\n", "Created a chunk of size 2061, which is longer than the specified 1000\n", "Created a chunk of size 1871, which is longer than the specified 1000\n", "Created a chunk of size 1065, which is longer than the specified 1000\n", "Created a chunk of size 1771, which is longer than the specified 1000\n", "Created a chunk of size 1184, which is longer than the specified 1000\n", "Created a chunk of size 1002, which is longer than the specified 1000\n", "Created a chunk of size 1062, which is longer than the specified 1000\n", "Created a chunk of size 2901, which is longer than the specified 1000\n", "Created a chunk of size 1715, which is longer than the specified 1000\n", "Created a chunk of size 1066, which is longer than the specified 1000\n", "Created a chunk of size 1419, which is longer than the specified 1000\n", "Created a chunk of size 1368, which is longer than the specified 1000\n", "Created a chunk of size 2422, which is longer than the specified 1000\n", "Created a chunk of size 2413, which is longer than the specified 1000\n", "Created a chunk of size 1327, which is longer than the specified 1000\n", "Created a chunk of size 1291, which is longer than the specified 1000\n", "Created a chunk of size 1291, which is longer than the specified 1000\n", "Created a chunk of size 1287, which is longer than the specified 1000\n", "Created a chunk of size 1286, which is longer than the specified 1000\n", "Created a chunk of size 1010, which is longer than the specified 1000\n", "Created a chunk of size 2359, which is longer than the specified 1000\n", "Created a chunk of size 1157, which is longer than the specified 1000\n", "Created a chunk of size 2244, which is longer than the specified 1000\n", "Created a chunk of size 2004, which is longer than the specified 1000\n", "Created a chunk of size 1701, which is longer than the specified 1000\n", "Created a chunk of size 1003, which is longer than the specified 1000\n", "Created a chunk of size 1186, which is longer than the specified 1000\n", "Created a chunk of size 1161, which is longer than the specified 1000\n", "Created a chunk of size 1666, which is longer than the specified 1000\n", "Created a chunk of size 1653, which is longer than the specified 1000\n", "Created a chunk of size 1045, which is longer than the specified 1000\n", "Created a chunk of size 1011, which is longer than the specified 1000\n", "Created a chunk of size 1069, which is longer than the specified 1000\n", "Created a chunk of size 1133, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 1103, which is longer than the specified 1000\n", "Created a chunk of size 1146, which is longer than the specified 1000\n", "Created a chunk of size 1318, which is longer than the specified 1000\n", "Created a chunk of size 1029, which is longer than the specified 1000\n", "Created a chunk of size 1680, which is longer than the specified 1000\n", "Created a chunk of size 1156, which is longer than the specified 1000\n", "Created a chunk of size 1190, which is longer than the specified 1000\n", "Created a chunk of size 1083, which is longer than the specified 1000\n", "Created a chunk of size 1192, which is longer than the specified 1000\n", "Created a chunk of size 1193, which is longer than the specified 1000\n", "Created a chunk of size 2539, which is longer than the specified 1000\n", "Created a chunk of size 2550, which is longer than the specified 1000\n", "Created a chunk of size 1412, which is longer than the specified 1000\n", "Created a chunk of size 1896, which is longer than the specified 1000\n", "Created a chunk of size 1618, which is longer than the specified 1000\n", "Created a chunk of size 1005, which is longer than the specified 1000\n", "Created a chunk of size 1331, which is longer than the specified 1000\n", "Created a chunk of size 1063, which is longer than the specified 1000\n", "Created a chunk of size 1394, which is longer than the specified 1000\n", "Created a chunk of size 1275, which is longer than the specified 1000\n", "Created a chunk of size 1279, which is longer than the specified 1000\n", "Created a chunk of size 1034, which is longer than the specified 1000\n", "Created a chunk of size 1693, which is longer than the specified 1000\n", "Created a chunk of size 1095, which is longer than the specified 1000\n", "Created a chunk of size 1267, which is longer than the specified 1000\n", "Created a chunk of size 1150, which is longer than the specified 1000\n", "Created a chunk of size 1076, which is longer than the specified 1000\n", "Created a chunk of size 1614, which is longer than the specified 1000\n", "Created a chunk of size 1378, which is longer than the specified 1000\n", "Created a chunk of size 2348, which is longer than the specified 1000\n", "Created a chunk of size 1270, which is longer than the specified 1000\n", "Created a chunk of size 1410, which is longer than the specified 1000\n", "Created a chunk of size 1220, which is longer than the specified 1000\n", "Created a chunk of size 1233, which is longer than the specified 1000\n", "Created a chunk of size 1067, which is longer than the specified 1000\n", "Created a chunk of size 1348, which is longer than the specified 1000\n", "Created a chunk of size 1355, which is longer than the specified 1000\n", "Created a chunk of size 1241, which is longer than the specified 1000\n", "Created a chunk of size 1219, which is longer than the specified 1000\n", "Created a chunk of size 1089, which is longer than the specified 1000\n", "Created a chunk of size 1946, which is longer than the specified 1000\n", "Created a chunk of size 1553, which is longer than the specified 1000\n", "Created a chunk of size 1373, which is longer than the specified 1000\n", "Created a chunk of size 1058, which is longer than the specified 1000\n", "Created a chunk of size 2175, which is longer than the specified 1000\n", "Created a chunk of size 2909, which is longer than the specified 1000\n", "Created a chunk of size 1353, which is longer than the specified 1000\n", "Created a chunk of size 1171, which is longer than the specified 1000\n", "Created a chunk of size 2949, which is longer than the specified 1000\n", "Created a chunk of size 3394, which is longer than the specified 1000\n", "Created a chunk of size 2081, which is longer than the specified 1000\n", "Created a chunk of size 1531, which is longer than the specified 1000\n", "Created a chunk of size 1221, which is longer than the specified 1000\n", "Created a chunk of size 1099, which is longer than the specified 1000\n", "Created a chunk of size 1028, which is longer than the specified 1000\n", "Created a chunk of size 1374, which is longer than the specified 1000\n", "Created a chunk of size 1129, which is longer than the specified 1000\n", "Created a chunk of size 1317, which is longer than the specified 1000\n", "Created a chunk of size 1053, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1175, which is longer than the specified 1000\n", "Created a chunk of size 1015, which is longer than the specified 1000\n", "Created a chunk of size 1328, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1102, which is longer than the specified 1000\n", "Created a chunk of size 1322, which is longer than the specified 1000\n", "Created a chunk of size 1063, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1061, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1071, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1117, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1241, which is longer than the specified 1000\n", "Created a chunk of size 1248, which is longer than the specified 1000\n", "Created a chunk of size 1391, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1527, which is longer than the specified 1000\n", "Created a chunk of size 1106, which is longer than the specified 1000\n", "Created a chunk of size 1297, which is longer than the specified 1000\n", "Created a chunk of size 1118, which is longer than the specified 1000\n", "Created a chunk of size 1296, which is longer than the specified 1000\n", "Created a chunk of size 1049, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1073, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1071, which is longer than the specified 1000\n", "Created a chunk of size 1248, which is longer than the specified 1000\n", "Created a chunk of size 1079, which is longer than the specified 1000\n", "Created a chunk of size 1248, which is longer than the specified 1000\n", "Created a chunk of size 1079, which is longer than the specified 1000\n", "Created a chunk of size 1248, which is longer than the specified 1000\n", "Created a chunk of size 1081, which is longer than the specified 1000\n", "Created a chunk of size 1248, which is longer than the specified 1000\n", "Created a chunk of size 1108, which is longer than the specified 1000\n", "Created a chunk of size 1285, which is longer than the specified 1000\n", "Created a chunk of size 1087, which is longer than the specified 1000\n", "Created a chunk of size 1285, which is longer than the specified 1000\n", "Created a chunk of size 1045, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1051, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1129, which is longer than the specified 1000\n", "Created a chunk of size 1288, which is longer than the specified 1000\n", "Created a chunk of size 1332, which is longer than the specified 1000\n", "Created a chunk of size 1230, which is longer than the specified 1000\n", "Created a chunk of size 1249, which is longer than the specified 1000\n", "Created a chunk of size 1380, which is longer than the specified 1000\n", "Created a chunk of size 1171, which is longer than the specified 1000\n", "Created a chunk of size 1289, which is longer than the specified 1000\n", "Created a chunk of size 1362, which is longer than the specified 1000\n", "Created a chunk of size 1289, which is longer than the specified 1000\n", "Created a chunk of size 1200, which is longer than the specified 1000\n", "Created a chunk of size 1379, which is longer than the specified 1000\n", "Created a chunk of size 1131, which is longer than the specified 1000\n", "Created a chunk of size 1109, which is longer than the specified 1000\n", "Created a chunk of size 1282, which is longer than the specified 1000\n", "Created a chunk of size 1285, which is longer than the specified 1000\n", "Created a chunk of size 1282, which is longer than the specified 1000\n", "Created a chunk of size 1782, which is longer than the specified 1000\n", "Created a chunk of size 1262, which is longer than the specified 1000\n", "Created a chunk of size 1068, which is longer than the specified 1000\n", "Created a chunk of size 1386, which is longer than the specified 1000\n", "Created a chunk of size 1203, which is longer than the specified 1000\n", "Created a chunk of size 1431, which is longer than the specified 1000\n", "Created a chunk of size 1016, which is longer than the specified 1000\n", "Created a chunk of size 1769, which is longer than the specified 1000\n", "Created a chunk of size 2404, which is longer than the specified 1000\n", "Created a chunk of size 1242, which is longer than the specified 1000\n", "Created a chunk of size 1795, which is longer than the specified 1000\n", "Created a chunk of size 1414, which is longer than the specified 1000\n", "Created a chunk of size 1109, which is longer than the specified 1000\n", "Created a chunk of size 1357, which is longer than the specified 1000\n", "Created a chunk of size 1344, which is longer than the specified 1000\n", "Created a chunk of size 1972, which is longer than the specified 1000\n", "Created a chunk of size 1577, which is longer than the specified 1000\n", "Created a chunk of size 1165, which is longer than the specified 1000\n", "Created a chunk of size 1169, which is longer than the specified 1000\n", "Created a chunk of size 1206, which is longer than the specified 1000\n", "Created a chunk of size 1318, which is longer than the specified 1000\n", "Created a chunk of size 1011, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1001, which is longer than the specified 1000\n", "Created a chunk of size 1054, which is longer than the specified 1000\n", "Created a chunk of size 1060, which is longer than the specified 1000\n", "Created a chunk of size 1058, which is longer than the specified 1000\n", "Created a chunk of size 1479, which is longer than the specified 1000\n", "Created a chunk of size 1360, which is longer than the specified 1000\n", "Created a chunk of size 1165, which is longer than the specified 1000\n", "Created a chunk of size 1013, which is longer than the specified 1000\n", "Created a chunk of size 1760, which is longer than the specified 1000\n", "Created a chunk of size 1403, which is longer than the specified 1000\n", "Created a chunk of size 1179, which is longer than the specified 1000\n", "Created a chunk of size 1580, which is longer than the specified 1000\n", "Created a chunk of size 1009, which is longer than the specified 1000\n", "Created a chunk of size 1020, which is longer than the specified 1000\n", "Created a chunk of size 1661, which is longer than the specified 1000\n", "Created a chunk of size 1210, which is longer than the specified 1000\n", "Created a chunk of size 1067, which is longer than the specified 1000\n", "Created a chunk of size 1683, which is longer than the specified 1000\n", "Created a chunk of size 2169, which is longer than the specified 1000\n", "Created a chunk of size 1624, which is longer than the specified 1000\n", "Created a chunk of size 1489, which is longer than the specified 1000\n", "Created a chunk of size 1718, which is longer than the specified 1000\n", "Created a chunk of size 1726, which is longer than the specified 1000\n", "Created a chunk of size 1748, which is longer than the specified 1000\n", "Created a chunk of size 1074, which is longer than the specified 1000\n", "Created a chunk of size 1140, which is longer than the specified 1000\n", "Created a chunk of size 2314, which is longer than the specified 1000\n", "Created a chunk of size 1024, which is longer than the specified 1000\n", "Created a chunk of size 3166, which is longer than the specified 1000\n", "Created a chunk of size 1410, which is longer than the specified 1000\n", "Created a chunk of size 1032, which is longer than the specified 1000\n", "Created a chunk of size 1216, which is longer than the specified 1000\n", "Created a chunk of size 1096, which is longer than the specified 1000\n", "Created a chunk of size 1239, which is longer than the specified 1000\n", "Created a chunk of size 1007, which is longer than the specified 1000\n", "Created a chunk of size 1082, which is longer than the specified 1000\n", "Created a chunk of size 1046, which is longer than the specified 1000\n", "Created a chunk of size 1992, which is longer than the specified 1000\n", "Created a chunk of size 1043, which is longer than the specified 1000\n", "Created a chunk of size 1670, which is longer than the specified 1000\n", "Created a chunk of size 1057, which is longer than the specified 1000\n", "Created a chunk of size 1200, which is longer than the specified 1000\n", "Created a chunk of size 1120, which is longer than the specified 1000\n", "Created a chunk of size 1158, which is longer than the specified 1000\n", "Created a chunk of size 1169, which is longer than the specified 1000\n", "Created a chunk of size 1270, which is longer than the specified 1000\n", "Created a chunk of size 2222, which is longer than the specified 1000\n", "Created a chunk of size 1165, which is longer than the specified 1000\n", "Created a chunk of size 2671, which is longer than the specified 1000\n", "Created a chunk of size 1918, which is longer than the specified 1000\n", "Created a chunk of size 1240, which is longer than the specified 1000\n", "Created a chunk of size 1133, which is longer than the specified 1000\n", "Created a chunk of size 1847, which is longer than the specified 1000\n", "Created a chunk of size 1732, which is longer than the specified 1000\n", "Created a chunk of size 1679, which is longer than the specified 1000\n", "Created a chunk of size 1616, which is longer than the specified 1000\n", "Created a chunk of size 2420, which is longer than the specified 1000\n", "Created a chunk of size 1126, which is longer than the specified 1000\n", "Created a chunk of size 1583, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1044, which is longer than the specified 1000\n", "Created a chunk of size 1464, which is longer than the specified 1000\n", "Created a chunk of size 3234, which is longer than the specified 1000\n", "Created a chunk of size 1053, which is longer than the specified 1000\n", "Created a chunk of size 1167, which is longer than the specified 1000\n", "Created a chunk of size 2458, which is longer than the specified 1000\n", "Created a chunk of size 1220, which is longer than the specified 1000\n", "Created a chunk of size 2425, which is longer than the specified 1000\n", "Created a chunk of size 1304, which is longer than the specified 1000\n", "Created a chunk of size 1079, which is longer than the specified 1000\n", "Created a chunk of size 1302, which is longer than the specified 1000\n", "Created a chunk of size 1202, which is longer than the specified 1000\n", "Created a chunk of size 1155, which is longer than the specified 1000\n", "Created a chunk of size 3187, which is longer than the specified 1000\n", "Created a chunk of size 2512, which is longer than the specified 1000\n", "Created a chunk of size 1415, which is longer than the specified 1000\n", "Created a chunk of size 1132, which is longer than the specified 1000\n", "Created a chunk of size 1228, which is longer than the specified 1000\n", "Created a chunk of size 1896, which is longer than the specified 1000\n", "Created a chunk of size 2075, which is longer than the specified 1000\n", "Created a chunk of size 1599, which is longer than the specified 1000\n", "Created a chunk of size 2025, which is longer than the specified 1000\n", "Created a chunk of size 1731, which is longer than the specified 1000\n", "Created a chunk of size 1614, which is longer than the specified 1000\n", "Created a chunk of size 1268, which is longer than the specified 1000\n", "Created a chunk of size 1285, which is longer than the specified 1000\n", "Created a chunk of size 1309, which is longer than the specified 1000\n", "Created a chunk of size 1469, which is longer than the specified 1000\n", "Created a chunk of size 1022, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1027, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1025, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1029, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1025, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1023, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1032, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1016, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1025, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1019, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1021, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1016, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1017, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1025, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1020, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1159, which is longer than the specified 1000\n", "Created a chunk of size 1019, which is longer than the specified 1000\n", "Created a chunk of size 1493, which is longer than the specified 1000\n", "Created a chunk of size 1708, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1139, which is longer than the specified 1000\n", "Created a chunk of size 1630, which is longer than the specified 1000\n", "Created a chunk of size 1348, which is longer than the specified 1000\n", "Created a chunk of size 1219, which is longer than the specified 1000\n", "Created a chunk of size 1315, which is longer than the specified 1000\n", "Created a chunk of size 1535, which is longer than the specified 1000\n", "Created a chunk of size 1471, which is longer than the specified 1000\n", "Created a chunk of size 1712, which is longer than the specified 1000\n", "Created a chunk of size 1672, which is longer than the specified 1000\n", "Created a chunk of size 1069, which is longer than the specified 1000\n", "Created a chunk of size 1598, which is longer than the specified 1000\n", "Created a chunk of size 1836, which is longer than the specified 1000\n", "Created a chunk of size 1132, which is longer than the specified 1000\n", "Created a chunk of size 1212, which is longer than the specified 1000\n", "Created a chunk of size 1366, which is longer than the specified 1000\n", "Created a chunk of size 1686, which is longer than the specified 1000\n", "Created a chunk of size 1589, which is longer than the specified 1000\n", "Created a chunk of size 1406, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1419, which is longer than the specified 1000\n", "Created a chunk of size 1604, which is longer than the specified 1000\n", "Created a chunk of size 2042, which is longer than the specified 1000\n", "Created a chunk of size 1102, which is longer than the specified 1000\n", "Created a chunk of size 1709, which is longer than the specified 1000\n", "Created a chunk of size 1818, which is longer than the specified 1000\n", "Created a chunk of size 1199, which is longer than the specified 1000\n", "Created a chunk of size 1074, which is longer than the specified 1000\n", "Created a chunk of size 1047, which is longer than the specified 1000\n", "Created a chunk of size 1057, which is longer than the specified 1000\n", "Created a chunk of size 1394, which is longer than the specified 1000\n", "Created a chunk of size 2293, which is longer than the specified 1000\n", "Created a chunk of size 1293, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1648, which is longer than the specified 1000\n", "Created a chunk of size 1274, which is longer than the specified 1000\n", "Created a chunk of size 1672, which is longer than the specified 1000\n", "Created a chunk of size 1794, which is longer than the specified 1000\n", "Created a chunk of size 1034, which is longer than the specified 1000\n", "Created a chunk of size 1201, which is longer than the specified 1000\n", "Created a chunk of size 1191, which is longer than the specified 1000\n", "Created a chunk of size 1263, which is longer than the specified 1000\n", "Created a chunk of size 1245, which is longer than the specified 1000\n", "Created a chunk of size 1257, which is longer than the specified 1000\n", "Created a chunk of size 2273, which is longer than the specified 1000\n", "Created a chunk of size 1411, which is longer than the specified 1000\n", "Created a chunk of size 1403, which is longer than the specified 1000\n", "Created a chunk of size 1146, which is longer than the specified 1000\n", "Created a chunk of size 1348, which is longer than the specified 1000\n", "Created a chunk of size 1594, which is longer than the specified 1000\n", "Created a chunk of size 1113, which is longer than the specified 1000\n", "Created a chunk of size 1386, which is longer than the specified 1000\n", "Created a chunk of size 1006, which is longer than the specified 1000\n", "Created a chunk of size 1553, which is longer than the specified 1000\n", "Created a chunk of size 1478, which is longer than the specified 1000\n", "Created a chunk of size 2878, which is longer than the specified 1000\n", "Created a chunk of size 2036, which is longer than the specified 1000\n", "Created a chunk of size 1361, which is longer than the specified 1000\n", "Created a chunk of size 1424, which is longer than the specified 1000\n", "Created a chunk of size 1493, which is longer than the specified 1000\n", "Created a chunk of size 1184, which is longer than the specified 1000\n", "Created a chunk of size 1019, which is longer than the specified 1000\n", "Created a chunk of size 1187, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1058, which is longer than the specified 1000\n", "Created a chunk of size 1569, which is longer than the specified 1000\n", "Created a chunk of size 1009, which is longer than the specified 1000\n", "Created a chunk of size 1112, which is longer than the specified 1000\n", "Created a chunk of size 1880, which is longer than the specified 1000\n", "Created a chunk of size 2674, which is longer than the specified 1000\n", "Created a chunk of size 1403, which is longer than the specified 1000\n", "Created a chunk of size 1808, which is longer than the specified 1000\n", "Created a chunk of size 1069, which is longer than the specified 1000\n", "Created a chunk of size 1273, which is longer than the specified 1000\n", "Created a chunk of size 2464, which is longer than the specified 1000\n", "Created a chunk of size 1012, which is longer than the specified 1000\n", "Created a chunk of size 1090, which is longer than the specified 1000\n", "Created a chunk of size 2735, which is longer than the specified 1000\n", "Created a chunk of size 2569, which is longer than the specified 1000\n", "Created a chunk of size 1282, which is longer than the specified 1000\n", "Created a chunk of size 2993, which is longer than the specified 1000\n", "Created a chunk of size 1151, which is longer than the specified 1000\n", "Created a chunk of size 1882, which is longer than the specified 1000\n", "Created a chunk of size 1887, which is longer than the specified 1000\n", "Created a chunk of size 1053, which is longer than the specified 1000\n", "Created a chunk of size 2710, which is longer than the specified 1000\n", "Created a chunk of size 1634, which is longer than the specified 1000\n", "Created a chunk of size 1081, which is longer than the specified 1000\n", "Created a chunk of size 1612, which is longer than the specified 1000\n", "Created a chunk of size 1003, which is longer than the specified 1000\n", "Created a chunk of size 2460, which is longer than the specified 1000\n", "Created a chunk of size 1819, which is longer than the specified 1000\n", "Created a chunk of size 1136, which is longer than the specified 1000\n", "Created a chunk of size 1681, which is longer than the specified 1000\n", "Created a chunk of size 1882, which is longer than the specified 1000\n", "Created a chunk of size 1318, which is longer than the specified 1000\n", "Created a chunk of size 2682, which is longer than the specified 1000\n", "Created a chunk of size 1545, which is longer than the specified 1000\n", "Created a chunk of size 1340, which is longer than the specified 1000\n", "Created a chunk of size 1100, which is longer than the specified 1000\n", "Created a chunk of size 1161, which is longer than the specified 1000\n", "Created a chunk of size 2057, which is longer than the specified 1000\n", "Created a chunk of size 2224, which is longer than the specified 1000\n", "Created a chunk of size 2527, which is longer than the specified 1000\n", "Created a chunk of size 2210, which is longer than the specified 1000\n", "Created a chunk of size 1273, which is longer than the specified 1000\n", "Created a chunk of size 1062, which is longer than the specified 1000\n", "Created a chunk of size 2336, which is longer than the specified 1000\n", "Created a chunk of size 1354, which is longer than the specified 1000\n", "Created a chunk of size 1166, which is longer than the specified 1000\n", "Created a chunk of size 3286, which is longer than the specified 1000\n", "Created a chunk of size 1350, which is longer than the specified 1000\n", "Created a chunk of size 1020, which is longer than the specified 1000\n", "Created a chunk of size 1122, which is longer than the specified 1000\n", "Created a chunk of size 1282, which is longer than the specified 1000\n", "Created a chunk of size 1036, which is longer than the specified 1000\n", "Created a chunk of size 1057, which is longer than the specified 1000\n", "Created a chunk of size 1078, which is longer than the specified 1000\n", "Created a chunk of size 1343, which is longer than the specified 1000\n", "Created a chunk of size 1698, which is longer than the specified 1000\n", "Created a chunk of size 1247, which is longer than the specified 1000\n", "Created a chunk of size 1286, which is longer than the specified 1000\n", "Created a chunk of size 1899, which is longer than the specified 1000\n", "Created a chunk of size 1002, which is longer than the specified 1000\n", "Created a chunk of size 1095, which is longer than the specified 1000\n", "Created a chunk of size 1344, which is longer than the specified 1000\n", "Created a chunk of size 1211, which is longer than the specified 1000\n", "Created a chunk of size 1176, which is longer than the specified 1000\n", "Created a chunk of size 1038, which is longer than the specified 1000\n", "Created a chunk of size 1244, which is longer than the specified 1000\n", "Created a chunk of size 1303, which is longer than the specified 1000\n", "Created a chunk of size 1172, which is longer than the specified 1000\n", "Created a chunk of size 1051, which is longer than the specified 1000\n", "Created a chunk of size 1207, which is longer than the specified 1000\n", "Created a chunk of size 1257, which is longer than the specified 1000\n", "Created a chunk of size 1493, which is longer than the specified 1000\n", "Created a chunk of size 1535, which is longer than the specified 1000\n", "Created a chunk of size 1004, which is longer than the specified 1000\n", "Created a chunk of size 1484, which is longer than the specified 1000\n", "Created a chunk of size 1843, which is longer than the specified 1000\n", "Created a chunk of size 1103, which is longer than the specified 1000\n", "Created a chunk of size 1258, which is longer than the specified 1000\n", "Created a chunk of size 1227, which is longer than the specified 1000\n", "Created a chunk of size 1199, which is longer than the specified 1000\n", "Created a chunk of size 2183, which is longer than the specified 1000\n", "Created a chunk of size 1174, which is longer than the specified 1000\n", "Created a chunk of size 1262, which is longer than the specified 1000\n", "Created a chunk of size 1357, which is longer than the specified 1000\n", "Created a chunk of size 1765, which is longer than the specified 1000\n", "Created a chunk of size 1028, which is longer than the specified 1000\n", "Created a chunk of size 1115, which is longer than the specified 1000\n", "Created a chunk of size 1016, which is longer than the specified 1000\n", "Created a chunk of size 1168, which is longer than the specified 1000\n", "Created a chunk of size 1534, which is longer than the specified 1000\n", "Created a chunk of size 1543, which is longer than the specified 1000\n", "Created a chunk of size 1746, which is longer than the specified 1000\n", "Created a chunk of size 1113, which is longer than the specified 1000\n", "Created a chunk of size 1516, which is longer than the specified 1000\n", "Created a chunk of size 1632, which is longer than the specified 1000\n", "Created a chunk of size 1365, which is longer than the specified 1000\n", "Created a chunk of size 1157, which is longer than the specified 1000\n", "Created a chunk of size 1391, which is longer than the specified 1000\n", "Created a chunk of size 1046, which is longer than the specified 1000\n", "Created a chunk of size 2953, which is longer than the specified 1000\n", "Created a chunk of size 1019, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1140, which is longer than the specified 1000\n", "Created a chunk of size 1617, which is longer than the specified 1000\n", "Created a chunk of size 1549, which is longer than the specified 1000\n", "Created a chunk of size 1111, which is longer than the specified 1000\n", "Created a chunk of size 1830, which is longer than the specified 1000\n", "Created a chunk of size 2966, which is longer than the specified 1000\n", "Created a chunk of size 1019, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 3647, which is longer than the specified 1000\n", "Created a chunk of size 1331, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1300, which is longer than the specified 1000\n", "Created a chunk of size 2944, which is longer than the specified 1000\n", "Created a chunk of size 1019, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1117, which is longer than the specified 1000\n", "Created a chunk of size 1732, which is longer than the specified 1000\n", "Created a chunk of size 1372, which is longer than the specified 1000\n", "Created a chunk of size 1199, which is longer than the specified 1000\n", "Created a chunk of size 2220, which is longer than the specified 1000\n", "Created a chunk of size 1594, which is longer than the specified 1000\n", "Created a chunk of size 1069, which is longer than the specified 1000\n", "Created a chunk of size 1493, which is longer than the specified 1000\n", "Created a chunk of size 1500, which is longer than the specified 1000\n", "Created a chunk of size 1056, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1113, which is longer than the specified 1000\n", "Created a chunk of size 1232, which is longer than the specified 1000\n", "Created a chunk of size 1665, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1078, which is longer than the specified 1000\n", "Created a chunk of size 1136, which is longer than the specified 1000\n", "Created a chunk of size 1400, which is longer than the specified 1000\n", "Created a chunk of size 1043, which is longer than the specified 1000\n", "Created a chunk of size 2030, which is longer than the specified 1000\n", "Created a chunk of size 1384, which is longer than the specified 1000\n", "Created a chunk of size 1024, which is longer than the specified 1000\n", "Created a chunk of size 1863, which is longer than the specified 1000\n", "Created a chunk of size 1177, which is longer than the specified 1000\n", "Created a chunk of size 1696, which is longer than the specified 1000\n", "Created a chunk of size 1240, which is longer than the specified 1000\n", "Created a chunk of size 1029, which is longer than the specified 1000\n", "Created a chunk of size 1050, which is longer than the specified 1000\n", "Created a chunk of size 2264, which is longer than the specified 1000\n", "Created a chunk of size 1469, which is longer than the specified 1000\n", "Created a chunk of size 1437, which is longer than the specified 1000\n", "Created a chunk of size 1004, which is longer than the specified 1000\n", "Created a chunk of size 1634, which is longer than the specified 1000\n", "Created a chunk of size 1434, which is longer than the specified 1000\n", "Created a chunk of size 1929, which is longer than the specified 1000\n", "Created a chunk of size 1892, which is longer than the specified 1000\n", "Created a chunk of size 1002, which is longer than the specified 1000\n", "Created a chunk of size 2091, which is longer than the specified 1000\n", "Created a chunk of size 2571, which is longer than the specified 1000\n", "Created a chunk of size 2017, which is longer than the specified 1000\n", "Created a chunk of size 1372, which is longer than the specified 1000\n", "Created a chunk of size 1488, which is longer than the specified 1000\n", "Created a chunk of size 1545, which is longer than the specified 1000\n", "Created a chunk of size 1598, which is longer than the specified 1000\n", "Created a chunk of size 2661, which is longer than the specified 1000\n", "Created a chunk of size 3184, which is longer than the specified 1000\n", "Created a chunk of size 1439, which is longer than the specified 1000\n", "Created a chunk of size 1671, which is longer than the specified 1000\n", "Created a chunk of size 1053, which is longer than the specified 1000\n", "Created a chunk of size 2240, which is longer than the specified 1000\n", "Created a chunk of size 2670, which is longer than the specified 1000\n", "Created a chunk of size 1979, which is longer than the specified 1000\n", "Created a chunk of size 1116, which is longer than the specified 1000\n", "Created a chunk of size 1454, which is longer than the specified 1000\n", "Created a chunk of size 2863, which is longer than the specified 1000\n", "Created a chunk of size 1213, which is longer than the specified 1000\n", "Created a chunk of size 3143, which is longer than the specified 1000\n", "Created a chunk of size 1626, which is longer than the specified 1000\n", "Created a chunk of size 1917, which is longer than the specified 1000\n", "Created a chunk of size 1173, which is longer than the specified 1000\n", "Created a chunk of size 1244, which is longer than the specified 1000\n", "Created a chunk of size 1063, which is longer than the specified 1000\n", "Created a chunk of size 1491, which is longer than the specified 1000\n", "Created a chunk of size 1399, which is longer than the specified 1000\n", "Created a chunk of size 1164, which is longer than the specified 1000\n", "Created a chunk of size 1141, which is longer than the specified 1000\n", "Created a chunk of size 1297, which is longer than the specified 1000\n", "Created a chunk of size 1032, which is longer than the specified 1000\n", "Created a chunk of size 1630, which is longer than the specified 1000\n", "Created a chunk of size 1336, which is longer than the specified 1000\n", "Created a chunk of size 1325, which is longer than the specified 1000\n", "Created a chunk of size 2463, which is longer than the specified 1000\n", "Created a chunk of size 2375, which is longer than the specified 1000\n", "Created a chunk of size 1380, which is longer than the specified 1000\n", "Created a chunk of size 1114, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1122, which is longer than the specified 1000\n", "Created a chunk of size 1224, which is longer than the specified 1000\n", "Created a chunk of size 1169, which is longer than the specified 1000\n", "Created a chunk of size 1252, which is longer than the specified 1000\n", "Created a chunk of size 1175, which is longer than the specified 1000\n", "Created a chunk of size 1544, which is longer than the specified 1000\n", "Created a chunk of size 1176, which is longer than the specified 1000\n", "Created a chunk of size 1176, which is longer than the specified 1000\n", "Created a chunk of size 1174, which is longer than the specified 1000\n", "Created a chunk of size 1175, which is longer than the specified 1000\n", "Created a chunk of size 1188, which is longer than the specified 1000\n", "Created a chunk of size 1176, which is longer than the specified 1000\n", "Created a chunk of size 1176, which is longer than the specified 1000\n", "Created a chunk of size 1280, which is longer than the specified 1000\n", "Created a chunk of size 1322, which is longer than the specified 1000\n", "Created a chunk of size 1657, which is longer than the specified 1000\n", "Created a chunk of size 2798, which is longer than the specified 1000\n", "Created a chunk of size 2465, which is longer than the specified 1000\n", "Created a chunk of size 1140, which is longer than the specified 1000\n", "Created a chunk of size 1886, which is longer than the specified 1000\n", "Created a chunk of size 1324, which is longer than the specified 1000\n", "Created a chunk of size 2948, which is longer than the specified 1000\n", "Created a chunk of size 1613, which is longer than the specified 1000\n", "Created a chunk of size 1672, which is longer than the specified 1000\n", "Created a chunk of size 1653, which is longer than the specified 1000\n", "Created a chunk of size 1705, which is longer than the specified 1000\n", "Created a chunk of size 1755, which is longer than the specified 1000\n", "Created a chunk of size 1674, which is longer than the specified 1000\n", "Created a chunk of size 1102, which is longer than the specified 1000\n", "Created a chunk of size 1468, which is longer than the specified 1000\n", "Created a chunk of size 1170, which is longer than the specified 1000\n", "Created a chunk of size 2336, which is longer than the specified 1000\n", "Created a chunk of size 1049, which is longer than the specified 1000\n", "Created a chunk of size 1999, which is longer than the specified 1000\n", "Created a chunk of size 1492, which is longer than the specified 1000\n", "Created a chunk of size 1199, which is longer than the specified 1000\n", "Created a chunk of size 1788, which is longer than the specified 1000\n", "Created a chunk of size 1455, which is longer than the specified 1000\n", "Created a chunk of size 1207, which is longer than the specified 1000\n", "Created a chunk of size 1860, which is longer than the specified 1000\n", "Created a chunk of size 1616, which is longer than the specified 1000\n", "Created a chunk of size 1482, which is longer than the specified 1000\n", "Created a chunk of size 1147, which is longer than the specified 1000\n", "Created a chunk of size 1119, which is longer than the specified 1000\n", "Created a chunk of size 1080, which is longer than the specified 1000\n", "Created a chunk of size 2603, which is longer than the specified 1000\n", "Created a chunk of size 1126, which is longer than the specified 1000\n", "Created a chunk of size 1268, which is longer than the specified 1000\n", "Created a chunk of size 2503, which is longer than the specified 1000\n", "Created a chunk of size 2900, which is longer than the specified 1000\n", "Created a chunk of size 1150, which is longer than the specified 1000\n", "Created a chunk of size 1119, which is longer than the specified 1000\n", "Created a chunk of size 1039, which is longer than the specified 1000\n", "Created a chunk of size 1719, which is longer than the specified 1000\n", "Created a chunk of size 1171, which is longer than the specified 1000\n", "Created a chunk of size 2856, which is longer than the specified 1000\n", "Created a chunk of size 2011, which is longer than the specified 1000\n", "Created a chunk of size 1084, which is longer than the specified 1000\n", "Created a chunk of size 1120, which is longer than the specified 1000\n", "Created a chunk of size 1442, which is longer than the specified 1000\n", "Created a chunk of size 1114, which is longer than the specified 1000\n", "Created a chunk of size 1860, which is longer than the specified 1000\n", "Created a chunk of size 1322, which is longer than the specified 1000\n", "Created a chunk of size 1425, which is longer than the specified 1000\n", "Created a chunk of size 1228, which is longer than the specified 1000\n", "Created a chunk of size 1458, which is longer than the specified 1000\n", "Created a chunk of size 1224, which is longer than the specified 1000\n", "Created a chunk of size 1187, which is longer than the specified 1000\n", "Created a chunk of size 1445, which is longer than the specified 1000\n", "Created a chunk of size 3103, which is longer than the specified 1000\n", "Created a chunk of size 1660, which is longer than the specified 1000\n", "Created a chunk of size 1136, which is longer than the specified 1000\n", "Created a chunk of size 1145, which is longer than the specified 1000\n", "Created a chunk of size 1201, which is longer than the specified 1000\n", "Created a chunk of size 1350, which is longer than the specified 1000\n", "Created a chunk of size 1123, which is longer than the specified 1000\n", "Created a chunk of size 1135, which is longer than the specified 1000\n", "Created a chunk of size 1008, which is longer than the specified 1000\n", "Created a chunk of size 1123, which is longer than the specified 1000\n", "Created a chunk of size 1064, which is longer than the specified 1000\n", "Created a chunk of size 1151, which is longer than the specified 1000\n", "Created a chunk of size 1145, which is longer than the specified 1000\n", "Created a chunk of size 1051, which is longer than the specified 1000\n", "Created a chunk of size 1300, which is longer than the specified 1000\n", "Created a chunk of size 1730, which is longer than the specified 1000\n", "Created a chunk of size 1260, which is longer than the specified 1000\n", "Created a chunk of size 2134, which is longer than the specified 1000\n", "Created a chunk of size 1806, which is longer than the specified 1000\n", "Created a chunk of size 1972, which is longer than the specified 1000\n", "Created a chunk of size 1231, which is longer than the specified 1000\n", "Created a chunk of size 1340, which is longer than the specified 1000\n", "Created a chunk of size 1658, which is longer than the specified 1000\n", "Created a chunk of size 1090, which is longer than the specified 1000\n", "Created a chunk of size 1399, which is longer than the specified 1000\n", "Created a chunk of size 1042, which is longer than the specified 1000\n", "Created a chunk of size 1483, which is longer than the specified 1000\n", "Created a chunk of size 1651, which is longer than the specified 1000\n", "Created a chunk of size 1371, which is longer than the specified 1000\n", "Created a chunk of size 1085, which is longer than the specified 1000\n", "Created a chunk of size 1035, which is longer than the specified 1000\n", "Created a chunk of size 1033, which is longer than the specified 1000\n", "Created a chunk of size 1298, which is longer than the specified 1000\n", "Created a chunk of size 1017, which is longer than the specified 1000\n", "Created a chunk of size 1609, which is longer than the specified 1000\n", "Created a chunk of size 1113, which is longer than the specified 1000\n", "Created a chunk of size 1462, which is longer than the specified 1000\n", "Created a chunk of size 1363, which is longer than the specified 1000\n", "Created a chunk of size 2234, which is longer than the specified 1000\n", "Created a chunk of size 1336, which is longer than the specified 1000\n", "Created a chunk of size 1350, which is longer than the specified 1000\n", "Created a chunk of size 1265, which is longer than the specified 1000\n", "Created a chunk of size 1310, which is longer than the specified 1000\n", "Created a chunk of size 2441, which is longer than the specified 1000\n", "Created a chunk of size 1120, which is longer than the specified 1000\n", "Created a chunk of size 1046, which is longer than the specified 1000\n", "Created a chunk of size 1041, which is longer than the specified 1000\n", "Created a chunk of size 1189, which is longer than the specified 1000\n", "Created a chunk of size 2226, which is longer than the specified 1000\n", "Created a chunk of size 1726, which is longer than the specified 1000\n", "Created a chunk of size 1882, which is longer than the specified 1000\n", "Created a chunk of size 1950, which is longer than the specified 1000\n", "Created a chunk of size 1089, which is longer than the specified 1000\n", "Created a chunk of size 1131, which is longer than the specified 1000\n", "Created a chunk of size 1685, which is longer than the specified 1000\n", "Created a chunk of size 1792, which is longer than the specified 1000\n", "Created a chunk of size 1385, which is longer than the specified 1000\n", "Created a chunk of size 1034, which is longer than the specified 1000\n", "Created a chunk of size 1032, which is longer than the specified 1000\n", "Created a chunk of size 1004, which is longer than the specified 1000\n", "Created a chunk of size 1659, which is longer than the specified 1000\n", "Created a chunk of size 1214, which is longer than the specified 1000\n", "Created a chunk of size 1600, which is longer than the specified 1000\n", "Created a chunk of size 2382, which is longer than the specified 1000\n", "Created a chunk of size 1024, which is longer than the specified 1000\n", "Created a chunk of size 1093, which is longer than the specified 1000\n", "Created a chunk of size 1020, which is longer than the specified 1000\n", "Created a chunk of size 1485, which is longer than the specified 1000\n", "Created a chunk of size 1572, which is longer than the specified 1000\n", "Created a chunk of size 1477, which is longer than the specified 1000\n", "Created a chunk of size 1214, which is longer than the specified 1000\n", "Created a chunk of size 1201, which is longer than the specified 1000\n", "Created a chunk of size 1243, which is longer than the specified 1000\n", "Created a chunk of size 1210, which is longer than the specified 1000\n", "Created a chunk of size 1089, which is longer than the specified 1000\n", "Created a chunk of size 1188, which is longer than the specified 1000\n", "Created a chunk of size 1020, which is longer than the specified 1000\n", "Created a chunk of size 1183, which is longer than the specified 1000\n", "Created a chunk of size 1136, which is longer than the specified 1000\n", "Created a chunk of size 1254, which is longer than the specified 1000\n", "Created a chunk of size 1236, which is longer than the specified 1000\n", "Created a chunk of size 1270, which is longer than the specified 1000\n", "Created a chunk of size 1287, which is longer than the specified 1000\n", "Created a chunk of size 2233, which is longer than the specified 1000\n", "Created a chunk of size 1104, which is longer than the specified 1000\n", "Created a chunk of size 1468, which is longer than the specified 1000\n", "Created a chunk of size 1205, which is longer than the specified 1000\n", "Created a chunk of size 1477, which is longer than the specified 1000\n", "Created a chunk of size 1449, which is longer than the specified 1000\n", "Created a chunk of size 1577, which is longer than the specified 1000\n", "Created a chunk of size 1126, which is longer than the specified 1000\n", "Created a chunk of size 2336, which is longer than the specified 1000\n", "Created a chunk of size 1277, which is longer than the specified 1000\n", "Created a chunk of size 1005, which is longer than the specified 1000\n", "Created a chunk of size 1157, which is longer than the specified 1000\n", "Created a chunk of size 1151, which is longer than the specified 1000\n", "Created a chunk of size 2352, which is longer than the specified 1000\n", "Created a chunk of size 1658, which is longer than the specified 1000\n", "Created a chunk of size 1378, which is longer than the specified 1000\n", "Created a chunk of size 1683, which is longer than the specified 1000\n", "Created a chunk of size 1512, which is longer than the specified 1000\n", "Created a chunk of size 1449, which is longer than the specified 1000\n", "Created a chunk of size 1183, which is longer than the specified 1000\n", "Created a chunk of size 1042, which is longer than the specified 1000\n", "Created a chunk of size 2679, which is longer than the specified 1000\n", "Created a chunk of size 2082, which is longer than the specified 1000\n", "Created a chunk of size 3082, which is longer than the specified 1000\n", "Created a chunk of size 1288, which is longer than the specified 1000\n", "Created a chunk of size 1017, which is longer than the specified 1000\n", "Created a chunk of size 1125, which is longer than the specified 1000\n", "Created a chunk of size 1009, which is longer than the specified 1000\n", "Created a chunk of size 2066, which is longer than the specified 1000\n", "Created a chunk of size 2512, which is longer than the specified 1000\n", "Created a chunk of size 1521, which is longer than the specified 1000\n", "Created a chunk of size 1414, which is longer than the specified 1000\n", "Created a chunk of size 1537, which is longer than the specified 1000\n", "Created a chunk of size 1874, which is longer than the specified 1000\n", "Created a chunk of size 1679, which is longer than the specified 1000\n", "Created a chunk of size 2415, which is longer than the specified 1000\n", "Created a chunk of size 1518, which is longer than the specified 1000\n", "Created a chunk of size 3242, which is longer than the specified 1000\n", "Created a chunk of size 2933, which is longer than the specified 1000\n", "Created a chunk of size 3004, which is longer than the specified 1000\n", "Created a chunk of size 1429, which is longer than the specified 1000\n", "Created a chunk of size 1078, which is longer than the specified 1000\n", "Created a chunk of size 1986, which is longer than the specified 1000\n", "Created a chunk of size 1010, which is longer than the specified 1000\n", "Created a chunk of size 1816, which is longer than the specified 1000\n", "Created a chunk of size 1052, which is longer than the specified 1000\n", "Created a chunk of size 1046, which is longer than the specified 1000\n", "Created a chunk of size 1202, which is longer than the specified 1000\n", "Created a chunk of size 1312, which is longer than the specified 1000\n", "Created a chunk of size 1052, which is longer than the specified 1000\n", "Created a chunk of size 1703, which is longer than the specified 1000\n", "Created a chunk of size 1009, which is longer than the specified 1000\n", "Created a chunk of size 3369, which is longer than the specified 1000\n", "Created a chunk of size 1380, which is longer than the specified 1000\n", "Created a chunk of size 1093, which is longer than the specified 1000\n", "Created a chunk of size 1702, which is longer than the specified 1000\n", "Created a chunk of size 1084, which is longer than the specified 1000\n", "Created a chunk of size 1880, which is longer than the specified 1000\n", "Created a chunk of size 2745, which is longer than the specified 1000\n", "Created a chunk of size 1085, which is longer than the specified 1000\n", "Created a chunk of size 1893, which is longer than the specified 1000\n", "Created a chunk of size 1033, which is longer than the specified 1000\n", "Created a chunk of size 1282, which is longer than the specified 1000\n", "Created a chunk of size 1218, which is longer than the specified 1000\n", "Created a chunk of size 1857, which is longer than the specified 1000\n", "Created a chunk of size 1324, which is longer than the specified 1000\n", "Created a chunk of size 2083, which is longer than the specified 1000\n", "Created a chunk of size 2213, which is longer than the specified 1000\n", "Created a chunk of size 1611, which is longer than the specified 1000\n", "Created a chunk of size 1706, which is longer than the specified 1000\n", "Created a chunk of size 1094, which is longer than the specified 1000\n", "Created a chunk of size 1999, which is longer than the specified 1000\n", "Created a chunk of size 1396, which is longer than the specified 1000\n", "Created a chunk of size 2097, which is longer than the specified 1000\n", "Created a chunk of size 1226, which is longer than the specified 1000\n", "Created a chunk of size 1607, which is longer than the specified 1000\n", "Created a chunk of size 1197, which is longer than the specified 1000\n", "Created a chunk of size 1106, which is longer than the specified 1000\n", "Created a chunk of size 1214, which is longer than the specified 1000\n", "Created a chunk of size 2238, which is longer than the specified 1000\n", "Created a chunk of size 1310, which is longer than the specified 1000\n", "Created a chunk of size 1459, which is longer than the specified 1000\n", "Created a chunk of size 1030, which is longer than the specified 1000\n", "Created a chunk of size 1913, which is longer than the specified 1000\n", "Created a chunk of size 1152, which is longer than the specified 1000\n", "Created a chunk of size 1265, which is longer than the specified 1000\n", "Created a chunk of size 1360, which is longer than the specified 1000\n", "Created a chunk of size 1571, which is longer than the specified 1000\n", "Created a chunk of size 1194, which is longer than the specified 1000\n", "Created a chunk of size 2089, which is longer than the specified 1000\n", "Created a chunk of size 2191, which is longer than the specified 1000\n", "Created a chunk of size 1021, which is longer than the specified 1000\n", "Created a chunk of size 1009, which is longer than the specified 1000\n", "Created a chunk of size 1016, which is longer than the specified 1000\n", "Created a chunk of size 1043, which is longer than the specified 1000\n", "Created a chunk of size 1035, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1245, which is longer than the specified 1000\n", "Created a chunk of size 1579, which is longer than the specified 1000\n", "Created a chunk of size 2218, which is longer than the specified 1000\n", "Created a chunk of size 1179, which is longer than the specified 1000\n", "Created a chunk of size 2477, which is longer than the specified 1000\n", "Created a chunk of size 1867, which is longer than the specified 1000\n", "Created a chunk of size 2035, which is longer than the specified 1000\n", "Created a chunk of size 1783, which is longer than the specified 1000\n", "Created a chunk of size 1579, which is longer than the specified 1000\n", "Created a chunk of size 2081, which is longer than the specified 1000\n", "Created a chunk of size 1744, which is longer than the specified 1000\n", "Created a chunk of size 1480, which is longer than the specified 1000\n", "Created a chunk of size 2124, which is longer than the specified 1000\n", "Created a chunk of size 1637, which is longer than the specified 1000\n", "Created a chunk of size 2884, which is longer than the specified 1000\n", "Created a chunk of size 2713, which is longer than the specified 1000\n", "Created a chunk of size 1127, which is longer than the specified 1000\n", "Created a chunk of size 3167, which is longer than the specified 1000\n", "Created a chunk of size 1532, which is longer than the specified 1000\n", "Created a chunk of size 1152, which is longer than the specified 1000\n", "Created a chunk of size 1443, which is longer than the specified 1000\n", "Created a chunk of size 1874, which is longer than the specified 1000\n", "Created a chunk of size 1155, which is longer than the specified 1000\n", "Created a chunk of size 1020, which is longer than the specified 1000\n", "Created a chunk of size 1119, which is longer than the specified 1000\n", "Created a chunk of size 1226, which is longer than the specified 1000\n", "Created a chunk of size 1737, which is longer than the specified 1000\n", "Created a chunk of size 1585, which is longer than the specified 1000\n", "Created a chunk of size 2830, which is longer than the specified 1000\n", "Created a chunk of size 1098, which is longer than the specified 1000\n", "Created a chunk of size 1535, which is longer than the specified 1000\n", "Created a chunk of size 2684, which is longer than the specified 1000\n", "Created a chunk of size 2617, which is longer than the specified 1000\n", "Created a chunk of size 1363, which is longer than the specified 1000\n", "Created a chunk of size 2994, which is longer than the specified 1000\n", "Created a chunk of size 1098, which is longer than the specified 1000\n", "Created a chunk of size 2157, which is longer than the specified 1000\n", "Created a chunk of size 2944, which is longer than the specified 1000\n", "Created a chunk of size 1286, which is longer than the specified 1000\n", "Created a chunk of size 1049, which is longer than the specified 1000\n", "Created a chunk of size 1868, which is longer than the specified 1000\n", "Created a chunk of size 1290, which is longer than the specified 1000\n", "Created a chunk of size 2997, which is longer than the specified 1000\n", "Created a chunk of size 1098, which is longer than the specified 1000\n", "Created a chunk of size 2677, which is longer than the specified 1000\n", "Created a chunk of size 1388, which is longer than the specified 1000\n", "Created a chunk of size 2318, which is longer than the specified 1000\n", "Created a chunk of size 1852, which is longer than the specified 1000\n", "Created a chunk of size 2610, which is longer than the specified 1000\n", "Created a chunk of size 2823, which is longer than the specified 1000\n", "Created a chunk of size 1032, which is longer than the specified 1000\n", "Created a chunk of size 2408, which is longer than the specified 1000\n", "Created a chunk of size 2497, which is longer than the specified 1000\n", "Created a chunk of size 1269, which is longer than the specified 1000\n", "Created a chunk of size 1181, which is longer than the specified 1000\n", "Created a chunk of size 2115, which is longer than the specified 1000\n", "Created a chunk of size 2382, which is longer than the specified 1000\n", "Created a chunk of size 2291, which is longer than the specified 1000\n", "Created a chunk of size 1132, which is longer than the specified 1000\n", "Created a chunk of size 1615, which is longer than the specified 1000\n", "Created a chunk of size 1798, which is longer than the specified 1000\n", "Created a chunk of size 1960, which is longer than the specified 1000\n", "Created a chunk of size 1444, which is longer than the specified 1000\n", "Created a chunk of size 1106, which is longer than the specified 1000\n", "Created a chunk of size 1168, which is longer than the specified 1000\n", "Created a chunk of size 2435, which is longer than the specified 1000\n", "Created a chunk of size 1041, which is longer than the specified 1000\n", "Created a chunk of size 1190, which is longer than the specified 1000\n", "Created a chunk of size 1147, which is longer than the specified 1000\n", "Created a chunk of size 1491, which is longer than the specified 1000\n", "Created a chunk of size 1397, which is longer than the specified 1000\n", "Created a chunk of size 1358, which is longer than the specified 1000\n", "Created a chunk of size 1056, which is longer than the specified 1000\n", "Created a chunk of size 1025, which is longer than the specified 1000\n", "Created a chunk of size 1065, which is longer than the specified 1000\n", "Created a chunk of size 1198, which is longer than the specified 1000\n", "Created a chunk of size 1362, which is longer than the specified 1000\n", "Created a chunk of size 1085, which is longer than the specified 1000\n", "Created a chunk of size 3227, which is longer than the specified 1000\n", "Created a chunk of size 2444, which is longer than the specified 1000\n", "Created a chunk of size 2449, which is longer than the specified 1000\n", "Created a chunk of size 3763, which is longer than the specified 1000\n", "Created a chunk of size 1157, which is longer than the specified 1000\n", "Created a chunk of size 1119, which is longer than the specified 1000\n", "Created a chunk of size 1027, which is longer than the specified 1000\n", "Created a chunk of size 1250, which is longer than the specified 1000\n", "Created a chunk of size 1107, which is longer than the specified 1000\n", "Created a chunk of size 1442, which is longer than the specified 1000\n", "Created a chunk of size 1931, which is longer than the specified 1000\n", "Created a chunk of size 1285, which is longer than the specified 1000\n", "Created a chunk of size 1411, which is longer than the specified 1000\n", "Created a chunk of size 1069, which is longer than the specified 1000\n", "Created a chunk of size 1227, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1203, which is longer than the specified 1000\n", "Created a chunk of size 2467, which is longer than the specified 1000\n", "Created a chunk of size 1168, which is longer than the specified 1000\n", "Created a chunk of size 1264, which is longer than the specified 1000\n", "Created a chunk of size 1145, which is longer than the specified 1000\n", "Created a chunk of size 1042, which is longer than the specified 1000\n", "Created a chunk of size 1215, which is longer than the specified 1000\n", "Created a chunk of size 1279, which is longer than the specified 1000\n", "Created a chunk of size 1708, which is longer than the specified 1000\n", "Created a chunk of size 1629, which is longer than the specified 1000\n", "Created a chunk of size 1140, which is longer than the specified 1000\n", "Created a chunk of size 1138, which is longer than the specified 1000\n", "Created a chunk of size 1626, which is longer than the specified 1000\n", "Created a chunk of size 1442, which is longer than the specified 1000\n", "Created a chunk of size 1125, which is longer than the specified 1000\n", "Created a chunk of size 2046, which is longer than the specified 1000\n", "Created a chunk of size 2532, which is longer than the specified 1000\n", "Created a chunk of size 1864, which is longer than the specified 1000\n", "Created a chunk of size 1180, which is longer than the specified 1000\n", "Created a chunk of size 1054, which is longer than the specified 1000\n", "Created a chunk of size 1227, which is longer than the specified 1000\n", "Created a chunk of size 1806, which is longer than the specified 1000\n", "Created a chunk of size 1925, which is longer than the specified 1000\n", "Created a chunk of size 1251, which is longer than the specified 1000\n", "Created a chunk of size 1108, which is longer than the specified 1000\n", "Created a chunk of size 1514, which is longer than the specified 1000\n", "Created a chunk of size 2568, which is longer than the specified 1000\n", "Created a chunk of size 1632, which is longer than the specified 1000\n", "Created a chunk of size 1111, which is longer than the specified 1000\n", "Created a chunk of size 1853, which is longer than the specified 1000\n", "Created a chunk of size 1703, which is longer than the specified 1000\n", "Created a chunk of size 2197, which is longer than the specified 1000\n", "Created a chunk of size 1128, which is longer than the specified 1000\n", "Created a chunk of size 1358, which is longer than the specified 1000\n", "Created a chunk of size 1445, which is longer than the specified 1000\n", "Created a chunk of size 1782, which is longer than the specified 1000\n", "Created a chunk of size 1485, which is longer than the specified 1000\n", "Created a chunk of size 1041, which is longer than the specified 1000\n", "Created a chunk of size 2644, which is longer than the specified 1000\n", "Created a chunk of size 1238, which is longer than the specified 1000\n", "Created a chunk of size 1192, which is longer than the specified 1000\n", "Created a chunk of size 1078, which is longer than the specified 1000\n", "Created a chunk of size 1880, which is longer than the specified 1000\n", "Created a chunk of size 1362, which is longer than the specified 1000\n", "Created a chunk of size 1386, which is longer than the specified 1000\n", "Created a chunk of size 1700, which is longer than the specified 1000\n", "Created a chunk of size 1284, which is longer than the specified 1000\n", "Created a chunk of size 2113, which is longer than the specified 1000\n", "Created a chunk of size 1360, which is longer than the specified 1000\n", "Created a chunk of size 1515, which is longer than the specified 1000\n", "Created a chunk of size 1217, which is longer than the specified 1000\n", "Created a chunk of size 1322, which is longer than the specified 1000\n", "Created a chunk of size 2058, which is longer than the specified 1000\n", "Created a chunk of size 1045, which is longer than the specified 1000\n", "Created a chunk of size 1536, which is longer than the specified 1000\n", "Created a chunk of size 1338, which is longer than the specified 1000\n", "Created a chunk of size 1009, which is longer than the specified 1000\n", "Created a chunk of size 1043, which is longer than the specified 1000\n", "Created a chunk of size 1481, which is longer than the specified 1000\n", "Created a chunk of size 1172, which is longer than the specified 1000\n", "Created a chunk of size 1498, which is longer than the specified 1000\n", "Created a chunk of size 1812, which is longer than the specified 1000\n", "Created a chunk of size 1209, which is longer than the specified 1000\n", "Created a chunk of size 1342, which is longer than the specified 1000\n", "Created a chunk of size 1966, which is longer than the specified 1000\n", "Created a chunk of size 1279, which is longer than the specified 1000\n", "Created a chunk of size 1237, which is longer than the specified 1000\n", "Created a chunk of size 1578, which is longer than the specified 1000\n", "Created a chunk of size 1602, which is longer than the specified 1000\n", "Created a chunk of size 1057, which is longer than the specified 1000\n", "Created a chunk of size 1150, which is longer than the specified 1000\n", "Created a chunk of size 2287, which is longer than the specified 1000\n", "Created a chunk of size 1923, which is longer than the specified 1000\n", "Created a chunk of size 1438, which is longer than the specified 1000\n", "Created a chunk of size 1196, which is longer than the specified 1000\n", "Created a chunk of size 1227, which is longer than the specified 1000\n", "Created a chunk of size 1043, which is longer than the specified 1000\n", "Created a chunk of size 1811, which is longer than the specified 1000\n", "Created a chunk of size 1006, which is longer than the specified 1000\n", "Created a chunk of size 1153, which is longer than the specified 1000\n", "Created a chunk of size 1687, which is longer than the specified 1000\n", "Created a chunk of size 1007, which is longer than the specified 1000\n", "Created a chunk of size 1269, which is longer than the specified 1000\n", "Created a chunk of size 1235, which is longer than the specified 1000\n", "Created a chunk of size 1246, which is longer than the specified 1000\n", "Created a chunk of size 1274, which is longer than the specified 1000\n", "Created a chunk of size 1444, which is longer than the specified 1000\n", "Created a chunk of size 1298, which is longer than the specified 1000\n", "Created a chunk of size 1390, which is longer than the specified 1000\n", "Created a chunk of size 1435, which is longer than the specified 1000\n", "Created a chunk of size 1215, which is longer than the specified 1000\n", "Created a chunk of size 1010, which is longer than the specified 1000\n", "Created a chunk of size 1629, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1468, which is longer than the specified 1000\n", "Created a chunk of size 1110, which is longer than the specified 1000\n", "Created a chunk of size 1003, which is longer than the specified 1000\n", "Created a chunk of size 1658, which is longer than the specified 1000\n", "Created a chunk of size 3490, which is longer than the specified 1000\n", "Created a chunk of size 2540, which is longer than the specified 1000\n", "Created a chunk of size 1818, which is longer than the specified 1000\n", "Created a chunk of size 1453, which is longer than the specified 1000\n", "Created a chunk of size 1047, which is longer than the specified 1000\n", "Created a chunk of size 1241, which is longer than the specified 1000\n", "Created a chunk of size 1496, which is longer than the specified 1000\n", "Created a chunk of size 1301, which is longer than the specified 1000\n", "Created a chunk of size 1012, which is longer than the specified 1000\n", "Created a chunk of size 1254, which is longer than the specified 1000\n", "Created a chunk of size 1152, which is longer than the specified 1000\n", "Created a chunk of size 1401, which is longer than the specified 1000\n", "Created a chunk of size 1236, which is longer than the specified 1000\n", "Created a chunk of size 1158, which is longer than the specified 1000\n", "Created a chunk of size 1004, which is longer than the specified 1000\n", "Created a chunk of size 1095, which is longer than the specified 1000\n", "Created a chunk of size 1151, which is longer than the specified 1000\n", "Created a chunk of size 1553, which is longer than the specified 1000\n", "Created a chunk of size 1430, which is longer than the specified 1000\n", "Created a chunk of size 1134, which is longer than the specified 1000\n", "Created a chunk of size 2678, which is longer than the specified 1000\n", "Created a chunk of size 1224, which is longer than the specified 1000\n", "Created a chunk of size 2336, which is longer than the specified 1000\n", "Created a chunk of size 1016, which is longer than the specified 1000\n", "Created a chunk of size 1057, which is longer than the specified 1000\n", "Created a chunk of size 1496, which is longer than the specified 1000\n", "Created a chunk of size 2006, which is longer than the specified 1000\n", "Created a chunk of size 1029, which is longer than the specified 1000\n", "Created a chunk of size 1010, which is longer than the specified 1000\n", "Created a chunk of size 1201, which is longer than the specified 1000\n", "Created a chunk of size 1646, which is longer than the specified 1000\n", "Created a chunk of size 1043, which is longer than the specified 1000\n", "Created a chunk of size 1680, which is longer than the specified 1000\n", "Created a chunk of size 1116, which is longer than the specified 1000\n", "Created a chunk of size 2876, which is longer than the specified 1000\n", "Created a chunk of size 1473, which is longer than the specified 1000\n", "Created a chunk of size 1364, which is longer than the specified 1000\n", "Created a chunk of size 1295, which is longer than the specified 1000\n", "Created a chunk of size 2806, which is longer than the specified 1000\n", "Created a chunk of size 1279, which is longer than the specified 1000\n", "Created a chunk of size 1106, which is longer than the specified 1000\n", "Created a chunk of size 1278, which is longer than the specified 1000\n", "Created a chunk of size 1030, which is longer than the specified 1000\n", "Created a chunk of size 1272, which is longer than the specified 1000\n", "Created a chunk of size 1069, which is longer than the specified 1000\n", "Created a chunk of size 1608, which is longer than the specified 1000\n", "Created a chunk of size 1138, which is longer than the specified 1000\n", "Created a chunk of size 2239, which is longer than the specified 1000\n", "Created a chunk of size 1203, which is longer than the specified 1000\n", "Created a chunk of size 1020, which is longer than the specified 1000\n", "Created a chunk of size 3116, which is longer than the specified 1000\n", "Created a chunk of size 1202, which is longer than the specified 1000\n", "Created a chunk of size 1801, which is longer than the specified 1000\n", "Created a chunk of size 1025, which is longer than the specified 1000\n", "Created a chunk of size 1148, which is longer than the specified 1000\n", "Created a chunk of size 1153, which is longer than the specified 1000\n", "Created a chunk of size 1609, which is longer than the specified 1000\n", "Created a chunk of size 1078, which is longer than the specified 1000\n", "Created a chunk of size 1083, which is longer than the specified 1000\n", "Created a chunk of size 1027, which is longer than the specified 1000\n", "Created a chunk of size 1001, which is longer than the specified 1000\n", "Created a chunk of size 1034, which is longer than the specified 1000\n", "Created a chunk of size 1167, which is longer than the specified 1000\n", "Created a chunk of size 1270, which is longer than the specified 1000\n", "Created a chunk of size 1489, which is longer than the specified 1000\n", "Created a chunk of size 1238, which is longer than the specified 1000\n", "Created a chunk of size 1042, which is longer than the specified 1000\n", "Created a chunk of size 1128, which is longer than the specified 1000\n", "Created a chunk of size 1496, which is longer than the specified 1000\n", "Created a chunk of size 1823, which is longer than the specified 1000\n", "Created a chunk of size 1345, which is longer than the specified 1000\n", "Created a chunk of size 1344, which is longer than the specified 1000\n", "Created a chunk of size 1001, which is longer than the specified 1000\n", "Created a chunk of size 2550, which is longer than the specified 1000\n", "Created a chunk of size 1342, which is longer than the specified 1000\n", "Created a chunk of size 1130, which is longer than the specified 1000\n", "Created a chunk of size 1498, which is longer than the specified 1000\n", "Created a chunk of size 1090, which is longer than the specified 1000\n", "Created a chunk of size 1424, which is longer than the specified 1000\n", "Created a chunk of size 1038, which is longer than the specified 1000\n", "Created a chunk of size 1383, which is longer than the specified 1000\n", "Created a chunk of size 2152, which is longer than the specified 1000\n", "Created a chunk of size 1155, which is longer than the specified 1000\n", "Created a chunk of size 1721, which is longer than the specified 1000\n", "Created a chunk of size 1084, which is longer than the specified 1000\n", "Created a chunk of size 1058, which is longer than the specified 1000\n", "Created a chunk of size 1238, which is longer than the specified 1000\n", "Created a chunk of size 1222, which is longer than the specified 1000\n", "Created a chunk of size 1339, which is longer than the specified 1000\n", "Created a chunk of size 1269, which is longer than the specified 1000\n", "Created a chunk of size 1992, which is longer than the specified 1000\n", "Created a chunk of size 1196, which is longer than the specified 1000\n", "Created a chunk of size 1191, which is longer than the specified 1000\n", "Created a chunk of size 1367, which is longer than the specified 1000\n", "Created a chunk of size 1354, which is longer than the specified 1000\n", "Created a chunk of size 1624, which is longer than the specified 1000\n", "Created a chunk of size 1228, which is longer than the specified 1000\n", "Created a chunk of size 2308, which is longer than the specified 1000\n", "Created a chunk of size 1205, which is longer than the specified 1000\n", "Created a chunk of size 1577, which is longer than the specified 1000\n", "Created a chunk of size 2166, which is longer than the specified 1000\n", "Created a chunk of size 1060, which is longer than the specified 1000\n", "Created a chunk of size 1005, which is longer than the specified 1000\n", "Created a chunk of size 1150, which is longer than the specified 1000\n", "Created a chunk of size 1171, which is longer than the specified 1000\n", "Created a chunk of size 2633, which is longer than the specified 1000\n", "Created a chunk of size 1264, which is longer than the specified 1000\n", "Created a chunk of size 2249, which is longer than the specified 1000\n", "Created a chunk of size 3309, which is longer than the specified 1000\n", "Created a chunk of size 1596, which is longer than the specified 1000\n", "Created a chunk of size 1771, which is longer than the specified 1000\n", "Created a chunk of size 1203, which is longer than the specified 1000\n", "Created a chunk of size 1287, which is longer than the specified 1000\n", "Created a chunk of size 1943, which is longer than the specified 1000\n", "Created a chunk of size 1033, which is longer than the specified 1000\n", "Created a chunk of size 1341, which is longer than the specified 1000\n", "Created a chunk of size 1220, which is longer than the specified 1000\n", "Created a chunk of size 1041, which is longer than the specified 1000\n", "Created a chunk of size 1431, which is longer than the specified 1000\n", "Created a chunk of size 1853, which is longer than the specified 1000\n", "Created a chunk of size 1357, which is longer than the specified 1000\n", "Created a chunk of size 1270, which is longer than the specified 1000\n", "Created a chunk of size 2401, which is longer than the specified 1000\n", "Created a chunk of size 1459, which is longer than the specified 1000\n", "Created a chunk of size 1017, which is longer than the specified 1000\n", "Created a chunk of size 1273, which is longer than the specified 1000\n", "Created a chunk of size 1030, which is longer than the specified 1000\n", "Created a chunk of size 1174, which is longer than the specified 1000\n", "Created a chunk of size 1054, which is longer than the specified 1000\n", "Created a chunk of size 2716, which is longer than the specified 1000\n", "Created a chunk of size 1021, which is longer than the specified 1000\n", "Created a chunk of size 1316, which is longer than the specified 1000\n", "Created a chunk of size 1120, which is longer than the specified 1000\n", "Created a chunk of size 2122, which is longer than the specified 1000\n", "Created a chunk of size 1610, which is longer than the specified 1000\n", "Created a chunk of size 1042, which is longer than the specified 1000\n", "Created a chunk of size 1483, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 1644, which is longer than the specified 1000\n", "Created a chunk of size 1167, which is longer than the specified 1000\n", "Created a chunk of size 1431, which is longer than the specified 1000\n", "Created a chunk of size 1379, which is longer than the specified 1000\n", "Created a chunk of size 1235, which is longer than the specified 1000\n", "Created a chunk of size 1197, which is longer than the specified 1000\n", "Created a chunk of size 1092, which is longer than the specified 1000\n", "Created a chunk of size 1010, which is longer than the specified 1000\n", "Created a chunk of size 1165, which is longer than the specified 1000\n", "Created a chunk of size 1153, which is longer than the specified 1000\n", "Created a chunk of size 1065, which is longer than the specified 1000\n", "Created a chunk of size 1395, which is longer than the specified 1000\n", "Created a chunk of size 2342, which is longer than the specified 1000\n", "Created a chunk of size 1114, which is longer than the specified 1000\n", "Created a chunk of size 1079, which is longer than the specified 1000\n", "Created a chunk of size 1177, which is longer than the specified 1000\n", "Created a chunk of size 1471, which is longer than the specified 1000\n", "Created a chunk of size 1160, which is longer than the specified 1000\n", "Created a chunk of size 1007, which is longer than the specified 1000\n", "Created a chunk of size 1120, which is longer than the specified 1000\n", "Created a chunk of size 1676, which is longer than the specified 1000\n", "Created a chunk of size 1430, which is longer than the specified 1000\n", "Created a chunk of size 1188, which is longer than the specified 1000\n", "Created a chunk of size 1401, which is longer than the specified 1000\n", "Created a chunk of size 1372, which is longer than the specified 1000\n", "Created a chunk of size 1457, which is longer than the specified 1000\n", "Created a chunk of size 1343, which is longer than the specified 1000\n", "Created a chunk of size 1450, which is longer than the specified 1000\n", "Created a chunk of size 1006, which is longer than the specified 1000\n", "Created a chunk of size 1216, which is longer than the specified 1000\n", "Created a chunk of size 1121, which is longer than the specified 1000\n", "Created a chunk of size 2417, which is longer than the specified 1000\n", "Created a chunk of size 1093, which is longer than the specified 1000\n", "Created a chunk of size 2310, which is longer than the specified 1000\n", "Created a chunk of size 1694, which is longer than the specified 1000\n", "Created a chunk of size 1095, which is longer than the specified 1000\n", "Created a chunk of size 1200, which is longer than the specified 1000\n", "Created a chunk of size 1219, which is longer than the specified 1000\n", "Created a chunk of size 1140, which is longer than the specified 1000\n", "Created a chunk of size 1466, which is longer than the specified 1000\n", "Created a chunk of size 1754, which is longer than the specified 1000\n", "Created a chunk of size 1058, which is longer than the specified 1000\n", "Created a chunk of size 1334, which is longer than the specified 1000\n", "Created a chunk of size 2057, which is longer than the specified 1000\n", "Created a chunk of size 1465, which is longer than the specified 1000\n", "Created a chunk of size 2354, which is longer than the specified 1000\n", "Created a chunk of size 1025, which is longer than the specified 1000\n", "Created a chunk of size 2263, which is longer than the specified 1000\n", "Created a chunk of size 1274, which is longer than the specified 1000\n", "Created a chunk of size 1011, which is longer than the specified 1000\n", "Created a chunk of size 1165, which is longer than the specified 1000\n", "Created a chunk of size 1404, which is longer than the specified 1000\n", "Created a chunk of size 1080, which is longer than the specified 1000\n", "Created a chunk of size 1110, which is longer than the specified 1000\n", "Created a chunk of size 1473, which is longer than the specified 1000\n", "Created a chunk of size 2057, which is longer than the specified 1000\n", "Created a chunk of size 2658, which is longer than the specified 1000\n", "Created a chunk of size 1236, which is longer than the specified 1000\n", "Created a chunk of size 2367, which is longer than the specified 1000\n", "Created a chunk of size 3230, which is longer than the specified 1000\n", "Created a chunk of size 1389, which is longer than the specified 1000\n", "Created a chunk of size 1602, which is longer than the specified 1000\n", "Created a chunk of size 1038, which is longer than the specified 1000\n", "Created a chunk of size 1444, which is longer than the specified 1000\n", "Created a chunk of size 3048, which is longer than the specified 1000\n", "Created a chunk of size 1444, which is longer than the specified 1000\n", "Created a chunk of size 1635, which is longer than the specified 1000\n", "Created a chunk of size 1025, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 1450, which is longer than the specified 1000\n", "Created a chunk of size 1824, which is longer than the specified 1000\n", "Created a chunk of size 1201, which is longer than the specified 1000\n", "Created a chunk of size 2496, which is longer than the specified 1000\n", "Created a chunk of size 3350, which is longer than the specified 1000\n", "Created a chunk of size 1883, which is longer than the specified 1000\n", "Created a chunk of size 2566, which is longer than the specified 1000\n", "Created a chunk of size 3425, which is longer than the specified 1000\n", "Created a chunk of size 1005, which is longer than the specified 1000\n", "Created a chunk of size 1504, which is longer than the specified 1000\n", "Created a chunk of size 2904, which is longer than the specified 1000\n", "Created a chunk of size 1725, which is longer than the specified 1000\n", "Created a chunk of size 1657, which is longer than the specified 1000\n", "Created a chunk of size 1217, which is longer than the specified 1000\n", "Created a chunk of size 1008, which is longer than the specified 1000\n", "Created a chunk of size 1380, which is longer than the specified 1000\n", "Created a chunk of size 2316, which is longer than the specified 1000\n", "Created a chunk of size 1256, which is longer than the specified 1000\n", "Created a chunk of size 1009, which is longer than the specified 1000\n", "Created a chunk of size 2934, which is longer than the specified 1000\n", "Created a chunk of size 1398, which is longer than the specified 1000\n", "Created a chunk of size 1805, which is longer than the specified 1000\n", "Created a chunk of size 1002, which is longer than the specified 1000\n", "Created a chunk of size 1487, which is longer than the specified 1000\n", "Created a chunk of size 1159, which is longer than the specified 1000\n", "Created a chunk of size 1246, which is longer than the specified 1000\n", "Created a chunk of size 1143, which is longer than the specified 1000\n", "Created a chunk of size 1061, which is longer than the specified 1000\n", "Created a chunk of size 1286, which is longer than the specified 1000\n", "Created a chunk of size 1158, which is longer than the specified 1000\n", "Created a chunk of size 1168, which is longer than the specified 1000\n", "Created a chunk of size 1041, which is longer than the specified 1000\n", "Created a chunk of size 1429, which is longer than the specified 1000\n", "Created a chunk of size 1073, which is longer than the specified 1000\n", "Created a chunk of size 1030, which is longer than the specified 1000\n", "Created a chunk of size 1111, which is longer than the specified 1000\n", "Created a chunk of size 1587, which is longer than the specified 1000\n", "Created a chunk of size 1333, which is longer than the specified 1000\n", "Created a chunk of size 1318, which is longer than the specified 1000\n", "Created a chunk of size 1076, which is longer than the specified 1000\n", "Created a chunk of size 3419, which is longer than the specified 1000\n", "Created a chunk of size 1149, which is longer than the specified 1000\n", "Created a chunk of size 1300, which is longer than the specified 1000\n", "Created a chunk of size 1077, which is longer than the specified 1000\n", "Created a chunk of size 2404, which is longer than the specified 1000\n", "Created a chunk of size 1101, which is longer than the specified 1000\n", "Created a chunk of size 1116, which is longer than the specified 1000\n", "Created a chunk of size 1548, which is longer than the specified 1000\n", "Created a chunk of size 1353, which is longer than the specified 1000\n", "Created a chunk of size 1040, which is longer than the specified 1000\n", "Created a chunk of size 2182, which is longer than the specified 1000\n", "Created a chunk of size 1182, which is longer than the specified 1000\n", "Created a chunk of size 1908, which is longer than the specified 1000\n", "Created a chunk of size 1306, which is longer than the specified 1000\n", "Created a chunk of size 1360, which is longer than the specified 1000\n", "Created a chunk of size 1979, which is longer than the specified 1000\n", "Created a chunk of size 1241, which is longer than the specified 1000\n", "Created a chunk of size 1757, which is longer than the specified 1000\n", "Created a chunk of size 1309, which is longer than the specified 1000\n", "Created a chunk of size 1587, which is longer than the specified 1000\n", "Created a chunk of size 1136, which is longer than the specified 1000\n", "Created a chunk of size 1997, which is longer than the specified 1000\n", "Created a chunk of size 1178, which is longer than the specified 1000\n", "Created a chunk of size 1229, which is longer than the specified 1000\n", "Created a chunk of size 2674, which is longer than the specified 1000\n", "Created a chunk of size 1246, which is longer than the specified 1000\n", "Created a chunk of size 1286, which is longer than the specified 1000\n", "Created a chunk of size 1047, which is longer than the specified 1000\n", "Created a chunk of size 1237, which is longer than the specified 1000\n", "Created a chunk of size 1295, which is longer than the specified 1000\n", "Created a chunk of size 1257, which is longer than the specified 1000\n", "Created a chunk of size 3650, which is longer than the specified 1000\n", "Created a chunk of size 1897, which is longer than the specified 1000\n", "Created a chunk of size 1216, which is longer than the specified 1000\n", "Created a chunk of size 2524, which is longer than the specified 1000\n", "Created a chunk of size 1964, which is longer than the specified 1000\n", "Created a chunk of size 1456, which is longer than the specified 1000\n", "Created a chunk of size 1653, which is longer than the specified 1000\n", "Created a chunk of size 1299, which is longer than the specified 1000\n", "Created a chunk of size 1094, which is longer than the specified 1000\n", "Created a chunk of size 1296, which is longer than the specified 1000\n", "Created a chunk of size 1057, which is longer than the specified 1000\n", "Created a chunk of size 1089, which is longer than the specified 1000\n", "Created a chunk of size 3304, which is longer than the specified 1000\n", "Created a chunk of size 1895, which is longer than the specified 1000\n", "Created a chunk of size 1734, which is longer than the specified 1000\n", "Created a chunk of size 1786, which is longer than the specified 1000\n", "Created a chunk of size 1299, which is longer than the specified 1000\n", "Created a chunk of size 1301, which is longer than the specified 1000\n", "Created a chunk of size 1141, which is longer than the specified 1000\n", "Created a chunk of size 1117, which is longer than the specified 1000\n", "Created a chunk of size 2210, which is longer than the specified 1000\n", "Created a chunk of size 1285, which is longer than the specified 1000\n", "Created a chunk of size 2381, which is longer than the specified 1000\n", "Created a chunk of size 1772, which is longer than the specified 1000\n", "Created a chunk of size 1701, which is longer than the specified 1000\n", "Created a chunk of size 1239, which is longer than the specified 1000\n", "Created a chunk of size 1082, which is longer than the specified 1000\n", "Created a chunk of size 1159, which is longer than the specified 1000\n", "Created a chunk of size 1806, which is longer than the specified 1000\n", "Created a chunk of size 1106, which is longer than the specified 1000\n", "Created a chunk of size 1396, which is longer than the specified 1000\n", "Created a chunk of size 1309, which is longer than the specified 1000\n", "Created a chunk of size 1087, which is longer than the specified 1000\n", "Created a chunk of size 1812, which is longer than the specified 1000\n", "Created a chunk of size 1177, which is longer than the specified 1000\n", "Created a chunk of size 1152, which is longer than the specified 1000\n", "Created a chunk of size 1112, which is longer than the specified 1000\n", "Created a chunk of size 1357, which is longer than the specified 1000\n", "Created a chunk of size 2293, which is longer than the specified 1000\n", "Created a chunk of size 1076, which is longer than the specified 1000\n", "Created a chunk of size 1137, which is longer than the specified 1000\n", "Created a chunk of size 1199, which is longer than the specified 1000\n", "Created a chunk of size 1844, which is longer than the specified 1000\n", "Created a chunk of size 1013, which is longer than the specified 1000\n", "Created a chunk of size 1431, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 1211, which is longer than the specified 1000\n", "Created a chunk of size 1263, which is longer than the specified 1000\n", "Created a chunk of size 1574, which is longer than the specified 1000\n", "Created a chunk of size 1536, which is longer than the specified 1000\n", "Created a chunk of size 1334, which is longer than the specified 1000\n", "Created a chunk of size 1301, which is longer than the specified 1000\n", "Created a chunk of size 1664, which is longer than the specified 1000\n", "Created a chunk of size 1054, which is longer than the specified 1000\n", "Created a chunk of size 1254, which is longer than the specified 1000\n", "Created a chunk of size 1069, which is longer than the specified 1000\n", "Created a chunk of size 1634, which is longer than the specified 1000\n", "Created a chunk of size 1525, which is longer than the specified 1000\n", "Created a chunk of size 1040, which is longer than the specified 1000\n", "Created a chunk of size 2201, which is longer than the specified 1000\n", "Created a chunk of size 1989, which is longer than the specified 1000\n", "Created a chunk of size 1613, which is longer than the specified 1000\n", "Created a chunk of size 1158, which is longer than the specified 1000\n", "Created a chunk of size 1576, which is longer than the specified 1000\n", "Created a chunk of size 1758, which is longer than the specified 1000\n", "Created a chunk of size 1920, which is longer than the specified 1000\n", "Created a chunk of size 1588, which is longer than the specified 1000\n", "Created a chunk of size 2327, which is longer than the specified 1000\n", "Created a chunk of size 1417, which is longer than the specified 1000\n", "Created a chunk of size 1074, which is longer than the specified 1000\n", "Created a chunk of size 1221, which is longer than the specified 1000\n", "Created a chunk of size 1054, which is longer than the specified 1000\n", "Created a chunk of size 2147, which is longer than the specified 1000\n", "Created a chunk of size 1389, which is longer than the specified 1000\n", "Created a chunk of size 1811, which is longer than the specified 1000\n", "Created a chunk of size 1167, which is longer than the specified 1000\n", "Created a chunk of size 1205, which is longer than the specified 1000\n", "Created a chunk of size 1109, which is longer than the specified 1000\n", "Created a chunk of size 1336, which is longer than the specified 1000\n", "Created a chunk of size 2256, which is longer than the specified 1000\n", "Created a chunk of size 1074, which is longer than the specified 1000\n", "Created a chunk of size 1425, which is longer than the specified 1000\n", "Created a chunk of size 2745, which is longer than the specified 1000\n", "Created a chunk of size 1546, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 2549, which is longer than the specified 1000\n", "Created a chunk of size 1227, which is longer than the specified 1000\n", "Created a chunk of size 1064, which is longer than the specified 1000\n", "Created a chunk of size 1277, which is longer than the specified 1000\n", "Created a chunk of size 2037, which is longer than the specified 1000\n", "Created a chunk of size 3185, which is longer than the specified 1000\n", "Created a chunk of size 1258, which is longer than the specified 1000\n", "Created a chunk of size 1061, which is longer than the specified 1000\n", "Created a chunk of size 1158, which is longer than the specified 1000\n", "Created a chunk of size 1267, which is longer than the specified 1000\n", "Created a chunk of size 1359, which is longer than the specified 1000\n", "Created a chunk of size 1883, which is longer than the specified 1000\n", "Created a chunk of size 1741, which is longer than the specified 1000\n", "Created a chunk of size 1743, which is longer than the specified 1000\n", "Created a chunk of size 1940, which is longer than the specified 1000\n", "Created a chunk of size 1057, which is longer than the specified 1000\n", "Created a chunk of size 1207, which is longer than the specified 1000\n", "Created a chunk of size 1071, which is longer than the specified 1000\n", "Created a chunk of size 1119, which is longer than the specified 1000\n", "Created a chunk of size 1038, which is longer than the specified 1000\n", "Created a chunk of size 1113, which is longer than the specified 1000\n", "Created a chunk of size 1773, which is longer than the specified 1000\n", "Created a chunk of size 2551, which is longer than the specified 1000\n", "Created a chunk of size 1220, which is longer than the specified 1000\n", "Created a chunk of size 1055, which is longer than the specified 1000\n", "Created a chunk of size 1318, which is longer than the specified 1000\n", "Created a chunk of size 1992, which is longer than the specified 1000\n", "Created a chunk of size 1289, which is longer than the specified 1000\n", "Created a chunk of size 1046, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 2079, which is longer than the specified 1000\n", "Created a chunk of size 2021, which is longer than the specified 1000\n", "Created a chunk of size 1865, which is longer than the specified 1000\n", "Created a chunk of size 1928, which is longer than the specified 1000\n", "Created a chunk of size 1393, which is longer than the specified 1000\n", "Created a chunk of size 1704, which is longer than the specified 1000\n", "Created a chunk of size 2249, which is longer than the specified 1000\n", "Created a chunk of size 1699, which is longer than the specified 1000\n", "Created a chunk of size 1126, which is longer than the specified 1000\n", "Created a chunk of size 1378, which is longer than the specified 1000\n", "Created a chunk of size 1529, which is longer than the specified 1000\n", "Created a chunk of size 2345, which is longer than the specified 1000\n", "Created a chunk of size 1199, which is longer than the specified 1000\n", "Created a chunk of size 1240, which is longer than the specified 1000\n", "Created a chunk of size 1927, which is longer than the specified 1000\n", "Created a chunk of size 1396, which is longer than the specified 1000\n", "Created a chunk of size 1141, which is longer than the specified 1000\n", "Created a chunk of size 1251, which is longer than the specified 1000\n", "Created a chunk of size 1790, which is longer than the specified 1000\n", "Created a chunk of size 2229, which is longer than the specified 1000\n", "Created a chunk of size 1133, which is longer than the specified 1000\n", "Created a chunk of size 1303, which is longer than the specified 1000\n", "Created a chunk of size 1279, which is longer than the specified 1000\n", "Created a chunk of size 1023, which is longer than the specified 1000\n", "Created a chunk of size 1266, which is longer than the specified 1000\n", "Created a chunk of size 1316, which is longer than the specified 1000\n", "Created a chunk of size 1260, which is longer than the specified 1000\n", "Created a chunk of size 1447, which is longer than the specified 1000\n", "Created a chunk of size 1094, which is longer than the specified 1000\n", "Created a chunk of size 1193, which is longer than the specified 1000\n", "Created a chunk of size 1609, which is longer than the specified 1000\n", "Created a chunk of size 1234, which is longer than the specified 1000\n", "Created a chunk of size 1122, which is longer than the specified 1000\n", "Created a chunk of size 1191, which is longer than the specified 1000\n", "Created a chunk of size 1392, which is longer than the specified 1000\n", "Created a chunk of size 1341, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 1301, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1152, which is longer than the specified 1000\n", "Created a chunk of size 2646, which is longer than the specified 1000\n", "Created a chunk of size 1177, which is longer than the specified 1000\n", "Created a chunk of size 1183, which is longer than the specified 1000\n", "Created a chunk of size 1759, which is longer than the specified 1000\n", "Created a chunk of size 1518, which is longer than the specified 1000\n", "Created a chunk of size 1975, which is longer than the specified 1000\n", "Created a chunk of size 1224, which is longer than the specified 1000\n", "Created a chunk of size 1328, which is longer than the specified 1000\n", "Created a chunk of size 1652, which is longer than the specified 1000\n", "Created a chunk of size 1223, which is longer than the specified 1000\n", "Created a chunk of size 1013, which is longer than the specified 1000\n", "Created a chunk of size 1563, which is longer than the specified 1000\n", "Created a chunk of size 1793, which is longer than the specified 1000\n", "Created a chunk of size 1393, which is longer than the specified 1000\n", "Created a chunk of size 1904, which is longer than the specified 1000\n", "Created a chunk of size 1176, which is longer than the specified 1000\n", "Created a chunk of size 1161, which is longer than the specified 1000\n", "Created a chunk of size 1149, which is longer than the specified 1000\n", "Created a chunk of size 1799, which is longer than the specified 1000\n", "Created a chunk of size 1198, which is longer than the specified 1000\n", "Created a chunk of size 1022, which is longer than the specified 1000\n", "Created a chunk of size 1171, which is longer than the specified 1000\n", "Created a chunk of size 1770, which is longer than the specified 1000\n", "Created a chunk of size 1103, which is longer than the specified 1000\n", "Created a chunk of size 1035, which is longer than the specified 1000\n", "Created a chunk of size 1100, which is longer than the specified 1000\n", "Created a chunk of size 1940, which is longer than the specified 1000\n", "Created a chunk of size 1405, which is longer than the specified 1000\n", "Created a chunk of size 1021, which is longer than the specified 1000\n", "Created a chunk of size 1065, which is longer than the specified 1000\n", "Created a chunk of size 1193, which is longer than the specified 1000\n", "Created a chunk of size 1407, which is longer than the specified 1000\n", "Created a chunk of size 1052, which is longer than the specified 1000\n", "Created a chunk of size 1438, which is longer than the specified 1000\n", "Created a chunk of size 1071, which is longer than the specified 1000\n", "Created a chunk of size 1442, which is longer than the specified 1000\n", "Created a chunk of size 1874, which is longer than the specified 1000\n", "Created a chunk of size 1005, which is longer than the specified 1000\n", "Created a chunk of size 1022, which is longer than the specified 1000\n", "Created a chunk of size 1047, which is longer than the specified 1000\n", "Created a chunk of size 2386, which is longer than the specified 1000\n", "Created a chunk of size 1178, which is longer than the specified 1000\n", "Created a chunk of size 1435, which is longer than the specified 1000\n", "Created a chunk of size 1289, which is longer than the specified 1000\n", "Created a chunk of size 1036, which is longer than the specified 1000\n", "Created a chunk of size 1951, which is longer than the specified 1000\n", "Created a chunk of size 1676, which is longer than the specified 1000\n", "Created a chunk of size 1550, which is longer than the specified 1000\n", "Created a chunk of size 1682, which is longer than the specified 1000\n", "Created a chunk of size 1417, which is longer than the specified 1000\n", "Created a chunk of size 1314, which is longer than the specified 1000\n", "Created a chunk of size 1461, which is longer than the specified 1000\n", "Created a chunk of size 1371, which is longer than the specified 1000\n", "Created a chunk of size 1209, which is longer than the specified 1000\n", "Created a chunk of size 1811, which is longer than the specified 1000\n", "Created a chunk of size 1390, which is longer than the specified 1000\n", "Created a chunk of size 2683, which is longer than the specified 1000\n", "Created a chunk of size 1298, which is longer than the specified 1000\n", "Created a chunk of size 1372, which is longer than the specified 1000\n", "Created a chunk of size 1058, which is longer than the specified 1000\n", "Created a chunk of size 1170, which is longer than the specified 1000\n", "Created a chunk of size 3152, which is longer than the specified 1000\n", "Created a chunk of size 2373, which is longer than the specified 1000\n", "Created a chunk of size 1102, which is longer than the specified 1000\n", "Created a chunk of size 1002, which is longer than the specified 1000\n", "Created a chunk of size 1552, which is longer than the specified 1000\n", "Created a chunk of size 2016, which is longer than the specified 1000\n", "Created a chunk of size 2785, which is longer than the specified 1000\n", "Created a chunk of size 2042, which is longer than the specified 1000\n", "Created a chunk of size 1933, which is longer than the specified 1000\n", "Created a chunk of size 1299, which is longer than the specified 1000\n", "Created a chunk of size 1393, which is longer than the specified 1000\n", "Created a chunk of size 1401, which is longer than the specified 1000\n", "Created a chunk of size 1341, which is longer than the specified 1000\n", "Created a chunk of size 1080, which is longer than the specified 1000\n", "Created a chunk of size 1157, which is longer than the specified 1000\n", "Created a chunk of size 2608, which is longer than the specified 1000\n", "Created a chunk of size 1224, which is longer than the specified 1000\n", "Created a chunk of size 1134, which is longer than the specified 1000\n", "Created a chunk of size 1146, which is longer than the specified 1000\n", "Created a chunk of size 2354, which is longer than the specified 1000\n", "Created a chunk of size 1549, which is longer than the specified 1000\n", "Created a chunk of size 1829, which is longer than the specified 1000\n", "Created a chunk of size 1183, which is longer than the specified 1000\n", "Created a chunk of size 1615, which is longer than the specified 1000\n", "Created a chunk of size 2238, which is longer than the specified 1000\n", "Created a chunk of size 1536, which is longer than the specified 1000\n", "Created a chunk of size 1008, which is longer than the specified 1000\n", "Created a chunk of size 1811, which is longer than the specified 1000\n", "Created a chunk of size 1420, which is longer than the specified 1000\n", "Created a chunk of size 1054, which is longer than the specified 1000\n", "Created a chunk of size 1489, which is longer than the specified 1000\n", "Created a chunk of size 1109, which is longer than the specified 1000\n", "Created a chunk of size 1513, which is longer than the specified 1000\n", "Created a chunk of size 1332, which is longer than the specified 1000\n", "Created a chunk of size 1542, which is longer than the specified 1000\n", "Created a chunk of size 1605, which is longer than the specified 1000\n", "Created a chunk of size 1283, which is longer than the specified 1000\n", "Created a chunk of size 2063, which is longer than the specified 1000\n", "Created a chunk of size 1035, which is longer than the specified 1000\n", "Created a chunk of size 3129, which is longer than the specified 1000\n", "Created a chunk of size 1247, which is longer than the specified 1000\n", "Created a chunk of size 1039, which is longer than the specified 1000\n", "Created a chunk of size 1054, which is longer than the specified 1000\n", "Created a chunk of size 1036, which is longer than the specified 1000\n", "Created a chunk of size 1077, which is longer than the specified 1000\n", "Created a chunk of size 1048, which is longer than the specified 1000\n", "Created a chunk of size 1375, which is longer than the specified 1000\n", "Created a chunk of size 1023, which is longer than the specified 1000\n", "Created a chunk of size 1042, which is longer than the specified 1000\n", "Created a chunk of size 2850, which is longer than the specified 1000\n", "Created a chunk of size 1061, which is longer than the specified 1000\n", "Created a chunk of size 1346, which is longer than the specified 1000\n", "Created a chunk of size 1304, which is longer than the specified 1000\n", "Created a chunk of size 2778, which is longer than the specified 1000\n", "Created a chunk of size 1023, which is longer than the specified 1000\n", "Created a chunk of size 1855, which is longer than the specified 1000\n", "Created a chunk of size 1136, which is longer than the specified 1000\n", "Created a chunk of size 1223, which is longer than the specified 1000\n", "Created a chunk of size 1219, which is longer than the specified 1000\n", "Created a chunk of size 1191, which is longer than the specified 1000\n", "Created a chunk of size 1022, which is longer than the specified 1000\n", "Created a chunk of size 1606, which is longer than the specified 1000\n", "Created a chunk of size 1140, which is longer than the specified 1000\n", "Created a chunk of size 1826, which is longer than the specified 1000\n", "Created a chunk of size 1430, which is longer than the specified 1000\n", "Created a chunk of size 1081, which is longer than the specified 1000\n", "Created a chunk of size 1099, which is longer than the specified 1000\n", "Created a chunk of size 1088, which is longer than the specified 1000\n", "Created a chunk of size 1099, which is longer than the specified 1000\n", "Created a chunk of size 1107, which is longer than the specified 1000\n", "Created a chunk of size 1515, which is longer than the specified 1000\n", "Created a chunk of size 1099, which is longer than the specified 1000\n", "Created a chunk of size 1099, which is longer than the specified 1000\n", "Created a chunk of size 1099, which is longer than the specified 1000\n", "Created a chunk of size 2214, which is longer than the specified 1000\n", "Created a chunk of size 1309, which is longer than the specified 1000\n", "Created a chunk of size 1100, which is longer than the specified 1000\n", "Created a chunk of size 1008, which is longer than the specified 1000\n", "Created a chunk of size 1864, which is longer than the specified 1000\n", "Created a chunk of size 1084, which is longer than the specified 1000\n", "Created a chunk of size 1544, which is longer than the specified 1000\n", "Created a chunk of size 1009, which is longer than the specified 1000\n", "Created a chunk of size 1005, which is longer than the specified 1000\n", "Created a chunk of size 1360, which is longer than the specified 1000\n", "Created a chunk of size 1263, which is longer than the specified 1000\n", "Created a chunk of size 1481, which is longer than the specified 1000\n", "Created a chunk of size 1171, which is longer than the specified 1000\n", "Created a chunk of size 2543, which is longer than the specified 1000\n", "Created a chunk of size 1593, which is longer than the specified 1000\n", "Created a chunk of size 1027, which is longer than the specified 1000\n", "Created a chunk of size 1631, which is longer than the specified 1000\n", "Created a chunk of size 1933, which is longer than the specified 1000\n", "Created a chunk of size 2567, which is longer than the specified 1000\n", "Created a chunk of size 1031, which is longer than the specified 1000\n", "Created a chunk of size 1422, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 2202, which is longer than the specified 1000\n", "Created a chunk of size 1391, which is longer than the specified 1000\n", "Created a chunk of size 1154, which is longer than the specified 1000\n", "Created a chunk of size 1358, which is longer than the specified 1000\n", "Created a chunk of size 1499, which is longer than the specified 1000\n", "Created a chunk of size 1514, which is longer than the specified 1000\n", "Created a chunk of size 1011, which is longer than the specified 1000\n", "Created a chunk of size 1474, which is longer than the specified 1000\n", "Created a chunk of size 1983, which is longer than the specified 1000\n", "Created a chunk of size 1322, which is longer than the specified 1000\n", "Created a chunk of size 1235, which is longer than the specified 1000\n", "Created a chunk of size 1098, which is longer than the specified 1000\n", "Created a chunk of size 1603, which is longer than the specified 1000\n", "Created a chunk of size 2790, which is longer than the specified 1000\n", "Created a chunk of size 1588, which is longer than the specified 1000\n", "Created a chunk of size 2713, which is longer than the specified 1000\n", "Created a chunk of size 1454, which is longer than the specified 1000\n", "Created a chunk of size 1276, which is longer than the specified 1000\n", "Created a chunk of size 2790, which is longer than the specified 1000\n", "Created a chunk of size 1588, which is longer than the specified 1000\n", "Created a chunk of size 1454, which is longer than the specified 1000\n", "Created a chunk of size 2658, which is longer than the specified 1000\n", "Created a chunk of size 1360, which is longer than the specified 1000\n", "Created a chunk of size 1117, which is longer than the specified 1000\n", "Created a chunk of size 1193, which is longer than the specified 1000\n", "Created a chunk of size 1272, which is longer than the specified 1000\n", "Created a chunk of size 1991, which is longer than the specified 1000\n", "Created a chunk of size 1586, which is longer than the specified 1000\n", "Created a chunk of size 1104, which is longer than the specified 1000\n", "Created a chunk of size 2724, which is longer than the specified 1000\n", "Created a chunk of size 1167, which is longer than the specified 1000\n", "Created a chunk of size 1028, which is longer than the specified 1000\n", "Created a chunk of size 1238, which is longer than the specified 1000\n", "Created a chunk of size 1278, which is longer than the specified 1000\n", "Created a chunk of size 2547, which is longer than the specified 1000\n", "Created a chunk of size 1365, which is longer than the specified 1000\n", "Created a chunk of size 1794, which is longer than the specified 1000\n", "Created a chunk of size 1516, which is longer than the specified 1000\n", "Created a chunk of size 1306, which is longer than the specified 1000\n", "Created a chunk of size 1492, which is longer than the specified 1000\n", "Created a chunk of size 1491, which is longer than the specified 1000\n", "Created a chunk of size 1667, which is longer than the specified 1000\n", "Created a chunk of size 1943, which is longer than the specified 1000\n", "Created a chunk of size 1588, which is longer than the specified 1000\n", "Created a chunk of size 1478, which is longer than the specified 1000\n", "Created a chunk of size 1330, which is longer than the specified 1000\n", "Created a chunk of size 1326, which is longer than the specified 1000\n", "Created a chunk of size 1154, which is longer than the specified 1000\n", "Created a chunk of size 1579, which is longer than the specified 1000\n", "Created a chunk of size 1179, which is longer than the specified 1000\n", "Created a chunk of size 1396, which is longer than the specified 1000\n", "Created a chunk of size 1084, which is longer than the specified 1000\n", "Created a chunk of size 1281, which is longer than the specified 1000\n", "Created a chunk of size 1071, which is longer than the specified 1000\n", "Created a chunk of size 1325, which is longer than the specified 1000\n", "Created a chunk of size 1262, which is longer than the specified 1000\n", "Created a chunk of size 1576, which is longer than the specified 1000\n", "Created a chunk of size 1367, which is longer than the specified 1000\n", "Created a chunk of size 1371, which is longer than the specified 1000\n", "Created a chunk of size 1028, which is longer than the specified 1000\n", "Created a chunk of size 1093, which is longer than the specified 1000\n", "Created a chunk of size 1122, which is longer than the specified 1000\n", "Created a chunk of size 1767, which is longer than the specified 1000\n", "Created a chunk of size 1607, which is longer than the specified 1000\n", "Created a chunk of size 1122, which is longer than the specified 1000\n", "Created a chunk of size 1380, which is longer than the specified 1000\n", "Created a chunk of size 1699, which is longer than the specified 1000\n", "Created a chunk of size 3680, which is longer than the specified 1000\n", "Created a chunk of size 1656, which is longer than the specified 1000\n", "Created a chunk of size 1622, which is longer than the specified 1000\n", "Created a chunk of size 3387, which is longer than the specified 1000\n", "Created a chunk of size 1815, which is longer than the specified 1000\n", "Created a chunk of size 1307, which is longer than the specified 1000\n", "Created a chunk of size 1724, which is longer than the specified 1000\n", "Created a chunk of size 1144, which is longer than the specified 1000\n", "Created a chunk of size 1719, which is longer than the specified 1000\n", "Created a chunk of size 1444, which is longer than the specified 1000\n", "Created a chunk of size 1995, which is longer than the specified 1000\n", "Created a chunk of size 1195, which is longer than the specified 1000\n", "Created a chunk of size 1387, which is longer than the specified 1000\n", "Created a chunk of size 1089, which is longer than the specified 1000\n", "Created a chunk of size 1621, which is longer than the specified 1000\n", "Created a chunk of size 1103, which is longer than the specified 1000\n", "Created a chunk of size 1127, which is longer than the specified 1000\n", "Created a chunk of size 1018, which is longer than the specified 1000\n", "Created a chunk of size 1322, which is longer than the specified 1000\n", "Created a chunk of size 3027, which is longer than the specified 1000\n", "Created a chunk of size 1077, which is longer than the specified 1000\n", "Created a chunk of size 1093, which is longer than the specified 1000\n", "Created a chunk of size 1503, which is longer than the specified 1000\n", "Created a chunk of size 2045, which is longer than the specified 1000\n", "Created a chunk of size 1032, which is longer than the specified 1000\n", "Created a chunk of size 1103, which is longer than the specified 1000\n", "Created a chunk of size 1224, which is longer than the specified 1000\n", "Created a chunk of size 1679, which is longer than the specified 1000\n", "Created a chunk of size 1644, which is longer than the specified 1000\n", "Created a chunk of size 1634, which is longer than the specified 1000\n", "Created a chunk of size 2009, which is longer than the specified 1000\n", "Created a chunk of size 1248, which is longer than the specified 1000\n", "Created a chunk of size 1292, which is longer than the specified 1000\n", "Created a chunk of size 1194, which is longer than the specified 1000\n", "Created a chunk of size 2349, which is longer than the specified 1000\n", "Created a chunk of size 1054, which is longer than the specified 1000\n", "Created a chunk of size 1077, which is longer than the specified 1000\n", "Created a chunk of size 1082, which is longer than the specified 1000\n", "Created a chunk of size 2161, which is longer than the specified 1000\n", "Created a chunk of size 1637, which is longer than the specified 1000\n", "Created a chunk of size 1376, which is longer than the specified 1000\n", "Created a chunk of size 1650, which is longer than the specified 1000\n", "Created a chunk of size 1263, which is longer than the specified 1000\n", "Created a chunk of size 1022, which is longer than the specified 1000\n", "Created a chunk of size 1544, which is longer than the specified 1000\n", "Created a chunk of size 1867, which is longer than the specified 1000\n", "Created a chunk of size 1418, which is longer than the specified 1000\n", "Created a chunk of size 1916, which is longer than the specified 1000\n", "Created a chunk of size 1533, which is longer than the specified 1000\n", "Created a chunk of size 1951, which is longer than the specified 1000\n", "Created a chunk of size 2685, which is longer than the specified 1000\n", "Created a chunk of size 1015, which is longer than the specified 1000\n", "Created a chunk of size 3138, which is longer than the specified 1000\n", "Created a chunk of size 2981, which is longer than the specified 1000\n", "Created a chunk of size 1316, which is longer than the specified 1000\n", "Created a chunk of size 1163, which is longer than the specified 1000\n", "Created a chunk of size 1939, which is longer than the specified 1000\n", "Created a chunk of size 1189, which is longer than the specified 1000\n", "Created a chunk of size 2918, which is longer than the specified 1000\n", "Created a chunk of size 1193, which is longer than the specified 1000\n", "Created a chunk of size 1045, which is longer than the specified 1000\n", "Created a chunk of size 1380, which is longer than the specified 1000\n", "Created a chunk of size 1135, which is longer than the specified 1000\n", "Created a chunk of size 1461, which is longer than the specified 1000\n", "Created a chunk of size 1287, which is longer than the specified 1000\n", "Created a chunk of size 1716, which is longer than the specified 1000\n", "Created a chunk of size 2132, which is longer than the specified 1000\n", "Created a chunk of size 1252, which is longer than the specified 1000\n", "Created a chunk of size 1183, which is longer than the specified 1000\n", "Created a chunk of size 1673, which is longer than the specified 1000\n", "Created a chunk of size 1645, which is longer than the specified 1000\n", "Created a chunk of size 1058, which is longer than the specified 1000\n", "Created a chunk of size 1401, which is longer than the specified 1000\n", "Created a chunk of size 1678, which is longer than the specified 1000\n", "Created a chunk of size 2278, which is longer than the specified 1000\n", "Created a chunk of size 1350, which is longer than the specified 1000\n", "Created a chunk of size 1406, which is longer than the specified 1000\n", "Created a chunk of size 1629, which is longer than the specified 1000\n", "Created a chunk of size 1694, which is longer than the specified 1000\n", "Created a chunk of size 1203, which is longer than the specified 1000\n", "Created a chunk of size 1045, which is longer than the specified 1000\n", "Created a chunk of size 2095, which is longer than the specified 1000\n", "Created a chunk of size 1215, which is longer than the specified 1000\n", "Created a chunk of size 2584, which is longer than the specified 1000\n", "Created a chunk of size 1448, which is longer than the specified 1000\n", "Created a chunk of size 2556, which is longer than the specified 1000\n", "Created a chunk of size 1055, which is longer than the specified 1000\n", "Created a chunk of size 1373, which is longer than the specified 1000\n", "Created a chunk of size 1303, which is longer than the specified 1000\n", "Created a chunk of size 1298, which is longer than the specified 1000\n", "Created a chunk of size 1354, which is longer than the specified 1000\n", "Created a chunk of size 2151, which is longer than the specified 1000\n", "Created a chunk of size 1258, which is longer than the specified 1000\n", "Created a chunk of size 1041, which is longer than the specified 1000\n", "Created a chunk of size 1383, which is longer than the specified 1000\n", "Created a chunk of size 1026, which is longer than the specified 1000\n", "Created a chunk of size 1340, which is longer than the specified 1000\n", "Created a chunk of size 1484, which is longer than the specified 1000\n", "Created a chunk of size 1354, which is longer than the specified 1000\n", "Created a chunk of size 1685, which is longer than the specified 1000\n", "Created a chunk of size 2132, which is longer than the specified 1000\n", "Created a chunk of size 1194, which is longer than the specified 1000\n", "Created a chunk of size 1433, which is longer than the specified 1000\n", "Created a chunk of size 1051, which is longer than the specified 1000\n", "Created a chunk of size 1047, which is longer than the specified 1000\n", "Created a chunk of size 1293, which is longer than the specified 1000\n", "Created a chunk of size 1568, which is longer than the specified 1000\n", "Created a chunk of size 1014, which is longer than the specified 1000\n", "Created a chunk of size 2151, which is longer than the specified 1000\n", "Created a chunk of size 2046, which is longer than the specified 1000\n", "Created a chunk of size 1258, which is longer than the specified 1000\n", "Created a chunk of size 2128, which is longer than the specified 1000\n", "Created a chunk of size 1554, which is longer than the specified 1000\n", "Created a chunk of size 1523, which is longer than the specified 1000\n", "Created a chunk of size 1738, which is longer than the specified 1000\n", "Created a chunk of size 1317, which is longer than the specified 1000\n", "Created a chunk of size 1118, which is longer than the specified 1000\n", "Created a chunk of size 1123, which is longer than the specified 1000\n", "Created a chunk of size 1309, which is longer than the specified 1000\n", "Created a chunk of size 1234, which is longer than the specified 1000\n", "Created a chunk of size 1190, which is longer than the specified 1000\n", "Created a chunk of size 1449, which is longer than the specified 1000\n", "Created a chunk of size 1212, which is longer than the specified 1000\n", "Created a chunk of size 1407, which is longer than the specified 1000\n", "Created a chunk of size 1382, which is longer than the specified 1000\n", "Created a chunk of size 1640, which is longer than the specified 1000\n", "Created a chunk of size 1144, which is longer than the specified 1000\n", "Created a chunk of size 1149, which is longer than the specified 1000\n", "Created a chunk of size 1253, which is longer than the specified 1000\n", "Created a chunk of size 1239, which is longer than the specified 1000\n", "Created a chunk of size 1049, which is longer than the specified 1000\n", "Created a chunk of size 1721, which is longer than the specified 1000\n", "Created a chunk of size 1493, which is longer than the specified 1000\n", "Created a chunk of size 1213, which is longer than the specified 1000\n", "Created a chunk of size 1328, which is longer than the specified 1000\n", "Created a chunk of size 1171, which is longer than the specified 1000\n", "Created a chunk of size 1064, which is longer than the specified 1000\n", "Created a chunk of size 1072, which is longer than the specified 1000\n", "Created a chunk of size 1047, which is longer than the specified 1000\n", "Created a chunk of size 1075, which is longer than the specified 1000\n", "Created a chunk of size 1947, which is longer than the specified 1000\n", "Created a chunk of size 1027, which is longer than the specified 1000\n", "Created a chunk of size 1178, which is longer than the specified 1000\n", "Created a chunk of size 1298, which is longer than the specified 1000\n", "Created a chunk of size 1053, which is longer than the specified 1000\n", "Created a chunk of size 1569, which is longer than the specified 1000\n", "Created a chunk of size 1444, which is longer than the specified 1000\n", "Created a chunk of size 1088, which is longer than the specified 1000\n", "Created a chunk of size 1395, which is longer than the specified 1000\n", "Created a chunk of size 1055, which is longer than the specified 1000\n", "Created a chunk of size 2274, which is longer than the specified 1000\n", "Created a chunk of size 1252, which is longer than the specified 1000\n", "Created a chunk of size 1163, which is longer than the specified 1000\n", "Created a chunk of size 1222, which is longer than the specified 1000\n", "Created a chunk of size 1520, which is longer than the specified 1000\n", "Created a chunk of size 1506, which is longer than the specified 1000\n", "Created a chunk of size 1335, which is longer than the specified 1000\n", "Created a chunk of size 1099, which is longer than the specified 1000\n", "Created a chunk of size 2014, which is longer than the specified 1000\n", "Created a chunk of size 1079, which is longer than the specified 1000\n", "Created a chunk of size 1227, which is longer than the specified 1000\n", "Created a chunk of size 1376, which is longer than the specified 1000\n", "Created a chunk of size 1131, which is longer than the specified 1000\n", "Created a chunk of size 1148, which is longer than the specified 1000\n", "Created a chunk of size 1224, which is longer than the specified 1000\n", "Created a chunk of size 1619, which is longer than the specified 1000\n"]}], "source": ["from langchain_text_splitters import CharacterTextSplitter\n", "\n", "text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)\n", "texts = text_splitter.split_documents(docs)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Execute the indexing. This will take about ~4 mins to compute embeddings and upload to Activeloop. You can then publish the dataset to be public."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Deep Lake Dataset in hub://adilkhan/twitter-algorithm already exists, loading from the storage\n", "Batch upload: 31310 samples are being uploaded in 32 batches of batch size 1000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Evaluating ingest: 28%|██▊       | 9/32 [00:50<02:09Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 851354 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 38%|███▊      | 12/32 [01:13<02:09Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 836180 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 41%|████      | 13/32 [01:29<02:43Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 875259 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 802651 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 44%|████▍     | 14/32 [01:42<02:57Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 884425 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 47%|████▋     | 15/32 [01:51<02:41Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 815327 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 50%|█████     | 16/32 [02:05<02:52Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 867281 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 53%|█████▎    | 17/32 [02:14<02:34Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 908595 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 834375 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 56%|█████▋    | 18/32 [02:26<02:33Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 904522 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 62%|██████▎   | 20/32 [02:44<02:01Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 938638 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 863952 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 66%|██████▌   | 21/32 [02:57<01:58Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 906069 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 833688 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 72%|███████▏  | 23/32 [03:21<01:40Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 855806 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 75%|███████▌  | 24/32 [03:31<01:26Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 845993 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 81%|████████▏ | 26/32 [03:50<01:01Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 904644 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 832413 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 84%|████████▍ | 27/32 [04:03<00:54Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 912569 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 839877 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 91%|█████████ | 29/32 [04:25<00:32Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 890015 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Retrying langchain.embeddings.openai.embed_with_retry.<locals>._embed_with_retry in 4.0 seconds as it raised RateLimitError: Rate limit reached for default-text-embedding-ada-002 in organization org-ciTI4gyz985F6II5qjFfD4Gf on tokens per min. Limit: 1000000 / min. Current: 814898 / min. Contact us through our help center at help.openai.com if you continue to have issues..\n", "Evaluating ingest: 100%|██████████| 32/32 [04:54<00:00\n", "/"]}, {"name": "stdout", "output_type": "stream", "text": ["Dataset(path='hub://adilkhan/twitter-algorithm', tensors=['embedding', 'ids', 'metadata', 'text'])\n", "\n", "  tensor     htype       shape       dtype  compression\n", "  -------   -------     -------     -------  ------- \n", " embedding  generic  (31310, 1536)   None     None   \n", "    ids      text     (31310, 1)      str     None   \n", " metadata    json     (31310, 1)      str     None   \n", "   text      text     (31310, 1)      str     None   \n"]}, {"name": "stderr", "output_type": "stream", "text": [" \r"]}, {"data": {"text/plain": ["['081a3beb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bec-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bed-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bee-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bef-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bf9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bfa-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bfb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bfc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bfd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bfe-3a8d-11ee-b840-13905694aaaf',\n", " '081a3bff-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c00-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c01-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c02-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c03-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c04-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c05-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c06-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c07-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c08-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c09-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c0a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c0b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c0c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c0d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c0e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c0f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c10-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c11-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c12-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c13-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c14-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c15-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c16-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c17-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c18-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c19-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c1a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c1b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c1c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c1d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c1e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c1f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c20-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c21-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c22-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c23-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c24-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c25-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c26-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c27-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c28-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c29-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c2a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c2b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c2c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c2d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c2e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c2f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c30-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c31-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c32-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c33-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c34-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c35-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c36-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c37-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c38-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c39-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c3a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c3b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c3c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c3d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c3e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c3f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c40-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c41-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c42-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c43-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c44-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c45-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c46-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c47-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c48-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c49-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c4a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c4b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c4c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c4d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c4e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c4f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c50-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c51-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c52-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c53-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c54-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c55-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c56-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c57-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c58-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c59-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c5a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c5b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c5c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c5d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c5e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c5f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c60-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c61-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c62-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c63-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c64-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c65-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c66-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c67-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c68-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c69-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c6a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c6b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c6c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c6d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c6e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c6f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c70-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c71-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c72-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c73-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c74-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c75-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c76-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c77-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c78-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c79-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c7a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c7b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c7c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c7d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c7e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c7f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c80-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c81-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c82-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c83-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c84-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c85-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c86-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c87-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c88-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c89-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c8a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c8b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c8c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c8d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c8e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c8f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c90-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c91-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c92-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c93-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c94-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c95-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c96-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c97-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c98-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c99-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c9a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c9b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c9c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c9d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c9e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3c9f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ca9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3caa-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cab-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cac-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cad-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cae-3a8d-11ee-b840-13905694aaaf',\n", " '081a3caf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cb9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cba-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cbb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cbc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cbd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cbe-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cbf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cc9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cca-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ccb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ccc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ccd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cce-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ccf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cd9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cda-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cdb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cdc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cdd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cde-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cdf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ce9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cea-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ceb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cec-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ced-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cee-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cef-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cf9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cfa-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cfb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cfc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cfd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cfe-3a8d-11ee-b840-13905694aaaf',\n", " '081a3cff-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d00-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d01-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d02-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d03-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d04-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d05-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d06-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d07-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d08-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d09-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d0a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d0b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d0c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d0d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d0e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d0f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d10-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d11-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d12-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d13-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d14-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d15-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d16-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d17-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d18-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d19-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d1a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d1b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d1c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d1d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d1e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d1f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d20-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d21-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d22-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d23-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d24-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d25-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d26-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d27-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d28-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d29-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d2a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d2b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d2c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d2d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d2e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d2f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d30-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d31-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d32-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d33-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d34-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d35-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d36-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d37-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d38-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d39-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d3a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d3b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d3c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d3d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d3e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d3f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d40-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d41-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d42-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d43-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d44-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d45-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d46-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d47-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d48-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d49-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d4a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d4b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d4c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d4d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d4e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d4f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d50-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d51-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d52-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d53-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d54-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d55-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d56-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d57-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d58-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d59-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d5a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d5b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d5c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d5d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d5e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d5f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d60-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d61-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d62-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d63-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d64-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d65-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d66-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d67-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d68-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d69-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d6a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d6b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d6c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d6d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d6e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d6f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d70-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d71-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d72-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d73-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d74-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d75-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d76-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d77-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d78-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d79-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d7a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d7b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d7c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d7d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d7e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d7f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d80-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d81-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d82-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d83-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d84-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d85-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d86-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d87-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d88-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d89-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d8a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d8b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d8c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d8d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d8e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d8f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d90-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d91-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d92-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d93-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d94-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d95-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d96-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d97-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d98-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d99-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d9a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d9b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d9c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d9d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d9e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3d9f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3da9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3daa-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dab-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dac-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dad-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dae-3a8d-11ee-b840-13905694aaaf',\n", " '081a3daf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3db9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dba-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dbb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dbc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dbd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dbe-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dbf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dc9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dca-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dcb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dcc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dcd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dce-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dcf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dd9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dda-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ddb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ddc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ddd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dde-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ddf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3de9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dea-3a8d-11ee-b840-13905694aaaf',\n", " '081a3deb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dec-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ded-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dee-3a8d-11ee-b840-13905694aaaf',\n", " '081a3def-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3df9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dfa-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dfb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dfc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dfd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dfe-3a8d-11ee-b840-13905694aaaf',\n", " '081a3dff-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e00-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e01-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e02-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e03-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e04-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e05-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e06-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e07-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e08-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e09-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e0a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e0b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e0c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e0d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e0e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e0f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e10-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e11-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e12-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e13-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e14-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e15-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e16-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e17-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e18-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e19-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e1a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e1b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e1c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e1d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e1e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e1f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e20-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e21-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e22-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e23-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e24-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e25-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e26-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e27-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e28-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e29-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e2a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e2b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e2c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e2d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e2e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e2f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e30-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e31-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e32-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e33-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e34-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e35-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e36-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e37-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e38-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e39-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e3a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e3b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e3c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e3d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e3e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e3f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e40-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e41-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e42-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e43-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e44-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e45-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e46-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e47-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e48-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e49-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e4a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e4b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e4c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e4d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e4e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e4f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e50-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e51-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e52-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e53-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e54-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e55-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e56-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e57-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e58-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e59-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e5a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e5b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e5c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e5d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e5e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e5f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e60-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e61-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e62-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e63-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e64-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e65-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e66-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e67-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e68-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e69-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e6a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e6b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e6c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e6d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e6e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e6f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e70-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e71-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e72-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e73-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e74-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e75-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e76-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e77-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e78-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e79-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e7a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e7b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e7c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e7d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e7e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e7f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e80-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e81-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e82-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e83-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e84-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e85-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e86-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e87-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e88-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e89-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e8a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e8b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e8c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e8d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e8e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e8f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e90-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e91-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e92-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e93-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e94-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e95-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e96-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e97-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e98-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e99-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e9a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e9b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e9c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e9d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e9e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3e9f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ea9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eaa-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eab-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eac-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ead-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eae-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eaf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eb9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eba-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ebb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ebc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ebd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ebe-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ebf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ec9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eca-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ecb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ecc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ecd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ece-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ecf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ed9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eda-3a8d-11ee-b840-13905694aaaf',\n", " '081a3edb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3edc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3edd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ede-3a8d-11ee-b840-13905694aaaf',\n", " '081a3edf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ee9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eea-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eeb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eec-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eed-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eee-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eef-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3ef9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3efa-3a8d-11ee-b840-13905694aaaf',\n", " '081a3efb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3efc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3efd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3efe-3a8d-11ee-b840-13905694aaaf',\n", " '081a3eff-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f00-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f01-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f02-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f03-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f04-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f05-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f06-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f07-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f08-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f09-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f0a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f0b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f0c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f0d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f0e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f0f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f10-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f11-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f12-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f13-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f14-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f15-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f16-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f17-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f18-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f19-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f1a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f1b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f1c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f1d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f1e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f1f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f20-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f21-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f22-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f23-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f24-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f25-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f26-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f27-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f28-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f29-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f2a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f2b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f2c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f2d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f2e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f2f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f30-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f31-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f32-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f33-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f34-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f35-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f36-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f37-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f38-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f39-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f3a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f3b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f3c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f3d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f3e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f3f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f40-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f41-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f42-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f43-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f44-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f45-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f46-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f47-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f48-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f49-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f4a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f4b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f4c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f4d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f4e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f4f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f50-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f51-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f52-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f53-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f54-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f55-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f56-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f57-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f58-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f59-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f5a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f5b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f5c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f5d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f5e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f5f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f60-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f61-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f62-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f63-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f64-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f65-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f66-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f67-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f68-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f69-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f6a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f6b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f6c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f6d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f6e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f6f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f70-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f71-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f72-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f73-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f74-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f75-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f76-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f77-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f78-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f79-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f7a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f7b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f7c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f7d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f7e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f7f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f80-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f81-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f82-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f83-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f84-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f85-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f86-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f87-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f88-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f89-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f8a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f8b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f8c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f8d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f8e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f8f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f90-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f91-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f92-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f93-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f94-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f95-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f96-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f97-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f98-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f99-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f9a-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f9b-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f9c-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f9d-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f9e-3a8d-11ee-b840-13905694aaaf',\n", " '081a3f9f-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fa9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3faa-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fab-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fac-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fad-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fae-3a8d-11ee-b840-13905694aaaf',\n", " '081a3faf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fb9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fba-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fbb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fbc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fbd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fbe-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fbf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc1-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc2-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc3-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc4-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc5-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc6-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc7-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc8-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fc9-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fca-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fcb-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fcc-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fcd-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fce-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fcf-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fd0-3a8d-11ee-b840-13905694aaaf',\n", " '081a3fd1-3a8d-11ee-b840-13905694aaaf',\n", " '08d1623e-3a8d-11ee-b840-13905694aaaf',\n", " ...]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["username = \"<USERNAME_OR_ORG>\"  # replace with your username from app.activeloop.ai\n", "db = DeepLake(\n", "    dataset_path=f\"hub://{username}/twitter-algorithm\",\n", "    embedding=embeddings,\n", ")\n", "db.add_documents(texts)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["`Optional`: You can also use Deep Lake's Managed Tensor Database as a hosting service and run queries there. In order to do so, it is necessary to specify the runtime parameter as {'tensor_db': True} during the creation of the vector store. This configuration enables the execution of queries on the Managed Tensor Database, rather than on the client side. It should be noted that this functionality is not applicable to datasets stored locally or in-memory. In the event that a vector store has already been created outside of the Managed Tensor Database, it is possible to transfer it to the Managed Tensor Database by following the prescribed steps."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# username = \"davitbun\"  # replace with your username from app.activeloop.ai\n", "# db = DeepLake(\n", "#     dataset_path=f\"hub://{username}/twitter-algorithm\",\n", "#     embedding_function=embeddings,\n", "#     runtime={\"tensor_db\": True}\n", "# )\n", "# db.add_documents(texts)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2. Question Answering on Twitter algorithm codebase\n", "First load the dataset, construct the retriever, then construct the Conversational Chain"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Deep Lake Dataset in hub://adilkhan/twitter-algorithm already exists, loading from the storage\n"]}], "source": ["db = DeepLake(\n", "    dataset_path=f\"hub://{username}/twitter-algorithm\",\n", "    read_only=True,\n", "    embedding=embeddings,\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["retriever = db.as_retriever()\n", "retriever.search_kwargs[\"distance_metric\"] = \"cos\"\n", "retriever.search_kwargs[\"fetch_k\"] = 100\n", "retriever.search_kwargs[\"maximal_marginal_relevance\"] = True\n", "retriever.search_kwargs[\"k\"] = 10"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["You can also specify user defined functions using [Deep Lake filters](https://docs.deeplake.ai/en/latest/deeplake.core.dataset.html#deeplake.core.dataset.Dataset.filter)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def filter(x):\n", "    # filter based on source code\n", "    if \"com.google\" in x[\"text\"].data()[\"value\"]:\n", "        return False\n", "\n", "    # filter based on path e.g. extension\n", "    metadata = x[\"metadata\"].data()[\"value\"]\n", "    return \"scala\" in metadata[\"source\"] or \"py\" in metadata[\"source\"]\n", "\n", "\n", "### turn on below for custom filtering\n", "# retriever.search_kwargs['filter'] = filter"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from langchain.chains import ConversationalRetrievalChain\n", "from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"gpt-3.5-turbo-0613\")  # switch to 'gpt-4'\n", "qa = ConversationalRetrievalChain.from_llm(model, retriever=retriever)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["questions = [\n", "    \"What does favCountParams do?\",\n", "    \"is it Likes + Bookmarks, or not clear from the code?\",\n", "    \"What are the major negative modifiers that lower your linear ranking parameters?\",\n", "    \"How do you get assigned to SimClusters?\",\n", "    \"What is needed to migrate from one SimClusters to another SimClusters?\",\n", "    \"How much do I get boosted within my cluster?\",\n", "    \"How does Heavy ranker work. what are it’s main inputs?\",\n", "    \"How can one influence Heavy ranker?\",\n", "    \"why threads and long tweets do so well on the platform?\",\n", "    \"Are thread and long tweet creators building a following that reacts to only threads?\",\n", "    \"Do you need to follow different strategies to get most followers vs to get most likes and bookmarks per tweet?\",\n", "    \"Content meta data and how it impacts virality (e.g. ALT in images).\",\n", "    \"What are some unexpected fingerprints for spam factors?\",\n", "    \"Is there any difference between company verified checkmarks and blue verified individual checkmarks?\",\n", "]\n", "chat_history = []\n", "\n", "for question in questions:\n", "    result = qa({\"question\": question, \"chat_history\": chat_history})\n", "    chat_history.append((question, result[\"answer\"]))\n", "    print(f\"-> **Question**: {question} \\n\")\n", "    print(f\"**Answer**: {result['answer']} \\n\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["-> **Question**: What does favCountParams do? \n", "\n", "**Answer**: `favCountParams` is an optional ThriftLinearFeatureRankingParams instance that represents the parameters related to the \"favorite count\" feature in the ranking process. It is used to control the weight of the favorite count feature while ranking tweets. The favorite count is the number of times a tweet has been marked as a favorite by users, and it is considered an important signal in the ranking of tweets. By using `favCountParams`, the system can adjust the importance of the favorite count while calculating the final ranking score of a tweet. \n", "\n", "-> **Question**: is it Likes + Bookmarks, or not clear from the code?\n", "\n", "**Answer**: From the provided code, it is not clear if the favorite count metric is determined by the sum of likes and bookmarks. The favorite count is mentioned in the code, but there is no explicit reference to how it is calculated in terms of likes and bookmarks. \n", "\n", "-> **Question**: What are the major negative modifiers that lower your linear ranking parameters?\n", "\n", "**Answer**: In the given code, major negative modifiers that lower the linear ranking parameters are:\n", "\n", "1. `scoringData.querySpecificScore`: This score adjustment is based on the query-specific information. If its value is negative, it will lower the linear ranking parameters.\n", "\n", "2. `scoringData.authorSpecificScore`: This score adjustment is based on the author-specific information. If its value is negative, it will also lower the linear ranking parameters.\n", "\n", "Please note that I cannot provide more information on the exact calculations of these negative modifiers, as the code for their determination is not provided. \n", "\n", "-> **Question**: How do you get assigned to SimClusters?\n", "\n", "**Answer**: The assignment to SimClusters occurs through a Metropolis-Hastings sampling-based community detection algorithm that is run on the Producer-Producer similarity graph. This graph is created by computing the cosine similarity scores between the users who follow each producer. The algorithm identifies communities or clusters of Producers with similar followers, and takes a parameter *k* for specifying the number of communities to be detected.\n", "\n", "After the community detection, different users and content are represented as sparse, interpretable vectors within these identified communities (SimClusters). The resulting SimClusters embeddings can be used for various recommendation tasks. \n", "\n", "-> **Question**: What is needed to migrate from one SimClusters to another SimClusters?\n", "\n", "**Answer**: To migrate from one SimClusters representation to another, you can follow these general steps:\n", "\n", "1. **Prepare the new representation**: Create the new SimClusters representation using any necessary updates or changes in the clustering algorithm, similarity measures, or other model parameters. Ensure that this new representation is properly stored and indexed as needed.\n", "\n", "2. **Update the relevant code and configurations**: Modify the relevant code and configuration files to reference the new SimClusters representation. This may involve updating paths or dataset names to point to the new representation, as well as changing code to use the new clustering method or similarity functions if applicable.\n", "\n", "3. **Test the new representation**: Before deploying the changes to production, thoroughly test the new SimClusters representation to ensure its effectiveness and stability. This may involve running offline jobs like candidate generation and label candidates, validating the output, as well as testing the new representation in the evaluation environment using evaluation tools like TweetSimilarityEvaluationAdhocApp.\n", "\n", "4. **Deploy the changes**: Once the new representation has been tested and validated, deploy the changes to production. This may involve creating a zip file, uploading it to the packer, and then scheduling it with Aurora. Be sure to monitor the system to ensure a smooth transition between representations and verify that the new representation is being used in recommendations as expected.\n", "\n", "5. **Monitor and assess the new representation**: After the new representation has been deployed, continue to monitor its performance and impact on recommendations. Take note of any improvements or issues that arise and be prepared to iterate on the new representation if needed. Always ensure that the results and performance metrics align with the system's goals and objectives. \n", "\n", "-> **Question**: How much do I get boosted within my cluster?\n", "\n", "**Answer**: It's not possible to determine the exact amount your content is boosted within your cluster in the SimClusters representation without specific data about your content and its engagement metrics. However, a combination of factors, such as the favorite score and follow score, alongside other engagement signals and SimCluster calculations, influence the boosting of content. \n", "\n", "-> **Question**: How does Heavy ranker work. what are it’s main inputs?\n", "\n", "**Answer**: The Heavy Ranker is a machine learning model that plays a crucial role in ranking and scoring candidates within the recommendation algorithm. Its primary purpose is to predict the likelihood of a user engaging with a tweet or connecting with another user on the platform.\n", "\n", "Main inputs to the Heavy Ranker consist of:\n", "\n", "1. Static Features: These are features that can be computed directly from a tweet at the time it's created, such as whether it has a URL, has cards, has quotes, etc. These features are produced by the Index Ingester as the tweets are generated and stored in the index.\n", "\n", "2. Real-time Features: These per-tweet features can change after the tweet has been indexed. They mostly consist of social engagements like retweet count, favorite count, reply count, and some spam signals that are computed with later activities. The Signal Ingester, which is part of a Heron topology, processes multiple event streams to collect and compute these real-time features.\n", "\n", "3. User Table Features: These per-user features are obtained from the User Table Updater that processes a stream written by the user service. This input is used to store sparse real-time user information, which is later propagated to the tweet being scored by looking up the author of the tweet.\n", "\n", "4. Search Context Features: These features represent the context of the current searcher, like their UI language, their content consumption, and the current time (implied). They are combined with Tweet Data to compute some of the features used in scoring.\n", "\n", "These inputs are then processed by the Heavy Ranker to score and rank candidates based on their relevance and likelihood of engagement by the user. \n", "\n", "-> **Question**: How can one influence Heavy ranker?\n", "\n", "**Answer**: To influence the Heavy Ranker's output or ranking of content, consider the following actions:\n", "\n", "1. Improve content quality: Create high-quality and engaging content that is relevant, informative, and valuable to users. High-quality content is more likely to receive positive user engagement, which the Heavy Ranker considers when ranking content.\n", "\n", "2. Increase user engagement: Encourage users to interact with content through likes, retweets, replies, and comments. Higher engagement levels can lead to better ranking in the Heavy Ranker's output.\n", "\n", "3. Optimize your user profile: A user's reputation, based on factors such as their follower count and follower-to-following ratio, may impact the ranking of their content. Maintain a good reputation by following relevant users, keeping a reasonable follower-to-following ratio and engaging with your followers.\n", "\n", "4. Enhance content discoverability: Use relevant keywords, hashtags, and mentions in your tweets, making it easier for users to find and engage with your content. This increased discoverability may help improve the ranking of your content by the Heavy Ranker.\n", "\n", "5. Leverage multimedia content: Experiment with different content formats, such as videos, images, and GIFs, which may capture users' attention and increase engagement, resulting in better ranking by the Heavy Ranker.\n", "\n", "6. User feedback: Monitor and respond to feedback for your content. Positive feedback may improve your ranking, while negative feedback provides an opportunity to learn and improve.\n", "\n", "Note that the Heavy Ranker uses a combination of machine learning models and various features to rank the content. While the above actions may help influence the ranking, there are no guarantees as the ranking process is determined by a complex algorithm, which evolves over time. \n", "\n", "-> **Question**: why threads and long tweets do so well on the platform?\n", "\n", "**Answer**: Threads and long tweets perform well on the platform for several reasons:\n", "\n", "1. **More content and context**: Threads and long tweets provide more information and context about a topic, which can make the content more engaging and informative for users. People tend to appreciate a well-structured and detailed explanation of a subject or a story, and threads and long tweets can do that effectively.\n", "\n", "2. **Increased user engagement**: As threads and long tweets provide more content, they also encourage users to engage with the tweets through replies, retweets, and likes. This increased engagement can lead to better visibility of the content, as the Twitter algorithm considers user engagement when ranking and surfacing tweets.\n", "\n", "3. **Narrative structure**: Threads enable users to tell stories or present arguments in a step-by-step manner, making the information more accessible and easier to follow. This narrative structure can capture users' attention and encourage them to read through the entire thread and interact with the content.\n", "\n", "4. **Expanded reach**: When users engage with a thread, their interactions can bring the content to the attention of their followers, helping to expand the reach of the thread. This increased visibility can lead to more interactions and higher performance for the threaded tweets.\n", "\n", "5. **Higher content quality**: Generally, threads and long tweets require more thought and effort to create, which may lead to higher quality content. Users are more likely to appreciate and interact with high-quality, well-reasoned content, further improving the performance of these tweets within the platform.\n", "\n", "Overall, threads and long tweets perform well on Twitter because they encourage user engagement and provide a richer, more informative experience that users find valuable. \n", "\n", "-> **Question**: Are thread and long tweet creators building a following that reacts to only threads?\n", "\n", "**Answer**: Based on the provided code and context, there isn't enough information to conclude if the creators of threads and long tweets primarily build a following that engages with only thread-based content. The code provided is focused on Twitter's recommendation and ranking algorithms, as well as infrastructure components like Kafka, partitions, and the Follow Recommendations Service (FRS). To answer your question, data analysis of user engagement and results of specific edge cases would be required. \n", "\n", "-> **Question**: Do you need to follow different strategies to get most followers vs to get most likes and bookmarks per tweet?\n", "\n", "**Answer**: Yes, different strategies need to be followed to maximize the number of followers compared to maximizing likes and bookmarks per tweet. While there may be some overlap in the approaches, they target different aspects of user engagement.\n", "\n", "Maximizing followers: The primary focus is on growing your audience on the platform. Strategies include:\n", "\n", "1. Consistently sharing high-quality content related to your niche or industry.\n", "2. Engaging with others on the platform by replying, retweeting, and mentioning other users.\n", "3. Using relevant hashtags and participating in trending conversations.\n", "4. Collaborating with influencers and other users with a large following.\n", "5. Posting at optimal times when your target audience is most active.\n", "6. Optimizing your profile by using a clear profile picture, catchy bio, and relevant links.\n", "\n", "Maximizing likes and bookmarks per tweet: The focus is on creating content that resonates with your existing audience and encourages engagement. Strategies include:\n", "\n", "1. Crafting engaging and well-written tweets that encourage users to like or save them.\n", "2. Incorporating visually appealing elements, such as images, GIFs, or videos, that capture attention.\n", "3. Asking questions, sharing opinions, or sparking conversations that encourage users to engage with your tweets.\n", "4. Using analytics to understand the type of content that resonates with your audience and tailoring your tweets accordingly.\n", "5. Posting a mix of educational, entertaining, and promotional content to maintain variety and interest.\n", "6. Timing your tweets strategically to maximize engagement, likes, and bookmarks per tweet.\n", "\n", "Both strategies can overlap, and you may need to adapt your approach by understanding your target audience's preferences and analyzing your account's performance. However, it's essential to recognize that maximizing followers and maximizing likes and bookmarks per tweet have different focuses and require specific strategies. \n", "\n", "-> **Question**: Content meta data and how it impacts virality (e.g. ALT in images).\n", "\n", "**Answer**: There is no direct information in the provided context about how content metadata, such as ALT text in images, impacts the virality of a tweet or post. However, it's worth noting that including ALT text can improve the accessibility of your content for users who rely on screen readers, which may lead to increased engagement for a broader audience. Additionally, metadata can be used in search engine optimization, which might improve the visibility of the content, but the context provided does not mention any specific correlation with virality. \n", "\n", "-> **Question**: What are some unexpected fingerprints for spam factors?\n", "\n", "**Answer**: In the provided context, an unusual indicator of spam factors is when a tweet contains a non-media, non-news link. If the tweet has a link but does not have an image URL, video URL, or news URL, it is considered a potential spam vector, and a threshold for user reputation (tweepCredThreshold) is set to MIN_TWEEPCRED_WITH_LINK.\n", "\n", "While this rule may not cover all possible unusual spam indicators, it is derived from the specific codebase and logic shared in the context. \n", "\n", "-> **Question**: Is there any difference between company verified checkmarks and blue verified individual checkmarks?\n", "\n", "**Answer**: Yes, there is a distinction between the verified checkmarks for companies and blue verified checkmarks for individuals. The code snippet provided mentions \"Blue-verified account boost\" which indicates that there is a separate category for blue verified accounts. Typically, blue verified checkmarks are used to indicate notable individuals, while verified checkmarks are for companies or organizations. \n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}