{"cells": [{"cell_type": "code", "execution_count": null, "id": "a384cc48-0425-4e8f-aafc-cfb8e56025c9", "metadata": {}, "outputs": [], "source": ["! pip install langchain-chroma langchain_community tiktoken langchain-openai langchainhub langchain langgraph"]}, {"attachments": {"ea6a57d2-f2ec-4061-840a-98deb3207248.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "919fe33c-0149-4f7d-b200-544a18986c9a", "metadata": {}, "source": ["# Self-RAG\n", "\n", "Self-RAG is a recent paper that introduces an interesting approach for active RAG. \n", "\n", "The framework trains a single arbitrary LM (LLaMA2-7b, 13b) to generate tokens that govern the RAG process:\n", "\n", "1. Should I retrieve from retriever, `R` -\n", "\n", "* Token: `Retrieve`\n", "* Input: `x (question)` OR `x (question)`, `y (generation)`\n", "* Decides when to retrieve `D` chunks with `R`\n", "* Output: `yes, no, continue`\n", "\n", "2. Are the retrieved passages `D` relevant to the question `x` -\n", "\n", "* Token: `ISREL`\n", "* * Input: (`x (question)`, `d (chunk)`) for `d` in `D`\n", "* `d` provides useful information to solve `x`\n", "* Output: `relevant, irrelevant`\n", "\n", "\n", "3. Are the LLM generation from each chunk in `D` is relevant to the chunk (hallucinations, etc)  -\n", "\n", "* Token: `ISSUP`\n", "* Input: `x (question)`, `d (chunk)`,  `y (generation)` for `d` in `D`\n", "* All of the verification-worthy statements in `y (generation)` are supported by `d`\n", "* Output: `{fully supported, partially supported, no support`\n", "\n", "4. The LLM generation from each chunk in `D` is a useful response to `x (question)` -\n", "\n", "* Token: `ISUSE`\n", "* Input: `x (question)`, `y (generation)` for `d` in `D`\n", "* `y (generation)` is a useful response to `x (question)`.\n", "* Output: `{5, 4, 3, 2, 1}`\n", "\n", "We can represent this as a graph:\n", "\n", "![Screenshot 2024-02-02 at 1.36.44 PM.png](attachment:ea6a57d2-f2ec-4061-840a-98deb3207248.png)\n", "\n", "Paper -\n", "\n", "https://arxiv.org/abs/2310.11511\n", "\n", "---\n", "\n", "Let's implement this from scratch using [LangGraph](https://python.langchain.com/docs/langgraph)."]}, {"cell_type": "markdown", "id": "c27bebdc-be71-4130-ab9d-42f09f87658b", "metadata": {}, "source": ["## Retriever\n", " \n", "Let's index 3 blog posts."]}, {"cell_type": "code", "execution_count": null, "id": "565a6d44-2c9f-4fff-b1ec-eea05df9350d", "metadata": {}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_chroma import Chroma\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n", "]\n", "\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=250, chunk_overlap=0\n", ")\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Add to vectorDB\n", "vectorstore = Chroma.from_documents(\n", "    documents=doc_splits,\n", "    collection_name=\"rag-chroma\",\n", "    embedding=OpenAIEmbeddings(),\n", ")\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "276001c5-c079-4e5b-9f42-81a06704d200", "metadata": {}, "source": ["## State\n", " \n", "We will define a graph.\n", "\n", "Our state will be a `dict`.\n", "\n", "We can access this from any graph node as `state['keys']`."]}, {"cell_type": "code", "execution_count": null, "id": "f1617e9e-66a8-4c1a-a1fe-cc936284c085", "metadata": {}, "outputs": [], "source": ["from typing import Dict, TypedDict\n", "\n", "from langchain_core.messages import BaseMessage\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of an agent in the conversation.\n", "\n", "    Attributes:\n", "        keys: A dictionary where each key is a string and the value is expected to be a list or another structure\n", "              that supports addition with `operator.add`. This could be used, for instance, to accumulate messages\n", "              or other pieces of data throughout the graph.\n", "    \"\"\"\n", "\n", "    keys: Dict[str, any]"]}, {"attachments": {"e61fbd0c-e667-4160-a96c-82f95a560b44.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "251feeea-c9a0-404a-8b55-bef3020bb5e2", "metadata": {}, "source": ["## Nodes and Edges\n", "\n", "Each `node` will simply modify the `state`.\n", "\n", "Each `edge` will choose which `node` to call next.\n", "\n", "We can lay out `self-RAG` as a graph:\n", "\n", "![Screenshot 2024-02-02 at 9.01.01 PM.png](attachment:e61fbd0c-e667-4160-a96c-82f95a560b44.png)"]}, {"cell_type": "code", "execution_count": null, "id": "add509d8-6682-4127-8d95-13dd37d79702", "metadata": {}, "outputs": [], "source": ["import json\n", "import operator\n", "from typing import Annotated, Sequence, TypedDict\n", "\n", "from langchain import hub\n", "from langchain.output_parsers import PydanticOutputParser\n", "from langchain.output_parsers.openai_tools import PydanticToolsParser\n", "from langchain.prompts import PromptTemplate\n", "from langchain_chroma import Chroma\n", "from langchain_core.messages import BaseMessage, FunctionMessage\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.utils.function_calling import convert_to_openai_tool\n", "from langchain_openai import ChatOpenAI, OpenAIEmbeddings\n", "from langgraph.prebuilt import ToolInvocation\n", "\n", "### Nodes ###\n", "\n", "\n", "def retrieve(state):\n", "    \"\"\"\n", "    Retrieve documents\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New key added to state, documents, that contains documents.\n", "    \"\"\"\n", "    print(\"---RETRIEVE---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = retriever.invoke(question)\n", "    return {\"keys\": {\"documents\": documents, \"question\": question}}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New key added to state, generation, that contains generation.\n", "    \"\"\"\n", "    print(\"---GENERATE---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "\n", "    # Prompt\n", "    prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "    # LLM\n", "    llm = ChatOpenAI(model=\"gpt-3.5-turbo\", temperature=0)\n", "\n", "    # Post-processing\n", "    def format_docs(docs):\n", "        return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "    # Chain\n", "    rag_chain = prompt | llm | StrOutputParser()\n", "\n", "    # Run\n", "    generation = rag_chain.invoke({\"context\": documents, \"question\": question})\n", "    return {\n", "        \"keys\": {\"documents\": documents, \"question\": question, \"generation\": generation}\n", "    }\n", "\n", "\n", "def grade_documents(state):\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New key added to state, filtered_documents, that contains relevant documents.\n", "    \"\"\"\n", "\n", "    print(\"---<PERSON><PERSON><PERSON> RELEVANCE---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "\n", "    # Data model\n", "    class grade(BaseModel):\n", "        \"\"\"Binary score for relevance check.\"\"\"\n", "\n", "        binary_score: str = Field(description=\"Relevance score 'yes' or 'no'\")\n", "\n", "    # LLM\n", "    model = ChatOpenAI(temperature=0, model=\"gpt-4-0125-preview\", streaming=True)\n", "\n", "    # Tool\n", "    grade_tool_oai = convert_to_openai_tool(grade)\n", "\n", "    # LLM with tool and enforce invocation\n", "    llm_with_tool = model.bind(\n", "        tools=[convert_to_openai_tool(grade_tool_oai)],\n", "        tool_choice={\"type\": \"function\", \"function\": {\"name\": \"grade\"}},\n", "    )\n", "\n", "    # Parser\n", "    parser_tool = PydanticToolsParser(tools=[grade])\n", "\n", "    # Prompt\n", "    prompt = PromptTemplate(\n", "        template=\"\"\"You are a grader assessing relevance of a retrieved document to a user question. \\n \n", "        Here is the retrieved document: \\n\\n {context} \\n\\n\n", "        Here is the user question: {question} \\n\n", "        If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \\n\n", "        Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\"\",\n", "        input_variables=[\"context\", \"question\"],\n", "    )\n", "\n", "    # Chain\n", "    chain = prompt | llm_with_tool | parser_tool\n", "\n", "    # Score\n", "    filtered_docs = []\n", "    for d in documents:\n", "        score = chain.invoke({\"question\": question, \"context\": d.page_content})\n", "        grade = score[0].binary_score\n", "        if grade == \"yes\":\n", "            print(\"---GRADE: DOCUMENT RELEVANT---\")\n", "            filtered_docs.append(d)\n", "        else:\n", "            print(\"---GRADE: DOCUMENT NOT RELEVANT---\")\n", "            continue\n", "\n", "    return {\"keys\": {\"documents\": filtered_docs, \"question\": question}}\n", "\n", "\n", "def transform_query(state):\n", "    \"\"\"\n", "    Transform the query to produce a better question.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New value saved to question.\n", "    \"\"\"\n", "\n", "    print(\"---TRANSFORM QUERY---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "\n", "    # Create a prompt template with format instructions and the query\n", "    prompt = PromptTemplate(\n", "        template=\"\"\"You are generating questions that is well optimized for retrieval. \\n \n", "        Look at the input and try to reason about the underlying semantic intent / meaning. \\n \n", "        Here is the initial question:\n", "        \\n ------- \\n\n", "        {question} \n", "        \\n ------- \\n\n", "        Formulate an improved question: \"\"\",\n", "        input_variables=[\"question\"],\n", "    )\n", "\n", "    # Grader\n", "    model = ChatOpenAI(temperature=0, model=\"gpt-4-0125-preview\", streaming=True)\n", "\n", "    # Prompt\n", "    chain = prompt | model | StrOutputParser()\n", "    better_question = chain.invoke({\"question\": question})\n", "\n", "    return {\"keys\": {\"documents\": documents, \"question\": better_question}}\n", "\n", "\n", "def prepare_for_final_grade(state):\n", "    \"\"\"\n", "    Stage for final grade, passthrough state.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        state (dict): The current state of the agent, including all keys.\n", "    \"\"\"\n", "\n", "    print(\"---FINAL GRADE---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "    generation = state_dict[\"generation\"]\n", "\n", "    return {\n", "        \"keys\": {\"documents\": documents, \"question\": question, \"generation\": generation}\n", "    }\n", "\n", "\n", "### Edges ###\n", "\n", "\n", "def decide_to_generate(state):\n", "    \"\"\"\n", "    Determines whether to generate an answer, or re-generate a question.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        dict: New key added to state, filtered_documents, that contains relevant documents.\n", "    \"\"\"\n", "\n", "    print(\"---DECIDE TO GENERATE---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    filtered_documents = state_dict[\"documents\"]\n", "\n", "    if not filtered_documents:\n", "        # All documents have been filtered check_relevance\n", "        # We will re-generate a new query\n", "        print(\"---DECISION: TRANSFORM QUERY---\")\n", "        return \"transform_query\"\n", "    else:\n", "        # We have relevant documents, so generate answer\n", "        print(\"---DECISION: GENERATE---\")\n", "        return \"generate\"\n", "\n", "\n", "def grade_generation_v_documents(state):\n", "    \"\"\"\n", "    Determines whether the generation is grounded in the document.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        str: Binary decision score.\n", "    \"\"\"\n", "\n", "    print(\"---GRADE GENERATION vs DOCUMENTS---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "    generation = state_dict[\"generation\"]\n", "\n", "    # Data model\n", "    class grade(BaseModel):\n", "        \"\"\"Binary score for relevance check.\"\"\"\n", "\n", "        binary_score: str = Field(description=\"Supported score 'yes' or 'no'\")\n", "\n", "    # LLM\n", "    model = ChatOpenAI(temperature=0, model=\"gpt-4-0125-preview\", streaming=True)\n", "\n", "    # Tool\n", "    grade_tool_oai = convert_to_openai_tool(grade)\n", "\n", "    # LLM with tool and enforce invocation\n", "    llm_with_tool = model.bind(\n", "        tools=[convert_to_openai_tool(grade_tool_oai)],\n", "        tool_choice={\"type\": \"function\", \"function\": {\"name\": \"grade\"}},\n", "    )\n", "\n", "    # Parser\n", "    parser_tool = PydanticToolsParser(tools=[grade])\n", "\n", "    # Prompt\n", "    prompt = PromptTemplate(\n", "        template=\"\"\"You are a grader assessing whether an answer is grounded in / supported by a set of facts. \\n \n", "        Here are the facts:\n", "        \\n ------- \\n\n", "        {documents} \n", "        \\n ------- \\n\n", "        Here is the answer: {generation}\n", "        Give a binary score 'yes' or 'no' to indicate whether the answer is grounded in / supported by a set of facts.\"\"\",\n", "        input_variables=[\"generation\", \"documents\"],\n", "    )\n", "\n", "    # Chain\n", "    chain = prompt | llm_with_tool | parser_tool\n", "\n", "    score = chain.invoke({\"generation\": generation, \"documents\": documents})\n", "    grade = score[0].binary_score\n", "\n", "    if grade == \"yes\":\n", "        print(\"---DECISION: SUPPORTED, MOVE TO FINAL GRADE---\")\n", "        return \"supported\"\n", "    else:\n", "        print(\"---DECISION: NOT SUPPORTED, GENERATE AGAIN---\")\n", "        return \"not supported\"\n", "\n", "\n", "def grade_generation_v_question(state):\n", "    \"\"\"\n", "    Determines whether the generation addresses the question.\n", "\n", "    Args:\n", "        state (dict): The current state of the agent, including all keys.\n", "\n", "    Returns:\n", "        str: Binary decision score.\n", "    \"\"\"\n", "\n", "    print(\"---GRADE GENERATION vs QUESTION---\")\n", "    state_dict = state[\"keys\"]\n", "    question = state_dict[\"question\"]\n", "    documents = state_dict[\"documents\"]\n", "    generation = state_dict[\"generation\"]\n", "\n", "    # Data model\n", "    class grade(BaseModel):\n", "        \"\"\"Binary score for relevance check.\"\"\"\n", "\n", "        binary_score: str = Field(description=\"Useful score 'yes' or 'no'\")\n", "\n", "    # LLM\n", "    model = ChatOpenAI(temperature=0, model=\"gpt-4-0125-preview\", streaming=True)\n", "\n", "    # Tool\n", "    grade_tool_oai = convert_to_openai_tool(grade)\n", "\n", "    # LLM with tool and enforce invocation\n", "    llm_with_tool = model.bind(\n", "        tools=[convert_to_openai_tool(grade_tool_oai)],\n", "        tool_choice={\"type\": \"function\", \"function\": {\"name\": \"grade\"}},\n", "    )\n", "\n", "    # Parser\n", "    parser_tool = PydanticToolsParser(tools=[grade])\n", "\n", "    # Prompt\n", "    prompt = PromptTemplate(\n", "        template=\"\"\"You are a grader assessing whether an answer is useful to resolve a question. \\n \n", "        Here is the answer:\n", "        \\n ------- \\n\n", "        {generation} \n", "        \\n ------- \\n\n", "        Here is the question: {question}\n", "        Give a binary score 'yes' or 'no' to indicate whether the answer is useful to resolve a question.\"\"\",\n", "        input_variables=[\"generation\", \"question\"],\n", "    )\n", "\n", "    # Prompt\n", "    chain = prompt | llm_with_tool | parser_tool\n", "\n", "    score = chain.invoke({\"generation\": generation, \"question\": question})\n", "    grade = score[0].binary_score\n", "\n", "    if grade == \"yes\":\n", "        print(\"---DECISION: USEFUL---\")\n", "        return \"useful\"\n", "    else:\n", "        print(\"---DECISION: NOT USEFUL---\")\n", "        return \"not useful\""]}, {"cell_type": "markdown", "id": "61cd5797-1782-4d78-a277-8196d13f3e1b", "metadata": {}, "source": ["## Graph"]}, {"cell_type": "code", "execution_count": null, "id": "0e09ca9f-e36d-4ef4-a0d5-79fdbada9fe0", "metadata": {}, "outputs": [], "source": ["import pprint\n", "\n", "from langgraph.graph import END, StateGraph\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # generatae\n", "workflow.add_node(\"transform_query\", transform_query)  # transform_query\n", "workflow.add_node(\"prepare_for_final_grade\", prepare_for_final_grade)  # passthrough\n", "\n", "# Build graph\n", "workflow.set_entry_point(\"retrieve\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\n", "        \"transform_query\": \"transform_query\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_edge(\"transform_query\", \"retrieve\")\n", "workflow.add_conditional_edges(\n", "    \"generate\",\n", "    grade_generation_v_documents,\n", "    {\n", "        \"supported\": \"prepare_for_final_grade\",\n", "        \"not supported\": \"generate\",\n", "    },\n", ")\n", "workflow.add_conditional_edges(\n", "    \"prepare_for_final_grade\",\n", "    grade_generation_v_question,\n", "    {\n", "        \"useful\": END,\n", "        \"not useful\": \"transform_query\",\n", "    },\n", ")\n", "\n", "# Compile\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": null, "id": "fb69dbb9-91ee-4868-8c3c-93af3cd885be", "metadata": {}, "outputs": [], "source": ["# Run\n", "inputs = {\"keys\": {\"question\": \"Explain how the different types of agent memory work?\"}}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        pprint.pprint(f\"Output from node '{key}':\")\n", "        pprint.pprint(\"---\")\n", "        pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint.pprint(\"\\n---\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "4138bc51-8c84-4b8a-8d24-f7f470721f6f", "metadata": {}, "outputs": [], "source": ["inputs = {\"keys\": {\"question\": \"Explain how chain of thought prompting works?\"}}\n", "for output in app.stream(inputs):\n", "    for key, value in output.items():\n", "        pprint.pprint(f\"Output from node '{key}':\")\n", "        pprint.pprint(\"---\")\n", "        pprint.pprint(value[\"keys\"], indent=2, width=80, depth=None)\n", "    pprint.pprint(\"\\n---\\n\")"]}, {"cell_type": "markdown", "id": "548f1c5b-4108-4aae-8abb-ec171b511b92", "metadata": {}, "source": ["Trace - \n", " \n", "* https://smith.langchain.com/public/55d6180f-aab8-42bc-8799-dadce6247d9b/r\n", "* https://smith.langchain.com/public/f85ebc95-81d9-47fc-91c6-b54e5b78f359/r"]}], "metadata": {"kernelspec": {"display_name": "Python 3.11.1 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}, "vscode": {"interpreter": {"hash": "1a1af0ee75eeea9e2e1ee996c87e7a2b11a0bebd85af04bb136d915cefc0abce"}}}, "nbformat": 4, "nbformat_minor": 5}