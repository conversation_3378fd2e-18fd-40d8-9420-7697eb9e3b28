{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Human input chat model\n", "\n", "Along with HumanInputLLM, Lang<PERSON><PERSON><PERSON> also provides a pseudo chat model class that can be used for testing, debugging, or educational purposes. This allows you to mock out calls to the chat model and simulate how a human would respond if they received the messages.\n", "\n", "In this notebook, we go over how to use this.\n", "\n", "We start this with using the HumanInputChatModel in an agent."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_models.human import HumanInputChatModel"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Since we will use the `WikipediaQueryRun` tool in this notebook, you might need to install the `wikipedia` package if you haven't done so already."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/dev/research/chatbot/github/langchain/.venv/bin/python: No module named pip\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install wikipedia"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain.agents import AgentType, initialize_agent, load_tools"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["tools = load_tools([\"wikipedia\"])\n", "llm = HumanInputChatModel()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["agent = initialize_agent(\n", "    tools, llm, agent=AgentType.CHAT_ZERO_SHOT_REACT_DESCRIPTION, verbose=True\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new  chain...\u001b[0m\n", "\n", " ======= start of message ======= \n", "\n", "\n", "type: system\n", "data:\n", "  content: \"Answer the following questions as best you can. You have access to the following tools:\\n\\nWikipedia: A wrapper around Wikipedia. Useful for when you need to answer general questions about people, places, companies, facts, historical events, or other subjects. Input should be a search query.\\n\\nThe way you use the tools is by specifying a json blob.\\nSpecifically, this json should have a `action` key (with the name of the tool to use) and a `action_input` key (with the input to the tool going here).\\n\\nThe only values that should be in the \\\"action\\\" field are: Wikipedia\\n\\nThe $JSON_BLOB should only contain a SINGLE action, do NOT return a list of multiple actions. Here is an example of a valid $JSON_BLOB:\\n\\n```\\n{\\n  \\\"action\\\": $TOOL_NAME,\\n  \\\"action_input\\\": $INPUT\\n}\\n```\\n\\nALWAYS use the following format:\\n\\nQuestion: the input question you must answer\\nThought: you should always think about what to do\\nAction:\\n```\\n$JSON_BLOB\\n```\\nObservation: the result of the action\\n... (this Thought/Action/Observation can repeat N times)\\nThought: I now know the final answer\\nFinal Answer: the final answer to the original input question\\n\\nBegin! Reminder to always use the exact characters `Final Answer` when responding.\"\n", "  additional_kwargs: {}\n", "\n", "======= end of message ======= \n", "\n", "\n", "\n", " ======= start of message ======= \n", "\n", "\n", "type: human\n", "data:\n", "  content: 'What is <PERSON><PERSON> the Rock?\n", "\n", "\n", "    '\n", "  additional_kwargs: {}\n", "  example: false\n", "\n", "======= end of message ======= \n", "\n", "\n", "\u001b[32;1m\u001b[1;3mAction:\n", "```\n", "{\n", "  \"action\": \"Wikipedia\",\n", "  \"action_input\": \"What is <PERSON><PERSON> the Rock?\"\n", "}\n", "```\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mPage: Bocchi the Rock!\n", "Summary: <PERSON><PERSON> the Rock! (ぼっち・ざ・ろっく!, Bo<PERSON>i Za Rokku!) is a Japanese four-panel manga series written and illustrated by <PERSON><PERSON>. It has been serialized in Houb<PERSON><PERSON>'s seinen manga magazine Manga Time Kirara Max since December 2017. Its chapters have been collected in five tankōbon volumes as of November 2022.\n", "An anime television series adaptation produced by CloverWorks aired from October to December 2022. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\n", "\n", "Page: <PERSON><PERSON> no <PERSON><PERSON>\n", "Summary: <PERSON><PERSON><PERSON> (Japanese: ひとりぼっちの○○生活, lit. \"Bocchi Hitori's ____ Life\" or \"The ____ Life of Being Alone\") is a Japanese yonkoma manga series written and illustrated by <PERSON><PERSON><PERSON>. It was serialized in ASCII Media Works' Comic Dengeki Daioh \"g\" magazine from September 2013 to April 2021. Eight tankōbon volumes have been released. An anime television series adaptation by C2C aired from April to June 2019.\n", "\n", "Page: Kessoku Band (album)\n", "Summary: <PERSON><PERSON><PERSON> Band (Japanese: 結束バンド, Hepburn: <PERSON><PERSON><PERSON>) is the debut studio album by Kessoku Band, a fictional musical group from the anime television series Bocchi the Rock!, released digitally on December 25, 2022, and physically on CD on December 28 by Aniplex. Featuring vocals from voice actresses <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, the album consists of 14 tracks previously heard in the anime, including a cover of Asian Kung-Fu Generation's \"Rockn' Roll, Morning Light Falls on You\", as well as newly recorded songs; nine singles preceded the album's physical release. Commercially, <PERSON><PERSON><PERSON> Band peaked at number one on the Billboard Japan Hot Albums Chart and Oricon Albums Chart, and was certified gold by the Recording Industry Association of Japan.\n", "\n", "\u001b[0m\n", "Thought:\n", " ======= start of message ======= \n", "\n", "\n", "type: system\n", "data:\n", "  content: \"Answer the following questions as best you can. You have access to the following tools:\\n\\nWikipedia: A wrapper around Wikipedia. Useful for when you need to answer general questions about people, places, companies, facts, historical events, or other subjects. Input should be a search query.\\n\\nThe way you use the tools is by specifying a json blob.\\nSpecifically, this json should have a `action` key (with the name of the tool to use) and a `action_input` key (with the input to the tool going here).\\n\\nThe only values that should be in the \\\"action\\\" field are: Wikipedia\\n\\nThe $JSON_BLOB should only contain a SINGLE action, do NOT return a list of multiple actions. Here is an example of a valid $JSON_BLOB:\\n\\n```\\n{\\n  \\\"action\\\": $TOOL_NAME,\\n  \\\"action_input\\\": $INPUT\\n}\\n```\\n\\nALWAYS use the following format:\\n\\nQuestion: the input question you must answer\\nThought: you should always think about what to do\\nAction:\\n```\\n$JSON_BLOB\\n```\\nObservation: the result of the action\\n... (this Thought/Action/Observation can repeat N times)\\nThought: I now know the final answer\\nFinal Answer: the final answer to the original input question\\n\\nBegin! Reminder to always use the exact characters `Final Answer` when responding.\"\n", "  additional_kwargs: {}\n", "\n", "======= end of message ======= \n", "\n", "\n", "\n", " ======= start of message ======= \n", "\n", "\n", "type: human\n", "data:\n", "  content: \"What is <PERSON><PERSON> the Rock?\\n\\nThis was your previous work (but I haven't seen any of it! I only see what you return as final answer):\\nAction:\\n```\\n{\\n  \\\"action\\\": \\\"Wikipedia\\\",\\n  \\\"action_input\\\": \\\"What is <PERSON><PERSON> the Rock?\\\"\\n}\\n```\\nObservation: Page: Bocchi the Rock!\\nSummary: Bocchi the Rock! (ぼっち・ざ・ろっく!, Botchi Za Rokku!) is a Japanese four-panel manga series written and illustrated by <PERSON><PERSON>. It has been serialized in Houbunsha's seinen manga magazine Manga Time Kirara Max since December 2017. Its chapters have been collected in five tankōbon volumes as of November 2022.\\nAn anime television series adaptation produced by CloverWorks aired from October to December 2022. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\\n\\nPage: Hitori <PERSON>cchi no <PERSON>umaru <PERSON>\\nSummary: <PERSON><PERSON>cchi no Marumaru <PERSON>tsu (Japanese: ひとりぼっちの○○生活, lit. \\\"Bocchi Hitori's ____ Life\\\" or \\\"The ____ Life of Being Alone\\\") is a Japanese yonkoma manga series written and illustrated by <PERSON><PERSON><PERSON>. It was serialized in ASCII Media Works' Comic Dengeki Daioh \\\"g\\\" magazine from September 2013 to April 2021. Eight tankōbon volumes have been released. An anime television series adaptation by C2C aired from April to June 2019.\\n\\nPage: Kessoku Band (album)\\nSummary: Kessoku Band (Japanese: 結束バンド, Hepburn: Kessoku Bando) is the debut studio album by Kessoku Band, a fictional musical group from the anime television series Bocchi the Rock!, released digitally on December 25, 2022, and physically on CD on December 28 by Aniplex. Featuring vocals from voice actresses Yoshino Aoyama, Sayumi Suzushiro, Saku Mizuno, and Ikumi Hasegawa, the album consists of 14 tracks previously heard in the anime, including a cover of Asian Kung-Fu Generation's \\\"Rockn' Roll, Morning Light Falls on You\\\", as well as newly recorded songs; nine singles preceded the album's physical release. Commercially, Kessoku Band peaked at number one on the Billboard Japan Hot Albums Chart and Oricon Albums Chart, and was certified gold by the Recording Industry Association of Japan.\\n\\n\\nThought:\"\n", "  additional_kwargs: {}\n", "  example: false\n", "\n", "======= end of message ======= \n", "\n", "\n", "\u001b[32;1m\u001b[1;3mThis finally works.\n", "Final Answer: Bocchi the Rock! is a four-panel manga series and anime television series. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': 'What is <PERSON><PERSON> the Rock?',\n", " 'output': \"Bocchi the Rock! is a four-panel manga series and anime television series. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\"}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["agent(\"What is <PERSON><PERSON> the Rock?\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}