{"cells": [{"cell_type": "markdown", "id": "SzvBjdID1V3m", "metadata": {"id": "SzvBjdID1V3m"}, "source": ["# Multi-modal RAG with Google Cloud"]}, {"cell_type": "markdown", "id": "4tfidrmE1Zlo", "metadata": {"id": "4tfidrmE1Zlo"}, "source": ["This tutorial demonstrates how to implement the Option 2 described [here](https://github.com/langchain-ai/langchain/blob/master/cookbook/Multi_modal_RAG.ipynb) with Generative API on Google Cloud."]}, {"cell_type": "markdown", "id": "84fcd59f-2eaf-4a76-ad1a-96d6db70bf42", "metadata": {}, "source": ["## Setup\n", "\n", "Install the required dependencies, and create an API key for your Google service."]}, {"cell_type": "code", "execution_count": null, "id": "6b1e10dd-25de-4c0a-9577-f36e72518f89", "metadata": {}, "outputs": [], "source": ["%pip install -U --quiet langchain langchain-chroma langchain-community openai langchain-experimental\n", "%pip install --quiet \"unstructured[all-docs]\" pypdf pillow pydantic lxml pillow matplotlib chromadb tiktoken"]}, {"cell_type": "markdown", "id": "pSInKtCZ32mt", "metadata": {"id": "pSInKtCZ32mt"}, "source": ["## Data loading"]}, {"cell_type": "markdown", "id": "Iv2R8-lJ37dG", "metadata": {"id": "Iv2R8-lJ37dG"}, "source": ["We use a zip file with a sub-set of the extracted images and pdf from [this](https://cloudedjudgement.substack.com/p/clouded-judgement-111023) blog post. If you want to follow the full flow, please, use the original [example](https://github.com/langchain-ai/langchain/blob/master/cookbook/Multi_modal_RAG.ipynb)."]}, {"cell_type": "code", "execution_count": 1, "id": "d999f3fe-c165-4772-b63e-ffe4dd5b03cf", "metadata": {}, "outputs": [], "source": ["# First download\n", "import logging\n", "import zipfile\n", "\n", "import requests\n", "\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "data_url = \"https://storage.googleapis.com/benchmarks-artifacts/langchain-docs-benchmarking/cj.zip\"\n", "result = requests.get(data_url)\n", "filename = \"cj.zip\"\n", "with open(filename, \"wb\") as file:\n", "    file.write(result.content)\n", "\n", "with zipfile.ZipFile(filename, \"r\") as zip_ref:\n", "    zip_ref.extractall()"]}, {"cell_type": "code", "execution_count": 2, "id": "eGUfuevMUA6R", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import PyPDFLoader\n", "\n", "loader = PyPDFLoader(\"./cj/cj.pdf\")\n", "docs = loader.load()\n", "tables = []\n", "texts = [d.page_content for d in docs]"]}, {"cell_type": "code", "execution_count": 3, "id": "Fst17fNHWYcq", "metadata": {}, "outputs": [{"data": {"text/plain": ["21"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["len(texts)"]}, {"cell_type": "markdown", "id": "vjfcg_Vn3_1C", "metadata": {"id": "vjfcg_Vn3_1C"}, "source": ["## Multi-vector retriever"]}, {"cell_type": "markdown", "id": "1ynRqJn04BFG", "metadata": {"id": "1ynRqJn04BFG"}, "source": ["Let's generate text and image summaries and save them to a ChromaDB vectorstore."]}, {"cell_type": "code", "execution_count": null, "id": "kWDWfSDBMPl8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: NumExpr detected 12 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n"]}], "source": ["from langchain.prompts import PromptTemplate\n", "from langchain_community.chat_models import ChatVertexAI\n", "from langchain_community.llms import VertexAI\n", "from langchain_core.messages import AIMessage\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnableLambda\n", "\n", "\n", "# Generate summaries of text elements\n", "def generate_text_summaries(texts, tables, summarize_texts=False):\n", "    \"\"\"\n", "    Summarize text elements\n", "    texts: List of str\n", "    tables: List of str\n", "    summarize_texts: <PERSON><PERSON> to summarize texts\n", "    \"\"\"\n", "\n", "    # Prompt\n", "    prompt_text = \"\"\"You are an assistant tasked with summarizing tables and text for retrieval. \\\n", "    These summaries will be embedded and used to retrieve the raw text or table elements. \\\n", "    Give a concise summary of the table or text that is well optimized for retrieval. Table or text: {element} \"\"\"\n", "    prompt = PromptTemplate.from_template(prompt_text)\n", "    empty_response = RunnableLambda(\n", "        lambda x: AIMessage(content=\"Error processing document\")\n", "    )\n", "    # Text summary chain\n", "    model = VertexAI(\n", "        temperature=0, model_name=\"gemini-2.5-flash\", max_tokens=1024\n", "    ).with_fallbacks([empty_response])\n", "    summarize_chain = {\"element\": lambda x: x} | prompt | model | StrOutputParser()\n", "\n", "    # Initialize empty summaries\n", "    text_summaries = []\n", "    table_summaries = []\n", "\n", "    # Apply to text if texts are provided and summarization is requested\n", "    if texts and summarize_texts:\n", "        text_summaries = summarize_chain.batch(texts, {\"max_concurrency\": 1})\n", "    elif texts:\n", "        text_summaries = texts\n", "\n", "    # Apply to tables if tables are provided\n", "    if tables:\n", "        table_summaries = summarize_chain.batch(tables, {\"max_concurrency\": 1})\n", "\n", "    return text_summaries, table_summaries\n", "\n", "\n", "# Get text, table summaries\n", "text_summaries, table_summaries = generate_text_summaries(\n", "    texts, tables, summarize_texts=True\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "F0NnyUl48yYb", "metadata": {}, "outputs": [{"data": {"text/plain": ["21"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["len(text_summaries)"]}, {"cell_type": "code", "execution_count": null, "id": "PeK9bzXv3olF", "metadata": {}, "outputs": [], "source": ["import base64\n", "import os\n", "\n", "from langchain_core.messages import HumanMessage\n", "\n", "\n", "def encode_image(image_path):\n", "    \"\"\"Getting the base64 string\"\"\"\n", "    with open(image_path, \"rb\") as image_file:\n", "        return base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "\n", "\n", "def image_summarize(img_base64, prompt):\n", "    \"\"\"Make image summary\"\"\"\n", "    model = ChatVertexAI(model=\"gemini-2.5-flash\", max_tokens=1024)\n", "\n", "    msg = model.invoke(\n", "        [\n", "            HumanMessage(\n", "                content=[\n", "                    {\"type\": \"text\", \"text\": prompt},\n", "                    {\n", "                        \"type\": \"image_url\",\n", "                        \"image_url\": {\"url\": f\"data:image/jpeg;base64,{img_base64}\"},\n", "                    },\n", "                ]\n", "            )\n", "        ]\n", "    )\n", "    return msg.content\n", "\n", "\n", "def generate_img_summaries(path):\n", "    \"\"\"\n", "    Generate summaries and base64 encoded strings for images\n", "    path: Path to list of .jpg files extracted by Unstructured\n", "    \"\"\"\n", "\n", "    # Store base64 encoded images\n", "    img_base64_list = []\n", "\n", "    # Store image summaries\n", "    image_summaries = []\n", "\n", "    # Prompt\n", "    prompt = \"\"\"You are an assistant tasked with summarizing images for retrieval. \\\n", "    These summaries will be embedded and used to retrieve the raw image. \\\n", "    Give a concise summary of the image that is well optimized for retrieval.\"\"\"\n", "\n", "    # Apply to images\n", "    for img_file in sorted(os.listdir(path)):\n", "        if img_file.endswith(\".jpg\"):\n", "            img_path = os.path.join(path, img_file)\n", "            base64_image = encode_image(img_path)\n", "            img_base64_list.append(base64_image)\n", "            image_summaries.append(image_summarize(base64_image, prompt))\n", "\n", "    return img_base64_list, image_summaries\n", "\n", "\n", "# Image summaries\n", "img_base64_list, image_summaries = generate_img_summaries(\"./cj\")"]}, {"cell_type": "code", "execution_count": 7, "id": "6WDYpDFzjocl", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(image_summaries)"]}, {"cell_type": "code", "execution_count": 8, "id": "cWyWfZ-XB6cS", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:chromadb.telemetry.product.posthog:Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.\n"]}], "source": ["import uuid\n", "\n", "from langchain.retrievers.multi_vector import MultiVectorRetriever\n", "from langchain.storage import InMemoryStore\n", "from langchain_chroma import Chroma\n", "from langchain_community.embeddings import VertexAIEmbeddings\n", "from langchain_core.documents import Document\n", "\n", "\n", "def create_multi_vector_retriever(\n", "    vectorstore, text_summaries, texts, table_summaries, tables, image_summaries, images\n", "):\n", "    \"\"\"\n", "    Create retriever that indexes summaries, but returns raw images or texts\n", "    \"\"\"\n", "\n", "    # Initialize the storage layer\n", "    store = InMemoryStore()\n", "    id_key = \"doc_id\"\n", "\n", "    # Create the multi-vector retriever\n", "    retriever = MultiVectorRetriever(\n", "        vectorstore=vectorstore,\n", "        docstore=store,\n", "        id_key=id_key,\n", "    )\n", "\n", "    # Helper function to add documents to the vectorstore and docstore\n", "    def add_documents(retriever, doc_summaries, doc_contents):\n", "        doc_ids = [str(uuid.uuid4()) for _ in doc_contents]\n", "        summary_docs = [\n", "            Document(page_content=s, metadata={id_key: doc_ids[i]})\n", "            for i, s in enumerate(doc_summaries)\n", "        ]\n", "        retriever.vectorstore.add_documents(summary_docs)\n", "        retriever.docstore.mset(list(zip(doc_ids, doc_contents)))\n", "\n", "    # Add texts, tables, and images\n", "    # Check that text_summaries is not empty before adding\n", "    if text_summaries:\n", "        add_documents(retriever, text_summaries, texts)\n", "    # Check that table_summaries is not empty before adding\n", "    if table_summaries:\n", "        add_documents(retriever, table_summaries, tables)\n", "    # Check that image_summaries is not empty before adding\n", "    if image_summaries:\n", "        add_documents(retriever, image_summaries, images)\n", "\n", "    return retriever\n", "\n", "\n", "# The vectorstore to use to index the summaries\n", "vectorstore = Chroma(\n", "    collection_name=\"mm_rag_cj_blog\",\n", "    embedding_function=VertexAIEmbeddings(model_name=\"text-embedding-005\"),\n", ")\n", "\n", "# Create retriever\n", "retriever_multi_vector_img = create_multi_vector_retriever(\n", "    vectorstore,\n", "    text_summaries,\n", "    texts,\n", "    table_summaries,\n", "    tables,\n", "    image_summaries,\n", "    img_base64_list,\n", ")"]}, {"cell_type": "markdown", "id": "NGDkkMFfCg4j", "metadata": {"id": "NGDkkMFfCg4j"}, "source": ["## Building a RAG"]}, {"cell_type": "markdown", "id": "8TzOcHVsCmBc", "metadata": {"id": "8TzOcHVsCmBc"}, "source": ["Let's build a retriever:"]}, {"cell_type": "code", "execution_count": null, "id": "GlwCErBaCKQW", "metadata": {}, "outputs": [], "source": ["import io\n", "import re\n", "\n", "from IPython.display import HTML, display\n", "from langchain_core.runnables import RunnableLambda, RunnablePassthrough\n", "from PIL import Image\n", "\n", "\n", "def plt_img_base64(img_base64):\n", "    \"\"\"Display base64 encoded string as image\"\"\"\n", "    # Create an HTML img tag with the base64 string as the source\n", "    image_html = f'<img src=\"data:image/jpeg;base64,{img_base64}\" />'\n", "    # Display the image by rendering the HTML\n", "    display(HTML(image_html))\n", "\n", "\n", "def looks_like_base64(sb):\n", "    \"\"\"Check if the string looks like base64\"\"\"\n", "    return re.match(\"^[A-Za-z0-9+/]+[=]{0,2}$\", sb) is not None\n", "\n", "\n", "def is_image_data(b64data):\n", "    \"\"\"\n", "    Check if the base64 data is an image by looking at the start of the data\n", "    \"\"\"\n", "    image_signatures = {\n", "        b\"\\xff\\xd8\\xff\": \"jpg\",\n", "        b\"\\x89\\x50\\x4e\\x47\\x0d\\x0a\\x1a\\x0a\": \"png\",\n", "        b\"\\x47\\x49\\x46\\x38\": \"gif\",\n", "        b\"\\x52\\x49\\x46\\x46\": \"webp\",\n", "    }\n", "    try:\n", "        header = base64.b64decode(b64data)[:8]  # Decode and get the first 8 bytes\n", "        for sig, format in image_signatures.items():\n", "            if header.startswith(sig):\n", "                return True\n", "        return False\n", "    except Exception:\n", "        return False\n", "\n", "\n", "def resize_base64_image(base64_string, size=(128, 128)):\n", "    \"\"\"\n", "    Resize an image encoded as a Base64 string\n", "    \"\"\"\n", "    # Decode the Base64 string\n", "    img_data = base64.b64decode(base64_string)\n", "    img = Image.open(io.BytesIO(img_data))\n", "\n", "    # Resize the image\n", "    resized_img = img.resize(size, Image.LANCZOS)\n", "\n", "    # Save the resized image to a bytes buffer\n", "    buffered = io.BytesIO()\n", "    resized_img.save(buffered, format=img.format)\n", "\n", "    # Encode the resized image to Base64\n", "    return base64.b64encode(buffered.getvalue()).decode(\"utf-8\")\n", "\n", "\n", "def split_image_text_types(docs):\n", "    \"\"\"\n", "    Split base64-encoded images and texts\n", "    \"\"\"\n", "    b64_images = []\n", "    texts = []\n", "    for doc in docs:\n", "        # Check if the document is of type Document and extract page_content if so\n", "        if isinstance(doc, Document):\n", "            doc = doc.page_content\n", "        if looks_like_base64(doc) and is_image_data(doc):\n", "            doc = resize_base64_image(doc, size=(1300, 600))\n", "            b64_images.append(doc)\n", "        else:\n", "            texts.append(doc)\n", "    if len(b64_images) > 0:\n", "        return {\"images\": b64_images[:1], \"texts\": []}\n", "    return {\"images\": b64_images, \"texts\": texts}\n", "\n", "\n", "def img_prompt_func(data_dict):\n", "    \"\"\"\n", "    Join the context into a single string\n", "    \"\"\"\n", "    formatted_texts = \"\\n\".join(data_dict[\"context\"][\"texts\"])\n", "    messages = []\n", "\n", "    # Adding the text for analysis\n", "    text_message = {\n", "        \"type\": \"text\",\n", "        \"text\": (\n", "            \"You are financial analyst tasking with providing investment advice.\\n\"\n", "            \"You will be given a mixed of text, tables, and image(s) usually of charts or graphs.\\n\"\n", "            \"Use this information to provide investment advice related to the user question. \\n\"\n", "            f\"User-provided question: {data_dict['question']}\\n\\n\"\n", "            \"Text and / or tables:\\n\"\n", "            f\"{formatted_texts}\"\n", "        ),\n", "    }\n", "    messages.append(text_message)\n", "    # Adding image(s) to the messages if present\n", "    if data_dict[\"context\"][\"images\"]:\n", "        for image in data_dict[\"context\"][\"images\"]:\n", "            image_message = {\n", "                \"type\": \"image_url\",\n", "                \"image_url\": {\"url\": f\"data:image/jpeg;base64,{image}\"},\n", "            }\n", "            messages.append(image_message)\n", "    return [HumanMessage(content=messages)]\n", "\n", "\n", "def multi_modal_rag_chain(retriever):\n", "    \"\"\"\n", "    Multi-modal RAG chain\n", "    \"\"\"\n", "\n", "    # Multi-modal LLM\n", "    model = ChatVertexAI(temperature=0, model_name=\"gemini-2.5-flash\", max_tokens=1024)\n", "\n", "    # RAG pipeline\n", "    chain = (\n", "        {\n", "            \"context\": retriever | RunnableLambda(split_image_text_types),\n", "            \"question\": <PERSON><PERSON>blePassthrough(),\n", "        }\n", "        | RunnableLambda(img_prompt_func)\n", "        | model\n", "        | StrOutputParser()\n", "    )\n", "\n", "    return chain\n", "\n", "\n", "# Create RAG chain\n", "chain_multimodal_rag = multi_modal_rag_chain(retriever_multi_vector_img)"]}, {"cell_type": "markdown", "id": "BS4hNKqCCp8u", "metadata": {"id": "BS4hNKqCCp8u"}, "source": ["Let's check that we get images as documents:"]}, {"cell_type": "code", "execution_count": 10, "id": "Q7GrwFC_FGwr", "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["query = \"What are the EV / NTM and NTM rev growth for MongoDB, Cloudflare, and Datadog?\"\n", "docs = retriever_multi_vector_img.invoke(query, limit=1)\n", "\n", "# We get 2 docs\n", "len(docs)"]}, {"cell_type": "code", "execution_count": 11, "id": "unnxB5M_FLCD", "metadata": {}, "outputs": [{"data": {"text/html": ["<img src=\"data:image/jpeg;base64,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\" />"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt_img_base64(docs[0])"]}, {"cell_type": "markdown", "id": "YUkGZXqsCtF6", "metadata": {"id": "YUkGZXqsCtF6"}, "source": ["And let's run our RAG on the same query:"]}, {"cell_type": "code", "execution_count": 12, "id": "LsPTehdK-T-_", "metadata": {}, "outputs": [{"data": {"text/plain": ["' | Company | EV / NTM Rev | NTM Rev Growth |\\n|---|---|---|\\n| MongoDB | 14.6x | 17% |\\n| Cloudflare | 13.4x | 28% |\\n| Datadog | 13.1x | 19% |'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["chain_multimodal_rag.invoke(query)"]}, {"cell_type": "markdown", "id": "XpLQB6dEfQX-", "metadata": {"id": "XpLQB6dEfQX-"}, "source": ["As we can see, the model was able to figure out the the right values that are relevant to answer the question."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 5}