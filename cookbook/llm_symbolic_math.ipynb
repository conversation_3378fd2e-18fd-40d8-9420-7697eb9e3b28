{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LLM Symbolic Math \n", "This notebook showcases using LLMs and Python to Solve Algebraic Equations. Under the hood is makes use of [SymPy](https://www.sympy.org/en/index.html)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain_experimental.llm_symbolic_math.base import LLMSymbolicMathChain\n", "from langchain_openai import OpenAI\n", "\n", "llm = OpenAI(temperature=0)\n", "llm_symbolic_math = LLMSymbolicMathChain.from_llm(llm)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Integrals and derivates"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Answer: exp(x)*sin(x) + exp(x)*cos(x)'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_symbolic_math.invoke(\"What is the derivative of sin(x)*exp(x) with respect to x?\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Answer: exp(x)*sin(x)'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_symbolic_math.invoke(\n", "    \"What is the integral of exp(x)*sin(x) + exp(x)*cos(x) with respect to x?\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Solve linear and differential equations"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Answer: Eq(y(t), C2*exp(-t) + (C1 + t/2)*exp(t))'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_symbolic_math.invoke('Solve the differential equation y\" - y = e^t')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Answer: {0, -sqrt(3)*I/3, sqrt(3)*I/3}'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_symbolic_math.invoke(\"What are the solutions to this equation y^3 + 1/3y?\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Answer: (3 - sqrt(7), -sqrt(7) - 2, 1 - sqrt(7)), (sqrt(7) + 3, -2 + sqrt(7), 1 + sqrt(7))'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_symbolic_math.invoke(\"x = y + 5, y = z - 3, z = x * y. Solve for x, y, z\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}