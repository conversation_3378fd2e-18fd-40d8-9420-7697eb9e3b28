{"cells": [{"cell_type": "markdown", "id": "fa6802ac", "metadata": {}, "source": ["# Shared memory across agents and tools\n", "\n", "This notebook goes over adding memory to **both** an Agent and its tools. Before going through this notebook, please walk through the following notebooks, as this will build on top of both of them:\n", "\n", "- [Adding memory to an LLM Chain](/docs/modules/memory/integrations/adding_memory)\n", "- [Custom Agents](/docs/modules/agents/how_to/custom_agent)\n", "\n", "We are going to create a custom Agent. The agent has access to a conversation memory, search tool, and a summarization tool. The summarization tool also needs access to the conversation memory."]}, {"cell_type": "code", "execution_count": 1, "id": "8db95912", "metadata": {}, "outputs": [], "source": ["from langchain import hub\n", "from langchain.agents import AgentExecutor, Tool, ZeroShotAgent, create_react_agent\n", "from langchain.chains import LLMChain\n", "from langchain.memory import ConversationBufferMemory, ReadOnlySharedMemory\n", "from langchain.prompts import PromptTemplate\n", "from langchain_community.utilities import GoogleSearchAPIWrapper\n", "from langchain_openai import OpenAI"]}, {"cell_type": "code", "execution_count": 2, "id": "06b7187b", "metadata": {}, "outputs": [], "source": ["template = \"\"\"This is a conversation between a human and a bot:\n", "\n", "{chat_history}\n", "\n", "Write a summary of the conversation for {input}:\n", "\"\"\"\n", "\n", "prompt = PromptTemplate(input_variables=[\"input\", \"chat_history\"], template=template)\n", "memory = ConversationBufferMemory(memory_key=\"chat_history\")\n", "readonlymemory = ReadOnlySharedMemory(memory=memory)\n", "summary_chain = LLMChain(\n", "    llm=OpenAI(),\n", "    prompt=prompt,\n", "    verbose=True,\n", "    memory=readonlymemory,  # use the read-only memory to prevent the tool from modifying the memory\n", ")"]}, {"cell_type": "code", "execution_count": 3, "id": "97ad8467", "metadata": {}, "outputs": [], "source": ["search = GoogleSearchAPIWrapper()\n", "tools = [\n", "    Tool(\n", "        name=\"Search\",\n", "        func=search.run,\n", "        description=\"useful for when you need to answer questions about current events\",\n", "    ),\n", "    Tool(\n", "        name=\"Summary\",\n", "        func=summary_chain.run,\n", "        description=\"useful for when you summarize a conversation. The input to this tool should be a string, representing who will read this summary.\",\n", "    ),\n", "]"]}, {"cell_type": "code", "execution_count": 4, "id": "e3439cd6", "metadata": {}, "outputs": [], "source": ["prompt = hub.pull(\"hwchase17/react\")"]}, {"cell_type": "markdown", "id": "0021675b", "metadata": {}, "source": ["We can now construct the `<PERSON><PERSON><PERSON><PERSON>`, with the Memory object, and then create the agent."]}, {"cell_type": "code", "execution_count": 5, "id": "c56a0e73", "metadata": {}, "outputs": [], "source": ["model = OpenAI()\n", "agent = create_react_agent(model, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, memory=memory)"]}, {"cell_type": "code", "execution_count": 36, "id": "ca4bc1fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: I should research ChatGPT to answer this question.\n", "Action: Search\n", "Action Input: \"ChatGPT\"\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mNov 30, 2022 ... We've trained a model called ChatGPT which interacts in a conversational way. The dialogue format makes it possible for ChatGPT to answer ... ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large ... ChatGPT. We've trained a model called ChatGPT which interacts in a conversational way. The dialogue format makes it possible for ChatGPT to answer ... Feb 2, 2023 ... ChatGPT, the popular chatbot from OpenAI, is estimated to have reached 100 million monthly active users in January, just two months after ... 2 days ago ... ChatGPT recently launched a new version of its own plagiarism detection tool, with hopes that it will squelch some of the criticism around how ... An API for accessing new AI models developed by OpenAI. Feb 19, 2023 ... ChatGPT is an AI chatbot system that OpenAI released in November to show off and test what a very large, powerful AI system can accomplish. You ... ChatGPT is fine-tuned from GPT-3.5, a language model trained to produce text. ChatGPT was optimized for dialogue by using Reinforcement Learning with Human ... 3 days ago ... Visual ChatGPT connects ChatGPT and a series of Visual Foundation Models to enable sending and receiving images during chatting. Dec 1, 2022 ... ChatGPT is a natural language processing tool driven by AI technology that allows you to have human-like conversations and much more with a ...\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the final answer.\n", "Final Answer: ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large language models and is optimized for dialogue by using Reinforcement Learning with Human-in-the-Loop. It is also capable of sending and receiving images during chatting.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["\"ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large language models and is optimized for dialogue by using Reinforcement Learning with Human-in-the-Loop. It is also capable of sending and receiving images during chatting.\""]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[36], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43magent_executor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvoke\u001b[49m\u001b[43m(\u001b[49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43minput\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mWhat is ChatGPT?\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/code/langchain/libs/langchain/langchain/chains/base.py:163\u001b[0m, in \u001b[0;36mChain.invoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    161\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    162\u001b[0m     run_manager\u001b[38;5;241m.\u001b[39mon_chain_error(e)\n\u001b[0;32m--> 163\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[1;32m    164\u001b[0m run_manager\u001b[38;5;241m.\u001b[39mon_chain_end(outputs)\n\u001b[1;32m    166\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m include_run_info:\n", "File \u001b[0;32m~/code/langchain/libs/langchain/langchain/chains/base.py:153\u001b[0m, in \u001b[0;36mChain.invoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    150\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    151\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_inputs(inputs)\n\u001b[1;32m    152\u001b[0m     outputs \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m--> 153\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43minputs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_manager\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    154\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m new_arg_supported\n\u001b[1;32m    155\u001b[0m         \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_call(inputs)\n\u001b[1;32m    156\u001b[0m     )\n\u001b[1;32m    158\u001b[0m     final_outputs: Dict[\u001b[38;5;28mstr\u001b[39m, Any] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprep_outputs(\n\u001b[1;32m    159\u001b[0m         inputs, outputs, return_only_outputs\n\u001b[1;32m    160\u001b[0m     )\n\u001b[1;32m    161\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/code/langchain/libs/langchain/langchain/agents/agent.py:1432\u001b[0m, in \u001b[0;36mAgentExecutor._call\u001b[0;34m(self, inputs, run_manager)\u001b[0m\n\u001b[1;32m   1430\u001b[0m \u001b[38;5;66;03m# We now enter the agent loop (until it returns something).\u001b[39;00m\n\u001b[1;32m   1431\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_should_continue(iterations, time_elapsed):\n\u001b[0;32m-> 1432\u001b[0m     next_step_output \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_take_next_step\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1433\u001b[0m \u001b[43m        \u001b[49m\u001b[43mname_to_tool_map\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1434\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcolor_mapping\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1435\u001b[0m \u001b[43m        \u001b[49m\u001b[43minputs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1436\u001b[0m \u001b[43m        \u001b[49m\u001b[43mintermediate_steps\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1437\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_manager\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1438\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1439\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(next_step_output, AgentFinish):\n\u001b[1;32m   1440\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_return(\n\u001b[1;32m   1441\u001b[0m             next_step_output, intermediate_steps, run_manager\u001b[38;5;241m=\u001b[39mrun_manager\n\u001b[1;32m   1442\u001b[0m         )\n", "File \u001b[0;32m~/code/langchain/libs/langchain/langchain/agents/agent.py:1138\u001b[0m, in \u001b[0;36mAgentExecutor._take_next_step\u001b[0;34m(self, name_to_tool_map, color_mapping, inputs, intermediate_steps, run_manager)\u001b[0m\n\u001b[1;32m   1129\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_take_next_step\u001b[39m(\n\u001b[1;32m   1130\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1131\u001b[0m     name_to_tool_map: Dict[\u001b[38;5;28mstr\u001b[39m, BaseTool],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1135\u001b[0m     run_manager: Optional[CallbackManagerForChainRun] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1136\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Union[AgentFinish, List[Tuple[AgentAction, \u001b[38;5;28mstr\u001b[39m]]]:\n\u001b[1;32m   1137\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_consume_next_step(\n\u001b[0;32m-> 1138\u001b[0m         [\n\u001b[1;32m   1139\u001b[0m             a\n\u001b[1;32m   1140\u001b[0m             \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_iter_next_step(\n\u001b[1;32m   1141\u001b[0m                 name_to_tool_map,\n\u001b[1;32m   1142\u001b[0m                 color_mapping,\n\u001b[1;32m   1143\u001b[0m                 inputs,\n\u001b[1;32m   1144\u001b[0m                 intermediate_steps,\n\u001b[1;32m   1145\u001b[0m                 run_manager,\n\u001b[1;32m   1146\u001b[0m             )\n\u001b[1;32m   1147\u001b[0m         ]\n\u001b[1;32m   1148\u001b[0m     )\n", "File \u001b[0;32m~/code/langchain/libs/langchain/langchain/agents/agent.py:1138\u001b[0m, in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m   1129\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_take_next_step\u001b[39m(\n\u001b[1;32m   1130\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1131\u001b[0m     name_to_tool_map: Dict[\u001b[38;5;28mstr\u001b[39m, BaseTool],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1135\u001b[0m     run_manager: Optional[CallbackManagerForChainRun] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1136\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Union[AgentFinish, List[Tuple[AgentAction, \u001b[38;5;28mstr\u001b[39m]]]:\n\u001b[1;32m   1137\u001b[0m     \u001b[38;5;28;01mret<PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_consume_next_step(\n\u001b[0;32m-> 1138\u001b[0m         [\n\u001b[1;32m   1139\u001b[0m             a\n\u001b[1;32m   1140\u001b[0m             \u001b[38;5;28;01mfor\u001b[39;00m a \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_iter_next_step(\n\u001b[1;32m   1141\u001b[0m                 name_to_tool_map,\n\u001b[1;32m   1142\u001b[0m                 color_mapping,\n\u001b[1;32m   1143\u001b[0m                 inputs,\n\u001b[1;32m   1144\u001b[0m                 intermediate_steps,\n\u001b[1;32m   1145\u001b[0m                 run_manager,\n\u001b[1;32m   1146\u001b[0m             )\n\u001b[1;32m   1147\u001b[0m         ]\n\u001b[1;32m   1148\u001b[0m     )\n", "File \u001b[0;32m~/code/langchain/libs/langchain/langchain/agents/agent.py:1223\u001b[0m, in \u001b[0;36mAgentExecutor._iter_next_step\u001b[0;34m(self, name_to_tool_map, color_mapping, inputs, intermediate_steps, run_manager)\u001b[0m\n\u001b[1;32m   1221\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m agent_action\n\u001b[1;32m   1222\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m agent_action \u001b[38;5;129;01min\u001b[39;00m actions:\n\u001b[0;32m-> 1223\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_perform_agent_action\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1224\u001b[0m \u001b[43m        \u001b[49m\u001b[43mname_to_tool_map\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcolor_mapping\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43magent_action\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\n\u001b[1;32m   1225\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/code/langchain/libs/langchain/langchain/agents/agent.py:1245\u001b[0m, in \u001b[0;36mAgentExecutor._perform_agent_action\u001b[0;34m(self, name_to_tool_map, color_mapping, agent_action, run_manager)\u001b[0m\n\u001b[1;32m   1243\u001b[0m         tool_run_kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mllm_prefix\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1244\u001b[0m     \u001b[38;5;66;03m# We then call the tool on the tool input to get an observation\u001b[39;00m\n\u001b[0;32m-> 1245\u001b[0m     observation \u001b[38;5;241m=\u001b[39m \u001b[43mtool\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1246\u001b[0m \u001b[43m        \u001b[49m\u001b[43magent_action\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtool_input\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1247\u001b[0m \u001b[43m        \u001b[49m\u001b[43mverbose\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverbose\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1248\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcolor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcolor\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1249\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcallbacks\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_manager\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_child\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1250\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mtool_run_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1251\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1252\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1253\u001b[0m     tool_run_kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39magent\u001b[38;5;241m.\u001b[39mtool_run_logging_kwargs()\n", "File \u001b[0;32m~/code/langchain/libs/core/langchain_core/tools.py:422\u001b[0m, in \u001b[0;36mBaseTool.run\u001b[0;34m(self, tool_input, verbose, start_color, color, callbacks, tags, metadata, run_name, run_id, **kwargs)\u001b[0m\n\u001b[1;32m    420\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (\u001b[38;5;167;01mException\u001b[39;00m, \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    421\u001b[0m     run_manager\u001b[38;5;241m.\u001b[39mon_tool_error(e)\n\u001b[0;32m--> 422\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[1;32m    423\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    424\u001b[0m     run_manager\u001b[38;5;241m.\u001b[39mon_tool_end(observation, color\u001b[38;5;241m=\u001b[39mcolor, name\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mname, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/code/langchain/libs/core/langchain_core/tools.py:381\u001b[0m, in \u001b[0;36mBaseTool.run\u001b[0;34m(self, tool_input, verbose, start_color, color, callbacks, tags, metadata, run_name, run_id, **kwargs)\u001b[0m\n\u001b[1;32m    378\u001b[0m     parsed_input \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_parse_input(tool_input)\n\u001b[1;32m    379\u001b[0m     tool_args, tool_kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_to_args_and_kwargs(parsed_input)\n\u001b[1;32m    380\u001b[0m     observation \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m--> 381\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_run\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mtool_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrun_manager\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrun_manager\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mtool_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    382\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m new_arg_supported\n\u001b[1;32m    383\u001b[0m         \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_run(\u001b[38;5;241m*\u001b[39mtool_args, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mtool_kwargs)\n\u001b[1;32m    384\u001b[0m     )\n\u001b[1;32m    385\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ValidationError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    386\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandle_validation_error:\n", "File \u001b[0;32m~/code/langchain/libs/core/langchain_core/tools.py:588\u001b[0m, in \u001b[0;36mTool._run\u001b[0;34m(self, run_manager, *args, **kwargs)\u001b[0m\n\u001b[1;32m    579\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfunc:\n\u001b[1;32m    580\u001b[0m     new_argument_supported \u001b[38;5;241m=\u001b[39m signature(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfunc)\u001b[38;5;241m.\u001b[39mparameters\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcallbacks\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    581\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m (\n\u001b[1;32m    582\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfunc(\n\u001b[1;32m    583\u001b[0m             \u001b[38;5;241m*\u001b[39margs,\n\u001b[1;32m    584\u001b[0m             callbacks\u001b[38;5;241m=\u001b[39mrun_manager\u001b[38;5;241m.\u001b[39mget_child() \u001b[38;5;28;01mif\u001b[39;00m run_manager \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    585\u001b[0m             \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m    586\u001b[0m         )\n\u001b[1;32m    587\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m new_argument_supported\n\u001b[0;32m--> 588\u001b[0m         \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    589\u001b[0m     )\n\u001b[1;32m    590\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mNotImplementedError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTool does not support sync\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/code/langchain/libs/community/langchain_community/utilities/google_search.py:94\u001b[0m, in \u001b[0;36mGoogleSearchAPIWrapper.run\u001b[0;34m(self, query)\u001b[0m\n\u001b[1;32m     92\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Run query through GoogleSearch and parse result.\"\"\"\u001b[39;00m\n\u001b[1;32m     93\u001b[0m snippets \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m---> 94\u001b[0m results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_google_search_results\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnum\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mk\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     95\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(results) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m     96\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo good Google Search Result was found\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[0;32m~/code/langchain/libs/community/langchain_community/utilities/google_search.py:62\u001b[0m, in \u001b[0;36mGoogleSearchAPIWrapper._google_search_results\u001b[0;34m(self, search_term, **kwargs)\u001b[0m\n\u001b[1;32m     60\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msiterestrict:\n\u001b[1;32m     61\u001b[0m     cse \u001b[38;5;241m=\u001b[39m cse\u001b[38;5;241m.\u001b[39msiterestrict()\n\u001b[0;32m---> 62\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[43mcse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlist\u001b[49m\u001b[43m(\u001b[49m\u001b[43mq\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msearch_term\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgoogle_cse_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     63\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m res\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mitems\u001b[39m\u001b[38;5;124m\"\u001b[39m, [])\n", "File \u001b[0;32m~/code/langchain/.venv/lib/python3.10/site-packages/googleapiclient/_helpers.py:130\u001b[0m, in \u001b[0;36mpositional.<locals>.positional_decorator.<locals>.positional_wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    128\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m positional_parameters_enforcement \u001b[38;5;241m==\u001b[39m POSITIONAL_WARNING:\n\u001b[1;32m    129\u001b[0m         logger\u001b[38;5;241m.\u001b[39mwarning(message)\n\u001b[0;32m--> 130\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapped\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/code/langchain/.venv/lib/python3.10/site-packages/googleapiclient/http.py:923\u001b[0m, in \u001b[0;36mHttpRequest.execute\u001b[0;34m(self, http, num_retries)\u001b[0m\n\u001b[1;32m    920\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mheaders[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent-length\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbody))\n\u001b[1;32m    922\u001b[0m \u001b[38;5;66;03m# Handle retries for server-side errors.\u001b[39;00m\n\u001b[0;32m--> 923\u001b[0m resp, content \u001b[38;5;241m=\u001b[39m \u001b[43m_retry_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    924\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhttp\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    925\u001b[0m \u001b[43m    \u001b[49m\u001b[43mnum_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    926\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mrequest\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    927\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sleep\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    928\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_rand\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    929\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43muri\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    930\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    931\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    932\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    933\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    935\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m callback \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mresponse_callbacks:\n\u001b[1;32m    936\u001b[0m     callback(resp)\n", "File \u001b[0;32m~/code/langchain/.venv/lib/python3.10/site-packages/googleapiclient/http.py:191\u001b[0m, in \u001b[0;36m_retry_request\u001b[0;34m(http, num_retries, req_type, sleep, rand, uri, method, *args, **kwargs)\u001b[0m\n\u001b[1;32m    189\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    190\u001b[0m     exception \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m--> 191\u001b[0m     resp, content \u001b[38;5;241m=\u001b[39m \u001b[43mhttp\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43muri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    192\u001b[0m \u001b[38;5;66;03m# Retry on SSL errors and socket timeout errors.\u001b[39;00m\n\u001b[1;32m    193\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m _ssl_SSLError \u001b[38;5;28;01mas\u001b[39;00m ssl_error:\n", "File \u001b[0;32m~/code/langchain/.venv/lib/python3.10/site-packages/httplib2/__init__.py:1724\u001b[0m, in \u001b[0;36mHttp.request\u001b[0;34m(self, uri, method, body, headers, redirections, connection_type)\u001b[0m\n\u001b[1;32m   1722\u001b[0m             content \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1723\u001b[0m         \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1724\u001b[0m             (response, content) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1725\u001b[0m \u001b[43m                \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mauthority\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43muri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_uri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mredirections\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcachekey\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1726\u001b[0m \u001b[43m            \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1727\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m   1728\u001b[0m     is_timeout \u001b[38;5;241m=\u001b[39m \u001b[38;5;28misinstance\u001b[39m(e, socket\u001b[38;5;241m.\u001b[39mtimeout)\n", "File \u001b[0;32m~/code/langchain/.venv/lib/python3.10/site-packages/httplib2/__init__.py:1444\u001b[0m, in \u001b[0;36mHttp._request\u001b[0;34m(self, conn, host, absolute_uri, request_uri, method, body, headers, redirections, cachekey)\u001b[0m\n\u001b[1;32m   1441\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m auth:\n\u001b[1;32m   1442\u001b[0m     auth\u001b[38;5;241m.\u001b[39mrequest(method, request_uri, headers, body)\n\u001b[0;32m-> 1444\u001b[0m (response, content) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_conn_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequest_uri\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1446\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m auth:\n\u001b[1;32m   1447\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m auth\u001b[38;5;241m.\u001b[39mresponse(response, body):\n", "File \u001b[0;32m~/code/langchain/.venv/lib/python3.10/site-packages/httplib2/__init__.py:1366\u001b[0m, in \u001b[0;36mHttp._conn_request\u001b[0;34m(self, conn, request_uri, method, body, headers)\u001b[0m\n\u001b[1;32m   1364\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1365\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m conn\u001b[38;5;241m.\u001b[39msock \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m-> 1366\u001b[0m         \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1367\u001b[0m     conn\u001b[38;5;241m.\u001b[39mrequest(method, request_uri, body, headers)\n\u001b[1;32m   1368\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m socket\u001b[38;5;241m.\u001b[39mtimeout:\n", "File \u001b[0;32m~/code/langchain/.venv/lib/python3.10/site-packages/httplib2/__init__.py:1156\u001b[0m, in \u001b[0;*****************************.connect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1154\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m has_timeout(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout):\n\u001b[1;32m   1155\u001b[0m     sock\u001b[38;5;241m.\u001b[39msettimeout(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout)\n\u001b[0;32m-> 1156\u001b[0m \u001b[43msock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhost\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mport\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1158\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_context\u001b[38;5;241m.\u001b[39mwrap_socket(sock, server_hostname\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhost)\n\u001b[1;32m   1160\u001b[0m \u001b[38;5;66;03m# Python 3.3 compatibility: emulate the check_hostname behavior\u001b[39;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["agent_executor.invoke({\"input\": \"What is ChatGPT?\"})"]}, {"cell_type": "markdown", "id": "45627664", "metadata": {}, "source": ["To test the memory of this agent, we can ask a followup question that relies on information in the previous exchange to be answered correctly."]}, {"cell_type": "code", "execution_count": 7, "id": "eecc0462", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: I need to find out who developed ChatGPT\n", "Action: Search\n", "Action Input: Who developed ChatGPT\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large ... Feb 15, 2023 ... Who owns Chat GPT? Chat GPT is owned and developed by AI research and deployment company, OpenAI. The organization is headquartered in San ... Feb 8, 2023 ... ChatGPT is an AI chatbot developed by San Francisco-based startup OpenAI. OpenAI was co-founded in 2015 by <PERSON><PERSON> and <PERSON> and is ... Dec 7, 2022 ... ChatGPT is an AI chatbot designed and developed by OpenAI. The bot works by generating text responses based on human-user input, like questions ... Jan 12, 2023 ... In 2019, Microsoft invested $1 billion in OpenAI, the tiny San Francisco company that designed ChatGPT. And in the years since, it has quietly ... Jan 25, 2023 ... The inside story of ChatGPT: How OpenAI founder <PERSON> built the world's hottest technology with billions from Microsoft. Dec 3, 2022 ... ChatGPT went viral on social media for its ability to do anything from code to write essays. · The company that created the AI chatbot has a ... Jan 17, 2023 ... While many Americans were nursing hangovers on New Year's Day, 22-year-old <PERSON> was working feverishly on a new app to combat misuse ... ChatGPT is a language model created by OpenAI, an artificial intelligence research laboratory consisting of a team of researchers and engineers focused on ... 1 day ago ... Everyone is talking about ChatGPT, developed by OpenAI. This is such a great tool that has helped to make AI more accessible to a wider ...\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the final answer\n", "Final Answer: ChatGPT was developed by OpenAI.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'ChatGPT was developed by OpenAI.'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke({\"input\": \"Who developed it?\"})"]}, {"cell_type": "code", "execution_count": 8, "id": "c34424cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: I need to simplify the conversation for a 5 year old.\n", "Action: Summary\n", "Action Input: My daughter 5 years old\u001b[0m\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThis is a conversation between a human and a bot:\n", "\n", "Human: What is ChatGPT?\n", "AI: ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large language models and is optimized for dialogue by using Reinforcement Learning with Human-in-the-Loop. It is also capable of sending and receiving images during chatting.\n", "Human: Who developed it?\n", "AI: ChatGPT was developed by OpenAI.\n", "\n", "Write a summary of the conversation for My daughter 5 years old:\n", "\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "Observation: \u001b[33;1m\u001b[1;3m\n", "The conversation was about ChatGPT, an artificial intelligence chatbot. It was created by OpenAI and can send and receive images while chatting.\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the final answer.\n", "Final Answer: ChatGPT is an artificial intelligence chatbot created by OpenAI that can send and receive images while chatting.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'ChatGPT is an artificial intelligence chatbot created by OpenAI that can send and receive images while chatting.'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\n", "    {\"input\": \"Thanks. Summarize the conversation, for my daughter 5 years old.\"}\n", ")"]}, {"cell_type": "markdown", "id": "4ebd8326", "metadata": {}, "source": ["Confirm that the memory was correctly updated."]}, {"cell_type": "code", "execution_count": 9, "id": "b91f8c85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Human: What is ChatGPT?\n", "AI: ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large language models and is optimized for dialogue by using Reinforcement Learning with Human-in-the-Loop. It is also capable of sending and receiving images during chatting.\n", "Human: Who developed it?\n", "AI: ChatGPT was developed by OpenAI.\n", "Human: Thanks. Summarize the conversation, for my daughter 5 years old.\n", "AI: ChatGPT is an artificial intelligence chatbot created by OpenAI that can send and receive images while chatting.\n"]}], "source": ["print(agent_executor.memory.buffer)"]}, {"cell_type": "markdown", "id": "84ca95c30e262e00", "metadata": {"collapsed": false}, "source": []}, {"cell_type": "markdown", "id": "cc3d0aa4", "metadata": {}, "source": ["For comparison, below is a bad example that uses the same memory for both the Agent and the tool."]}, {"cell_type": "code", "execution_count": 10, "id": "3359d043", "metadata": {}, "outputs": [], "source": ["## This is a bad practice for using the memory.\n", "## Use the ReadOnlySharedMemory class, as shown above.\n", "\n", "template = \"\"\"This is a conversation between a human and a bot:\n", "\n", "{chat_history}\n", "\n", "Write a summary of the conversation for {input}:\n", "\"\"\"\n", "\n", "prompt = PromptTemplate(input_variables=[\"input\", \"chat_history\"], template=template)\n", "memory = ConversationBufferMemory(memory_key=\"chat_history\")\n", "summary_chain = LLMChain(\n", "    llm=OpenAI(),\n", "    prompt=prompt,\n", "    verbose=True,\n", "    memory=memory,  # <--- this is the only change\n", ")\n", "\n", "search = GoogleSearchAPIWrapper()\n", "tools = [\n", "    Tool(\n", "        name=\"Search\",\n", "        func=search.run,\n", "        description=\"useful for when you need to answer questions about current events\",\n", "    ),\n", "    Tool(\n", "        name=\"Summary\",\n", "        func=summary_chain.run,\n", "        description=\"useful for when you summarize a conversation. The input to this tool should be a string, representing who will read this summary.\",\n", "    ),\n", "]\n", "\n", "prompt = hub.pull(\"hwchase17/react\")\n", "agent = create_react_agent(model, tools, prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, memory=memory)"]}, {"cell_type": "code", "execution_count": 11, "id": "970d23df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: I should research ChatGPT to answer this question.\n", "Action: Search\n", "Action Input: \"ChatGPT\"\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mNov 30, 2022 ... We've trained a model called ChatGPT which interacts in a conversational way. The dialogue format makes it possible for ChatGPT to answer ... ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large ... ChatGPT. We've trained a model called ChatGPT which interacts in a conversational way. The dialogue format makes it possible for ChatGPT to answer ... Feb 2, 2023 ... ChatGPT, the popular chatbot from OpenAI, is estimated to have reached 100 million monthly active users in January, just two months after ... 2 days ago ... ChatGPT recently launched a new version of its own plagiarism detection tool, with hopes that it will squelch some of the criticism around how ... An API for accessing new AI models developed by OpenAI. Feb 19, 2023 ... ChatGPT is an AI chatbot system that OpenAI released in November to show off and test what a very large, powerful AI system can accomplish. You ... ChatGPT is fine-tuned from GPT-3.5, a language model trained to produce text. ChatGPT was optimized for dialogue by using Reinforcement Learning with Human ... 3 days ago ... Visual ChatGPT connects ChatGPT and a series of Visual Foundation Models to enable sending and receiving images during chatting. Dec 1, 2022 ... ChatGPT is a natural language processing tool driven by AI technology that allows you to have human-like conversations and much more with a ...\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the final answer.\n", "Final Answer: ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large language models and is optimized for dialogue by using Reinforcement Learning with Human-in-the-Loop. It is also capable of sending and receiving images during chatting.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["\"ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large language models and is optimized for dialogue by using Reinforcement Learning with Human-in-the-Loop. It is also capable of sending and receiving images during chatting.\""]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke({\"input\": \"What is ChatGPT?\"})"]}, {"cell_type": "code", "execution_count": 12, "id": "d9ea82f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: I need to find out who developed ChatGPT\n", "Action: Search\n", "Action Input: Who developed ChatGPT\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large ... Feb 15, 2023 ... Who owns Chat GPT? Chat GPT is owned and developed by AI research and deployment company, OpenAI. The organization is headquartered in San ... Feb 8, 2023 ... ChatGPT is an AI chatbot developed by San Francisco-based startup OpenAI. OpenAI was co-founded in 2015 by <PERSON><PERSON> and <PERSON> and is ... Dec 7, 2022 ... ChatGPT is an AI chatbot designed and developed by OpenAI. The bot works by generating text responses based on human-user input, like questions ... Jan 12, 2023 ... In 2019, Microsoft invested $1 billion in OpenAI, the tiny San Francisco company that designed ChatGPT. And in the years since, it has quietly ... Jan 25, 2023 ... The inside story of ChatGPT: How OpenAI founder <PERSON> built the world's hottest technology with billions from Microsoft. Dec 3, 2022 ... ChatGPT went viral on social media for its ability to do anything from code to write essays. · The company that created the AI chatbot has a ... Jan 17, 2023 ... While many Americans were nursing hangovers on New Year's Day, 22-year-old <PERSON> was working feverishly on a new app to combat misuse ... ChatGPT is a language model created by OpenAI, an artificial intelligence research laboratory consisting of a team of researchers and engineers focused on ... 1 day ago ... Everyone is talking about ChatGPT, developed by OpenAI. This is such a great tool that has helped to make AI more accessible to a wider ...\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the final answer\n", "Final Answer: ChatGPT was developed by OpenAI.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'ChatGPT was developed by OpenAI.'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke({\"input\": \"Who developed it?\"})"]}, {"cell_type": "code", "execution_count": 13, "id": "5b1f9223", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: I need to simplify the conversation for a 5 year old.\n", "Action: Summary\n", "Action Input: My daughter 5 years old\u001b[0m\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThis is a conversation between a human and a bot:\n", "\n", "Human: What is ChatGPT?\n", "AI: ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large language models and is optimized for dialogue by using Reinforcement Learning with Human-in-the-Loop. It is also capable of sending and receiving images during chatting.\n", "Human: Who developed it?\n", "AI: ChatGPT was developed by OpenAI.\n", "\n", "Write a summary of the conversation for My daughter 5 years old:\n", "\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "Observation: \u001b[33;1m\u001b[1;3m\n", "The conversation was about ChatGPT, an artificial intelligence chatbot developed by OpenAI. It is designed to have conversations with humans and can also send and receive images.\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the final answer.\n", "Final Answer: ChatGPT is an artificial intelligence chatbot developed by OpenAI that can have conversations with humans and send and receive images.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'ChatGPT is an artificial intelligence chatbot developed by OpenAI that can have conversations with humans and send and receive images.'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\n", "    {\"input\": \"Thanks. Summarize the conversation, for my daughter 5 years old.\"}\n", ")"]}, {"cell_type": "markdown", "id": "d07415da", "metadata": {}, "source": ["The final answer is not wrong, but we see the 3rd Human input is actually from the agent in the memory because the memory was modified by the summary tool."]}, {"cell_type": "code", "execution_count": 14, "id": "32f97b21", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Human: What is ChatGPT?\n", "AI: ChatGPT is an artificial intelligence chatbot developed by OpenAI and launched in November 2022. It is built on top of OpenAI's GPT-3 family of large language models and is optimized for dialogue by using Reinforcement Learning with Human-in-the-Loop. It is also capable of sending and receiving images during chatting.\n", "Human: Who developed it?\n", "AI: ChatGPT was developed by OpenAI.\n", "Human: My daughter 5 years old\n", "AI: \n", "The conversation was about ChatGPT, an artificial intelligence chatbot developed by OpenAI. It is designed to have conversations with humans and can also send and receive images.\n", "Human: Thanks. Summarize the conversation, for my daughter 5 years old.\n", "AI: ChatGPT is an artificial intelligence chatbot developed by OpenAI that can have conversations with humans and send and receive images.\n"]}], "source": ["print(agent_executor.memory.buffer)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}