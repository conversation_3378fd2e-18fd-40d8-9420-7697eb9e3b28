{"cells": [{"cell_type": "markdown", "id": "9fc3897d-176f-4729-8fd1-cfb4add53abd", "metadata": {}, "source": ["## VDMS multi-modal RAG\n", "\n", "Many documents contain a mixture of content types, including text and images. \n", "\n", "Yet, information captured in images is lost in most RAG applications.\n", "\n", "With the emergence of multimodal LLMs, like [GPT-4V](https://openai.com/research/gpt-4v-system-card), it is worth considering how to utilize images in RAG. \n", "\n", "This cookbook highlights: \n", "* Use of [Unstructured](https://unstructured.io/) to parse images, text, and tables from documents (PDFs).\n", "* Use of multimodal embeddings (such as [CLIP](https://openai.com/research/clip)) to embed images and text\n", "* Use of [VDMS](https://github.com/IntelLabs/vdms/blob/master/README.md) as a vector store with support for multi-modal\n", "* Retrieval of both images and text using similarity search\n", "* Passing raw images and text chunks to a multimodal LLM for answer synthesis "]}, {"cell_type": "markdown", "id": "2498a0a1", "metadata": {}, "source": ["## Packages\n", "\n", "For `unstructured`, you will also need `poppler` ([installation instructions](https://pdf2image.readthedocs.io/en/latest/installation.html)) and `tesseract` ([installation instructions](https://tesseract-ocr.github.io/tessdoc/Installation.html)) in your system."]}, {"cell_type": "code", "execution_count": 1, "id": "febbc459-ebba-4c1a-a52b-fed7731593f8", "metadata": {}, "outputs": [], "source": ["! pip install --quiet -U langchain-vdms langchain-experimental langchain-ollama\n", "\n", "# lock to 0.10.19 due to a persistent bug in more recent versions\n", "! pip install --quiet pdf2image \"unstructured[all-docs]==0.10.19\" \"onnxruntime==1.17.0\" pillow pydantic lxml open_clip_torch"]}, {"cell_type": "code", "execution_count": 2, "id": "78ac6543", "metadata": {}, "outputs": [], "source": ["# from dotenv import load_dotenv, find_dotenv\n", "# load_dotenv(find_dotenv(), override=True);"]}, {"cell_type": "markdown", "id": "e5c8916e", "metadata": {}, "source": ["## Start VDMS Server\n", "\n", "Let's start a VDMS docker using port 55559 instead of default 55555. \n", "Keep note of the port and hostname as this is needed for the vector store as it uses the VDMS Python client to connect to the server."]}, {"cell_type": "code", "execution_count": 3, "id": "1e6e2c15", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a701e5ac3523006e9540b5355e2d872d5d78383eab61562a675d5b9ac21fde65\n"]}], "source": ["! docker run --rm -d -p 55559:55555 --name vdms_rag_nb intellabs/vdms:latest\n", "\n", "# Connect to VDMS Vector Store\n", "from langchain_vdms.vectorstores import VDMS_Client\n", "\n", "vdms_client = VDMS_Client(port=55559)"]}, {"cell_type": "markdown", "id": "1e94b3fb-8e3e-4736-be0a-ad881626c7bd", "metadata": {}, "source": ["## Data Loading\n", "\n", "### Partition PDF text and images\n", "  \n", "Let's use famous photographs from the PDF version of Library of Congress Magazine in this example.\n", "\n", "We can use `partition_pdf` from [Unstructured](https://unstructured-io.github.io/unstructured/introduction.html#key-concepts) to extract text and images."]}, {"cell_type": "code", "execution_count": 4, "id": "9646b524-71a7-4b2a-bdc8-0b81f77e968f", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import requests\n", "\n", "# Folder to store pdf and extracted images\n", "base_datapath = Path(\"./data/multimodal_files\").resolve()\n", "datapath = base_datapath / \"images\"\n", "datapath.mkdir(parents=True, exist_ok=True)\n", "\n", "pdf_url = \"https://www.loc.gov/lcm/pdf/LCM_2020_1112.pdf\"\n", "pdf_path = str(base_datapath / pdf_url.split(\"/\")[-1])\n", "with open(pdf_path, \"wb\") as f:\n", "    f.write(requests.get(pdf_url).content)"]}, {"cell_type": "code", "execution_count": 5, "id": "bc4839c0-8773-4a07-ba59-5364501269b2", "metadata": {}, "outputs": [], "source": ["# Extract images, tables, and chunk text\n", "from unstructured.partition.pdf import partition_pdf\n", "\n", "raw_pdf_elements = partition_pdf(\n", "    filename=pdf_path,\n", "    extract_images_in_pdf=True,\n", "    infer_table_structure=True,\n", "    chunking_strategy=\"by_title\",\n", "    max_characters=4000,\n", "    new_after_n_chars=3800,\n", "    combine_text_under_n_chars=2000,\n", "    image_output_dir_path=datapath,\n", ")\n", "\n", "datapath = str(datapath)"]}, {"cell_type": "code", "execution_count": 6, "id": "969545ad", "metadata": {}, "outputs": [], "source": ["# Categorize text elements by type\n", "tables = []\n", "texts = []\n", "for element in raw_pdf_elements:\n", "    if \"unstructured.documents.elements.Table\" in str(type(element)):\n", "        tables.append(str(element))\n", "    elif \"unstructured.documents.elements.CompositeElement\" in str(type(element)):\n", "        texts.append(str(element))"]}, {"cell_type": "markdown", "id": "5d8e6349-1547-4cbf-9c6f-491d8610ec10", "metadata": {}, "source": ["## Multi-modal embeddings with our document\n", "\n", "In this section, we initialize the VDMS vector store for both text and images. For better performance, we use model `ViT-g-14` from [OpenClip multimodal embeddings](https://python.langchain.com/docs/integrations/text_embedding/open_clip).\n", "The images are stored as base64 encoded strings with `vectorstore.add_images`.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "4bc15842-cb95-4f84-9eb5-656b0282a800", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from langchain_experimental.open_clip import OpenCLIPEmbeddings\n", "from langchain_vdms import VDMS\n", "\n", "# Create VDMS\n", "vectorstore = VDMS(\n", "    client=vdms_client,\n", "    collection_name=\"mm_rag_clip_photos\",\n", "    embedding=OpenCLIPEmbeddings(model_name=\"ViT-g-14\", checkpoint=\"laion2b_s34b_b88k\"),\n", ")\n", "\n", "# Get image URIs with .jpg extension only\n", "image_uris = sorted(\n", "    [\n", "        os.path.join(datapath, image_name)\n", "        for image_name in os.listdir(datapath)\n", "        if image_name.endswith(\".jpg\")\n", "    ]\n", ")\n", "\n", "# Add images\n", "if image_uris:\n", "    vectorstore.add_images(uris=image_uris)\n", "\n", "# Add documents\n", "if texts:\n", "    vectorstore.add_texts(texts=texts)\n", "\n", "# Make retriever\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "02a186d0-27e0-4820-8092-63b5349dd25d", "metadata": {}, "source": ["## RAG\n", "\n", "Here we define helper functions for image results."]}, {"cell_type": "code", "execution_count": 8, "id": "344f56a8-0dc3-433e-851c-3f7600c7a72b", "metadata": {}, "outputs": [], "source": ["import base64\n", "from io import BytesIO\n", "\n", "from PIL import Image\n", "\n", "\n", "def resize_base64_image(base64_string, size=(128, 128)):\n", "    \"\"\"\n", "    Resize an image encoded as a Base64 string.\n", "\n", "    Args:\n", "    base64_string (str): Base64 string of the original image.\n", "    size (tuple): Desired size of the image as (width, height).\n", "\n", "    Returns:\n", "    str: Base64 string of the resized image.\n", "    \"\"\"\n", "    # Decode the Base64 string\n", "    img_data = base64.b64decode(base64_string)\n", "    img = Image.open(BytesIO(img_data))\n", "\n", "    # Resize the image\n", "    resized_img = img.resize(size, Image.LANCZOS)\n", "\n", "    # Save the resized image to a bytes buffer\n", "    buffered = BytesIO()\n", "    resized_img.save(buffered, format=img.format)\n", "\n", "    # Encode the resized image to Base64\n", "    return base64.b64encode(buffered.getvalue()).decode(\"utf-8\")\n", "\n", "\n", "def is_base64(s):\n", "    \"\"\"Check if a string is Base64 encoded\"\"\"\n", "    try:\n", "        return base64.b64encode(base64.b64decode(s)) == s.encode()\n", "    except Exception:\n", "        return False\n", "\n", "\n", "def split_image_text_types(docs):\n", "    \"\"\"Split numpy array images and texts\"\"\"\n", "    images = []\n", "    text = []\n", "    for doc in docs:\n", "        doc = doc.page_content  # Extract Document contents\n", "        if is_base64(doc):\n", "            # Resize image to avoid OAI server error\n", "            images.append(\n", "                resize_base64_image(doc, size=(250, 250))\n", "            )  # base64 encoded str\n", "        else:\n", "            text.append(doc)\n", "    return {\"images\": images, \"texts\": text}"]}, {"cell_type": "markdown", "id": "23a2c1d8-fea6-4152-b184-3172dd46c735", "metadata": {}, "source": ["Currently, we format the inputs using a `RunnableLambda` while we add image support to `ChatPromptTemplates`.\n", "\n", "Our runnable follows the classic RAG flow - \n", "\n", "* We first compute the context (both \"texts\" and \"images\" in this case) and the question (just a RunnablePassthrough here) \n", "* Then we pass this into our prompt template, which is a custom function that formats the message for the llava model. \n", "* And finally we parse the output as a string.\n", "\n", "Here we are using Ollama to serve the Llava model. Please see [<PERSON><PERSON><PERSON>](https://python.langchain.com/docs/integrations/llms/ollama) for setup instructions."]}, {"cell_type": "code", "execution_count": 9, "id": "4c93fab3-74c4-4f1d-958a-0bc4cdd0797e", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage, SystemMessage\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnableLambda, RunnablePassthrough\n", "from langchain_ollama.llms import OllamaLLM\n", "\n", "\n", "def prompt_func(data_dict):\n", "    # Joining the context texts into a single string\n", "    formatted_texts = \"\\n\".join(data_dict[\"context\"][\"texts\"])\n", "    messages = []\n", "\n", "    # Adding image(s) to the messages if present\n", "    if data_dict[\"context\"][\"images\"]:\n", "        image_message = {\n", "            \"type\": \"image_url\",\n", "            \"image_url\": {\n", "                \"url\": f\"data:image/jpeg;base64,{data_dict['context']['images'][0]}\"\n", "            },\n", "        }\n", "        messages.append(image_message)\n", "\n", "    # Adding the text message for analysis\n", "    text_message = {\n", "        \"type\": \"text\",\n", "        \"text\": (\n", "            \"As an expert art critic and historian, your task is to analyze and interpret images, \"\n", "            \"considering their historical and cultural significance. Alongside the images, you will be \"\n", "            \"provided with related text to offer context. Both will be retrieved from a vectorstore based \"\n", "            \"on user-input keywords. Please use your extensive knowledge and analytical skills to provide a \"\n", "            \"comprehensive summary that includes:\\n\"\n", "            \"- A detailed description of the visual elements in the image.\\n\"\n", "            \"- The historical and cultural context of the image.\\n\"\n", "            \"- An interpretation of the image's symbolism and meaning.\\n\"\n", "            \"- Connections between the image and the related text.\\n\\n\"\n", "            f\"User-provided keywords: {data_dict['question']}\\n\\n\"\n", "            \"Text and / or tables:\\n\"\n", "            f\"{formatted_texts}\"\n", "        ),\n", "    }\n", "    messages.append(text_message)\n", "    return [HumanMessage(content=messages)]\n", "\n", "\n", "def multi_modal_rag_chain(retriever):\n", "    \"\"\"Multi-modal RAG chain\"\"\"\n", "\n", "    # Multi-modal LLM\n", "    llm_model = OllamaLLM(\n", "        verbose=True, temperature=0.5, model=\"llava\", base_url=\"http://localhost:11434\"\n", "    )\n", "\n", "    # RAG pipeline\n", "    chain = (\n", "        {\n", "            \"context\": retriever | RunnableLambda(split_image_text_types),\n", "            \"question\": <PERSON><PERSON>blePassthrough(),\n", "        }\n", "        | RunnableLambda(prompt_func)\n", "        | llm_model\n", "        | StrOutputParser()\n", "    )\n", "\n", "    return chain"]}, {"cell_type": "markdown", "id": "1566096d-97c2-4ddc-ba4a-6ef88c525e4e", "metadata": {}, "source": ["## Test retrieval and run RAG\n", "Now let's query for a `woman with children` and retrieve the top results."]}, {"cell_type": "code", "execution_count": 10, "id": "90121e56-674b-473b-871d-6e4753fd0c45", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GREAT PHOTOGRAPHS\n", "The subject of the photo, <PERSON>, a Cherokee from Oklahoma, initially regretted that <PERSON> ever made this photograph. “She was a very strong woman. She was a leader,” her daughter <PERSON> later said. “I think that's one of the reasons she resented the photo — because it didn't show her in that light.”\n", "\n", "DOROTHEA LANGE. “DEST<PERSON><PERSON><PERSON> PEA PICKERS IN CALIFORNIA. MOTHER OF SEVEN CHILDREN. AGE THIRTY-TWO. NIPOMO, CALIFORNIA.” MARCH 1936. NITRATE NEGATIVE. FARM SECURITY ADMINISTRATION-OFFICE OF WAR INFORMATION COLLECTION. PRINTS AND PHOTOGRAPHS DIVISION.\n", "\n", "<PERSON><PERSON>\n", "\n", "<PERSON><PERSON>\n", "\n", "NOVEMBER/DECEMBER 2020 LOC.GOV/LCM\n"]}, {"data": {"text/html": ["<img src=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAVhBFADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD3wcEDI57UfnXzr8dvEGr6T4xt4bG/nt4WtlYojYBOTzXlg8ZeJsf8hi7/AO+6APt4HHXNGfrXxD/wmHiY/wDMYu/++6D4t8SsCDq93g/7dAH27jHOM0vB7c18QL4r8Rr01i6H/A6lHi7xOBka1d/990AfbXP0or4wsfH/AIrs2Zk1ed84zvO79K6O3+MPiqB0Vb21kTuZIGB/9CoA+rPw/Wl/Cvl8fHLxNC2DHYyj18sj/wBmrRtvjzq4iLXFvZg9gIm5/HdQB9HH34o49a+cl/aD1Veuk27e4JFWYf2hLwuvm6TCFzzhucUAfQnHrS5FeBP+0LtPyaPv/wCB4/pQP2iGyo/sMgdz53T9KAPfcjtRmvBJ/wBogxsPL0dWB/6bD/CrFt+0ValD9p0aRWxxtl/+tQB7nmjNeHn9omy7aPL/AN/P/rUjftE2QUY0iXP/AF0/+tQB7jmjNeFN+0Xb7fl0SUntmXH9Kr/8NFyA86F8v/Xbn+VAHvuaM14Z/wANF2nlr/xJZd/cecP8KqTftGSBj5Whcdt0v/1qAPfs0Zr5/tv2i5zN/pGhgp/sS/8A1qu/8NF2eTjRZT7ecP8A4mgD3PNGa8LP7Rdt20ST/v8AD/Cqcn7Rc3mnytEHl54zLz/KgD6AzRmvBR+0WDH/AMgRt/tLx/KqK/tE6iZmzpMQTsN1AH0RmjNfO/8Aw0VqG7/kDw4/66Ux/wBojVt526Tbhe2WoA+i80bhXzl/w0PrB/5hVt/31TW/aF1ntpVsPxNAH0fuFG4V83/8NDa1/wBAq1/M0p/aG1nbn+yrUH1JNAH0cSD3o49a+cIf2htXLnzdLtnHYISMU4/tC6t5hxpNvt9NxzQB9G8etHHrXzrN+0NqJjHl6TArf7TZpq/tC6qEO7Sbbd6gnFAH0ZkDvRuFfNf/AA0Hr/VdOssfQ/40h/aE8QkcadZf98n/ABoA+lePWlBHrXzKP2gvEuT/AKDYf98H/GhP2gfEwcFrKwK+gQ/40AfTW4UbhXziP2hdZ6HSrUn6mk/4aF1nP/IKtvzNAH0fuFG4V83P+0Jrh+7plqv1yf602P8AaE13DbtOtCe3B/xoA+kiQe9HHrXzX/w0Jr2P+QdafiD/AI1G37QHiI9LCzH/AAE/40AfTO4UZr5pX9oHxFvGbCyxj+6f8ajX9oDxMshY2tltPRSp4/WgD6azRmvmdv2g/EasG+yWRXuAp/xp5/aF8Qb8ixs9pHTB/wAaAPpXNGa+aT+0H4jPSxsx+B/xpjftA+JccWdmP+An/GgD6ZyaTcPUZr5n/wCGgfEhTBsbMn12n/GoP+F9eJA2WtbP2+U/40AfT+T7Uua+XW+PXicn/U2Q/wCAH/GkPx68V7SFWzAH+wf8aAPqPNGa+Vh8dvGB5DWv/fB/xqVfjx4uC4K2pP8A1zP+NAH1JmjNfKjfHHxkW3ebbKPTYf8AGnr8dvGAGd9qQP8Apmf8aAPqjNGa+UJPjf4xecut1CFI+6EOB+tOPxy8YhcCe3/FD/jQB9W5FGRXycfjf4yMqn7Xb4xyBGf8akX43+MSpzNBn12H/GgD6tyDSEj1r5Qb41+My2ftUAA5I8s8/rVSX4t+NpdxGqlRJyNq9PpQB9d/hRj2r5Ft/i142h66j5v/AF0X/wCvT5/i943lIP2wRAf3EOD+tAH1t+lH4mvkuP4veNAQzXKsvvGf8aWT4weM2bKTIAPSM/40AfWn4UZ/Cvks/GDxqy/8fKr/ANsz/jTU+MPjZAQ1+vPTMZ4/WgD62yPWjI9a+SV+M3jRPvX6E+8Z/wAaH+M/jKSIwnUY42JyHVDn6daAPrbPtRivkQ/GLxhwTqIBAxjb1/Wmx/GDxjGGB1IPu6Er0/WgD685o6dTXyCvxc8XqTnUw5914/nTbj4r+Lpwv/EyEZB/5Zqf8aAPsDr3pelfHw+K/i5V2nUufXb/APXqM/FLxeTk6q35f/XoA+xTz1FJxXx0fih4tPXVX/AUn/CzPFp/5i0n5UAfY3FA54Br45HxP8Wqu3+1WPuQc/zqOP4j+K/NZ/7XmLHselAH2SSc4xSnjuK+Of8AhZ3i4nB1ZwR7YqGb4keK5nVm1iVdpB+Q9aAPszjvQPbpXxy3xN8WMR/xNZfypF+Jvi1eP7TYj3H/ANegD7I5pOfWvjn/AIWf4t5/4mR/I/404fE7xZt/5CJz/n3oA+xOe5FHHYivjpviZ4rbrqGPpn/Gox8R/FOc/wBpy/h0oA+ysH1FJ07ivjsfE3xVjH9pN+IP+NQ/8LJ8VF8/2q/HbH/16APsrJ9aOvevjv8A4Wh4rB41EH8D/jT/APha/i9QAuoAD/dP+NAH2DnbR05xXx3L8UvF8p/5CrLjn5RQfip4uYjGpFce3/16APsXnuKTI9a+Oj8U/F5P/IVP5f8A16T/AIWf4t3bhqjfl/8AXoA+xsk9sUZ96+QB8WfF6/8AMUwP93/69M/4Wx4xPP8Aa/6f/XoA+w/wpOa+Px8W/GeP+Quf++f/AK9Mb4q+MpBn+1259BQB9h/Sl+tfG4+KHi8Ek6zJ+VA+J/i5jzrEv5UAfY+ecYo5r46PxR8Ysvl/2xJtNRf8LG8XqNi6xNj2oA+yuKM59fxr41T4jeMEfeus3WfrVl/ih41nUK2rTgD2oA+wMjIB6mgc18bn4heMmcN/a1ySOmBT/wDhYPjQEn+1bo5oA+xSecCgsDXx5H8QPG6Fiup3ZDDByKIvHHjiJy0epXpJ6hloA+xce9FfID/ELx0XAOoXat2Xb1pP+E68eHJF/esT1+Q8UAfYHWk/GvkFfHXj2P5mv73HqUNOHjv4gEqVvrxsnshoA+vfxNH4V8ljx58RWPFzd8df3RpT8Q/iGhVDdXI5/wCeRoA+sj+VB+Xqa+TT4/8AiP0+03JA5z5ZprePfiJIRm5uRj0jNAH1rn2pM+1fKX/CefEgjAuLj67DSjxp8TGXAuZyPUoc0AfVmM980ua+UD4n+JTcm6uR/wABNKviT4ldric/VTQB9W56D1pM84r5WbXfiTM677ibge9Vk1T4hQSu/nzkvwTk0AfWQORwRxRn0r5LgvviFCXK3dx8/OS1TRa18RI84vZ8Y5BagD6v4HYmgH6ivju78beMLG4MVxqcsT4zhTUD/ELxW4x/bE+3/eoA+zOaQHNfFZ8aeIySTrF0Sf8AbqI+L/En8Os3Q/4HQB9sfjThnHNfEEHiXXW1C3kk1O6LCRcEv719p6O5l0SwkYks9vGxJ7kqKAPAP2g9MkfXLXURgxLAqN7cmvGCwJyBgelfS3x3tgPB73fBJlRAMehP+NfNFABk0ZOaKM45oAGOCc0c54YYq3aWy3fmbiBirY0uEAZJzQBk7iOnWm9sYOK2RpkA9TSjTID0UnHvQBijg8Hj6UuRnPPtWw2m2/8AdP50wadBn7rfnQBk7jkkAc+1Ge+OfpWz/Z1sP4T+dL/Z9t/dP50AYuecnP4UueCAeD61rNY24/hP51H9jgzwDQBm59MAUHPqD+Na6WEB6qaedPtwPu0AYn5fnSHPbH51tfYrf+5R9it/7lAGMCcYJGKXgdP1rZFlB2Sl+xQf88xQBiflSc+5rc+xQf8APMU4WUH/ADzWgDDyR60n8PTFb/2O3HWJT+FKLW3H/LFfyoA5/cKNwPXp7V0XkQf88Vpfs8H/ADxWgDm8g9SeOlLuGMA4roWggUf6lagaOHP+pWgDEJx3o3e4rZKRH/lktAjj/wCea/lQBjbvUilGPUfnW8kcP/PNT+FTrDCR/qV/KgDmvxNGK6byYv7g/KgxRAfcX8qAOZLEd8UmQepzXSeXHnhF/Kl8tP7iflQBzee3alwR93OPrXSbR/dWkZU7qv5UAc5gjoCaTLf3TXQFUP8AAPypNqf3BQBgDOT1pdjHBCn64rfKEc4X86MD0FAGBtfOdrflS+Wx/gb8q3sD2/KlGPb8qAMHyZO0ZH4Uotrg8iJvyrok59PyqwpIAGBQBy/2O4bpA/5Uosbr/n3f8q6sMfpTwTQByY0+8I/1En/fNSLpN83/ACwbHvxXU5x6U4SfWgDmBod8R91Pxag6JeZ5CY9M11G4kdBVWYnJ6UAYS6HdscAoPxqUeG7s9ZY/zrVgJLdK0F6D/GgDnl8MXP8AFcIPoM1PH4Wc/fuh/wB81vqxFPBJ70AYy+EYjgtdk5/2anXwjaDIa4c/StbdwOKkGSc5oAyl8IafgbpJG/GrEfhLSQPmEp/4F/8AWrSGQOtOBJ78UAUV8LaOhyInI92/+tTh4e0dWOLYfiavecFXaBmmYJ56ZoAjXQtFXn7Eh4709dJ0hORYwk/SnA460u6gBUsdOTlbGLd6kVMqWy5H2WEfRahD4pwYY6UAWVEGciGIcf3acJIlwBFHgf7NViwxxxTCTmgC1JMhOfLj/wC+RVeRkc/6tPyoaPMec1ADjjNAE4dR/wAs0yf9mrSyRhAPKj/75rPXnqalQ+9AF/zIGXmKP/vmoj9nPHkx/wDfNQlgF6UxS3bpQBaVLcj/AFER+q0jQWhGDax4+lMUsKcGyDigBps7FvvWcWfpUL6dpzAhrOLH0qfJqNiOeaAKL6Xpm4D7HFtB6EVLHoOjSHc1lH+FDHDZ61YilAxQBC3h3RwuTYx5qo+g6PvI+xp+da8km5aplCXzQBXXw3pDDP2Nfzph8M6SxwLQD8a1oyQvSno3PSgDPj8NaOE2mxQ+9I3g7RJMH7M6/wC6+P6Vrb6cJT0xQBht4K0XORHMP+Bj/ChPBWi4OY5vwcf4VvZ4zSBuDQBhHwbooIIjl/77H+FH/CHaJjHky/8AfY/wra/GlBoAxV8HaH3gkP1f/wCtT08HaGzZ+zPj/e/+tWvnmpM4HFAGKfBuhA8W7/i3/wBanDwhoQ62hJ/3q1wTnpT8kmgDHPhPQsc2X/j1InhXQgf+PIfia2XbA4pqgnmgDPPhfQQny2KZqOPwvpDtzYoAK1kIZs1YyNpwMUAZA8M6FuB/s9Mj3qQeHNF2kf2dD+VXRxzTw1AFIeG9Dxj+zYfypU8N6OoyNPhwe2KvbqUlgtAFYaBpSx/8g+D/AL5qJdF03dxp0H/fNXfMYgc0B2AI70AVzpWm4x9it/8AvmkXTLAcLYwf981KCalQMOaAIYrGzOc2MAH+7Si0sRn/AEKHP+7UwZsY96a8bHndQAq2dmybvscWfpTDbQMP+PaED/dp+WC4BqMLICWZsL70AM+zWyHPkR/981MojI+WGMgeiioX+bHBI9u9Nwc7Uyp7g0ATFYSciNB9FFORU3f6tMf7oqADb3p28rn3oAS5ZE7Lj6CmxyrtyAuMdcCqczMz7TnHrVYxyl/KjbKk5JFAGkn7+cSHBI6cVe6jGAp78VVgTCADjFTEs319KAJDMywtCNhB7lelQiQJyoAI9utI+QhPeqk8hRlPbHNAFm51JIJFBITcQOR3NHn+XG8UzxO27cCB2rG8s3t3ulH7pRkL6mmXDFXJAK+1AGg8suTyCpPpR5YAyrAms6K4lOAGH41dV1ZctlTQAmTv6c+tTecyDDYqBpdoLY6UGZZYwcdaAJzcZHyHnvk0guQvUEn2qp5X90809SFOGPNAFozOxzuPNCSuykEcHuarq4JIBHFTKcHg8UATKdzoAcYouN2zb/ePUHpUasrOB0p7FflyTwaAPNvGKbPELKGJGxev0FYJHzHpXQ+NiD4jcg5Gxf5Cue/DFAAKXNJS5oA0b+OOOfSyg5KqW+uRX2noR/4p/Tf+vWL/ANAFfFd2hWbSyT98D/0IV9qaIMaBpw9LWL/0EUAed/HmNm+HzMvRZ1Jr5f47dK+pvjoxX4czgd5lzXywOlAAaB1opR1FAF7Szjza1RyBWXp2P3laa0AO7ilRtu4eooI6UYH40AMPNA6U4ik7UAIaaeBTutNNAEZ+ahUp4UU/HFACoB3pJCBRnFRuSe1ADSaTcelAGaXHNADlJp/UU0CnZAFACgU4U0Gl3UAPoPSmbvejNAC0o6U2lU0AJIMgVXZasMaiLDNAEW3FGKkODTW4AoAE4PFWEY1AlWEoAcSaQtSk0wmgBOlGaYWozQBLmmM3NIWpueaAFXrS7c0LirESgjkUAVz6UYwMU+VMNxUfPrQAhoFBFKBQBLHVkDiq8Yq0o4FABt4qQDkfSkFPHrQAmKO9KTRQA5TikaJWBz1pBTxQAyOILU2OaQCngUAKoHepVxUeKeoAoAkFSCoxT1689KAFZ8YwMmlCu33jge1OGB0pwoAVVAqQsMYpuBTSoNADuD0oxTeg4qTcNg45oAaB608HAwKTrR0oACc0Cig9KAGM7A7R0ph605iaZzQA4dakWoR1FTZGBigCTdmnAcVDu54qUN2oAep5xQvygmk6EGnHpQA0tmmnBpDgUmRQAjICOKagwakFNfC0ASfw01OtCNuWkzjpQBYJwtNVuabvylJGcmgCypp/eoRkU4E+tAE27imA9aFNPG30oAZ1pRTWOG46U7INACqMmn1GCQaUk5FAEqjHWn9uKYTkUIx9MigBSCelKAe9BYrzjFKWymRQAqhV6U7ORUKsAal3LQAAZPNLJgClyvamyLu5FADUJJzUhJJXPSiPG2gnj2oASTAf5fuilX+9Qq5VvTFIp+XBPFACjGak3MrcYxUa7SMgU1gzHrxQBOTkZOKjZielCKR1pThaAGNntTcllIY0MW7GoJH9eTQBIvAPPTpTx93f3qsj4x6VKJMtjtQA9tp5FNHIpjMF+Xp701pdi/M2PWgCvdyiBCx6ngUllbfJubOWOaRUNy+8nKjoD/Or0bBQAwzQBKeIxihQQQ5NNLgKc01pVA559KAGu5JPOeahnOYxnrQTsG/PHeqqy/aZmYH90nT3NAFlkAiGRtOO1ZDhmY8kjNaM0p2jmqjMck44oArplXFWQxdSD61W3BnIU8ip41O3r1oAlyFX5Ocdj3qPc4XaBtc8g0MTE3IzgdqkR9xEijae1ADHkYsOOQME1KgLSDPpRkBtzDOTzUyIGbcOBQA1Ix5h3fpUyqFyR0o/dgAjINPcgRgigBI1HJNQlWG7DZ5pwkAQgkj0qEfL82SvP50AcB4xDDX2z/cX+QrDrd8Ytu15j/sL/IVhUAIaUUUdqANW9/1mjfQf+hV9paL/AMgLT/8Ar2j/APQRXxXdkn+xiRzj/wBmr7T0T/kA6d/17R/+gigDzz47ybPh464zunUV8uV9PfHo/wDFAY9J1r5hHSgApR1FJSjqKAL2mjPmc1qKcVl6Yf8AW1pL1oAnzkelNI96Qc0YNADS3tSZzTiKbQACiiigAHFLu9qTNJmgBGJ7UhJxTuKQ9KAGgUvekFJnmgCTpTSaQtTCaAJRR1pqmn0AA4paSnCgBcUoGDSjFK2AKAIHbk1DnNOc8moxQBIDQTmmA0uaAHrxU6GqoPNToeKAJTzTGpaQ0ARkYpucU9qYaAFLUnvSUtAD1PNWUbaPrVRTVmLkc0ARyklqQDinyD5qYOtABilC08DNOC0AOjqwOlRotS0ALS54php4oAAvvTttAp1ADSMU5aTrRnFAEwpwqMGnigB4NKKYaUGgCYUpPFMGaGPFAEgkAGKesgJFU9/PNTxkHtQBaDYOKsp5eORk1VHTNAY+tAFp1THyjrUJGBimbzTwcigAWlJ5oFJnmgB2KQ0ZppNADWXNJ0GKUmgDIoAZ3qQDim4PpTx0oAYBg5qdeRmouKlQfKKAHgH1pzHCg4pBTfMO7aQcUAMkOKYvNOkpqdKAJkXNJKh9aVTzT2GRxQAyNMLTigIpUfAwetKDmgBFAHB71Kke3kc00YB5p+ScbTxQArDFNzihjSbcgGgB45FLnApAMKKU9OaAG9aUZUUgoZuMUAOLDinj5sVEq96eGwKAJs/LTQ5BAFN371oQDFAFnI24bmmdOO1MQlqe7BRgUABUE5BpQB6Zpi0vPY8UAP4A6YpASP4qjJPc0AE0ATK2BinE5UCo1IXuKkzmgAJwhFQgcY3VI7ADrUKruPBoAsIuwDnNS7eOtRqNwHNPIPbmgAHBqGZ9x9MUPJ0HvTZORxzQBG7lRkDIqPO/mn7WZR2A7U1sA4WgBB94DFOA6mgLyDT1wAfX0oANgdOTnFVJF82Ux9cdfep7lwiKUOC3alihKA54buTQALFtT5eMdqeo4yaAxCsO9V2L7TzQA93yGHSqklxgjNRXErKBweagc7Y2dj16e1AD5pJJnWONsEnGKsrE0KiFVxgZxVO1jZQJXzk9DVwne2cnd60AV5ZQcKcg0q/MmC1MlZ0kxgMKkTa5Y4x7UAVGQJISvNTRtxinMq+XkdScU1E2ZPXNAEqg+Zgc8d6mCk8nANQwIgbJY1MDxxQAu0EgHvTiGSP5eeajBGcsDx0qZXfChRx3zQAis5A+Wnlt4x39KlDKcDGDiq7xsznYcGgBzbNoHQ5qOdyyBWIA9abtK53cmpPs5aIFjk+lAHnni1NuuNzn92p/QVhDkV0HjJNmvkf9M1/kKwBQAUUUHoaANG6k3NpK/wB3HP8AwKvtXRP+QDp3/XrH/wCgivii4Tamktnkn/2avtfQ+fD+m/8AXrF/6AKAPOfjoiP4CnO7lZkOPrXy+OlfTPxxTd4KvWB5EkWR+Jr5moAKUdRSUo6igC9pQz5v4VqotZukD95IO3Fa64zQA0DDUPSnrTGNAATUZNOzTDnNAADzRmm4OaVuKAAmgdKZmnZ+WgBaM01cknNOoAaaTtSmkoASilxTaAHrT81FnFBkAoAl3UbqrGU+tKJCaALatQ7VApPrT+TQBGwoAqQLntTtg9KAICKTFSsvpTdvFADVHNTKcVH0p4NAEmaCaZmmljnrQA4mmGjNJQAuKcBTMmnA0ASJHmpgMLRDzT5BgcUAQnmkA5pTSZoAepqVelQJnNWEHFAD1p9NAp4oAKcOlAAooAUU4U0CngUAGMCmGpSOKYwoARWqdeVFV1X5qnBwMUAOozzR1ooAlU5pWpkdSkZoAhxg9KsRsOmKjwRT1zkc0ATikI5oFIW7UALmpFNQgHnJqReBzQA+k70gNLQA4UhpRTSaADFOA4pKUGgAIoofOBimj3oAQrzU6cKBUWaXeR3oAmzRjNQ+Z71IHGKAGSCmKKkPNNIx0oAfmpFPFV85HJIqVM49RQAoXc2asKo61XDbTxzUgYn2oAc2MmkUkHIoxmkGR0oAft8zkjFIQV4FOD8YNNzQA9Scc0SGk3ZprZNADhQyjGaZkgDmn5zQAqZpSO1KlOOKAI8beKkQg/L6Uw0qLls5xQBKCA2BSHl6Tbz1yaMgH3oAcfl4pQcCmsQRnvUayfNg0ASlhxkZpxAIyDimZFNY5oAkA3c0EsPu02Nqk3elADScrz1pI/kNNc88daFySM0AWg6oM+tJvOMjpTNu5sHoKGbaNvYUADAHJNKMZA9qj354pVIzyKAExjcM00R45pxX5ulSDAHPSgBowOtQySiMF6kcgggcVnCX7TMUH3VPPvQBNbbnk8yTlewq4zcnmmIgVMDikUYZQec0ARlyGOKTIHXvTnTDkCoLjhMDrQBWvJEhUljkdqz7V2vZCx4hU/nSXbPPMlvjrWiluttCIlAAHWgBWkUgAcAdBSbz2qJsFhTx8rUAKoLclalCgDd607D+WGwMUnLYyPwoATywTj8aiZSucetW0255FV5cI5ycj0oAVDu6AVLtPU1SiYckDAq00u6MbePWgCdNuSBinbWHORIPbtVaJS0jYGakVDz2oAnA3vnOeKFRcMRkN6U0MwAOKc5ZFDKcZoARQSDuApdwHCfrTcfxE0jY4KnOPWgDzvxln+3+f7g/kKwBXQ+MnD690/5Zj+Qrnh0oAWiiigDQuU2waRJnO7PH/A6+1NA/5F3TOc5tYj/44K+LJ4yLbR2LZUswx/wOvtTQV2+HtNA/59Yv/QRQB5N8bJJDpeoRZHleVGTz0OWr5z96+hPjZcfudQtdgw8ML7s+hevnvORQAmacOopopy/eFAGhpBxJJ9BWuD3rH0viST6CtUHigBxOKYTmlJpKAG7aDxTqY1AACPSkbk0UtADcD0pMU/FNNACdKXNIaSgAJzSU7FGOKAGimk+1PpNuaAGHimlcmpitN20ARFaVVqTAoxzQA5Vp+KQcU4GgByDFOI4popSaAEIppxjpQTTSaAGtSUGg9KAFJpp60A0UAFLSUooAXFAHNLSgUATwsF61K7qRUCinNigBDTM80E05BmgB6CrKDio0WpRxQA4UopuacOlADxRSZFKOtADgKkA4pAKeOlACEZpNtOpMigBhXmlpSaSgB2cUuabTwKAHK3tU69KhAqVegoAeRmlAxRRmgB/Tmgr3ppOQKXPOKABeTSucYpcYBNR8nPFAEoNOzVUOQ/p9KmB3NtU5NAEmaTFMLYOKcCCKAHg0oFNFPoAU9KjNKTTSaAFprA4pFOaduOcYoAhJINSpk0MhcDHFIEYd6ALAKgZJqN5EPQ5NOUDHzc0MoxwABQBHtkIGMKPep0UAfMc0wcDrRuoAkAGcgVIDmo05qUHAoAeFyKYxxTlfNNbnpQAwA7utPIpoHOal4KigBqoeuaXFOA4ppoARhwKA1IxpoPNAEytSlvaozyOKcCVHIoAcemcUIe9M8wscYqVQAtABjnOacBmkxmmM+04oAmLAcYpu0dcUwNkc08PhRQA0KS1G07sUBiTxTwrbs5oAAADipBhTmoznPFKScUAI67jkUqYFNBYZ4NOQEnNAFmPG0k8VC2CSKC5PFCjHWgBigBjmlLAnpjFNk68Umw0ASdTnHFDMG4zjFN37F2+tRSsI1GepoAguJGPyJ1J61LFCsUYI+8ep9aahXOSOamZd2OaAGgk1IjbCdwz6U1iR2pofeSp4xQA5ZApO4Zyap3cqgMw5P92pZZhGMFSR2NVYYiheZwTnoKAIbKNgWmlXLdvapml354qCaYluuD6URszDJ60AH8frUqqXOegFM2PnIyB64pGEgHyruoAlLlgFVuKQbw+M8VGinHTb9aGLg8HJoAvRtwdwzUD7d5LLxT4lYpk9qjlO/jpQBHGoOT0B7VZXbgnbVPdhsCrKMQu0jBoAd80ZyrYzT0kIB3cnNGzODSxxcMW4oAmALKPmxSGQ52sOlInTdmm+cGbJX8aAHFQ2WLYAHApjMpUZHPpUr7RHuA6VGwBVXA60AefeLwP7czjH7sfyFYFb/jP/AJDuOn7ofyFYHTjNABQelFB6UAalxg2Oi/77f+h19paHx4f03/r1i/8AQBXxbP8A8eGi/wC+3/odfaWh/wDIA03/AK9Yv/QRQB5P8bcDQNQyF/5YLnvjc1fOP4Y9q+ivjcc6RqY7BbfH/fT1869BQAUo6imilHWgDQ07/WP9BWmDxWZpo/fP9BWkKAFJpM0wk5pMmgCTNGM01eaeBQAgFJTjTTQAUjdKOlBoAD0FJijNFACgU7KjgikzTSeaAHNg4wKQDim7s0hf3oAcTTKQtmgGgBacBSAc1IMUAJilApTSgcUAAGKQ089KY1ADCaTPFBFNNABmjNNb2pOaAHUmaTmgUAL1pwFAFSKvtQABc09VxSgCnUAHSmk0ZppoAMZap0XFRoOamWgCUCnY4poqUqdoNADMU4dKXApG9qAFAzT1HNRg1ItAEop1NFHOetACmkPSlx60YFADQKQg5qTimEnccGgBwz3qRelRAnuakU0ASAYqVRxUa81IDigB2KTFLml7UAIDilB5zUbOqjkrmo/OYH7y4PSgCZm596Y29jhuAfSk8wA4HzHHNJFchmYKO2KAJfLKp14pVcBAUfBHU0za7n736UksZC8Fh9OhoAlLZABOT1zSioVmyoDEbvQ07zVXl8r9aALS80p61Akh+8pytSNMGGVHWgB/HejANRq27qKeMZoAeEAWmAc1ISKjJx0oAkxxTCKevIoIFAEYNP7Uw9eKUZzz0oARjigDNBXJpyrigCaMYWng8VHk05aAHgdaaW2fjT6ay56igByjK0AYFC5pxoAaX9KjJOKecCo8nnNACAk04LQlSGgBMEYpWzxTSSKecuODQADFOzgUzG2jPFADg/pTGBJpM4PFSKaABfepAN3SosE96eMqODg0AKOGxTw3ao8+vWnAd6AJei1GG5pWfI46UiLu5oAfnIp33QaYFINI+4jigCRVyM0hPFIr7U5poJbpQAhwetJlemTUvlkDNRsy+nSgBkn7tSW59KqQ+ZcSFpFOO1Ss5lcD+H0qcx/KNvGKAG+VtHTBp4GRSDJ6nmnBtp4oAYS2cGlO1VY96JXwMkc1mXF0zyCNMgnqaAJWm3ttxnmpdysuzOKrlfJH+1jrTUkXdlhk0ANkhCtk80+PBHAxU3liRNwOPaodpDHtQA9WKjpkUNz904pQeMZpAiHkttoAawpoGCDSyFeMOaX5Qo6/WgCaN8qRUUgxk05RHgYfB96ad+/51OO2KAIFjLDOcGpUUheWyaRxu5BwfSiMf3hQBIGcdOanXeI8qPzqtIgO361JgkgE8elAEyEEnPWjeEwWGfakjcK5VhjNK+1CQpBP8qAFlkDoccZHSo06Bc1AznB3cjsRVkKm0Y4bFAHB+Nf+Q8P+uQ/kK5uuj8bA/wBupjr5Y/kK57AoAQdaWjFFAGjLIWtNIUjgSNj/AL7r7U0Pnw/p3/XrF/6CK+LJXU2WkAA5Ejc/8Dr7S0I7vD+mn/p1i/8AQRQB5L8bOdL1Uekduf8Ax56+de1fQ/xrJ+wasP8Aplbf+hPXzwelACDrSjrSCl6UAaWnHEz/AEFaGcc1maef3z/StHPFACHrSUppKAFU1IDxUYpw6UAKeaUCkpe1ADWFN7U/rSY4oAZnFANDCoy2KAJaCMimq1PFAEWCKYwPrVhl4qFxQBGKkWo6lXoKAJBwKM0dqXFADhThTaM0APJqNjTgaYxGaAGk03NKTTCeaAFPNJigUtABtpdnPWlFPAoAaFxUqikAp4FAChaCKdSHpQAwiig0Dk0ASRjNTBfemIMVJQAuCe9SZO0Co6ctAD80g65p2KMUANA5zUi8U3FOFAEgNIc7qBS96AFXNOpKCeKAEJxTc807rSAc0AOFPXikGKcMUAPWpO2c1GtKSMHBGfSgBxbFI8vlpuK/rULSsXCoMnv7U14wWzM/HUUAMklZxuCgD3qs11HHy559ucVTvb1fO8qMk1EZUiiJllI9ABQBo/2jiRRtO3ucVTXWIo/MCgq27uetZUt6XJ2ZA9SaZvVhuZue4xQBurrjgYCsfxqZNbcr80ZJ7c1zRZf4XYU9ZHHG7I9aAOhGo+c2Wj57HNTi7bHzJvHua5xXdiMMPzqyskoxjBoA3kv0TgoymphfIwGDWIt3KPvqWH0qRZ435xtPoaAOghlEnG4D6mrQQqBnHNcwssgOVG4dqtR6hcJgMpNAG4+F75pivu7YrM/tRM4cbXqeHUEZsEgn1oA00IHFDVGkisNw6etSnDAEdKAIv4qcxxxinBDnOOKHAoAF6dKdjNRg4FODcUAOzzT1FRLktUo60ASDml7UoHGcUhOAaAEBxT+CM5/CoQaUtzwaAEk7VEzZ7VKTu4NMKH0oAdHzzT93NIi4WnBeKAAkY6U1G5p1NYY6UAScdzTGxk4pMUm3nNABtNO6CgHNLQALn1qVR3qMEdqeMgUAOAAJJpTkg1GzYI+tTEfKKAEXHl80xTg5zxTipIx0ppGBtzQA8v6GkUt65qEIc1KpxxQA/g04DsOKVEB704qEPtQBHu2t8zcVWmfzpdkQ+pqWWQAFSMluB7UsMYgj/wBo96AGrFsX1oXJbOfwqTfxim/cO7ue1ABIpIGOtIoJB9anRc/MfyqK4bylz0FAFKabahLN0qKBFdTKRyelVxvupjuBVAe/ep3kESlAelAETlick5pB16UK28c1MqjFAD0bPtTn6ZPWofMw+Km6jNAFYgs3pinBCfvc09sU3zdvbNACYy4TjNSyZUYBUj2qSKcq3VQCOmKaSq5Bx1zQAiIMbjg4pHkxzg4pd+VIC5pGyY+4oAjJUjPeo3b5Qc8mkYYNMdff6UALvIYAnNWASTkHmoYVPcc+9PCgyZJ/KgCQvnAfg08EbjgfUmoGRi+8gkCpch48qKAJdgwoGMZz0qUkmXheKhQFV3e1SxTYHzDmgDg/GQT/AISJd77VMY5xmubYBXYA5APXFdP40Cf2/CzA7fLGQBXNSgCVsEkZ4zQAyg9DRRQBpyMzafo4ONokOOOfv19paED/AMI/p2cf8esf/oIr4ynMf9j6KVxkTNkenzmvs3Qjnw/pp/6dYv8A0AUAeSfGpf8AQdWP/TK2/wDQnr51r6K+NZJ07VsdoLb/ANCevnQdKAFpD0NLQelAGhp4/eH/AHK0dtZ+nH5m/wB2tPGBmgBmMU004tmmGgBwxS1FmlBPrQBIKUnimZOKaSfWgB+aQtTC3vTd1ACu1Qs1PaoX6GgCVXqQPVMOalV6ALYbIpCKiRiak6igBpAHNN8zFPKjGKgfgkCgCQS81IrZFUhnNWY80AWV+7RTQadQAGom608mmMaAENJSZozQAtLigU4UACipBTRThQA+ikzRmgBwNBbtTCcUxiT0NADzT4lqJcmp4wRQBMOKXvSAUuKAFqRaiXPepFoAkooooAKd2ptO7UAKDS55ptAoAkBopBS0AFKOlJRmgB1OFMzgUbwDigCbNMeUJnc4UVTe8PzLGwaQdvSs+bUoEJDuZn7qOgoA0DeFXOxd2e/pWXc3d1cSCMNtT1qtc6lPLHhQET0HWqn2yTywoJoAtTmGFQAT5nc1SZjI5+cmmjduyxyfWkYt2FADtsa98t3pN2OlR4JPzCnqvvQBIJh/cp25mAIAxTVX3qYAAcjNADl8zHAFSKWBGc5poIPTinhyO9AEyzsOoqQXKspVlAx3qFZ16NzTmEEg4OG9KAJo5lAPJxUqTq3c8VUVJYx/Cyehp3nL/CNh9+9AFrfHIfl4PvSpuBPqO9U/mkOSB9VpDLJHkRuzf7PcUAbFvczKMBsj0NaEGoORtAAx1zXLpebSMnaw7VbS/ViC3BoA6uO6WQc8GpeCMZrnI7/uDx6GtCC+jIHzBT6HpQBojB4p+0YNVUlBOQc5qz1HFAAkZHep1QmoU61bjOFFACcqMGmNzU74I6VAw5oAaEyuaRR82ak5AwOlNUc0AIeopx6CmkgNzTs5FACilJ5pnPrRk0AKQe1NfIIp4bFK67hmgBlNJPSn9KaTigBnIpQTijBan8DtQAiKetTA5FMLgYAp+CSMDigBMAkZp7kgDFIF9R3p7EDHtQA3LDrUbN8xqdyHQY61XdD60AKG5o3fvKhG4Nyadnv3oAtK+KSafYmTznoKrlyq5Jpke64k3H7vYUAWIAWPmP36CrEjAgACmBlztC8ipQpPVaAK0gI5FEZLYzU7qFHNVpJAgz90UASXN0kCEk4wKyfOlvPmz+7BqpetNdT+WrEoTVsEWdqIV5J60APkmVIwgGD61VZWz1z70bWfgnNP2OoXacj0oAdEuBnFSeYFHSlVwvVcHFKNr54oAWMKyZPWgE00BV7mkaUDgUASqwB4ANRSIrZJODmlVlK56Gmkh1wpwaAICzK1TJufmk3pn5xU8eGUbOBQA+JgvynvxTiAgwTmlVFUZbrTZIyx3JigCJ4srkVX2Z4PbpVjcwHXimnDDdQA2Pbkh85xxUsAKknj2zUPLvkcEdaeJAW56CgBhDSb+SDSwwMqncxzTzt2kK3Wpg2xQoHagCPbhRhjkGpRJvAGzGO9RnLnHTHNTRnAxuoA4TxgS2uxj0jH9K51zl2+profGRI1tCvB2D+lc633jn1oASjtRRQBLHMzy2cOfkWQEfUtzX3Dof8AyANOx/z6xf8AoIr4YgP+mQD0kGPzr7m0L/kXtN/69Yv/AEAUAeTfGcY07WD1/cW//oT185jgc8DHWvpH4yKG0rWye1rAf/HpK8V8G6Vb3Ul5qmoxF9P0+LzHXH32/hUfXFAHL5GcCg5OQKsXzxvfSyRriNiSv0rtvAnw3m8SJ/aWozDT9Gi5knl48z1AJ/nQByOm/ebkfd7HNaYbIrqfHup+G7y+trHw5bRxw2qbHuE/5aH+v/165UZJ6flQApHpUZqTPFMJ56UAMpQKWjIoAXtTDTiaaaAGke9NpSaKAGmmFc1Lil20AQCOnKozUu2lC0AIFxTxS4zTgtACGmGPPNS4oz2oAr+XzUipUmAaMYoATpQTQaYTQAE0xmpc0mKAG0ooxThigBacKbxThQA4U4UAUHigBT0puaaWphagCQmgcVHup6c0ASIOSasL9KjQYqVaAJBR3pBS96AHAZpwHNIKUdaAH0meaWlxQAlOHSkFFAC05fSkFKvWgBc+1Ju9qO9NNADt1AbJ6VGWA71C1x1VecelAE7ygDGKryzKmWZsbRUTy4Gc96zLuUyuRuwfSgAubtrgeXEuxM5LDqagdogMbQD6imhmUEMxA9qrsyk8Zx70ALI6gYXqajQ4HPNGM9AakVR3oAcjr3qQOvpTdiU9QuOlAC7UbrUyxREdvzqIqT0WpFhYjOKAHrCgqQRIeBUYVx2NLyvJoAc1t0w+PwpDYzdVKt+NL53TjNS+YhwTx+NAFGWKVGyyke1NDY68GtUSZHGGHvUcscci424PrQBQErg4DZFKWZeRz9aJIjF93n6VWMxB5GPrQBZS9CtyNpq0kiS/MeGPcVmsUcdPm9aakjRnBNAF2VQT/WohkdTn6U0ybwM03eR2oAmE0kbZDcehq5BdJJjLbX756VltIfSm+Ye+D7UAdLFdSwnJbKnoc8Vq22pB1XJ61x0N6VG1vmX+6a0oZFdP3Em0j+A0AdvA6SruBqwoyM5rlLHU5YJFjlwoPTdxXTwTLIvDAkdcHpQBKXxxjNN3Z7UjEqc4JoUh6AFJ9qUYpSAOtNdfSgCCU/NxUkfSgIDzTgMUAKOacAKaSBSqeKAHED0pp5xTz0ppoAY3XFMde2anA4zUT9aAHJ8i8jOaaV6nNO/hpmc8CgBygNin72Em0DgVGpCd6kV9x6UASAk8E0kg9KQjmkDHPSgBN3AXGKHbAHekJ3GlZM45oAhOc04Lx1qZkyOlV2cbtoPPpQA3lpMdqsgBVwgxiokQk5xUoYJ944z0z3oAVCxIIXmrhYhBxVUTxxjD8H0qtLqMfIXNAE00mOpqjcSbwU6ioTcNNJjmrDokSBiwz6ZoAjhURR9MmoZGVnJHWiSTIBpuzfyxH4UANMoHQUizsrAg02RKai+qnHrQBdWRZOSOacFABw1VQ6A4yBT93vQBOrEdcGmvtc+hqLfSZ70AO27TycikZctuXgelOABXqKfFgJ81ACJgPnH51ayPvDH4VEEVxkEU9IyF45oAUO2Tnp700E5NNkJIwKI2wDmgB5YFMYqsWw22p8grxURQlulACL/snk8UOn8PT3pgBUkg80r79oPegB3k4kUA4A71MCyNz83vTYz5rKvTFWP4OnTvQARYLEkdRSFAhznNL9wZ6U0OCeSKAOG8Yf8AIaj/ANwf0rnZD+8b610XjL/kNR4/55iubf77fWgABzS0g60tADIP+P2H/rov86+6NB/5F7TP+vWL/wBAFfDEH/H7D/10X+dfc+hf8i9pn/XpF/6AKAPMvi6AdJ8QKRn/AEKEg/QyV4skepSeFrLQ9PiMr3TG6mjRTuxwBn24NeufFu8b+0L3Sh928sAfptLf414jqWt3Vtrc82nytaqFEKhOoUf/AKzQB2+jeGPDHgpYdS8YXkc94PnTTYiGI/3q5zxp8QNQ8UsLSGNLLS4iTDbxArx/tetVvB2nWniHXp5demk+ywwNPK5kAOAPU16Za+C/hpBolxrH9oJc+VGzLA90pBIHTgUAeU6Boeqaossun2U1wqDLGJCQKcYpIZvKkVoyOCGGDX0b4TuYtH8Brd3qWtlbxyMx8scMgAICnvXh/jDV7XxB4ku9RtYfKgdvkUDGfegDC3YJX07000MpB68dqbQAE0gNKRShRjpQAYppNPpCo9KAGYoC07FOA4oAQLijFPAoIoAZilA4pwFSLsUfMuaAI1FOo/iJHSigApM0UYoATNGaMCjFACE0ynEU3FABRSgUYoAbTSeacQaTac0AANPU803ZTguKAJRSGkBxRuBoAY1Rk0rEk9aSgBVqZBUKip0oAmU1KtRqKlHAoAfS0wGnUAOFLTM04UASg06oxTs0AOFLTRThQAYpR0paO1ACE0xjwafjNQyNzgUAQyNkYFRAAdeB3NOZ1jOTWbqGoE/IgwPUUAJc3W6Uxofl/vVUeZI8qnzHuarszHgGhUJGDQAPIWpU3EDgUbOaeFUfWgB3ls3oKnjtd2Mmo0HvVmPjvQA4Wif3TUqwFeAoxSqW9qkBbHQ/hQAhXaOUoVdwOOKkUvzwfxpBLGhwQc0ARFTSGIutT7lbpSEkdGxQBVMLDpULIwNaKncOvNMZfXFAFFldfnDcelNF4w4YVNMo7GqjigCwbhJBgDmqUwBamtuXlTTC57jNACFWHQ0B8/KRyO9Ox8tNFACgkcZp4Jpirg5pwRj0agAIIppwfrTnDgZJNR5zzQAYINWLW52OdxwO9V9xIIz+PpUjRgp5sfQ8MPSgDooCt1B5bnjqjd60dN1Awt5d18rdEPr9a5KwuzBOE3kg8fSt2SRJo+VBbGRQB2EcpmI2nI6mrGQ7bV4IrntL1DZsRuC3ANbq5cNKnUHBFAEox0brTsfKaaF8wbv4h3pyOGX3oAYg60pOKceBxUWc5zQAEZ6U9VIFNQgdKkBJoAWg9KWk70AM5qOTrUz8VD99jmgAVuMUijGad5e057UjHjigCMnmpN3GB1qHkmpANvPegCZZBjB607eKomX94fWpo8sRzQBM3BqRMGjyjjJ5oVSTxxigB8zBIsiqEa75C9S3EhcmNTSBfLX0wOaAJCwiQuzBUHr3rON1NdOwtI+O7NUDznU5hDu/doeSKfJcQ2aFYzjHFAD/ACoYhmVmeQ9Tmq1xPt6EBaozXjzZ2ce9QnzWXljt9aANGO7jU5UE1K948q/cwtU0vbWCJVON3rQ+qW+4jPFAFgTIPc+lSrJnoKz/ALZbORg4NXreeIjhhQBIXcdVFNMvykEUsz7h8tVGmZFOSMd80ATllyMipUlQjGKyX1BD9zn60R3MjdHQUAapINGOOvFU4LogkOoOfWrAOenBoAd5wyFx0q0pDLVBwfTJqSKTbwxoAtGUJwBT4pSc84qvK21Qw5Y9F9akiUhAZBhjQBMCO5phBLDFNQDByO9ODbQaAB8BvegswRiai8zJ4qXqmKAK4fByR14FPXKAluRSFPbimMWxjNAE8Jwd3rUxYE4DHJ7VWhDFcdQKsxqJGyD0oAQs5U7ugGKcgXaM0xOS/p6U4gDPHSgDifF/Grxf9c65s8sfrXSeLznVk/65VzXegBR1paQdaU9KACD/AI/Yf+ui/wA6+5dC/wCRe03/AK9Yv/QBXw3EjCWCTsZB/OvuPQDnw3pZPe0i/wDQBQB418X4yfFbzsGaOLTGJC8H+KvB71Al07LkoTlS3Uivob4tWhkutXu1k2tDpYwPXO+vENL1+CxjjmksI7m6iwInkPA+o9KALL2y+HfC8vnSbdQ1JABD0ZIvU/XJ/KuYD4XAbIPVc8CrN1dXWqXnnylpbiRsbcZyewH+FeifDrwJpt/f3EviZJE8jDLYqv7xh3LL1xQBxWmX1zJGYGuZWiTrHvyoz3xV6PO3nqOlen+ObLwHF4cVtGS3j1FH2IsJySOM7hXl/wB0Y4/CgCuQd2Cc0YqRhzmm4oAQU7FIaAeKAFxSZozSZFABThTc0A0AS5pKQUtACgYpcDvRSE0ABppNBNNJoAUmjNNJpQeKAF3UbqYTikzQA/OaMUgNLQAUUopcUAIFp2ylFOoAYVxSEVJjNJigCEim5walcVGRQBGTzRjJpxX2pQKAFVamUUxRUy4oAkXinUzsKcKAHilzSUUAKDmnCminCgCQU6mignmgBwNSDpUSnmpVPFAC0HpRTHcDuKAGvJtHAzmoGYZx3/lRJIQMdz0qhdXHlthTlscmgCO9nAO3r71muGY8nipHJdsmjAx1oAh2gHNPHTgU8R7qlVNvagCsIXJqZYRgZPNTgjsKdlz91aAI1hLcBTU62rgZxTdtxkVJmcDk0AAiI9aeEcdG4pBubrxT1Uj+IUAOUPQyS+gOfanq+3rUiXAzyKAKpikX+E0qDHLCr+7f1qGW335wwoAiaNHA2nBpv2FmBIaq7QzxsduT9BUsN28R2vx9aAKk9tMnfNUnV161vSOXXPb1rPulHOaAMwk9jSLlzj1qZYiXAAzmtJNP2x72UjAzzQBmmJox8wqBjg9KtzN8+EOahZN3XGaAHRIJMcjJqWS2ljwcZFV1UxkH8q1bS4JXbINwoApYDLtIwarSxGM+3rWzPbI/zKQD6VTZdgKyDI9aAMxgccDK1LG2EOOjHkVIUwcLyp9O1RtEYzkA4NADHAWTKjHf8a1bO4zbFmPzK36YrKP3uauWe3y5gemM/jQB1GlxpPp0nmNyr7g3pWxZXLyISDh14A/vCszSURdL+bjKktVuyYIyMOuKANkNujV14BOG9qco2uT29Kqxy7GdSPlbmrQYFkGRytACs36iomGBnv6UoPBJ7Gm553H8qAHqDjNTKO1R9QKmB2qMc0AKODyKXAPagnIFGeKAIpF3cZqMIVbrU2QW60jgZoAY3Tk1COvrTnPYnFR79uaABsbuKGJC9aaDk57UP6igCEoS2RVy3TI5OKgjJJxirqKABk4oAsLyvJqCV9gIHU0ryBF4IqqHM8v0oAmjQAZb7xrP1a6FtblAcse9XZJQhYnjjpXK6rdebchCec9KAGxTPFA+043nJxVdpCR8xLZp1wfLiAPGapsTgtuxjoKALBnMakZCjvUAuJZflWUCP6VEsZmPmSHAHb1qOaYLxj5R0AoAsSLGn33z9Kr+dGrYHNU3Z5D1xSbT91cs3fFAGmlzEOuBVuLEgyko/OqlrojXEe5nxntUN1pd1YMCp4+tAGt9ulthiVSR2OetVZ7r7S5IJA7c1RW7dhmc7gOAKbk7gI8sD6UAXF3MuSA1SR+QOSXB9qgaZYlAz83pT4ZHkOGYKDxQBaZt6Aq2CKs2dyeFbr65qMWCeXlnwPaqs1o9sd8bkj070Ab/AM2QR0pssyKPuktWZFqjiIKMbu+aR9SCjJKk0AaKykOrucsOg9K0XuFlC84I61y0WqIJCX+YmrsF8rsCTxQBvKN2cGo3JPGMUQTIVBUg1IxBGRQBGqYOc1IGycAUw/6s+tMhc45FAE+M8YqJwAeal3dwKgkc55FAEYn8tjtOVParVsrbic7R15qsVOzKgVZjYlFB696AHg/OVA49aVuCaYDiXH605+HGaAOJ8X4Gqx89Y65sd/rXUeNQo1G3wOfLOa5igBR1pT0pB1paANFI1/smyfv9qA/DmvtTQf8AkXtN9PssX/oAr4qRv+JXaDsLof1r7U0D/kXdM/69Iv8A0AUAeY/FYZh8QDv/AGYhH5vXz7omg6j4ivBaabC0j8KSR8q57k9q9++KZn+3awqbfL/skbs9T9/FeSzeN0sfD0NpoVl9hlQ/Pchskn6UAd14Ps/Cng5baabGqa3InEUWHeNvQKOn1NcB4w8X6lP4qu721hOnSupjdUPzMp7N/kVqfCxLl4/El9bRPLqMVkfsjMMnzG3dPfOKo+G/h7qHiDWLuPV7hdL8j5rhrr5X5z0zQBzOms7MzOzMxGck960TwuDXovjH4V6Z4Q0D+07LUXlPAEcpB3e4rznOVFADSaYTzS4oxQAUlOIpMUAIRSYpwFLxQAzFOAopQaAHAUuKAaWgBaa3SnUHGKAIu9NLdqc2BUZFADqKBS4oAaRSYp+KXbQA1RUmKMYpwHFADcUYp4FIaAEFOpuaUUAOFLSClPSgBpFMIp9NNADMUYpSaTNADxThTVFSgDFAAKkXpTAKetADqO1LgUuKAGr1p4pABS0ASCmseTSFjTc0ASKeaeDUINTLjFADyflqGSRIx84yp/nTnbOMHFUby4VXC56UAJLKIwWkPzHoPasuSTcxY0yeYySEsc1EX4x2oAfnNPGMDNQB/SgyE8YoAvK0e0U8SRgVngP6VKiMf4aALRmTsKUSjHFRrbOeoAoeEqeMUATpKc1JuyKqqrAZJp6yAA5NAE3NJg5601ZC1NYsCeBQBY6DrSFXIJFVvPKnkU9bwjr0oAlW5ki+8M08XKSnIba1KksM4wMA02SzB+YUATC4KjDYI9ajdoZM5wCarMjIMZ4qs+c8MT7UAXY28tipbK9qJNjk7h8tZryyL64FTw3YkUI4zQBpabbQS30alhgmtLxBLDBGLeHBIHJrn41kjl8yMkAelWLoySRh2ySeKAMpoyzFhSKDjPer9vGCSCKJbMo28D5T2oApA7T865BqwsbKA0ZzUjQbgMDimLutH+bO00AW4ZVk+WQYeiaLaSrjIPekTYy7s9alyCAj8jsaAKLRqnQcCmCNW5Xvxg9qmnRlbHUDoaiwSc56daAKs8JQ4xx60sOVXaMFWIzWgqrNGVxWe0ZtJgp4B6GgDcjv96/Z49wBxux6Cti2kaMK6xOFfjmuZt5lUbCu5v71a9vdzKgQfc9M0AbU1ws3kqOJOc1aXcEBJ+btWKrlW3nIJ7n0rSS4V9hTkigC0hYsRnjPNTON2NtLEij5gPvdRTkUBjzigB8Y9am6VGASfapGBCkigBGz2puDTwwoOaAI9vemseRUhOBUZ2dSTQBDKcvVaVsCp5nUZKjcaozLJIMlwg+tACh8cu2z2pDfQ7toLk+wqu89pbrmRgx+uaqtrcCf6uMkdsUAasdyy5ItnepFvm/jgZRWCfEUxyI4CPc1E+r3kq5PFAHQyarAvyM+z2NEd9BtyG49RXKSX7lv3wz9KaL/AGnMfyj3oA6a8vIWj/dt83fNc1uE1/5jHpxTnvDJGTjcfaqQYpIJOAOuDQBbvbkPII8dKph1kYls4pslxvlLbeaiEmRg/KKAJJJMDCk4qrtZ261JITt6ZFNjZSw/hNAE3lBEwetOt1VVZz96kEgk+9yaRuAaAL9vqzW0Zwuc8D2qvcalNcN8+CKos2BgVFnBoAnfaynFCSNCc9qgRzk1KD5gAPOKAJAfMfea0rONZMs33ccVmRA8qBkHitWArb2/znPoKAJo5ZdjRt9wdzUM1zFCmGcsarXF67jao2g+lUwGZupB9aAJHuHmb5RtXtSCGR+akWMKMkDd61ZgXKgZ5oApixmkb5UxT2gu7P5thIrcisxtzu5pXjkCbcqV96AIdG1LzXEbnn0roTwOK4yUNaanE6jCk84rqo7qObYFkXJ7UAXN4AzjrTcDaDSOxTjGRTC5kbAGKAJCNy8HGKru5zjGamwygg1GBk0AKGxxUqdMVEE+apfucnmgCVQM5pOWk+amhs80hfmgDjvGZzqsf/XKubrd8WOX1dcnICVhCgBR1paQdaWgDQXB0u1/6+x/WvtXQf8AkXtM/wCvSL/0AV8UQq39m22cc3a4H519r6DkeHdMB6i1i/8AQBQB5V8VFcX2uSK42ro43A9878V45p/iHR4LNY54Z/ldDzggAHkdO9eu/FmeePVdSt44N8U2knzHH8ON2M/ma+dc8Y4IoA+ofA/jXw94tvpdL0y0a0CQqQgTBJ55zivKvGvxE1mDU9R0WKWFlikMRuwn7yROwJ6dz2rz3TdWvtHuvtWn3MltPgrvT0qtLJJK7PI5dnOWLdSfWgDat9Vv9QZvtl5PMQP42yPyqfFZWmnDvWmDkUAOxRSUHpQAh5pR0pmaTdQBJnFMJppamFqAJM0opimnigCRRTqQUZFADqaxozTTQAxuaKDRQA4U6minUAGM04cUgozQAvFOHSmZpQaAHUw0pPvTCaACnA0zNKtAEopSOKRadQA3FNYYqRsCo2PFAETGgDmk6mpFFAEi1IBTVFSAUAKFpwFLjiigAooooAM4paaaTNACsaTNJnNKuDxQA5RzUoOAaiY7VPr2qK4l8m23Nwcd6AI7m7SJT83PpWHPO0z5zTbiYzPnmo88UALg+tNzSgFu1SLGOpoAapz2qwi5A4poZV6KTTgztyFIFAEwU1OqnbnIFVow5PXNWUhkYd8UASIgYZL04wxt/FTVgKnGeKmWLB9aAIWt1C8NTY7cE4q4AeMLUwU44AzQBS+zMpwBUn2bcuCDmroU45pA209M0AZ508jmoXtivbNbP2lOjAUxp4D6UAYyRFOVBFTrcuBsKnirTSxg/dqB5ogxOBQBJEVkyGHUVDNZeWdyHOaje6TjBAxU0V8CMNigCmwGdrD8aaIUU5BFXJDHIc8VXkjAzg0AWIplUBcVLJIrRkZAAHSskFlY0jux6NQBYScRyjPetdNskI5BBFYHlllJPBFLa6g9o+2QEqelAGvsKNjGQe9STWyyxepFLFKtxFlSCOtCyeUfY0AZoUxP6r/KrYwycj6VLJCp+cDIPUVXH7tsMeOxoAFIJKufoarSqYm55U96nnXCg+p61GXGzZIQR2NAEbPhVKHFSsqXcWGHzAcVWK7T147Glil8uTJoAiRdhMcmcjoasWlwyyFSRx0Jp1xEJl80D5vSqpQ7QR94dRQB1ttdrLACxBwOafbuRNwuA3Q1i6dMvQ8DuK196jDrkhT1HSgDdt5MoOfmHWrKL5mT0rOhlY4IxyOgrSgb5KAJB8q0bty4zUTPnODkCnrgrgDmgBTx2o3e1MZmT7x4qFpTjPb1oAfJMoOO9U7m4ZFPYU6WZFXOMt61SKvcMdxwPegCtLfzkFIlyT1NVvstzL952561pNBFCM/rSCVFB5oApppkSffOfanMLaL5RCOO5ptxOT8w4+lZ8twSSWb8DQBo+dFjhY/wFRSyRhcbVrHefc2E4PrmnqJXHJoAnmthIwCEDNUZoRExDSKMeoqw0bhxuYr9KilsfO5V93+9QBDHJyQGGPUd6dLPGZFUjjFMFp5RA5PrjoKb5Y3EnqOmaAHZQnAHXvSTWpVeCGHtSq7OSpA+oqaOGTGe1AFDy5AOuB6YoKKxHYitIWr7iSc57UfZlXO6Mg/SgDOWNmcjO3HrTsH7rdasS2ykg5OR61FOMEbRQBVkBDY61HgmrMikruH41EkJfnPFACRAcnv6VZgj25Zu/amhSCCgwo45p4O0AA5oAkiO0MFQkn2pCJWGGBNTxIrL8zufYCrKKij7hP1NAFBbfb80gNI7Z4jGF7dzWo7W2z56rjycfulBPvQBUiRi3CH3JNTiB0O/eAKJFJGN2Ce1NkAWIIeffNAFtJmCYDA/jSG4mAwchfbms/zXVsKf0qRbqdW29fwoAS8JkZGUtycc1sWjJGI2C8gVnpcpI2x1Bb0NWYlIbEbHPdaANxJmc8808PuOV4rLh3R/vY3yO6+lX451dR03UATs52/N+dNRgTxUDEgnnIp0SkfMvzD2oAnDHdUhXIyT+FRgc09uRwc0AIQVbGeMU1zgGkkf5h9KhkfigDjvETbtUB9ErGXpWxr/APyE+f7lZHSgBR1paQdaWgCSJmzAuTtEw496+4PD4x4d0z/r1i/9AFfEEQ5gP/Tda+4NB/5F7Tf+vWL/ANAFAHlvxR/4/wDW/wDsDf8AxdfNg6V9GfFeXZq2rx5IZ9GOB/33XzmM4560AFB6UUh6UAW9OP75vpWon3RWVYcT/wDATWso4FADqD0pCaYWPrQAGkpc8e9NzQAGk20pIpQRQAqinUgIpcigBxOKTNIaKAFzS5yKbSjpQAmKMc06igBcUoHFAp2KAEApuOakppoAKYTzTjTSKAEJzSUooxQAtKDTRTqAHhqcGqMUZxQA9jUTGlJNNPNADU5Jqwo4qJFwTU6igBy1KBTAKlXpQAoGKDS0hxQAlIRS0hoAb1pM4pelIeRQADmnrgdufWog2Pl70ks3lLg8k0ALLMsPzHn0FYd9dPM+c/L6VcuW2Rl2Oc9PasZ3Ltk0AOzSd6OlJmgCQPinh8iq+amjQkc0ATR81aQcHNQxqFFHm7T97PtQBbTaoqwjnGBWeu5sHoKma5WJfpQBeAfvinCRE+8wrFk1Bm+6SKhE8j/ec0AdAbuEDhgTUYvVzwaxlIByT1qcSRqKANb7Xmo3usHNZ32qMUx7hW6UAXXuAeTUDXQB4qk8pA61A0mTQBpfa89ajklDDg1R8yjdkUAOLMDnNKJyKiJNNoAuLO3rTjcNnrxVPIFPBOBQBaEgbrUkUYaq6x7gK0rS2yAc0ARFMcVGbYMfmGa0JbVhyBT7WNXG1h83rQBSS3ngIeNsr3WrZk81Mng+lXBbvFgjkHrTZLZHyyjae9AFeKbZ8rc0swWRSfyqCRfLbI5NOVywyaAEDAxFH69qqycjae1Tygscg8iq5cOcEc0AMVuSG6dqAuSc/hSsm5T7VCrnHJ5FAFqOTOFJwac8QySp7VV3ZOR1qVXI70AIrtBID2Nbltco1uY5DlXIwB2rEkYSR49KLaUx8Oc4oA6qGZrZ1zlo+g9q2TvAVhIArDOBXO2lyk8CxOeprXs5DFG0Eh5XlGPcUAaUTqfp3qZpNgyTx2xVWABoywXAPJp8lwgjBYADoTQBHc3SRxF3bag7nvWNJqz3LMlomQO5qpcyyanqHlR58lDjjoTVltPdfkaTaPagCITXm0+ZIq0Pe3CJtWRWA9qsNaxRpjAbPeojEgGFGcdc9qAK3227fjaDSedIfvjH0qTyndtsYHHcVTvr6O3QxRtulxzQAs02B96qrT5HUfU1RNxJNG3XIFRhZD8zMAMd6ALzzAAEsuPYVPDdxBflEjH6VTMYCIN2SeTikEZL9aANOS7Py/uwKge9Ck5Vc1HkKOWzUBbcxJ5UdqAJRe5bChRu4phRnBkODioHYxuOAc9PakdHcsA20dcUASiUj7oC0G6lH/LQVEIT3apEiT1oAab2dSPnzUyajchuWVhUbKf4RTVQAk45oAtG6STl+W9qrvLuJ+XFRSdwOOKaSVjAzz60APE43BSOD1pxjBkwn3etV+oz3qUuVUFeD60ASMpHzE4HpQjoxz0qB5GmOMnIpiuEOW5FAGkJFUcHFNMpJ4c4quky5znAq1DcIOqg0APUg9Bn60pmEYwU/EVZiMEo+ZlWpvssDJ8kyk0AZjvIwyq5FRiWXo68VpvGijaNufUVVdIyfmcUAU8Fjwm4U4QF2G0lPrUhAD5BzUm5SRkgUAUpbd1mAySfUdquQ3JhISdSydnHUUuFMmQV47U2S3WZtxfH+zQBrwBLiI4O5TyGWnlJVbCsARWZDK1qRsHHc1qRXSXEWMDHr6UAEZlL8sDjk+9Xd6lQR8vsKrhflG0dKkEgZNyDkcEUAW05WlQfMaiQsFCng+tTBGX+LNAEM3BJqru3HFTzk+tVgMHIoA5fxCMasP8AcFYx6mtnxIf+JquP7g/lWOfvGgAHWlpB1pT0NAFqNP8ARoX9Lla+2tAGPDumf9esX/oAr4kikIt4Y/WdTX234fz/AMI5pmf+fWL/ANAFAHjvxec/8JJcKAcHSZAePY18+19DfF1pB4iuFBGP7KkOfwNfPA6dc0ALR1oooAtacN1zj/ZNa4HyisrSsfax9DWsRg0AMYYqMmpH6VXY0ALu5zQW5plLQApOaA1JRQBIDThTBTx0oAf1pM0UtABS5pKMigBc0ZpuaUUASA1IOlQ5pwbigCQ0w0maQmgBTTSaCabmgABpe1MNLnigB4FLTAaeKAHAUhFOFIaAGmkAyaQmlWgCRRUi01akAoAcBTwaaKUNjtQA88img5pd2aRuOlAC00mkzRQAU1vumnZqJm5oAbuAXJ61Bu8yUluQKSeT5ioNQO/lwsScGgCpqE5llKDhRVMDAoZy7FjSUAKTmkHpRkUqjJoAfGvOTVgMBx0qANspfvUASPKc7QKkhi/ic0kUR+8RTZ59uVU9KAJZrkINoFUJJGdutNLknmgAntQA9VJ/iq3DCHxk1VCMOxqUGRQMZoAvm1XA5FRPAKiE0gHWmNK570AK8SjtULDHAOKcXc96QIzckGgCL5s+tLgn+GrCWzHtVhbNu4oAobfajy36gVrx2Of4TV2DSt4HFAHPpC5/hqQ2zDtXTro7D+A/lSvpLf3T+VAHLeQc4xU0dqxwMVvDSG3dDViPSyoHFAGVBZ8cir0EG3oK0o7Ep1FWRZ4HC0AUhFmPlaoTxlZAY+COtbTQuBjFVZYdhLEZzQBFBKXG1hg+9OmjKj7p5oiaMscEZ9KnaXYuJBkdqAMeeMIc4qruGSc/hWldKHGR0rNZVQkmgBuGJOO9RSRbckGpTcr0AqGWXd0NAEKSbgw6VXyalZCBvFVy3JoAlVgDzUx4HrVPOSOelTLKOhNAEwPpSnBHTBFRA7W9qlcZAZeR3xQBatJcOvO0ZrpY7gTxgH5TFgg+orkEk5x2Heugsp1kCMRxjBoA6mKcG3DDhCM5rCvrmWffGh2p61JFIw3wFvkI3JSzW4jt0X+J2BoAnso1trRVVPnYdfen7MDk7nJ5HpUwX92TjGBxUSAjc5796AIJUPmBVOFqG7kChYIxl24wOppb248mM/zqGwXa32yXn5flz2oAh1C5Gn2ggT/Wt97npXOxoZPnlO45z71o6gTdTvcucAmqbMI13LzntQBIsfLKgBDLkGqnyNlQctnrV6yd1PKqM9MnoO4qeaySGTcVCq5yvvQBTl83cmxQMLjp1pwtmLbpCRU8sqxvtjGXxgg9qi8ydj8+MUAM2b3Yf3RmoXfGNqEjvV6RNkDyHjis2BypZzyO1AFiFBK4DjHpU81oqNkBjn0qKK5VlztAPvU/2iV1A6UAV2jB4U5buPSlysK5bGaWRgjEREbj19agYP8A3ef9qgCTzDOCEIBHTjrVb96h+Y8d6lVSCCTyPSpTHHccpkkdVHWgCncK4YN/Cw601W3DHWrgi86Bgh+52NV0Zs8JxQAxRyasQqsgK9MDqaa0gU/OgpHVmUNGc+ooARkA+71qOSEkZxxThLtfB61ZTc4OBwaAKCDD4IyKss2wDHFRSxNG+Ac1YiQMuHHNADonDg/Jn8cVMjKjYA2n65qDAjyjZ5p2xSf3ZzQBa89/4QGxTi6uBvjwTVeGNnbgn8KknSVJQARtx1NAClEH3RTNo3ZODTRIQ23HHrTmUL82ePWgBwQMxI+U9jSspdcM/wA3qKYr5OKk25BoAYjyxkh13Ke4qW2lCSFUO5T/AA96hSZojjadvvViN7eUsU+SX+9QBrbwFUo3bBpu4rJujOB3FQRh3UEcj+tOwQRmgC2JphguhK/3qtxy+cfkORiqaShlAzn2qxHJgqEABxzigBJcbcnrnpVdjxmieTDlT1pFO5KAOV8R/wDIUB/2BWS33jWt4iH/ABMx/uCslupoAB1paQdaU9KAJ4sbIh1Pnrg19t+H33eHNMOP+XWIf+OiviGEf6rn/lstfbnh0Y8OaaP+naP/ANBFAHkXxhfHiC4UffbSnVf/AB7NfPa8CvoP4uRPL4vBVG2ppcpY/ga+fe5+tABRRRQBc0v/AI/F/Gto4rF0zH21a2TigCN8Y4qqV61Yk7YqA0AIop4WkUVIKAGlelJtqQ0YoAYBTwOKKUdKADpRmkJpKAAmkp2BTT1oAUUtNFGaAHilzTc0ZoAdmjNNJpM0AOJpKKQfePpQApFJinEc0oFAABThRilxxQAtNJoJpKAExmnoOKQCpFHFAD1FSAUwU8UALQDRRQApNJmg0maACg9BRR1oAD92oHOKs4xVK6yW2KcE0AQY3bierdKzr2feSq9BxV2ecRZb+6MVkHkk+tACDOKU9KKKAEAqUYVQe9MFHJPtQAvLGrcMWRmoYk3MBVxv3Kde1AEU0/lptFUc7jmnytuamqOaAFC+2asRqMdKYvHarCE+lABspjAirSoTTxas/AFAGdg5pdprWTS3OOKm/shuPloAyoYDJ/DWlbaW8mOOK2LTR8KMrzW9ZaWV/hoAwItG2qMrVhNJLEYXiuqj08kcirUenADigDmrfRwf4a0INJ2n7tdFBp20VcSzIHSgDnksFHBWpP7OQj7tb7WfGcU1YMcEUAc62mIP4ajaxVRwtdI8HsKrvB2xQBhC0UjGKa1tt6VsNb+1V5I8A8UAZDwCqU8S8ritaRcdqoXHHagDnriEoxZODTYrkMpWQZ4rTnjEgPHSseZDGx4oAbOCVyjfhWXKzZOatSMR82eKgLJKOvzUAUnkx0pqOCDmlkiKt93ioDlXyOlAExc7cVWYcmrCMCuD1qF+GINAEYbFLnvSFcc0lAFlH3LU0bfKVqnG2DirC8uCOKAHAFflFX9OuDynvVAuMH19adFJ5cqkcAnmgDq7hjGtpIP72G+lah2zOD2XpWUGE2nbuoyMGpreeQxiNeSerelAFm6uxnAzjpgU15X8licAY6U2aSG0CgkO5qgszy+bI5+UAmgCvOxuLlIwGOTitLUT9m00R4w7jAA9Kzrbc0kUnYnirWqNumROqonGfWgDGuXdVRFGRjpUcUQkIcjnoBUsy8xsDyBWgIBa24lkOCwyF9aAKnlrGMyHBHpS/wBoROgibLLuwD6Gqdw7SEvjYv8AOs95CHVlGBQBqX+Fc8AEDBK9x61BaMXmUMxJJ4qS3nNwmxj+8A4B7j0qTT4Cl0pI+VzxQBo6nABZRwDhmBP1rCjtnJweO1dPrK5+zEdh1rLwGk5+XHegCCK2MWBgH605l2PwQB3zVohVUtncQOtZkxMpyT0oAkJCklRketQNcKeOp9afITtBQfKetVGxuG0ZHegB25t2d3FTW+6GTzEOahjgdu5I9auIqxrjODQBbMcccsU6/dk4YDt61UvrdrOVkxwxypq7aHeGt3HBGVNWbuH7VpySBMtE2xvb0oA51l+XdITk8CnW8rQHjDD3qa6TyQAec96pRJvb5WP40Aa6WsV6m7hZKRbKaBiHGQO4qvFmPocVbW8kiXJ5HvQBBOUIwR9KjinER/erkdsVekkimjBaEfhVRYImY7HKf79AFzEM6jjnFVhbkOUAxnv6VPDCI+BICfU1ZAU/LMMn+8KAKHzRnA5I43Cnx3qods48wHjjtV17eJjgtnA4Jqk0H3vukUATyWsE0ZeF/lqoCEQoQSvbNVxPLbOSM7AelX1nivYiUwshH3aAKcuVI2/XNEdz2NOx5ZKS/e9KjaAD5gaAJd4daYvyNww57VEuA3LECpnVSAUfJoAtQTeW4DkgVoG6ClMAGsJBJuy4GB0NWI59x5PIPFAG0sgdAUXB71IshVcr1qCEsVUgrz1qUfKGoArSy5bJ61JDJxVdwWcmpoojjigDn/EJzqi/7g/lWQ33jWv4hXbqi5/uD+VZDfeNAAOtKelIOtKelAE8AXy4vXz1r7b8P/8AIu6b/wBesX/oIr4gh6RevnLX3B4fH/FOaZ/16Rf+gCgDzL4kYPiHVNzfd0SVh+TV876fo2o6m6pZWcspZ9i4U8k9K+jfHg/4q+/yuR/Ycp56dGrxXTviJq2iqYdPFuoYqV3pkqQT0oAo33gPxNp3lC50i4VpSQihCScdcCufmikt5GimQxyqcMjjBH1FeoeEfiF4r1TxZa3V7JNeWttkzKkW4Ip69PpWP4is7jx98SryLw9Z58xgMbcAf7THt/8AWoA5PRbee5vgsEMkrAFiEUscev0rUIPevabT/hGfg7ogtpAl3r1wm51OCSfT2WvGppTPPJKUCF2LbR0GTQBCy1CU5qwajI5oAYFxThS4ooAD0puaWkNAATRuppNJmgBxOacOlR5pc8UAPoxTVNKTQAU3NKKQ0AKDQTTSaQnigBc0oqPPNOBxQBIDmlpgzS55oAlyCBSiowacDQBIOaUjFC0poAi60lSEU0igByCpAaYtSAUAOHNOFIBS0ALRRSZ5oAQmkzTjScUAAOaeBTMgU8HigBTyMVnu2bgy9AOMVekOFJrIu38uA54YnpQBnXUhlmkx930qEHIozwT3JooAKKKD0oAU05eByKYvNSovmOFFAFqCM7DIR9KjnkJHJ/CrLvtRYwOlVREXkNAECxOxHHWraWb8cVct7QsV4rdttOzjIoAxItPYgEj9Kvw6YTjj9K3odO5xitW300AA4oA5yDSMnG39K1LbRQOdv6V0MFgMZxV+C26rt/SgDnY9KX+7j8Kl/s5Bj5eldJ9kAAo+yLjpQBjQWSkj5cVqRW4C8LViOEDoKsLEccUAV1h4xipUh5FTKpHWpVA9KAFVAFp68DFLjC00dKAFYnFMxgHinkgdaYz0AQOahI3GpZCGqAZ3EUARuOcVE6DHIqZ+tQysMdRQBSmhBrMu4PStdjmq8qAg5FAHPGMgnjrWdcwkE5Wt+eMg8Cs+4jJzmgDmbmPk46VmSgqcpnNb13Dt3YGaxJSUJJB5oAjWbeuxhzQ8YCcjFQsf4h1NR+bJyHOaAI3OH4pxxIvoaQjIzTckdKAAnHy0xlxzmpCMrnvTBz1oAQcHNT5JVCDioH+XrxU0J+X5uPSgCcAMhX+Ic/WmZB/DimhyDkdaVuCHA4PWgDf06VpLAQ78BevvV/7RtQRwffI5rnLOYx7hnitOCXcf9rsRQBZdw3yg5lHU+lMjl2yFEPDggg9zTWZYY2kx97jn1qlEx8+NgeS39aANC2H+nxxk4A6Cn3UpluZW7LxTbZf+J383pwPekuR+4uHAxufn2xQAzToVuZyzD5E5appm+1zsz/6uPhant4Tb6cjL9645/Cq94QlmY04PVjQBi3bs83lj7o6Cq+3cMDoOc1ZZQ6qgIEnXPtTJAFjAUck4oAbGMzrIvG0cVpxylpVKkZyM1RgjKxMxHtU1uGVsHPPSgDotZTdZ2zL1AyfesLcZHOB8nUV0iRfbNDjZc74sqc965+SN48DaQd2CMUAOKERZwQCM1SdfMdYx8ueprWIO1gegFZDyAzk9jxQAkn7tdueBwB60QWvmA/wZ6g1LBH85kflR0Bq/GiudqA5HU0AVWRUwinFSfZAo3MC36VdEcaLygY9zTZfKYkLI2fQigChGXSQKSAQeD7V0GnBXtr5eDvUEfUYrAlj2vkgH8a2NHYE+gORj8KAMPU0QyquOMZqmsap/9ar2sL5d+g7EVEsG47j931oAjQ4bDdPWmcSsTnC5/KpigkUg/LjpnvUIiEbjJ4HWgCYKYzjk8VDK5ADBs89KuRkeZhiORxVZ7f5uvGaAJopg6nOM/lVlWeOMM/MfqO1ZQUxk4ORmtCC7ERWOQDn7oPSgC1HLzngg9KmWOOVScDNM2Rlf3QBJ9fWovNeMn5BtH3sUAQXEGQQwHNUjCYpdyAjHpWujw3C4z+fWqtxZskmY8kenegCuZBcJuZDvHWhOnKnHaoHDo2VJU5qxby7/AL7YcevSgBjxl8ggbh2qoVZHzk49K1XYCMFVw56sKqSIdwcg4bpkUARxvnkZOOoqWRcRbkIqrtmikLpnIPIIqVJFkbcQUk9B0oA0bC8Z8REAN71pc7TnrWAxDSK8Z2Oh6HvW3HIXRWPcUAATJxV+CLAGagiTLVoIuFFAHGeK1xq6D/YFYb8ORW94t/5DCf7g/pWC33z9aAAUvakFFAEsQ+aH/rstfcOg/wDIu6YP+nSL/wBAFfENuMxofSZa+3tB/wCRd0z/AK9Iv/QBQB5z47OfFd8P+oJN/I18wkfO4A56fjX014+Kr4yuP3hGdHmBHrwa83+GvhTSTKfEmu3dutlbkukbuOGHQsKAOz8E6QdB8L6XBeaZLKbjddTrCQrIoAwXBBJ6HA4rmNQ+Juj+HNcvrvwlZAG7gCSs6lSsgJ5wfrXN+LviBNe397b6JcXFvp86COQM4Ynr0IHA5rhDkY5yT1NAGouo3Wqa0bu+neaaTO53OfwFXxwOuaxtNH+mofTpWySc0AIxxUefmqQ81EetAElNPWkBpCeaAHUw0uaac0AFNPWlNJQAopaQUtACinUylzQAppD0ozmm5oAMUhFOzRgZoAaF/WlC7SM80/tSDAoAdSYpcigGgAAp4pDSigB6mnnpUYxTs0ABpKM0UAPAp4pgJpwNADxS00GlzQA6k68UmaUGgAPpTO9P60mBQA3+KpMjHvTcClxxmgCOaTZC5PpxXP3srSSc9MVr6hMFG2sKWQyNQAyiiigAo68Uhp64oAb04q9YR7S0jdKoHLMMetaygJbKoGOOaAGH55Se1Wra23vUUMZLADpW3Y2/zDigCazssFeK3YoQmBUUUBQAjirsaFiM80AW7e2DYNasVsABVW2GABitSFSwFACpCBUyrtNII3z1pzI4oAk2bhmmMMVLDvwRjNMlRs88UACECpxjbkVXRB3apwAOKAI1YlzTsmkHDGpB0oAXLlaTcVXpzTg5oMmKAIGd2PI6VG8hBFTu4Iqq5yaABpR6Uitk5pSu5eBSBcDnrQA2TmqMx5qzIxzwarMMnmgCIUx+SRU5UDtULD5jQBXaMEHNZtzDycVssm4VRnAGRigDm7hRkjFc9fQkMTjiuruIjvPNY19b7hyaAOdIGMCoHWrU8XlPxVdj60AVzmkDMO1SnHpRkYxQA1ZB6UpAIzTWj3d8Uqpt6tQA3Z5nXtSF8HA6Cp0wVYjiqh+8aALAcYAx161Ip8s4Jyp6VWQ9RT1bjFAFpG21cilKqrL64rORie9XIGBUL6c/jQBfvW3wRhTwDz9cVWtkLMpz0NIZd1lsPLCQkn8KI8wBeeSaANzTJUubtJ9vzIrKw9+aa8ZuIvKU/wCsl2mmeHRtu5wfcrVnRx5lzKjcskxbPpQBNdqIHjizxEmBWNfSkSBQeMZatXUTm4lcjIU1zNxcb7pmXlT1FAElsrzXSrwTnr6ClmTEpOMbD0osBglj95+h9KlmT9zu7s20+/NACqMW7e4otgzEFu3FTPEUCLj/ADipIE2pkj3oA2tHckT22eGTK/XrVCZWS6cyNx2p+n3AivY27E8VZv4fMkIztCNkn1HWgCCf5bNirDLYFYD7hM65X2roWTzbXOfl5wawpFUTE9wfzoAkthv4PatBZEhU5HX0qlaoRuctj0FPkkROfvZ6GgBWuJMbVwAT1NPglZ2eM7SQM5qGON5ZMNyKuJEsDDjqOaAKZO7dn1q7pRCXKDJwWqoEJLj34q3poJuI1PRWoAr6p8t/h0BU5A/OoAh27RyCa0dQh35mHLKxA/OqS5TaUOST8w9KAKojJMhPRelQKC6sTWgUbac8bmOR7VWCZ4HAJoAf8nmgcD5f6UiheChzz3qu2GmZuuOKfEfnIPSgCRwjEmNef4s1F5e9SeoXuetWdh8lh1B701Y84IGT2oALGZ5SY5TtI+7ircLq5KzkKQcH3rMkQ28vmKTsY8+xqzIBcRfaF5ZRhgKALTxbJfu4U9CKiZ2hcoxJVujUltf7oxHIeewNWW8uSMqfvjtQBWlhjuoiq/KR3qiITB8rc4PWrgXex2Phx29ahZwzeXKOG4BHY0AOVfNiO04PUCpxC0unNIISVR8O3p9KggXZMiE8DjdU63csIltSxMZycUAU9pkiyvqaiEJU5NSKXVty8A9vWpWcMBvXafUUAJHGG5x+dW7dmEm052VRBPm8GtSFCQCaANKDH4VfXG2sqFyrY7VpRtlRQBxni3/kMJ/uCsF/vn61veLf+QxH/uVgt940AJRSijAoAsWxwij1mWvt3w+c+HNM/wCvWL/0AV8OwyHzol7eYv8AOvuHw/j/AIRzTMf8+sX/AKAKAPNPiDgeM5jjk6PN/I181PLIA6h22EnI3HH5V9LfEAZ8ZH30if8Aka+dtKsY7y+Zpyq28JMkpJxkZ6fWgCXS9B1LWInns7YvDGwV5CPlBrdvfh9fR6Hc6zBdW9xFbNsl8o8A4zgc1R1LxfdTwTafYYstPkO4xRjrjuTXXaHd3Xhb4R6jfTRkf2rKYohIpYMoHJ9uvWgDzfTc/bVwM8VsH3rG07LXquxweTxWuxPfrQApNRE80paoyeaAHFqTdTSabmgCTOacOlRA08GgBSM00jFPppoAQGlzTaM0AOzSUlLmgAzikoooAWnDpTc0o6UAKTTS4FO47igqvpQABs0uaTGKO9ADwacDTBThQA8UtNFGeaAHClpBS0AKDShqbSZ5oAlDUuajzSg0ASUopmaeDQA4CijNLmgBMU8LlaAKc3EZ9cGgDCvZAWYkZzxWSPvGrV1JukIz3qr0NAC0UUUAKKKBSGgB9uu+YLWkfmbaO1ULQfvcir0eTJQBfs49zLXRWUQDgVk6bFnHFdAiBACOtAF1seWAKsWC735quOYqt6ZGxbNAG3BEoxkdavRhR07VFBASmSelWIYvnJzxQA5WDNUjqdvBFP2YGQBUbM2MFTQBFHMySEVJM29eaqtvD/dI/Cp1V3XBU8+1AEW9FPWpVlXb1qtJalZcE4qzFbL65oAjMwDHipo5lYUPAnpSRooOKAJQ6+lDAMMjvS4X0pA+CRjigCGRcDrVct82MVPKxOeKS3j3cmgBCdq9Ki8wEcirDrzio2RQOlAFGRsN0zURbnpV9olK5xVKVMNwKAGORUWOal2ZppXFADSPl61RnXnJq7k1SuzhhQBnzQj73rWdd2YK8c1scOSDxVW5Ur34oA46/tirE7elZEhUdq6rUF3BuM1y92gB60AQsBjNR7h6YqMuVOKC2RQA8+xzTWBYjtUfIPWly3rQBPuCRketVh93mlLEnFBoAQdacpxTR1paAJ42wcYq4h+QMvXNZynjNWreXgZ7GgC2qbw6g4z39KjkkPmqD6gY9KFl2uR2Jzmn31v5bpOp4cflQBt6M4XUcdscmrmigx69fKw+RgWBrK0aTN3G/Y/K1a2jkyazeKSDtQjFAFTXJmig8wdJJNv4Vzij9+wU8dTW94obyILVGGQ3OPQ5rGMYWTKnlscUAT2reVHK7c4ACir7IDbW46ktzVGMB5ZIh0AH860Y8ZQk8A0AS3e1ZY16YHNA/wCPYsF9ajZxNMC3rgVNMCqCNe6mgCrA4DQ+vJzW5ORPp4lXkk4asKNzHOsZ2jCfrWrpchKyQP8AxqWUUAPCqLJU7bTxWM8BWR3YdBkVr4yoH90VFcgG0Vwvfn6UAZJ2iIAvgE806KPziTj5R0pPKSUAZHJz9KsRKQ7InQdMUAW4I8ALxuPSmnibDHOOKfGjIC7fwjOKjcF4PtB4KZwPWgBilVdiR907SP61NCnl3SbTwec1CyllFzjlgA6j09asQY3CJz8pGY2FAFlgyeeMZONwH41mCHLb05JbkVsxAuy8jIG0/Ssm532s7lASFOKAGXOROOMccj8KrOqpb7m4bsK1WhilQSnIbAOKz7gx3ExjUHC0AUII1djzjJzVg2x3NzjIqUIig7VIIFWoExEXk/CgClCxJMWRhRzTbWUJP5bjCsSPpSKhS8b3O6m3K4uDIn3uOKALs0KyJtwPTNVYlNtJsAyvapPP2yAt3FK7gsGHPpQBXmiWOcErtbt71ZG/G8DkfeplwhmQEn94ORUljIZFKNwehz3oAjllCusiADPBpspgZjtJH+NSXsAWNtrYHpVCMqUZCxLCgCxCBG/7w8Z4qa7j+5MnIPFUXc8Fjx61oQFXjCHPPSgDMWUF2jJ5HQ0CR4iQ3P1ps8ZhuWzgYbNPmw8QlA+tAEsI3EOBmtmDBxjkVi2bfOTn5fStq2U4HBxQBaVAWGK0IlAWqsa1aiOaAOL8WD/ibx/7lYDfeNdB4u41aP8A651z56mgAHWlpB1paAFiXE8P/XRf519w+Hj/AMU3pf8A16Rf+gCvi+0SI6YWKAyLcIAfQYr7Q8PjHhvS/wDr0i/9AFAHm/xBRm8XuR8oGj3GG/4DXFaDP8PdB8ERDVJoLy8vjunQKWZMdsA8V2/xA8tfGALEljpNwB/3zXzI4Adgeu489hk0Aeq3N/8ACNzJJDY3wMaghACBIfTkcVyur6zd+LntLC0QW9jbrsjizxnPU+nb8qw7jRNUttLj1KewnjsZmwkxQgE+xr0/4S+FbDXPD+tTXQcSgFCy9QCO3vQB5hbWstjqYimQh0+8KvEgjI6Vs+M7C003x79ksnZ0SNd+/secislx8x780AQmmGpSKYRQAzFJin4oxQA0CnClAFIfagB1NPWgE0UAGM0hGKUUhoATOaWm0ZoAdQelNzRmgBRUgqMU8UAPFKelIKWgBtJ3pSKMUAPSnUxeKdmgANIKXNNzQBJRmmA0uaAHE0gPFBwaBQAuacKbingUAKKcDSUooAfmgHmkpRQBKtNupPLhLe1OU4qnqkmLNue9AHOyHMjH1NNoJyxNFABSd6WjFABQelFIehoAtWa4yav243PVSABYQRWhYpuegDoNNj2gGtNjg1StRsjFSvMT0oA17UB05rW09VVsVhWDu/Fb1iuxzu70AbyMBDUkbKq1WQfIOeKtIileaAJRIOwodhwSKVdqDOKduVlyRQBVmYE9OlRx3ODirDhGXkVGbRCoKgCgCK7lXcppLefJxS3FsMDLZpsUSIRigCWSUgjAqNJGLnipHcBsbRTkYZyFoAYZmHUUCbI6VK+D1AqPcqD7ooAindimAOTS26yomWpvm+Y+MdKsPKQg9qAK8srjtUaMG65BqYzbuq5pFUE5xigCF5lTjJqvvDE1fktY5AM4FQGzRc7WFAFamMKleNl9Ki55zQBWmyBwaoyIztnNXLg8YqrgnOKAIWUBqrXiFl4qywweetQTvhCPSgDm74ldwrnLpQ2Sa6a+ZeciueuwvYUAZEg+am1LKBuqM0ANNFFFABRmijigBcUUmaKAAGpkNQ09DQBZ3ZWrkT74gshyvas9G5I7Gp4DhghPGeKANG0UQ3IYNgBhx+NdRoUIGq3spHJT865CRgJBIB0I3f412OgyFmuJcZygANAGV4stmuljeP7yDlfaucO7dGw5wMn8K6PVrsx6zKCPlChW+hArL1Gxe2iBhGY5ec+maAG22SryDGXxz6c1oSsFiYAYwKo20AjtirNjpmnXlySojizjjJ9aALOnsu8u3zcHFXpl3puUYYLmqmlxDylOPmwSfzrTj2nBI7YNAGKrBrr5hzj+lWIZGi1CFicDgD6HrUM6iG/Uk8E1NdjZcRSY+THFAGkE2zyAnjLAU5NkllJF3CmhV8xI5F79aoC5FvelD3bpQBmfMqlehzVy1Yw4B5dv0pb228u53sfkflakt7b99uLZUdTQBadSIwCevU1BL80LIDhRUk8m0KCuQThaGgdwExkHkle1ACMAvkovTGW/HtSuvkuG6xrz9KsmBIkXzOGHI96hvJEhcb8bSOVoAtW8qyhZlIG3qPUUapbblF3bjcsgwV9DWKsrIxktzhT/AA+ldBomoxXsZhm+VsHCn+dAGbBJuiIP3wNpqsyor4Pyn1rWn037JOzjPlvyDWNcqBdAZ/CgAdwv7sdx1q0nyW2HqnEhe4GRnnirN3KMlB0AB+lAFcyLJdLhcZ4qC4ASdh71IisbleOhzUcq77xwRxmgBzqCi8ZytVNzHaQcBTWlt2xE46DiqDADdx70ASRXALsGHParUSLvMiH8KyjKFlBP4GrccxhxxkP+lAGhIBcQEY5FYzRtHdD0PNblqyFigOcrnNZupqbeRWx7GgCGaAqWH8J6VbthmMN/dxUcrtJbpIBlV4IqezAkBC8LjJFAFK/AE+4jOajt2VSYnGVbpU94CWPoRiqaZzt/jHINAFm3t2huCr9D0rctckVT2+fbxTqMsvDCrsHCgA0AXowNlTxjrVeNgBU6N6UAcd4u/wCQrF/1zrnX+8a6PxkNuqwY/wCedc4/3jQADrS0g60p6UAXbJ/9FZPWZDX2voAx4d0welrF/wCgCviK1JGwA/8ALRf5ivt3Qf8AkXdN/wCvWL/0AUAea/EFwfGsUZIy+l3AXjqcV4x4C0bT7/VbrUNaA/sjT1Ms+T9884X9DXr/AMSwP+E3sVWURSvp86Ic9SR0rwm91Ka20g6JHGqgyGSVuQX9Afy/WgD1TRvHGl+OLDUfCWpQxWdrKCNPcfKF/E9xxXocdv4e+F3hlSJ0hhQbmDEb7hsc/X8K+TVJRldcrg/KwNT3Op318qC6uZZgmdokYkCgDVk1OTWvFVzqEpzJcSM4B/u1MQTyayNJ/wCQgrKeDxk9q2W46nNAELCmEVIxphoAaTiikNA6UALSGloNADetHSg8U3NADs0hpAaWgBMUnfFOppxmgAopM0uaAFFOFNBpwoAeDilzSUUAOFLQKKAENFBpuaAFJpM0hNNzQA8GlzxTM0oNADwaeKjBqQUAPWnUwU6gB1KKSlFADqcBSClzxQA7tms3V2xZgeprQY/dxWbrC5iwOgNAGLjAopMYJpaACiiigAooooAvR8QgVraQu5qx0P7sVu6KuaAOgWPEY5qI/Kcdatn7gxVcrufA60Aael5yK15N0ZV88VmWELqma0SxaEo350AakE/mRgg1cikYjrXLw3TwN5Zzj1rZgujsBNAGurjGC1PVgwIBqgk6v14q3E64wOtAA4YAimrI+0rQWYOd3SnEgUAVp3bgEGnoOBxzUN3OqsOlSwyBlHrQAkrPuzTo5Tj0pHkAPNRs3pQA5rg5wajknOMVC6szdDUEkUpfAzigDQtiCSTU0kinpVKON1j70+NCT83H1oAnVx6VJyRwtNXy1Pal83DYHSgAMbnqajMDE/eqcygjG8D8aidyvRhQBG9sP4s1BJGseccipXuSPvCoGnEmcUAV2RTkkVVIUNU8jnkYNZ0kjAnrQAk+N/FQSFGXB608NvGTTZIxtDHrQBhajEoB71y96h7dq7O+QMpwK5u7gyp4oA5uU/N0qImrVzHtPSqh60AFFFFABSYpaKAEApaO2aTI9aAFpV4NJnikzzQBIDhgamLcgiq+asQjeDQBdiPmrknrwRXW+GpcQSg/wr0ri7d9jgZ711vhuQebMOu5DQBQ1wrLrD4P8IyPwqTT7galaTWUjBXAzH7VU1MbtYuRuClgAufoKqaeWtrrzs/MhOfegB8gaMvFyGU4JNQSzMXHGOQK0L51muEuAPlcZYe9ZmC86jsWoA6jTEUWxPdUJNW7aPdbsO/XNQ6fCRayH2xVy3IW1JHvQBh6ukaTxHPWrOoqp0+OUDAXbUWtI/2ON1AO1sfn/wDrq1JGLjRdgOSAOntQBLp+TEU69CKxtXjMeoqwPHUn0rX03Kqoz/DzUF3Gr3vlSLwRkMelAEUDC+hMJ5kQbk96ltYnjbAwVUZfJqlYxMl87o21UOCTV24R75BJB8nOH9x60AWEWGVy3EhH3fQVcjJjUqBtJHGO9VbOMQxAjaEXjOetRXF2zyMi8HHyn+7QAy7uZYmDnDP39BWfqGZkikLZ3ZzTJJGdxHuJHc+tTiFTbsmfuHcAetAGd/q8fMcdhV60k2zrLvAPbA7d6r+Rv2HHB71aEKpHnGP7uaAOrtrtbhFhmwUcfuz/AErF1ez8i7DOh571SsbwozRM2Uz8p/umuhjmS/g+y3JUyEfK/rQBgwRld8n90ZFRMm+Ns8FjnNXJYns2aGT1xn2qjPLtRh2oAS1ZnvCMcAZzUKfPdMT3Y1dtwEt2k/vLtBqtbR77nb2XvQBZmUeSRmslwSshHYVq3ZwCmeBzmqCbTFKTjFAFQR+bCrAc+tP2gxgBvmBwaZGzJAcdAelWYNjliBwe/vQBbtGK8D+Hv607UlDxnePvYIPpVOHzI5yueOprUuCs9kTjJA4xQBl2suC0TDKtT7UtFfbf4TVJW8t1I5rQU8xyAfWgB+ox8MQOgrLXlvMzggdK2r3/AFI3DBPrWM64cgUAbWn/ACx4HRhVxU2nAqlYfcStDFAD04q3EM1UT71aNuucUAcX42XGqwe8dc033jXVeOVA1a3/AOudcq33j9aAAdaWkFLkUASQOFkjQ9TIv86+4NAP/FO6Z/16xf8AoAr4ls9giZSuWMi7W9ORX2z4f48OaYOf+PWL/wBAFAHm3jyGKX4hWZdAzRadO67vUDg187a5dC81i4mjRVVmIAU96+kvGsiRePrZ3j3D+y7kAD+L5a8BtfsemuJZLf7VqUshEUB+7Hzx9T7UAZGmaNfavfw6faW7tcTHC9gK9C0q+8EeCHRb7T5NWvzxPlvliI9OOa6bV7q48P8AgaJLu0tLDVZd2GgIHl7gMA5Jrxax0u91vU0tbOOS4uJm6KCT7n6UAe5eP/DGg33hSx8X6NAtspC7kUYDof69a8kY56dK9b+Jupw6J4d0bwlayh5VQNPGP4VGMfrn8q8jc8kigCMjFNNO60hFAEZoB4oNJQA7OaKaDSFqAFNNJozTTQAA0pPFMpC1ADt1Jmm5pN1AD80ZqPdRu96AJQaeDUIanhqAJgad2qMGpFoAUGlJpMCg0ANJpCeKDSHpQAdaMU3J9KcCT2oAAKeBxQBnrxS4xzmgAAqQdKYPm6U4ZBxQA7uKe3QUKM9acRQAgpwpMU4CgB60GgUjUAGapaiN1u1XF96iuIvNjZB35oA5nuRS06VNkzDGMcU2gAooooAKB1FIacvUUAW1GAK6HReASa54HgVv6Sf3NAHRb1OAKntIFeXJ9azPNWPvzirlleomSTkigDpYUSOPFSqsbDFYo1VSmTUY1qNW+9QBtzWSumV6ikjQxIA5rNbX4gg+aqF14jjYEbs0AdO8wVAQ1SQ3vIG7muG/4SPPBJxVm31oPIpBxQB2r3TeZ1zVhbkbeTWDb3YmwfarYZicUAS6iS6hl7VFa3TYBJq7HBvgIPJxWOwMMxRhxQBpTTZUEHNQpfDOCapGfajc1i3OoiNzhqAO2huYGHzMM1YWWEDORXmR8QGInDE0n/CUSOvDEGgD0l76JAeVqnNqShfvCvPv7blc/M5wfekfVWYYDH86AO0l1VRyGqm+v7WI381yZv5CuM1A0245Y80AdimsM7gl+K2be5E6j95XnccsjD5MnFaVnqM0GA4OKAO+2IV+Zs1TnhGSUfbWNFrSsMZqvd6k+SytxQBqNOVJVjn3qCbJTIrITWEchZSB6Voi5V4wAcg0ANQ8H61NIMxikjjBqSddka4oAzriPMZrFuLfKmujeIsnPSs24hAJ9KAOMvrfaxrIZMGuvvrMSBnFczcRFS3H0oApYopdpA560lABRRSGgBepxW/p9rBcWe5oxxxxWJGvB9TW1pF4tijRyfdbsaAKWpaa1riRBlD+lZ2McV1zmO7gcDDDHArlZ4jDOUIxz0oAjHWrlnyW+lUx3qaCQo3BxQBKw2PW/wCG59uoKN3UYxWCx3YJ65qWwla3v0lUkYIoA2PEkPk6orDgsc/0rJJZEYZ+Zmrf8Sr9pFrdxLuXADVzlySJyB1HNAGhDKDp4BOSuRUcA+aI4yWPSo7YfuXXGOM1f0y2M17GQfu/rQB0sEXl2YcnBY5A/Cp5YxDY4kG3jj8eac6iSaKBBuXHNQapMZ7hbVDkJgsfpQBl6o2zTfpzU2ljzdIkbvjI/Kq2oOLm0nVFyFHH51NoLsdL5H8RVh6DpQBJYHfEGH0NTTBbiFkxiVOh9cVDYJtWaJDwHJFOklMd4qr94jOaAKNwfMjCKNh7461NaI8G47sqBl/pVgW32jdcwAM/8aetQXzC3tNifeYfMvegAnf7RJG0Ofs3XA7Gq102IfMBwzHHFLpHmLBIzDKHgrVkxlpcRqCijO3vQBStotp3sOG9e1WI4wlywJ3DFMKuzjPzMx7dqlVHSRmQZPfNAEkVoPNkb/lmg4FQXuXUIq496vtLtg38Jv8A51nTSPJsjjcFgct70AUhC4nXahyKvm7CTRoMgd29DSvELZCznl+lRwRsYWfGQTwaAN5Gh1K38mcDzR9xu5rntQtpVmFu42qTw3rUIuZbe6ZgST/D7VvWV7BqUHl3KjzV6NQBmyEQpHFnKgYqSC2MUALcNK2R7CrM+mMLpVPKdcjvRcMZZC4yioNqigDKvZSTJxgL8oqlGcoU9QTUl9KzOUDZCnJPrUCOPOOONo5oAiBIRl9qntfnzAx2gDOfWmtjcxA7VXWRvtStngCgDUBATPpwas2DebDJEeo4FV41EgcnksvA9TTrB9l5gnauOfrQBk3kaw3ToGPBq/pitOqxjJ5qPVbcLdlwfvf3qk0u6EEZ2gGToCO1AFnUZd2Mdnx+QqnGoaY59Klu2IiJI53bqbagPNn2oA07GP5MelXxg1WtBhc1aVRjpQA5VBYYrTtk+YVnxL84rVtlO6gDh/Hv/IXtv+uR/nXJt94/Wut+IAxqtsf+mX9a5I9TQAlFFFAFq0bBx/00X+Yr7d0H/kXtM/69Yv8A0AV8P233x/vr/MV9v6Bz4c0zP/PrF/6AKAPOvHO0fEC2ZmI26VcEf9815jqXg7wxc2Uus2XimCFoow7QOwMm/nOP0r0/x223xxb7gCZNLuVX2+WvmKQ7ZZME7d+SD9aAPbrHxx4P1VZ9F1xmubd1RRcyjBJHfPYis9PEng/wHpkzeG5jc6yz4E5QnKemeleZaZoVzqaP5RAVF3szDAA+tZ0sQhZgCxAOM9MUAazatea74lfUL2UvPOcsfT2FWmHJrI0n/kIx1st1NAEQFIRTzTDQBGwxTe1S4zTSKAI6aRTyMU00ANPFJnig0wmgBSaYTTWb3pm7nrQBJmkJpNw9aaT70ABNJuppNJmgCYNTwarBqeH96ALStUytVEP71PG/TmgC2DmkJpob3pSaAGsabv8AalPNMxnpQA7J9aVWINLHHmrCQjgGgBqncKXAHDDFWBbBehGacIwOHGfegCsY2AyvIoVfm61ZEEkfKncp7CgPg7WTA9aAGhcCgZParKQowyp5NJJbupyOlAEGKM47U5himhh60APB9qQmlBBFIcHoaAEBokB2qc4NKMUpwSM8igDF1KFSfMTt1FZgORmupmgilRgB1Fc7c25tpCMHFAEJ4opDzQOlAAaVetIaXtQBZHKg1u6dII7YknHFYSYMHXmphJIUChuKAL81+0suEJHNXIJHVQc5rOs7cSSYwfrXUW2mq8QoAzJrtwuAaoNdNnljmukfQ91QS+HX25UUAc/JdMVwGNVxM5bnJrXk0OQE5qH+zPLPzMPxoAogknrV2CZkUVNHYqe4qb7Go4B6UAaelX7+Yqlq7C1kLkZrzqNjbThvQ122lXYniUkjNAHU2rA/SqOq2Zceag6frVm1bjrVqTmPB5z2oA4m5Vwh5x7VzV7uDHIPNeg6jaRsCVGMVyWoQK2QMcUAcpKjZOBVYxMO/Na1xCQeCKgEYwSaAM0mVemaetxIcAjFSykkkKM/SqpD5PB4oAuxCWVvlJrTtdNmlI3cVzkc9x52IjWrFdanbYYjj3oA6eHTDHipZYIwMbTmsJdc1BVGYjg98VJ/bM8a5liOCPSgC8Yst8hxUn2ZnTByTWbb6zHJJjAU+hrobGVJYS2QT7UAZEulMWGM5q1bxT25AILAVsBvu/J+lTiNWbkAUAVreQt1UirRw+B19qbtAfAFX7ZYk+ZhzQBUeEhNxHHpWVdqgP1rYvJs5VCMGs+W3GNzcnFAGTJApQjtiuV1O3CSEjpmuykHzYFc/q0B2Z2mgDlpFyagZcVdlXB9qgK5oAr05VJqTy/apYAFb5hkelAEsFuWCkHrU9xaMI93pWjp9nvKtjj0rSurZVt2G3nFAHM2tw9vtOThjjFQark3Ksf4hmrFxEPOjVfUdKk8RwLC1vt/ujOKAMYDJNOjGWxmm/T0p0RxIM+hoAnU5GfXipD9xWHB6VHGDkDtmpgM5X0oA3rG4Nzp5jPO0Vm3Vu0twjnAGPzxTNOnNu3XgnGKmkkkVniYYKnIJ9DQBHB8wlbGQRgV0mmWwtrVJmHzMOKydMtZJgmMbQ+SK6iN1hXe6jC8KtAFp9tjZiTrMwyK56+uvsW7JzNLzVzUdSNu6s2GmZfkT0rnLh5ZpP3pDMT+IoA1LNWk02UsQST2+tXdIgKW0sanlmyB9DTbCMKPK7MB/KpNOm8u7ZOoLkEd/SgB9lEVuLhRxg5x9aqXSlBcSN96Pp9K1pUMWuumMB4x+gqLUbYM1wn/AD0iyPqKAM3T5WiIlQkDGam/tbTbti13EAy5HmJ/WoChg04YBB24NYKhIpyUXh+oY0AdbEmkta4guHVGOTuFMH2C3f8AdyljjlietUJrcyW0bR4QY5U1lXhJkES5BFAG/vgfcyOitn1oEtuZVhySSckg9BWFEGVR05Na0MQRXcYDbepoAmmmtpWaFVLbPu89aitmhM8jNHtCjis9Faa8EiuEVeOT1rS2xuvmcBWyPxoArT/6VMgzkZ4FXZ1MSiBAAirkk9qh06ESXIbshxTdQctLNGSR/CxFAGX5iXM52HAXqfWrQZrO33ovLetNsrXzZhFGmIk5LetaSWbajdlgpSCLrkdcUAWLS8fyIluD87j5aj1KGRY96ncvqKZehUQzngD7g9KzrbU5URpJOY8/dNAGfcNt4HTqx9TUER64HLdTWtNJZamCqFYmxnBOKpNYyW7bmO5WHBWgAbCxs2O1UgN0TMBg4q6wPlkY7VTdgsG0EY68UAaNhlooy/APy9aUtmdmz9w4GKpafMCdoJ9eatyrh5EB/hDZoAk1hd0MMpGRgZrJsMpdsFPHatS43TaUBnJFZdkhjk+brQBr3WGtWLEbgtVbKT5hjuKiupmI254NO01CW+lAHRWxxHnFXIzuFU4uFArRgQEUASQrk5rVtRk5qnEg21oWy4FAHA/ENMapaH1iP86449TXZ/EX/kJ2f/XI/wA64w9aAEooooAsW7Yccfxr/MV9vaAf+Kc0z/r0i/8AQBXxFaYC7SMtvXn8RX234eBHhvSwev2SL/0AUAec/EE7fG1uT95dKuSv1218yPjezHltxyB1619M/EUE+NrTA5/s25x/3yK+adpZ5WA4U8+/JoA9N8C69/Y2hWs7aR9uLySQRmFCXPC5DDnI59Kx/GGhqmg2HiGO3+zC7dkkiI6MMf410XgrX7Lw9pdlZXjpHL5v2hBInO44GD7cCub8feL212f+z7cxmyhkMisikbmOM/yFAHLaQM6jHWyy8n61j6L/AMhGMVvMME8UAVitMIqYjNRkHNADQKCtPVTTitAFdkzUZSrW2mlPagCoVqFxjNXWSq8ideKAKTmoyaklGOlQEkGgB+6jNMzQDQBJnNFIKWgBppN2KGpnegCXdmrEZqqoyatxAY5oAspzTtrZ9qYrDsKmDjAG7mgBuw5FSLDkjbQNxzgZq1CjbRsX5vegBgtj3OKkESqAN3SrSWzyDLnJ9BSmxGQWwB6UAQkJu5btT1ERP3jT5YYY8kNzUPmoo+6D70ATqVU/f4qR1jkQDAx61UN8q8FBj1IpY7uMngj86AJTaA48uTBo3yRfK4yDRvyQQce4p32kP8jj2DUAJiOTjpTJLQJgr0PWkeN0JYj5fUUQ3AyIyxZW6E9qAFEIHQcGmNA6g4WlleW3kwh3LT/t74yyjb3oAr+W4/hqWKByC2KnS+gccgZqxFLEejYzQBnbsAgrjFZ95CLhvu9q6F7ZJASGqo9tszwCMdaAOPuLd4X9qjrevYoipGGrFlgZSSoIWgCOg9KTPbPNHagCzCMpV20iL5GOBVayG4gGtrTYCySY6g0AS2qqhAIxW0l6kaAZxWNMjRndnisq61FslFJ9KAOyGuW8P3nB/GobrxjaQIQq7mriYYpp3IIYjFC2bl9rZBHegDbk8RXF4X2RcEcYqt5Wo3Kh2Q4PPFLZWE0bE7jtI7966vw5Zs9yAQDGAcA0AcvFHcIOdwPvVqKSZeHHPrXc6rpEH2VnVV832rm0s2VNtwhHoaAMx13AknJrc8OyM04jbpVSTTCCGjHymtPRbRo7sdqAO8s7KNowdxq29oFBwaLBcQircijB4oA5e/DKGHXNczeQMQTtrtbuFGDcVg3seFIoA4iWFjIRUH2GV3wo471vGweSUlfWrE9pJBABCmZG6mgDmbi2S3Tgc1Jpmnrch2ZeorYbS2lTEi/P1NSWlq1uCoGBQBygs9l1LGuAQeDig2l67YZyQD6V2KaVC7+ay4JPJrUFlbJENka/WgDk4LSbZHGq5J4OR0rduNLM9qkHljdjrityCCEbTtXNXWMeBgDNAHnsvgxyGkLFT7VmhL3RptoZnTNeo4eb5FXr3qrL4bimJMoJJoA5q11gXKLldp6VdSVkbdu3e1Xj4ThHEZKYOaUaBND9xs0AVVkeZshcVcjt5CMl/wAKT7Hdx9qMXCjJXmgB5gUHLVTuZVXgU6aabABXGarsqg5kagCqSJCSFxWfexBoyp71qO65IUVnXHzEZ6elAHIXcBjkI7Z4qmU5roL2ESFhjkdKzZLb93wOaAKax4681atbYSltow3vTIlwxGKmjdlfr0oA1NOnED+S/wB71rWuU86HA6461z5JkAI+961saZdC4jNu5+YDvQBgPBtukP8AtVF4kOZYh/s1rTWxWZFPJ3/pWL4hP+nKnoKAMhThQKX3pMZ6Uq0AWFbaqZ7mreza2fWqDZITPatKL99HtH3l70AUpGZD8vUHNa3F5HbyDqw2vWdPHtctnA6fWtLSkENuXlPy5yBQBv6YUsYSAoz6mqV9rO2UiMgzf3uwqrJds7hFB8sg1S2fOdigkdTQBbRGlPnOzMW6sfWpUtWZ1J6KcsfarllaPcRHziVTA6Dir7QLt8pFIyMH396AItPBknadOFA2qD+WabHcC31BRtyS3J9KlOYkZVYbsBUFVvs8iHe3JPf3oA2tSJj1KKXOQ8YOatTRCZBJ6Lj86qt/pmnRuBlohtNWbeUNZjcO/NAGTOo+xNGwyVOM1zZjKXyx4DIf0rsrq2DW8m1fkPOfWuNk/dX6mQ4ZTxQBsX8fl2geMnaq96w0UyyrIT7V0V5J5+kAkcsMZrChjChVDfMf0oAfCN0hHoa0LljHbMAeWX8qq2+FndWjPbmpdRYLHhSR2z3oAr2I8pSjEE9ye2atuVVAhlDAdAO1Z0Cko5Iznoauwx5TJPbkUAaVqnk2jHo2Cc/yrJuJ2igcv87ucDHetOeUjTxgYJXmsq2k824SMHKLyT60AaVhbSxQRx4PnS8nHYVutFHZ2nllsHGWNV9NKoXunGEXgGsvWdXiM5t4/mJ5Y+lAFe+na9yFG2Fe/rWHfzq0ixRcRgVPd3rMUgU4A+8B3rOYl87x3oAiDNkheoPX2rTtrt44gN27tg1mLEzPweKuxfIAB3oAtSyI74PBxVJ1ABXGafO377FRZKxt82PSgB1oQsx47VpzqFIb1UCs+1AwhcZz/FWheYwmxuMdKAAECzkX0rOBCsWq3DIrxSKepqlsMisV4xQBBLKWfFaumLgZrMSAvLj0FbthBsAyaANOFcmtK2GAapxKAeBVuPIBOaALkLc81oQuMVjJLiraTYAwaAOP+IZzqNp/1zP864w9a63x2xe8tSTk+Wf51yVABRRRQBPbnEi/76/zFfb2gc+HNM/69Iv/AEAV8R2iZYk84Zf/AEIV9t+H/wDkXdM/69Yv/QBQB5549J/4TzTgf4tPuV/8dFfMvmGC5Zl+8JCcHpwa+mPiEpbx3pXIA+w3HP4CvmScF5XAI4Zhx160ASz3c107S3E5d+gPFVxyckYrsvFtlpun+H9CgWLbqbW/mXPGOvT+RrjegGaANDQudWh966h4xzXM+HxnV7f611zqOaAM5o8VGY+etX2jqFkOelAECpSlalCe1KVoAr7aQrUxFNIoArstVpl61cYVVm70AZswxVR+tXJu1VWHNAEdFLik70APU4p2aYKd2oAa1IBSmgdKADBzViPPHNQDrVuEcjpmgC5DCxwccetWzBGq5xlv51WiLMwRck+1akMITDuQxHYUARW9uxbdtwKvK0cfLEH2FRSOW5+4tQebboeW3H2oAsNeEH5eF9qqzTyu2UVzT4ZfMk2xJk1rwWErDdIAoPrQBgiG6mzuXaO1J9muEwMjr1roGt0RiC4qtOIADmZR+NAGZ5co4kMbD3prR22B8uxvUHinTy2y9JQ30qk90mcKp/EUAXlt32/upQwPUUjB04foKqRzKTneVb2qc3LY2sN3uOcUAWlkbKg/d9DT2WJ5AF+Qn1qi8+0hscf3qkDrdJ5e75zyjCgC2rxEtC/ynsTUMwjX92QcevrVcu6kq3316Z61ZjuBLFskADDoaAKLQAt8rY+tMLzQtw4IFTyqQfWq8rYXpQBbi1aSIYYZqVtWgkXDZDGsVpDjioGlGeTQBpzuJOVcGqbg8gkfSqvmsDgGjeepOaAGyQjOV4qAjBq0W3Dmq8goAt6aczgV0WnMI1uAfWuasDi7THeu20izjmEpbvQBiXUxcFVOSarWelSyTBpEIGe4rtodGtlctsyTV5dNXAAUe1AGLbaUiAN5dTDQ7aeUs4Kmt1bN4wBU62zf3fxoAyYtHtRhdxIFaNpbwWUeEPzetWfsWMMzAfjStHCo6g496AKszySqQD+dQNBK6hSoOBVpzx8gqM7hyzYoArQ2syMRKvynpV3T4QLvjFQy3vyhO4q7pIzIG6kmgDrLZNsANOkb5TxTYziMLTZT8tAFC5HBrJuYgyH5ea1Z+VNU8/LypoAyYYdgOeSKV7lSNm3nsautHhiQPvVTnsCrbxkigCBmJ69aRUwQStWFi3dulW4rcSjkYxQBSJ4wVwKsRlHXAU4q2tmv8PP1qQae3JHH0oAbFbhlBFXY7Mbeabb2jgdatiCQY+bNADUj8sYGKmXpk0qKM88U6QAD5efpQBGyjGc1XYgnuKe7kcVWlbB4oAZKTnGaqSdTU7OCDzzVZ2yTQBBIEI5FZVyihjnpWlKTsJrLvCTFnBzQBUkYICy1SeTzIzkYNLI7LxzVdmJQ+9AFSc/vTx1FVwoEmDyKsT/KQ/pUEQ3OR680ARy2nzblxzUAi5ORgitZB86gii4tgMEDr2oAz4F+bParkcDxOJo+B1JpgiETDjI71esw0hfP+rAPFAEcDC7nLj+GuR1iTzNTlb0OK6JGa1llZDhTmuVuWLXEjNwS3egCJOQRSYwaeo9O9NbrQBI3KrVuJipQr3ODVPPyircfyqjdfmoAuG2Eync2MN0pwfIZP4UGKejYu8H7rCo7h1twcDJc4oAmkJSOIL3XpWzZ6dDpsEV5qPBcbkjz9/0z6VBaWsccaX033I0+VD1Z6jkSfUyqsWZ85I9PYUAXXvnkdZGwFB4jToBWpawPIwZQxDc89qba6db2pWS4ORtA29gfWppNXQOUiT5QOMUAOuLZEkjYAbl7VDNaGVuAw78dKjiu5bk/IAp7FuabqMWqiBWWbkckKKAJ7aWPTt6Ssdr8YPY1NH+5ZkdgY35VhXJyyTyYeR2DA4yRxWtZagsA8m6XdFwd3pQBspOIZBbzfccfLWHrunokiyFCOeGHSti4Vbmy82Fw4HKnPQUyGdL2Bra6ADBflJoAzliMuhkj5mAOAK5+GVHdUlBDHuK6uytntjJbsTjnHvmuUuo57HU5o2G5VywOPWgDRtYZC3yMGy3c1FeLKZSSjEFuoGagsXaRkZcq+7mpdQklUuAQmTnOaAHx2864wgUHuxxUxaKKMqZAzE/w81QjkEiqzzc4x1zS7gp+TPJ6kUAWtauWS0hSPjfgGm2UIWSMR85wMepNQauS1tA+PuHFaOiRb4fPfgLyG9DQBoapeLYWKoPvEcAetcbLcOBmQgux7datavqjXVzIoO1FPHvWUwJdWA57mgCZVyXJJzjGaai9s4A7nvU2MW5b1qsWLtjcABQBYjIzgdRSxHBGT93JqqZ9p4HPSno2SD60ASsd82/oB2pkzBVLhcn0PSnEgE81UuJG+6OmaALdmzTyLnIA6DtWjejOwe3aodNt9kQdzggZwamvSMJJnqvSgCpaOBcOuO1WUiEdlIx65P8AOs+0bddfLyfar1/KYrdYx1J5oAn0+3VxuPXFa0cSgDAqlpiYtgx71poKAJ4lzzUwyB7U1BgU4PxigAXk4qypwBVdRzmpwRxQByHjf/j6tf8AcP8AOuT611fjY/6Ta/7h/nXKUAFFFHegC3ZEgMP9pf8A0IV9s+Hx/wAU5pn/AF6xf+giviWzI3MM87l/9CFfbegf8i7pn/XrF/6AKAPOfiGrP480hB/z53B/QV82wxRzakkTnajz7S3oM819J/EAynx9owixv+yT7h7YFfN1raTX2spbW4Jkkm2rj1zQB0/xSMieM2t3UKkFvHHHjuvOD+prij0ruvi1CIPHDR+eJpBax+Yf7rc5FcMelAGn4f8A+Q1b/Wuv25rkPD3/ACGbeuyJwOlAEbL0qMrwalzmo270AQkYprU5veoiTk0ANOe1JxjnrSk01uhoAhkqnMetXJPu1Sl5zQBRk5NQleasPwaZgUAQFabtqyVHpTNntQBDtxRipSvtTStAERpAacwplADwTkYq1bq0j4HWqi7mOF61s2MXlhSyfN60AXbeNYkB/i7057naSIl3N6YoLxr97H1pUugvEEY/3vWgCH7Je3bfN+7X1NXItNs7fDTymV+uFoK3E2CxOPalaNlXAU5PegC0NQW3G23t0H1HNV59QuJMlmK57CoSwjHzk59Kqy3BLYRM0AEkrSthnY/jVR2HmbRkkdiaurCXUNtANIUKIWWMb/U0AZzCUtwgFIyyY+YDFaS2s8y7mXFH2IjlzxQBnKmSArYY+tWvJkICng/3hUpEUeelNF1GBjOfagBUGB865UcYp0kGYh5R24Oc+lQteOTwopYrza5DLQBLOXnQSBcSKMH3qutyY+q5YdavLMhUndjNQlFZiQASaAFWVZE3Z59KgaUZwcCkeMRtnGDTGfqTQAyXa4GMVA0K9e9K0h3cHFRvIQetAEbR00IR9KcXJPWgydqABgMCo5BxTt2aUjIoAWxGLuP6132jnbEx9a4ayUfa1OOAK7PSmPkAE9aAOrso96ZNaUVuMZqhYZCrzxiteAEnGeKAG+Wp7UoikPCKMe9WxGD0FOELYyT8tAGfJaFhh25PpUC2Mducsxb2rTZMfdqnNHIzDBNAFWUEjCIBVOaFwpJGW9K0xbPu5qYW6xgu4BoA514Cke9/vela2hKM7mrN1GYPLhema1dHAwoI4oA6eIAkH2qvdybAaniwU44qlfD5OtAFZiXHFQNnNPhbHU8Us2CMrQBDty1XoYkkhKkc1no53cmr9vJhvY0ARzWSqAUFRpEV4rWwrjmozEueKAKkcWGq2iHBo2AdBTlfHBoAcoAWnA0KBjOKQkA0ASFV2c9ar7tuac7n0qu7E9TQBHM3Oaqyv69KmlYYGapzNigBk7kxkqR5g6L6iqsNyJARjaf7p6ipC+459KrMCs7yE9RmgBJWKqeCKoTuNhzVuRywPPFZt1IoWgCg5Bc5qBsZGOlKSTIxPSomfAoAr3fQ1Wtz+9FS3L7l96bbKPMYkcAUAaMYBmSp7vamOKgjlhit9zkGQHih5GvDxwBQAwHc3yrn1qSSMRwFlJBPpTiiw9DnjmmyEzlUThe4oAp3CrHprMw+Y965W7iyN3XNdNrTPDbiLPHpWEm2ZShHToKAKCDAX2pkq/OR681M6FHKEVGAZMv/AHeKAG9FFXEP7pD6VT6nFXIf+Pd89ulAF7BYK4ODilWE3NxDAepYZPpUak+VHjuK1Y0jty9wRktGAp9DQA3UbzzX8mAEiL92oHc+tblmE07TlY4a4fGT6Vg6TsVpJmGRGSTnvUkd7JdGSLLfMSR7UAWr28kuIpEyQ2eMd6NORxIxdhjbgZqjHdBJAJSCD0FW8EvvQ/KSMAdqANJWWAiPpnoa2bKZJofLlOT0B9aw7mLzIA6nJTiq0F9JalBNkgfd9qANrUNHUAvGuY26j0rDeDy4Hi25ZO57iupstQhliCytuB7Ul9pkU0fmWz5I/goA4y01GbT281PmiHDJW20kd5El1DxIBlVrH1eyljUrs2AHcMjFQWs08CRqj8kH+dAHYW0sV5GpORIo5+tZfiPSXuIRcW6GR4/vY7iqNvq0lpcoJ0wT1YdxXSwXVvdxiSKTaemKAOCWQxsgG5XzyMVev4jPbrIOhWt7U9OdZDPDGrSDqCODWcEluomDFFAHQUAc9DlOD6VOZFERKnJB71HNA8cp3bto6GmkKYgpJ560AXpYmvbOMIe+Wq3c3iW9vHZRNhSuGIqjaXIt1MSnkjFUcSLLJv8AmIOcmgCGbGXU84PWoyCFGKmIjkc7mAOOhpCuyM72UDtigBZJgLUDFU2ORkVOzD7OJG+4Gx9agk+8SOAegoAjXcW5qwDjFQbsHmnCQGgB7knI9Dmp7W08yUPIflPNIgXaSwycdKekrYAxtx91fWgDTeRT8qDgDFVdQbeVROw2inI4jiLtxx096otMNxOfunNAF+GOKxt9w5kPeqoR7ucvK2EH61BJcktgHAxTVuDwNxoA6uCRfs8YT7oFXkGQKxtObeqg1uoAMYoAlPC1EGOae7cVGuSeKAJ0Y96kDUxUJFPEZoA5LxnzNaf7prlq6nxopWez/wB01y560AJRRRQBbs1UlmPXK/8AoQr7a8P/APIuaZ/16xf+gCviOz++2TzleP8AgQr7c0D/AJF3TP8Ar0i/9AFAHnfj0KfiDo2ScC0nPHXoK+eNE1pvD/iSPU1gSZoZWZY26Hmvonx8UTx5oxbAzaXCjPc4HFfMciSPdNHGCWZyAAMknPTFAFnWdVuNc1e61S62+fcPucL0H0qgTxkcj1rr7fwVcRabLfagZYfKj8zyYkyw9z1xW7e/B+/j8KjX9Ov4rxBEZZIFHzKvf6mgDiPDpzq9ua7JselcZ4fBGswDB4ODXYufmNADTgVExpzGomNADXNRnpT2qMnigCNqaTTutNYcGgCKQ8YqnLxmrje9VJupoApP1plPfrUZNADgaWowakBoACuaYVqUU4LQBVZKbsHerZSmiHzDj0oALSLEu4rkCtOdnVA0eAKrFDHFtUgZ6mrFo4dPJkI9ATQBCgDsGYlvYVdRV42gilFmLZtxOVbvU5uIYUAPJFADkLHALEfhVhFjPBl+btk1nf2oCwCIT+FOAknfIQLnuT0oA0zaq6fOAR7GmLaRqp8pCSPWm2toqHfLOW9gav8A2lANqbQPUmgCBNPEgBYqv1pzWttDESxyRzzTbi/iQj5dxA656Vh3V5NdMRGCeelAFu61KOIYBB9hWRcX8sxxGjYNWU0wthp8qT2p7RCEhVAKj86AMkx3DAlvyzTltm2bs8+lX2h3PuINOjglDFtmEPf0oAoxwMBuJx7GrcMYP3lzT2tl3ZyW9qeh2/LtIA9aAEaBWIxxikMbLyDgCpDljlT0pkhZuvFAAJUb5HH41XuYlC/JyKbKhznNVy7p3zQBC4PpioXOalcknPrURFADegxSL1xSmnhAqhjwTQAEYGaFbJprPkYpoJoAu2alZwOpPFdpZxeXCgzkmuS0pRJeoDXYwf6v6GgDoLN8Koz0ratn4zmudsn/AHfPrW3bSrs4NAGtH0BzVjflAtUY5AVAB5qdW55oAmZBimCIdTUnUZprOADyKAIWwMnFZeo3Py7VOM1dncgGsC8k3E5bFAFJvmn2nn3rpdLQbR7CubhTMwaup01cAcUAbCcR8VnXzEgitHpHWddYINAGaJCOKcXIHPNMYYbilPUUAJyTmrMJJxz0qo0gV8HirUMi7c0AasDBk5qbYOtU7aTIPGKtK4I60AKRmkKgj3p2aaTQA5ThcVGx5pQaa54NADJJCBVKSYkmpZn+WqTHigAdie9V5WzQ71Vkk5oAGbg4PSq08jY/DFVryWQgeWTye1IshCAORuHWgBzSfuz61lTyZyDVuWUEEA1myN89ACHkYqGQAcVKTioXBLdDigClL1xToVZo2VOSe9OnTgkelLp0hhflSfwoAlt7CWaQFwQB61oTxpbRiMEbjTpLlnQLCOe+Kha3dpFMhyf5UASeUphAPD/zpjJ9nI9xVoABtrckDrWZqN2IQS7Drgc0AZupO8s4YnIHUVlbCspdenpWiWDhnzkGqwj5NAFWdRKcgYPrVBDsZ17Voy/Lu9ccVQmXBB9aAGY71aU7bVuOtV4xgGrB5gNAF+0Tzo48D7tW7p9sGHYf7IFU9Mf5cAj0o1CYeac/8sxx70AA1BUtDHFgMTlqqWl3L9pOHxk/lVVXZGLleDRHukc7BgnvQBsT4eYDhe/1qe0uGsXw+SnXmoHI+yRsOWUbWIprtthEmd/FAHTWtwswV4xlG/hpLy3E425AI9qwdHvJQ5hJAb7y/wCFbks7TxfOMMvXFAFW2leKQjJBXvmtq31V4Vyep71hSneMKQTjpnmp7GZJV8iQ7W6DNAHStdWt/GEuYstjGfWsG/8ADgdy9pLgDoKswrLA5jOD/dNTbwYmBdlNAGNc6VOYlDEF1GOaqxyTWE2HbEeMHBq9f2t+o82GQuo7d6ypYpriJlZCHPJ3cUAbFp4hlhysuJoh0Pephc2WokvauIpu8Z71xwaW1lK9PY0r3Hzhh8kvZloA6a9ti+V6PjoRWHKWikKOuKns9Zkkj8uU5boCafcSxyrtkX5/71AGfMpSMup5NSW0yzxbZHAb1pPOVfkdcr69qqzxqWDxHAPpQBLcxDyj5Y5HeqYlOQrrnip4pWGUbkVG6A5b0oAZHkM2TlT2pdnGKcop+BQBTZecUnSp2XDHNMK0APjY9e9DSO0nzdB6Usa84qyIOeRQBTuLiSU8nAAxiol3bBk/WrksIAJqJk2pzQBA/JyOwxSxKWkUU4LkGpoY8SpxQB0OmrhR7VuK3AqhpkIMfPWtMw4HFAEZG7gVYt4GOKijXDc1q2iggcUAIsBAp/lgVc2DFN8sUAcF48TbLZn1U/zrja7b4gjE1iP9g/zriT1NABRRRQBas22ErjOSv/oQr7b0AY8OaZ/16Rf+gCviK2/1g+q/+hCvt3QP+Rc0z/r0i/8AQBQB5949RX8faKGGQLa4bn1AWvCNBt10tZvEtzEksEEzJDC5wZXzzj1xx+de8ePQzePtCXPy/Z7j+S14T4lD2vhrSbUGM28hkuMp13k4IPvwKAMzWvE+oaxKxeZ4bQDasMZ+VR6Z6muh+HPxDuPB+rFL1nn0u4AWaNzu2j+8BXBkkH5hn2oOGPXk9sUAe5/EbwfZ2eoad4k0eyWGxuBtkER7no2O3WuNeQZOetbyeIEtvhnZaJqz3g1KSUyxJIu0bMDaeR0yDXIPcDJyaALTyDtUTS1Ua496jM49aALplFMMgqiZx600z+9AFx5QOlRmWqjT+9NM3vQBZeSqsr5zTGlz3qF5Mk80AMY81ExpztURNADwakU1CCKmjHc0ASqKmUVCp5qfOBQAhFOiUbs0macnBzQAtydsWRWd9ofoM1pzqHiwB1rMZdpwowRyaALcWoTBdkh3J/KrCzRScB/zrIjb5296sRkrjBxQBrxj/aUYq3HLxjaG+lZkSedgDr61rWtvDagNIQx64JoAnjhlZc4IBpxSG2TdKxZ/4VFRz6qCPLiPH6CokLMpK85+9K3b6UAHlNO+6TCj+761JvigGERU9SaieYIuF5PdvWoUj+0NkbqAHXN60q7UIXtuPelis5FXJfJ96sxaekbZcgr1wame8t4ugBoArpbPKAvy8etSrbSKmzCqO+TUiapA5C+Vz2xVuOa2XlsBvQmgDP8AsKdck/Sg2ydNxx6YrTkuodvyxjHrVCd4mOVIye2aAKz2kS4PzZqvMoUcMMe9NmYq5xKV9AelVJJnU/vAGHqKAGSgFutVpAATT3libpuWoGePdjeaAGsuelRlCOc1KSoJqNpUHGOaAG7e9MdtxAoZzjANR55z3oAVuGoDYIobmjbkUAaGlSbL5D6mu3t/mjBrz6BjHJGwOCD1rudPn3wr34oA1oZwgANa9rIuAa50HkEitS2lwBk0AdBG/ORVuN9+KyreUHjNX0kAA28UAXt+ABUM5H8NNEoC89ajLjBJoAhvJdi/hXMXUxkmIHrWzf3ICEE/SsJBuYseeaALtsDvUV1VkNsa1ztpFmVTjiult8YC4oAvyH9zxWVKSSc1pM2I6zpckn0oApPjNGC4460jj58VPGoGOKAKV1CwiDHrVaC6ZHCNWxcqGXmseVBvY45HegDchnURqAeacJmDketYcF3tYBjyK0RcBgCDQBrKSUHNPYgAZrOjuvl6U4zMx5PFAF3zBt4qvJPtzUfmfKcGqjyEvgnNAEzS7qhkPFRbyHIzTZJOOtAEMrYqjK/NTyyZB5rPdmycmgBrvhqidgSaa7VFu55oASTAFU5WBOKlkbLAVAygvigBFG7j0qXZ+6Y0irsOaezHy8KOMUAZ8gLOqDv1q/DZAKGH0NVo/wDj7UEdq2bcDZgrwaAEjs1iG4HrUkcIOSaY74JH8I7VTutWjtYzlgMUAM1K7jtULZxiuJvtSe8ut/8AyzHAFO1TV3v5mVSRH/OqEQAPA5oA1bafEBBqwhzGWHesreVIGeO9XbWQ+TgmgAuRgq1U50/dZ/umrkzBk57GqspzEw9aAK8Z+b8Kd5n7kj0NMjGHGaXK+aUxwe1AFu0kEBUHoeafqYI/ej7pqizndg/hWopW/wBOMI++o60AZJbMWKnRSkO5eoFVGBR9h/h6+9aEDDYEPO6gC3p7h4Wjb+IU6BfLZ7eUcH7tVrYmG5wThTWrLEbgBsYlX7re1AFK0jNvf7Scknr6V0FyC1s0icEjkVi7PLuVJHJPJrakkVIj7igDFFxzgcOo5p8cyyMxRsOOlZl3IBcll4pgnYMGVsEUAdXZ6opCQ3RwM8PV2WaKJR5x2j+CUdPxrjzIZYcA8k5NTR37rCIpHJPQZ5zQB0b35iUgOHB6Vm3d0HiJVgHrHaVwxCkqT2FRGNnOec0ASSTITh8O3qKgd1K7AvIqZbMk8ipltAOe9AGaiSiTIOKsC4l3YcEir4tAT0yalFoO4FAGS7yE525U9vSnRxnoScVsCyVOcjBoFoqEBgceooAorbxkdyaDBjpitX7JE3AIHpTxp5T7wyD6UAYxtyVyy4+lQtCRjHSukFiyoMEEehPNRy2KPwEKt+lAHNSRsxxQI2ArXk09lPGSaati/cHFAGWEKsK0AmVBNKbba2WPHYmrAgJgP6UAZ10F8sAdSarzJxj2qxMhacBRwDzUFzIMvjr0FAEEeC1X4I9zDHas+DBfFatkpy1AG9YSYH4Vrqdy1h2Kmt2FfkoAjx+8rTsvvVQ2/NV+0BGPWgDQxkUbaeo4pcUAeffEUYksP9xv51w9d18Rs+Zp/wBG/ma4brwCPcntQAlFXni03YgjuJd+OSVyM/lVaWFU58wMO204oAktceYfqv8A6EK+3NA/5FzTP+vWL/0AV8SWYG5jj+7/AOhCvtvw/j/hG9M/69Iv/QBQB598QG2+O9B5wWgnUfiFrwq61rT73wW+l3ICX1rO7QEA5bPqfwr3D4mAr438MuoJOJgQPTC18z3JH2qUHoXbt70AbXhPwvL4qvri3FyIUgi8x8LuduvCqOSeO1dnP8J7rw7HDrV6ov7aN1JsU+WRlJ715jb3NzaziW3leGQdGjbB/OrLa1qkg2yahdOvceYf60Adx498YW/iW/so0sFgazBBw4bA4woI7DH61yTXHufzrPgKlsnJI53CpCDQBObjNNM3vUJU0m00ASGU0eYaj2mnLGaAFLn1pN5p/lUnlUAMLE00gmptgppFAEJBphFSsKhegBAakD/KBUOaUNQBajkx15qyr7gKz1arUbcUAT7ucU0sQ1PUZGTUbHDZ96ANLZmyL4wcVkyjam7qTWuz/wCggdKo3MYFuDQBTgiyc1bjgUnqOKrI5UdKu2/lSxgPkH1FAD/tcNuCF+9TY5pbluAzelSLpimQMvKn1rTit2iTEKgH1oAbDYxRKsty+W7ItTSEzjaoCRjtSi0uHO5ufrVoReXEWkxnvigCmsLBgzKNvQA06acQjsvsO9I8j468dqpS5Zsvk+lAEklxLP8AdyBVYwsGAPJ71YjRm+6MU24cRIQ5+YUANknitozgAydvaqeTMfNdzu9jioCkk7gkkLmrEduynG0n3oAtW93LAe7L6Gp5LyC4H3fLbvVRkZUyeKrNFIx46UATPP8AORkOPeoJHVvunFNaF+O/0pCoAO9gMds0ARsrN0ORULhUwSOac91j5VH41Xkck80AOaQt7VEead2ppoATFIRjmnZpOpoAMGpY+aMYUe9KqnBNADCcNiuk0e7YxBRziubPXNXNJufIvNpPD0AdtHLuXk1dhfBHNYkMmFIzzmr8cp457UAdBbTDPWtCKXmuct5SMHNasEmQOaANcPnmopZeDg4qDzTgc1Tu7gqpwe1AFDU7jMgUGmxsAqjPNUiWnnOMnHpSiYRyhWGCD3oA6uwUMAR6V0NrFgZIrmtNuECDkdK3o9QVUxkfnQBoyIPLrKndY+DU734KdR+dY19eKT1oAkLqZODmpg3A4rDW5Hm5BqzJqSxqBuH50AXppeMZrPnOWAHHvVaXU0I4I/Om28j3lwoXO2gB00ZUbwKLe5JO05FbJ0/MOCayLrTpIWLLnFAF+CfPGasCYEYrn4ZmjY5NWobnPU0AahlPOKj8wA8jNQLID3FBagCQv8xNQyy8EYpjyY71BJLQAx3Oaruc0SSZqPcD3oAhkODioWbAqZxk1BKuc4oAr7svmgKWbdSAYY5qWMcGgBwQ9zTHYhDj6VOMYFVJn2MPQ0AYzamINSIf+EYrai1eExA7wPxrg9ScyajKwPQ1CJZMY3nFAHWalryoSqNk+xrmrq8mu3+ZiB6VW+Y9TkUq4zxnFAC5xx2p6HBzTO9GeKAJWO48cZNWo32fL7VUTkipyfn4NAE5OUNR43rikDfI1LCeOaAID8rZ9KZkGdTT5fvNio4wGbOelAD5ADJketSQzGE5T1yargnnPrTgcUAWLxFkQTJwT1FRWz7ZBu59KZ5hVSOoNJkggigDTwCob+IHIrRtbxCux/vVl28gK4PU9KWUbSCud2etAG1KiuFcdRUdzMRCcnHFJA5NuCap3zmQbR0oAyZnMhL++KSPg8mntFtTb700IaALkI5ODUyW5JJxnNRWvB5rSjB7UAVhbbT04qzHAMdKnRck5qdVGOlAFcRYGaTywecVa2fLTBwcYoAhC7KdhyOBVnAxxj8aeqx9zigDPKyDrkjtTkmmQAFM49a0DGG6ENUM1sccHHtQA+L962fLAP1rQjjwvJXHuwrECSR5IJNNEkxOC2KAOlR7baFfYx9RUwgtCu7ft/3jXMfa2i+VRlvWmm7kHzSNub+7ngUAdOkdk8gRTuJ46cVMbW0iBLlRjtnNcvHfXMgxvEcfc96sSXkSoscW589WJoAt6hJaODmNSg6bRWZBJHMzJGMDtU26SRfuKg96gVzb3anAKnqRQBXS1KtI7joTWJNCwYsOQSa6WeVD5gU/erKKBGZCMjsaAMeMGObmtqzYDJ65rPuowGyBVzTeUFAHRaenf1raVdqVm2KARitM4CjmgAVcgNVy3GCKqKegFX4V4FAF6MgChuKQZwKRjnpzQBwXxIHz6efZv61wZAJ5HFd78SeDp/0NcEepoAAMdz9KT15NLRQBcsOVk/4D/wChCvtjw6f+Kb0z/r1i/wDQRXxNYnBkH+6P/HhX2z4fGPDmmf8AXrF/6CKAPPviISPHPhtTzvWdR+IWvmO5BW6mBOSHb+dfUHxD48a+GmHULOQfwWvl6fm5l/32/nQBHRRSHgUAWbMbplFXjCQaq6UvmahCuOCa6F7TBNAGOYT3pDBWo1vjrzUZi56UAZ4gqQQ1dEI9KDGB2oAqiKkMVWttNIoAptHTNnWrbKaZsoAz2XBNQOKvyx+1UpBg0AQGkFOIoxQA5TirMZqpViNulAF5Dlajdc8+9CNUhIoAsu2YFWh08yJV9DVYyHHWpLWVm+Vjk5oAq3IEZwKgWV1BINS3anzMt0qoScUAadleTkgA5rctWYn5mIJrl7WQxyKTyveujilHlq0Z3ZoA1w+1AC9V7k5baHyDyaZMiSxKV3K4HT1qmUlb1Vh1oAvwxrK2P4R1qukEM10xDHAOKuWsbJZSEdQOTVOKR442EQAYnJJoAluJILddqvzWYR9oc7mGDVk27zPvkkU/SrUVtGADtBHrQBSWzYAY5FW47IY+Zvwq4WCgADFRsgbpk+pNAFSW3VjgHAHrVWdoogQB5hHYVdl2RggGqTMmMYGaAM2W5lJwoCj0qo8ckjZIP1rVZYlyWA56VTlZuQg4oApPCV71ERirLpI3Wo/LPQ0AQEntSYJqYoBSAjcBjqaAGbacq808rliBSqhHJNADgvIp3Qn6U3JzT05WRj0A4oAqOfmpu4o29eoORTiM00cPz0xQB19nKs9vHIOu3n61fR8jFc94fnDI8JPvWyjFWwTQBpwyZXHpWlay5XOaxY22ir1m4IoA1/M+T71Zd7MckbqmeTalVEiM90uRkZoA0dHtMqZGXrUGr2Q+aRB83at6BfJhCjgY6VSulD9TQBzttqEsK7XOCK0ItXPGWqhfWuXJUc1mlGVu9AHWf2ruX71Up9QO5snIrBE7KcFzUyzK/U5oAlm1CZiREpFUwb6UksxArSgGWAC4q6sO5uVoAoWdnczMoOcV2mj2HkAZqlYQgY+XiugtwAoxxQBaEYYD2qKa1Eg6VYTAFKWoA5e9sBG+QvWs2WF4z0rsbmJZR05rKuLVSxDdaAMKOYqcGrYmyKhu7CSJi4J2+lVo5TnB7UAWnOWqKQ9adhmGQeaik3AYJ5oAic5qKnsDjNRZOaABzULMMY71K3I96gKc5oArgEyketWETLbaRY/mziptuDnuaAGugVcA1n3Iwp9gTV2bCrmsy7lIhkbPG00AcXcHddSN6mmUE5dj70UAAFOpuaUHigBBS0uKFGetAEicMPpUiDJY1CCd1TA7U460AOFG7ZyaRBmmMcsVNACNndkHhu/pVi2WF2KyfKR90+tQREMDGaJAVAXuKAG3UDpLk/pTVyRzUwlLJtc5ApvyZ4oAaFzTglTxxBugqdbf2oAropByKe7uRVtbc9hS/Zv9mgBttcHZtNTbNwpqWp3dKux25GOKAKL2mRzVZrYhuK3zACv3arvb89KAM2KPa1aMY4FV2jKt0qaF8YBoAtKuamUYFMj9e1SgZoATGaaUb0GKnCA9BTxGBjIoAp+Vz3NSpGoHKmrEiZGAMVEEGcGgBysq9BSl1bnb+NL5QXvTWAzgE0ANO08cVA8QGTVsRZAyaGROKAKIgJTdkBfQ9ahdEA3KpI9K0Gh3MTn5fSiO3BPAoAzUVm6jj0q3FbEDO2rqWw3ckAe9TErGPlJbFAFF4pF4wR9aqSxPkk81qGcvn5arscgs/Q9qAMg7lbkU126cVblU7iwC7e1VZC2e1AFa6QFhU2kx7pcNwnaopl3LnvWlpUJUrxQB0ECbEAHSp5GwBTY1AUUsnIoAkgOWrXgHArIth8wrYh4AoAn3UqbVyTUTGlHI5oA4b4kgN/Z5+v8AWuBP3j9a9B+JMW2HTW9d38zXn7LtODQA0UtFFAF7TwhMu7sFI+u9a+1/D+f+Ec0zP/PrF/6CK+JtPjLGT22n/wAeFfbOg/8AIu6Z/wBesX/oAoA4D4jHb408NcdVnH6LXy/cDFzL/vt/Ovp74kKf+Ez8MnPaf+S18w3H/HzL/vt/OgCOg9KKKANHQl3atAPVq66WIAk1yfh7/kOW3purs51wxHvQBQaMEVB5fNXnXC1WxzQBEUqNlqwRUZXnpQBXxmk8smrGypUjBoApeSaPJ61o+SPSk8n2oAxpYutZs8eCa6CeLHase6XBNAGY3BpKc45ptAAKlTsajHWpUFAEyPUm/NRqKceKAHk8UtrJi5z26VFmkhbbLk9KAL2owERo3Qdc1klcd66R4je2y9lUVjXEAUnAoAgiILqMda2bY7MAnC1kQ4SUE81rwssozjpQBt2xRwMv9Ksm3QjiRS3esWMsGG0MBWrEyMgBI3Y5INAEycQMgfBPXioTZxSyBs59hTo2C4UncCfyqZItjknhT3NAEJgjjGNoAoU9gOKkmnjh7hqotevM5VEwO2KALblI1yxGe1VJbqRgcMqCmGEk5eQjPalUW8QJClz70AVXjdvmw8hPftUbpJj5tqj0FT3WoyCMLGgUegFZE01xKxwDg+lAEsiIehJIqAtwRyB9KcInAB5yetNe1duhP0zQBCcDuTULsdxABq2bR1TlW/Ko/s7YyeKAKoDE9KlEYGCakCqmcsCfrUZcnIAJoAU4jOfvZ9Kifc0mF6VLtCp1yT29KFIQHjJoAZ91wvU1K6mOzbPVmpLaEzzh/epL9h53lDoBmgCj0ppGaUkUmeaALWmy+VfR4OAeK6YsSwNcgMqyuOq811kTiW1jYf3etAFuGQNwTV60bB+tYsRKyH0rTtZQCATQBqsuYzmrOnxjdvNVkfdGeOMVatpNq4oA1Q4xnFVnCvJilBJTIBpYE3sTQBnzxKXYAVRaz3Z+WtloCxJ96VIMHmgDlJtOYvgKadDpzpIOM11htVJzxQLYBxwKAM63smG3IrSS0wRx1rQS3BAxirSWxIGaAIbaBUXtV2Ic4pyQACnFdgoAlzxTWPB5oXJFRPx3oAaS2c5qGdfMBIHI61MCPWkLKp6jnrQBQDJJlGFZN7YspLRitO8Tyn8xeh9KIpQ68jNAHORztE22QEGrUmGjz3qfUbNHBdR83tVKMMseGPNAELkr171F706dt0irSlMDrQBDyWp+2nds4pN2RQAmBQ3PSmOCMGgPgUAVbxsL0rG1KXbpsh74xWrdyZbFc5r82yJI1P3utAGADnFLSd6WgAoB5oooAcx4FPRCRnNRj5jgc1MuVHPFAAEYckUMSRnH4UeYzHFPVeuTmgCRVKx7iKrSAiTPrV8sCFUCq0qAqzE4xQBWQlST3qzFiUYY4NVc09Q3UUAPddrkYJHrQEDHip4yJBsYYPvV23sSvO0nv0oAhtYyTya0olzgY5qSCx4MmCBTl/dndigCRYRiniAH0qSL94N1WFSgCFIBnOKnEQPtUqgelPAXPagCDYFqOSLvVojLU5owRQBizQEnNV9pD7fTvWzJEKqyQY5xQBFEexqyrCq6ripVX3oAuRY69anG1gOMVViO0U5txO5TQBNJ14FNWM4ztpg3nrVjftQDIz6UAN8r1pNoU9Bipd4FIf3nAoAhYM+eOKeluNm4/lVqOLaACp+uKsJCN2D0oAzvsxfAWrCWrdAmK00jT+EYqTCLnP50AZDWLZy5BHpSNbDA6j6VqFVOTjIx3pNgKcfpQBktCFzharSW/wC7yVzmtpo89qgePcMDGKAMBtPDnO4jPaqc1jtYgMa6Q25AJ61TmgyMgHNAHMNHIjhSM5Nb2nKMKSKhmtCzA4xV+0g2AA0AXwQcAUrcimAbTS5B70AS252sO9bEBygOKyYFAGa0beXgCgCdutOTnimM2aWM80AcX8Sd3laaCePm/ma8+JOeea7/AOJX+r03/gX9a8/PWgBaKQUtAF/Sjhrgnn5F/wDQ1r7W0D/kXdM/69Iv/QBXxRpn/Lx/ur/6Gtfa+gf8i7pn/XpF/wCgCgDz34l/8jj4YA9J/wCS18xXAxcyg/32/nX1B8RU8zxn4XK9vPJ9xha+YLs5u5iP+ejfzoAiopBSnpQBpaCcaxa/71dtMNz1xGhf8hq1/wCuld1IuHOPWgCrKuSR6VXCc1cZcnNRFcGgCArSbamYelCpQBCI6lWOpAlSqtADBHigr1qWkx1oAzbhflNYd4vFdHcKNh4rAvRxQBiyD5qbUk3WocnNADwMmrEa8VCgq5EtAC7flphFTnApFUMenFAEe3AGR1qaG2aQ8LUsds0sgA6V1djo+y0SU49aAKaW/kWY3DGRXPXo2swB710Wp3asSoPyIMYrnJAJSctmgCkAQQfWrlqkzP8AJIAe1LHBuUYHTrSNMbc/IuTQBsWthdyMDKQV9ziteKxt4F3SSgf7INc7a3c05Cl2Ud8GtuCBAVLyHae2aALjSReUPIhJI9e9RPBcyKHmcInZalkmkhX/AEeFcd2NUHkmuid7t9O1ABL5cfU7qh87K4Tag9McmpPKAGCFHvmj7PxkOpoAzZrtlbbtakEgbkkg1eCEL8yZ59KryLCcghgfagCFZYwTvOaQXMQ4Vaja3hz980gjgTpJQA4XA3HC1G87DJCimOUz1yKidx26UAK9zIerVXknc8DrTXfJ4BIpUjZuSu0etADQoIJPWgZAOKefLTvuJpm/g46UAHAGT1NMGRn+9jFG4MfcVYsrdri7AIyAck+lAF63g+z2Xmt97GayXfzJHdu9amoXQ8pokPTjNY4yY8UARkUoHFOx601s5GOlACjniun0hlks2i7pXNQpumHoBmtjQZsagyE/LIDge9AGjOrJipYJQMZqa5i3DfjOOMVQkhuITuMZwfagDp7SQPBirdvt3D61gaTcswZXyPY1sxOA3BoA0jOEG3tVdr7ynYL0NZ91ebP4uKorfRySY3EkdqANdr2RsgGoDdXCnkms5r1xMdqMw+lWkmmmXmF/yoAuRalIv3qc+pSs3ykYrNn84AfuX/KmASkDELg/SgDaTVbgLj0qWHW7kH5s4rHhgvXP7uNvfIq8umalIBhAM0AaX/CRSL1qSPxMn8dYz6NqPmhGwKbJoFzt+YjPrQB0A8UQDIJAFMbxJbMfvCsaPwnJMFZpvl9BUc3hpIm2hiT25oA3l1y3Zvvj86tJqULqSrLzXNN4cZLbeGIf61HFpdzEhJkbNAHTvMJYiOOfSmW6kKVFZVks68OTW7bJiMlutAEEybiB7VmXEO1jWy+BzWXevySKAMaUHzxSvkGnMcvnvQ4LUARgllpc4QDHNP2YAwcUHr7UAR5LKcioXZVSrEhOzis67lCpgDmgCjcuzz8NwK5vVpBLPtBztrZlk2BieprAuUJYv60AVB0paQUvagABzTwnOSeKjU4p2c0AS7kUfKOaiLsT81GM0HnrQALlmq9HGBGCaht4SwJqzcMIY15oAjkkCDjrUUMclwGB6VExMh/lWxbosNuvA3GgCJNK/dgnk0/+yZsbgpxWlZv80YPc10/2cNaF0jBJ6UAcfa6apf8AecGtOMRI2MnjirdxbbNroo3dxVXywDkjGaAHzZEQKnC+lUpG3LxVmeVUTa5C+lQJBLMu8LwPSgBlvcFH8snFakeCc5rn7gsJd2MEGtKxuQy8nNAGnvA7UoYNzUAkyetSgrQBKCO1KTxURJ7cUjORjmgAao36VIx3dKibigCu4xinIygc9aR29aiIB5oAuJzUoBFVFk2AZqz5u4Lt/GgBxZh0o2u3z1Oqqw6VMqDbjHFAECIzgZq2kWMADn1p0SD0qyrKpwaAGpC56nipBheCM1JuQgYFK2McCgBhkxwKYzEqRThHk8CnLC5z0oAZuOAtO3AAKPxpdnOG4x3FCIpbKg59TQAvmRxdW6+tQOTLKREhPHXHFXCYkT5lDGoTN5hIBCgDOBQBXYSou3aqsR1zVOWGZVyZFGatS3cKSLHlQxHUmsG/1VSnlFj5pk2KE70AWHjwRl81aiXGKpW0WHxOe3A7itJQFHTFAA2O9RDO7inOaWLBPSgC5AvFWk+UZqKEDFTlcqMUASKd1SoNtRouFHrU+Pl5oA4f4knMen/U/wBa8/brXffEj/Vaf9T/AFrgT1oAQUtFFAGhphXFz/uL/wChrX2r4f8A+Rc0z/r0i/8AQBXxRpg+ecdtq/8AoYr7W8Oj/im9M/69Iv8A0AUAcL8QXEXjjwqzkBGM6/olfLl5xezjGP3jfzr6i+IMKzeOfCiyDMYaY/jhK+X73m/uDjH7xv50AVxSnpRQelAGloIzrdoP+mn+Fd7KMMa4LQP+Q7af9dK9BlA3GgCvt4qJxVrAxULrQBXxThT9tKF5oATFSAcUu3ilxQAzFB6U7FGKAKFx901g33Q10NyPkNc/ffdNAGJN1qIdalm61GOtAE0fWrsfAqnEMmrqjC0ANkOCBVm2jypGOagCb3HFdLo+miYh2GAKAHadZjywWXmtu/m+z6aqIcEVZNqlvblgK5fU78uJAD8ooAw7ySSeRwpIGeait42LkMM1PB85yRyTWrb2akhsdaAIYLMjBAz7VPPYRzJnAz7Vqw2wGGX+GkuolKl4/lAHT3oA5SfzNOY7Vz71Cmoylw2cnuM1q6jGZYcAZNc9JEyNtHB9TQB0Vjq+5isnTjgVvxCCdRt4JFcNFCyqGDEGtWLUJICoySPpQBsXcNwrYEQZfUGqZjkXkxsuant9Vif5Wcq3vVzcJY94dWx70AZAd1+XzGHrTJJUH3yD+FaLgSgs8YGPSqxQZz5Y2jvigCiZoCMCNjURjgc58pwa0HdFGVjP/fNQvKSueAD6CgCo6xKMCM1F+6HVDU7vHjIJJ96pyuN3WgBzyqg+VBVZ5dw+Y49qR2ycZqMBmVm4AX1oAaXyQCfwpT27CpobeWRN+zAPcinbIoOZSGPYCgBkUDMQ2O/StV1FhZt2eQYz6VHYKZ2MrjbGnLVDfzmd9yA7c8ZoAz5CWYY6d6bjaDzTjhSfWoic0ABoOFXJ702lVTNKqj6UAWIRsiZyOvAp1hL5V7G4PQ1KIJLhlt4ULsOOBXXaF4FuHeOe6j2gEHFAG7pukm8jSZlwpGeR1rok0a2Cb7lFCAelW7GznjCxKn7tRxxVu7t28v8AfMNmORmgDgNU0+GGVpbVcJmsoTlJQM5+ldhPbx3s32eEfuu5FMm8K2tvbtIjZfHQ0AcVqcm5QF6mr2h6fGjfvgCxGTUV3p0kU0bODjd3FaCyrHzjnFAGutra8FVGa1LeC2CDgc8VzlteKAcsPzrViuVZFIYce9AGsLK2mJGBxTk023YE7QAKow36JJ1HPvVk6giE/MNp96ALUcEEa7lUE+mKc0+xcqgqgNRiLAAjHrmkkvELYUg0AWJZGkO8qAaYZUEBVwCTVRp3Y8dKVE3/AHjQBLDdKDsFDBGl3EZNRLCqSZqQyIrYoAfIA646GovKDntmopJSHxUiOepoAkESIQTjinu67MKcVUnlIXrVZZ26k8UAXJJAEINZF2/XmpJ7gk4Bqo3zk80AQBdzdam2YAzTUQhzUpIx1FADPLC5J6VCxAzippWytUWkKuBQASy4Q44rHuJt+VzVu8uMMVBrNCbm3GgCCZC4A71nahGFTArcMe0Fj949PasvUUG3igDBPBxQFZuimlkHzcjNCSSJ0OKADy2PQH8qd5bKOQact1KvcflU4v8A93seJTn+KgCssbucKD+VWFtmxzzVqO8tpCoA2nGKlCquTzyaAIkTyojWfPKZZDk8CtKY5iastV4oAfHgsBirqyncoJyBVIDaQakRstzQBtQygBWB+6a6fStVUqFc7Vx3rhFlMannipkvWBCpkn/ZoA7K+uIEEjlwfTFZqSSXZEcY25/iNVbCxkupUNwzbM8A12MGnwKAAoUqO1AGFDpccb5lJdif4ulaCiKAbAo24p1+wViV78VmtKXbbnkHFAGbqaqZW2rgMOPas+xlCuYyeQetad6QRID/AA9KwmBjulOcZ5oA6GNsnrVteRnNZMEpNX42yBzQBZY8DBoDBu1N25ApxO0cUABGOlRPUoOaZJ1IoAqvzTelSOMVXc80ATBgwAq5bbTxWYGxVu1mUHmgDVQqoxU6MDVAyhjxU0bnigC5gt0OKeqEDk5NMQ04uAetAE0Zw3Iq2qggVQWTPsPWpxITjbz9KALLFVPApNrP0yPenRpvGTUowo2gdKAGrEqgFiSaZKkfBZ/wHFSGQKCSaqygSHOOKACZkVc5rGvNUWBW8vaG6EntUupXa28Zibh3+6fSuTvrlsyL3yMg9TQBeuL+OaQytguT8qj6U+ytTdTCWSNVkHzKB2NZunWT7ftE5wFbKiuitZNzbkARjwPegCZgPN8qRMMRy/rUu07QvpTDJhNrsPMHpVmJcgZGDQBGISR61IsGzkmrSqFGcUw/MaAJIjgVMki9DVXewXgH8qqtd7HINAG4rKelPLZGMVix3wyMGrn2nKjBoA5T4jHMVh7E/wBa4Gu28fSb47H6n+tcUetACUUUUAXtMIEsnuo/9CFfbHh//kXNL/69Iv8A0AV8U6RHuml9lH/oQr7U8Pf8i1pf/XpF/wCgCgDz/wCJMrReM/CLDoZJR/6BXzNqAA1G5A6eY386+k/itJ5Pirwg25FAuJBlzgH7nFfNmo/8hK54x+8P86AK1IelLRQBo6B/yHLT/roP6V6HJ1rzvQf+Q5Z/9dB/OvRpB8xoAhIyBTCKmAoKAjpQBX204JT9oHenhTjpQAzGKNtPK46ikIP4UAMPFNNObBpnc0AVLn7hrn70cGuhnHBrCvR96gDAnHzVGo5qa4HNRA80AWYRzV6NMiqkC55zjFdBo+mS3sg+U7c0AS6XpTTyKxXiu3srGOCJV2dKm0zSxbxqmBmtd7YRoM8nFAHIeIbloYvLi+8eK5V7ZigUjk8mut1CyeS/LP8AdHSqy6a0k2QmVFAGFaadlh8tbkOn/IBjpWlbadsOdlXWt9iZAxQBhtA0Z+Xt1qGUCQcjGK1XAyfWqc0Jkzxg9qAMaa3DhscVh3lsBkEdO9dLcIUbaaybpMqVZc470AYKmRGwOQKupNkAMgqNoEDHLYqSN4Ix8zE47UASguBlVUD1NPR7l0YRk/yFRxXsbkosBHoTVhIS6Ydmx6A0AMMl0Bgyr7gU37bMDtBDYq39nCxZO1R6k9arMibiIepHWgBjai/Qpz6Upu1IHmqBx2qFonPyjLP7ChNKupSC4wPegCF51YEbRjtVUb3JzGT+FbUekww8ysD7Zoknt7fhVXigDNj01plyVKj1qZktbWARkbivX3oe7d9xwcHooqmYXY/Nkn3oASe9Mh/d5WPoBTLaJrq5VY1LNnipo7NywAUnPQY61rAppFruVFNww7dRQBXvFaGNbJCAn3pGHr6VTuCEjpHkJJlkJ+Y5xVK5mZ2PPHpQBGzgnIphPemrTj06ZoAaSQOO5rd0jSbi8cJBGWlfgHHQVX0XRp9Yvo4Igducsw7V7ZoWi2+n2kccSKJAMM2OTQBS8MeC7XS0R5F8yY8sT612qQxKg+QAChITGqkjtzUM86qhOMAUASS3CQocACuU1bUpp3aGIZ+lP1G+aeTy4WLEnBx2q9p2mqgVnQFsdTQBQ06zlSDeRhs81pR25ZS0p4xWh5YBwAAKp6hMIoDtOMUAcR4qlVNvl8AHFUAoaJfm5Iqn4s1EIAp5y1QwXe5I8Z6CgC20TJzmljupI+AamYB4gcVUELlulAExnmblWxS+dcsmC5NCW0hHAOKnitpAMYOKAGRNKFG5zWhAx4JY02O0bIwtXhZNtBAxQBNHLnip1JFVFQRnJNTiVRGCWFADjKQTmqs0+DmkaRnYgHNNkt2ZRjrQAgmLEE1a89cLlqqGEp1qKaJnGV4oAuXE8ZXAIJqm8o2YHaq6wOrEu1P8v/aoAZJJlc1CkvXFK6kNt7UggKvjHWgCeAMQT605lCg5qaMeVHhjVO4mBJGaAIZZMfxVQnuNgPNFxLjjNU2Bk680AQMWlcmrSQ/uwT1FPjg+XGOandQiqoHNAFSQZ61nXse5Dj0rWZc9aqzw7g3HAoA5O6XbIPpUFXL9cykjoOKpAZOPWgAAzSlcU9EO9lPYVI0e5AyjigCuDg+/atG2ug42OeR3rOKkcGlU4HHagDXlGY3A7jNZyDtVi3uC3DHnpUUg23BA6UASCMt0FDQS9lGKEkKvgVaEwxzQBFFYSSMATxWzbWEFvgkDdVWG4AWplug7YzQBsxSquCo6VrW98HLH1rm0lKr1zT47poxgGgDcnaKWNwBz161hSyZueOMdaje/dW+X7xqNILu4zhCoc9aAC6laVwiLuyOaxtQSRJlypGK7mw02G1iDMQ7nqTWRrtusjEqBmgDMtHyErTjYY96wbSVknMb4GK2LbdKwxQBfVjUgGRUsVk7gc4q9Fp6KOTuNAGWdwXgVCTkncDmugW0Rv4RThpyHd+7BNAHKSSgHHNV2mGetdBf6OsmSmVx7VzF/aT2jkgblBoAl8zc1WEIH1rGjuzvxnmtKF92CetAGjG5zVuN+KqRAEVPGDQBdWYjinj+8TnPaq4Xmp49oGSOaAJkVs5J49KtRMq/dqsrVKigA4FAFwTjoKkWXHWqKhgc4p4c7vmoAsFst7Gq97crZwF5COeFFSCZEBJPFYV9dxy3BllwYwcKvagDJvbnzgeQ7bjkE8g9RSadpkkk7XV6Oo+Vfeporfy5PtrIjb24A7Vp+buVkXqy5PtQAuzahR41HGeKpSSFJVwDkdMVK2QCgYu3Gasq0apveAblHBoArRuXkG7g1rQMBgE5NZZPmP5hXAParkTAKKAL8kgC8GqH2xVcgtT5ZP3dcvqU7xyEqSKAOtF5H5R+YVlXdwhBIYZrlxrEwGDSi9MnXNAG5DcsH61uWshZRXLWxLYIrp9OIKAGgDnvHQwll9T/WuLxXc+PVHkWJx/Ef5GuG70AGKKKKANHRyBcS54Owf+hCvtPw9z4b0vP/AD6Rf+gCvirSwxuWCccDP0yK+1fDuP8AhGtL/wCvSL/0AUAeb/FmKGXxV4PWYb0NzJ8v/fFfNt+MahcDn/WN1+tfSXxdKx+IPCk392eU/olfN+okNqVwR3kNAFWiigdaANDQBnW7L3lH869JcfMa848OqH1+xGcfvR/OvWBaJuw3WgDLxiithrBCBiqsliR0oArW1v5zZ2nFaUlskcYG3kVHa5iXYOtXwBIpB60AYUxQE8YxVQtnPOKtatEYcOOmTk1lI7SuAP0oAssCBmm+tbNl4fuLtFckhfQ1el8MbE3bwPxoA4+bODgZrFvVwCfWutv9IkhQlDke1crfKRlSMkUAc9cDBqKKMsw471ZnXLY710Xhjw3JqEqyOpCZyMigBuiaFNfSD5CF7nFel6bo8VnbKsagt34q/pWlRWkQRFAx1PrV/wAsRglRgigCGOBUUFuCKjuXVYzI3AHQetWcFhuaqd0nncfwjtQBgzK91Nv6LngVegtyqDAHvVgWeFyMAelPiVhkUAJ5QEfaqUytg+lX2IztB5qpOCCc9KAMa4kVTnHIqFLqOTO7gjpU9zGrggcGufuVe3kJ5wKALd0FlRgrDdWXIC6lGGCO57057g8Mp/CkkfzIi54I/WgDGuIdpOTVERFm4rd8tLmPJ6isq5RoGyvQUASwMqYXPPvWjHIDkBAawludzfOOPatG1dcAxsVPqaAL5MYHzKfcHOKes8ORsjHHpTFckfvHVhSpLEPuKPegCbzCw+VQp9hUTyycgkmka7jQ7sfhUD6m0jFY4ST64oAa1tI7Endg0n2AHrhvqcUefO/Dtt9AKGhkbmViB+WaAIzEsZ+YgEdMc0CJGIOfzFSrC3SMY/3qACpyoDSDv2oAe90llHhFDSkcD0rIuWZB50j75nPT0FPmkWOYncGZupz0rOkclWbd82e9ACGRpW+Y/SqztliKkQncDzj1pojLHPrQAznGew61asbGe/nSOJCSxwKI4RuAAJY8Aepr1jwV4W+xQJfXCfvWGVBHSgDS8KeHk0OxUlA0zjnjpXa2tksaB2Xk8020tiz73HGKsXcvlLjcOKAILm42sVB4Fc3q+onZ5Uedxq1fXvJC9TWZFZtcShjnOeKAJtMs/KxK4yzetdHGNoAFVILRljUPxip5HEUfBFACzyqi56VzeqXBZCq859KsXl48mUUH8KbBaF13OM+xoA8h8Sx3T34LRtt3VuaLZFoFMnPFdvqmjW1yuSgzjNc7GVtJGh4G3gZoAkWEfdHQVPFbKzYxTIXDNmrkTcmgC5b2kWzBAqyllGOwqCB8DmryHKDFADfsqY4AoEWOMcU7eR2NPUljyDQBRuLUN90VkzabM0pw525rqgq+1IYgx6CgDFtrDy1UnOe+astFtbpxWl5W2oJgKAMqXDMVxUewAVYkUAk1WdsA0AVJly3XpUHGadK5DHNQB+tAFhggXJOTQCoXcefSqryggcVDNcbQQDxQBYluRjk1lXNx83WopbkklRkk1HHCxbLnOaAG4aRsmp0j289qlWICpljGDQAwp8vHBNKV8xQAPmFSMoJAzTsbMYFAFUxnOBye/tUNwFWMgHtzV5iE3MOp61m3Q4XB+92oA5i/T9+cdDzWZ0lx71valGBMo9uaxbhNrkigC0IC0wVSCzDJqaK3d0kRV+7zRZRJNNblW2k8MTWjDb7dQ8qN+pwfSgDHuIMxh8Y7EVUVSu7jOK6X7MfMnglTp04rJktiG3LyAcGgDPyUfcOnpViNvOkB7mieLaOhqK1fyp8npQBfMBPIHSmLGXz2xVlJ15PHNIrDBIFAEW1lHAqWE7BufimOzP8AcFNW1mmwGfAoAsG9UNgPn0Aqe3iu737iFBnqasWWlQKMtgtjua04Q0KlRx6YoA09H8M28ZElw3mSdeelad9AkePLUKB1Aqhbai8AG887etVJ9VaW4ZSw29uaAHi4GXUdF5rD1OYlxlsA1I1yVuG2glj/AAjvVpvD80sH2iVvnbkKegoA56HTXuroOMhc8n1rr7CxjijXC1Bp9sF+RuGWt6CHgZFADRHjA7HirKWoXoKsJCvGRmpADu44FAEAgUDIpRuU9OKsBecU7C5I4oAqSSA2xjZFyTkGsG/tY5VZWGc1t3Q4O2s24jJizg5oA4fUdNaA+bEuBnpVe3uOQCea6G9Zg3zYK5wRXM6jALW4EkfKk54oA3beUstX4wVXJNYGn3JZ19DW8h3NjPFAE2409SajDc0pbjigC0kgB5q2kqgVjeYc9cVMJsY+agDV833pGJdgB371TSYGpHuRFCzHoKAItRkKQPEjjdjk+lVI44DboJFO4DOCaHkEsDz7Sc8YxUKWk0iiSV8f4UATTRFoF8tcbDyoocBkWNDtbb8xNSovlxbVfO7GTmkkt1AG2TvgetAEcbKEVFVgW4LGpvL2qQXOSMjNTNApjSMMeOc4qG4iWQI4l+4MGgCvAXdGZuinhfWrSEADBqFWHYdKcoO6gCweV9aw9Wt8jIFbyDCmq9zCJUxigDhZoNsmOasQQjitW6sMHOKbbW4zgigCa0ULxjtW5p0gU4JrIZfLIwKlhdlkBBoAZ49/49rE/wC2f5GuE712vjObzrCxP+2f5GuKPU0ALmikFLQBp6FIiXkpbkGPA/MV9o+G/wDkWNK/684v/QBXxLYSMlzkAc4H6ivtnw2f+KZ0vP8Az6Rf+gCgDzz4rpE/iPwik6lomupS2PT5OK+ab/YL+42Z2+Y2M9etfT/xNiM3irwfGNp/0mTIc4H8FfMOo/8AISuen+sbp9aAK1FFFAGj4e41+yP/AE1X+dexOeUY968d8Pj/AIntgPWUfzr1/eHg2/xrQBewpQEVBKy7WHtSRygRY/iNMc5G0rljwKAK2NrZFTpORjA5oaMEBRw1JGhWTBoAluYIb2Dy3GCab4d8OKkztKQSDlc09S2fl/GriPJGd6cEY70AdB+4sowDgH0rOvNShdyp2gd6ztVvXbT2YKd6jqK86udeusurKw56mgDqtZ1SOA4BBVj2rkb6NZg8qHr6VSF1cXoJwWA6Cur8O+H5bkLLcphAQdp70AZHh3wpNqU/nSpiIH+LvXpdjpsVlEI4kwAMVZgtEt4VRFCqOwq8q4UHdigBsahEGaa+WPtTiNx65oZNo4oArkNnHakaMY4qzj5PeodpFAFZkPeoz8hqy4PFQSr+dAFO4Oxt6fjVSe5DMv0qe4DAEA1mSId30oARyrZPesq7hEpO6tBlYDNVpB5meOlAHPzw7G4qu7MFx2rUuYjnOKoyR5B4oAzzK0ZLr26j1ougl3bh14PcU54DuPaljjMWWA3etAGM0BRvl/WpEkaLBY5PpV68UTLlV2ms3y9jnPNAFoO8hBLkCpguMbJDmqiyJ6hcdauQqgXeCWFADlt2blmqwsSqvLtj2qD5nbg4WpldYhhWJJ70AWkG2IEKkY/vMeTUclygB/5aN2xVZFlnc7xhKdLc29kuFwWoAlKySgNI4jXGazLzUdymKEbIxxnuaZLcTXOXf5UH61SUmeX5VJ9OKAGl9oznOajKtJ0XrWilh8xeU/gaHdIjtVB9aAKgtiiAs2B6U13XlFHOcg0598jkNyKmsrB7y7igiHzMw6elAHWeAfDv9p3gu7pP3KHjPc17DBCsaiNR8vTHpWVoGmx6bp8MCIB8oJx610NtETJuPegCwg8qCsXUrj071qXc3lowJ4rnp2+0yBV7GgCOK1a4kyRxW1baesaqT2pbG3AAAHNWL+5WztizHkCgCC7mjjUrnmswl5T7VGkpuZBIx3A1qRRJtA2igChHZKWyasGJY0+lTuAoJHFZV9eeWhGe1AFDULtY2b5u1efalqiG/YZ5zWhrmsqiP82W6VwT3DTXDSknk0Ad1Z3fmBcGtdXxg5rjdNuhsXJ5roY5iyqd3FAG7HPkCtCK4Cp71iW0iMAKuoO+eKANNJSRmrMZzisyOTtnpU/2nZgbqANHKBqcZFArKN1zndSi6BPXigC+Zs5qpNJyaiNyBnnFVZbjr81ACyyVSkk6mkluRVK4ugFIB5oAinm3NioDMEqnJckMec1AXlk7mgCzPd4GFqoGlkbk8VJHbsTzzVpIMcYoAhigwcnnNWNgBHFTpCQRxT9p34NAFcYGeO9SHmggmQqKkWFsEk0AMRATzRJkcCpcYb8KjZg7EAdKAKj7mfb2FQyIGnz/AAqM1ccfKVzhzzVNj2I68GgDCv0Jbc3UnP4VkXi4Jro9QjG3cRyOBWJepz07UAM07YbiAZP3ua3p7ZF1FfKfAyDWJpUe64iDHbhuTXU6hZQLcQ+XJnco5z0oArr5kerEkbw3X8qqvBHJcTqQU74rRmglt9Ti8ti+MH60+YL/AGmxkhwzcEfhQBzk9tvXh6yJ4Hjc98V2iW6NK2MbQcYqhqGm7zJIq8D9aAOYWVhwanSVyOKfPZFcMucH26U+2QY+agB6M6rUqzEKPWl+WmBQzkdqAL1tdndzWib1Qo9awXmijGAeR6UsC3F64CAquetAGu95mM8nPpTIIru8k2wxNz0YitzRdHtY2RpsSN3zXXSpaww5hRFwO1AHM6HoYtn8y8O+Q/pWxespjKrwAKhku0Rl559azdR1ILGx7kYA9aAItNl866lcr8qnb9a6CKNyBzisjR7Yi3V2+Vn5rX+0MJAgjzjqfWgC7GpUjcakABB+tMX5gCal2gDgUAMYgUzrSsAW56U5wqrkHFAFORQMk1n3cyqlXLmdQjc1zeo3DeXkNQBnXsoYuayLjEyNH3I4p93ckgBe55qmkhecY6igCGylMMhRuoNdLazbsVy94jQ3AkUdetaVhffKA42UAdGp3c0/IAJqnDdKwwPzqTzFPG7NAD8BlJqJWIY7qRpQowp5qOWQuVx17gUAWo5+DzT5pWOwgblH3hVWJPMjkG7YwHH50slwsaBUYuxG3j1oAmMsvnLGAPK68VMXmlYxsoAHTFR6ZaO0hMrcEcg1ZgjdLzLD92CRmgCWSDyrTaRufGeKiZgAjhCMDpUs11uZZg2WHyFOnFMmYJFvIKgnkDmgBbdmWOWWZ9pbhRWddskaHDE4OfrVtp45JmGcxbeCexrBupHlnRUbhThVoA1LVi5rRSPvVTT7WRE3SYB9Kv7wB6UAPC8UbaVXUin/AC4oAzbsLjpWcMK+RWvOgYVQkgwc0ABXcmagQ7WqQyFVABqGU914NAGf4nk3WVqvo5/ka5et7XZC8EIPZ+PyNYFAC0UlLQBYsx/pI/D+Yr7c8Pf8i1pf/XpF/wCgCvifTkDXRVmxgDH5ivtjw5n/AIRnSs9fskX/AKAKAOH+JEKz+LPBsTZx9qlPHXjZXy9qLbtSuWxj963H419K/Fu4ktfEHg+SJ9ji7cZ9jsr5q1PjU7kZz+8PP40AVqD0pBSnigDU8MDPiPT1PP75R+tetzRm3u9zDCV5N4W48Uac3/TdePxFezX0RuFOR/8AWoAxpZXinL5/dnpVm3kMhDt931rMmnMc/kS/cHTNOineJj18o9KANpX37gq/jUQcKwyct3ognXysKQM0ohYozHBx3oAmjizyxwDVldqg7icY496owTKNuTkDqKtbg53EfKOgoAmDoV244PXNVbnTLK7jKG3Q5HUCnllPapY2wMKeT1oAyNK0C1tZnXYDg5rqIVAAUKAB0xVL5BwThh39aIrmWNycfIOuaANcOCoBoJyfaqsV2spxjFWVIPSgBw45p5JNRMemKesnbHWgCRRmkkXHagnFODBhzQBWdeAaqzDgn0q7Ic8VWkHBoAypSWOKqSIcnitJ0GarSqRnFAFAp6jiopIlAyO9WWBbNRyp8ooAyrmHcpxWc0BA6ZrbZQQc1V8nPXgUAYslqx7daj8h4uAOD1rd8g59qbJb96AOXlTczKVxWfLBtzzmunmtFYk4rIu4dmeKAMlIl35ccVYacLxkKvoKrzOeAOPWowV9CT70AXklZx1wtOWdEyp+6KqvOqqqKOTSMC/yjHFAD5r+TJWPJzxVeNMvmTLMTwKtwWrE4wMdzV9I7a2wcBmoAqw2DSjdMSF/u1MGhtBtjQZpLi8Z/lQYFViAMFzzQBHPPJK5yePaoN23JaknnG8heKh2SSnjJzQAGYkEqOM4Fei/D7w/Kpa/uo+TymR2rnPDegx3d2Gl5Re3vXs2jWnlwogwFA6CgC/BCFwe3Wr4wkZYHHFCLgfMBjoBVXULlYY8Dg4oAzdSuedu7k1DY2565GTUSxm7l3E8Vq2MO1sEdKAL9uBFHyea5/Xbne3l7s1u3DBIzzXH38m+95BoAtaXJiQKQSOwrd5HP6VkabEpkD5FbrLlM0AZV9c+XETzXAeIfEYtoX5OeQBXe3pUIwIzmvIfH0XlvCVGASSaAOXuL+W6lZmY4JzioUkGcVDGeTml2nOaANW0n2MOa6S1utyKM1xkbla0LW+ZepxQB2cc5jOQavxahlBk1yMeo5Xlh+dWI74YHNAHXLeDs1K14D61zCagM4LY/Gp11BcYoA3muht+9TUvCo+9WA18M4zTDfAcZoA6GS9P96qz3nB+asRr0twDUZd3PU0AaUt63Y5qo8zyMeetMSNu9TJHzQAxISWyxzV2OEEDApEQVcjQYoAjWHB7VKkR3dOKlCjbnvRGT+FADgq/lURGWO0VJuDsVUc96kjQR5z3oAgjjC5Lcmmgk5qY/eNR4GcUANK85qIhd+QMVYbKgcdagkQ5BxxQBASdze/T2qrKmBx1FXnT5ulVnHzmgDMuwZIOmOe9Y16vHPpXRXg+XaBgVg6kAoGKAI9HiUyksflHHNdVf29uUtzC+WZRk+lc9o1t5sMjE/Lmupms4msrZonBYnkZoAhEUsN/CpB3DByeatM8V1qmZFwR3p9rJIurjzwCI16H0xSWwtrq/upX+VQrbR+FAFVLeJpZVVsck/hTjDC4OCXB4AFXLeIfZsSLz/Cwp8IVndBGBgjAHWgDB1WENbpAIlQKc5xyTXKXNtNbOxHzD2Fei3sLSJ9wYB/Gs82ituEkQxjuKAOEWf1NTJIWPpWrqukQs37j5T6Viy2U8KZJNAFzy4BhiMk1o2kyRrwMfSuc81hwTzViK4YY5oA6yDUWgbcW4q0+viRcEnnjrXKi7GPmOajN7GpIVSXPSgDobjU1UKS2SeAvc1c0qymvX+03SEID8qmqWh6E0zrdXgYnqqntXYwKVXbjCDoKACGLk8fT2q5EuwcgHNEaj0qdVBNADo1OMk1ITximLjseKk2cUAREH0qGUcHJqd93rVeQEgk0AZN6AEbFcrqkpSPrXU3oIVq4vW5CqGgDFlnJOSe/SpLI8s7CqQ+d9xq2jhV9AaALbxG5ljVB0OTU0lqrqU4DDiq+nO7yOUPIFXbdtk7GT5jjNAGcZ3s5TEWyB3q3DdfKGLdaz9U+bJHVjnNXdI0mWbbJOxSM9AepoA0LaOe5kAjXJP5CtCaGGyRYw4advvHstWxtt7f7PAm0nrgc4rOdGmlypBTqQT3FADdpluS6HciptJHrmpo0S0bCKCzHJBPNBxDFtClC3zHH1qJBunMzMWYrjgUAXYryQTyAKBET3p0fMrsZGWPORTIrYRBHZiUPJJNPlIPKqdntQAx4WluVkDgjqT2Iqw7qWSJR8p557VXQNIIwuVwx4p5ZxHgKCzPgEdqAIboxE+VFwuefrVCK0IvVUgluo4rVksizs2QNmC3PWpoYoAjXTzESNwqigCSOLy1IJJNVZZsHk81I0rYPzVRdWkf1oAtRzk9M1Y3MFzmm2loSOQavPa4UcUAZzzYGTVaSQyfdqxdQHOBUUFq2QKAKxjY9qckDN1FbUVoMDIqf7GoHC0AcF4ig2RQnpl/6Gub65rtvG0IhtrUgdXP8jXFHg0AFH1oo+tAFqxZUuPmOVIAH5ivtrw4MeGdK5z/okX/oAr4l02NWufmHy9vzr7Z8N/8AIsaV/wBekX/oAoA84+MiF9V8IBVBb7a3U9vkr5w1Ng2qXJGADIelfTvxMmgh8S+D3uMeT9rfO7/gFfMOpZ/tO5yMfvW/nQBVoPSikPSgDX8LceJ9OP8A03X+Yr3CTlcjvXh3hj/kY9O9fPX+Yr3Lbt6mgDE1LTBcbiv38VjQNJauYrgFlrrtqs5OeTUM2kpLl8ZNAGPHGsq74HHH8NWY74ohikXB6VVmsHtpS8JKkdVpwkW4j2zLslPQ0AWFjK/OOhqZZSRiqvmSx7UOSo7+tTQsjSDnn0oAsofWpGIQAr1NRGRd20rg0jdflNAFqP5xk9amIIxxkVFByANvNWThH2sOCKAG8bsn5fpSxXWwlc5HrTJFJPy9KhwqMQ4xmgDWSZSgPrUyuDWKZSq4Rs0R35RgH6UAbTtnpTQ+3rUMU6uu4HIpxcMc4oAk3Z5qKYZoLYHFIz5UZFAFSTiqz465qxK3Ws+QnJOaABmB6DFRsMg0rNwKidztIBoAiMYJNQvGNoqYHC9eaglmVEORk0ARlgBgUn3uKbFiSMv703cQetADJYgHP0rDvIcua25ZQTmsy6wW4oA5m7tsSE81UXaGAyc1u3O1Qd2OayGjRmJFACqsAbdklvSrKgsAQgGahjCoMquaf5z9xxQBMuU+Zm2gUnm5JZF4PVjVdQ8kmMce9OlIU7dxPsKAB5VDcVVmLyuOePapRDk5bilLxWpLLhmNADBbBfmkIwO1KJd52W64PTNQlZLmXc7bU9BWhb6fe3KlLO2ZVxy5HJoA6vwtIIbfa+N+eSK9I027jRR83avJ9M0+60uAtJIzN1I9K6PTtYDnaTyKAPSPtSlNyt0rCvrh7icqGzVaPUFFs2DzioLG4WWUFjk5oA6Gyt1ESk9a1oI1A561n2xXC1daVVXI60ARX5RYW56VxV9dxyXqojDd3rZ1zUBFCwLY4rhdO3XOtM+4mgDt9NbBGa3lfcnFYNuuxQe9a0Eg2CgClfodrEV5L4/BIT617DOPNJWvMviHZqlmHAAINAHl4OGqdTkVDgU+NvmwelAE6LmrCRZxSRLk1oQxrgcUAQrBirCxsE4NTiPuBT0jz2oAxL9popFIYioUu5j/AMtDWnq1uTbFwOVrDQlSG7Y6UAXo55S2TIa0IpdwGTk+tZ0JDjFWo1ZCPSgDSjq9EuRWbDJk81pQvwKALKL8tPVeaFcBelOVsjIoAnjx0P4VaiAH3ulUU3FvpVwbnABNAD3de1MUu5wOB608QD1qULhQOwoAbH8h469zTixLmkAORgU8jHO2gBuKUIvXvTsr6UzaSzEdKAFIG01XY/Id1WMEJk1VkDSHAOAKAEPOPpVcr94+9WCQOnNQyOEViRwegoAp3IDYBrntSwDt9K6AqW/eMflXk1zmpOGlZl6E8UAb2h6dI2i+eCoUtWlJaPDHZFWyHfoPrS6Np0knhrzDJ8o5AzT4DPBLZzbS67yFB6UAWLeeJr24edMAIQD+GKqw2LC3eZHGHbA+mavLHFcNelmCPsJI9TmoY4TDFaRvja+Wzz70AWbaHDBHkyAR1rTe0gVxMdpiHUA4NVrdAJieqHoe1WJEBl3R4bC8emaAK88IlYNGwRc52n0qG4svNbEQycVoyohSNSmHOOhqJyVidgVGDjrQBzr2iiWVGQEjGT6Vl3dkWTBj711PlozO+QrEdaqThBGgHJzz70AcFd6bumCquDms66tJbYlQDXf3FlH9rhbgK2T+lZOp2QeJnVc44JNAHGZfZjuTXT+HNF80i4uOR1ANULPS2uLpSfuKeRXb6dbBFAUYUCgDUiVVVQowAKtxjiq6AL1qVXA/ioAtIMVOgBBHrUCEN/FVlF/GgBVUKu33pXJA4owN3NKcYPNADAMrUEwwKm3YWoJTkZoAyL04DVw2vnlq7bUH25rhNefJNAGJGO5p7uCpAqEOemaQnrigC3p9yYpCo/i4rfRCx4xuxx9K5LO0gr1HStG11QxurNncBjNAGrcac4ZZSoIB4WtRJkhtzIBlwvC+hrIi1MyODvLPjC+1allo893CJnk2liTjvQBC95NNJJGjYZlUhh26ZqaXMcaCLJJHX3rYtdGihxHgEEfMTSSpFAQowy5xigDKXzZIkjkz5jnBPoK0XtTAVUAZ24FTxiPc0Sxnd13EVZms2kXzlf50XgepoAzTGzDy8naB81WVljjiT5chRzSXsw0+xQTD99KMk1Rs7xDDIJsYPSgB8k6uyogO4nOR2qfyiEwuS5Pb1pbSAlXZF+Vhjnt71oQqtlCrthpP4R60AUmtmuEaJWKyBfmJ9ariTZC0O0ZC43elNuLh578KxKAnLAcZq5MkMaMqKOQM0AVEjLoKsQ2h3A1LbR/KARWmkSjHFADbeHaeasyRjApVUDtTnG7igDOntw7ZqHyNpzWq0Oahkh+WgCtE4DYNX1AZRWcYirZqxDKR1NAHLfEFNtjZ/wDXU/yNcA33jXoPxBYPp1oR183+hrz7HPNACCloooAs2KO1wuG24I/Gvtvw4c+GNK/684v/AEAV8UaWnm3yqeQOa+1fDRJ8L6UT/wA+kX/oAoA86+MUCXWo+EYZZBGGvHy3/fFfN+qcapcgHIEh5r6N+NUbS6h4SRAxc3rYCjJ/gr5w1Eg6jcEf3zQBWooooA1vCy7vFOmL63Cj9RXut4gjHIrwvwo+3xXpf/Xyn8696uP3smAPrmgCjGu3DetXFkZcZIAqB4ismAOO/tTinydyPegCaSzhkHmcbvU96xNXsVldfLGGx2rQWd5JQgPyL1q4yxStxjIFAHHxz3FqfLnTcvY1PGglJe2cbvQ1rXFmCrGQbgemKwptPubNzNbP15xQBKJWDHzWwR3q9bujYwMms2DUbd4zFdxkP6471JGpD5gfI7YoA31O1c5AxUysCdzEHisI3pxsb7w61fiuFKrzQBbmcMCAMfSq5j3kCT7o/Wh3BY4NTLExUMeQaAKrRsjFl+76UzYZgw2YrR+zMw9qikXyCeRnNAFWGR7dgvY1fWYMoIOapEEgblPPfFRlJLddwJK0AaYnAB5pDPuXr0rKW5Vs+tPScUAWJHBzz1qs/K4p5ZT2qNiM0AV5DgVE4O3Oamm6CoJGwAM4NAFdmbOKYU3Z3DOakYMT1pQjHuKAIhiKEjFRFS3NTSp8vJFDYVAB6UAUZeDWdcnDVfn+9VG5xwaAMW8yWOTkVUtYGnuBEByau3KF24rZ8K6T59xJcycBeBmgDPGkyoMBfxqOW1KYVsZrrtVeC0hI71xtxN5k5YuQpoATy0T7zflUDGOMlgMe5qGW9SEMVO4+vpUdnZX2sS4t0MgJxyMUADymb5Y/zq5p+gXN8ykISCcEmuu0fwhb2CLNfN5sh/gH8Nbh+z2+UijA9CKAMSw8N29id0sYd+2egrXijij/ANXhX/uAVO53KF284yTUcaLvHXeDkt7UARyxAyYOAjDk+lcxextp85ZD8jHiutnljUB1XPbmsq+g+2W0isvz9VOKAILLUSY9rk5qa2vvJueWOBXPxsY3Ib7ynFWfNEnzDqKAPQLLVFkQANzWzbXIkTk5rzjT79UYA5rqrC9yoIPXtQA/xDbmWFsVzWkQi3vCzCutu51kiIb0rmLpxBNuHSgDqYpkKCrSShec8Vy1tqIKgZrRjuy2OaANgS/MTXnPxFM89qFjRiAcnFdzHKTUFzZQ3cbJKoOeOaAPn7B3YIxSKfnrpfE+gPpl+xTiJjkHtXOMuHoA07YbgK1II88Vl2fOK2Lb74oAuxxqVxilEJB6VZhjzVkQE9KAMm5tWkgZduQRXHzxNDO0bdM16P5DAe3esDXNH81TLEORyQKAOeg4ORWlGQVBP5VmQfLJg9uta0EYcBgQc0ATxhSemK0IFGBVVICcYFXYVK4BoAuRKpGCKlEYBwKSEVaRMkHFAESAc8VZhxSxw5zxViOAKPm4+tADMBulO2ECrCLGOgzQ7A8AUAQBdvNDMSKew4qJqAABT359KUDA96j27TnIx+tOzk5oAOTwajkAHTvUucioGbnk0AQvhOlVJzvIP6VNcPgdapmTapYmgCC6l2xlRxxiufucPPtA6YrUncsGY96zrceZeoCOpoA7vRrGWTw5IwYrt7ZpI5Z7aC0lePfDHJV7SLOZ9NnSPJTaSQPpVeOdv7NNtIo8pJQcmgB8kUU0908bbNyZx+FLbqzRRGRsAJhc9uakv4YXvFFq2wyRjrx2oiDLAhkGRGCrD3zQA4QSlgIvnReWbpT1hfzgu9vn+6MVJbvtlDI4ZCOUz0q4+Uy4XOe57fSgCJ38vB5BQ88iq8jM4DBvlB3MMVNJHLNHklQBzjHNQuyFdqkDA55oAqkQTMzgnNVrhFRhwSw4GKkb5GIUA1DI6i6DMCCB0zxQAtyp/cwxKDJtySe1V7yFRbNuw3y8/WrtsZDe72HGw4zzUWrBWECxkAY+YUAYtpbBV+VcFjW/AgiXHtVS3QB1bHC8VbYZBxQAM2WxT0UlsCiKISOORxWlb2yhs0ANtkweQa0FXAzSBV9MU4HjFADdu4nApoXPUY+tSYbI2nHrTWO7aO9ADCoAqtPwMVaY1SuW560AY+oMOQRXAeIDh8D1rudRbqc1wGuuWm/GgDJoPAozTSeaAHKCWBFTbAF5606JQBmnSLnkUAaehWamZpnGVXmurj1BFcbB90cHtXL6PI/kTAAldvatS3hMsIKA4oA1hdO6yGNjuan2tvudC7dOT9ajsbSTAIBODzxV2cfZ1CgEs3PFAErXKxzDbETjg8VPDKLtHb/V7OgqpbjcXJB4GetQmby3XacAnmgAu4f7QXE3/LM1ltZRxP8AuwzDI4q/dXSJOQzY3dMVHNcNFGCFGD3oAjl1JS5t1YJJjGAalgmmhuITcneBwpFZTaaHvGuNzbyOK1dMkjhlWK6O/eep7UAJcW5utVG0jJ+YkdqkC4lKk9TjNWL63Wyu/Ntm3RykDJ7VCEKg7vvbsigC7CAABWjGy7RWQjkVYSb3oA1AwqRBVSF9x5q2hGKAFIpjDNOc471AX5oAa0eaj8kirANSKAe1AHD+P1K6ZaH/AKa/0NcD9a9J+IyhdKszj/lt/Q15uTk0AJRRRQBoaNKIb8MVLZUjj3r7T8Nf8ivpXHS0iH/jgr4r0Yf8TBcjjmvtXw6c+GtLP/TpF/6AKAOJ+JmP+En8F5Gf9Nf/ANkr5e1P/kKXWBj96386+l/ixdSWuueDZkxuW/bg+nyV80aoc6rdH1kb+dAFSiig9KANbwr/AMjTpn/Xyn86+gmQA59a+fvC2B4n0s/9PKfzr6AmdQeT2oAadq9e9VJnLsEjpjzmRgqnPNTRxLCN+75vSgCNYljjLD73eo1crLu7GpmZWBxwT2qNIic7ug6UASqyyrUclurggdTQqbAecVIFIOd3FAGPe6RHMcFdrY6isJrW80ucuhLxjtXcBUc5PNU7i2wz7FBBHegDnra8t73iQbJDVvy2hOFcFfX0qC70fYRKoKN1yKpLcXNq+ZxmP1Hce9AGxbSBpsbt1bkUyLGFPBFc9H9nuDvhbYxHABq1++gUFwWUnIYUAbryqF+U4OKoSSbmJcZzyKoQXn2iX5XPHGDWgMcbzjA4oAcZFdAMYqOeL5BhvlPWpHKBOgb3qBCJCw/hx0oAy50ZX/d9KVXKj5qvyhFQhQAcVnTKc0ATibK1GTk5zVRZWXOelHnjqaAJ53KR7upFUhKzzbiKm80MMZ4pGII4FAEq7j2FRudjHNIpK96SQgigCN2BHNNLgJSsBsNQSjjA9KAIZznmsu7fJAq9M+1cZrOYeZLigCLy97DjIrpdOmFrY4UYrNgtv3ecc1pRKEgwRn2oA5PxBqEsrlQWwDWAZnkOAHY9gBXW6jarNlIk3yMegHStjw74USyVbm6UPIedrDpQBy+h+ELnU5RNdsYbfrzxXo1hp9rplrst0UADAYDk0+QbpcMAkQ7L3qzujdgV4AFAEUqKY9wJ3d6gSNFYzSHgA4FSmZPPU7/kxlhVe52kb1+ZSCAtAEM98qAlRzzT7d2nAccDjI9ayp36qi885/Ktmx2bUDnAGKALPliSEZVQAetVnTO4rgoo606XEwdI3IAPUVAp8pWjQ5Xv9aAOX1G3+z3RdckN1qAKeGU4Het/UohNEFUfNjJ+tYUalJDG9ABHP5c1dFYXo2r81cxPEUfcOlWbOcAjmgDrXvSVIJrJ1CTcuaRZCy5HNQXpZ4sKtAFWG5YScNW3aXbttFcxGDE2WHetmznG5SooA66yJfr6VoeTkVlWN0uACK2Ipg6dKAMPxJokepWGzZlwMg14pqNk9jfNHMCMHFfRpGUI9uK4PxN4RTUJjIgw554oA80tGUYC5rUsmZ5jioL/AEq40yby2ibHY4q5paMnzFetAHQWo+QZFacaA8gVm20hOARxWrBcIPlIoAaYgT0xUE9qGBq5I4b7ozULM5420AcBrtj9ivBJGvyN96mafOM47V1eoWguoJEkiye1cWY2s7h42JBU9PWgDqbZkbbWlHDGzds1zVnc8A5x7VsRXHAOeaANuCNU4IBq0ipu6YrHhn5q4k/Gc80AX8IDSSEPwOlVTLkHmhZCTgHqKALIOxcClyQFJqBWIHzHJpWl4APagB8hyc5quxIPWnlxgZ5qu7rz9aAJM7uacG4qKNt3AoZgG20AOLkdKqySjBJ9akaTapJ59KqOcjJFAEYYuzM5wvYVTmkDZx0qWeQEVWOMc9+lAFG5Ysu0U/RbZ5dRJA3eWCfpTJ1wGx1xnPpW54JZIZ7uRo/MZk2hfw60AdPozXQaVIt+GTBwPaqnn7ILmKWPOGBzjvmr2m3E8F0kiHGcgiq+Un+3iQDeWBHtyKAJL22jd7OeOT5mAyKiCXC3lzEQSCA3HtUt7bBbSymhkye/PTmpnnubS9VpIsmVMKfwoAr2luxKyCVcnPytwRVwyTRMFUGQfTpUVkE81t/DAnOavQRbJJJF4U9s0AQNiTIDMrY52moZ7YiEMGAOOcirEoIC4IBDZ5Haq167SOhU/KMZwaAKcNo07FnmVcHPPpSSShr5W8sgDCgno3vVho4S6MckNnIH0qq5H2sBzyMYFAFm5eC2mlkjfcAgzxjBPasnmWQZ79KfPMWeZXT5S3B9aWBcqPrQBOAFQYFSRqzEkLUkagnGOK0IIguCBigBlrajbkjmr8aBRilSMsPlOKkEeBycmgBpFOVRtzSHjrS5Xb978KAEYgDioSdppzHNRSSAED3oAR87jVG55SrjviqN042UAYGpAqjHdmuJuYxcTsHrrtUl2hua5dU8yTd70AZM9nJHygyvrVcHafmFdWLVDGD69qavhmTUZcx4jQHnNAHORrLKwWNSc9ABWxp3hq+vXO8+SMZG7vXW2Wj2mlOoEZc7eZCM0+SUmbcXKlTxjvQBTt9Mh023EbMGkxlwK0dM+ym2ZXAQs3yc1mTmVmnZJQ7uMZrNWG8ilXeWAxk+2KAOm1LUJLeT92EEcfUL3rPGpky+Y/fkD0rMEs6h3IL7zgD0FXLbTpLnewJDKM80ATXd8/k7oGyx+ZgO49KrQXTzxksCCOmalk0+e2jSZdu3vnrSW6LFHJvTJJ+XP1oAjDbZ1kmG8dAPStG2dMsku1kbke1ULiINcCNOhxk+nFWLK38m4ALbkHXNAF50BtdywnlsA+1Z6wpFNvctjPA9K14381yjtwvQVXnTKlfvDNAFK9vSqKhOVU7lNXUmWeCN24bHase6/eM0arwtTWLEKAzdKANQmlQ9KjJ+WkV+KANa3cEDNXRIoFZMMoAqbzsjrQBeaQGoXbmq4lB6mnbwaALSHIq1H0rPRiD1q5ESR1oA5b4kHOj2X/XY/wAjXmdemfEhcaPZf9d/6GvMz1oAKKKKANPQYTLqQTPVT/jX2h4a/wCRY0v/AK9Iv/QBXxv4V8sazmQkfI23H0r7J8ODHhnSxjH+ixf+gCgDzH44nbJ4XbJUi8bkdvu187amNupXA/2zX0t8XUik1XwjHcDMZvWB/wDHK+atW51e6/66GgCnRRR9aANTw023xJpjHoLlD+te8zgM4OeG6V4L4cwPEOnFvu/aE/nXvibWfcR8gPFAECQeSWJ5BPWnvExGWNOkZmJ4O0U6Pc6hSfm70AR7dq8DNKhOzGDVoRALyKqySFGICnCtmgBrR7sD+IdKcvz/AOs69ABS+aMq2M55FMjB3F25z0FADwAcbe9OYPtbPIA4xUEbeWAeoJH4U5p2UEjt2oAYHZ+GUYHGDVafTY5iTgcnBXsatpl1Mh4z2p7gs4RDyVz9DQBz91ovkSK8DMrjt2NQJql5ZfJdKCAeOOMV0kuWIBILL3qtJYx3u4uASKAKFv8AZbyRXRgjnnANTP50JJcFlzjI5rOuNGmt2kmgLDHaoLbWri1YR3AIXPpmgDYEg27t34USSMOV6kc4NRxzW14hMJCSegPWnCNwSsiED+8OlADGmABzkmiYbow6jNPdFCBT8wPQ05F2rtJGPSgDNZGAyR1qhPuxkflW7Ii7AKyblNrdODQBSiuBkqTzVqKYZwax7vNtcB8Eqx/Kp4rpSM+1AGoZATgUpPygZqitwpFPEpIzg0ASTSbY+KqPMSM06V8r1qrKwCgA0AMkfe1JEmJN2OKYMlu9PZmGAKAL+7bH6VdiiaeNVQZzVey0+W72g5Cjk8V01tapBtVAOlAFKx0+CAnKZl65Iq+GJHXBp8oCrvJz/s1E4BdNo2gnp6UALIEZV5AJHNQSzkL+6AG04Ix1pZVZDtA3E8CoWdrQPG+CzdAOtADWjXJYn53AwPTmokWUPnqemPSpC+SjYORnJppnAVQgPmsRk496AG/YwzZRMk5z9asG1bLtnAXg/lVuMSuoWLaCoBJ/Gq80wTcrnliBj3oAFYRgKqjng02URpGoXAcnOfWpVjH2dmYgENxmqZjLyMzH7v3aAILny4wMtmQ9sdKxb63LB5IhnYeSO9aNxLI8zBguQOOeajkA/hyFH3h6nFAGVxJHsPJx1qkVaCQema0HiMEpPZufpTHVZ4vQigC1bT5XGevetGAowCnDVzsE3lSbG71vaeq7sk9aAC8sUeNmUAVlwSNDJgjgdK6kxB0IrE1C0aL94BwO1AF60uGypz+FdFazsUA9a5KwnWTHY10dsx2igDdjfcBUhtDc3EYX+I469KowuauRSbXBJx3BBoAzNc0WCRmilRWK8BveuTbSRbuVKYGeDXdzqZsk9znNZ89t5g2sBx0NAHOW9qufuVdjtkPRQDUnklJCKtQR9M0AVxa56AUptiOcDitJYhnjFSC33elAGHLbqynKjNcxrOhRThpEX951r0CWyG3is+WwJJIHT1oA8l8uS3mKSDBFaEM3A5xW9r2iGUNKgww5OO9c3CmMo4IYHvQBqw3B9atx3A9ay4054NW4lAxmgDSWUEYFTRvyDVOIY7VZXpQBbUjcOabKck4FRr94UMCDnNACAsOvQUw/d+tKzEjGaTI2cmgBVG0ZzTZJAOTUElxzhefpTOT8z9PSgB24kE9qqzznaQO1LNPgYBwKzZJSzUAOLl6VQxBGM46fWlgTPWrAYYZcceooAzbgFep5PLV1HgXyYpXlcbmOQBXM3alyRjA9a7fwTDbWWzzl3lhnFAFqC7kTU9yxrsEmCKe7Wx1W53qUV0IH1xU9xJbRakXEWF3g4qXUEtpdUikRPL8wDPPFAGKbeVrLfG25EPTPvVu9kuTZ2ksoOU+62PSo7izlFzcQQuGUDPy/TNWY7qWfRPs5XLQ88igCvLK736EAAMFJ960GnYFA3BOcr9KpxzxTRwl4sOoxkfWpElBLgpwejE0AShsNluQRkGoZGjkhICc5xzUkbyrcM1xgRGPanHTis9g9vnYrnc/Ibn8aAJoiYmWMqDkEg46VSmVpbwMcbcYq6itOoYHkKRwfrVEOId+8bsdOcnpQBXdhKBxwHNWokLOMLxVK2Ut8xXAJPFatvG5cY6UAWoIhuzWhEmWAA/GoY0wauqdsXTkUAKq4qTaKFwccjpQSM4oAhkFQFVzyeatPg9DUDbfxoAjzj3qKUAkHFSMwGMVFJIKAIps4zWfcv8lXZpRjHesq8fC9cUAc9q7gKx61jW3TOfer2ryHa3pSabY+ZF5h6AA0AX7a2R4Ud+MfrWxDFsuEY52EcIKzYnKYLxgIThcmtuEGSFZThSp/SgBPtOI5GHzn7oXHSoooY/vlOF5cN3+lWrmDCJParkH7w96gf/WGNsjdg0AV7q3t2/e26bSpycUxbRrg7Zfvkc9sDtWsGt3fytoUADP1qSZoxGCFzg8UAYo0lo5B5a7wowakti0cxMibVHBx3q/FJmNmMhVj/CvPFLbxBnZljLx4Od3rQBEzRT28kBQFhyDWPcDMgQAZQjir8wURymNSrKckk9adJbxy2STrHiTvzQBmyBUVvkIdsZOaFHlT4HMeBk1Wvb9LaXMiY7VQttZ+03f2dBkP3oA6JCkU+5G3BqfM/lHc5AU9KalvHHCm7PmVWn3q5MpBHYenFAGcsm6efac5NTQJz83rUFhEuHmLAsxPAPvV6MiRsdMUATvwMVXMmGxU0hpscO85oAfHKcYqYSnFOFsQM4qNoyDQA9ZeasRvnFU9hBqaM4IoA0Uq/B0FZsTe9aMDDA5oA5j4lc6LZn0n/oa8x716d8SD/wASO0/67/0NeY0AFFFFAGx4bYprK4A+ZDyfpX2Z4cx/wjOl4Of9Ei/9AFfGfhlxHrablLgo3AGccV9l+G/+RY0vH/PpF/6AKAPP/i3bG91bwfbKxVpdQIDen3K+atVXbrN2uc4lYZr6e+J7Bdc8Hln2AX5JYDJHKV8waqSdZvCTn963P40AUx1NLR3ooA0vDnOv6cP+nhP51764At+DgivAvDf/ACMOnf8AXwn8698li3O3PGOlADPOYoOPu9ferCoEKyD+LqPSiONcDJ4ApYzl3wOMUAIZG37e1OYJtY46Dn3pdqnkEjOcUMVI6Z4NAERhDorIMbe1RS8gKvysOtWnJXBTg1E+1iS64Y5+agCFGjVAjdCetJ5AY8Hsab5W8bCMKG4NWEjUZA4IFAEIjZT1yBnimKjrM/POM5qbeg2jPzZ60BRF8zHNACR7UyWGSaduAi4AFRNJl8g45HFN8xPNOTye1AEihWLDOcjkGqdzptpcxEMgB7kVO+WYlRz2qMb3YMVCqDQBzc2gXltK1xYTfKvOw9TT7XxC0Moiu4zG44IYcV0i72k6fLVLUNOgvonWWJQexxyaAGiWG5JeFhk9Rnio3TB+Y89q5drLUNJdnhLSQg5wOcCtOw1xLw7TwwGCp6g0AXpA+7rTWiLrz1qyVBKkAMD19qbIAoG1uO9AGLeW6mJ1aublka1lIP3e1dlPGk3XoK5vWdPkeIsq5280AQ291kZq6J9yda5y3lKZXfkjtV1bkADAoAvNIwY81CzEtmohcA9qdndyKALK4QBs5J7Vr6fpn2gefN8qDkD1qjpFgbyRJnUiIHHPrXYCBIYAGHOOB6UAR4EaKYhgenrQszyna+U+lSIjvHnHzZGKRlYbfMAB9RQA7ylU4Usy4Ocn2pgJicu44IHFJLvSXCvhdpyKinuWlCooyfX19qAJmneUARqAR0JqhIN0vmyyfMSelTqZsqEIz1x6VIlp85L9V5IoAjcL5JkycYwoHc1EZ2EI2xfMvWrsRC5KqMg/L7U9IgfmYD5hz+dAFWB5zGWIxgGpI5y7AEKSD6VK2RHtjUHPDVEIFRN2MvjsaAEIxK3mt94DAHbmpntlitzub7wz9azZbt0ZEaM7nOAT2q3JcERFurdKAMS5bzGIRNnPBPWlhOAzMwIHanyN5tu65+brmo40AjyuN2OeetADLxUazeRmy3YAVS8tWjDoe3StO4cvZyRsADjjisi0cAYxkUAV5wM7wMMK1NPn3RgD71RSwq44TFR2v+izc9CaAOlgl4UGpbyBZosDqRVGN1ZQQeauLKQvJ5oA5lUe2uivQZrotPugRgtWZqMIaQsBz60y0VlPXtQB2sEqOg+YVayCcCuZtZXXg5wBWtb3RIxigDSD9u1PniQxAr1qmkhYE1KsvQZoApTw55C81GhMajcK1SgZge9OmtUZMlRmgCijBgMCrSdKqMphOQMirNvKkntQBPtytQNHnIq124HFMYd8UAZF1ZrIMfnXFa3oxgkM0Q9zXociZzx9azru1WSFhsBzxQB51E6bRgYbvVhBzhDk9xS6tYvY3O8LhD2qrHJ/EoxmgDWix3PzdxVjcBismOcqcnvUwuCcfpQBolsc0m9T8zHrVMTuTsNPZQqguaAHM5J+UcVGfMNTK+EGF47UhZiPagBgjULu71WmkYKSand8LWdczHB5oAp3E5JxUKMWNNYNI+BV63tfLAZxnvQBPAjJHyOD3pzBY4+KkZgAGJ47CoZDuzmgClMWd1UfxMBXc6Zbi0uYfNO0ALkD6VxcR/0uMAZ+YYHrXeSWFwrQTTDG9QRz2oA1NXFks25FP3QR9arak9vPYWskJKuDhqv39paHT4JfMJfvVBrGGXSn2TfMj5KmgCNoJYdRjKjcJE7d+KdYXRt3u4niXDArg0OtzbxWl2rEj7uRzjmmLIPt8rTxY3A/jkUAZ32iIW8vyEOJABVuMo0YEhIz0wKgMFu9hckMNykce+atWEamJQ4wsfIOc5oAtLscZb7oH51Tn2tP5fHJGTnpxVi686BELqpV8lfUVUkXgEA72HJ60AWIY1iSSPcBkZP0rDklEk7+WgCg4FbQXbaYJBbJzz7VhMI1lwo68k5oAngQsASBWrbrgCqVvGFXgcVoxcCgC2i5OanUFsjtUMRJWp4zgHLAe5oAeuFAx604lV+Y8ZqhPqMMe5Y/ncdcVkSay9zctASEXFAG5NewRE5kX8KzpNXgG5lO/aDkCskzW74K5O04b3qJ7iKJW8qMDzDgmgDSOsQNyEYDAqF9UhBOc8VmTSFCpKgDPI9RVaW+ha3kyvzHjK+maANiS8ilAZJBjHrWZdTh8EHIFYd7uhRngR93y7f0ofUJNiRhfmfCn60AJNC15cdcRg/nWqbtLK0jiiiAJOGJqMBLZFhWMOcDcfQ1C1t9rwFYlt2Mn2oA0VxcxGRgFSI5A9TiphK7Wca5xubtVX7NMWKEHanUdicVJHuSNexU0Aa9sVjtfK8w7j19qsOENusbLjn/AFh61WWSJpIpNvC8N7mrcjLcyhONq84oArPBGk3zMeowR3qzKo8lyR82MYogkVWfcgPzYGarTTgTeVGdzMcn2oAqwvO1w0ccfOMZNakDMVznAT7wHeo0ZR8+ME9TVWG4EN0TJzGxP40ATXcSzghEwG5NVojJCjQsM+gPatNbkGM7I8k84x0FZVxPLPPmMbc5BoA4rxaJI51Uk/N3FXfC+nW4Xe5+cDINaGp2kd5GI51+ZP4vWo7CAwWzOpyvYfSgDaec+aWZBhMDHrWTeSyFZ94AB5qZJLicKxAJzk/SoNRGxSWwc+lAGPZeZGxC5xj+tbNqCBlqhtYv3e/aAKtZAUYoAWQ5NW7IAkVRY5Jq9Y9aANfygUFVJowDV+LlKguIx2FAGfinKvepBH7UpXaKAFXIq3E5AqrHywFW1UbDxQBzXxAk8zRLVfSf+hrzivQfHnGlWwH/AD2/oa8+PWgBR1pabRQBt+FW262p3DlG/ka+yfDn/Is6X/16Rf8AoAr4w0BpI9Wj8tdxwR9BjrX2d4bz/wAIxpef+fSL/wBAFAHCfFe5FnqnhK4IyI74nGM5+5XzJqhzrF2fWUmvpX4yIZZfDKBtpa+Pb/dr5r1UY1i7B/56GgCn3oo70UAaXh3jxDpv/Xwn86+hgo3DKn1zXz14b58R6YP+nlP519DzMFcAZyKAIH+R8Ic5NLkKV+Ug/wA6kB+bLL9DTGG7lj0HGKABZGZVAXBGM0kStgBup/rUszeQj4UcCliIkViOwoAa2STtI461Wb7uDzkUrswc7WpybeQw+lAEYVhw33c5FNb5iSvUjFTSttOMdqitxg7j3NADEjGzceT0p83BCjkmpVAwSexzioucmQjvwKAIXVQ6sOpG386YYkCrKTlu9WJIQN0gIPPaogQV2jqKAEJZVG0c46mmwzu0jI6AADOalMbt+VRzHYUBHGOfegCeM/MTjJ9KZMjbg/B46UrnCfIfnIprt0JJoAobVjlYSDEbdawtU8PMrG504YfOcjvXRTrv6Hgdc96I2KICPuigDkbHVnhk8u4JSQcFW710UWyVcbhuIziotX0KHUIy8ShZQfvCsSzvZdMufs96DhPlVz3oA157chCoBDVSm3ICrrkYxitKOcTgMPrzVe8jeQblHIoA5HUdJVd09sQCeWFY/nFSVPUV1E6OhY85PUVzuo25VvtCjgdQKAGpLlsH0rS02F766SBB16+wrASY5Jzx3r0TwhpRtLT7bKMtKOAewoA6G2sFtLcAKMBcgCo7i9xdJCQMbttX2mUWuThcAg+1ZMCLc3L3Lcqh/OgC2zYDNnAyOKZOmIlIYnK5qW4RSgToX6GkaFoYwCdxJwD1xQAyKEyq5IIwo61n+W0dwhwQB8345q40svnbIzkY+T3NQyjfMAoOVGTxQBLZRow8z5t+cn86tTpIHYx4Pq3tVXzhCdgKgYzxUszSNCixcngsaAKqTL5uwODtbJP9KlfeuHZgq7v0qK0tWAbcBtALEn1pbmJN3zOxyOnpQA8HbCzsSVzxg1HK22MGN/wPemyqscSBXJyRnPQDPNRSSNc9QFUZAxQAx3nllDmNTGgwORmpYE2wBn96giDIjLkBac4ZoirOCm3AANAGdLE5ucoQI+4z1qWK3iX52LA5yBUU5MaKShULxn1qXcZNpAG3HU0AWGRbhw2MKBg5rmhmKeVAfuE/jXQCZkCqFPLY4FZmr2YhP2qFhnPzgnrQA6CQSLgelR3MT7c45HNZsN0Fl+9jnpW2GSaMHd0oAis5ztAJyR2rUil8zjp9a5oyG2vCc/KTWrHcblUgigC5fAlMgVQt5ykwyOAavfaBJFtPasuf5HJAoA6SGVZQSvGa0rYgJyOa4+z1IRNg1vWt+JRkEc9s0Abit8maikkKc1Gk+YxSyOCg9aAJ0vRkZOMVbF0JE4Nc9NGzNuBxUtq8itg5xQBtYyCTyKhaLaNyHBqaH5oaH4UUAJBdlBtkFWd6uuQwOaqFUYZIqvJG4P7tsCgDR2cGojGNhU9agivSmEkHPrU7yqcECgDA1qwWe3bK5PauFYeRI0TDBU4+telTyhvlIrhvE9qIpRcoO/IoApqQcZqxuQgYUg1lR3WOcjmp1u80AXi+DnHNSJIMbm5J7VSE+etBmXpQBdWXcx549Kk3tg5GKoRSgNxUzs+ASc/SgBJWBBGcEVmygu3Wr8qfJvzz6VUCln6UAOt7cEZ7jmr6ISgyOKLaEnAHGauCPZleOlAFGSIdT+FVWBLc9x1rRliyvHP0qv5GUAPBB70AV9NjJvi5H3ATzXoc6TXGlW10d2FTbmuR0yKPFxO44GFHvzXVw3k19oxto1wsZ7CgDTtLWG60mRnk+ZRwKrWVokrOFYN8uWUnrUnh+yku4JovNCEDJ3HGajs7Vo9SESOMkkHLUAVxDcfZbiEHITLgelVWvA/2eWaMkcK2PY1fjS7g1SaIDO7Kn3rMllZYJ7aZAGR+o7UAR3dvbtczrBJhWTcB6nrim2ZeWMLuCEevFWLy2jjFrNDKMOoX6GqdvKI5HDodytjJ6GgC1PJLOwV3I2jAz0qERMbglSy4UHg5zU8nlyuJegUdjxmo3Zw+1mXcQCAvXFAEWoym3t1ZYsFuTzWfDGHCsBjir2rssilckAKMVBbKEjA68UAXYACo7VdTAxzmqcRBQ5FSyTLaxKxxuPQGgC+0qIvLADHGa5++1Kee48pCQgOAB1pZ2knHmysQFOcCmrAFkDNz5v3CKAKqCSNyiPlz96pX00RyRy7wzH73PSnOv2aOWVly7HC00iYsikbSwyST7UASJ5ELbFXcxOSKijMbbo3jyN3GKmndFH7tR5rLtz6VXYSPAuw4dOpA7d6AI7iOBpmQly23I9qz1iROMjaxxj1q+zM14FjGWZQMkcAd6dNax3DtIMIsXGPXjmgDHSG4uppMHMakBSB2ok06G3lhYlmkDbiBXQGe3trLESgFeTmsLTLma61G5YoGQEbSe2aAJTGz3S+SMox+Y1rWtrCi/MuMc8etT21rErKgODuJY9qmkELyNEmQV5zQA3OyDyxgvI/y/SqtxZsr4UHCD5s9zUiyAIrMQZAx21O6/b49wn2H+JRQBnqXjtjtwecn2qzaFZ3R1fPZsVWkT7POBI37kDB9zSw6hDZySKqjy8E5oAvGTZIUkKrgkjnrTWECMZITl2OCfSvO77X7q6vnWHOAxC813GkEvpgmnRlcLkDHU0AWDOqRTpkEY61Sif8A0UcElW3YNSTp9oQkAqx6+lW2hh/s1dmBIFwTQALfyrGzQquSveqNyspImchY2GeO5qrCrruHmEr0qV3ZlX+4vYmgCu8rEfKN31qaHEkbRqvI5AqvLLG8gKHBU8rWrZTQSB2CgEDHHrQBDbbYsqwy3c1lai2WIU/Lnir8r/Z5WLMDv9+lY+ohmcsh49KALts/+j7TS5wKp2jMI8E1cXkUAITj8a0bNTwcdaoqu51FdBY26kCgCxD8qciiTDVaaIBcCqUxKZ4NAEZFROKVJSakI3DNAEcSkNmran5DUKrgVIDxQByfjw50m3P/AE2/oa8/PWvQfHQ/4lEH/Xb+hrz89aAEooooA1vDk7w6wpXBzGw/Svs3w1/yK2k/9ecP/oAr4x8NiM6wgZcfK3U+xr7N8M4/4RbScDH+hxf+gCgDjviVbrc614QR/utqGD+aV8u6wc65e/8AXVv519QfE+6W11bwjM43IupDKjvytfL+rkNrd8QcjzWIP40AUu9FIKWgDT8Nf8jJpn/Xyn86+jZV8w4IC47+tfOXhzjxHpmP+fhP519HzxnaG9vWgCAMV+V1G0dD61GykvgDhl4NPQbjiQ5X0p338KGOF6CgBqrnHmc7hzTQAM7DjNDucF88LxioI33AnuM0APlKoAFTLZ5NIjsEG9ABilyXcZGcVDMZd6qBlD39KAJATICzjkdKNoAJ9TUjB9wOflAqsWcD5uQaAJ2UEgiopgdpA4NPVuMnuDimzdWGecnH5UAVo98SkMc5NPCBSx9aQLluTkA0sx3YRflbjmgCRWyAAetJOFACNyRxn0puwRqFDbmA/Wm5MfzH5mJ6UAOVVXGOW296SRWVQxGc9qjCu+ZM5/pT/OZBtA3GgCOWMFVPf0qGMYLRt9asiNmJctg+lV3YpMHYZx1PtQBIXEJHcY6VlanpkerRl1G1wMj61onG55CeFpG4YNH3oA46xuZrS9NvdZXacDPetzzWlGc/KORin6rpEWpLvXC3K8g+tZFtcy2Un2e5G0r0z0NAFy+sg4EqHnHIrmb6JY1bePlY9K6wsdm4tktyMVgazBvgd1HQZoA5q00w3eswQRco7DI9q9igtVhtUhUcKMV5/wCBrR5dQkunBxGCF49a9BDlyUztx8pPrQAy5gVrVggLM3Lj0qrZ2htrd8ZxuyoY+nOKvgJIsiqx44P48VFcuBFjOSBnH0oAhhAkAPDOvUe45qSc71AIK9+KrRyrbKBhix65qzBcM6MSOCMZx2oAqNGqzLCHIOchvampCyEMrknv9KmmhleVMLyePwpZmWJQONq/Lj1oApeXI5YmMfKetT25G/5iRuNTvICFwuFI5AqqBKTtjXGOnvQBcuZduY40XYRy1UYYglx5jEybuSO1Kd89myEeXLg59qCjrBEq/ewdxoAf5a3Iwy7faq00IPyxH73BP6VY3FOV4OMEVAzJEdsR3lsk57Y5oApOjwhiTlRwRUUxZJUCnqOlXS8cqhQSCetV7gxxRmUIzSggY9qAIrr/AEm1jDAgg84qS0QtCIuAuOp+tVo5Z5/MwuEHOKsxiVGwm0ccA+tAE8jqhVcrjoM9qhmgEgwQrITjB/nVZoZPPD3FwBzyoxU8ssJPmghuMAZPFAGHqGmoztNB8pXjaKy4b+a2ciUHHSumVRM7HJWMA5wOgrPvbGC4gLIGJycZU0AZ1xMl1GGXgio4rkpjc54qnKJLZ9pzim7wTkUAbUepgYAqw12kkZz6VgB93B/Cpo3KYDHOaALM29f3iHj0qzYak3ToarFsrjOarMxjYkDFAHX22pOCuWzV43rMwri7S+KuM10VvN5igigDWS4y3NaUDIyL61gbyO1Sw3jKwXOKAOnjmAwBVoFWXmsWCYFck81ehlDDk0AWHT0qCRWAqyjjvSSDd06UAUSMjBFM3tAePmHvU8qEAVEOeDQBHNJHJjHBxXO63B9otHXqRzW7dWwxlTg1j3KssbK3OetAHnTOVdl9DinRzkU6/j8m9kXGATkCq24CgC6J2PepInLNgmszzMdKsW8mWXjmgDehQDHPWrqqFWqVmm9enSrxUgDJ4oArS5JwKfBB69aljj3Etjj1q1aw5Zi3NAD4IW2VKY8jntVyGLnA4FSvbLuyOlAGaItg3betV5IizZI4zW3NEqwDBBqkyjy8uMgUAT+HrSGRriK4cKm0sM9zWvo9x5Md1FEgfcpHSqGjWkd7d7WfYhQ1etriPTb94oQGyCvzUAJpsU5vgI3ZSx5UmrF3a3FpqZLD5jjBBqolw66gGDHcX4xVzVJnt7iKVySSOQTQA/U0urG5hn5+YBs+tUL1QbxjKmBMm4mrGrXk01hbTFhgcDNJe3kU4sfM25ZdpxQBn3FoDYI8Mu7Y2duelUUQm9YMDn2Oe1X47aMfbFSU8AkDNYlvI7yMrk788NmgDQ2qjOGI56ZPSooCZ5mdmJIIAJ44pZULxZIGwA5z1JxSWke4qYclFPIb6UAJfSK0gVRnHrUka8A4xVeR/NumHoatxsAASPloAmTCgknAHX3rPuC1xNvJOB0BpJrlJbgR5OF5AFPMiyFJZQAoO35aAGKyyRyJuPIqzYIZFYZz5Qyuaj8tPLeaFvunGKWGby3OeCVwAKAH3ChvnJHHY0xdOu59zS9CMr9KZcECRI+dpwavPqDiAk8FBgKOhoAzxbIoKFiGbgE9qnVDFAY1Ictxn1pqZuNu44RufpTLpltHUbuhypoAeYY41BlO1xxVC+uo1QwpgHGdxqreaouZHlfmufe6n1i7EVqCR0LCgCxNczarOttb7gBwzCuq0nTYbVUhDf6wcsfWl0bR/wCz7RfJiDTMfndquXFsSUjTOQd2aAFuLVY4JQr/ADg9u9Fhg2zsy5Y9fpSujORIp4Xhx60sbLbTIYxvVgdw9KAKzLaxzB8nBPANQyFslo/l5qe6tzckS/dVTkCoZpMgDGSeMUAU7iUTK0bcsDzVO5gJsZyDiRhhRVuKEQmR5TyxwAajvoZFjRgOvSgDD0nQmgmWW4T5t27Brubi6VbdMIoUgDis5GCxKHHzbajneYuq4O09qAJJBtUSIxKtxipDIdgVlwuO1FoR5bROOc1DcTtB8pGUJwDQBUDPDMd6DZ1FSoUP7xgNrHpV17ITWayMwfdxgdqy5rb7NISxOxRmgBbqG3RmmU8+gptrOLRSgUESHOT2qYvDOoYAKAPzNUX2yu6DgA560AT3QZyX2jHGKoywyvuO3kVbQSTZj3HCjNWrUSYfcAy4xj1oAzLdQIc4w2asf8sxU1xbGHAwPm+aoHO2PFACLPskrZsL0CuZdjuq1bTlMc0AdvDNvXNNni3isqyvcgDdWskwZetAFIQbKXGKuMAe1QOozxQBCDS5qRYC3SkeLZQBynjps6NB/wBdv6GvPq77xwf+JRCPSb+hrgetABRRRQBqeHU8zWYx/st/I19m+GP+RW0r/r0i/wDQBXxp4cO3Wosd1b/0E19leFufCulf9ekX/oIoA4X4us6T+GDEQG+38EjPda+adZBXXL5TyfNNfUPxNgFzqvhKD/npqQH6rXzBrp3eIL8/9NmoAzhSnpR3o6UAanhsZ8R6b/18p/Ovo+6TcmATkAV82+H22eIdNf0uUP619Js3mbWHVhyKAIIImRtxGRznNLI2PmTAGeaYJ3VypORwOKSRg8YCdSRkfWgBLhNzLsYbT1FCRKm4ZBzTkZYyVYdqi24GRz+NACNGUzznd0xShdpCHIAHJPemtIRxjH1pqFmAYHJzyDQBI0hxsB4prAYAxx2pGk2tyFpDIWJ457CgBrZI2+4xTefN+YZBGOKV/mTJyKbInlSbQSSaAGTAxYK981CWb7zdWGBU0kgQJv55qMOrvkjjtQAqJsAdm+cHvUsY3fOw5PrScl9pA54yaazBpMcgA5oAEDAkIeG6k0EbQWX9afEhxnPHPFI2Og6Hj6UARbyep5zio5VIDbulSiLLcYwBUMr7cYyQeuaAIWZTGMZ5ODTyD96M9KEiAlkXOeMikaIrjYTnvQA9kRuh+bHOKzb2xjvoSkw27Tw3etKNjtywxg8mmy4kVguCc9PWgDlGN1p8nlScxdFc1JNsmjbBBXb0rTuYjOjxuAfr2rlnlbTr77O5LKx2gfWgDptEhSy08unBLdfUVridC6k89+KqQ2YS1jjPdeB3qWNFiHXnpigCSNvKSQq5y7Zx6VLDKGSTKjPXJ9O9EUTSSRkrhX//AFU9HiikYun3jt/LrQBGsXmtJkDJXvVqCNbWHbgZboTyKrCVo5pQqhwxCD29/wBak3yKg3Y9RnmgBjzAXqRqS3qQOBVdoDJcM8vIPYVIIyxEgkAGSSQRU6CM5YkhG5GRigClgRR7SfnY4xT5AzeXtkVVyAB3NSXMEdxOZI9wOOBjqagktHKwCRthUnAB5NADJNsbMwfcSOakTPlAHqQSakCRRJ86/KTjnqai8z98wQcds0AEsDiLzMfO3A9qp20QglJ2sWfPzH071PqMU86xskxT1UdwKrxTyswhZQAehz0/GgCzFFHuIAUKOeaoXwYK21sNzjNSSJIjCPf1Oc5qa4tTOpkLgEKBjNAGPb28uPJbOcElhUptWhfzPN3Dj5c1LcyOo/dqSRhSRUPmCFj5oDKfftQACJQ370Ak9ADURt/LdlU8HkVJIFjdXckbvu0OxZvlbOOaAIYmbY0bZIxg9qUx5+UkBVGRip7bEcW6RAxdu5xioby3O7zFfYMHHNAGVewRTJwvzHgcVz1xCbaUo5wewrrGQssI3ZK85xXNa5EWVssPNJ3Ag9KAKitzVqMhx8xqhbt5kIz96pgWQ4waALTFlOUNI0hYcjmmQyjPzEVJIAeRQAKdvzVpWV83AzjFZOTjGKWMlDkGgDsIpywBJHNPJw+6sWyutyjJrXT5lzkUAaMFxgYrSt7gDFc6km1zzV2G5wBzQB0yShhkGpRJkYrFgvgR0qyt1mgC/JytV8YOKaLjPFSKu7mgAMWV5rPu7bepAX8a1wAVpjR5BFAHmniLTHMqyovHQ8Vz5tWA6Zr1G/tA4ZSuc/pXHX1ibWdhjg0Ac60AAzTrcbZRgcVblTBxSW0QabFAG3aAiIYFWCC3UHHei3TagAqxsYjGOtADU5UKnAq/bxYXgdRUFvb7TzWrboNoBoAdHFhTzyacU+TFSxphj6YpWAwaAKMp+XAzVWYnymPYDmrzqO9ZWo3SrIlpHyzgliOwoA09CBkvo8HahHeqGteIbbQtXeRis2xsYBzWDreuSrIkGnv5bYCZHc1yOp+cJWWYlpDy5PrQBt6z43vb25L2qiBd2RjrWNP4g1a6cNLeynBz1rNC8dalgTeTxQBoDxBqrRKjXbsingGtWy8T3oaIzsXWPoKwkjUZyKuwLGF54zQB1lv4kSVpJCGUuMVJbSbJTIdxJ6VkWcSCPkBl7cVvW+5UVUXJyO3SgC1HcB1XaGIJ5BHFSWcgfzlDCPnopqKfZ5cihyCDjKdjRbQpHamaU/vegxxmgBYV3SuwHOafeSbIfLU89aLaQIhZuO/NUS/2iWQ/N5j/AHKAJvLiaLzEP7wrjFVrWVipSQEIp6+9W2t/3CIGAdeSQaj2GCPylG4McsTQBOyL5QCN1GTiqsYlEgdmzszUm9VwVPLHAFT3JjtYQu0l25OBQA1mVrPzc4ZT0NLGxu7dkTAUYJI6msm5ee6AhUFFJxkCteyhWOCNUJ44agC8ixJCsbEEnsKoX8ShWDn7qkipWhSG8RmZjkVHdyb0ZcZPSgDze9mkvL3ygT1xXe+HdPg0+2RkQGRhyTWRpthENQkd4xkE8GtoZLZVto5AFAGzBdmY7DgKDximyzG3lV2bk8ciqtiuFXnkHmtGQJKQHjznocdKAK1wEWEujfO3Yd6RFIAbpkfd706ZHs5AWUOO2OcVBJM5kxEAWPJHpQA55lVSrsA3pVaYBZo9incSDUiWolLTS53DtVh5BLAg8vY68AkYzQBRvEjecb+NoziqovFljMEgOc/KfSr2oxPhNy8Y61TQCH96kYYt2PagBzAFC6H5kHfvUlrLJLA1zMoCDgUTXYLbfLABXnHapbUolq4HzZ/hPSgA2qwWRCMMOao3CySxBBghGyfep5kzb+YX24PCg1AJhHECcn6UAPhnfygqErg96ZO26Ft3JPc0HzYo0uNnysemKS9MssayRpiPHpQBSS4hjDAqRu6CqC72umJDYJ4xWssCyRIzKMnirTWRt4BJtVgemKAK8GNpwcOeCDV+zt90RJ+8OKz0jKTB2BBbityCE4CqwBPvQBlXkTJ8pOSKoMN61p377WdW6g9azUbPGKAKk0WDmlihZuAOlaBhDYq9bWqDrigChapIrda3rZXZRzUQtkA4xU8coi4oAt7Sqc1VLfPjNOkudy9apGQ+bQBrwg7etJMPaoYJsKOaleQEUAcZ46Qf2RF/12/oa89r0Xx4d2jx/wDXYfyNedYxQAUUUUAafh3jWovYN/6Ca+zPChz4T0o/9OkX/oIr4w0QldYhx3Df+gmvs/woMeE9J/69Iv8A0AUAcd8UbsWOo+E7ry2fy9SVto78rxXzHrgxr+oYBH75jg19RfEqITaz4PRhlf7UXj8Vr5e10EeINQB6+c1AGaOlLSDpQTgZoA0dBw2v6cD/AM/Cfzr6WuF8oAoOcCvmfQ/l17T3HQTp/OvpaeVimQdp2jBoApMuZGK8Y5NOjYA8rtPY1LEqlvmHJz81OmClQJeB2agBlxhY2ZsYI6iqq52gj7napv8AWBxjCr3PeoCG87I+4wGB6GgBSFcM45btmldmWMfKAfalnJGFX7oHBFQRq275ycUANcZkBP0qym0OGPTBFI8Yxkgduai3AMsfJGRz6c0AOlciFsjOVOKgRjJL5rnC4qVzI8S4XAAx9RUcY3gDOAMUARMpkkLbgR2BoZQAGYcE5IFPmKjG0ZI9KhuAzJnoCaAH+d5pwqkDAwfpUylXUIRyOp+lV4iEZUjHydCKnRSoLFsjsKAIz8jkAkduaev+rBY5Dd6fhS5Iwc84pgfPEi/LyQv0oAJCI8sv3SOPrUZClQD3wac2NqhhhQw+WoGYjBA5GcUAIw2zRv0zwamiHB39M1HdMskLHGCBxT0IaKIdcjr70AI6AggfdJ5qHYUbcv3hVkjETLjHemRIxfn0oAz5s7vmXJPcVi6lpyvdQTuANjjn2zXVyxGWNgP4a5PU7l1keI5yvP1oA6g5l8sqowflUiqNy32ePciFnDZOe+KbpGoQPbRea+QO+ehq/d224CY/6pPmyP4jQBJZXisqleePmU/w0S7Y1PyliSSPw5rEWWWKdpF+WL7zA9xWzHcmWGG4UbuQQq85oAREC43nq2MVb4i2iRQ7lMoB2qKZliUzLjzOpB7CqjXZLfuN7ZwGbHTNAE6woRlF6NyAc0qxRCV5nV5GK42Z4FNAMNs6gEZ/u9TmmWu83BLMQFXke9AExJxuICFei55NV7m6cSbjESGO0bhjGfSpYBvdpFBL5xk1LcLLvKhRgfxn1oAoSthuudpG0Go5p8/Nt+baflAq4bNCpuJeCD8qg08NEYz+6VS5xgnoKAM1d7srRoXz13HpUMoyzMFYBe5HQ1fmjCM+G2gADA75qup3GViu0AbV+tAFcENMScKQQM5ySPp2p8rlz8qgADqT3p9xCsNsJguXJ6jqaoK3nqwDH5Gy4HGKAFlkMcJEvGeBx1JqrKdsoDAYXgH19P1qWeSK6uEickR9Q2D2psUsc3CqW28AsD19aAGrI42OBu3cfMRgc9qitkkMzgAcHdyf0qwsYDbCpbHIx0FUstbTMoO3dnDetAFm4MkBy6rjHHeq8krtFGhZS2dwXHSthEinjw/3iASSODWf9l868eXYioVKgknGMdqAGq5Rfu7yRz0wKw9ShaaKRhEML3roWCvCYoRjZjJrMODDPEzE72wvsKAOIgIjnKucDP5VsSxRyBSjHbjrWVqcH2S+C+nJNaVjIRbljyTQBC1uY23N07U8MR1q2/8ApEGCCGHSogg+6459fSgByorrk0x4sdKegKPjqnrV3yA6gqKAMyKZoc/WtywvfMCg1Rkscr0qGPfayZ5xQB0rAYytIkhU1BaXIlT3xVkxk8igC3BIc5q9FPk4rJR2SrEUvQ45oA2YyM5NW0mBXArISbI+Y/SpopWB4PFAGqJdq01LndJtxVcOHAAPNWoolHOOaAG3CBzgDr1rm9dswU3r2ro5WIbg1lXuJEZTyCKAPPbg4m21dsoAGBxye9RXsAW+IA71oWownPbpQBpQQDgk1aMePmFU4HIUZNXVbcoXPFAD4xmrcPXFVgNhABqyh2n39aALasF4NQySAHNMkl496qyPnNACyyjPLcGufK+XJNK7HzWbaPYHitOWMhdzHIHIrMnCvBI8rYzIoH6UAZgsHHmXbgeXCeD6msTVIZZc3UiMvmdDiuhuXeW3+xRfNHu3Nz1q7rFux0W3ilh24Xg460AedY5NaemQ/umYrnceKzpAUlZfTNdRott/oIPOW6YFAEMNorqUMfJPWrEVggUgjJ6CtNIn8lFQDc7/AC/yNTW9rnzAVAYNwexxQBWt7cxjaecEVt284SQEqAiDkepqmYWQ7gDg9x0q1t8xolCK+OSQcUAKys7khQEc5NTXQYJGgxjGTUMiscr5hQZztqJ3cSFXbPAAoAdMwWJInbLOO1LaReTJGGYD+6T2qjPNFNchjuJT5cCnmd3Yoc49COaAL4Vo5dwYSLk5xTL7K2+A2GJyPpSReXDbM7HGB69af5sUsCyliB2GKAMhJ5EvY5GUlRxitxp/nVGQNvxz6VnTPGJBIFGalN0soSRW2gHBoAtDiea3VB0yDU9uYoE3yHkcECq0sqI7FDlmHU1VEsgkyzAj0oAv3paSIOnRTxUVmnnuzseB61KcGNHyAfSqkkzo4EYAVj8wFADrix+z3QmX7rdakCxzIgj5Zasb0uYHjGSVHBrNsi0c7qPkYc5oAnima381W6irVvflnVmPy7cGo7m2RoftBcO7jkCs6AF8xnIXPegDpI2+1JKd33RxVGJvJuhsQls85qaxkhhyB8xPbNWGUszSMAfQDtQBLM6eQuxRuzk1UFwHkZpMYHRaSH947divNQMhEuU7nmgBJ7lrkMNmAvrUNrJ55aMoFxVlQjNhsg+1H2EqxkQ8GgCKJbeKbynGd3UmpZ2hCCNdoxzmq0cKpK3mHLdqrzxGSfA5PcUAPZlG5VG4N0NSiMeSE8sZz1pFKeQIQmGQ9RUz4K7Q5AxQBHMJZI9hUBR0xSRMTbiJgCFOeamgdCxiZ84FV5VAlyudp7UAPaEOUQR4A5JFTvEFQRqCQvPNRQzZchxhTwDT5FeRnBbCDvQAn2UOgdyNw7VWvbyaHYQoCjjipZHManyidhFZl3dDYEYcdzQA+4mE6ZcD1zVKJvn56U133lSp46UKjM+O1AF5XXOalW4x0qGO3YnHapWgCDpQBZiuuTmn+ZuqgpG7Aqyh4oAsA5qRIw496q78Yq9bYJBoAesZXg09uBU7p8oIqGUYSgDj/HDZ0hP+uo/lXAnrXeeNhnTIxnjzB/KuDPBNACUUUUAX9FfGrw8ev/oJr7R8Knd4U0o/9OkX/oIr4y0BWl1qFcDjd/6Ca+zfCwI8K6UD1+yx/wDoIoA5X4jkrrXg8gZ/4miD9Vr5c17/AJGHUPeZv519SfEVguueDmPT+1UH6rXy54gXb4j1AA5xM386AMwUtHeigC9ony6xY55H2hD+tfTF2quVAHy7R07cV8zaPzq9jj/nun86+n5UKbWAJBUZGPagCswGNu3Knv6VBcRr5SxFSw+tSTOG6ZAPQURhVJLk8DvQBFvUARr8oqOUFioUjGeTUkiKZwwqJh95lHH86AEWRfLwQSFJ59aJ8j7g5ppG59oPGOlSufmGOcgUAUrpn5weVBJGfarEamQADb2JHfiozGCCSmSykZp5jEeASQ3tQAx5TF+7PB6c96aIwke49afIihg8nO3OKY7eYu7scAUAMYKiNxk9ahVTyZDgEZ5qUZyAR1pWTfgAdj1oAiLeVtYEMoJzjvUgPGQCQBk+9DxooAIOecg0hy6Ex8YPI9aAH24UbXLYZcZFGC0ylyOMD9eaZGVZzwQxAP58f0pZ+ehwykt+lAEcpllZwpVSBzn36VGLeQ/O5xzxTxHIJBIfmUrgqOvHP9anj3KShXIxgZ7UAVHQhMEbs+lOt0dICCeR09vSrE0ZwNvFQoximKuQwkXtQA872QE8cU8AqufamoWZGBHvSqf3Z9RQBIW/c8DHrXJeIbcpIbkYxtINdSrkwnIzWXq0K3lpJEBj5TQBi+HbdZdPY8ttc10kDNG5Xd5sRxkelc74DmCte2zgExv3NdZNZ/8ALWKTb/srzmgDJEMEt22STETg89vSqv2gabe74s+TjGPSnPbT2twU2sVzu3YqwIT9kaIoryFs7jQBatruO+iKRJkEBmY9fpVmJWTlUAA9uCK5qNriwumlyFiDYKnjFdFa3X26ASKQyZxtHGBQAryeWHdhuT39ueBTjKoCsBtLc4PXFPEkUq+Q5HIJDY5qpc7jbqdoAXJIHUigCyXWPaAMEnOFINMd5JYCoGFY5GTyaZbTQrCW2qu845P3fSmBXYldxXbkkHj8qAG2yGKIu0rSSHgg9KkJUrnglTTIYCGVZTjb8wA6nmnRktcsNgAYY/PigCEkSKxK85zikCOqoRjk8AnrVhpArsjJk5x06VBPFG+GDE45UA9KAKE0ksbOin5U5JbpmqeSqtmMpznc3cfhWpcjziTgHA6dKpXaLyuW3Y+tAFT7bFMqOFAAb5i3HAoNwm9imzYG3AL6VVKtDcCKRVEZPfkmpw4abd5e2P7uQKAGpqBSZmicbc4IYVSv3lcpO2AEfAAH40+WFWdn8wZ9BTzEgiUMWIPTNAE8N/cNFtK4IPOF6ikunzsO5yqdIxxkmra7UsAxAL9M5ppAjbdOqfKAR1NAFTy5ifnBjzyFzjpzVV2MmcFAy9cfWr1xKbn5iefpioo7QKrMcbcH0zQBzGtQGWB5WUBj0NZ2nShl2M2NvWuk1aBmt8IC57kjtXJqxhunUpQB1dpbxPtBkyrDg1UMaJOqAlkORmkspEAj5OV5xU9yqxMWC9TnOeKAKRygI/g3cVbtZvm2n86g2vMssfULg8CooiVlK915oA3CCUziq8kIkXng1LaymRMEippIgFzuGT2zQBQhbyJMdq3raVXj65rEmiJHHWn2crwNhs4JxQB0IRSAduaXyZG4jSn2jq6DJFaShDgZ49qAMtbeYHL/AJVaiWQkADjNaSwM4+WMsPUirEVo+P8AVgGgCta2xEuW6VobQhwORUkVmyjLGpWQKBQBmXCkbjWVKpJJPStq5cAmsy5P7vgdaAON1GEfbSQR1qSMYUVJdRZuCetKq/KKAFDdO2KtxvuQhTzjiqT4UDg1NA4A5zQBfiYqoDnLVcjPUnqKoRvvx0x61oRlQABznvQAhXfg56momUBsVZYrtwOtVJT8uT360AU7qRjgYwvc1lXEb3F39njjZsjcvoTWjcE+SGXkMcAGrsly1sLZoYkDBOWNAHD6a1xa60La4BLMxG0fnXe6xIZNPjgdVyq5Bx2rj2uoH8X2krEFfNAc59eK7q+jS81CS1XG7AEeO4NAHi16u2+lXtur0HS7cR6ZBmPGV4Yd64nWbY2utzW7AkrLg/nXp9sFFhAmOAi49uKAKQiSKIAMA2CMntUYLwxRlfmjYHB7g960PKhk3JJ1PcdqjSxSKNY4pN+M9896AGtFIttE7kGPOTirsf2eQ+UqnpwRVa2hLwkFiyliDnoKRWW1AERZhkgN9eKAK90oW4EO5hubCkiiVRA7PncF65+lKrP/AGjlyJF25B9D0qjeSCRwFJwx2sKAFt1AZ5GG3dyDVqOMOvmMcu3HSmwPGkbIRkKvGadbXa7DuVRjigBZAI1ZZV3KRxWc8rLcBORF2ArXeeJ0GBkgY5rMkWSc7FTG09aAIJJMZJHy5xVu0CC2JdQVz0zzWUyyBGVzkq9acUamSGRTlTjcuaALsoEVvvdQQelVEuI2kX5e/Sr7xKrsv3t3TuAKqSxLbzLkAA96ALlzBny9uRketNR4YyYyNzHqafM4MK8n2NQQxrvkOQRt65oAs2CDEjg/KDVeRg1xuC45/Oo7SdY7eSMMdzNUjsJtoOFK/rQBbhRC5XBzjpVWa2MLE5wWHArRSVEjAUDcR1pFh81N8hzjoaAKNiqqd7SBXHGD3rSWVgpdiACccVl3FkWmzk568VMk32dhCybwR37UAWJ2CSNJD0IxgU6LEoUkhas24gI2Y7d6rpAm5skg5+XNADPNWC4KkA+5p63W9zkgJ2qhP5hnCuRkH8xUYR5CxXO0dAKANOWNNyISNzDrmqc8RDbEI3A8nNLtZwrsp8xR0qu8geXbyJD70ATQSGJmV9pOM5ojvIZNjHscEVTRHiuw+N3Y5NNVyLl9iKB70AX7sIZEkgwoPU1DJcGSIhSCV4pks8cdr+94bPAFZrXCwMzA4VuQDQBooAIMtIAQelIl+Csiu3y44rJuLg4Rg3Dds1WmkDEbW59M0AXZ9QleIRhgoB5rPlvNh2g7yaqXUxwdp/KobaNp2DZ5BoA3LZS6BsYPXFaEKgHJqCzwYeRhgMVYVSTxQBcikUDpRMwYHFVeVp6ZYUARYw5qwmcU9IBnNTrGBQBTkyMVYtrkLjJpZI93aqkluwbINAG59sQp1qrPdBhgVmxxyE45q3Hbsw5BoA5jxjIX0xD6SD+VcT3r0LxtZ+ToUb46yj+VeenrQAUUUUAavh1QNbg+bbw2Sf8AdNfZfhY58K6X/wBesf8A6CK+M/DrINfg8z7mG/8AQTX2b4Xx/wAItpeOn2WPH/fIoA5L4mErqXhIgA41WPr/ALy18w+JFKeJ9RUjBEzcV9QfEmPzdV8IL2OrR5/76WvmHxT/AMjXqmOnntQBk96KQUp6UAXNHJGs2OP+e6/zr6hkLORnI4Wvl3STjWLPHaZP519M3UpcIDkYUcj6UAV1QliTkbR1PelkkAOCOCcE0ssT7F2v8vcetRykMAuNoxz70AJJIplUjpjFINpc7TgrUc8e0Dy0IWlJQLgjk/rQArvudTwCD+dNJyVR/lJxgioVU+aCfuqeVqVwXkUZxwOfagBu9oc7uYuPzpqq7Fpd2emKGXzWG5v3a9vWn7D1Q9DgigCIyFzmQcZ5pgkHmeWo+UEGpJjlcA9T0qCNh5gQD5jnJoAsquSc/hUgQKg4O4c/lTDJtALKCOgpJTIrDA5ZTk0AMLCQtvIwOlI8Z2b1b3pzRFocN8rDJz68UM0PCrn5ck578UAQxqS27vgUMRtzjL7e9Tbdo3jvnFRjBJyOcYB9KAI1mDRErxLirW4Tx5+6wHJ9apeWizgI2SM5arRAMCLnjdlgPQUARBWLMrHPPFQuPLAc8EGrEhUKCjEEc5qrLKtzkKOKALSyBHHGQDk0OyrJ04PX6GooyVgDN8zd/wAKR5V2c0APRwJCo6Uy4jWWPagwc4JqAO+NyrhT0qa2dpHGOVzyDQBymixvZ+KL21C481NwPr1rtRD+4zuIfoQOxrnNRiWy16z1AH7xKN7Z6V0UkjwIoADnIJ980ASSxTJFhyhBTv1qibZCjOrYkRsj2q4Jy5/fgnA4BqvI6vJkLtDcY9aAMK6s7m4kMkxEhLjaF9KqgzaKfNikzC3ylT6musP2dQHyysR0xVGbTo7kfIfM5ycj9KAGWs0VyyeWwcf3jxiifzhJ5ZZd3Xfmsd7e+0m6jdFxEcgqOa0YZFuYfJyC0pOMH5setAD42FvKS6mRywzkcfXFSSkqGClgWbB7sc1NJChjESyYIAyV5OKbHG3c7Y14DdWY0AOMzNbI0yBMY4Xljz3qRAcK64ODyRUUhVl8hF+VhkgZyfYmlinClI2XJHGOgFADCjuzpyRn71R2mwXEjbCUQgY9TVqSVIbBvMlCyO45H6VVgCxs3I3OeQD096AJFt2aZ2cqIiD8vcGsm8lHmrCjD5fvDvj0qwzkTkpKeXBJqGe1jnlZlYl+59aAKGpiMquMFgA3XpT4JVNsUwSMdQM4NQ3KMXdNq/X1pNPjeFWSR22yHAGKAJERYvl8vcW5yRSttLgSKfYAdBWhJaHzOZFWMfdweTxVSSGVboN5Y2sv3c0ARpKrjaisFUkY21JcsVK/KHjOASaRNyMwKEK3Byf0oubdxEoA+baSFz0oAo7j9pZHUKpb1p7zxBSW28HHBp8cbSy54wMAnFNRFSQq4G9m7AUAVrp8sCOVAzt9a4zWI2iuvM27cnIr0Sa0yyvjoOTXKeIrbdk4yNpOaAKGnyARIzHJ6VfcmcxgHg9RWHpknzlWPTkVt2rAM755HNACwSS+aYoxxIpUmqc6bLhVLEOPvGrcsxVcxAqRyMCquoh1wdm1pACC360AWoJJ4cHAKnvW1Zr9oIY4xXOwB3hVVbJHarkH29TtQBR6g0AdP9kiIJ+WqtxbQKB6+1R2lrOwBlnP0zWt9lgEYBbJ9zQBBYSwIQGya6C0kUj91ECPesaO1jVvkda0bUFSB5nHoKAN+3LFfnAH0q4qDaTWXDIyYHUVpRklaADFRuualw2elLIhwOKAKDQIepArHvzsZgCNtbc0T84Fc/rDmOIjGOKAOblw0rH3p4UEVBGdzHPNSgjFACmMGgADijNLtBoAniwOKtpIUAAqnGABk08yfMvPHegC7nOCTiq8nKsc9KSSdeBnjHFRwr5koTOQetAD7W1luZPMCZjj59qfqAB0qa4kIV4wQFFadlOyWctrGu3dkZ9a5O9uGur37Ar4UHBbPWgDnNKt/P1uHeMln3Zr0xo3snttQ6kZUfTmuQ0GxW31+RZ8ERxsV/I13ur27DQLR1PykZAH1oA8p8Twg+IPPJ+eaVTj8q71Ij5Mat8qiMcj6Vy/iSzBvtMbOWkYZ/OuylUQ26Kw3bVAz60AVeu2NU+U/eaozCsLglWDnoAeCKmMxxGVQbC2PpVe6eR33wfOA2Pm7UATGzk+x5aQRKuW+U9artCmF+dgAMkVYWOaSQLK48thn8B2pbt4tjYOPl6MOvNAGVGFWO4ctypwMVUUkyMWAAAGD9a0HSGGxd+fMl5A7VlWxZ5gZjhQc4oAmXlWCn161ZtU3QMjqobHDetMmhiMxCsQGxgUPbt5gjVwuxfXrQBGiB4zul27SfxqeFtkYIUuPWoLnFtbDG3J6j3pba+PlhDtC4NAEXlq8jnFEBKzEBflU8ikinAdgOc+tLHKyliCu49aANGWUrt2DOaguwJJAGONozioPtkqShQBs9Md6WQNdRyv0kxQBLG7XFysZbagHAFRmcW7TRA8HiobJGUCTdhxVfUZgJkduGfrQAuSjR4yOcmtOK4iZ8cFqwWuD5bbjuI6ewqG21AIxYZ3e9AHaxODECQMg4pFuGiKxOvBasOPVIzCuX2uTzVw3qsivuBx3oA2bhY+Jg/tgVn3EyyNmPBPrWcmrqzyjdkHjBrPa8fcxX5QDmgDp4LhLeBjIQWPGaY16Cu5TuboB6Vzovmmwgzt64qVZGijd8Z/GgDayivumb94Rmobe7ELsI/mySeayIrySd9zdhTLm6Hkny/lkB6igDXk1MPJhD83QmqVxC8cwfzcv1ArLjmMas5OSae1xI5WQsfkHWgDTW4kZNpOCTyaoS3EtvcEE7lPSqonlXdIZCQ3am3ErvCHU0ASXN/JLtizkjvVeYmVlDueKYnB3beT3qSVPk3DigAYfKCGyB0qIlvvDqaVGzAc/wAPNQrcFeWGQTwKAFMJiRm+9znmp7RWLBwME9qa0+5ArDAJ5x6VYtoSkqFTwaAN63A8rpzipYvvUQgeVzRGPmoAtCNWXmjy1VeOlNdsKMVGWfGMnFAEodc4qZMnpVNAC3Iq/BgCgBQuetIVG4cVKRnpTTkcUASQxoQeKspFgjFV4eorQhXLCgDmPiGm3wxF/wBdx/I15VXrfxJXHhaH/ruP5GvJKACig0YyKANDQsDW7ct0y2P++TX2j4Y/5FbS8f8APrH/AOgivirSedXth2yf5GvtTwvx4X0sf9Osf/oIoA5n4hHGr+Es8Z1WPn0+Za+WPEJJ8S6lu6+e1fUfxJ/4/wDwqB1OrR8+nzLXzD4nA/4SrUsf892oAyAMUvWjvRQBa0nnWLP/AK7J/OvqC4URxgY3EqP5V8vaT8mq2Z7+cv8AOvqCc+aF3fKNo5/CgCsxwODz/Kq8r7pQNwBAqSVGUPgjjpVbO1w5GWHrQBON2SC+c1FIrAgkcMcZ9KNpecMx2n0FSnawwDkjkUAVvMXOxQeDyajmYkbFBLAckelTvGFjLkgfSkt4BvZy5ORjkUANChVO/oozTyRHEGXkk8+9SFR5bFyOaqxo8joRkKD3oAZJjLMPvelQIu85HDDvVydFIJAO/PpUUK4yZRjnjFAAqgYV2J4FKjMMbiT0wamCK6lsHvUU5AZYwPukGgBGZiBzk/40BRKeQMrwcd6iEu0EHocHNPt5PLVmcHGOaAHLKAXU8qv9ajkLGNti/MxPfpxSyDcBIuAzY4qOD50YBctkZOaAGwRLDlGbJY/e9M1aQKhOeVK5B+vFVpVBO3PTkmnnKrg/dA4oAjlmLq6BNpU9T3ApIY1G5gMjrx2q35SzIVHHA5psEOxio5UjmgCCKJzJJHuBGcj6elP8lSORyKlJ8lRKoyF7U4MGw3ZuaAIPLABOQMdBU0aBcEDaDTTEXLEdKLliicHhRQBi+JtiaeWAyUO7P05q9aSGeO3uRuK7AW5rivFmvlrV4IyM8g89a6rw5dGTw9bEDJcbTQBbuLaZ/JYSliznOPSrInER2SQn5Bw3rQ2zytwbAQZyKSFw8Ee4hixzz1xQAXTrPD/o/LHg8fdFLBGViAjbae+T1p1tAYw7MyorZ4prxrMqJHuyh5IoAjmjeRmZyGPbJ4xXNXcU1nI7xqxVz95eqj2rpki8vPDEZPX0xUSR+YfLyq5JZs+npQBUtdQSa2t/LTB+4cnGBnqau7VuU82NwkaNtwD1/wDrVz+oQNbu0kCuc9FxgEVZsL+KdVEJ2yBSGDdj9KAN0BXj3sQABxjjkdKxbmeVMlEHytkyNwK0Ypo5IlZ1ZSowc9CfpVW7V9m2QDZ15HH5d6AKcVzG9wzSDe20Eb+mc9hU0JeabcqFB6HrVK8snitTOoYseVGOfw9KhtdSkV9zED5SAD16UAawKwuUkYcnPTNVRKzOywxOW5OcdRTUufPdUADnGSw6VNb3FwyAsFRM498UARjaSnAZsHdkVQu55Le6jlYARofu+taM6RzBpg+0LwAPWs+/IeMB84K9aANOCeO9jIVOvzhqjeyk3b2ncrnPb8qo6PM5Sa2YjcV4PcD2rVlEsduVhQsu3AJNAFNSDdqzjco5HOOadco0NvI7MxZ+Co7VUSGQzIJI8/8AAqtXa/KzlQMYBG48mgDOaeZNu1ERTz8x7UOyefGVwzdT7UNEk6BmbYVHTHv709PKaPO9t2ccLQBKb0SwmMMCFGSyg/lWBqERlDbm+TbxxWtOirCERXUMOoGCTUDQNNb7AeG9aAOBy0F2wwRzxW5YYfYM4JcZz3FVdbhEU6uFxt4NSaad4L55XnFAGvtTz3ydqg8ClnEV3Y7vl3IccdhTJEDjzOeR29abblQkyFT+8GAB6igClCojODkgnHFTr5it+7kYjtUUTPDcqxUFQcYHNairb3BAQ7ZO46UAOt47t8EkkfWtu2tnZRvRj+NVbVDCMZLH2rcsAXxk/nQAyLT23DauPrV2K0kBAOK0YoW3DGKsx27ZGaAEtLXAG7P41ppGFAwKdBb5AqwUCjGKAIDwc8U136dKfJxUDEcZFAENxNjOPSuQ1yUtGT+GK6a8cAHFclrDfvQnY0AYiqVPXrTl5FQyPjd7GnlsKMUASbsGniQCqxOe9HY80AWXuVXjBNHmjAOetQHO3OB0pGYkjgdKAH+cGYDk84rT0yW3tLlZLths7jNYnn/ZgZioYL29TVD7NLcwPdSSSGRn5TsBQBsav4jAvX+wq2wHaMe9UItPmBFwSTu+Y4q5pVvBHE6yIN2c4IrYs2FzEYkAXDFQBzQBnWVu0N/HcyHIlGzGea6a5uJ7S3itJwSijjPoTXH3fn2V/EcNxIM7vrXZ61cxalLacbSqKDigDnfGUEIvtFeHgF1z+dbU8iKoD/MuM471T8YacYxpUiyZAkUnHbmtKRYZ03ZAPAoAqrKCVVEBVuQD2pk6xgmKMfMD849c1XlG28RQxwvTbUryRqTIucn72fSgB1vbrZ582YlD9wdfwqLUy5TeQuwgAgdqdaLHfM7bmYRHIFRavKGgZdyhQMkCgCnMjRabEGTGSSMntVe0gDxOZPwp91IJbaPJYgKAKbHIufIBZeM9KAHbxFMm8BlHepSoIaUYJLcYqtJCwIjXLFuavW6fLtbHyckUAZc6sUlMi89AKrJDIu1sBQR3Fa0knmyNtTPHpSRFniXzUAwe9AEK2yvbEggNiq9raHflnI/Cr42GQhDxUjR/uHGcZ5zigDL5SQgnOG6kdatRvkh16HjFMuQqrGWPOKS2mRYgSOhoAdEuy8+bhTUep2olO9Vzj0pk8zXDF1+UKelSJeFVKlc57UAZ0tqqIC3BNV5tNeYDygFxyavzzFmAwDjnFTc7BJuxkYIoA59oZPN8rpjvTwbhCsYc7M9TWy6wtEGjAMgPPvVo2sdzGGVQNooA5v7OyzkAnnmrEabn8pyQavSW2ZQTlTnFJ9jk+1bkG4Ac4oArE+V90dKj3ysHySQegqWY4zketLiPyVycMRQBSDyIMKCM8UxTK0nTjvU7IY5OCSKdbHLtu4oAZwgYOenamSyNgKo+VutWJYd8rYxtx1NRQ4YlT/DQA5YwYtxPA7VErDnP3ewpzHMXXABquo8xyAeB3oAfJJsA6H6UMzyxgL3FRshMhHXAqa3k8sNxk4x9KAIYcjdGwwDxmrUcEVy21RgJwaiSOR42JHOcgVo2EZhQsQPn70AVpbVbYtkbuMD2pLSfMqqSMird+j+UQBn6Vj2Ubi7+YnrQB2EZzFxSwnLEUyMYgFPth89AFn5eAal2Iy4FQSAk8VJACDzQAxoiG4qaMFas+WG7UeVgdKAIi/pQCT1oaIj1oUEcUAWIfvVq2w3HNZCcGtey6UAc58TRjwtCP+m4/ka8gr2T4nr/AMUpEf8ApsP5GvG6ACjpRRQBd0ghdXtif7x/ka+1fDYx4a00eltH/wCgivijTBu1W1A/vf0r7X8Njb4b04H/AJ94/wD0EUAcd8UZo7W58LTyMRHHqiM2B1wVr5m8Uf8AI1akc5zMTmvsDxNp1pew2RuollMdyrJu7HNfIXixQvi7VFHQTmgDG70h6UvekPSgC5pIzq9n/wBdk/nX03O33Ux/CP5V8yaST/a9n/12T+dfTU5KqGz8xUYP4UAVvM3swI4xURjAjDKN3OSKFOQu7qDyalk2kBQ2DnmgCAOJDnODwMfWplQLtU8HjmmmJdpAIqvNI0cIIYkgfnQBNKVdl9u1PyBGSeMjPFVWdhhioG4YFLI5EYVc7jwKAJJvnbb/AAYpclYyB2zRlViEeMsetPDKH2Hnr/KgCvLIVc+9Qod8bRk89j6U6eQDJYZGaii2oC/TORQBKxZECbj0bn8KhkDeaCDkJkn3q6FLKULA9cfTFRsiBTkdSeaAKS/O7ZHAPT3qQPthZH5J5NCx/vGYkqxJx+VV5pC3y7SMHlvWgBWRplDhsMP4amAy6hCBjG4etQF2BUquPWp1jaT5o8KV7etACThVIKqcHirIiCxBHOcjGarhnLDcnGcH2qwJFDIMk5I6UALhQAIwe/X6VIg8uJs9QxP6VHKAVGBzx/On5AjJ+9nqPrQAwgmNvyqCIss3lHoOVq7ldpTBDHmoZBHkuxIYH5fpQAkan+Juc9KhukBXGe3OP5U4ygw+Yo+Vjge1MKEKNvzE4JJ7UAePeJ7c2upzhiwV+VJFdz4FvDdaZaoJFCxZVgad4l0YaxZOmAJ1yVOO9cx4ViuNL1B7S5JUMePrQB6ogAi8nywVUksf71OSK3tZmypZscD0qkJZ42iRH3Aj0p8xZXYhizk8E+vpQBKsK3DeaCVD9VNSbNgLRNkA84rJe7nDhAcY6D1FMfUWto5RtYDGeOc0AapmVgyyyhcc475qGVkSUeUPMGQtZDaiNiuU3mRh1HapobgTbssqgZxg96ALdxPGS+5R6IMVh3WmSxstxbLtkY7uK6FRB5S4xhRjmkaRVZyjDKLjFAGDZaruuPLuiVckLgjrzWz5sYADHewPTqRWRqmmreRpcW58qbPDH1qvY6htxaXLGKdThif4vxoA17mQ3UhibG0dl6msPWNNaKPP3QODt/hradguxYR87dT6e5qScSyeYrIshXAUdjQBwkGoy2YMbZCY4I71u298t3Ajb9uwc+9QXujkuxOTweg5z6VilJ7abEb5APKjofagDsILmFYX3RbgW4xTL+1JhVcDkYyT0rCttSEqKGLK2cFTxg1u2u66CQGQu0hwMetAGUjJZXMM4JL52k9vStm2uJ5pfIZz0LHC8VkazYvBJ9nYjcpzwat6VeF7Zzncw+U+1AE0ty4ulTySEVsF8enNLK29WlIO1Oee9PdxNEV2AsOrZqrPE6xRW/zFMhiM0AZt5eNMoXZsXPBHepYXEcJG3cxx36Gpnh8qEN+7wW5LdqngTdb8BRkkkkflQBBJMkcqZiJYDDZPtTUizCsvCrtORnp1qXaRJuZtw+8flp37tg8bsfnwRhentQBzOq2bXMT4UALznOc1hafMsTEHOc4/Cuyv4zIu2IDao5GK5CeNobtSVxu7YoA1llZ7NlizuByPpTbWZ/Oj3DhSSxpdOcmcxbgAVNTRRAZRhjqaAGyQYvI2UfKTnFXZbdHG9PkcDqKkgj+0BGA+Ze9SSptfI70AO0pp4flkG7J6mux0+MPghBXKwMQ4zXWaI4PWgDoba1ATNWFRQelNhkOMAcVP1HAoAdGRn5RTt4IOfWmK21ScYNMZgFzQA2UjdVadxxT5CKqzPg0AVbnBBJrlNSAe4OewrprtxtNcpqDH7QSTxigDDkPzn60u7CCmMcyP6Z4pcZUZoAkj9acVoRQRxxUoUDvQBBkjrTHbkVPKMgVWeJi3BwKALmm2Tag0kapuKjdW/pejRSQvFLIqnk4rF0ad7a+WOP70vyk1thZob9U24KnDHPXNABY2tml4yTRls/KDUE1s+makJLVcJuyQa2r23jsp42LA5AYFe5qzqE1pNaRSxxkkDkkUAcT4hnM17BPIhVXkXoPeujngEt7GbfDKwXH5CsvxPcJcaKjRRhWjGScVa8OPKWt5WIICbvrxQA3xE8hmhifojqP1FWnij2kbscZH1xVPxNdedOjKgBMiqfrxUy7+AVzjjNAFSCNvOLA5KnB9qfdFUOY0LD+IU9Wj854gSCeWIHWidpSQYRtI45HWgCK3c+epQeVGw5x3rP1NGml2hhhiP51ftSyzSBmwccVVnVZL2FNw+RssPWgCldSAXC2qggR459eKsQyJLK4VfmC4H1qC6VP7THpnP1q5DEkdzuB+Q80ARJOYp0WUYYcZpImMTzPncCeKsS28cpL87t3FMOxBtAAGfmPrQBQZ3XeRnJGQBUEN45bbK2Gz0NWlG7MysXKk5GO1Vn8mefzCCuPagC3bpI0ikAAA5q5OnmqED4APJFVIJEaJl3Hd2qWDc0bRBuetACR28dyzRucsOhrOuUMWLcdQetXFd/tXyLgKOarXME00wkyQp6DFAD2hLWwdT8w6io4MvIS2MgfnTWlljO1Wwcc1CtxIWAVOVz83rQA8BTcbypXB71DNMROEHQ0+TzHPmH05FVyv8ROHHQn0oAniZVkIFXLaSQyAJ0U8isiGXZc5cZU9BVuK9ZQ+3gDNAF+6mjF6jsOMYxUxuoYGIVfvCsmK7huELyZZlNLI5kAdWAA9fSgB0ssBiluCDtXt61l2l5/aDkrEQFPWr01zH5ACqNg4b3quojQDyBtU8tjvQBPOjRAfJ+NVBIFfcR1qw87tzuzHjGKrSSK+MLxQA7Lu554pjyJCpxyxpnnbX8tST6nFO+zhtzjkehoAgeQvEM8YOTVm3tNj73I2OOAKERWPlPHy1WILaVkdehXp9KAIJLchnRDhuoPrTFRlICkZI+erkaF5RIcbYuPrUsBiWeTMed/b0oAiT/WspYA7eK07MxzRCLZjaOPc1QvFhWWJ1z6HFaFoNvKkEqB+tAE81sPJb6ViR24Fx+NdPNHi3zu6isNV/ffjQBfOEiFJDL81MmOEAqKE/NzQBrRHfVtIwBnvVC3bBrShy2KAHqrE4qUgAc1PDDxn2rP1O6WEEL1xQAkrjd96iJgW61gm+d/WrtlOxGWNAG2qDcK0rMANWPFNvxiteyBLDNAGJ8UOPB0f/XYfyrxavavikMeEI/8ArsP5V4rQAUUUUAXNJ41i0/66D+Vfa/h058PWB/6YJ/IV8S6eT/aVuQ20iQc19seGxjw3p3Of9HTn/gIoAh8ROFgtGY7R56/zr5A8X8eMNV/67mvr3xMpe1tVXHNynX618ieNH3+M9VbGMzngUAYfvSe1L2pD1oAt6Uf+JvZ/9dl/nX0zd7k25GRtGMfSvmXS/wDkJ2Z/6ar/ADr6fnz5aEDJ2qcfhQBSCkx4GASep9qX5Q7FxwfSnNC3ls27kknH4VGi4QZUjk5oAZNlTuUEj+VQqS8RDYOMgVNPLIN6KBhxjB7VGABHtx82OcUANJBiR2XOBSRMHyxyPSk5XcpOeeBQqkJkdeaAJGmEZKAZznJpHKxKG5PJFESyOzFlGOadcNEqlCfmIz7ZoApzYZgMZANPmj8p9obcoJ6fQVHvDSIh/SrcitA6JtDbwTk9qAHEbYyxB3HkD2xUZKZxnKjJxUzMvlsrH5RnmqrzRoWOPU0AJOAzjGfmJAx2qMgGLY3J5xTkYeazkgg8j2pxi3hmBoAijgXYCxIBP5UxlEMgAc53Zq0WDwbAMKePemBVDbSuSO9ADI3I3s3OecVLBLjOEAGOabNGCeSFA6Y70jRDH3tpxx70AW0QFST0NLtVZQR0Kj8SOaq/aNiBHPrzQboF0iXn3HagCd3X/Ws2GHG2qroXd2kJ6/KvrTTJ84f7zY4A6Go5fMPltkgZ6+9AFpFBjKcBMZA9DUMrNECv8HfFJHtSLY7kse9SSEFM4yCDkd6AKkkilVyMEGsfUbSEgzJgSDJzWrPGFBLHk1yfiPVRY2DY+82QKAOltbqRIoLlVLxkc4rQguortPLKspJ9Oaw/DDiTw3AJJSHLZArfsSLZGJIZipyCOaAK1xJFbF3K7j0zjpUIWK7tg6yADBzkda01thNEudu0NvbPf2qw9jGkKqsSgADGD3zQByl0rkqkSjYByapRWsivhtwTafm966t7FN+W4O09KUWcSW6hhu3Nu+goA5iRrhZMIW8vb3PcVDJeyxuGVslhkjNdLPbW5hdowS3OBWS2itJmTbj0x2oApf2tJxJjKjjB6UyYQzIfNXnGFf8AizUkmmPHmMAlV5PuapiJorlRIxCL95v8KAJ7O6ls5zDMWZW+63sfWt8tm2Zo3AYMAD61kSPBcptjQhOnPUmoEujYXSpIxkiz9/04oA3CqywNuypY/OcfMfpWY+nbTNKAnlBSVHv71eF9biPzkl3Fhjp1/Ck8yN7NFAPJy0YOfzoA5W5sSY1ljK7u+OCTmp9F1/8AsmVhdR/OudmB3rSkS3Sdnc5KlSB2xmqWraM13aPdxgq38I6UAXJryK/Z7iTBdzkYqjZn7Bdys5CRyqSAeeaz9K1AKv2a4TEitjNblyiCIzKquyYIxyaAH219ELaRicnHdTTGu2ljjkLcggYA6iqE0sjYCxbVYZc9KvRxlIRMoAQDuelAFhp45lltVgbK87iPbPFMMMwgyWLDaMYxmn28iSMDNtAA4IJ+bmnGXy5c5Gw87RzigCCRmZVh44Xn1qfYlvCpjUO7Lz9aY7R70mRTzy3FPEylEbBX5uh96AKN1ZP5ckhkbcF3ED+VcvqaB5DnhgM812srxss8ZJywGDXM6pZbg0uRk8UAYlrN5c0Zyd2etat2JNy9iSCMelYIUxzfNnA4roLJBPNGNxYqO/pQBtWMfkQqCOWGc0yRC8u0VdWP910xiooYis+48g0ANVQGAA5rb0uUxOOaopEjSEgVZgQq+RQB2NvcfKB61bSY4rDtJc7cmtaM5AoAmZywNRM/GKkOAOtQOR60AMdqrSfMfpUjtUeMjNAFG55Fcpq5Kykg9q6y5GBXHay373HegDJwShIFSL91R0pihlQ808AnBoAnRMHBOfpUmwU2EHefpVgLxQBA6cConB7cVb2g1FIMZGKAII38maOTdhgwOfxrrb4vhJ+cSoDmuPZeveuojv31DQVhVBuj4yKALn2drnS1uN4/d9icmremrbXNjPHJKfkGQBWXpMcu17bBbcOBVvRreQX/AJLkKMlW9KAMfWJ7ePQ76HbuOwlTjpV3wTPCdNjmdNwEWMVS8X2sdnBdp5g+6cY+lL4Hlhk8FZDnzlcpjueaAJNTgE8+QCymUMCO3NW5YiICqt93r7mo3SSBkU8qxGfbmkvGdYmWPk5zxQBUluRblYwMt1PrUTPOJQ7v1HygUjFnX9+oDHGG9KiuJVVUVNzOD1xQBcspZHZxIiAkcE1X3OupRxBVBIOTjr1qW0iaRWkcDO3jmqPnyR3e8jfIqEfrQBRv2ljvyxO7stXy7iGOIjBbkmsy4nM95gR7cdT71oQziLBkUv2HFAEquyzxgFipbmpJ7IyyMwkxuOdp7U6eExKpjOWY545xTAwLszP8w60AOgtvsshUr8jcE0y502AxvsfLZyMUM7SDliAOtMt3QPIGLbu30oAyfJkhux852ira3HkvuB56VbkjRssn3ves6Uq2UI+cdcUAWRM0c4bgq3Nac8YkgUx4wBXNi8KrkgHae9WrbVN5eN2xkZFADLhdh3scY4NPVEZV2d+vFO2pLCXJDc9Kcs0ZJUYGFoAoshhu/mbKelQ3IMrkOdoP3WFW7i2ZoxIThSOTVOc7oAI8OB3JoArpiCQbmBx61AZC8zPyATzjpVh0VYA0nLk1TIY/KoPJoAmU7Cdv3TzxQJXVz3UjgUm0xSrGzDp61IUypA+9nAoAhYjbuJI9VpEn+QqOtTJbkMQH3Me2Kelsq5EoyfUUAV1d/JZQKsW0JKFXIx/eqVYmj27QNhOOasyxKWRMEIvJwOpoArzxJC6eWAwKjPHSrPkiSUOq4UAY9+KtxW8aBC7Agct/hQu2KQoMsWOenRaAKU0aKN4B8vqTToriNpMKTuK9ParLRJNHJJGxVVOAp71Ta0+YSo+Bt+YgdKAHxEKBvj+VjgY7fWponjFwQwVVwQPektCvm7JMhMfePerQtrfcTHyQPlB7DuaAIWjjlT92gKKcnPr6VEn+s/d5GTzzU81xDbACLBXHPPf1rMWRFlG6UkscgdKAOlB322TnpWYExJx61Ml6ogIyPzqCOXewNAEkqkkCmqMVI53Gk2n0oAtW55rYtB8qk1k2cRJ5rUDeUgFAF+a5SOIrnBrmL5/NlPOafqF6UJyeKy4b1HY7j+dAEojJ6Yq3ApSMZ61BHIp7irsG1yBkUAaGmxGQj2rqrW22qDWFpiKp7V1EBHljFAHHfFUf8Uin/XYfyrxLvXt3xU/5FFf+uw/lXiJ60AFFFFAFjT1zqVsD3kFfbXh1Qnh3T1Ha3T/0EV8T6eQNTtSeB5gr7Z8P/wDIv2H/AFwT/wBBFAFHxVuFjalfv/aUA/OvkrxqAPGeq7ennn+lfXPifCWlq/cXUePzr5E8Y/8AI36p/wBdzQBh0UUUAWNMx/aVrk4Hmrz+NfULIGhQo3GxeTXy7YErqFsR18xf519SvEWRdp/gHH4UAVllzuXGMHk0wnK/J6d6kZME4wSOvtSMN+3gEe1AEUsZCiQKMk1UMZVwWyCRiroRjjdkrkHFMkYOyYU5oAoIn7xn3ZC9qDMHZccVMwwrAHkjkYqo0RYoAcetAFxCVgOT9KpSIwZmY7sjgVOxDkKr5VeoqAyfvQ5O1ehFADmijCIw4c9qlE0kgyR8vTNRPII5MnnAzmpwgNisgccnJFACS/dYD0IqhMSTtI45qyZgrFmyVPYetLs3RrnvzzQBRVmMBBO1txx71pF40RVYHjnI9ahdPKZTs3A5/CnsS8XqOc0ANiPzMT07U4BtuSOMHmkYbNoOMHp604McFTk0AMfYxK5ySDUckiJgsc8YqOWcRbnI4ArON4DN5ajJHzZ9qALh3TrszgY+9VyJIVRUB++cbhWSsnmEx/czz9atWswUYHQEkfXFAFlUJQbBgDAHvzQyMxG4/KoJ/SpY8HbzgLt/nTJJAEK4znHP40AQpjJ38jPGKnjkQJ8g+bvmqsrboy4T7hOR68UiPyF6EYz70AS3sakZ6ktz7cCvKfGDkzmMt8ob5a9RkmcK+QAGHB968p8ZRNFeKp5LZINAHofgySOTw5CHjXK9GrSibdMHaQL3HuK5DwReh9OWB3PHYV1sCxwTqcBh2yaANBGjUsI8kM2ST0q15hdXYuAqj1rDR3d5iHKrnIUdKnbCW53EtluinNAGnE22HLFdvQE1WWZij7V3hT17YpYpY5ECBgAOmfWlntmRDHvIJJYkfSgCupSM5l4JOQR0q0syrmJU3Zxn3qrPbs2CynZjmpWQLEDuIYsAp9KAKQjLXLu8bKmcgVl3tq0iPJEM/NkA1rPNMgILZypxmqE8jSDcwK/J91e9AGK7PbzZOMZ5FWorbzokZgCrseKW4tVcDzIyD1FQFZ7YIoPAUkc9KAKl3ZSabJ5kJeSPqw/u1o2V8v2X5V4Y/MO+aZHfmZnjIUnbio54HhbzrcZH3nA7UAWbhV3x7lAOMqD/AFqXM86BZDlFPJHc9hUFnKl6w3AhV55q85RVI3Y24O4dDz0AoA5jXdOEaC8tjiVT0HSobTUC0DxkEyMOQK3LseYzkADC5YHog9frXIzxmyuVuY93lk/NmgDbtrguuGwBno1aIaVwYFdMN0OKxdNkFxMQnIxkCuihBE4iMWAF3FhQBSuOHX5ivlrtwBwTVx0V7YKoZXXGTkc0Nbxb49mH3t8xJ6VZdbaL5ny3sMmgCubdxDKWyFBG3J60yUlYw+wMOhGat3F4jKqpH0HyjBqtK4JCBSrHBPy9fagCmYvP4O5Oem4dKiureNoHwxJA4zWo1s+d8apvC857CqcssM1uQoxIfQGgDibpCj5YAfNXQ+HofMmab+EDFZWqWrqSCOeua6Tw/B5WmICPmPWgDTlH7vioIhhcmrWP3R9aZs+UCgB8C7jWgke1QagtYemK0hHlQO/agBbX5Xya2oXyARWVFCR8p5NX4VZABnigC0WzUTjOaXdt5NNLFvumgBmzNI4wuKkXPeoZGO8jHFAFG64Q1xOrnddV2V9JtBHQVw2oSFro896AKxGakjU5pBjIpyMelAFtMDFTk/LVWPrzVk/doAaRjpUMucHmpQ23OarSvuGaAK5GGOTxWz4avmWWW08sFZAeaw5DgVPo12tnqlvK/wBzOCKAOrhmksroMFOQ2BV9IpIrt32ncxDCq+oXcTypLFFhRzzVi71LzYoblAQCNnA70AYvxAs5ItJa4YYDp+tZHgy3kTwm7q+HEwwPbitbxe0994UcncdvBJ7c1ieE7h10VIipyH/OgDqtzixUydS461UuzKiOEIAP51py3ML6RDC8YSQuOfxrM1KPy4l8t8j0oAzBMpxGVJbvmpZQmQ5BUKOPeh7eJ41kY7XXn60sk4eAeYNqDpxQA6NXFq8gPLj8qpwxMxlZTllwGNTLdBbeQKpZGOMiq0JlNvcRxD7z8EH2FAGNdy+XchR2PP51ehLPG4ViRjNZV+pRtrk7t3NaUD7CoHyqF6etAF+xOSCWYnHOafdOEV1QAZP3qoR3TJMSRwegqWVm2MgKgN69aAGq8puBHG+9sZqCWe9WU74gFHBIpYpGtp1746/StGWWJdxXlXHOe1AGQNRBn+Uk8Y5pBc4kZiOalls08+JocKucsKbdWu05U5LelAFKMhpCrj5Sc0jFVY4HParbRwmDLcP0FRSQtt37cEdPegCEXDgsgOOKa8pjVEDEsetSfZ1ClpG/eHpSLFvXlR9aAJ1lmktyjsAP6VXfYkYWM5Oec1BLkSjJzj3qJ2Z1yF4zQBYnkURop5OetQ+W6s0gOQOlARjHlznHSrNsyFAjjGTwaAIooftKGRhhk5q5FCs2GX5WHOaFhIZ0VsAjg02J/JSRmfOOBQAog8qZypJUD73fNSxw3AkVWVS56flT4pBINyAZHc0jyosZYE5zzk8mgCNyGjMZzuU5Y1PDcefJ5UY+6MEntTUeNwHwAMcL70NPDbMoUDzM5ZhQBahURS4kcNGp+X3NTy3apvDIuSOSO1Qo8TJ57qBnkHNZUtwA7gPuUnNAFi4lk84SBdsWNoHqfWpYJk5jbuKzmvEZSGPTmoDcqT5gOGHegDRmmFvOFzuHX8KpzXMsTsyMSG64NVxKZXLZ4xUci7I/MDcZ6Z60ASLOSNu0/jRbjzJJWlGNv3KiiyxJDc5yK0vK82NdgGe5oAh3sI8e9aVlnaM1SnTy0UDknrWlZplNxGOKAJAw87mrkShnrNeQLJ71pWg3bTQBrWsA25pl2Co4qzAQsdV7x0CZzQBzOovknNc29wySNtrc1N9zkA1hPH8x4oAki1N4+K1dP1MlsmsPyFLVoWNsdwI6UAdxp16WKmuy0+YMoye1cHpceFXiuusnK4GaAMX4q7f+EQGP+ewrxE17T8TmDeEf+2wrxY9aACikpaAJ7Jd99bp3aQY9ua+2/DvHh/Tx6W6D/wAdFfEtkdt7AR18wH9a+2/D/Ph/Tz628Z/8dFAFDxe4GmWpZgqi6jyxPTmvknxic+L9UI/57mvqzx2iPpdpG4JD3sQIzjPNfKnjJdnjDVFxjE54/AUAYVB6UUUATWXF7bt6SL/OvqqVR9nQ7sEoPx4r5TtT/pUH++v86+rGx5ULP1CLj8qAKTMVZguGBxk08gxhSuMZ5p6xYchfuE5oADSlEPQ857UAQxs28buE45IpuXMuNo2joaWbfM/ytiI4HTvSNuBB3DBAxQBUmcb9mMe9Z5ZhOw3DBNXZT85BByR1qiSFlKEGgCSM5RoyME/xVHcBFjSIo3HepYvkJLjjtUF3cDeoOM0ANf5jknAxjmp4G2kKx+UdvWiUL9lD457VAjgRksec8UASvI0l1gIBGMdO9TyB3wAMY7004iiQgZIwadlyinsRQAxpsDBYcginQAhtg5zk5qEqGZeh5zT42UPltwLH0oAmePL5bAApmRuwe/elZsE45+tZ11M+fl647UAOvYwVdAdzEcAd6gttKWO3yFbcSM56/SrtpDlld+XIwPar/mZQMBjnJFAHPlcqRICpXAH51NGoVMDtk5q7cW7B920HPaowoJycDOOKAAhsNg89ak2YwCcndggdqmhQHPrkAUFfLBc9TnNACQQJhtx/h5/GqzxL5nP3c4yO9WnkXlAMZIFVLzese1egHWgCpfYjhLKSQDwK4LxpEZbOGbb86nmu65dP3nIHNch4skL6exUcBsUAVPAk6qxThnz0r0L7O9xIpwRjrivLvBT7dZET4G/oa9etY2iiIDgkdyaAKUipDEUU5cnpVeOcrFIYgQ4bnNaE1mzXOUI3kZGaz0DozFkwG4bj9aABpgzKWBUYycda1ft8AUHcWGOc+uKyWCs7MgLBeOlJF5qODtBQnkGgDUmuGk24cAMAfoKjmYOPmc4DH+VU4mkkY7lA5APPagySbizAbeRigBj+ZNIiMcRbCQfTrUohiEyyKWZNnSmNOrFQepAXA7c0eaYWlIHykhVFAEsvlmdSxHzcEHtVO+s82++PLBuvsKsTL50yOpABfJyenSomMhiwpIGScfSgDCS3Md4kQGAx5NaaTQRIyKc84I9aepw5dkGdvFVbmzBKFOGblqAKN/mFvtMMoWQdFXpj0qLT9WWWfbIdrgc57fQVJcWEm3uW7VianYTo6zQbvMUZ4GKAOqESiHcWLwDPB+85Pr7VlajbefFKpACqOcjr9KraP4gkumW1uMQyRjPzDG41tXq77Hywp8xlyTQBxej3psdSCsCEyQCa7+G5AffFg/KNxPOK891a0a3KyYIC4NdboF2kmlpKSAzfeyfSgDXZj5jMqtjPHyYz6mg3QkDLsK7R145p+ZZ15UBRkA5xwRR9lZoHKL1bA4zgYFAGdO/mXUYVicYwR71LN5ssoVV4A5OasW1nGzxq/wApXrxjmlvYfIR5EHIGQc+9AFMn5jkSNJ6A1DlVHyrgg9BSrPKrrsbIcZZscinskOBKeWwWPPegDL1FPMkAwWJI9OK27SPy4kUDAwKz44PnVjnLnOD2rXjVvKB9KAJNuBinBdzYoXmp4Y/m5FAE8KEbQKvRqScdxTYohtzVpEwoI60ASRLz8w5qaQFVBHemxH5vmqw+049KAKr5280kRYHoaknzwBTYg2c0APdsdjVd3I5xU7qzNu7VWnfANAGTqUhdWPSuFuWzcOT612V8+Uce1cPcNmZ/rQBMp+6aA5DECq8bknB7UqMScmgC9FMS2DVsuAtZcbfMTVgSk4BoAn3Zzmq5bApDJ8xHaoXegBsh556VVZsNkHocj2p8h3VXYHPWgD06K0t5vD0F15jNIVGVqe38l9CKIgZ0kz0rN8GhtQ0oxNLjYDnNbeiLal57eWTqCMe9AGTcl7jRb2zcLgoWyK4zwpK0rSR9FibH64rupXhgllhVTghlye9cZ4KWNtfu7JsDMhIz9aAOx1uKILp4gIJzuYVSu9hjDAEHOKk1NGi1IohyIlIz9f8A9dULnf5RyduB+dADSgwC2OR09Kh80MAsiBkNOj8vAnEnXgg1GFyPM27k54/GgCSa0JtmCfKG5AWqNvAY9NmaMky7zzn2rTk3vaDYcJ6mq8X7rRpCw+beelAHI3Ku12okHzZBJzV1WaY/KeF9qrTxvc3684weMd607eVx+5aNR2yO9AFHzZpWJC7Qvr3qSO83hsrlh3pZkdJJRghu3FZVxO1tGxxhj+tAHQMY5IVf+OpEiZ423dPQdaztGv473ZH5bFl5OBXStBtDSIpAx6UAZMcMjMxPA6DdUnmKiEYXcOKnOGgLTHZt5rDN3BJekoxwDjmgCe5dHIAXqeaj3+fLhmxtHFPuEMfXGCMg1ml2VywNAFiYbmHPzA06RhIwVMjHWqLzN15pPtRC/L971oAuGGMOzck45FVlkEZYcbah8+RmwOGPWoJA0dsWZgWLdKANAMu7lgVPaklvI1PyrhQMCswzujgcfnSPJvHI5oA1BqO6HBPIHBquLlAAWOR3HrWW2QDg4qFmbb96gDcgvDG27dlT/D6U6W5JkBzkGsSDeTgmrseWQg0AWpLs7wynGOPao5roNkk5J61FMgjQJ175qD5SDk0AWP7RkCAEsQO1VnvXYkjgHtULH5ajjXc2KALUbGRvmb6VcTZtAY557VWt4WMg449602iRI+VG7tQBCFAk2qwxnn2ppUv8nq3HtSSDYPlPLfeNKkgDdO1AFqGIRkMgyO4q8qcn5tozWZBMwkZB9RV6JHCuJDncMjHagBHO+5AU5Ga24k2QZOOlYtpDsnAJya2pTshx7UAZr8ymtS0k2gVloNzE1djO1aANc3O2PrWXeXjMMAnFQyzsBjNVC+84NAEcuZOarG3JNaAjz2qZLfI6UAZQtADkrWhZRAEDGBU5tz2FSQRFWGRQBu2KKij1rahPQg1gQSYFadtMSOtAGZ8SST4Rz/02BrxvOea9g+Ikm7wj/wBtRXj5oABS0gpaAJbY/wClxHHRx/Ovt3w5z4c00n/n2j/9BFfElif9Nt895AD+dfbugf8AIA08Yxi3jH/jooAyPGxX7HpysMk3seD+NfKnjhdnjbVlznE55/AV9U+OsJYWDZwBexk/nXyv45jMXjjVkJJ/fH+VAHPUUgNB6GgCW14uYj/tr/OvqojfbwnB+4v8q+U7f/XJ7MK+qlLLZQsTx5Yx+VABIPlCJkMe9VZ5HRmEbLvzk+9S+egYnn8aotIfOPYmgC2SscR55AyB71RLtJkMdpXpTZrgE7RJ8o61XeQSFck4PegCwWBxu596pvhg4zgnj3psk2NwBIXsKUYI34ycUAPysajLHp3qhLtnn6cZ61ZW6TeA4yB2qD5pJvlXv0oAsMGWEoD8tZzFvNCKWK9+K0nzHhSDk1VELN5nzAMBQBchbzoQI/lZf73eleTZIApy3cdqrxuWACH5h1qQP50gjK7CP46AJEA2byBupn2gFvmHTpUm0Rghcn+lRTIqxF+pAzxQAk1xsbHqKht4gXMsgOMcVFJG8syHIMajLHvVkNmRNoOxOx9uaAJ3l2kFcA9Sfap0lAC8ZDnBqGRFYcgBcYpISQ3lgqQBwPegCeYGWPI4bGfxPFVxFsOGAIHOasJIGU7m+fnAqJyC21o+mO/YcmgBw3nlAMdaecNgMMnpio1yNqoDwQD+dWNp8oZOGI6+9AEY2mPLKPMCniqV5MNr8gY7VNNKELEHLZ6/SqEVq84cyE4JPWgDKN3LM5iQjA9BXO+LYzBpqpn5nbJrtjYrA+5UAwetcd4zxLPGijpQByOhz/ZNZtpW/hbFe4WwWay85WGdua8KkjMc29RjaQa9h8NX0d5pAYMMFMH60AaStJ5iyn5sDjFLu82JpGCgHpTrXMpKv8qYIA9akit0jURryF560AZ4Hyoi4+Y/NgVE0gDGLA44rRki3iR4yq9AAKbNZLCgkKjB/nQBlFMEkA8ioTb75Wd2YDgitBLFppiu4hRzTXtZUiKSSbgeAPWgCjmHfkMd54qtHNKbkB/ujkVfjs9oOFBwevpVeWHEKhZF3BsHPpQBMzxFcZ5PSqrXDxTBJD8o9KjET7hluxxile1LOQTltuS1ACtIuUkZwBnkUk17FMVReOOTVW4s3JIAJAqpLbSwsjAHBH3aANj7XEzpgcY5NRSJGz5JG3OMVl7pUhCquMnAJqN3nZQWc4UbgMd/8igC9JaWU1wJGhUN2YVWmuTCX8xz5DEAHuD6VBBckn96wyvT+dPmuUeEIFVh04/nQBHcCHVPMSNcYXknoay/Dr/Zb+5s5gXA+6Ow9aliuXsLn7KcGOTnd6U2YLDeJdxNwDggUAdtFA32YP53ynnb6CmJLIhcLvYs3rwKpWZYxiUuSrjGM1K9wYm2KOW+b6UASyhlRgxJY9Tu6VEGRkKsS4APJahp3kYLHjd3XHWomRY3kIU7++AcAYoAaQYnKqi/MODmonVpf3ZQIo/iB60vnKX2E8kcAinxpLhxK2QB6UAOijVrr5DkKOua0gP3QFU7CLYC3qavkcDFADlXAFXIQCQKr4+UYHNWbYr5gyKAL8eBgVZjhZiTu4qvCu5quqmBnNAAigNUrjJAFCoMZI5pSMdKAIJQcgCpI48Lk9aQ8tk1KpBHNADGwF5rPuNrMcValbORWfKSATQBi6owVH9hXCSP+9ZvVq7LWZNsUnPJFcOSSWB9aALCsDTzVdSFGTThJuoAmR+TUgeoAKmVQRQAFqYx4qTb61GyntQBAzVG3INSOhpu2gDo/Bty8U8sSlvmGMV2FravDf5kZU56/WvP9CkkjuwEPJrv54pZDC5DY28n3oAfrFnFDeDEm5duePWuE0u1dvH80dpxn5s+23Jr0qeyMukC6Zxx/wDqrzyxlW08cyyocFYmGfqDQBrxxzXN5cLITlWIznrVWWeORvJG4S5IOe1WrctNI7qSDuyT61WnUeYTgHnk9xQBUgOZDF5e4nI+nvVsWfkW+1ZCVc5JP8qoXBPlyeQzbgBz+NXVaVrK3QuFYcHPfvQBBdSyx2oHO0dKq/aQNGLMTuLHj9K0b6aP7AEfaHXsO9ZJgMmmhy4KNnCe+etAGdZuW1BXYDGf6VoWkkRmlaRPlBODWbafuZ843BOue9a0ewW+OAzHOMdaAK93PEW4OA461zOryxySJHES7LxW1fw+ffwxodqjtUg0e3t28yQhmY8UAVPDXm2d4JHTaCOpFdbdTN5JVG6nNYoidCWYjaMYqa4lO5VViSV5PpQBT1W8mluUt1ALFcYHQ1ktZstwEU4I5NaKpGWY+Z8471SuH2ONhJz3oAlu5jIEjUk7F6iq3zBPvD8aSNh52wDtzT3XCkkd+tAERLjlgMelROQ4LKMY7VNJGpYsp3D0qFcvu2jaM80ARje0ikUySNmYZNWiv3SBgdKiKkvk9jQBVERZwD2qwtq91MIYuX7Ad6lCruznmmSMI2DLkMvRgcGgCG5s5rOVorlWR8Z2tVB1GOKvztLdHfJIzH1Y5qpJHtUc/WgCeCPcFNW1UbfeoIiBGuD3q0m3y2Y9R3oAqTMeFqtJ8tTPKjyFe45qCQ560AM61Zs41Z+RVdFLHjpV63Ij6rQBcjT5zkYUCoJ7k8gdQOKkmucAjopWs13yc9+lADhMzg1YiBKjNQxqcDC9asHKpy20Dt60ASAhCGB5FTrdTKm1AXZj2qoCzjb5ZJPI967fwz4YKbLi5ALMNyoe1AFfRdDuCDdXGcHsak1ILG2wHpXazQCKAgDAxXC6r/rnA7GgCjCRk49auA/LVCMhaka5CjGaAHz4IqsvBoMvmHAqWKBmPIoAkWTaKsR3CgcmmfZC3A4pp09yepoA0YHjcdalKoOlUYbWWP1q9HA5UZzQAiMd3FalqQoBNUltypzirCEqOaAM3x4+7wqw9JRXlHevUfG758MN7uK8tPWgBaKSloAltiRdwkf89Fb8c19v6Axfw/pzHqbaM/8Ajor4fhO2eHHXcP519veHOfDWmZ6/ZYs/98igDC+IomfQbZICqyNcoAW7c18v+PnaTxvqZcYfzPm+uBX1J4+yukWr56Xcf86+X/iIu3x/q4xj99/QUAcuOtKaSloAdBzMg9WFfUYkxawqenlqePpXy7b/APHxH/vCvpeSXy7eEE4BQDI+lAGddTukwBc+X3NUTqhZ35w4HBPcVc/cXBceYW2HkEVk3VnmXPT0xQA43rNwCM05rsMu3p+NY1yk0UuEzSQyPu+due9AG3DOJCVbg+varaSZX5TkDisQSY4B61dhuEI2R84HNAFsxRl9/c/pVh3VJ1ljwAfWq6Mrhdo2nvS3EirsC84PagCy7GZmcHBFVtjSSEgAfLzk1cRUMQJHPpVdsibhcDHQmgCgspgmBXPzHFaUQEzc8dzVFkEjv0Xb0xVmNkj6senWgC4WLAgAehqCVkWJiMkjt60yGZQGIJJqJJ1dgXxnPy470AOt4mUHzP4jUobDKOpbrSs+9AxHPpSOVPC/e6A+lAEw3yIcAAcjmoniEcvnEfdPb2pQ2+VRu2qCAPc06fE6Z3beO1ADlZeo5J4zUqsWmVWA5H86pQhkfnLAAmpHYbfMYkbckAUAWHzCN4I7k+9Qvdb3U8kE4wKrlmdlj3fLgDmrEaxQxZPJAyKAHxWqshJ6nPWlZfvKCMZ4oWYsduMfNSEfvB6EZoArXbERsuR7VwurqZrs55xxXZ3BJVz1rl5ohJcSN70Acxc2uATt69a2/Bt6YZWsXJ2l8jnpSXcB29KzbDda63Ew43NQB6WLuRLggIGA6D0qbzCHMgJ2sQNoPeqlo+d7HqccHvT4Zg+HRdgDFcE9TQBpbVTBK45ANSyjdEgeRcA5wKzLi6fYOcnOTiqzahFGWf5lcHqehoA10iYQu5/iPyjvxVV/mmiYnlT0rOuNSZlDJIckHA/CiPV1jjAdcuo5JoAvvZyiWSSNwdx6VUXTyG+dQzZJI9KiOvOc42qnPNS2+oBpsnhCuSx70ASTWKiJGUYIGfaqknyykAEA8c1cm1WPYASvlhu9VZNUtXVpNox95fqKAG7W8xhj8ajMDSzbyo44A9ahOsQR7gzrkHdgGqn9vQbiQcMW+UdhQBcmtirogQMMk9OlJ/ZBlh3bgq5xULa/CoJbr6iopPEUMSD5h5Z5x70AQ3WglW4fORnio10QxxoRKASPxqC48RozAq3bH9aoyeIFLqCTxyTmgCS98PzTLiJwX65zWLPHf28TxSRNkcAjnNbcOvRx7juwW6d6ih1NFlJPzfXmgDR8P3sVzp4jZj5qDGCKW6uGY4/iHAYelY6X8cOpLsAG/wC9gYrTkBldQvAoAu2MriYbgMH+InpVgSBpJM8IW4561AIcxod3yry2O9SYaeLy4vlIOQTjigAmtkadehx3B6U4qAhRc89GNNzLbpl3O7uSBzQkpmlCgHA7kUAXbaIqgX9auIvIzTYlwMEjpUg6igCZFXnipYIsSA0kS5qzEPnxigC7DGNtWAoqKNcAe9TnjFADxyMUNwKUU1uRQBEWyaDkc03+KpG6UAV271SnGQRWgy1n3Z25oA5LXmwj89BXGk8nFdZrp3I9covFAC4OME0JwcUuaVB81AFhe1WUXioo1zirapwMUARstJs+XOKsiPPWlKgDGKAKDJmomTmrxXJ6VCyc0AGnSm3v4nx8p4r0b+1DcaSqovK4GcV5uoZW3HoprvtDukNsIljVgy5BPrQBf08y3kM1spZgOcV5/fQvYeLyJOPMQ/yrs4Li5tdTITCBm5x3rA8Y28kWq2V0V+aRtvPvQBegxFY7hwT1JqEwlm3HnuMfxVMTHFCkcmS2Oe4pGV0fdwN33cUAVmiWV8BdrDqFqvLbNLKm5iF659K0GHkv1w7cYPam3UbAIADtHU0AY16MhWVOD8uTVe4URwxFM/dxjPHWtu/EcVgilDnrkisq8jjW1jyT0yCOlAGVZHzLso/C46Dqa0lDRMHPKqemKoWdsZZnuY227eMEda0nM/lFhGGGOaAMa6W4u9QMicKPTtWhChNsvmKzYPBp1pAdkmGG9+celOO+34lYYz+FADE2thm3EA/dFRyDflhweQB6VdaeJ7ZQgCuD1qgxZioD/MSaAM+fEbggjJHIqhLKz5UAjHStWdY/MIIyQKpJjDYTL98igCG33EdRv9anlkyApPXgiq/AJwDvoGT1OGoAuRRRqQBwD0zUfkHfJ/Cuefeq0TS7juJKg8VeffsRSeo5NAEcgXKY6ConQLk9Qe9SbQTj0/WneXu6/d9KAKiwZbOe2akeNJIcjgipWAG/HYVEoOPk5J60AVAAYyAMVXZBgg1oFBgg8N3qnOmG4PGe1ACxoFRcg9atSSeXbMFA+tMtIwuT19jUeoMUt+mCT0oApF8nOBmozyaZu5NT267uTQBPDEccDFPmbyoQDgNQdsa792Koyym5c4JxQBIHeZsdsU9U5AxyKdCAgA71MmA5PSgBwk6KqcipAGmYqEBPamQqbi4SKH75PNd1ofh5I5VeUbiORQBF4X8OEFbu7Xc4+6pHFd5a2m3LYwT+lLb2+1RwB2q9GCooAztVPlwH6V57fKWlc+9d7rbEREVxNwuXY0AYzK2cCgQPJ2NXPKy3FXLaLA5FAFOCxIOcVfjjVMfLV+JEYdAKbLEo6YoAZEqk5xVpIoz2qopxUyuRQBfSGPuBUwiToBVKOUmrkTEgUADwjAxTRAKthcimlcGgDl/HkQTwu2OzivKSOa9e8fL/AMUm5P8AfFeQ0AFFFFAEtsM3UA9ZV/nX3DoeBoOngDAFtGP/AB0V8PWbbby34/5aL/OvuHRDu0KwPT/R4/8A0EUAc/8AEGH7RolqrFgGu4wcfWvl/wCIBDeO9WAJIEvU9egr6f8AiJMINDt5mbCrdxZPoM9a+YfiFEY/Her44zJkflQBzI9KWk70tADoBidT/tV9KbXlWIKVVPKUHPrivmyDHmrg5O7pX0irNHCg8vIaNcj8KAI5LdWY+UAJM4JHeqzxMziORAGB6+tX4yrDIXafrTJI/OB2/eFAGRdWqM24fe6VjXdnsL4zuxziunCl4ZE2gE/dqpJDHL0OOxoA5gxyQguGJPpVqzkHAHD5yat3EChztIPrVNAEm3leR0oA2IZVZMMclfSnQyAyjrj0xWMborM6D7zd617O9jZVG3BUYzigDWETnAC/MehNQyxsrbWwWqxGXWMSpl/YmogMyu5XLHoc0AZwCpv9aRJFbjqR1BouIjExZsls8iqkuVYkcHHNAD7qUIoRSQznHFV2H2aZcOWC9Kkt8XM+eoUfrU1xaEMwUZHrQAq3paQVdinR06cgc+9YsamPJfORVmG4AcbjtGOKANdsSnci464FThESLceueR7CoI7lAxPBwcinZk3BioKHA/WgBjsIQWPITkj1qqHB/eYbDdvTNWyAeccEE8+lRxoQx5UDrQA0sgfduByM4p5cE7VXJOAKeLVGLNuBboRR5QAGByOSaAABy4fvjOKVmcpgj5gentQVIjXBOOB+Gaau9XDdcgjBoAqXm9Ld9vYViQQiVjt6d81vXgJic/pWdBHmL0oAz57MscY4rn9VhNrMsq9U5ruzADCM9a5zXLAsrN1BB49qANy2kWaztphnJTPFTFVeNZsGMKc1keH5Wn0lQThY8qauvKWXGDs4GKAEidftLN5hYHtUd4ivESRxnsaka1LYRCQp/iAqSK3iGYpG+760AYbcyDYrjb6moJxJ97PfpmunEcIyoHbkkVSWw+2RyKY9uDwwoA56dnWMDAx3wahe+uvKCKGAFdKukRxjayllz3qQ6WJGIQAArxkUAcXLcXrqFJYimw297OxXzXAHIFdudHjiXadhdgOTViCwSEO3y+2BQB5++m3ecb2yTzU40WfqHau9k0+BAWIBJ5GKYLdcgiPGBk0AcM2i3BIDOwFRroctxL5ZdgoruZY2WRfkXHUVKEVl3GNRjrxQBwcnh/ZtQsT3p0Hhzz42IPWunvVaFvMCZBPy49KksoSUxswxBY0Acfd6C0MRKnkelYtza3MOFXduNeoTWccc21uQ2PwrPudPtZGcOwWQdMelAHmTXcucvw6EfjXaWl6k9nDMnJC/MPSqGraFaSMWjkO48DFV9JD6fI9u5ysn3Sf5UAdfb7ZowQwA780rLGjvhzk8cGsq0lG7ZuP3hnHatiO2MDM7yApg8nHpQA0sZ0w4BXoCTS2QzcNn7vaopkPkxsWOME8DrVuyTMYOMGgC+MBwB0qZB82aiIwVxVuGEsM0AWYkyCfarUMYyTUMXynHarqIpXI4zQBMmCgAqQiolXb0NP3cUASA4Wk3ADPembj3oBXPNADAC0hNPc4GKcMEnFMfHegCtLKQMCsy8f5fmq7IwUkmsq/uA44oA5zVNrrJjsK5QnjHvXS3THzpEA3FlwBXNSo8LFZAUfPQigAFSxgZqsrnOOKmXPrzQBow4FXIyMVmxNjGTV6MggYoAtAZo28mhDxzSh+TQBCV+eomX5zVnq1RsOc0AVitdL4buY41UOCdjVgbRV7R9q3pjZsBxxQB2OpyxCcS28fGOSfWud8Yzvc6ZaXhOTFIuMeucV04hgOis4k3sp5Fc5qsiXOhtYxJkmVTnHTkUAQSo8gR0bgqCwNWIszAZ+VBnk/SmPESqRP8qoAB6mh43mhYn90AcAetAEignLcM3TmnXDy/ZhmMMPanhomgEQOJAPmI6moNskbMobco/McUARzndZpk5P54rP1uQOkKogVNoxVqR3aFyVBX8jVbUI49qFmcbU+63egCjYxZXC8nPQ1pQI5JR41xnPWotNgHlE/MHOccVaaKe23NlCCO4oAqTmKB2ZMAKpLYHPsKqraNfwo8rYXOQDWoLNsC4aNWYjgCqcsMjzbQXVFPzccUARPaRwKx++OgArOnLpIMrsAPFa1skm5kXBy3f0p9/aqcq20sRQBhNZSOofd94E1XEG1Dvk21o2zsUMb7iVOBx2p1xBEsG4Lg+nrQBiPbbgNrg81WdgjeWRmtDd1+XbiqD7XkLKMk0ALG2E/GrDP5jBEyCByT0qu8ZWJcde9WYW2QsWxyMUANY7Su7oO4qVWV1J56VUwytweD2q4hAQAYFAEXAcjH8NQkNkqpxmrp8suOOSKgkjDSgE8CgCuSYxtbk1BIBsJx3q5LGpmVVI9qJLds7Co9aAIrcrjnrVLWJMeWtXGUINyj5h+tZepuXlTccnFAFZRuNX4o9sQJqnaIWOTyBVy4kEdvweT0FAFW6l8x9i9BUkEQ2j60WVjNO24KQD3NbcGiHZkv+VAGZs5O2lFvLJnANbcek7Vb1Pc1Xd2sn8vaDx1oAm8OWaNch8EvnBNepadaeWoHfFc74R0fNt9qdAN3zYPrXaRIByFxQA9ARxVlB8tRhelWVUBPwoA5vXGwhFchMODXUa653kZrmJaAK8afOM1pRQAxmqca5ata2A2c0AVjFIvQU3Y55IrWCBhUckahaAM3btp6rmllGD+NSRKOKAJYo+avRJgCoIiuelW05oAkHSpVjyM0kagnmrkaDb0oA5H4iLjwfJ/10FeNV7V8R1x4Ok/66CvFaAAUp6UgpaAJrQhb22yM/vE/mK+4tFIbRrEgYH2eP/0EV8N2xxeW/OP3i/zr7i0MhtD08g5zbR/+gigDlfinAk/hiCCTOyW8iRtpwcE183/EmNYvH+pxoTsRgOTk9K+lfiaCdAs8DP8Ap0P86+bPian/ABcHVzn+MfyoA5IUtFIeRQBJCczKpwPmHIr6VKqLeEMxI8odPpXzRHyyjvur6ThDLBD/ABfu1/lQAq4wAhzSOzKcLwe5oXerM20AY7UhbJYtwuOtAEEiF2G3Ix1qn5bxllCYHqa0MLtJRjVV5WiUb+RQBVmixHliMn2rPuYh5Z2feAzWwwDqGJwvpUM8CSZKKQMcmgDmow4YbhlwetWYt6zD5toHUVea0I5ABHrVaSEo25eSe1AG1DcEIMM2McD1rV05FyBKcIRnmubt5OVBOPXNbdtMHhG054waAC7QbnD4IPQ1hXg2xH+dbl0cRgNyT0rEuraaVwkZznk+1AC6OgWHLD5y2c1o7JDgY+XPNUXCpEu1trJ196upLviADc4oAp3kflpkrw3P0rMdPLw4Oc9q2pZlkzG/fArOmVQwA6UAWrRlZwT/ABcY9K0sLkpuLEHIx7Vz67o58jlfrWik3BYNtG3JoA0B8wRQNpGB+tHkhQSwJbkH8eKqpPvyRnDHGfpU5uMJtwegHP1oAN/7z5Rtxn8anLFZCg5BwKqxks54PAxUsZ3tljjOeaAFLj5QeOCcU+eJCw5OAQOKiCEPgcqB1NTRqWi3E/eY/oKAKc0e2B+cjHFZ9urcZ4Ge9bFwFW3K9TVCMDZhucntQBOQpAUAms/UId6NlTxzWxFGMDb1xUV2gMByOTxQBh+HFRbi5t3HyZ4A9617y1WKTZwu4cVkacVg18qc4cdq67yFnddy7wBjNAHPzI1tFjcSPQdahty8knmMmB15NbNzakBkOAB09ayo7V4Wk8wMS3INAEktx5gIMYAUZ44ojkmSNCgG0kYHekaPzGBbAQjGAaeVkjZEhX5MHLGgCSLfNONx4yAc9KsGHe7b8KvQY7UkCxLBtDZYtk4pZSCVRpsZ5oArTeWJY0+9gHnB4pYlxGpIyWPApZZxJCdgUcY3GmNIj7RvwUXtQBHLG8SEg4kfjnoKI0PzbpOD3PpUMkk8q7F59zWfGtyJ9js2GGBQBrySxFlyDxxmh2RVLLkAetUpIJ3dcnCgYGD3qcSKlsInBJOc5NAEMkuEIUBs8ZPSqRvmtdyA/OeM4rVjEUiH5dqqe1V5BbndMY9wzgcc0AZMl7PuLsrM3b0xVC5nlkyduwNwTnmt64ZCyyBQqgYIrOktvMLH+E9B3oAxnifBAJ9s1n3yeXGkm/5kORXSvYsuRkFsisLU7NmfAzg0AS6XPHcR7nJBJyQO5rd3POqhkIjX9a4zTLprO/MBwFbjJrsIpHYKqOTz6UAWZcIgC7ueMHtWlax7EGTVIhWmRSOcVoQA4BNAEhO5+laNvnbVNQGar8I+TigCwiAkE1cRB+VV4hkCrYO3PFACthUBpn8QGaf/AKxMelQKCQaAJW+tRMrdR0pQCOpqQAkc9KAGq21etRM5OTmpGA5AqAjg0AQzYKEmsS7CgcnpWvcNiOsC9kBJBoAwRdquo+aP4WAAP1qTWGt9RugWVVcrxgYqssUa3AZ+QZBVnU2tftMQjUj5eTQBzl9YNZyLvyFIyGqCKTDd8V119aw3ekRNuyw4HrXHujQysrDoaALqOSav27YHPNZEbkEVowNkCgDTV/l6U8DbyahXkCrCjNACqB1pjCnHg4pT0NAEJGKWNvLuInB5BpDTGBJBHY0Ad/o8KTxvG8ihSu6sR1RbqWKJgQGyT9Kk01pB5GD97jrTb+FrHUZdyAZXOQfagCm14Wfywu8hupqaRyVClTkn5Qe9QRMjMGYDJParzKs7qoHC+vGKAIkR5pASFQrwcdaSaK4S4ZoSNvcnr0q0BEGw33BnBPBJxUPmnyt43Dk+9AEEpUKjMuAeCPes/XF3/vApO0DIzWpcOsiYCkPgcgZzVHVV8kEyAgMgBHvQAunrIYFY7c7eBmpvMMgIZcnbxz0NM08rDbrM6s3G1eKstcK7NiNkUD+71oAqwzuiFWJGaZGzyXJT/lm3JB60s1xa/IDw2fpSMpWX92G5/izQAK6mWQ7cFfugVBPJJMd3lYwOuKtvAJJFIfB74HWoZA6S7Q5IA6etAGd5zwkYRSvfjmqs8ib/ADOnoCetXJT9onZSpj+lQSQCJdzFZM/d9qAMe7Yuxdf4uDioVjSOMhT81X7woEXC7WJ5FVxHGqszc0AVXyrfN0pgdpMgKQg6Gp2QH5RyW55qNuSqdPXFACbsSjjIApdsxk3jhfQ0xAuT8xBB9KladSMAHIoAcjyBwxAwKcW3TMSpGe9QqzNT/LIOXY4oAcIVdtz5C5606QgkxliUXoe9RKDtxuyCeKXIDlB1HegCMOituft04rF1J1e7BTpWzcFthzj8qwbg7rnj+HvQBYtiVjI28ntW5p2itdSq864XGQp70vh/SWuZFmlHyHpmu2h06JpI3LECMYAHegDMg05Y0wqjH0qwtnsjJVeK2RAoXpTWjIUAKcfSgDAlhkkjxtwM84qG00Q3mpIjfcHPNbbW8ryGNQQD1OK0tOto7R8gkv70AbdrbpBDGkYwqjGKtDrUUf8AqxU6gYzQBKuKkc/IfpUUZy1F0+1ODQByWttmUiuflGDWxqr7pzWJNy4oAkgwTWjEwUCs62Q1dwQtAFszYHFRGRmHvUAfHerETAjJoAgKlm5qaNcClcDsRT0HFAEsQzV2JflFVol5q/CuVFADk4q5F0qNY/arCJigDlPiSP8AijZT6SLXiNe4/EoY8GT/APXRa8OoABS0lLQBJbgG6gzn/WqOPrX3DoKBNB09R0+zR/8AoIr4egO25iIGfnB/WvuDw+xfw9pjEYzaRf8AoAoA5z4ly+XodkfW/hH/AI9XzX8Tf+Sh6v8A74/lX0Z8V28rwtbSDkpewkD1+avnf4oqB8QtTYfxFSR9RQBx4paQHNLQAsJ/eLmvpJpPLsLbb3jX+VfNa/ez78V9HQOJbK2JK7BGO/PSgCwrjZ1+opk0iL8jDII6VEF3Plc7fWkVBOzSiTDDjBoAJHjyoUkVUlVS5yxPFWmUk5bk/TimCLDl8jntQAxbcFRljjtVgQL5DDJPPShpSoHyZp6z8dPfFAFFowMqOKyLtXjmAXmtmfBJZuPpWZIDJMAjfXNAFYEkAntWjYSHb8rAYPSqLIVc/Nn2pbQiSV1ZtqqM49aANWSZmXLfez2qZ2WMARLlnXJJqnpTfbHeQN8qHG01tPbx7PnK+1AGLJbjzN5HBHSoIVdbiTJwpHFbTorJjZx61n3SAQbo/mZc5oAJbIum8HmsyVHRVBWta3ug6Ddxwc0+dYpFUKvpQBz7Sbc8VPazl5FXGQe1PvLI8MOF71REphcCMZPSgDe5WLJAVc8AU+NxLlm6A/yqgkytGokPzemae1yEyOuc5FAGgx2uG3YHFCPxjg4H86z2vDtznKjqKIpQJck8nBFAGoHyHGPU/pUkUbMm7dhQOlV1mHPupqxvXp3YgD8KAI7nayMw4BqlYqWmIbpnirtwnygDoRmoI4mDfLwM0AXRH5J3dc1HKglKnaeKsKGcKvenSDyxjuOtAHHXsv2PVreXIALbTXdwAq8ZDfLtBzXB+KYS8RdBgodwP05rf8Oaz/a+nwlDkgbZD6YoA1LiLdJtb5mY53D0qtfWrGIypksSOM1pyRNtIU446nvVMBpmVCH4OCe1AGXHaAKrbDnnr0FKzSQFUIGD3rTnt9sOxJOAM9ari1kVtzqcg8emKAKOSVJhJyTzxTBG5mPnDCAZzVvynJxjhjgEUr2n7gAPww53detAEH2WKUhlYiEDAX3pkkK26Pggjgk+1X44D5ZIAGAQMn2qu1pv2jn/AGvegCrKC6qVYID93FEpkl6RjIAANTtaGSM7Pl/rVtINrIdvKjnnqaAMueN4wo69PwNQxwjz28zkEfL9TWvOkZiYvhSxyOe9ZlwpjlAJQAKTGM9/egCwZUEAj8oKOhI71FcLv5iUBQuaqf2qqW8aSxq0itg8/rUUutWxLx+cFbbtPHFAD5rTcqsWUbhzmoJLcKQTINw4AFVJLqGSTBuBtQZwD1p0OoRbA8jAZOFoAJ4zuJyTkjp2rPnhZZMfeLdPard1q0Qbggj2rPl1YSzN+7BAHFAGBrFh5D+bk7lOeK1dE1QywKowWXNRXlx9otyGQCsPTJXs9TULna5xigD0Kz3vJvbvWqBtI+lVbJd0cZK4GOavbQcGgB0SnIPqa0YMhsetVoE+ZR2zWkEG4bRg0ATQpirGDSQrzg9asFCOKAISNo470bAOtSbOppsv3MigCMqKOi1HvzTg2eBQAhqB+KmcEAc1Wmzg80AVblwVrntSICnHXFa902BWDqjhYCxHSgDEdHC+YzfKrVBdOj3CBJCSVqSwJvoplzhcn8KgniSO4QKcsBjIoA1ZraWPSopEYEd/zrFubZriA3CAnYcNW5IskVpGDkIR0pbQQx2NxvU/vBkfSgDlYvvYP4VowAYFVZrcQygrnYx49qtWwAbaTzQBowjirUdQRCrCjnigBdvNNK1OFprCgCqy0zaaslKaRtB7+1AF2G7k8lGjBJjPNWnuGvWImPzNj5qybW5MBljT5gRknFX4IncIhbOTke1AF2K3CruAUlTwO9Qpcp57JjqD8vpVp4CFYBzuxxu4qhbItvN5WzdI/Xf1oAu2WblvLmTCjJye4pkiKt6Y4AyrjknpU0cbg4UtHj1Gahl2iR5GJLqAAFPX3oAlZxJLmML8pGT0NZmsKwcq8jPnknHStm1i8yYbgEXuTwTVK9EH9rIjEtHyTn0x0oAW0t3nsY+FG0cDPWmS5tyZnwzHjbngUtpdmJZUWNtuSVbHQelEkL+R5jKxBP8AEOlAFS7UTXKSIimILkkDvTJmYQqUJGeTxjirAMcEXlnOyRuOKqXBxJsGSpGetAFlceUuG5cce1RSReWCGO5iOtNh2jKSHA65NSySRqhZQSoHBNAGXdHaPkO1gMHHNQYIjUFiW6k4qeW3Eu6VmwDySP5VHEUeLcuWIPegCjeK3kMz8v0GF6VUEbBFyxKnrxWvcPK+d0aAYwDWWS6AKeuaAK5KpcAjJI7VXuMFtzHZnsKvSxfKSzfMfaqLx8/PzQBE6YwykketSkDywysM1CxwGDHAxTUKbQA560AShmIJYY+lTh12BuTVXeQ2zHBp8kixqoA+bvQA4hpGBDAAdqUAo5JPUYpu9WHoaVWRh8zHFAEUzGMYLZ4qhpli+oaoEAJTPzVZuMEE9jwDXUeEbJYjtKZZuSSKAOl0jRnULGFxGo4IrprfTlSNQYs+5rT0q0SOJR1GO9aMkXyfLgUAc81gjenHtUMtuPMUAcV0awKsZJAzVQwox6DjmgDINqAhOOe9ZwYteiMdq3Z2A3JWPZRh9RlOOR0oA1lO2MCpUbrVaQ7TinxyZFAFuFuRUF++yNqkj46VR1OQ7SKAOYvjvlJrHmPz1q3BySaypeZDQBbtTxV7aCtZkLhRzV6OUEUABh+bipNhUYNSIy4z3pWIYZoAg71YiHFQbfmq1EDigCzCuTWjAu0A1UgTmr8YGADQBYTnmplFRx9fapRQByfxK/5E24/31/mK8MPU17r8SgP+ELuP99f5ivCT1oAKXFIKU9KAHxEiePH99f519v8AhwlvDmmE9fskX/oAr4htgDcwbjgeYufzr7h0EKNB04L937LFj/vkUAcn8WgE8GxyY/1d5Ew/Ovnb4nkHx/fyD/loFb9K+iPi3/yJYxk/6XFwfrXzx8UcH4g6hjGAExjp0oA48AZPrS0i9M0p6UAIo6GvX4ZbiK2iZJflZBkE15BnAFezWUcMmnRFyQQgoAt2GrSwb0lyVAOPrU8Gp/IxnUhxyMd6z1khaTCHDKe9Cjfnc4IzQBtwajFOuOR7GpQ4c4WsS2bbIQFGKvfaNuNpAOOeaANGSb5VUYz3qJWEjlAeR1qn9oVVBJ+cninGZfMDfdY9SKAC4fDDByM4warOA0rEfKasIvmPt4I61TJzPtBoAd5ZMWcc54NV7m1eJS/Qkdq0lkTyGRgcjoQKgulIgJZuMUAc/puoTWV3IM/IDzXVwaitwcHJB6Vxkq/u2PQk1Z0m/aMiJpO/pQB3aM2NhOVxxWWMi5kST5VPFXLWUPt5GQOuaZeQZmDuRjPagCrCixsyAZ5IBq0FwC2R1/lTbsLE8csf3cgGnAgoADnigCDG+PD9OlY9xGYnzEOSe9bs6benQGqU0XA45ABoAowAT9T8461LLE0bZX5s9T6VTmZoJdycc81ow3EUiqOcHrQBWKMHHoevvT8bXJbqOlTyGEuEBIA6Ux4l3Ft2W/h+tAD0nVjw3bB9quLLkjGSQCQayirL86rgfxCnw3bZKspGcc4oA6JB8uH9cA0u0BjjmsmG4y2A5bByavRvl+tAGjFhFLNUcrqyEk8mkEgK4U5+tMMYbA78cCgDn9dWOa0kQnsa8+8J6/JoHiF7eR2FvK+CM9M969J1GGPYwJ5wa8Z12Ixau5wQc8UAfRMDtcIJSwMfGDng0w3ywTGF0G45OK8w8JeJ7pbXyJ5WYR8oD3ro2uZry6W438rx1oA6lbu0kUeXtL9AM9aJbiSSNkA564xXLpbTWyvMqs6occHv14/Op11a4d1UKYwRjJ60AdKslskSO/yFeCPemS28M0W5pSUALfLWbhMkK/mrwVz/AHqswwTIrDcApGSvpQBWFyFSN1UlckAZ5NOjuTOG35QIrZ/Lim3Iy0QhXJQZwePrT7GGVRMZQCknVf6UAUI9VSIGFiW9GHSrUEs8xcurJ8h2E9D6UTxQxqU8qNUJ4wMmkR96MHZjsGFHQUAUVjmNqVuJcybjhh/CDUEljHFLCwndwOST1JzWlNGDEp3gdyBTpbdGA3N/ugUAZc2nLdBpChUrwCPSqEulW7E+Yuc85/pW+ZJFURgfeOKRwseQ6bgDnp3oA5G50iNHfykYbveqkunShAgzgHiu02AxNNIg9QKrTtBFhig4HIzQByX9ly8cEmo3sJIZTnuMYrp3Y8yIyhWIbbVKcRSH5mUORwPSgDAktZWgxjHsah0Sw36kHlXIQ5FaFwh2EtIeT2NaGg2wG5+vuaAN20LdDjFXxHzgVVhTahNXIzyCaALES7CCatxHc/AqsOcVctsBuaALsQ/OpTkgnPSmoKVjgj3oAYSdpqHJI2mppHzxjpUQ70AR7cGpEUDmoWbB5oZyAKAHSA5zniqkr84zVzIKc1UmVeTigDKvWwK5jVZWKMgHGK6W85Brnb7biQn6UAc/o8kiXEkPRXyKiR9up+WW6NxmtBHW1mRwmSTjpWV86a829QQW3CgDp7m/89Y4yoGzrV+e3hXSUkLgE8YFYAkju71guQzYHtWtq1vJDDApfI25wDQBSXTmntJTtLbfut6VnwxhZOfvL1rdtLuWCwkyPkYEVlYBcsvegC1CuauIuBVWCric0APHSmMp7dal+7+NNK7s4OKAIDknA/H2oSATOVZwgAzk1chswUMk3yx+vrVOW2lkuC5ysXQUAQM8UNzHbQ/MGOWNbtosbONrcjjPpWbcRWaX1vHDhpdmTzWxaBV+8gQKN2c96AJX/dITnzCP73FZ7lVkEzElwerdqsi6t5/3EgIaQk89DVHyNt2ULHy2P8dAF9Zmgkafe7ArgZwQTUBJF4ikDLYLEfnVlLdAzKjllBGB2oaIjdcyKpxwvOPagB5lk+1FiAUAyPr6Vj6jKTfZwvPUjPFbdmsYlUuW2ncdgGcHFYN3ITdMqY+duaANe3cRxBTs2YyD6mni6m+zsXhVV/hAbOarRxo/l5lwB1B9aZcXEoAiAx82OBnigCe5VGETsQAq56jg1m3kLOgcKMsRg+1aHkxSIN33T1qKUkQgoBsBwAe1AFHy0Mhjc4Jxkg9qSWaOOExbmdQcDipTFsXg5ZjwSKikhZUKSEc8Ar60AUJJWiRo1XK4zz6mi1C+XgsoZjnaD0qy8PyEsRzgcmoRbRLmQJmTsQaAC5CrHtPzH1zWRJGPPbfJtBGRyK2DD5kW/GRzx71kXEYcMZE5AwKAGyqJYl2kAZxk1TlTA45qdzH5W0FsqM8VAxV03gkZ7UAU5GUKQy1Vz6YGKszEDHU1XYr0A5NAC72xk8mkBMhBYHNORQByaduAGAKAJAPSkbKdgc0kJyxz6VII2lyqg5oA1vC+g/23eGSYFbaL1/iNdZqdqljNbx2/yHIHy/1rW8OaetpoFvGiYLDcTjqag1qynSMXKAMwYZBoA6jT7h0tY/MPIAGauxXauSuaztM3XOnxGTAbFPaIRS5BoA1Gf5agwpyc4NMWQstPCBkB7mgChdQ45XqazdNQLfy7iKsarq8NnOkDAszHAA5NU7B2F67kZDHvQBrXNuW+7VTY8bYIrXjwTzVe+KomQKAIkYqODWVqs5wRkVHLqGyQjNULiZpzk9KAKcn3CaypPvniteZdgx1rOkQFyaAGRqWFWY1IojQAVOiigByZxipVyBigEU5WFACquTVuJOBVYEVaiPFAF+Fe9T9+KrxNgVPGc0AWojip1GaijXNWVXigDkfiVz4LufZl/mK8JPWvd/iUMeCbr/eX+Yrwc0AApaQUtADouZY/94dPrX2/4aGPDOl/9ekXX/cFfEEOfOjxx84/nX3B4c3f8Izpe7r9ki/9AFAHMfFuPzPBYQttBuosn8a+cfiXGsXju8jVtyqqAH14r6O+LjFfBBI/5+ov5186fE9QvxA1BV6KEx+VAHIDpSnpSClPSgBD3r2Zd0el225OHjGDXjB+6x9q99020juPD9m075JiGBQBzcsqwuFGSxPPFEty6Ix2HHrW5e2tra2/mIF3k9+cVz14XnGAw560AL/bKW8AG07j3rOGvOJPnyR2qndoVIVm6VnuCHFAHUWuruxyRle3tWzFdeam7ggda4m1uAMA1v2rh1G00AdJb3Kq5cD7oqqsy+aSepNQxXOyORNnJHBqBDufLcmgDfRozGXPQDpRdRE2buMFW7VFaOojYH0rTuLJG0hZ/M+Yt8yZ7UAcfd27AbAo6ZrIOYnyPvd63bssJWIyMdKx5FC7mzljQBtabqm3Csea3/tEc6A55HNcHF8pD7uRW3Y3w2/MaAOkhQToyOD6imREI2wDpxVeK4LsrJIQKtTptAkQ8EZoAfICUPOeKgkGGJqwEJhVh3IzTTGvyuWyp4x70AYlzB5hY1DagxNhulaUiE7iBgelULv5DtXt3oAsOM4YDjNKjqw6cjNMt5T5RB5pGJRcgYoAmZd+fpUcqbV29+OaWGbKH1p8ib42PcmgCtbvwfmK8dPWr8Uihzl8Ht+VUYYWYAE4OOatxwou4tycetAF4OUjDZ64/nQbh+BHwe5quG3KVI4BwKeko2BccigCtKxYFn5xmvOfFljm8SVF616NM+5DtX5W4rntWginXbgEigDnPD6LDfqkgzvFdWXMVwGjYhd3IrlRcR2eq2p/2wtdp5CTRk4wFGc0AdBpshuI/mA2L8wH0ph0/wA5XYx4Jb86oaVepb3AjlB2OuBXRwO0qhgAVXkGgDEwbJTEY23Me/atbzlz5YBYAAsfb0ps4SVWdxiQdax4r+KPcC6lgw+RjjigDZws7B0HloG6t6U0kRuybtwfoQaQzfaInYlSnTg8AVAghKja5AVTk5oAmFkM/vMkZyOaqi3ZwcthCeB3qGHXbe+Zo4JNojOCx6k0jXUyqzoqhAeMnmgC3JDFD5eQeP1qCUjaEVSOcbj1FVxezTIVli54O6mTXLeeG+9helAF8OIljQMpbsT2qCZWeNt0gHHUVSaWWeTcygE8AVLhos+ZIpYjp6UARhAoUMzEHjJNUdQtlaUeXNnPUGtCSwe9eMByiBc8HvTpbW3giyxyQAMnvQBzkpCPsklH3eBmoyiMDhGJ9Qaytetrl7/zYQdg6AGs+fUrmyut4YrlcAHscUAdMNOXcHcEKBnbmtfTo1WM7MBfSuS0C9u9SukDs21eXPY13FjCNh45zQBcRPkFW44g4HsaiiXsTVyMKMYPXrQAxUwxIqeIkkUg2hjjoangA9KALEb7eKkJqvsYP1qUuBx3oAU8gioc7Tj1qTcR+NRspbmgBroH/CmSEYWnZ25HrUDgbgO1AEzn5OKrSOdtOaXYcdjUE8nHFAGZd8sa5nW5BDbBsjlq6KckliTXH+LZvLskOeS9AEayeZHvIOBg1QZ8ar5jn059KbYXJeP5n+UDge9Wrm0jmtyyfeAyfegC3YRia7HlMDls8Vp30rq6oVJKdQawdBlaO5G0kOmeK1jfGa7JlXgnDUAar3ER0lIXiCk96y7ewZjLIr/KB0NaGpvZyGEW7lflGQfWm3ML2mlpIR95u1AFOL5ODVhJgDjH41QilaW5aN+rfcFaFvbSTMEAxg45oAnX1659K0bSw2ESzY56LS2tvFbk5XLjpUuoSeTZSTnrjb+NAFKdpLi6JPy28Y4Xsap+INRDWCtGojES8471btoZdSg2rkLEuR9a4/xNcvGhsYTulkPzewoATwlI9/rM00rZZE/ACvQIbfexJO5SMAVxvhLTTp9pLITiSUYOfSuttGdItoJwOT9KAF+zpG2XKhwDtFNKqXG5lz/eNW8xr82csR94jOKreWrQkYJVm7CgCOOTYzBjkHgbaJJZZMZZCq9AR0qwYlE3yBSVUcc5FMMSLKZyW5GNrLx+lAGhZiNYJXA/ebfvDpWBcbZLjdCiqerMa3YY2FnLsQhSM5B71gtG0t6Y2YhBjcwoAmkAujhEGxcEtnHNBLx4AjO0n72c0roiF1WQhh92qcdzN52zzCSPUDFAFv7PtlyJmySCRkYqa7UxxmQv8vYAVXKzu/mER8d80yWUuoRvuAckHigCMsrxF2JGAcYqLapIyW7EfWjCvKB5hMZ9KsiNGuFOCUX+dAFO4ja4UgEceoxTYIFjJ3tyB61duMROFUHLe1RzFY0wyBuOTjmgCsx8uJkUdRng1kXBKsC4b8xWpITIFMChgD82eOKranBG8KnowoAx1U5fC8N71XYBU+TbnuCatmYjCqQBjGQtQzW6nGx8epx1oApTpnaQRuxyBVQ4Rhlc81oSBFJB6gcEVUZ8g8jPvQBEWG88U9ACpOKjQMclv0qwhULgUARs208Dmus0uwjWzSR15frXKAOZ0B5BYCvR4bUfZI16bEGTQB1NpcxwWUC7flVQBUeoIb6ymjXKlgSCPWmWzwzWccJPzADkVpwgRxYXDZGPpQBg6LqDWkKW0z7mTqTWx9rW4b5Bk5rPk0FJJzKjkFjk1tafp0drGdoyxFAEkMRUfMaLm6S0geQ8gD5cU+f92nzDtziuU1m8MjJHCGHzYPPagB1rDHqE0l66nzOi5p0StFdFie/NWNOBiiIH4UC3PnliOpyaANaNy6g+1ZGrzSqjbWP0rXt8BMZqvd26yA8A5oA4eO58y52PnPvWusa+QRUGoacIpBIq4IPWqyXZAKFqAFuGwOKoO3OTViRiV61UJ3Mc0ATLKFHNOFwegFQouRU4QY4oAsI2RS5xUScLSSSFaAJw/PFXYCSBWVFIWeti1xnBoAuR5q1GcVFGOOKkQ/NzQBdhbBq3v4FUo8ZqwvNAHMfEr/kSbr/eX+YrwWvePiOw/wCELuVP94fzFeD0AApaQUp6UAKn+ujP+0K+4fDJz4W0o/8ATpF/6AK+H4x+8j3fL8wr7f8AC4A8K6Vhtw+yRc/8AFAHMfF1S3gU/wDX1F/Ovnb4ojb8QNRyQeE/lX0X8W8DwMxJwBcxdfrXzn8UlJ+IWpeh2EflQByA9aU9KQelKelADW4Q+4r3nS5c6PYMRwsI4rwdvufga9s02aX+x7NVjyPJHNAEGoXEjOcdCelZ5TOWLHPoK0pyUO90H51UclmbCj5ulAHPX0Z8zJ6VQcKG5PArekjDsyOOlZstqp3ACgDNSRN+Aea17K5KkA5HvWNPaFM7Tg1Y066AIim654JoA6mG8wACRx1qdZY94OetUrSyWZi4bj61sxWUalcgHFAFiJl2j5qsW8rkOZHJXpikOyNeEBprswtiVCjPagCrfKrjKCsC5jKygAHnrW+7b49rDH0rPlRY1YfeJoAy/Lw5wfrViNguNoNQOdnPrTopCe1AGvZXL7sccVuWNxuby5Tw3A9q5mP90Q4NaUU+8AjOTQBuMSjbS3yjpilysQCj5l4NVYplugCM7kGMetItx5oORtZelAEsis6FuhxiqE0KgElhkDNX4593D/NUdwhlDKsYAA4NAGUhMTnkYNWy3mKFAFUbhGicZJHPpU0XJ9eRzmgBxjMZww2/WpDKEjBboc/ypZFbGW+YdqZHtXLSLng8elAAJMtuGQMfnUoG4k7wOM80nyklhyvTGOnFIUCcLk9DQBIhkJPoT1pHZl+VSCR1NOQSAZ7VFJyWGdoPWgCtcTTgFYwAg6sTXNavfiFHw2GGcn1rZ1OZYrYxxyHr3rkLbT7vxFrKWkRPlqcufagCDTbWfVdSilUExxtuJNek2aHytvTjBzSWWhRabAIoY9wH3verEcbRSkgfL6HtQAstirAMrYIHGKdpupyW4ME/AU8E1ZtTvBz8pz3H+NF5pqGIs3zZPPWgB8ssN3MxjkYEjHFY8vhtXuXnZnZmGRzV2zthazFlbCZzt61ox3azTFudgXFAHNX9te6bZlIWZkfnC1RjvNSkgMYifaR1xiu7EkLtsZCdozkj1ou7ZVhWKIKWfpgdKAOOsrEoPM2CPPJ+taDui7EUluzfjWx9ltkhEbyAnHOD3qhJHZW0rI8oDEB8k/59KAHblRMBgWIy2fSqWAJcjLknoBTb7VNMjNuPtC7pG2nB96RNf0q3YuSzbG2jb0NAF5EJYboyFPQmqNwQHJjUtIx/i7Uk/iGxkvY7cOwKrnrwc1kX+vQQzkq+7PQelAG19pnilfDAAr2qpcagCPLdsrXOjxCJVbardapS3zzN8ucmgDR1G681vkYRhf1rnpJWJZpIvNzwufWtBIWlbLtz3FXbSwW4uIwB8qncaANDw7aNHArlFQvzgDpXV26YAA79aoWkQXaAMDHFaMeQQBQBYWLjIqxGoVec81HCrlDU+MAUAOjjAq1EoXFV46txrkUAOLDNNIBOe9KUOaYRhjzQBIBnrTTwaUNkYqJmO4UARvkycVDITnOKtZC5NVZSWXIoAiOCcmq1wcjIqV923mqskmFxQBUumCISe9cJ4tJeOJM/xZrtr5tycVxPiEebMiA5wM0AYVhMsR2OcjdjArqrdoxCpIyG6jvXHhWhl3D15rodOnaTacA8jr2oAkukbSrzzlwYZBgMOxqxZmOa5XzGKBjnJ5q3PHHe2zwEjDn5T15qnoaJLePazsI2iyMnvQBPcw5vf3cm9SQBjtV+6uZ4RDbyg7BzzWf5bJf4jbd83Bq3fXLXd2nmDGzC0AXBBapLZy7hvycgDtU8dyJbqRUICA9RVTUYntr2Da6kPH0HY1UsMrNJuYnYSzYoA6VJFU56jNUtSmlvLq3sE4JO5s0sWq6ekKzu+O+31PSoXeQXwv8AZ/rhlPYdKALs18umW5hjGJcENXGWMJ1DXzNIMxk/MfTmuh1HfcxM0S5ZeJCetMtLF9OtyZEw7EE8dqANK4gt0vNkGPL2DHtU0E2E2LkN06dRUFl5cpfjk8jNSO00bhnIRBkdKALRB8vy2ITd+NQJ56J877VB4C/Wqn2oF8sWXdwD1rQwPJ3sQ6qM5U5oARZ0jkO5t7YyT04pwcpbs+H2s2OeeKbiO6ty7RjHQL0JpiTSbSuGJAwuOQKANCUbNJJibA9s81gwtlmBwZD0HPNaF7Obe0hjZ8k8kYqlZRmSd5AxCt1Ixx9KAJvKVWdWQbsA96pStHEy4AO44xnpV0rJJK4MpCjgHjPSqRgk3EBg4AJ6A0ANhmKM7AvtHQdc1Yd1mi8vcM4+b5fWoIyGATGNh5IGM1FOHEypFuGT8zA0AWVjiihMMB3PjvSpD5eEDksw556HFRWzTSXm9EyApByuKt2+n3fmtIyDlsigBk5IaKLJOAMsRVeSFlYsMufrWnLp12IWYRZbrwc1mTyXKIEMZRieTtzQAxlJVWERVs8gHjFUNVBJCDI7kir8twcBWOOAMgdaoX0iSSAJzgdTQBlbGVjt2spHHPQ00LGAXOQ56r6VZOGUqdozz8oqs5CMTgke9AFGcL5w5OT6VVcR7mOM4q3dEO+4cYHrVDkhj70APRlMZwMZqIk7sD86XeqjGKQZYEr0oA09ItHu79IsZA+Ymu9G425Vfu4wRXP+DbZsTXB5/hFddBZ+eq7TjLHNAGlpUYW1yUCgL941bhfe5SDnPUmoJf3aLBEeMYNa9jarCFcABiKAJLe18teTknsasxjAPAqwqhiDjmoZRsBx60AZ9yxEchHQDvXGzyf6bhmU89q7ueES27L0JFeZ6vFLZ37HcdoNAHY6csbKM8mr08K4yuOlcfpusjaADXQR6iJI+SOlAFW6vvs7Y5p0GqRugDNz9ao6oFlUsK5S8u3tG3KSee1AHa6pIj2+R9a40FjdEDJqxFr63FuI264otoz9oEgHBoAJD8vFVgfnNaV3l0PyAfSsh8o2MUAW14Xinpu70yD5gM1cMYCigBiHjFMmFSY21BK+WoAWI4etS3kxzWZEuTWjBHwKANOKXjFWFbkVSiU5q5GOlAF+L7oqwDjFQwr8oqwV4FAHGfEp8+Fp09WH868PPWvaPiS//FOzD/aH868YPU0AIKWkFLQAKBvQk/xDrX294SYP4R0kr0+yRf8AoIr4iXGU3f3hX234Ox/wh+kbRgfZI/8A0EUAc38YgD8PrjP/AD2j/ma+evirlfH11jp5cf8AKvoT4xDPgC4HrNH/ADNfP3xVP/Fe3S9hFH/KgDisck0tIOlLQAw8K30r2ax1FINKskXO4QgGvGm+4fpXtOk6eH0uxnK5BiGc0AViWumOQcH1qZYVSLzD94cVoSqgwAqge1Z90+CFj+Y9qAKcsSh95HWqjW/myNs4rTlVmjAdcGkitskUAYV1p+Vxjn1rOl048Y9a7Se1VowB1qg9iTwaAMK2v57F1Q5KV0VpqSXWAGCmsu7svn2kVS8iSFg0WQRQB2UUvzEMeB0NMupAIcg9K50atKFRHGCO9WZLzcmB8y45oAuC8B4zSMQQTg5NULN0klO8cdq0kiRj14+tAGe0TO5HUH0p3l7QFUVfARc7cZpo2jPHJoAqhSKsRO0a7gevalRMk5HFNbCjAFAF63vNmChwx6mpyd6iZG4J+bFYoZlbk4U1atbtoiwX7uOlAGvbzoqN0J9TVpZkZeOtc5PLsZXByD1Aq5b3KNj58UAW7tPMQjbms4MY5eMg4wK0N7PwvPvVC5aaI/6vIPegC7GS0f7w4pjKoYKpyD1NVUmZgFFTMQqjB+agCYFUG3sM9KUSkScY69DVUEgEvn2xUqNgb8DA5JNAFjDsC7nYo6CqNy4PzBsmi5u2lG1DketZ8rhc7m5xQBl6rOyo2Twelbfw0tw1hcXG3MryY3e1clrN0WBA6CvQvhnDnwyTlVJkOBQBsywtHKxDc9hVK4jlKtlSFHcVszQtnBbkHtzSyqgtjFjJYZJNAGFAZ3zjGQR1rWiaSWEI65JPFVIURRI3KsRhSPWrUaSRtE7ggj3oAfJD5ZVAikk9cdqahtooyGTDelWJTPHGJI1Vs8gnsKoXMqsf3zKMH5sdqAJHu0TczABOMe9ZOq63JIHhhIVmUrvH8NVnuJL25MVuR5SHGTWxHpUOxVRFYty2e1AHHLa6jJhkklYbTkgdfxqjc6XeyCQTNNuxhcmvSypggztT5eAo4rPKtd3ILoPkGAFHf3oA8w/4R+UBnkZ8qf4qdJYmOHYGOMg139/p8ro+WVG3ZwvNZEunwxQfvjls9PWgDjXtZmm84FsjIH5UjWDytyCTXWJbAPjA24z0qaOwPl712ZPOO+KAOZh0qZFGFGD2q1FpVwrL8o/ya3oLKXzDujY5PGKvrZPuCOrqR1oA5NrKWKTe7DsOPrW5YW3kxFv7y4q+9jblxGASepzTWB3BQMY7CgC1aqdij0FXoUwxNVrc7UUHuauqCp+tAE6HaM+tOjJ59Kaq7lxShiMrjkUAWo8MvFTK+04qtbkjqOtTsBk80ATFs9KjIwSfWmqSOlErkqOaAEPrQATTA/HNWEGaAISvJqBkGKtEHearSg4OKAKk/C8VUIBBz6VbuCFUVnzyhTgelAFK6wCfpXFaoDNfMqHG3rx1rrbqTKMcc1ydyc3TPJlQDwT0oAyprYlnIGMfjU+nyCIkFjn6U+4lDA7CDnqcYqgpVXzz19aAOtsgIlVhhsHIXvVXXLeWIjUoVAbPzqvUCobG73QbMkMnPHf8a0lMtzHslIEW3ayqOTn1NAFLRrl5ZfNUbtvzc9q1LQG/nfJUEnP5VzdpLJoGpy2z/PDICQDzwfetnSgXiluYjjGePSgC15o+3xeYdwVsVHrk6aUrXUHImQ5X0qK0UTTS46rzmqfiKOS40uQgEbcYx3oA5zT7mfUNRijLHBbcR7Dk16lotq2rJtZtqRglc9gK858IWXnXZkb5P4Q3pXocdw1kpigO3cMZHcUAUNTnMLNDGfmL8n1xVyCdr2ELL1UcVS1C2KpHcSDClsfWtEWj2lglycAS8J7UALFAwt22nDE4FPCzkBdm4DqGPWjzSYxGjct1zSRxypLvdyvYDOc0AQSx+dIDs8sp129KnQO0RjVgEY4+Uc0s8DcknHfI71EZCkyRge57UAWIow8n2cO5AGORSRGJLj7OrfOrAklqmi3OvnLKqKp5781RilhutUOMSEdwMc0ATaxM3nIyYZFGdpHNM01GeM8bAXzzVHUpQ9yrbmGX59gO1PsJJmuWUoSpOQc0AackaBmC4Zu+aghg4cr19ATV0Wskjhfu4GWq7bwRWpJAB+tAGNHpEkk6kMVTqwNa8ekWsJ3sQxqRr2LzSAAcjmq8s4YsSD07UAWDJbxsqrGnXtUzzow2rgEVhHqHDEn0qvcXRhbzQTkds0AdNbzOMhgDTXEDMQ6D5vasey1RZkLhue61IdZyPug7fUUAaEmlWd2p2IuR1rDvPCkpZmgYH2IrdsZjcKrou0d/euhiRCikgUAeOXNjdWLsJYGA9SKyLyXBPFe4XNla3bMkigg+orktY8CQ3RZ7Z9jeg6UAeVvJhC22qp3Y3A4zXT3/AIW1O1Lp9n3IOjDvXPXNrcQjZJC6YPcUARqu5eaUAKhA60bwiDIyfakXBJPTA5FAHong+MJoiy+ma6DTS3ltI3Uk4H41y/hO6VtIVA3Q4x+NdfbACUc9BgLQAokla/jjVCUzy1bqeYZAATioLdFRCwAzmrsBGQTQBfQlQAT2pkozimliZVweKlI7npQBTkfBwe9c7r+krPBI4XJIrdmHmSHc+wA8U2aRTblWwSRxQB47572Ny0TfKAa1rfV8IBuqt4ws/Lk85VxzXLx3Dg43HGKAO4k1MPHjdWBqMwZTzzWel4R1JqC7uspwaAI7K4Iuce9dvYyAxjPpXB6WvmXGT6121uuxB9KANGTDrWZcRYYkVcEhpki5GaAKMTsjVowyBwM1QkGOlEE+04NAGrJgR1mty1Sy3GU61Xjbcc0AXYFrVt14rPgGduK14EAIFAFhU+UVZjT5hTFxViPrQBdhHFSEcGmQ/wBKlKkDk0AeefEg/wDEjm+o/mK8bPWvYfiQf+JPMO2R/OvHqAAUp6UgpaAHx7RLGr/d3Dmvt7wusa+FdKERyv2SL/0AV8QR/wCuT03Cvt/wuQfCulEf8+kX/oAoA5r4s7R4EmZ/uiaPP5187/FNg/j67cDAMSEA/Svoj4sAf8IJcEj5VlQ5/E187fFKRJ/HEs8f3JIIyP1oA40HIxjpS0Z+YgUUANPKkV71o5Y+HdOIPHkjIrwT1r3HRJH/ALCsR2EIoAkuI3kchAFHeq9vCuW3HB9+9Xpkbyyc4pyxqyoAo45JoApmMtJgY49amjiUksRjJxVloQCT7U2BMj5xxu4oAgkgw2c8UjWoKhhzVqVSWIHTtT1XZHt7mgDDubZRKGYcVTktELkqp5reuYQSN3NQ/Z9xwo4oA5y408EYA5NYN7czWE+wcqeDXeT2hRNxrjNetyJAzYH1oAn0+dJlB3YPpWvG+OuQB61wy3LWzZjJzW3putiRQs45oA3o3y+ADV4Q5A4qlbyI43KRj61pxTrjBoAcsG1CStVpovlztxVmS5QcBqiL71xnigCoY0kAXPNQNHJG2F5qWXCcr1p8c4Kcjk0AVlDZ+YcHrmkMe1sgNtHTFaKxB0yRn6VG8ZA2BSAfagBbe6YqBk8VZaRnXDHINZRDWzkZyKsw3Kkckce9ADZYzC24McGljkZwBnNXJfKlhGCKz3JjJ8vrQBZeXyozzkntUX2gshzwPT1qrNKSnXBHXNR/alZCp4NAFqWYCPAGAfSsu7faOCckVJJKWU45xWbLIzAk0AZl6xZjvG70Fel/DKWJ9CmDkYjfOBXml0dyBh94Ve8KeIW0G6ZGJ8mU/MKAPZIpAXZ1B2k8ZpZZBIcBSXPHFUrHVre8gzEVZcZ4NXllCLGUK89KAM26lS0UCVcDOTmkOtW0ksYVX3NwvpU18YpOZQJGI5GKrNajy0YJtAGQMYoAqat4hkgcWsUbcdT7VlrPJfTsxDrGB+ZrRazRneXI3d91MciFQikAMc9KAJbFYUJESlWA5J6E1pzTy7EUyBcj5tvesEzmLCgEknNSySyMEYqeBQBupJ9oUxgYduBk1OJRDAEijCsB87cHcaw7eaV4gxXaxOAc9BU01w8KIobAznd1zQBofZnYgsANw5Jqnd2UDTBChJ9e1T20ryuU3Md2OfSrB8mCdowS7ngE+tAGG9ohlGF2xj7xNG63VlwuGz+dXb/Y4WPIBB+YL69/0rMljRIv3m5SG+U9KAJzdgMCRgbuMUn2ySQvEn+skPBPYVHBFDBsLSksx5B596v6bbobl525JPH0oAZ5AijK5JbGSTVFBvlzWlM+8zMOg4qjBFuYMO9AFtI+F9uauREbhu5qNBhRxUirll+tAFjGW44pB94njmlXgmk2k8igCZGZdvSnvjOSaYowBTzHuGc0AODAR1CXyKdg4xUL/KcelAEsQ5JNTq2CMVViOanUe/SgBxzuJqpM+0sCanklrPuMl2bPWgCCdy5qpJtY59BUrnJxUEqED60AZOpOYrd2yMYzXLpLHc7mbBA7Ma6LXP8AjxdQedpFcPaOYpfLlGf0xQBrsizRHao2Lz0rNmgwpYqQhH51sWzKZVUn5T1J/wAKlu4A8bbV4zgHHWgDCtZ2iGwdyOK6i0dkiUzMCCMbRXKXKMrNnhh6VfsrrMQEjZI7k0AWPElmkqLPC+HjAJUZ5FR6XO39lvKhOM4bFXrpkkjUoQWxzmoPD+kyzTTyyP5dpnPsxoA0I3Wy0d5nBEs3CDvVuxiFzo84mAbKFR7E1R8RXESQRW9oM/MNzn69BW5qHlxaNaW1mhEzKGkx3oAx4LCKysoba3HIOXYdzmui+yeXp8d1IRgcAd6r6bDELVnkwZF5CmmSXE95OIAcA8bewoAqX9zJqMqQAYhh+Y1de4ku4YIgSEjGQDWUs0kWptbqowARIfatBJAsgXGBjigCb7VEu4Oh6YBA71IsErIjSv0GV5quPLD7ic89BU5dJYdrvhs/Lt7CgB8kzeUkbdQcjmo75mdUEZBY9TVfy5GY4cMR0zTolzIFkXBPTB70ATXEsNtabFYAAZbByc1StZxFBLcA7CykKMck+tF2iLdCMFeeu7uatR6ZLqLpbRZEQ5LAYFAGNbtdXd0qcuR6Cu20zSktUVp2w/XBqWCwt9Nj2RIDJjk4oluVXDSnp05oAtzIihmY4PUVj3d8irtBx9KZfaugzlh09a5PUtYj6Kck+hoAtyai8czMrd60rLV1mXbIQD71xcM7y5Jz1q3Gr8MCQaAOwlmUZZGU9+KwtSuWYZXJJ44plv8AaZGCLHKw74BrRTSrqcAJbP8A8CFAHNwXE9nLvRiAeoNbdjdR3mcttbOSKst4VvnTPkhapP4avrc7hlT7UAdfp8yxQPt5GPWte1vRJb4Dc1519o1CxTZIrMvtXSeGtSW7OzBDDsRQB1URLLzjPrT0crnvmo4Y90xOce1TTLgZA/KgCJ0V1+ZFY+4qpPpGn3iFZoI2OO4q+mCnUZpvlIc5zmgDg9c+HUV3EXsmETj+EGvN9T0W90eV47mJgOm7HBr6BEIjZcM2c1R1HTYNRheC5hWUHjJHIoA8r8CkSNMhzhDkV6BBhboyE9sYrFtvCf8AYN3cS2zkxv0X0q3HdeVw+d3SgDqBKvkKB1NWoznbjtWDa3O8AE1qW83I5H50AaqtzmnmYMm0HmqguAQRUlvtUknvQBDewNNEWJ246VSiimeTe33EGPrWvORLH5Y7Gq9wjGAxRjHTkUAcP4ztw1q+BXmpUCvXvEWlMlpKzSl8r09K8nni2ysPQ0AQ7c9KgnU461YPFQy85oAtaGg8zOK7JAAg+lcxoMWT0711DfKBigCWNQe1PkTC1GkmKlLBh1oAzphjNV1UZNaMsYYdRVNo9pNAETt2p0Pp6VGetSxYBPNAGlbvwK1oHLEGsWA8Vp27YAoA1VarUR6VQR91WoTyBQBpRHpip5MleKrxHpVrGVoA81+IwzpEp+n868gr1/4j8aVKPcfzryE9aAEFKelIKWgBU++g7lhX294UUp4T0kE8/ZIv/QRXxCv+sV/RhX234Pm+0eENJk/6dYx+SigDA+LLBfh/ds3Ch1z+tfO/xREY8Zkwrti+zR7R+dfQnxeTd8PL/acnK8Hp3r51+JF2954mWVwoc2seQvTvQByWB1FB6Um0gUvUUANP3T+Ne5aQWPhvTsL8ohGT+Jrw0/dNe+aIoHhXTsjIaEcUAPCb2IDEg1citlVdppsMO1txXatThgzkqxOKAK8y4V8dqajq0Cjb82etTSYKMfWooUbyc/lQBA0bmTAOM1YWPOFJ57n1p+wHGR9aFVSeR06UAQXSAAAU2KEqoOetTSrkAmnhM4HagCrdRBoxuNcD4lHmT7B2rudQlMaEZxiuIvl+03THqRQBzv2X2qVLXABHWtNbUk8j8qmFnjoKAILS5eA4PStmK8DJ8p5rLltmwOKhDSW3KnpzigDoBICM96cs2OoqhZTicAsAD9a147cP93BNADFHBJHWozgZOK0hCBF71C0IVCaAFt5lZABwRUkkjFAcA89azuEbIGKe0/yhc0AJIPMJBGOaoyoUJxV04AyDyaZIqumD1oAqx3Ri4JzSvcgkNnGaiESlmDLtx0J71VlQk8npQBLM4Zs5zVZmLHApp3A9c1Kq4GQKAFD7VxVW4PNSuWJ4FQyjKEnr2oAyrxiDxWecE81duCT1qgepoAvabrl5pko8uZvKPGDXqWl6vJPBEQMMydTXk2l2bXmqQW6ru3OPyr1xLRraAbVCjAUADkfSgDTtpY1KFTvn77ulPuGJYvK+2RvlA/hxWTJKw2Rx8sDjgdP/AK9aFpIDIVnUsVHGR0oAmktlwgA2mQ4ye9VpNJiiAeVmJB45rSWQyiMFRhTkexq3HAblkQxYXrubpQBzawLJLyCy/TGKhEcm5wVbYD39K6yeC13lExwOo9azrxI4/lLbiRgYFAGWsaJD5ZXqc53VLGsUhIcjYD8uTVO6s7qY5gztHFZ89lexP++nOV6BR0oA6E3caqQjqmDjPrUH2h9yEA7ySMn+dc+JXh5JZiDnJFRTaxNEDtTAH8RNAHTxGCOQs7bnzyak1P7LdWgVjtOflOeTXEvrlxBEWC4yfvsta+gW1zfsb29ZvJAymeKANGDTo0bzA7FR2atm3Ty1AHTFMeNVs0X+8anf5Yjjg4xQBlXGPKYbtgLfnT4EKKGHSqd0oubto5d3lwoGAHG45rWgjL264+779aAJI1Ii5780uPnT6U9jtULShQSD6CgAwWjNSxjCgUKMKMU7BDe1AC+lNdsU8FSRULZMp9O1AD93zD6VCwLFqkAxyT+FR+ZiQALwaAHQ5/KrKjgt6ioE5JAGKnLbUxQBXkBHNU5mBPvV2eQlOOtZx7luTmgCBky5z2qvOxqzIxzVC4cjOBQBz/iK4WFI0z941zV5GMfagM4681o+Kph9pgVuQKqQMlyDFtBTGMe9AEllKpAUkDd364rRV3b90rhUBHzdc1iz20mn3Krgsj8g/wBK07ZSwBbCr1OKAKt9Eu90C5z3rDZmjlOOimumuJojGQoIAOC2KwZolLMRwpNAF3TZXvrlIFBJNdrLprQRRLI/lw7QQo/WsTwpYrZQG/YZeQ4jU9hW/qdwbieN7hsYAG32oAwroQX2s21sgMdurbix/iwM/wAxWtFdfZJ5ZD87fdTPTFYtmHutfaNADHGpxWvIEinjSUZDHmgBLaSSSfPdjyewp+qzDTizR4ZyByKXVHgtQFtTyw6e9ZqQSzx/v2yQN3JoALNSyNcHl3OTmrPmuzE9GPTHNQvLgbI+BjqBUsOwY3S7aALMRgI8yT5HHAOcZoQyZd/MUhQcbhimtJBIRuIYjoTUb3SPNsMY2d6AHJLcKQTHhT/EOalnukg2SKQxA+YtxUAuQ0LMCyxqcKD3qzpuky6vOJ7kFbROQDwGoAk0XTZdUujcXCsIuxPQ11u2K0Ajg4AHNMBSCBUjAWNeAB6VRmukLEA8UAS3F2AG+asDUbotGpD9KbqF2BnbxWBczyEnAyv1oAq3dzK7ON2QeKyyv7xFAbce3XNdHp3hzUNY5gTy4c/M7f0rvNF8GadpyI8iedMOSz84oA4zRfDN9fbC0Rij9WrttP8ABdrFtMjl8dzXSRRouAiAAelSMQnQ0AMttMtbdQkUSfXFWGt4wONoPtUXnDAyanjdXHrQBWMB8sgmq8lpHIu0jJAq3OcCq6McnB60AYl5pkL/AHlzVHTrGOxvGkHArorjhMkZxWDqc4tyuV4agDXgvEMpIPWrZmBAFcxaSqXG05H1rcgO4c9qAJos+YcVaUDdk1VibD8VYz81ACNy1IEDsQOD60/Zxmo+d55oAjltyeAM+tZN9okNzyp2NW5v5wailQ4ytAHLtYXdmMoBKPUVGt/NFKqyxlCe57V06DeORg1DdafBdL5cgBPr0oAqwXKsoAkBOc5rTSdEUZ5rn5dFnsebRiy5zsJqW11EHMU42yj+E0AdIrowyKjLHzcg8VQtrlFBRzjPQ5pxu0Z1UH60AP1qLfYtznivGtQtXW6lIH8VeyXsoktGweleb38aPeSKBxQByzQmq80TYxXQyWJ7VA+nsQM0AWNCg2oprYlG0GqumxGEAe1W5eVbNAFdZDTjKRVcSbXIPSlMqsOKAHmYnrTJGzQi7m5okQjkCgBPLBpNu04pVYGhz3oAtQEAc1cjnCjiskOTxmpgWxwaANeO8G6tSzn3tXMIW3da3NKOXANAHTWyZGavYwn4VXtlwBVlzhTQB5f8R/8AkGT/AFH868gPWvXPiK+dMmHuP515GaAAUtIKWgAALlQDjnFfbHgiA23gvSIycn7LGfzUGvicKS6Y/vCvt3whu/4RDSNxyfskX/oIoA5z4vuIvhzfMRlQy5A/GvnT4kiNfFxMKFYjbRlQew5r6K+MBH/Cur4k4UMu7ivnz4rFW8as0TAxG1j2kenNAHHIQxK+tR9GYelKpwQaaehPrQAdsV7/AOHZ9/hrTYjH0hHNeA9/wr3rwyn/ABItOIbrAOPzoA1i4GD29DTZXCDIUcjtSyn5QHAFUppHLkDO0CgB8kojQ5BP0oSVvLXHApXQNESfSmDG0AGgCRpCx4qRWVVAP3qrbXXkUqSYOWByKAJpGymMHNOSQKuTVfz974xUlwuyDcTjIoAwNamYnKnjnNc2MmXcBkHtWxqL5Dc5zVG2XYpyMk0AKkR4O3FT+XyBU6oWUYFTC3yVJoAoyQEjHFZdzAQTkiukeLb+NY97HyTQBkkMhG1iMVqabqexxG+70zVQR561UuVMTEqeTQB28cglXg5HrSSIHGFYcVylhrL25Eb5Irdgv45jlCM9+aAHSx7TmoSATViQmSoWBUYIPFABleM0zC7t2eKiIz3qByw70AXJWikTDDn1FZ0oG496Mse9MIfdnNAERG3sab5jDIyKklYgDNQ4yetAD1ftUVwMDPFLjDdainPHWgDJulySe1Z7KQfrWrc8oOKoSJlfegDd8EKP7aEpAOxeM16cZPOljzgDua8w8IyCLWYoyQAxxk16KkzwvtEe8EmgCxdWv2cRywMGY9QKgV3UN5jEOxACr1q+g/cfc2sw4LHinLakQqzqCT1YdRQBXRpLORG3FlPOP6VqQ6tIy+Wqswb8lqtcWPnwhmJEUQzkdWp7wS2yfuV+Vx1xQBo2ioc7wAh7n1p1xFbtIVWMsQM57VnWdyQnlNliDmtOTa0XmN8pcj5fWgCsiYcDKKvde9RymK6yqRINvVyOTU6psWSRjgtwBgdKz5riNVMQGPfpQBl3OlrcyMXYoo+nNUoNMtHlZW+ZQeA1adxq8awtCLY5IwG706MKEiWSEEkZz0NAFW5FneTfZI7eMwxgZyB1rUNssSwQLgKMHA6VTtTDPdMLeHvhjWly95jH3R2oAZcMXuIxgbQcYFOlwVIPGTURLNc8diaWdHYEA0AZ0sRknUI4DDuKvRQZhBV23d81XtICsjued3ArQQ7MLigBdjNEM9RR/EBnGRUxznaOOKgGOp6igA8whgtT81Wx8+asq3ye9AC7TtyKRAT7GnrJhBkUje1AERxv560qjL7uOBTSmWJpV+VTn0oAnTB7U2VgDipYlDRA5HSoZE55NAFaY+lUpTwRVmdsVRdsmgBSeM1nXbEc1ofwGs28Py9aAOE8SqZrgSA8L2qlpVz5DlsDOOc1oalh5JQTnBrAO6KZgeA3qKAOjun+1QRqJMuMnrUFpIxIilyrdhVHBEaCFgWxkkGrZczKkicOBzigC3MSzMDjjgKOlQzR+eQqIAcAY96h+0/IQwLN/KtHQ1SbU4PtD4Vm/pQB1UUIt9PtfkG8KAAPWoWgL3MrXCksFOB+Fbdnai4jNxn91E+MGllhEt2ZiPlxg+woAwvC8cdrqc9y8YKnjmma/C08z3MZ2jqFHQVs3UcEGnSrAvzGQYI9OK4nxDrv2aVbdDzjLCgBbO+iJc3bM7LxGB3NW43bYzyEh34VfasLR7ea7lN6/wAsUfQEfeNaskryyENHtbsc9qAJRcNCx3KCDxUx2ypyQvtVFnZIsDH1zmkacqoAZWJ/OgC1GFLfLjA75qDaTIxMnJ6YNQNKB94jH+zWtoWjjUrlbiVWSJex4zQBd0XRp9RZZJ2Kwp13fx12A8qOBYY8rGvG3FNUx28CqoEapgY9ap3eoRoM/wAXtQAmo3Plp0wo9K567vkUDaxzU9/qTSIQo3exqvZ6JLeuJbmQJGeijrQBngz6hOI4FLE9eOBXTaT4YjjYNcsJJGPT+EVetrOG1jEVuqp6nHJrRhLIMbuhoAvCz8ookKBIlHIXgVaLqAEOQKqx3SmNUYjcPQ0NchQeR+NAF0TqibRUTPuXIOazPtq78Zq3E/mYCUADsxwq8nNXrZXjTL0kFuFbc2M1JPKWGF4xQBFO425PGe1VBJhqk2mRiXPApp8vHHWgBGYuDkcVjavatPZsMjcOQa1y5AOenaq7ujrhiMUAcbZStbtyeRXS2N2WQ7uDisrVLFd5lhIHPSq1vK0o8t3KY70AdXayiSU4OcVoqMtWTpKRxRDa25j3rWV8PigB5JxSAfLmnEUwk9BQBGR82alRweMVEc5HFPClR0NACPjP3TTSBkELS4c5+tSeWxoACMjIwPrVG7063uQW8sLJ/eFWyrg4PSlRgp5oA5Ge1vNPlLzZki/hK9qW3vELbi2SewrqpY0cEkYVvXnNYeoeH1ZvPtT5b9SO1AEbTF7VsNj2NcTqLEXbMp6muhkmmhVo5UIbpuIxmuZvmzP75oAZ9oYdaX7TzyDURFMP3hQBs2xyobFSSfdNR2X+rFSTOADQBkz7txIpkCO0mCatHDtUqR7TkCgCaCPB5q0YVK9qqhz6GpUkJ60AV5YUQ4AqtLwMVfmXK5rNmPNACw8tVwEAVSh61YLdKALUXzNW3pi4cVhQH5q39M5egDqbU5XPtUk7bUzTbZf3VNvDiE/SgDyz4gv5ljMB6j+deWE5NeleN3LWk3+e9ead6AAUtIKWgBQcOuOzCvtzwjn/AIRDSM/8+kX/AKCK+Il+Ug+4r7c8Htv8HaQf+nSL/wBBFAHPfF1gnw71EkEjAJ4yO9fPfxWTZ43dMYUWseMDHrX0J8XufhrqgJ2jbyfzr5/+LDiTxirqcq1pGc+vWgDhu+O1B6UZ5x6UUAN9fpXvnhiOIeF9PZ5CJGiG2vA+1e+eGoS3hfTZCAcQjH50AaU5ZAEI3D+9UCKG6N+dSTlxH1xTI4ycFhn6UAE42x4BqvF2qeaNVGT0+tQwo0kwWPAWgC5EAetIsJaU4qdbR0bLMMdqsLEF6GgCrFbkuTtBx6Vl61c7f3ece1a1xKLaJ3Jwa4jULqS7vCedoPWgCvN82BnPNWI4CCKjt4N8nNaSRYYe1ADY0PSpdrAgdqlEZUZ70kkwXAPWgCKbgViXIJk5rZlmU9KyZm3SkdqAKUg29KrSp5gq+YiT7VE0WGOBQBktBg5zjFLHNJbHepq3cQ8DioBHkYIoAv22tN8vmCtNb1bkcEc1zMkRqHfNA+5GIoA6pk2gk49qrhgSd1ZUOqMRiQmrP2hJRwaAJ2ZN3FRs4BNVnl2dBURkZmLZ60ASs5ZjUZOOaTPPJofGKAGM+aj8wD71MkfHQVWkbcOaAFnfPTpVCU85qd2yuB2qs+c9aAJbK7NtcxSf3Wr1zSryMrb3Lp5oK/cY4GfwrxlsE4PrmvWfB17HeaFFGwVpI+G4oA6SJ1mVjKu0s3yoOQK1GhjkSOEZ2Y5qlbRwwbWbqenetgMpXcqjGPzoApbFSYxbv3IHSnXB4VgcIBwpqVXjJZyAM9FNMniNwyTKMIOtAFNkjhZWUZLdcetNneV5U+RvlYVoGGyjXqxYfNtOaiaWN0OOQx4NAFD98WcyvhQOBWfPmZFij4kZ/vH0rQkUFzklEHRhzmoGlhtg81xtWNeeTQAyLTS0rFgCwAG40zVIvlEcUo8wjBYdquW80txaLdCPCsSAPaq8tvIx3beCfSgBNJs1tLcKHJkzkk96tW5Kzyt7VUa6eK8EBUcr/CKuQKSshz3oAjTP2jnuKfINvm/SnRgG7APQCgqWLZ6mgBIYwsO49RzTkOUyakCfLj1FRgbU59aAJGYcVCwIJbsacw3YFMkcrhQcigAVcyCp1jxzUSkhz7CrKklRzQAp6DIpMjGMdaeoOeaY2Qx9BQAgTbTSNxIPSpA24e9NA6rQAjMUcKv3aJSCKacggE8VE7HGT2oAq3BNVX+4PWrMj5UlufSqbhmOB1FACnPl1jX8uyORvRSa1yWZdmeRXP8AiF/I0qeQcHG386AOPNyk0zZPVs1DcxLMNynoKzFZ1O7BzU6SybcDoaAJtOjO54s89s1bt5PsNwYnGUc81nxyPbyeaoG6rQuFljbeh8xu4oAsXTKuWiZSM1LpUxk1G3z/AHwPzqraRE7o5BwBmrVtElnqMD7vl8xW4+tAHqisLVfso5WUA/jSSK9vbyWxOZJv0pJJEaWKZQCNoZSexxRes1vEuoXQKoec+tAFHUZ00yzZbjjy03Fv7x9BXm1vot1q+ove3h8iBm3Dd1IrpdW1tLu9V1XzIm4+bpVK51qPJVSGkI2hccCgAv547WBYbcbNowB6j1rMOpN5e0jLUms/abZYPOI+YZXHWslZ335B5oA14rg7Dnj60klyE/h3FiOVHSqMRnmmEUKlix6Cu50Hw2IQlxeoGfqFPQUAVtD8PvdkXF0pVByF9a7K2RYcoFCRjGBT4kCrnIVR0xVe5vAhONp+tAC6jMCRjsKwJZ3uJQkaFn7VfRpNRlKwfdH3mbt9KtpbwWSfKvzt/EetAFKw03y5fNuAGc9V7CtaPGwsRtwcCoYI2K4Y9TUsxVAAO1AEyMA7H3FOe5CoCpGTWY92BkDiso3FxcXpggUyOemOgoA1bnUhaNvLDPemf22brEcXzMRwAKWDwZc3hEl/cbR3RTXSafo9hpu3yYV3Afebn+dAGfYaTdTqJbhgi/3T1roLeCOEDZnA9ajkl3Ec96QSj1oAvGQDqM0w4bviqrXOxcg1Ul1BecHkUAaGEdiu7pTJI1OADWfbmaVjIAVB6k1dQhACTvNADXjHRulZ1wiAnk1rOw27sdaoTYOSRQBh3UfyffPWsiQNGxbd3roJot4zt4rKu7YMCFGKALekahiRVZq6yAifLA9K83ZZbV1ZMjitfTvEb2xAkJ688UAdypzwaNpBwOlZ9tqtvcoGDDNX/ODIrKwxQA8oBg0jvnAqB7yMNjcPzpvnxsfvD86ALITHepAMjrVNp0HVx+dNF0meJR9KALrLgdetV3TFRi6Uk/P0qUTo5A3CgCLJxg9qkU7lyTxTpFGCcjJqvtaMDPIoAj1DTIbyMqV5xwfSvMtbs3sbsxnkZ4avWRMpjJ6nFec+KiZLslR3oA5wNnrTGb5xikcN6GmIsjSD5TQBvWn+p/CmTE5qa1G2HkY4qKQktQBFGnzVoRxjywTVWJctV3+HAoAQKmeaRwo6U0xue9NMbnvQAjyrtxWdOAelWJ0KdaqFizc9KAFiU4qwqmkhIHHtUysKAJYlwBXQ6SvzA1gx84xXR6QuAKAOlg4Sq16/7hqnjOEFU784hagDzDxomdPuT7D+deYV6j4ubdpl3nsP615dQAClpBS9qAFX7yDtuFfbfhAAeENJC9PskX/oIr4lT7yqf7wr7b8Ip5fhHSV/6dY//QRQBznxeVm+G2qcDhc8/Q18/wDxZUJ40RVACiziwB+NfQXxeyfhpq2Ovl/0NfP/AMXFI8YRE9TZxf1oA4XjNFFFADTzxXvHhdmbwpp3zEDyh/OvB+5r3rwvF5ng7TdvJ8odPrQBoum7A5NWkwqgVV3+W2CpNDynrux7UARXJJfrwT0q3bokC7toJrNlbzLtEBFairiPrQBNvaTk8CpEwAc9aYrABcinSyIi0Ac/rU7DI7Vz8h+QhQATWtq0u4n61mxR73z2oAfbRspDetacadyKjiULjinzXAjxigBZTgYzVG4XnO4GnM7M2eTmgQsTg0AVtpwWPSqe3fIcDmteSEeXgVFBaclsdKAKyWzAcioXh5NaUhKviq5iLsTQBnPBuGKqSRCPNbwtwBzVeayWToQfpQBzz/NnFQkEjBrXNgRIRio2scDgUAYksWF46moYmkhbk8VsyW20cg/lVOSIelAEJugfvGjz17EVSu0C9Ky3kYEjeaAN8z+9DXI9a53zpM8MaUSy/wB6gDcafNQO+azDLL600zyDvQBdckd6gZzmqxnf1phkc96AJ3YZzXa/Di+Qas9o7YDrwD61wJya1vDl8NP121uTnCtg/jQB7xjMjLJnaPukVcjn223lqpyTgMT0qrZSfa0UqRtIzWnBbpKuxR93kluKAESL7PKDIhkkYfLjoKmkUSDAcRx9SM4zQZoba0Z2JbnANVJ5VnUnABxwT2oAbcTRgbg2f4eRziqwdS/mEYQcInrVHzjAXLH5/wC+x4/AVJo8LX959okl3pGeccA0AS6q4tLUzOQg9PSvNZrjUfEWtx2EBk+z7wXJ9K9I16GTUGFpGvux9qr2Whppzb1AWZxgsB2oA24oHitIbeJAwQAE9sUjbXWVCcccAdqmQSRvF5ZJTGDRNEp3MeM+negDO0uJrm/baFwinLNinw2U0bOjyoznL8DtURgWISzF2JK/KqnH50yHV5LyLiII5Gxm/wBmgB0LBnMgPtipkw7Z9jxTQFSQbuvUgU1lZZFdT8poAsRkFunSmTLlTinxj5Saar5yDQBXYnHXGOtKqAkY/WmzgseOBmpUXaR7igBJBsb1zTwWBApCOST2pgZmYEdKALHK4zTS3zc05TkndSMq9hQA0ctxU2ABnPNMVeM01sk96AGPkNnNRnG05pXJUZphwwPIoAgABDA9O1VpV2EkcmrRUeoqvKduT14oArk4UkdSK5LxnNs05IgeXYfzrq3OBn1Fef8Aja4JvIYgegzigDCj2hPnIoCo33WqmJAxw1WIyqY4OKAJ1hIPLZFXYIUiljVgH8zp7VWSYEYBxT5J0B3YO5ehoAuywvb3RBXgjqaa7CV2AKh+NtD3PmbCX3ErTI7Z5pW8pGc9yozigD0rQ5fP0ITTAHYNoPfNcN4t8W3t7OLFGK20Xyle5rZ8O3t+LGW0ezkGD8uBTG+G1zqM8l5eXYgVjkKOtAHEfaZmT5TvAHReTVWKW4Sbe0bhs5wUr2fRPCumaNGwhthI2PmklG4mtJdIt7uUP9nhAHpGM/yoA8Zv7ya+EZmVzsXA+Q1DZ2E19OIoYZOerFSAK94XQdOhYCSFG74KCiSytiCYLaOHHHypjNAHGaB4WWyRX2hpD3NdZDpYk/1jnjqKt20CowGeBVheC1AFRrCIJtwcVQk0S3mb5i2PTNbg6UjRDqKAMlbGKFNkS7QvT3qlcwsH3vyB0xW1OREuapSSpuOcAGgDGa52nGcfWoJLksT83A71oX2nxzxbom2t6+tcVq8t1aEpIrbf7wHFAFrUtXSJNinLtwMetdB4fj/suwW4nAN1L8xz2H/6q880dG1XXY0Y5hjIc59q7TV75mby4m+cjaoFAHYpqQeLIPJ5zUcuopsG5+a5ywE1lp6rcSZkbnk1Qv8AVvLHAZsei5oA6o6nGBgtVeTU1XJD5rzi+8TzRSYWNh/vDFaugtea2Q8riKEdMd6AOpn1pEAXfktwAOauWBRwJZQfUAirGm6PYWyZKCVxzubmtTEG3IQY6AYoArNeLjHT6UwXJ42VcS2jk/gobTASWU49qAEVy6DPJqK4KgY70PFcQH7m4U9Zdw+dMfhQBksZSxXbgCq0i7iGIx2IrcaOM8g9ahaCGMZagDn3i3g/ITiqUllvyfLIrqf3BOFUU+RI1QYTJPtQBx227tgBDuwPY0r6zqcSFSWA9CK7m2hQ/ejX/vmnXumWl3GUaJVLfxAUAeXy6tqzElXqrJrusxH7xrtJfDL2s5C/NGelTL4fhdfnQZ+lAHndx4m1f++arL4s1WJuX5r0C78KwuPkTH4Vi3Pg0HPy8/SgDnj411XaRvApYvHGrIwJYYFJfeFbu1YukZcemKxpbd42KyRFSPWgD0jSPHRuIlWfG6ux0/WYLwAFh9K8GgcwyK2OB6V12k6lkBo2w3cZoA9YmCiNnjPGK4bUT512wIrQs9dJt2Rzk461iz3gkumNAEZt4u60qwxKeAKR33UxFYvmgCw3C+1VGPzVdYfu6pkfNQBYtwMVOeBmq0Z2nFWhhlxmgCIzbaRpwKikjw55quc5oALqcHiqg5NE+d1JHwKAJlyBT0JPakQEipY1bNAFy1Qkius0yMqik1gWCAFa6e2ACigDQU4AqlqHMBqZmzgVT1KURwdaAPOfFUbHSbxuwGf1FeYV6j4nvVfR75ABkr/7MK8uxjigAFLSCloAFPzA+hBzX2x4JnFx4M0mQf8APrGPyUV8Tgfdz0Jr7W8DKi+CdICDC/Zk/wDQRQBjfF35fhpq5H/PM/yNeA/F07vFlqfXT4v/AGavfvi4Cfhpq+eB5Z/ka8A+LZU+KLKRPmjfT4sH1+9QBwfc0UY79jRQA0fer6A8HZj8EaW6j/ln/WvAMV9DeC3I8A6UNufkNAFq4jaRVxwRyaqTIVj9TWlOfNYjZtxVW4XZHjIyelAGbZwMZzKR0NbqKgX3qGC22xAA9eTTxEQfvcd6AJxgr9KpXTZPtUs7+Uvy8g1SmmPlk4oAxNTG6QAUy2iZcZp7uJJSTzg1Muc0AOcgDFQGPe1SMMnipY48rz17UANWMItKCBTmRgvNRBaAHmIMMimgbQRmnZOMVHgA5oAjnizg0kanpVk4dcYp0UWOTQBUeNiDSQwBSc1cYfNikMfpQBT+zh3NRSQBBjFaaQkHPeknjXGKAMSW23pwKzLq0KL0rpGAAxWXqBGCKAONvo9u6sRl+Y10F/lmYVjNH8x4oArhM04R1YWL2p/l8dKAKhSo2WrrxgDpVZhQBVYYpKldeKiFADqVNwJIOCDkUzNOHJXPagD3bwFef2hoIlYgFF2ZPeuhS5kj2KoOScHPPFcp4FiWDwfBkYZiW+tdJZSPJllyWHAbHT6UATXcVzeyYZtqr91cYzSpJFZgLIylFHOepNSTTmDb5h+YDOCe9cxquoyzTeWoBz0wKAILyT7bqDwQ9Cc7h2rp9ESGytNoXcf4qxbWxFjZl8bnl5Y56Vp6NJuicBhluBmgDUgk3BpGiAlYkAe1U4pGvbl8cIvGau2yKtsSzgnJ+YHNRWkZIkVFwrHljxigC0isGeIDgLkGoC5SNVfsDWm6G3iRwQcjGPWs25RzKryJhey0AYZuC0ksTcbumKisl+y/u3GCScZqbU7cLcJJnYM8gVWu5If7ThjilLkJuY9s+lAGmqh13ZySMU5CB8tRK7CNW4APpRyZThutAEyyHztvak24D49aVE2/MetIzbV470ADgFAKcQCgP4VGHDfhUqKpXBHFAEQjYnGeKnCKo2+lR7sKfTtSIT3NAEhA7Um7HFJnk004J5oAk346UbieaaigE+lLuxQA2XlOarFRtqy5DDBqq/oKAIGP7zA9KrtxmpdwWQk9aic7iSKAGHGDkZGK4PUdCk1XWGnmYoinAx6V29ySsDYOM9TWRLeohEcHzP1L0AZdr4WsYlJ2lz6txV06NYldhSLI9DU8cM1wA0jkjPIHFXLfTzICQvHagDmLrQIQTtUjPTaawrrw7qCPmJJHU16tFpyRKC6g+xq/YaeZ5CdgCdgaAPP/AAv4Iub0efqKmGFTwvdq9Bg0ey05EitLdenJI5rfS2W3VW7gdKrEFnLEYyaAIobVIhlY1VvUVIsZkYBhxUqHPB6Cnoc5z0zQA0QpnAHFSYjhU7B81SFlVMd6ZAm9iX5FACGEuQ7ntUMxH3RVyYqBgdBWfK3JxQAR9CPSiI7mYUkZPJ9RTYCQzEUAWIwMkGnkDBqIHv3o3etAFDUUdLdj171xcuslZ2Gehxg16E/lyqYZMFiOnoK4fXPA93cTNNYSAKxzgmgCey1TzmCAbi3GB2q/Noz6hA0UsWUI6+lN0HQG02INKgM+ME5zXRpKVGDQBxWkfDv+zbqWUXX+s5UE8gVoxeDoE1BblpnYp1U11IySGJ5FJu7HtQBmDR7cOzOm8n1OcU/+zLXGPIjH4VfJ444pmWJwEz70AYF/4U06+UrJbAejCs2Pw9LosRNkTgdFau2+zyP1Jp7WO9RvOT9aAPPtO1u8XVfIurdlB43Doa7S3haQL1Kn1q4mlWyOJDEhcdDirqRqo4AFAEcMIRcVMEGMUoAxz1pm7BoAf5KN96oXt0zwKm39KGcjpQBW+yR56USWUJHzLkVMz4pDKQM55oApnT7fqBj2oW3iB5FSliWzmo5W3CgCXCheAKrO5DmhCcdTSMhP8VACrIC2GqRYFL5qo2FIyc81aSQEcHmgCUQRnkimTWkTjIHSnrJk7WOKkyEypORQBnPp8UwIIrG1Pwja3UZ3xAE9xXTmPAyDgdqQNxhmyKAPINV8Cz2257YlgO1c0Vu9KmAmiZDnsOte9skG87ixH0rJ1LQ7HUkYGIezEUAecafqcc6BCdr9xV77Ph94NQah4Sm06586DJTPQVahJMI3cEcUASIobrViOMA1VDEHpU8UnzYoAfcfKnFZnmEOa0Z2+Ws1x8xxQAr3O2nxXoIC5qlLCzDiolidTQBtq6utR+WCc1BBuAqwpIPSgCvPBnmq4THFaUqkrVTZzQA9EygqaJcGkjRsCrMcLF8Y4oA0LFMsprpIhwKxbCLaQCK24yMYoAcTWfqI3xYq+/y81VaIz8dqAOA8Rabs0O+n9F/9mFeWV7n4yt1tvCN+pHzFR/6EK8MHSgAFLSCloAUdVPvX2r4FO7wTpB/6dk/9BFfFIzlV96+1/BChPBekKP8An1j/APQRQBifF3d/wrTV9p6xHP0wa8B+LDp/wk1nbxjCRWESgH8a+gfi0f8Ai2usDr+5P8jXz58TzHL4gtLgc7rKLLDpnmgDhBS0DoKKAA8V9EeBQW8A6UQP4SK+djX0L4AnYeBdMXsAaANu6IPDJj0rJkQS3CpkZzW9cEbCSAazoGRp2bYOOaAJlUBQvXFMKjBGfxqUOiKzY61XLYGRQBG6YQgkGsu8k8tMdquSuWz7VjajOQNvrQBDDHvkJyOavhFHFUbUHGa0lX5ATQBWCgSVPkdBxSRj950zUr5ORtAyKAIs7uM1EyEMRTgpVqezZHSgCFUOT3oKEEZ6U9W2HjvSyZIz2oAjG3dxTwSwyDxRAgYnINSBcHA6UAMIwRmp0j3AHIpyxg9afhVBwKAIXfa2MVE4UnkHmpymeTUcxAA5FAGbPtVuKyL1gc81o3LhJDk8Gsu6UNkigDnLpcyk1nvF+8JrYlhzIaryQAUAVFiyKa0e081diAXORTZo8nOKAKMiZWqbpitGU4XpVR1yM0AU2XiqzDaavMtVZVxmgCGlHQk8UlTWtrNeXKQwIXkc7VUDPNAHuHhUb/C1hGiE5Tn866JZfsmBtA46dqp+HdNk0nw/bWzHLqvzk9j6UXlykLtv+b5TQBR1e+cHn5cnOTyRTNMshn7XMrs8n3c9BVWzifU9R3y/6pT09q6lkijjQRvknoMcCgDDvmaMiLfwxxkdKqLJ9kt5Fg3u5BwV7GrMkry3kiqFITqNtLaxmNmDYHO48UAaXhsTrpDeeGLFs81rwqJlwCVOckDvVSxYAEbsKwpYJCLllQ8KetAGmyNJMp3EkcbfSopiPM3vnK9AaQuQxkUgt6ZqG7uDxng9cUAUr1hI+4gZ9DWBcWwXV8KpWLZuz71t3fzRNJnFV5tPnuLRbhTwOfwoAjU5hEYPQ5qdF6djVC3k8wFu4NX4m832xQBZHKnJFQtlj7VJEVDEPTmZQxCjjtQBFs6YqRsgdKCSBkUwuxUg+lADmQ7Bg0vTmhD8gzQwyc0AN6GmhtzYxS4ww5p4UButAC9QKcxXj1qNmw+Ka5Bbg80ALJxmqjuMZzU8gODu6dqg2LjNAFM5LHNR8hyM1YmAJ4qs7KmSetAGZrFwYrbAPLccVRsIdygbSAeSah1mYzajFCnQcmtawQtEMdqANGKCOO33ZGDxitGGNYolwOMZzVONHYiPZx1q06yuQgOAKAJI1a7uFCnCg966K2h2qFxjHpVHSrIKoY9a2yRFETjmgCpdMUhKgjJqnEGLYJ5qZj5zk54FNjGZSQKAHSAIh9aapxFjv1onzjFDDCLQA2IPJJ7CriDbwOgqNCI4xxyalVf3RbNAEdyQEzWYMtIV9qt3bbSoJ61Vt+WZj2oAeMp2z2ojOMjHNWMAqTjuKqq+y6IxwaAJYwSpP6UxzgZPSlLlXbAPPakLZXBFADYYU+0m5LEyMu3HbFXmOcDIx6VSQgE4BwKlRiwzQBcVEK0htlY8darpKUcHsKnjmzJu9aAHi2AFJ5AFOebFRNPwKAHrGhbBFWkjUY6VUjlHBNTGYY4oAnbC9KjEgLHNQmXPeoy+GzQBOzcnFR+dULyHjFRO+KALXmE854oMoxnNUvNOcCpFO7rQBaDhuhpfNB7VWHB4p+3HNADmlFJu4B9aQlfSmMw4xQA9nA4qHf8ANg96Rm+amkd6AFkcRjcBSBwRvJ69qbJ8wUetV5HCsooAWWVck81Cl0Q3ANMugXwEqBY3Q80AaiytINxPNSxT/NhsmqcLHpUx5OKANEbpMAHC0jKCD14OKiik+UJmpPOHXHTigB6bYjyAfrSPh8jYPwpnnI7VYSIspK0AY+oqptWG0Y964KdSJ32j5Qeleh6hGVtn3GuFJUzS9DzQBmvI+8j0p1vKWfmrc1uDg+tCWQT5hQA2bLCqbfLnNXpiEUVQk+Y9aAFhO84xU8luB83FV4kcNV8IWjGaAII2CU7dnpUUilWqSNMigBXbjGahDDPPNOmXAqBT0oA1bdAwBrVt4AeeKy7M5Wtq3+7QBYiUKavRiqqD5qtoKAEc5yDSQypECzGiQVgaxeNbRNg0AZPj3U0l0i4gVuq4/UGvGxyK63Wr1rtJ9zHFcn24oAKKKKAAH5ge+a+1PAhz4I0klg3+jJyPoK+K24XPrX2P8MCx+Hmkljk+VQBV+LYP/CtdYA/55H+Rrwf4m29rYQaNZ/M10bcTSHthug/Q1738Wh/xbPWP+uDfyNeAfF0keJrIfwjT4tv/AI9QBwHYZPzd6KQfrS0AFe++ASU8DWRZsjnjFeAjoa958AJv8DWJyeSc/pQB0lxONpx396bZoscLP3NMmgxluop+1lRQPu9hQA5pMjGwVUlkIJGMVOwINV5cFunPegCtuJzWDenzbjb6GuglTYhNY0VtvuizDIzQBNbw4jU1YyScVNHGBFjFPEQDZxQBEkJzmpWj+WpN2McUSnI+WgCmyYoWMtT3DHvQjsARnpQBE0Q3AVJ5I20pHRj1p4IK4oAMBY8CkjUEU05WpI+h9aAHAAEgUpHegIQMnrSFwvFAEc8uI+lUHO8CrtxMrR7QOaz87c7jQBmXqktgdqpSodw+lacw3MSKquhJzQBkm3Bck1Vni2gkVqTjbxVSVQ0RBoAoJHlQaGGRip48Y21FINj+1AGfOuKqkbhitGcKwrPJ2uaAIZF4xVOVeDWg/IBqrMowcUAZx64FepfCvwwzO+uXAGE+WFT39680trZ7m9htYxmWVgq/icV9C6VC2jaRb6bGixbI8b+uT3oAv6rKfK3q64U4ZegFc1fMJrhEQtlxgtjitpot5EbglOvPekaMRzpEFHzDK8dKAH6PZx2FuGI3FvWpWmClx5ePmzUodRIkL/eA3Y9qiuX3BtoGaAMyNf8ASmfPU56UKfMncDvVmOICN2J+fHy1Ut1eKXLDJzQBoxMIYfm4zUvybGZGyx7VUnYyWzblO4Hip48KiKoC8c570ATJK0YDMMHscVMI/tEu+Tnjjmqkhk2427uexq7CSkJYp2oAyZ23RSRj15rOTV7mCUQyKwiYYTB61osm6V2Vtu/gg1hXlnNFcLM7jywcAk9PpQBZtgxkYkYyc4rVgX5qqW4RlUAfMTnPqKvIrxt93NADinzZpS2TikLNnDHFIFy5549aAHkqBjOKZubPyjcO+aUAbiMc+tICQeaAFZttM3k/Sn7RITxTDGQMCgAY5xipOMc9ajVdpGaZJLh8EYoAkKk96VFA571GGG3NORwfpQAyVySBUTsRx61NJg9O1VpW7ntQBWmfDYqhcykDHof0qeeTc3FZ1/LsgkcnkCgDJH73UJJQMn7orqtMgxj5RgjmuO06VZLodf8A69dxpwXaAN2aANWzhBkkf0GKfHCDLgjvUtkm2Oc9scVZtkBOSKANC2CqgwKW5beMUKpB46YpSmTzQBWjiC5qNSVkOKtMoHAqIx45oAjlzjJpqtlBUkw3RcdRVWJyowx6UAThiQPrSG48kEPyD0qLzsKWB4HSoJN02HNAEU0rXEoI+6OlSR5VqYFESk0om+b6igC7G2RzUE+N29eop0ZJ4PpTAuVbd3oAlVhIqyDrSSBW+Y1BA4Tch79KV252UACsQ+0dKsADJHaqxGHGOlK0xRc5zQBZKpmgER8isz7fGXwZAD6VO0xK8EMDQBZM27mk84GqbSgZCntUZlA+tAGkJMdKlRyRWWLtccdakW/x3oA0WNNGT3qmLzf3oa4OOKALwCjqaaUVqpLLkZY4pBdbc45oAttEF6UquAMGqgvC3UYo89M9eaALpYAcU8SDy8VQNyOmKf5vHFAFkvjgU0IS5JNQ7yCCBUpfKkjgjrQA5owOc0jnBH0pA65BzjNSOI2wQaAICpfpxTPsuXBPWrRK05SrcA8igCsLUY/Go5rRsjA71oADpnmnY3Dk9KAM1YMSfMcH0q15ILjIp5C555NTR4AGTx6UAN+zrngU9bUGnMxY/LxUiSFRyaAIRaogJxQZCjnZwB1qw0ilCKqEneeOKAM/XJcac8g49q83jnPnsPU5r0DxE3+gMOmVrzuNCZC3egDUZ90CmpI5NybaorKwhKk9KfBId3WgBt3x+dUR98fWrd6/FV4wCAaAL8IjY9KtbVC8dKoRMV6VZMhKcmgCvPjPFMR8ZpXIJqPAFAEcrkioQeRU7gbahGMg0AbOnj+VbsP3RWBYt0roLcZUUAW4hzVtBzVeMc1ZUGgBsg+U1xnibiOT6V3BTcpzXG+JIwUfIoA8rvS2yXPpWKa6XVrfZbyMB2rmfT6UAFHaiigAGeATnPWvs/4fIieBdIVPu/Z1/kK+MBwcivs74d/8iLpP/Xuv8qAKXxXGfhprf/XBv5GvAPi583iDTDjGdOiP6tXv/wAVz/xbXW/+uDfyNeB/F1gdY0cDgjTYs/m1AHnf8ZpaD6UUAJ0yK+g/h3Gp+Hti2CSS39K+fO5r6J+GzMvw8sPlBBLY/SgDZYAjYfWnPgYHUdqJgxkzgfhTdpJGc0ARuuah8sbs4qSZsdKi3PjigCpf5EZxxVayTK5IqxOS52mrltagRjjrQAyOEbKk8kFRVpLfnFSmALQBmGDaCTVY8k4rWlUEYFUZI8ZoApPxUfQdOTVh480zy2H0oAbGpx81D4QE0eYQcGmv81ACx/P1qfCqR7VVB2dKTzTmgC2756dKgPzZpAxNDccetAFeXAGe9Zl05zgMAauTvsfFZ8/ltJnuaAGBmPFB4IzTjE2QVPFEhVF+brQBnXa5fiqcilVOe9X5G3HpVa4X5fSgDOQ7JMmm3WG5BqRgueaawVlOOuKAM9/lGCc1VkAJ4q3KhqswxQAhUeXVJ+pzV7qpqm65JFAGn4Jt0m8Z2pcZEfz/AExyK9viga733Ep+QDjnqa8Y8DSxxeKY1I5kUqPyr2sL5VkI93GcYHXNAFNZ2kkbccLGO4pqyK97GWYZ7ewonVjAwxtXPUnqaoeW4vYsHtQBtSsv2mViQPlwpPpSC3jlBTLbwM8VHPF5gQvlh7VOshhkDBNwA6UAMEEagANwOpPaoSFSUMCGXPapfmmgdcgbiSR3rNJaJSoPCnNAGjcBi4C4wRmpVjDwhnAz7VnG5keMPy2PSrVnK8ybd4ULzQBbCt5gAXauOtPleNI1Jk5B5GKQTojhfML59O1MlXdPs25VvegCjdwF3Bjycc57VUs1iupwl0hfaflXPH41avZ5reTZjEWQM0yG3Tzd4bCY3ZNAEy2GyaSSZkiQDKjNLE4kzhjtqjrN8l1LDFDMpA+8FNTWbDGcHb2oAtbcHP8AOkHzOQentTj81GdooAXpnjgVGw3EEVJu3IaYPu0AKgwTSkYzkg0zd83FKTk9DmgCNt2SccVDlQxJBP1qdmKj5iOaiMe4E5oAjzluOlTR4zwKjWM1Inyk0ANkbHaqMpJbrVt5NxIxVCZsNmgCq5BbAFYmtOdixqDljzWy52jzK56aY3eoMARtB4oAr6bEVvR1Ar0DTYiBuyeRXJafaM12T6V2tmjIgA5oA1rBC1jI2M4bBFXYogqAYwar6USC8Rxg81ok/uw3egBhBUZoBJPWn43xH2qGIEzAUAMkLLMFPeiUMD1GKsXUYC7+4qB/mC0AQOSML61U+5OVbnNXpAOD6VRnGG3DrQBBO207R0qeIhl4GBiqzPu5NSxSbeMUARzNhipqIcODVt41kIJproi8d6AJI25zSlTwc8VHF97FTvgd6AK88RBDrScMM9CKsKwIOcVA4ySKAASAkDHaoHz8x/SrEMWH5PanNEuG45oA5q404SymTcV/GprZ5IhtLEitR7YsBnpmmPaAjjjFAEC7toPvSbwScnHtU2xkGKPK3cleaAKzR5OUYGnxpnqKtRwhCSQORUcrhDwPyoAcseM4oMh24A5qCO6+cgjFSiUE9KAGMzsMEGo0MobgGrDuBjHXNTIBjPFAESo7jnAoWA7u+anMbZ3CjzGBPFACGMjANNZwDjOKaxkZuDUZT58saALIuRgL3p2/JOD1qoQoGaI5PmxQBZkfn2qDzmzwTUoKnO40z5OSBQAhmk9aVLl19c0q4PWmTkKOKAL8VyXHXnFPE7L6ms+E4XOaso1AFmNiWyelTO654yKgSiSVVOD1oAsCRto5pRMOhqp5428VVkmbcMUAa+8ZxSmQLxis+KU9TUnmlsnFAGf4jf8A0Ug+lcQq7UzXXa2xkhArnhbcAUAUkXcOlH3JenFaSW6gdqgmiAkyMUAZN024laW3BIAxRcgrN0OM1ct0XZQAxeDipG4WpJIwozVdm4oAjY4NN3Uxm5pwGRQAx3AFQ7uRUkiHFQ4wQKANeybGK6azOUFczZc4rpLQ4QUAakY5qzGuTUELZ7VdiGTQApACH6VxPiVgMj1Nd1IMRt9K898UP8+PegDkNXg3aXO4H3VzXBV6RqLAaDdjv5f9RXm46UAFFFFAAOa+0Ph8hTwNpGT1tkP6Cvi487QO1fYvwuvDeeANLkPaLZ+XH9KAE+K/Pw11o+lu38jXz78Wju1nSD66ZF/Nq+gvir/yTXXf+vd/5Gvnz4rD/iZ6KfXTIv5tQBwPVjToIpJ5CkYJZuBTM4/GtXw4s8niCyit41lkeZQsbcBjnpmgDMdTHOyH+E819C/DdSfh7p5ycBm/pXIeJ/hBqKkanZKI4p18ya23ZeA/1FegfD+0ksvh3b286gSxTMh/IUAXI+WJDVJIylfvc1HOpRfkH1qnKSACBz9aAJGiZ2zmn7fKTkCoYHdnwTxU10BtwDQBUSEyz5wK2Y4FEYzWfp8RJznOa1SVWPJGcUAKkaqtMl2r2pyfvFJzgVGxB4PIFAEEgBGQKoTDOavSMAMVQZ9wYmgCnJkcCod7YwassRmq7vl6AEGMHNIBngVFI3zEVLb+poAcIgOtDIh7c09W8xiDSrEQxPagCMEDgCoZjViUqoPrVCdzjINAFK5ciqI+dzu6VblJkPNRm2JGTwKAHbgijbVG5cu1WJv3cfytmqJct160AG1h0NV7ottwavRJkVTveGIoAzpAABSxDg05lBA4pVBHAoAqzIuGPes5hya157VtpbvWZIpDEGgCLOAapucsauYHNUm5m2AdTigDR8OW80erQXeD+7ava7S9ilj3lgDjn3rzrSbdYrRDjBI5rYguzGuMn5aAOpllSYEMPkJ+UVEiJ9tRhgL0PNYb618ihl6H0qaLUN0yOoBAIJFAHVhU425YZ6BqsMI9xJTaenNZkJeSUMvyqRkY7Vfl8xR8/KkZoAry2o3q8cgVgc49ap3sYMh+YD1wOtaWFSSOUgkHoKhmjjkMrjqf4cUAZyEtAVXp+VS2kbRyNjG3HPPWmRLu3IW289DUsahbgruynsaALbRqQHVAFpIGD3PAxjuTTx8q4PbJFVpJtuyV1CqQQcCgB+pW4O0k5zzntVeRVMaqQSrccdqLm63ouASO2ahQyKpRiMNyCKAMiWyWz1XaqHaec1vWwG0cd6zp71J7tIduTH3rWgxtGBgUASKoIb60eWM4H40qgZODSjhTjqetAEMg2nC9KZkUrHBPNRHk9aAJIzyacTwMGoclelOQbiCaABoC8mTSlQu1fXirPCqKi8sMwY9uRQA3btGKhlIVSMcnvVlyCc1WlIbg9B0oAgc7AM8g1SlcE9KtOcnmqcpAznpQBmaldCG3YjsKxdKgMreZ/eyal8Qz+XbBB95zj8KvaJbMIV44xQBo6bbHzTXVQxbEULWdptsFOduc1twRH8qAHRnyJ1k7Hg1rMy8BejDIrMlRfLxiprSbzIgoPzRHH4UAXVOEIqKA7ZjTlyd3PuKhibErbqALU43d6qZ2kipyAw/+vULIOtAEDMSagcjBHepyAMmqkhySO9AGW9x5V55Tj5T3qwu7eTmku7VZI1OOfWmISrAH0oAnJbcvzUrOGbBGTTY1EpOe1P2bDQA+MHd1qyYyRniqseATzzUiy7twZsAd6AF27c5xSkDIPtVcPl9obNPUvvGTQBIrDPoakJLcE81WlDM4K8GplBJBHUdaAFIA600qCDUkmDztyKgMgVhxigBv2fPNI6FR2xVhrhAuAKpytuYsCcntQBEX4bGc05I90Yb+Kg7mx8uPwp8Qba3rQBHJbgjdtGaiXPQryKtLGxUkk04RdeetAFRcO5JFP3bTgCrSQqvXmlMadloAjTeeppXBxipRF8vvSqny4NAECrjrSFAastFkcVCyY470AV3QCoSQvarcsLfLULQNjPagCHzAwIApFZvuimshU5PFSR89G5oATeTTWVm78VOIfamyRsoyKAEQAAZNSmXYODVB2YZ5IqGeVxEAmWY8UAaLX5BwrU9LhnJLDJFZltZy5BdTmteOBkA4wD1oAkRt4yBVhLbfyaZCgRenetCEAjNAEK2xFEkWBj0q3upkoyuaAOW1p9q49DWGZ8YFa2vkiQKPxrEMeSMigCdSXoeJgwJp0LBRTpJSxHPFAFa4thIOaiiTy14q05B4qCT5c46UASsu9c1WMALHPWnrcEDA6VE8xzkCgCGaAJytQrknFWHlJXkVEmc5oAcyZWqci7WrSwNtVJ4SfmFAFixfmuktn+QVytoSj810doSUHNAG9bNlBWpCeRWJayYwMVsW5yAaAJ5uIm+lea+J2/0g/WvSrji2Yn0ryzxPITdgDpmgDH1H/kCXf/XP+orzs9vpXoGoPnRboD+5/WvPzQAlFFFACklecADoTX1/8Koki+Hum7GypTOfxr4/IJyAeDX2P8MYxH8PdKQHP7oHigBnxU5+Gmuf9ez/AMjXz/8AFZf9N0NuzaZH/Nq+gfilz8NddHpauf0NfPnxQbc/h4nvpkf82oA8/wAZFafhyXyPEWnzeZ5YWdTu/u89azP4akgAMjKSB8pIzQB9E+OPHOn6LAmmTNLLI1nuM8cg/eHnapweP/r1Z+HVy118OrWWUlnlndiT+FfN1zeT3s3mzOSw4G45r6L+FXz/AA6sR6St/SgDoLi2JTg1U+xErkmtdoy8pUU25h8mPORn0oAyxbbBkLVWdCW5BrUVywwRSbFb7woAhsovKQH1qzIAyHHWm+YF4FML89aAI1Z8Hnioy+w4przYDVWefkUAPlaqWcAj1qR5dxNVyw555oAikznAqLGG5pJJtjVWeYmTIzigCaQrvNPjbngcVVyzyd6uKNq+9ACq21jxTzKVHXiog+1vn5pssq7TgGgBJHyhOaoTMTwKsFgYzioeDJzyKAIIl3t6Usr4DJ27VZIjVSRVXgtk9KAKW1dpycmq6xDzc54qad1JwgOc80kSHIJoAnMWyPIIrLuWV2PHJrTuHwmAe1Y8mS9AEZQAUwfeFTOuRTET5hmgCaYFrbIrBlU7jnrXTEAw4NYV9GFfIoAzG+U1HbRCS8UY5yKfcHHNTaSm+7BoA62L91Ag9qimmOzrj6Um/t6CqkzEpQBPDqiqwilA2+prYtoVdvNhkBUjpXFXKkg89Kl03WprFwrktHnpQB6np0uQUdjwOnetfzWztkYABeAa5bTbiG/tVlgkw4/hrXmZ5QPnUYGGXuaAL1ncGVAzcgEgAc4qPH+k43Nk1HaxiGxzD8p3dOtSwsXkd5iPl6YoApS5hunDDAxWcr3Q1FRA4MXWQsK1bzY1xkKTkdzUckS7WRQQxXOAetAFx7glDgZxjP0ps8fmwZSRWRhkjuDVMK8wKNuWRe3tVkwsEVVX5R15xQA2SJ1tcqA4HaqE1+tjCrMA8jHAQHO2tCcNDZFGJPsK5eKALqLqSWJ5AJzigC/CzXF8ZBGqjA5FbsTtjGOKzrWIK6kcZ6itFAeRkUASJgE5p4wM81HtIqNmKnrQA2QZc84FR4AOc1Iz5A4GfejBI+6KAGH5h6VJDjHvTlTctPSIDB7igCTG5RkY+tI/AwDTi27k9KjkK9qAIXBHeq0z9hUr/N3xVZ22Z70ARSsFQGqUrZB96nlycnPWoJB+7P0oA5zUY/tWpRJtO1a6/TbMLApAA4rKtrQPeK7L+ldbawqIFxQBNZIEA4FaKxgckdfSq9tH7VpR4K4IoAqSoTVWFvs99g/dcc/WtN0FZV+cHaOD1BoA1FYh9ufxprLmTiorJ/MhVyeRwanP36AAHC0rDEYNMHKnHrUm3cMUAVH5BrOfJf0rSnTaOtUmXzDxQBCZRyDUEwCuDmpGgIBNJJEJIiP4hQAKCmMd6sNlk6c4qvESMK3arqFTgUAUNkoYnFLtYrh+h9K0JIkboTUTRho9o/h70AVoUWOQYyavKmcHFQQRc89a0o4vl6UAVWiK84601QVHPU9auyqVAqvKh27/AFoAbGScqo/OmvbhuTikimCtjuauEBlzQBQNrnnIxS/ZV2g45q2y/LxTFJ6UARCIBRuAxTNiqDxVhstx6VCyGgCMD5SKaV+apQKXHPSgCMLilytSnGORSCJW5oAj3c4p2cDpTjHg07aD1oAEw3tQ0Q68GmEc4HapEJxzQBFKN2AO1Qtx8tTP96mbSWzigCtJbCQAGnQ2So3PWrqxgY4p4GGzjigCuIee1I9vnjFW12FulTfJQBkNp4cgEcZqaPTIY5AMD8avtjGFxTQwLhSOQOtACG3VQMKKJIl2Z4BNSB8daZKyk5PSgClcELtAPSrFs5KZzWbf3KxsM9M0RakjQ+lAGuZADg02R8ITniufutbgtxlpOfrWLe+LgI2WPOD0oAl1m6DXjYOazvtQY9Kw5dSkmlLEk5qRbg8E0AbHmgDGaUSbsDNUUlUmrCMg70AWcgk80kpXDZI6VAZkXJzVS5vF3EA9aALaKrDg1IAoqraozDvVsxlVoAjZFP0qJ1HapMkGnC3aQZGaAIFfPy4NWFiynOKgMLxvV2EHaCRQBX+zHcMCtS2DKopqFfSrUagjigC1A7eYDWzBKSRWDEcPWvanOKANOdsWrZ7ivL/EMe696Z5r0y5P+ht9K831KQPfkEZ5oAwNSj26Ndcfwf1rz09a9W1m3X+wLth/zz/qK8ooAKKKKAA5KkDj2r7G+GKmP4f6XkEfuRwa+OTyDjhhX2R8NpTL4C0snH+oUcfSgBnxSOPhtruOptXH6Gvnz4oMsqeHGjI2nTUGfxNfQfxSOPhxrh7fZXH6Gvm7xrBPDo3h1J5llJs/MV17Lk4B/KgDjCQQT0zU9vAJWlAIPlxlsmoB1zx6UoJAwCR9KAGnoa+kPg+2fAFvnnEzfyFfN46Gvo34NEHwFED/AM/Dj9FoA7KW7COSqfj6VUlLPly27NX5IhyDjBqnJGoGAcUARZxItSAbid3So1TLDLU9iFO0nAoAqSbt5x0qtIzgGrU0gRuBxVZ5QwIxQBUO/BJNVXmxxjNTSO2SO1VHO1MmgB5cuMjjFVJN4bdnrU3mZGB071WuNxGA2B6UARFSWyTmnGhFAX3puSXAxQBNGwUgmpmcPnHWoVjyfm4HapkjVDuz0oAjOep7U2SUGMmmXd4kMJLDr3rPiu1mG0dKALDSDZxTFOKiLqDjGacHGOlAEh+brULr1xT/ADAYz61JFCTGzuwx2oAzhCS5OcYpryY+XGafI+0tzTQBtyepoArSyEjFVGyDmr0kY61EYg3egCuQxAxUiRk9aspGuMFadIqogIoAqzNtTFYt8xJxWlNMGY5qjcIGUtQBhTsc4NX9FXLk1n3X3jWto6bYy3c0Aa+7FV5X5NOd8VUnmwDzQBUuG61RX5nxUk8xOeaZANzc9aAN3Q7+axuVdD+76EV6HZy/aovMUgZHWvN7WPaK6jRL8H/RmbaT0agDrQ/2eDqSc5qWxdXWTcQA4z3qqreYhglYAgcH1qaECFFyNp6DvmgB8CRzTvyWHbNSfL565jwccGlTEK+ZkAD0FMWcFtyjOfWgCWKIFnaUDHZs0OwDjYQwHXg0rqGhGMjnPWmMSgyoNAEGoiVrZQB16bewrl7WADVHKSFiDzmusvHFunmn5gF5Fcrpsif2hKY0OWbIoA6GFWJUsBVo7g4+XioIwuAcHP1q2q5GaAEY4qBwSwNWGXNRn2HSgCLy9z89qnXG3FNB55HWpI4gRn3oAVB8tLuAGMUuQinP4U0fMM0AG4HtUEjnnFTMABk1XdhzQAzAZeahkVAvFPkYBRj9aryt2FAEBAL4PSs3VLsWsSk9WYAVpFeMt0rldZm+16xb2qnIQhiPxoA7y2tUa0imUfMy81eVGSIYHGaTRistiqjqorTaHMQ+WgCO0YsvPY1d8zAqnakeayVYcZbHagBXbauR3rOuYy5BPQVcb7n0pJF3x4JoAzrS58hzG33TWgLgSZGOR0rLuIirgr2NWrWUOfMHbg0ATmVlb5eKeLiTvTZUZhuWmRMcAPz6UAPlclc460yMKF5HWp/L4wTULsFO3GaAGSRBqrKrC42FflHSrvWs++nMEq8Eg9KAFnULubA+XkUQyh0DAfNmgBnyexFRxERy7aANSIAxn1pIEyj+Z26VFGxRs5qaWXemBQBHAo3E+9acYGysqMEOPSr6MeBnigB8gzUDA4Iqz1ppUGgDHCMt1nueKvxkFfLP3l70XMIjXf0OaiVhuBA69TQBOfl4pFjBJNJnLEmnoRjrzQBG6YPFKI8qc04Eb+eafjLe1AEHl0mzFWcCmMBmgCsyk4xTgp2HPpUoXB4pdpoAqr7sakwCvBqYhOhXmmNGE5z+FAESxktzT3AVce1KHDDGOlN4J5oAZEjN1qfysCgOqdaDdLkjFADcHBwaDny+tRGYEmo/P7UATh9vFAYZbmqMtyFbORVSfUI1b7+CetAGqzqFPzVF9rVT97tXOz61EoZS4HvmsW98Sxwj5G3n2oA7SbUIkXJY/nWRd+Ioo1Zd4x9a4W68RXVwMRoQPrWXNLcz8yEqPrQB09/4lMxKqdwHSs5dYu5Ts3lVrEtMJPt3bs1qmzckMB1oAdLOZGwXLn1pI7Yu3z9Kmjtig6c1MoIGDQBVa3CsMdKsCEMoxUcjAGm/a1jFAE2wr3qOSYoPvVm3OsbDgCqE2pPMDg4oA0ZtSKggHJptpJJNIC3TNZVvE0rZbmuis4QqDIoA37VwsQFSSv2qrCvy1K6kjIoAfHFvNX4U2riqtsdvWr64K8UARG2DtmpPIwpqVOKmABXFAGaqYb8aso+1qfLbkAFfWoSCD70AWI23PmtixwVzWLAp3YrYtQVUAUAaF+w+xHHpXmt2f9Nb616FqEm22I9q89nBe7f60AR6q2fD19/1z/qK8lr1XVsroN4vrH/UV5SAQMGgBaKKKADOMHpxjPUV9kfDRDH4D0sEg/uVOQMdq+NyOM/pX2h4AEY8EaV5bAj7NH09dooAp/FHA+G2uk97V/5Gvmvxjbz2uj+GoZpDIxsQ654ABJ4/SvpT4qY/4VrrYP8Az7Pj8jXzDqV7ea74fimumO3TIEhjIUc5J4z3oA5roevPcUtJjBIJyetLQAg6Gvon4OH/AIoKM+ly/wDJa+dh0NfQ/wAHjjwCv/Xw38loA76XDAAms+UDcRzVp5AD81VfOQlsjtQBEpAbk02Q/Pkc/WmsVLcCguBgUAVZSdzVV3lWx61Ynkw/SoWwRQBRkkbzCKpXZdQBgnPpV5l2uSaryEyZGOlAEG4qoxxmmEbuSeal2/L81Qs4U8CgAHFNyRIMCl37u1M+bcfagCc72OPSmu+yMk5p8R+Uljiqd6dsDHceKAMTVNQMkiwLyM1YhjCRjZwfestYpZZ3lVc46Vcg1OLy9ki/vBwRQBaWGXefmXn3qZQ8bDzOBVR5Vc5UbffdSLKzfK0hYHigCedtrfKeKkMshiAGcYqJoAEBwx/GrNsFbILEAetAGW7MXxtb3qUsdgABNXJokViVYMO9VTIqn5Rj60AI4byqiSMjJzTrm4+UACmLLiLmgBGlPfimzTApjNVpJwOtUbi9AyBQAsky7yKpT3BwQORVOS4Z5Dg0obZHk85oAqzZLZJ61u6dhLf8K56Q5fdWna3gSICgDSlbjNZlxIcmrL3G9apSfMaAK4BZuau28Y4OOlRxx89KuogVaAJ1bamc1E168JDqSMHPFQzS7RjNUJZC74DHjqKAPS9D1ZNTt0PmAypwV710BIDoZgeOhzXjGm302n3azRMRg8gd69X0/UotWtYpQfujke9AGjNIDblFPU9jT7CKQSHBAA7nnNVvs7uwZYj165q9G4tt8jlTkDjFAF2SMSKQBkjqe1RgbTg4I9agS8lkdtqYj71PayCSNiThQehoApXBUKxlYBCcHNYkD2o1I/ZOVBwSRW5fLBb25lulPltkhvSseLTZEf7ZApNrJzuHegDYiXqW5J9KshSB1FV4XGxeBU55HWgCQ424yM1DnBxTgAaaSAxoAeFycmpBwuBUW/NJv7UAEoLDGQPxpsjqiDBproW6D9aTywEAPagCMzFuucUxmx1qT5RwBUEyFuBQA2RlJA6/Wo3xxxih1+ZRnpTJGyc0AQXcmyI89K47R1a41+5mfnnArpLtjK+wVmaZELfWJVx94ZFAHceHZhG7o3cYFdRCDtIccDtXGW37t0ccMK6y1ufNhEpIyo5FAA0CxXBK9TT2BGSQajmkLkSKfrSicyrj0oAYORinEDbk0mMU1nwuAKAIJoc/MOnes7cbafP/ACyJrVL5U5HHes67AK9MrQBfjmDFVz8p71JIqqufyrEs79knFu6gg9DWrLIx+VuDQA9ZQ3BPNNfOeKz3donznirsM4ZfWgB4yCKjnhErYcZx0qzkMgPSmsw6Y/GgCnypC1BLGVcNVs43k0MAynPpQA2Ill5qRvlCj1qCM7WxVvaGTPpQAqRsSD1xVpFORxVOGYqTV6MswzQBJikPApc1C+ST1oASTEqkNziqIJTKjrmri/KcmqczFZt2Dg0ASsRsByKkTYec1VJVgR6UkWQBzQBobVzkUhyGzniokc08N60AO3D1oxnmhtlMaULwKAHnijPHWoDOD1ppuF6UATAg9etMfn+LmoTKRULXADnJoAsK4U4Jpsjr2NUpbxAapXGoovVsD60AarTA/wAVQvcohxnmuZu9fhh6S1zmpeMAmRDln70Ad3cagIycMPzrKn1+KLO6QZrzubxTLM3zsymq7an5vJyaAOzu/FQIwoBNYt3rl3OB5fFYYuFY/M2PoKsx3UaDABb3xQBNm5lUl3PPXmkWJFHzNk1GbiVidowDT4bSRzlieaABpQvCJzTBDNOcsfl9BWta6WG5atWDTAo6cUAYVtprCRTtx3roYIfkG5c8VfSzVQMgdKe+xF4xQBlyQHdxioJIWANWpZxu4phlLLgCgDHnicAtkcVmTZ2FjXQTQM45rMuLbf8AKB0oA5qaNnlHJq7DZbUBPOatPaBHBq2ijaOKAKltFsbFdBaxjYKzVjG4YFa9sMKKALSLhaeD8tQtLg4xSht3FAEgfaw5rRgkBUVmCM9c1NAzA4oA1VYU7djmqZkIpRP8uKALjTZXGajRCTnOarJl261o28fAzQBLbwndWzaxcciorONSvatOGM44oAytZUrBxxxXClf37E+td3rxKW5z6V5+s2Z2HvQAmsxj+wL1j1Ef9RXkQORmvX9YkB8PX/8A1y/qK8gHQUAFFFFAB/nNfYvwwiEXgLTPm3ZiBzXxz3/2a+0PACJH4H0tU4X7Oh/8dFAFb4o7T8Ntc3DOLV/5GvnbxWYYfBfhiKBViimtfNmKDG9ge9fRXxPA/wCFb69/16P/ACNfN/i0v/whHhJ3I/49mAwAOM0AcZIipIyqQwH8Q702jGOAePSigBB0NfQnwfYDwKn/AF8N/IV89joa+gvhBGT4GQ44Nw38loA7q5IYDFUhExbO7irk+I0z1NUnPzDAIoAbKNvSoxjGT1qdhlaqucEgUARTAP07VWlXagNWN3XApsoVkGetAFEru5NM6E/IDU8i4XAqq0mwgGgCKZ93G3FVwmeankXqfXpTONhHegBhjw+R6Uw47jmpQWFKIyRmgBBjAwtZ2q/8ez81qq2MgCud8QTYiZB1IoApWDiKRcnIJ6VYvbKKQNMkYDdT71nafISyK3at3ZjBByKAMWIgkAgj2NXYiik/LT7iJZWygAk+lUjOyuY5E2MO/rQA+4FwsgdJPk/u0HUZsGMJtHc015gV96gZ3ZSPXvigCVTNnKy5B7Zp+8gfvBk9jUUEOer9OQKtFQ+AzAAUAV/M3ZEnQdKVjlFJK47CodQXao2tn6VmSzkpySGFAEl+SASAQPWudlnYyFcmtE6qzHymXI9TVOdVeTKKBmgBYo8jNPlHyYpYiVGM8VFPOq5FAFG4bbxUUdwynimTS+Y/tUYJHSgDWgut3Bq7H84AFYETlX61s2cw4yaAL6pjrRJIFGKczrszVGSTJNAEc0hLVBgk08ks1TxxZHSgCARt1zjFb3h3WJLC/VSf3DcEe9ZLJ2JxTFwpzu6UAexQ3bvFmF+oyCasx3C5WGQZ3DJ+tcT4U1bev2WQkkcqTXXwE7w0gOScrxQBpJEREJFIx3FSqpRM7QAeT71DC/mI6srDnOemaklJmTYmcr6UAY/iEzXGjNGSIFLceYeo9qp6ZqV0kEdskgMCDbWHrl9canqi2sjvsgOAvatnTbYxquCOBzxQBvQryMkHPPFSv8jfLUEXyLx0q2oyoI6mgCNVc/N29KUru56ZqwENDKuOTzQBTKkEUgB3mrDYHIOagLEk0ASqmRmkYDrTkYiPBpNu4ZoAgdlA5qlIGLZDHFXJQFPSq0zgA4FAEDtiq08nAHbPNPlfC5JrK1O9FvatIT8uDQBLZOj37ODuVcgiqGtWdzb3AuLfhk+ZR6j0rG8Nas0V/IZT+7kPftXeXUCXlsHaXgD5WH8qAM7RdcjvIxFMdkw6hq6O2uWgZSH3L396891LT7kTBwhTaflZOM1p6frM1uUhuImwBjeaAPSMmWLfCwweoqt5m1sZKmsC31cqQ0cgcd1BrVW7jvY8xFFk/uE0AaKTheCc1N5yMuAMVh+ZLE3zg59e1Oa/ZeMj6igDWdCVLZ4qrLh04GAOtRRX6lTltx96jku0YH5wB6UAZs6srmSMYwa0LTVI7jbDLxIo4Y1UknQqVXHPrWdNHlsodrL3FAGzPdLuIIyM4qOG7EJyOKp21zHKuyQ/MO/rUk0aFTzn6UAbAvAyEZ+8KmZiYQc9qx4c7U56cVeeT5VQUAO3HPWlJJxUAyj9c5qVnwOeaAJPL75qZDlcZqrFJvDBvwpiylGwc0AXVjHmZJq6soC4FZBucNxUqXQ7mgDXVxtpGyV4NZ32wDuKet2pGCw/OgCYnbkk1HMweP6VDJcIQen51ALkchiMUAPQhm5qb5c4FZ4uE8w4OR60kl/GhJ3j8aANTzFUCmSXCtxWHJrUWSCwJ9qpS64VPywysPZaAOlMqj+Ko3nX+9XFz+KRGx3QyqB/eXFZ8vjFXbagNAHdPeBP4qhbUUHO7kVwsniG4lOI4zUkUt3dDliuaAOtk1hBktIF+prFvPFNtAxAlDN7VVXRpJk+eRjn3qGfwiHUkLkdqAKd34smct5KHJHU1ltql7djDyFRUtzoF3asXCllHamQqufmGD6GgBsdvJIcs5bPrVlNHRl5TmrtsEO1TjNdFaWqvH90HA60AcVL4dSXgJzWXcaJdWjZCFl9BXqkWnKy5wM1I+lrj7oP1FAHllrFbFv3oKN6EVtW1rbsRtCEfSurn8PwSnLQpn6VCvh+FHyEI+hoAzI9PgPVVq0lpCo4UVpLoy9PmFNm0K5MLG3nyw6A0AVUSNP4al85FHA/WuI1TXNQ026e2ngdZFOBnvU2l/25rMiiOMxqf4jQB1sl4duFIHrmq6pJcnZErsa1dN8MiPBuZDLJjkZ4FdJb6ZHCuUUA0AcpBoUwQvKMGqzQLExGMmu8WHMZBGa5rVLYJcZAwD1oAxJIy4wBVOS225JrXd0QcVRuW3n5aAMCZC1wAF4qwYNq8CrYi/edKdOmFoAzQSGGK2LMBk5rHAPnVs2+FjFACyrtPFLCCxpHYGpLeRQ3NAE3lkc1LCozzSmVTxUYcg5oAu7FPaoZkXNOWY45qKSbcTQBZtowSK1YowGFZNiSxzW0gGAR1oA0rQKD0rUhAbOKy7XJb2rYgAHSgDnfEp2wN7CvP4k3Slq73xM2Y3FcVbRnPtQBBqcbDw/fn/pkf5ivIh90fSvb9UgB8M6gR2hrxEdB9KACiiigBexr7S8CD/ii9KP/AE6x/wDoIr4t7GvtPwJ/yJWk/wDXrH/6CKAKfxP/AOSb69/16P8AyNfNvi07vBfhBSCALVvx5r6S+J//ACTfXv8Ar0f+Rr5w8WYk8BeD26FYXU/pQBw/c0tGcgGgDJxQA0dDX0T8HpM+Ao19J2/kK+eCNgNfQ3wcMb+BkAHzfaGz+S0AdndoSAE696qFXI7cVpzxkDis9gwJFAEDbsYqILycirYRjTXjwDQBmSblPy96RiuwFjzUsvBPFV5hlB6UAQTN3B4qq7ITk81aZSF++NtVnRMZHNAELNk5HSmqCGOehp5IAGBTS3FAC8ZxSqxBwRxTEUs1TOjLj0oAgd9jk5FcVr14z34QHvXZXrRxwsxBzXn14TPqTEdBQBrWiK8QP8VXYpGBKk8Cqdh8q4rSWESDIoAiJ+YkcH1priOddso57EVI4EeQRUIZSeBzQBSu7WW1XzY18xO+O1Zp1EOduOfSt9pDECFO7PVTVEx2ckreZBsYnqKAK6MSu4E0GVpD8zYxVptNZlJilBXsM1TNu8LfODj6UATyvGIwC2TXP6tcxj7h6VevZUt1JJ6iualilvpSsW7rQA1bkl85GBVqOYSckEUq6cltGDL96oGkGcJwBQBLJMV6ZqjcSliTzVpPn61FcQcEigCiKWgrtNFAC981ctptpFU6dHkNQBuibevFR4OearQvjvV2Nd2KAHwwgnJq0FA7UiKAKN+Dg0APW0841fttBWTG7oetNsw0jgIMmuotLdwgzQBn2+mpZOrx/eXpiuqsroyBHZsEDG0iqS2oHzkj6VYjxGA4U49KANx5Ntn5xGcdq5XxD4gvrVQlqPK3nHA7VtXM2yyEqliDwV9K4m+vG1Scx87ouPwoAt2MBdxPIxLP1JrobVQjAZJHesjS8CIIeSPWtu3XkZ60AaiJGUBHA96sKRtAHaqUb4IB6VZ3KCpFAFjOBk0xiSTwKSQ7wGBqMyYkA9aAFB3j7uKBDzSg7W6U5ZPn6ZoARY8fepzAKOOlSbs9BiopHA4znFAFO44BPpWXcSknrVq8uMbhWRK5dxzQAsz8HNcL4n1YGYWqNx3rq9UufIgIzziuCntmup2mZckmgBLG7jQ5BAxXXaZ4h+zqELK8R7Melc5b6KHKnAGexroNO8Owu+JIsigDYbVrOc7vOxx0PIrOupUkJELmTPtWvbeH7NB8kQ/EVZXS4onOVVR2wKAOWgtb1WzEWTv1q35+oKuVbDDuOtdCYI1BI7e9IunTXDYgiIB7mgDFt9Z1VOGO9R/eqSTXmP37cbu+2ultvCrNh5nznsK0Y/C9iGy0Qz3oA4F9WncfuoZQT6VCLnWC3yQuQfWvUU0a1j4SFT+FTHT7YDHlqD9KAPMol1wsC1tkVeik1AKTNaOB7Yr0JbOMOBih7WNG5UFTQBwHm/KCy7DnuKki1AxsA5BUnGa7d9MtpjzCjL3yKjfQNNbG+2G30FAHKyXyIu5WyvqKrDXosgFjx7Gu2TQtPC4WFQPQilHh/Tz/AMusf/fNAHIjWLdhlZDn3FOTVUP3pFI+tdQ2gWGcG2TH0qvceFtOYf6kDPoaAMZdThK5SRT+NRT6rCFDFwD9avy+CLCUYVnj+jmqE3w6gJPlXkv0LEigCtJrUDL8soBrNn1yZW/dqz+mK0h4CuYWJEyOO2akXQri1IBg3AdSKAMhNX1OXpbkfWpVvdXzkQ8VvQQoh+ZcfUVpwwwyIPlBoA45r7WB0hBqCS+1sHiAV6CthFjIWlFhEwyUGPWgDziLUtQ3H7QhT8Kvx2/28qJLgAHsDiu3fR7SYbTGrA98VkXvhC3uFPku8Tj+JTQAWGl6dbqN2Gb1JzW7D9iCgRxx49wK4VtA1i0dlSdnQdCTVqz/ALRt+Jc8UAdtPpGm39sVmtoyCOoUZri9W+GtoxaaxZkPUKeldTpl4ZYgCea1xkDdu69qAPEbzSbzTn2yRsFB+8B1q3p1zgfMMfWvWLqztrtCk8YIPTiuP1bwkIcy2ZyOpFAC2LrKg27TWrFH5nGAK5ayle3k2PlT6V09hOGAOc5oAtf2bDIh3KD+FYGreEoLhS8ACSDn2NdZEwZamVVJwRQB5FcadPYviWNgR37Vp6PqI5jfjHrXod9pcN3EySxqR2NcDrXh650iX7ZbKXjB5AFAHSWuxkDDv3q1tU1z2j6oJ02kbcDJFb6yoVBFAEghD9qQWm48HFS2xYvjPFXliA64oAzfsuxgSc1IkKljlcfSrxiGSDSCIAE0AYeueFbLXYVd4wJk+6wHJ+tVLDRrmw/dCPIHAI6V0gkVOQxIqZLoOPSgCpaWjooMnBNTuArbc0T3KgDBzVSS5UrkdaAJy+CRjFZWoQJOpwRkd6dLfbcqxBqg9315/WgDCmh8qRlY5561WYY71oXZ8zmsw5LEUAMALScUTjg1NCo3E0yYdaAMzH7ytKIF4+PSqYTMlalsQicigCv5LmnLC4NXUkU9qcxU9BQBCkZ7kVYEWRxioDnPFSLNsFAD2XAqvtJcjFWY5BKcVaW24BFABZoVXpzWrCcDoaqRRsoq5EG4oA1rJeM5FakPSsq2BHFasAwv4GgDlvEnRq5u2QZC4610niLLkgGsi2tm3IaAJdRtgPCupnaTiAnj8K+fAMAYr6gv7dIfBOruw62x/pXy+v3RQAtFFFAC54r7S8C/8iVpPvax/wDoIr4sr7U8C4/4QjSOf+XWP/0EUAVPid/yTjXf+vR/5Gvm7xKwl+HfhV9pHliVOe/3a+kfiaM/DnXv+vR/5GvnHxKUf4beFGDZ2GUHg8H5aAOGYYI6dO1Abaciggg84/CkwKAG7ixJNfR3wcZF8BReXtDGdt3HsK+cuxr6O+D6qvw8iYKMmdsn8BQB28xOTiqvlgmrmcpUOwE0AQMm3vULn5Wqw8RINU3yqkGgCjMTtOKiAynNTuMA1Cx+TAoArXCALxVHBz1rQkQlaqeWA5JoAik+7xxUUbDOMZNS3Ckpx2qKIFWwRQBMGC9Rj6UGQNwDSc9xQqqoZmHOMigDM1Wby7Vz7VxEbB52b1NdN4jmKWZweTmuWs+SufXmgDctsKRgdquwvVSBgGGPSrPCjd60AE0oPGM1EsLnlTinqhZsg4FTkBV60AZzy+WSrjJ9aq43tnPWrlwgbNVFgwaAJJFKxAqTn1FNW4nRRvAkH+1UmSi47VFJIhHIoAiuYrG4UtMNrelUvtNnYofJjBPrTLuQNkViXc21cA80ANv9Q+0yfdxg1QaTbTGbJzTDkkZoAsRTEVZ37kzVBc54q7ChK80AU5V+amYqzcJiq3rQA6gcGkGaX2HJoAnjbBrSt5eAKy0XnBxmtaxs5rhlCRsee1AFkHPTOfatHTdInv5BkEL6nvXR6H4SKKLi4UkH+E9q6YWEcKBUUAD0oAwbexiswBFFmQdTWtawlo/MZQDUoREbIAzSSSMOhxQAjBVbpQzLgDI55pkrNsDd6rZcvuYZHagC/cMslk+OcKfu9a4PT226lIecM3Q9a7W2/feZCuVDKeRXJXVi9jfO65KKetAG7bxjeGrXt5ADWFa3AdF2+nNaNvIODnIoA2CRtJFPjlwce1URcLwOtSbs8hsUAaHnE8BRUbykfKV5PT2qk0pQgqalW6J4P8qALCs64DHOamEgGMVWDggDkilZgn3QRQBYluAq4FVGnyvvUUsn1qIZJDBuvagCKU7iSw6dKqYALOwq5JvZ9u3aPWql9IBblMjdQByuszNcXAjXoDTbaxwRkVMlsZLgsTnmtu2tQVGeSKAK9vp6Y3YrbsrfYQevHeoBFiPnAx0q6srSBEjXJA/h9aALBkSNevNVRHcXs5SAEZPzM3QfStOz0aSZhJdsQD/CK3Ft4bWFY41AA6etAGZZ6BDAN0h81iOprZSFI4QEQAURMNpHWmSGXB29KAJOFWmjnmowJHj5OGp8AZRtc5IoAmA4pNhJpQ2G9qkLDFAEfQ4prpk4qQhTyDzSZz1oAVQFXC0q470i8Zx3oxmgBHcr92oPNldyoOMUSzrCdrdaakqy8g4zQBOJAgw/JqWPbKOR9Kq+TlgScire5UUY60AHkjdT/KApgnGelK03GQKAHeUKQwIRziozdYHvTVuFY8mgBJtOhmTmJSPUVnS6K8fzWzlfY1tI4PcfganUL13UAcqXubbImRsf3hRBeiXKRN35zXUSQxSLggH6isS98P8AJlt22MeeKAHxt8uAamjxjB61z63c1pcGK4Vlx/FjitOG7RyGVgc+lAF4wKxOary2CODxViGbcuT+dWAwYH3oA56O3Npc8dDW3EwKiq93AeCKWEkKARzQBYYDPTNPWNGGCOtIvHNPTk80Acx4k0AbDPAuHHJIrmLO+e3l2SZABxzXqjoHiKPyvvXDeINBbzWmhj4PYUATWuphmG0jmtqCYsc8VwcQkgYK6kEVuWl7IFGQfzoA61HD06aFHTBUMD94N0rMtrn93uzzWhFKHwSaAOW1nwz8zXVgPLYclR0asKPVpIQY5kZJF4IIr04A8kEYx3rF1nw5barAWICyeqjFAHPafrEbuAXFdFDOsgDBhivOb7Rb/R7gnDGMHhhzmrtj4ia3VUlXg8fSgD0DzFZgSc04t8pxXO2GpxT4w+a1hc9l6UALcE4qqJCnNWpX3AcVBJGCOBQBUnuD1rPmvmXIqxc5jBJGRWOzGdyAKAB7omoTNnnvQ0RHUVG0R6jigAMxbg/hVd1ZuikDs3rU+wgA09pHeFIWxsU8cc/nQBBAm40+6gwgOantohuPHFNvFBNAGUpBmwBV8JlBWeM+Zn3rYt1DRjdQBW2MF4p0QbGDVptq8AU3bnnNAD0RSOaq3C7DxVqJCWOT2plwinrQBVtpCJBW9AwZBmsKJcScVrQE460AaccYNW44xVa1G5eauxj5sUAXLfitGJv3bcdqoW6nHNXw+y2PrQBy+runngY61LpsCySKMVU1Nw96o962tFjwd56igCXxUiW/gTVhjINucD8RXygOgr6s8ZTZ8Gatg9bc/wAxXymOg+lAC0UUUAA54PUdDX158KbmS58C2LSPu2IEx9OK+Q+uATjNfWnwetXtvAVo8j7/ADOcenNAGt8TOfhzr3/XnJ/6Ca+c/ENuI/hb4XILYlkmZsn2Svoz4l/8k51//rzk/wDQTXgfiWFZPgr4WnBGY5ZB/wCg0AeZ5yScYoozkA+1FADT3FfSXwdAb4dRe07fyFfNx6n6V9I/Bn/kncYz/wAt2/ktAHauMDA7UwfKMmppQBu5qEfOmKAELKFJzmqExDLkHFWioXIOapzRkgkdKAKkhBQjvVIEnjB+tTsCr80qgbCMdaAK5YEYzVWQd/WrZjXmqxGWx2FAETgsMCkMe0hsVKQdxxQ6lkAoAiZgemKjc8Hd0xVgxpEmTzVK5mR0IAxgUAch4nnG5Y+1YVtlW45FWvEU4e7CA9Kq2xEfvkUAbNuSMNV1DlRmqtscxjirYdB1oAeqkHrxSSH5TzzTjhl+WqshIagCJ2YnFIqtn2pS3zdKtQxhgCaAIiq7eaz7sogNaUxVQcnpXPahcKC3PSgDPvZtgJzWBPKZGPNT3t2ZH2g8VT2kmgBADTwKcsRNWEt+nFAEcUZY1pQRYUdKZDDjtVxFwtAGdeLhSQM4rPHNad0Mg5JAqKy0251CcR20ZbPHSgCkBzVq0sbi9lEdvEzknGQOlegaN8NJZQkt7IFU9Urv9P8ADunaVbrHbwAOP4sUAcdoPw7sms1lvgxlPUZrsNP0DTdPA8mAZXoTV6D5N6NzjpTvMUZ45oAawK5wcD0qrKFC5Jzmp5nytU+WOD0oAiaIHBFMaLNXUiGOaYxReMUAU3VdgXFVJnAIQfyrRdN9SW9tFIrea4G3saAM2wR5pHSMZJHJ9KqajbIv7ogZ6gk5ya7jSIbVoG8uNM9CR1Irm9Tto476RihCqfkzQBzstk8CLNCDk/eFLBcnoeo6ipbm9jZikbEHuDTLPS5r+X90CMnnigC1HPkjbnJrRtrG7vCPLjbHrXQaP4VjgRJLgBmPY11MVtFCu1FC8dhQBy1r4ZlaMGQH860I/DkS43Ct8ZCgEGlKnPHSgDJXRLcD7tP/ALFtyORWmFPepkQUAYZ8O27dqqzeG4wSVrqwg9KUpuGMUAcHJoEgclef6Vg6v4fvdhKRlj1OK9Va3Hp+VN+yDHK5FAHh8NhNAxEsTKT3IrSijZUAjDOx7AV6rNo9tMctCp/CoRodtGcxxKD6YoA4Oz0W5uSDONkfXHc10djpkFov7tBn1NbR08qOBURs5AeKAK+zHXIHaqc6qZgS5JHWtU2zn71R/YUZySDmgCordMACnszKpORVn7Jzih7XPGeKAMsM7SZyMe1WAwzjvUxstrfLQLQ7yxNADSC2MdqDkjFSeWRnFRbHB70AOQDacnpUYlGcYpCxBIwapSS7GJzQBpq2aDIMGsWTVDGO/wCArJutfuySLe0nb8KAOhugJDnr71BF8h6/hXLP4g1JB89jMF+lQp4nJkKvDIn1U0AdwLjaOhpDdjPNckniGLJ5Y8dMVBNrwJOxXJ7cGgDr21BQTzwO9Z8/ii1gyplG4fw1w13qGo3L4UOin2pLTR5ppA77mJ7kUAdU3ijzD+5Vm/Cmprd9I/yRH8qfp+hn5QUx+FdJaaOkfO0Z+lAGXb6teIAWgLewrRi1vp5kbKe4xV82yhgFQZ78VObG2lGGQZ+lADbbUopeAy59KvrOrqBtyfase40KJhmEmNhzkGqqXl5prhLhSYs4DgUAbt5p9vfwlZY19iOtcff6TeaVKZLMF4gfmB6iustryKcbkbirTbH4PIbqPWgDkLHUVlTGeR/D71rwXCMBzzVLW9AKqbmzGyQc7V71h2WsBG8q4BSQHBzxmgDsJsPHxVYZA4IqvBfo8fUfnUnmA8igCRZW3YPIFWkbeOOKzBJhialjudtAGkWOAM0LtdjuUFfQ1TW53VZDfuw1AGXquhRXGZIwA1cu0U1nOY3PHavQAx2ZrP1DS4r5NwXD0AYNpKWjxzWjFcYQdc1nC2lsZjG4OKvQbAM4zQBpRXXyY4JPrU6yknI4GOlYzPsckd+1TR3Bx1oA0JoY7mPY6KynrkVxGueDtu64s+SeSprtYZgR1qYkNyTx6UAeOwfaNPlxyrZ5U10ltq4jKJKSGYcV0+p6BaahGzbAjHow65ri9X0eWziAYMyg8Oo5oA6iG8R0HPNTs+8cVw9lqy2hEZZ3x3JrprK/W4AZTkH3oAnuY90eDisZ4mil4xgnmt/crsQaje1jk7UAZogWQZAzUb2YwPlIrUS1aI/LUrRkgZxmgDBa0A4xVd4cHpXQSQZPIqq9sCelAGdBHtUk1VulrXkiCR1k3JzmgDL8vL9R1rWtlxH1FZ+zLCr0QIUYoAZKrGTAq1HbMUBNMWNg2TVtZQqAGgCEwlBkVQuHJbFackwKmsqcbnzQBJbrzzWjCKzoDzWnbjIoA1LX7orQRckEVUsVypz6VowLk0AWoVwMYqWb5bc5pUwO1JenbbEnpigDj7rEmort9a3bR/KjxuArlbzUre3vGZ32471A3i+wiTmbJz0oA6bxRIH8HaoN2SYD/MV8xjoPpXs2veMreXQ7uJAcSR4rxnAHTpQAUUUUANYA5LdO1fX3wkZX+H9gVOflA5r5ExyAfu19c/CRFXwFZbPu4oA0PiWM/DrXv+vOT/0GvnTX2+zfCrwzGHdhLLLIVJ4HC19HfEYA/DzXs/8APnJ/6DXzb4iVz8MfDBdfl8yYA57YWgDhsY5/Sig9aKAEPNe4fC3xhoek+EPsOo36QXAnZlDAnggeg9q8Pwc+1BUE+n0oA+tbDXdK1gkadfQ3B64Dc/kau+479RXzN8Oria18c6b5Dsm6QKwHQjPevpuYF+U4oArySYOP51XZyM1O0XeTrUciKV+XrQBmTqxJIqDewkArQcFRyM5qrKAG34xigCsXLMQary/IOasHklutVpQ0incOO1ACeYoTPXNMWY54FEcQ29efSpkjAHzYFAFaZJWXdu49Kz7pwsfzDtWw42jnpWPrDKLViB0HFAHnWpssuouMcdqktoduDjIqrdsXuix65rStQQV9KANK2DBcgcelX4445Uyy4NNtFUr71YOeRxj6UAQ8RrgcioXKt25qw6hRxUW0HrQBXCjd0q0gUIDTkRC2COaoX12tpGxY0AU9VvEQEZ6Vxt/eNIxA6VPqGpNcTHn5c1SmhIjD9jQBTPJqzBEGIqvgbulXLbhhQBeitRirC24Ap8JXbyanCnGQM5oArFMU5QRgdjWlb6Pe3hBigYqe9bdj4MumlDTDYhoA5m00afUbtYVjOwnrXrWgaBbaTZqFij8zGScc0ul6NHZKFVR9cVtMhCgAUAIOfucVMkZOMnmmCPjjg0o3L1PNAFGcmK4yD1pxIA345NV75Sfm5yKfazedFsbqKAGl4i+XlAPpTjNCThStOltIpF+ZKptpURy0bsremTQBbONpxio2iyoOay5o9RtjlVDp9aS21VyxSZNh7UAXnHpxUHlszkZzSx3HnFt/4EdKR2OV2ZyBjjvQBa0yZbbUo4hIVTPIArK8Ya5BBqn2eMMxI5wOtXbJzZ5dcPcMeEYZzSJ4ek1HV/t9yhz3THAoA5/R9BudavBIYnihznLd69M03ToNPjCxICcYzSRRrCiRou3HAxV+CM8ZoAnjVivIxU6jFMT5TVhVyM0AN20Yp5oAzQAzkdKmTpk0IozUpUCgBd/y0itk00gAcUKMUAOB+aplXIqEDnNTI2BQApWoynNTBs0bQaAIdtRSLzVoqFGarsdxoAYkQNOa2BqaJBipSvFAGebUDrUMlsQDtHOOK0nXIqLBANAGSImx8/3vSmlGX+HHtWlt5yRzUbxF2JNAFRE4ywqQIvpVnycLjFQTstvGSxFADHgjb+AVWezhYnMa/lUttOs+cGrG31oAoJY2yMT5S/lUwhgA+VEH/AasqgJOOKbJb9waAK7RRMuGhQ/8BqjNpFnOcm2j4/2a0CCnrSBjycnmgDMXQbAcm2jH/AasJo+nKP8Aj3i/75q6vpT2AxxQBRfSbFgP9HT8qcmlWqH5YQPoKtjNO3GgCEW0cY4UUvkt/D0qQnbTgWPQ0AQeUd3NSeWB0qXaWPNNYbTQA3GOopstvHdxlHHQVNuAAJoB5yKAObezutJdmhHmQsclfSrsOoJLGHStZlV85Gc1iX1ibVzNAvyHqooA00m82HKcnvXP694ch1SEtF+6uAM7h3NaenyKY+HwT1X0rQKFgKAPI2u9Q0e5Frcxk7eh7Gtyy1nzkXceTXV67oUOp22CoDoM57muD/s6WC52YK7Tg0AdOs6ugINLuY9KzoIpVUHPHStCHfjmgCUMVxWlHJmEVQdMAGrcODGooAuqcAH2qVTkVEi5UZqUYFAEFzaxTody/N61ltp80JJU8VtPKFXkVB9qRvlzzQBivGe/WoHOytS7hYZdR1rOZAwyRzQBHHdlG5q9HfK2OazpLfd0qIIYm69KAOgEm8DBxTZYYphhxnPBrKgucnBbGK0Y5hgY5oA5PXPCCy75bM7G67fWuYtby60m6EdwjKAcc16yzCRgccisnWNCtdViIkUK/YjigDMs9ThnCkYyRWnHIpVSK4W60vUNCuC6qzwjuOeK2dK1qO6jHtxz1oA6Y8rxUZBCD1pkM25ODUoJZOlAEYJYYNMMe2pkPzEMKcygigDKuxhDWFMMsa6C9XgisOdcMcdKAKaL8xq7biqyD5jVyFTjigCw2MYqrKjg/L0q4kJduasi3BXFAGLtcAk1WdwWwa1rxPLT8axJVPmEigC5AATWtaLngVhwMVbmtywJJyOlAGzbphAK0YU4qnb52itS3UEcigCZRhRVbV222ZPtV9YyazvEClNKkfHCDJoA8H8QaoX1iW3ckIT2rFa3dGLYLL/DT9XcXerTsMD5jipbSOd2jWQZQe9AFl7fzNKlEn9zI/CuOPWvRbiFZbOVBxtjOPyrzvZz96gBB1pT0NG3b3zRQAmcjJ+lfXvwriEXgmzwc5jU4/CvkE8ofavsj4bxeV4C0tmOS8Cn9KAJfiKcfDzXv+vOT/0GvnHxAQ/wq8NEbvkmlBz05C19H/EQZ+Hmvf8AXnJ/6Ca+b/ED7fhJ4ciA5e4mOfoEoA4KloP3sUUALkYpKKKAOr+Gyh/H2lgjjzP8K+oJlAjCrwa+Y/hjj/hP9N/3/wDCvpyQbuc9KAKrqQvrVZVYscjir7HC9M1BgnPagCtIoxnFZlwxOeOO9azxsc5NVWiVgQcUAZD7MB0zkdu1GwykNjHqKsSQYBCN39KBGwGO4oAh8sZwQKY8WDkH86eI28zJNSSoXAxxQBSmYbCDziuW166KW7gA4rqZYyqnNcn4iAFs1AHDcyTEkjrW3axZCmsOJD5pz61u20m1RQBeV3iYDB5qw0+0cmqSXLSErjp3qOZmk+UHkUAXTPuxg0uGbkHGKpxIVAyakluVt03E8AUAT3FytvAXbggVxGqak9zI6gnbmpdY1WW7k2RnCCs2KEz5xnNAEIjyORV+2t3liKsvGOK2dN0AvGskvTtXRW2lQIF2qMigDzxNEvbm62QwMfetiHwXq4YExDH1r0rTtOh3AkhD7VvC0KBSH3igDx46Lf2jgPAW9cV1/hvwobgrPdLhM52muxNiksgyij8K14LeOOMKpAxQBXttOt7eIJGgAHtSvGiuRyR2FWZTtXiqbsc80ATxxKcHHHapWwuBjikt+VqXaG60AQEEcjpUbnjOKuSIBFlaqYLJz1oAqyqrEKR1rPmU2k6svKk8gVrvFu+tZ1xhcg8mgC5G6TRhlP4UjqAvJ6ViLdSWk2eSpq6L9ZY8d6AJJZQUKnGB61i3MS3UmxFx6mtC9wIMqfvelVAsmwCBSW+lAGY73Vk21PmT0rR0tpr1mcQn5B0zWrbaDcXKq9xgKeorZttIS0P7pto/nQBn6VpqLKbqWPE2eA3OK2WwAQgIJ6mpCgVfelijLsMg0AJBFvYE9a00jwM0kMAWrIUYoAhWPkk04vt4qXhQaqScvxQBOp3VIBzioo6sIOaAFUc05uaF6mnbaAG4pQKXFOAoAQrRipAM0YoAZnFOViaQgUJ1oAJCcVB3qZ6QLk0ATRDipMU1RhacOlACFahkTPSrGKjccGgCow5xShSBT9uWqQrgUARYJ6DpXG+KNTMcoiXcOea7KT5Y2OcVh3Gkw37FpFyfWgDO0Sf91uLZzW5HKHHBrIfSprGNjbDKjsazbfWZIZWjnQowODkYoA64beMGpdgI61kWl6s4BU/rWpG+VoAV4ARniqj27AkjGK0F5WgxkrQBljIbBGKmJGBU0tqWGfSqpUr17UASEUgU0xZcmpQcigAIHANA64FBTeR7VIqYJoAaG5xSOCalVBjNIWHTFAEOPWkwccGpdop20YoAhU4608jcp6HPrShBTtmKAMO8sWtn8+DOD95av2Nys8IOeR1B7Vd2h+CBWTdW5s7gyxZ2HqBQBqOgk5BrB1DTPNuDIFAPetSG6DxgofrViTleQKAOdjs9gxt4qYWwx0xWqVHcUxoxigDNkhzHjjNFspDYNW9meKfDGBKRjigB4GFzUbPgZqZ2Az6VlahdCGMnNAFXVNQKDahyfaodPM0jKznms1C15MW5xmugsY9qDI5oA0jGJIOcdKy5IQjYIrTj6YptxACuaAMh41zwDVSaI7jxWp5eG5pHRdx4oAwZImHIBojuZYeOa2jEv939KqTWQckgYoALe9JYAjrV7eGc55rHktpIgCDU1vcMCA3WgDRkgjmQq6hg3BDdK5LVvCYgdrvTyVbqU7V1aybmUVYUHBU4IoA8907VJYZTDcBkccEGuotLsSR9QKm1Xw/baghKqElxkOO9cuou9JmMVwCUBxu9aAOk+eRtw6Zq4EJUVl2V3GyBlbIPatVJA6ZFAGXqOFzWHKQc8Vt6jyTWJIOTQBWC/NmtC32AHcQMVViXc+PWtO0sftCygyqm2Pdz3oABIipkVCLwhie1Vn3AY5poU7aAHTzeaeelVvJB5xVgR81aigBFAFOG2yQSK3LC2CqDSR24AHFaVvEBjigCzBCMYrSijCioIUAAq10FAEqHmm31utxZSQsMhkIpYuWq1tzj0oA+VNdtHs9buY2QjZKSM+ma2NEb7Qq7kGBXVfF3REtNRgvYUwsw2tgd+tZHhLTGMe5unvQBLqNuttpFzKASxQ4x+VeWsuMfSvadYiRtPvUQZEcB/mK8WYksc0AMzQGFBXJFOaI4GP0oAbjHJ6d6+wvhcZH8BaZ5jBh5Q2kelfHvC5Gdy4r7I+Gmz/hANK2DA8kUAT/EL/kn2vf9eUn/AKCa+bfEC5+E/hlvS5n/AJJX0j8Qv+Se6/8A9ecn/oJr5t1ieKf4R6Cik+ZHeTKcjjolAHCdTn2paUrt78UlABSUZz0oIxQB2XwtUt8QNPAGeT/SvpeQBZME4r5p+FH/ACULT/xr6XuhubigBrD5eDVScuBxU20r3oJO3nGKAKhmIjOOW9KroZGJ3Jt9MVM0YLkgmnAKoyxyB70AZstq4kLF2XPoakjhEa8tn61alAcbh0qvt3ueeKAIyqknA+tM+UHAOanaPb05qFomznb0oArTAFW4rivFKAxYyRXcTBgpwK4zxN93nFAHErAMk5yaswSGMjf0pQoZhxip3gDDk7vpQBMroV+TjNOjjwetUujBQDUjziAfO3agCzPKsUbFmrl9T1WSc+VHwoPNXZJpLxsBvlFZ5tM3BHagCnFE7ygY611OlaOoQO9U9PsgZxuGQOldIkgjUKTgUAXEiCKqr0FWrRP3lVYZFYcGrdrNHvPNAGlH8rcVft55VbDN8naqMWD8wPFTF2d0VRQBu2/7181ZK4Y0yxVI4hvIU4qWV4xznI70AQMpHOajZS7Cq81y7MRH0otriQttYcigDQQbalJqEN8uT1qtLfBgUjHzDvQBZuZAsefMH0zVCG5OcgjBqnIhmbMjnP1qNoQo+Vz+dAGq8pxuJFY8krNcFgeKjklkRGBfIqGNgOWb60ATygOvNV4EYSMOg7Y71Mw3ssa8s3QCuk0nQYo9s1ycuOQvpQBnWuizXgVpCUi7+tblvYwWihI1G0fxN1NXGbLFei9OKg6Pt6j3oAfnHSnDJFKkY9KnWMetAEKxFjVuGLGKVI8d6nXIoAcBTu1JkUE0ANc4WqvV/wAanlb5agg+aXBoAtRrU4FCoAM4pw5oARRzTs4pwAxTD1oAU80DpS8Un8XtQBKn3BTWOKcDgYFNIBoAjJpyGkIFIOOlACtyakjHSo/4hUiH5sUATEZpwGBSA0pPNAATioyMmnmkxzQAmwCkYZGKkwKY2FzQBUnOQFqOKPHNSP8AM9SoBigBojUr84zWfqGjWt/GQ0a59cc1rAgdqMA9qAPP59IvNLk3wszRk9PStGw1J3AVxtI65rq5YEkXBHX1rDvNHVGLxjHc4NAFqC5V+9WfNFYkTCHPr6Vbhu1faG+U96ANNW3Ux4kYHnmiLLqCikgnrU0lttBOc+1AGVLbtEdw5qLzsgE8VrbVYYxxVG7tcZaMYoAIpARg96scBdtY4uhE21h8wq5DPv5Y5oAuZA4prLSDaee9PBBWgCEgnpQARUyqAaUqKAGqKcRxSD2oJNAEZBBzTnQSLg0o680GgDnrqOXTZzMmTCTyBWhDcrcKpU8Yq1cQpLEVPOa5u5lfSbkKQRC38XpQB0BI6k1GStZsOpRyRhlfI9aU3Yc7UOc8UAXQSThelTqFjXnvVeFhHHz1qK4ugsZdjwKAEvbuOGMgmuUu7mS9k2AZQGnX1695MVT7uauWFphADHxQAWVvsUfLW1CpUA4p0NvgcLkVbWMEfdxQBXUkPmrO4OOajfCdqgabBxQAssSbsio3h3jIpS4I4606KTC4NAEADIcEcUuxWHIqZpEycjNNLArkAUAV5bcYH0qmbUHkVoSS/L05qOLn+GgCqiEHFWUJWpTGFyStJt4BFAEifN1qK902C8hZJFyexqVN2fuirK55Ur1oA8/vrKfRpdoB8snrV/TtSdyIwFwa6u8tUuojFLGGBGATjiuSutPfTrrKJlPUdqAFvH3yGsyUD0qy8pZznGfaoWJPbNAEcSguO1WHYRofmNRxna2cVLIhlXGBQBBu8ykZCq09YGi+8MUSOduAc/hQAyIktitK3jyBWSJdrCtezmJA4FAGnFHzV6GI8VXt/nPFaMKlcCgCeOPAGRUjqMdKenToKG3n0xQAkPXpV1AePSool4GDzV1EJA6GgDlvG/h7+29BlRF3SR/vFHuK810eGaFipXaGG0j0Ir31LfjJ6EYrynUNFms/FM8UnELZb2I64oAzZLJrbQ9QkwsrvEc57CvBJABK4Bz8xr6Q1o+T4fvERCP3JyQOgr5vbmRuQeT0oAZ3FSRSlVYUw5B4pQowaAFyNmSnHrX158Koynw90wF9/wC7zn8a+QwxCkgZB6V9d/Ccn/hXemAnOVPP40AaHxC/5J9rw9bOT/0Gvm7VwW+EGg4wB9tnH6JX0n8QBnwBrv8A15yf+g1826wdvwh8PD1vp/5R0AcKSWAHYdM0mKXocUGgBi8PzSucsTSqMtSPwxoA7T4TY/4WDY+uGr6SnYh+DXyr4P8AESeF/EUWqPAZvLUgKPevR2+OkRYk6M4/4EP8aAPXvM9aikcn7q15GfjlF/0BX/77/wDr0g+OEJPOjuP+B/8A16APWwCBls1GwB455ryn/heFv0Ojyfg//wBegfG62yCdHlx/vD/GgD1bZldoIFM8jyzk9D3FeXH43Wh6aPN/30P8aB8brIY36TcH2DD/ABoA9PnC4GCaqFiGAGa83PxqtS5P9kzhT0+Yf40h+Memv97TLkH2I/xoA9FuXKpk4rhvE7FuRyCazZfi3p0gx/Z1yPqR/jWJqPj6xvGytpOM+uP8aALqIPKyQahSUK2Fz+NZP/CYWvQwyYHbAqC48UWsoO2F0PqAKANe6vUhGere1ZokmvZi2cL2zWYNWtnfdIXIq7HrunIoAEgx7UAbdpZbIyTVVYt07MAeDUlt4p0tYdjs4yOpWs/+3dPV2KyNhj6GgDotLiQsSwxVyeEOcpnFYVl4l0uEZafGf9k/4Vd/4SjSGx/piD/gJ/woA0Ahij60+xJZ2OTVNNf0d15vY/xB/wAKIta0tJeL6EA/X/CgDehuZEcKckVrW3zTod2MVgx69ouwZv4M/j/hUi+IdKWYY1GDH1P+FAHUzXX7wfMcCpRegLjk/WuUPiDSt2TqMJ+hNL/wkekgj/T48Z9TQB0bXagnHH0pttqCpPhzgeprmpfEuj54vo/zNU5/E+jlubxeO4zQB3txqC/8suc1DE4wSfvGuEXxhpaOR9sG0exq3H440VB813yPY0Ade43d6iEZY7Qfzrl3+IGiD/l5b/vk1VuPiDpGPkuXz/umgDsGtGIIYdehqpLYy7gqDPP51yX/AAsnT41GHkbHtV/SviZoomMt+0oUHKgLQB6LoGh/ZFF3cANKegPQV0B4A+UY9a87/wCFx+GNuA85x221E/xn8OAYVbk47baAO8kDebuB49KdGvzgkV5xJ8aNBB4tblvoAP60wfGvQx0s7rP4f40AepgY7Uqj5uteVH44aP8A9A+6/T/GgfHTSlHGmXJ/L/GgD1wcDg0oevIm+PGm4+XSrg/iv+NRN8d7LOBo9xn6r/jQB7LknoM07Bx0rxX/AIX1CPu6PL/30P8AGlPx9Tbj+xn/ABf/AOvQB7JN93rUdqf3ufevGJPjyCONGb8X/wDr1Cvx1mRsro4+hb/69AH0AeEzTYzkCvBH/aAv8YTSIx9XqMfHvVAwYaTFgdfmNAH0GTwfao92T0r58X4+ayN4bTLc5OR8xph+PWuknbp9qB9TQB9EZNKCPXmvm9vjp4kLbo7W1Vf8+1I3x18TN0gtR77R/hQB9Jgk9AaQntivmZ/jh4sdGUPAue4QcfpUP/C6vF6x7POh9m8sf4UAfT5UkZFNwa+Xm+MvjJ1/4+4x9Il/wqH/AIW54xfn7euT/wBM1/woA+puhz6VJF8zZFfKT/Ffxkeuobf+2Y/wpi/FfxkOBqR/CMf4UAfXABFOxXyK3xU8ZEZ/teQD/rmP8KjPxQ8YN11mb8AKAPr2jFfH5+JXi7vrU/4Yph+I3i0n/kN3H50AfYhOO1QTMDwDXx+/j7xW/wB7W7o/RzUDeNvExP8AyGbv/v6aAPsBBknkVKuB3H518cDxn4kJ51q8/CVv8aafF/iM/wDMbvf+/wA3+NAH2YXT+8o/EUnmxj/lon/fQr4xfxVrz/e1e/P0nb/GoW8Q6wSCdWvs+87f40AfavmRZ5ljH/AxUbyQYIM0Z/4GK+LW17V2P/IVvT/28P8A40w61q5/5id7/wCBDf40AfXl5HbxsZRJHj/fH+NU1ns5gSLiEOO3mL/jXyY+rak4w+pXrexmb/Govtt0G5vLgE9SJW/xoA+zNP1S3it9j3MGAf8AnoKmn1iwJ+W7gOev71f8a+ME1K/jVlF5cYP/AE0P+NRfa7knP2qcn3kagD7NbVtPXre26/WVf8ajOs6YAd2oWuP+uor42+0XB63Mzf8AAzSebKes0v03mgD65ur/AEWYEx6nabxz/rRWMviXSbacxy6lbjHffmvl3eRzvc/8CNNJLncSfzzQB9aReL9BIx/atv8A991IfGPh2P72sWo9t1fI+B9Pxox7An3oA+u/+E18NBcnWbXH+8aa3jnwwB/yGbb8z/hXyMAPTFBQUAfWR8f+Fo/vazb/AIZ/wpH+I3hNYy/9sQEemG/wr5OCgeg+lIRk4wPrigD6sPxL8I/9BmHpn7rf4U0/E7wiODq8Z/4C3+FfKu0N+HtSbVHagD6mf4o+D1/5iqf98N/hWPrnxJ8IXunywpqIZ8cAI3+FfOW0d80vC9O/tQB6tZ/ESwiPlGZvKXo20810Np8TvDUCgvcPu/3DXg4HGOn0pRg5yR+IoA+gpPi54aAwJpT9ENYmp/FXSbjMduZgnqV614wB6YowD1xmgD16z+I+hRn975w9wtbkPxc8MRKAVueP9kV4KRjpSc+1AH0G3xr8ORx/u4Lpz7KP8arj446GRk2t4D9F/wAa8F6cED8KTocYoA91k+NminpaXbfUL/jVZ/jPpBPFjc/kv+NeJnH+RRge9AHsr/GXTM8WNz+n+NKvxpsAONNuSPX5f8a8ZHPp+VLQB7E/xnsz0024/Nf8ab/wui1C4/s2f8x/jXj9GKAPXG+M9uT/AMg2f8x/jUsfxttYxj+yZifXcP8AGvHcZo4oA9kb44WzDA0iX/vof40w/G6HHGkSf99D/GvH/oKOaAPXj8cVzxpDf99f/Xpf+F6EdNI/8e/+vXj/AB6UcelAHrsnxznZTs0lM9izdKzf+FzanKkkdzp9tIrfdxwRXmnWjFAHbyfEm+ZyUtIlH1NR/wDCyNS/594a43FJ3oA7L/hZOqL0t4KcnxO1dD/qLf8AKuL60YoA7aX4m6zL1itx9BVY/ELVyxJSH8q5OjFAHUnx9qpOdkP5VPH8SNai+6sH/fI/wrj8UUAd3H8WNfj6Jbf98D/Cpx8YvEoHyi2A/wBwf4V57SjpQB6H/wALl8Ujo1sP+2Y/wpp+Mniw/wDLS2/79L/hXn+aKAPQE+Mni1DkTW//AH6X/CpE+NXjFDlbi3/78r/hXndKKAPQG+M3jJn3G/T6CJQP5VWu/ix4ovJEeWeJmXoTGuf5Vw5pwGaAOsuPiN4ku7ea2kniMc67WAjA4/KuVYbDk9/Sgbg68ilblgDQA09aKc64NRtQADBQ9sGvq34LyzyfD+2SbGEJ2Y9M18pD5uPTmvq/4LXAm+HVodpBVmXp1+Y0AdB4+/5EHXf+vKT/ANBr5q1YbvhFoT/3L+cf+Ox19LePRnwFrv8A15S/+gmvmnUOfg/pYD8JfzFhj1Cf4UAcR703vTuQuD2pp60ACffok/1hpYl3Son8W7BPrRKMO3+9igBlB6UHrRQA2ilxRigBKKXFGKAEopcUYoASilIpKACilxRigBKKXFGKAExQBnrTsUinDcUASLtZGwn3RUS4YpkHA6gVajkdAypt+YYORUEkbofmQc9s9aALyaZHdFWjuYow33VkyCD+FRX1kLMLE+Gl67xwMe1XNFtYbx5BPKqrCPMw5xlRyce9UL+6a8vJJSMg8Aeij0oAt2Z0wxqj2Ms8hXna5HP51Z1bTrJNJivraCS3bzDG0chJ3dOR+dWLMXkFvDPpcELgrlmbaWz9Gqtrcmq3FvFNqU8QIOFgXAx74HFAGTZwJcX0ERH+scL+dMmiEdzJGFyFYgVq6Npl617Z3giHkrIr53KOh9zUeo6XdxXFzcmPMSucsrrx+tAFaW3gi0iGZRunZyG9AMCqzwSxxK7RsFf7pPQ10OmR2Z0IPqGPsySlsKfmY8YFVm1Vb13trtAlq5/dqoA8v0OaAKt/Zw2hsn2HbLGHcZ68n/CrT6Tp7COZr9LeOUZVWVj/ACBqXxTHDHNp6RTJKqWgBKHgct1p2jm2vLeOO8dFFkxlG7jevXb+hoAo6xY2unCG2iZnnI3SSH7vPQUX+ktHqUVnags8iq35/wBKqajdPf389zJwznoPToK6bUtUXT7KKSLY2o3EXllhhtkWMZ9iefpQBk6lplpbaaZ4JTJtm8tm7E4Gf51mxWN3MoaO3d0PRlXrWqjIfBbqXXzfthbaTyRhazYdQvIECRXEqKOgDEAUAJ/Zt8DxaTf980v9m3+M/ZJf++af/a2o5yLyb/vs0v8Aa+pD/l8m/wC+zQBXktLqCLzJLeRB6leKsQWqto91c9XjdQD9TzUU+oXlzF5ctxKyf3SxxW14dSxawvjqM3l2ybXKA8uQchfx6UAQabo0TvAL4uslw22KMHnHr9KrWVilxJfqw/1MbsuP9nP+FW9N1I3fi22vZ3CL5ygA/dRRjFLpEsS3+rBpVUPBMAT0PDdKAOe52k/lWlrFmtleJGnRo1YfjSWGj3GpQs0Dwr5fB3uFz+db2uaFc3bJcQS27LFAA485cgj8eaAMPS7VLm3vQ8Zdkj3Jjseak0uxMk0z3sMixxQu/IK7iAcVe8KAM9+FliScRfui5ABbn1rasY9TOpwyaxqFn9iVgZgzK25P4gAPbNAHIab9lmvPKnHyTDap/untWpDoNlPqS2QvJGnz8ybcAAc/yrIv2gbUrl7TiAyHyvYdq6dNUs4dEn1W3ZBqk0X2Z0bqM8M4/wCAkflQBzGpPC1/MIECRodqge3etCGyhk8GXF8w/wBIS6EYPt8v+NZVrbSXVwsKFdz5wznGfxrroNIZPCl1pj3Vqtw1yJFBkBGML/hQByumJHJqlnFKMxvKgYdeN1aN49/a3c4hs/3COdrNbgjb9cVWhtv7P12BJZIyElQsynKnkd66XUhrF7NOqavbfZXHCeZgbfSgDjbiZ55DI4XPYIABUyaZeuoaOCRg3PFO1DTH06RYpJopdy7gY2zioxd3IUKtzMoHACuRQBN/ZOof8+rfmKP7I1D/AJ9H/Mf41D9ruv8An6n/AO/hpPtVz/z8zf8Afw0ALc2dxZhWuIWjVjgZI5rRurAz6XZT2duzFYysv+0ck5/LH5VlSTSPjzJHfHTcxNb93qi2uj6KbKYCeOJhMnYHc3X8MUAQajaw6VpENuXR7y5xJLjrEueFHvxn8aZEI7Twz9qWMNNPcGIMwztAAPFTan/ZuqWh1KCQQXq4E9u5J3f7Smo9PntLzR20q8mFvtk82OYgkA4HX8qAH6DGNUkutPuArZgkmV9uCrIpbr+FV7dQvhy8kVP3guFXf6Dir9lJYeHoLm4S/S8vZY2ijSINhQwwScgdjVbTDZ3GjXllcXsds7yKyuwYhuRnoKAGaA4ub82k0aOkqMpBHKnB5pdFt7e4uptNnVX3FvKOcHI6DPvj9au2Eel6Kst6+ox3VyqMkMcKtySMZbIHrXOx3Dw3IuVOJFfepHY5zQB0Gl2Wm3dxd+daNDDaRs8rvIeGAO1evcgD8ao6VYpqJvQwPyruBHAXJ7/SruuaxYT6XFBp4xLcsJrzI/iHAX6cA/jVTQL+3soNSW5bHn25ROO/OKAGro7Wtnc3N2cJGdkQz99j3H55rJHStey1VJLJdP1LL2nVH/ijb196y5FRJGWN96A8N6igBtFFFABRRRQAUUUUAFFFFABQehoooAYWIBI+lalnolzcwmXAVQu5SSBn86zcDGK0Li8v5rC2juSws0OIztwD+I60AMn00xquJkLEZKg9KqxoHkKOdmPSukGk6IFEltqEtwx2nZjGOBkVzrsI7lyBgKxHzd+aAFa3TGVkBPvUWCDgkE+1SPMkp5Tb9KjOO3SgAopB1paACiiigAooooAKKKKAENJTqTFAC0UUUAIaTGeM4pTjIya29N0m1Sxk1PVZdluuRDCPvzt6ew9TQBifw5bpnAOOtJ9c/jWl/bAuIjaSwxR2hb5NqDKf8C6mmahpU9gkUzHzLeYZjlTkH29qAKFFLgcfTrRigBKKXFGKAEo6UuM9M5/SpILaW6uVggjLyMcBR3oAjCmU5UHJ6Ko5b8qJAyMFdSCPUYrp7y5svDNt9ksTHcalKn7+42giH/ZXPf1P1rLtZbfVkaC7Ypduf3cxPBPoaAMsgg5zxRU93azWNw1vPGVkHqahIIY54x2oASilxRigBKKUikoAKXDFc4+XOMgdK1tF0CbVBNPJIILKDmWd+gHoPU0281eKJltrCFBZxHq6gtJ7k+9AGUTzyOnp0NFdGdDg1uwN9oufPRc3FmT8yn+8vqK55kePcroQynBB6igBtKOlJSjpQAtFFFABRk7l6YHr3pMt6de9a8en/YLBNSvgq7j+4gPJk9yOwoAynjIww4jPAcjim4HQnp39a3NM8QQmR7XVbWOTT7h8sqIA0RP8SkenpTde0I6TMlxbyLdabKf9HuF6Eeh9CPf0oAxxTgOM0zO1sYz7U8Hjg5FAABuO31ppVlyR24p4O0579qkV1MZDDk0ARIQHXIzzUkpVsEDGDQE2SIccHGKddLskYAc56UARM5c0lIv3fenUANCnH4GvqP4GXXn+AY4yP9VIyg/ia+YQcRn17V9J/AG5STwRPCBzFOd34kmgDt/HjD/hAtd/68pP/QTXzM8yv8II0OzeuoMNuecYWvp/xhazXvhDVrW3jMs0ts6Ig6kkV8qQ+DPGFsxik8P3U0KuW8tmGM/nQBy5BphGeO9dv/wjPiAgf8UW5P8A12P+NSDwz4gK4PgZsHv5p/xoA5HR1jl1uxEnMTTpv+mal12yNlq91CFIRJcAkV0T+FPFa6pb3kHhOSEQEMsasCCR9TWvfad4uv3aS68Eh3c5Lb8Z/WgDzMkZNJnJxXoS+GfEcZLf8IICG9Zf/r1IPD3iLr/wgaf9/P8A69AHnRODjIoyOcEcV6OfDfiRxn/hBov++h/jUbeEPFLgOPBkSj2Yf40Aed7h60Z/zmvSB4P8U7d3/CGwgH1K/wCNN/4QvxSWB/4RKEe2RQB5zkde3rQTg4x716WvgnxRuDf8IrBnoVJFZb/Crxe+9v7JKoTuHzDj260AcPncQBR04PX0r01fh14pa1X/AIp23BC4JyM/WmWnww8VQ2+3+xrdwxzuY8/SgDzbcOfQd6M84r01vhZ4rks2g/sy2Rt27f8AxfSnXPwn8T3TRD7BaRFBjK5G768UAeYEgH29aNw9a9bX4V+Iwqq2n6bkeob/AAqeP4SeIW5NjpY+u7/CgDx3cPUUgK54IzXtI+Euur1stI/8f/8AialX4Ra44z9k0ZQf+un/AMTQB4kGPqPzpGJbqc/U17cnwd1tXOYdGOfXzP8A4mpj8ItXXGbfRfyk/wDiaAPDNxyCT1684pOnK9u4r3c/CHVWA/c6L+Un/wATVG9+BmrXlwsv23TbcKANkayYPv8AdoA8YEki52Mw9gxFI7SP95yx/wBo8ivc734L6pdWqRC60qDZj95HG+5vr8tM074JahbXLzSX+nzZHCyxuR/6DQB4l5sm0KJX2dgHIAprSvsK7nKt/tH+Ve/S/Bm+uFQfadJjIOflhbn/AMcqjefAa8ublpP7XtItx+4kTAD/AMdoA8OPCkc/XP8ASk25OWGOPXNe72PwIvLOR3/tSyn3Lja8TED3+7VdPgDOjMTrVuS2eBE3H/jtAHiB64AIB4Iz1peN3IbbXtg/Z/uC3/Idj/79N/8AE1btPgLNBPvOswyAdmhJH6rQB4P/ABZ7Ub//AK5FfREfwRkQ5a/sz/27D/4mpF+CLbSv9oWwOc5FuP8A4mgD5yBHX8cZ70705619Gy/BBZoZI21G2XeMBltwCP8Ax2s+H9nyCEnOtls+sI/woA8C7ZHSkJx3Br6Cj+AEAjdf7ZJ3df3I4/StDTfgfa6esijUEl3cfvrZG/LIoA+bc+vFLxjgivpz/hTVsP8Al7tx/wBuaf8AxNSR/By0XBN3bk/9eaf/ABNAHy8TnOCKXq2AM8dBX1K3wismABnthjuLOP8A+JqtefBSwvbVoTfrGGOfMjtUVh+QoA+YeA2Dk85INKMZDZ5r6gt/grpdvarB9t3Y/je1RmP4kU4fBXRwctcnP/Xsn+FAHy5gZ56Hrilx8uAor6lHwZ0X/n4P/gLH/hTx8GtF/wCfg/8AgNH/AIUAfK7ZPGB6Y9KODkfrX1RF8G9FhckyFs+ttHUq/CHQwTk8/wDXulAHynjjAJ/wpAvQkZx7V9XD4RaFng/+S6U7/hUWh+v/AJLpQB8n+2Ce/wCNAQ87h16V9YL8JtEVvuj/AMB0qpqPwT8N6g6PKZlZRgGIBP0FAHy2AFHX86M+1fVQ+Dnh5Y1j2s20Y3GJSTSH4NeG2HMbn/gAFAHyv/P0o57jA9a+rR8HvDQi2eS3/fAzTbf4O+GYCxEDsW/vAGgD5TJ9OlIq5GcHOeSK+so/hH4ZjlD/AGEsQc8gY/KpLP4UeFbG6mnTTtzy9Q/zAD2B4FAHyUVz2II6Uu1j1XPpX2F/wrrwy3/MIi/79rS/8K58NZUjSIuP9haAPjvaw7MM+gpcHjg4x0Ir7Fk+H3htjn+yYfwjWo/+FdeGz/zCY/8AvhaAPj1VPoxAGAMYz9aNjY5U19hH4ceGv+gQh/4CKcvw68MAYOjR/iooA+Oyrd1z+FG1uMr+dfY4+HXhfIP9jQn6qKc3w88LNjGiQcf7AoA+NiCRjBz64pcEcYP5V9k/8IB4XU/8gK3/AO+BUg8DeGhwNBtce8a0AfGWG/un8qUKx6K35V9nr4L8OIcjQ7Qf9slNQ3HgLw1cqd2jW6H/AGEAP6UAfG219oIRjzjp0o2t/db8jXv/AIi+Det3GrTPol1ZQWL4ZI5UywP5Gsc/BPxmT/yEdM/74P8A8TQB4vhv7jflTtj4zsb8jXtC/BHxjnnUtMH/AGzP/wATUjfBLxgRg6ppmDwf3Z/+JoA8S2t/dP5UYI7H8q9qHwI8U/8AQV0z/v2f/iaD8B/E5/5iumf98H/4mgDxTB9DShXP8DflXs7fAPxKRhtV03n0Rv8A4mp0+BPiYEEaxpgAGOYz/wDE0AeIlWBxtP5Vr2OrrFapbXlu00UZ+QYzgZz0Nes/8KG8Sk/8hnTP+/R/+Jp4+A3iLvrWnfhEf/iaAPLZtdsNzyW2lFJSuFO0AD8q5/bI0rSMjNnJwRXuf/ChfEB/5jmnj/tkf/iaX/hQ3iEf8x2w/wC/Z/8AiaAPCirE/cYZ9FNARsfdb8q93/4UT4gyP+J7Yf8Afo//ABNJ/wAKG188nXbH/v0f/iaAPCQjZ+63/fJpMH+63/fJr3f/AIUNr4/5j1kPpGf/AImmN+z/AK2xy2vWn4If/iaAPC8N/cb8qXa391vyr3T/AIZ/1n/oP2v/AHyf/iaX/hn3Vz1161/74P8A8TQB4UVYfwt+VAVj/A35V7sP2ftWB/5D1t+CH/4mnD4A6t/0H7f/AL9n/CgDwfY+ceW//fJpfLk/55v/AN8mveV+AWqqf+Rgh/BD/hUn/Ch9U/6GKMf9s/8A61AHgXlyf883/wC+TR5Un/PN/wDvk178PgPqZ6+IkP8A2z/+tSn4C6iRj/hIU/79/wD1qAPANj/3H/75NIUcDJRhzjkYr34fAK/H/Mwp/wB+/wD61EvwAu51Cvr6nH/TIf4UAeN6ZpdqthLquqSiOCM7Yoc/NM3oMdBWNe30t7cNJIxCjhEHRF7AV7vJ+ztLKBv18sB0BT/61R/8M34+9rpx3IT/AOtQB4Fu5z/StjRtYWzD2t2nn2M42ujH7n+0vofpXs4/ZuQ/8x1v++P/AK1OH7Nseeddf8E/+tQB4tqunQWFwq2d2l3byDcjLnK+xz3qhskBwY3H4V74v7OMIC41+bI5+4MVZH7P56HxFMAPSJf8KAPnvY/9x/8Avk0eVKekUh+imvoZfgCR/wAzHP8A9+lqVPgN5ZyPElzn2jFAHzvBbTz3MUCRsHkbaAwwMn61u3lzbeGoHsLBlm1JhtnuhyEHdU/xr2eX9n+Kd1aXxDcsQc/cHFVz+zlpzOwOtXOScg7Bj6UAfO7SMzFjyx6mkzj165r6K/4Zv03/AKDlz/3wKB+zfpvfW7nH+4KAPHdM1Gz1myTRtSjAuN2LW8PVT2Vj1I/PtWTf6dc6ZfS2lwh86M4OOQR2Ir31P2c9IVgf7Yu8g5yEAxVpvgDp0km59e1Fz2LYJA9OtAHzeFcnARifTFJ0619IL+z3pCvvGtX2T14FRn9nTQySTq96Sf8AZFAHzkAXOFBJ9AM1paRo76neNE8yW0aJvkkl4wB/Wvfo/wBnfRYHEker3oYdwB/jSt+z3osmTJrF+/1wcj86APBdc1uK4hh07TkaHT7fjAPMrd2asHODX0r/AMM6aETzq15x0wgpR+zp4f76neE+u0UAfOVpez2M4ntZXhkU5BRiK6HV5NP1fSItXtiIdQD+XcwdnOPvivbh+zr4fHP9pXn5CnJ+z14eClft97z3H/66APm0Ru3RD+Ap3lSDjy3/AO+TX0on7Pnh5Pu6lf8A4Nj+tSj4C6Ep41PUP+/h/wAaAPmgQTt0gk/75NKba4x/qJP++TX01/wovRSMHVNR/CQj+tH/AAonRD11TUsf9dT/AI0AfP8Ap1la6fYjV9QIY5xbW3UyN3J9hzWFqF/PqF2087En+FQeFHoK+l5vgJ4bkRQbq+JB4zITj8zTF/Z68L4/4+bv/vr/AOvQB8xFhgALj1PrW74f18WDNY30fn6XP8ssRGdv+0voR6ivoH/hnrwv3uLs/wDAj/jTh+z54VH/AC2u/wDvs/40AfP2s6KlhfqtjOLy1kUSQyLkkr6H3H9Kzlgndj+5f/vmvpuL4FeF4lHlyX6leMLOw/rUifBDwsBkPqH/AIFSf/FUAfMP2S5LgfZ5P++TTXgcS7CpU+hGK+pf+FKeGR0k1EfS7k/+Kpp+B/hFm3vHdu/q1y5/rQB8xR4+0IHBwhANR30qvcyAAr8xr6fPwL8HZz9nusHkn7S/+NJ/wofwaTkwXBJ9Z3/xoA+W1IxjIpS1fUn/AAofwZkf6Pccf9N3/wAac3wM8FsAPstxx/03f/GgD5bzlRwelfRf7O8e3wnqLn+O54/I1t/8KO8FcD7Jcf8AgQ/+Ndb4a8LaZ4T07+z9KjaOEuXwzFjk+5oA3aQ/eFFFAFI/eWrPf8aKKAGHv9aG60UUAKfuimnoaKKAE/hoH+rNFFACDpRRRQA4UD77fSiigBy9fwp1FFAC0UUUABoFFFADqKKKACiiigBaKKKAAUtFFABRRRQAUUUUAFFFFAAaSiigApaKKACiiigBKWiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAEpaKKACiiigApKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAFLRRQAlKKKKACiiigBKKKKAAUtFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQB//2Q==\" />"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["© 2017 LARRY D. MOORE\n", "\n", "contemporary criticism of the less-than- thoughtful circumstances under which <PERSON> photographed <PERSON>, the picture’s power to engage has not diminished. Artists in other countries have appropriated the image, changing the mother’s features into those of other ethnicities, but keeping her expression and the positions of her clinging children. Long after anyone could help the <PERSON> family, this picture has resonance in another time of national crisis, unemployment and food shortages.\n", "\n", "A striking, but very different picture is a 1900 portrait of the legendary <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-yah- <PERSON>t-<PERSON><PERSON><PERSON> (Chief <PERSON>) of the Nez Percé people. The Bureau of American Ethnology in Washington, D.C., regularly arranged for its photographer, <PERSON>, to photograph Native American delegations that came to the capital to confer with officials about tribal needs and concerns. Although <PERSON> described Chief <PERSON> as having “an air of gentleness and quiet reserve,” the delegate skeptically appraises the photographer, which is not surprising given that the United States broke five treaties with Chief <PERSON> and his father between 1855 and 1885.\n", "\n", "More than a glance, second looks may reveal new knowledge into complex histories.\n", "\n", "<PERSON> is the photography curator emeritus of the Museum of Fine Arts, Houston and curator of the “Not an Ostrich” exhibition.\n", "\n", "28\n", "\n", "28 LIBRARY OF CONGRESS MAGAZINE\n", "\n", "LIBRARY OF CONGRESS MAGAZINE\n", "THEYRE WILLING TO <PERSON><PERSON><PERSON> MEENTERTAIN THEM DURING THE DAY,BUT AS SOON AS IT STARTSGETTING DARK, THEY ALLGO OFF, AND LEAVE ME!   \n", "ROSA PARKS: IN HER OWN WORDS\n", "\n", "COMIC ART: 120 YEARS OF PANELS AND PAGES\n", "\n", "SHALL NOT BE DENIED: WOMEN FIGHT FOR THE VOTE\n", "\n", "More information loc.gov/exhibits\n", "Nuestra Sefiora de las Iguanas\n", "\n", "<PERSON><PERSON>’s 1979 portrait of <PERSON><PERSON><PERSON> in the town of Juchitán in southeastern Mexico conveys the strength of women and reflects their important contributions to the economy. <PERSON>, a merchant, was selling iguanas to cook and eat, carrying them on her head, as is customary.\n", "\n", "GRACIELA ITURBIDE. “NUESTRA SEÑORA DE LAS IGUANAS.” 1979. GELATIN SILVER PRINT. © GRACIELA ITURBIDE, USED BY PERMISSION. PRINTS AND <PERSON>HOTO<PERSON><PERSON>HS DIVISION.\n", "\n", "<PERSON><PERSON><PERSON> requested permission to take a photograph, but this proved challenging because the iguanas were constantly moving, causing <PERSON> to laugh. The result, however, was a brilliant portrait that the inhabitants of Juchitán claimed with pride. They have reproduced it on posters and erected a statue honoring <PERSON> and her iguanas. The photo now appears throughout the world, inspiring supporters of feminism, women’s rights and gender equality.\n", "\n", "<PERSON><PERSON> is a curator in the Prints and Photographs Division.\n", "\n", "6\n", "\n", "6 LIBRARY OF CONGRESS MAGAZINE\n", "\n", "LIBRARY OF CONGRESS MAGAZINE\n", "\n", "‘Migrant Mother’ is <PERSON>\n", "\n", "The iconic portrait that became the face of the Great Depression is also the most famous photograph in the collections of the Library of Congress.\n", "\n", "The Library holds the original source of the photo — a nitrate negative measuring 4 by 5 inches. Do you see a faint thumb in the bottom right? The photographer, <PERSON>, found the thumb distracting and after a few years had the negative altered to make the thumb almost invisible. <PERSON>’s boss at the Farm Security Administration, <PERSON>, criticized her action because altering a negative undermines the credibility of a documentary photo.\n", "<PERSON><PERSON>\n", "\n", "The photos and evocative captions of <PERSON> served as source material for National Child Labor Committee reports and exhibits exposing abusive child labor practices in the United States in the first decades of the 20th century.\n", "\n", "<PERSON><PERSON>WIS WICKES HINE. “<PERSON><PERSON><PERSON>, THE YOUNG SHRIMP-<PERSON><PERSON><PERSON><PERSON>, FIVE YEARS OLD, AND A MOUNTAIN OF CHILD-<PERSON>BOR OYSTER SHELLS BEHIND HIM. HE WORKED LAST YEAR. UNDERSTANDS NOT A WORD OF ENGLISH. DUNBAR, <PERSON><PERSON><PERSON><PERSON>, DU<PERSON><PERSON> COMPANY. LOCATION: B<PERSON>OXI, MISSISSIPPI.” FEBRUARY 1911. NATIONAL CHILD LABOR COMMITTEE COLLECTION. PRINTS AND PHOTOGRAPHS DIVISION.\n", "\n", "For 15 years, <PERSON><PERSON>\n", "\n", "crisscrossed the country, documenting the practices of the worst offenders. His effective use of photography made him one of the committee's greatest publicists in the campaign for legislation to ban child labor.\n", "\n", "<PERSON><PERSON> was a master at taking photos that catch attention and convey a message and, in this photo, he framed <PERSON> in a setting that drove home the boy’s small size and unsafe environment.\n", "\n", "Captions on photos of other shrimp pickers emphasized their long working hours as well as one hazard of the job: The acid from the shrimp made pickers’ hands sore and “eats the shoes off your feet.”\n", "\n", "Such images alerted viewers to all that workers, their families and the nation sacrificed when children were part of the labor force. The Library holds paper records of the National Child Labor Committee as well as over 5,000 photographs.\n", "\n", "<PERSON><PERSON> is head of the Reference Section in the Prints and Photographs Division.\n", "\n", "8\n", "\n", "LIBRARY OF CONGRESS MAGAZINE\n", "\n", "LIBRARY OF CONGRESS MAGAZINE\n", "\n", "Intergenerational Portrait\n", "\n", "Raised on the Apsáalooke (Crow) reservation in Montana, photographer <PERSON> created her “Apsáalooke Feminist” self-portrait series with her daughter <PERSON>. With a dash of wry humor, mother and daughter are their own first-person narrators.\n", "\n", "<PERSON> Star explains the significance of their appearance: “The dress has power: You feel strong and regal wearing it. In my art, the elk tooth dress specifically symbolizes Crow womanhood and the matrilineal line connecting me to my ancestors. As a mother, I spend hours searching for the perfect elk tooth dress materials to make a prized dress for my daughter.”\n", "\n", "In a world that struggles with cultural identities, this photograph shows us the power and beauty of blending traditional and contemporary styles.\n", "‘American Gothic’ Product #216040262 Price: $24\n", "\n", "U.S. Capitol at Night Product #216040052 Price: $24\n", "\n", "Good Reading Ahead Product #21606142 Price: $24\n", "\n", "<PERSON> created an iconic image with this 1942 photograph of cleaning woman <PERSON>.\n", "\n", "Snow blankets the U.S. Capitol in this classic image by <PERSON>.\n", "\n", "Start your new year out right with a poster promising good reading for months to come.\n", "\n", "▪ Order online: loc.gov/shop ▪ Order by phone: ************\n", "\n", "26\n", "\n", "LIBRARY OF CONGRESS MAGAZINE\n", "\n", "LIBRARY OF CONGRESS MAGAZINE\n", "\n", "SUPPORT\n", "\n", "A PICTURE OF PHILANTHROPY Annenberg Foundation Gives $1 Million and a Photographic Collection to the Library.\n", "\n", "A major gift by <PERSON> and the Annenberg Foundation in Los Angeles will support the effort to reimagine the visitor experience at the Library of Congress. The foundation also is donating 1,000 photographic prints from its Annenberg Space for Photography exhibitions to the Library.\n", "\n", "The Library is pursuing a multiyear plan to transform the experience of its nearly 2 million annual visitors, share more of its treasures with the public and show how Library collections connect with visitors’ own creativity and research. The project is part of a strategic plan established by <PERSON><PERSON><PERSON> of Congress <PERSON> to make the Library more user-centered for Congress, creators and learners of all ages.\n", "\n", "A 2018 exhibition at the Annenberg Space for Photography in Los Angeles featured over 400 photographs from the Library. The Library is planning a future photography exhibition, based on the Annenberg-curated show, along with a documentary film on the Library and its history, produced by the Annenberg Space for Photography.\n", "\n", "“The nation’s library is honored to have the strong support of <PERSON> and the Annenberg Foundation as we enhance the experience for our visitors,<PERSON> <PERSON> said. “We know that visitors will find new connections to the Library through the incredible photography collections and countless other treasures held here to document our nation’s history and creativity.”\n", "\n", "To enhance the Library’s holdings, the foundation is giving the Library photographic prints for long-term preservation from 10 other exhibitions hosted at the Annenberg Space for Photography. The Library holds one of the world’s largest photography collections, with about 14 million photos and over 1 million images digitized and available online.\n", "18  LIBRARY OF CONGRESS MAGAZINE\n"]}], "source": ["from IPython.display import HTML, display\n", "\n", "\n", "def plt_img_base64(img_base64):\n", "    # Create an HTML img tag with the base64 string as the source\n", "    image_html = f'<img src=\"data:image/jpeg;base64,{img_base64}\" />'\n", "\n", "    # Display the image by rendering the HTML\n", "    display(HTML(image_html))\n", "\n", "\n", "query = \"Woman with children\"\n", "docs = retriever.invoke(query, k=10)\n", "\n", "for doc in docs:\n", "    if is_base64(doc.page_content):\n", "        plt_img_base64(doc.page_content)\n", "    else:\n", "        print(doc.page_content)"]}, {"cell_type": "markdown", "id": "15e9b54d", "metadata": {}, "source": ["Now let's use the `multi_modal_rag_chain` to process the same query and display the response."]}, {"cell_type": "code", "execution_count": 11, "id": "69fb15fd-76fc-49b4-806d-c4db2990027d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" The image is a black and white photograph by <PERSON> titled \"Destitute Pea Pickers in California. Mother of Seven Children. Age Thirty-Two. Nipomo, California.\" It was taken in March 1936 as part of the Farm Security Administration-Office of War Information Collection.\n", "\n", "The photograph features a woman with seven children, who appear to be in a state of poverty and hardship. The woman is seated, looking directly at the camera, while three of her children are standing behind her. They all seem to be dressed in ragged clothing, indicative of their impoverished condition.\n", "\n", "The historical context of this image is related to the Great Depression, which was a period of economic hardship in the United States that lasted from 1929 to 1939. During this time, many people struggled to make ends meet, and poverty was widespread. This photograph captures the plight of one such family during this difficult period.\n", "\n", "The symbolism of the image is multifaceted. The woman's direct gaze at the camera can be seen as a plea for help or an expression of desperation. The ragged clothing of the children serves as a stark reminder of the poverty and hardship experienced by many during this time.\n", "\n", "In terms of connections to the related text, it is mentioned that <PERSON>, the woman in the photograph, initially regretted having her picture taken. However, she later came to appreciate the importance of the image as a representation of the struggles faced by many during the Great Depression. The mention of <PERSON> suggests that she may have played a role in the creation or distribution of this photograph.\n", "\n", "Overall, this image is a powerful depiction of poverty and hardship during the Great Depression, capturing the resilience and struggles of one family amidst difficult times. \n"]}], "source": ["chain = multi_modal_rag_chain(retriever)\n", "response = chain.invoke(query)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 12, "id": "ec2ea7e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["vdms_rag_nb\n"]}], "source": ["! docker kill vdms_rag_nb"]}, {"cell_type": "markdown", "id": "fe4a98ee", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".test-venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}