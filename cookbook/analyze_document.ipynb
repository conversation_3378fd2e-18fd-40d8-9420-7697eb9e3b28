{"cells": [{"cell_type": "markdown", "id": "f69d4a4c-137d-47e9-bea1-786afce9c1c0", "metadata": {}, "source": ["# Analyze a single long document\n", "\n", "The AnalyzeDocumentChain takes in a single document, splits it up, and then runs it through a CombineDocumentsChain."]}, {"cell_type": "code", "execution_count": 3, "id": "2a0707ce-6d2d-471b-bc33-64da32a7b3f0", "metadata": {}, "outputs": [], "source": ["with open(\"../docs/docs/modules/state_of_the_union.txt\") as f:\n", "    state_of_the_union = f.read()"]}, {"cell_type": "code", "execution_count": 7, "id": "ca14d161-2d5b-4a6c-a296-77d8ce4b28cd", "metadata": {}, "outputs": [], "source": ["from langchain.chains import AnalyzeDocumentChain\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-3.5-turbo\", temperature=0)"]}, {"cell_type": "code", "execution_count": 8, "id": "9f97406c-85a9-45fb-99ce-9138c0ba3731", "metadata": {}, "outputs": [], "source": ["from langchain.chains.question_answering import load_qa_chain\n", "\n", "qa_chain = load_qa_chain(llm, chain_type=\"map_reduce\")"]}, {"cell_type": "code", "execution_count": 9, "id": "0871a753-f5bb-4b4f-a394-f87f2691f659", "metadata": {}, "outputs": [], "source": ["qa_document_chain = AnalyzeDocumentChain(combine_docs_chain=qa_chain)"]}, {"cell_type": "code", "execution_count": 10, "id": "e6f86428-3c2c-46a0-a57c-e22826fdbf91", "metadata": {}, "outputs": [{"data": {"text/plain": ["'The President said, \"Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service.\"'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["qa_document_chain.run(\n", "    input_document=state_of_the_union,\n", "    question=\"what did the president say about justice breyer?\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 5}