{"cells": [{"cell_type": "markdown", "id": "6db54519-b98e-47c2-8dc2-600f6140a3aa", "metadata": {}, "source": ["## Tool Use with Anthropic API for structured outputs\n", "\n", "Anthropic API recently added tool use.\n", "\n", "This is very useful for structured output."]}, {"cell_type": "code", "execution_count": null, "id": "8990ec23-8ae1-4580-b220-4b00c05637d2", "metadata": {}, "outputs": [], "source": ["! pip install -U langchain-anthropic"]}, {"cell_type": "code", "execution_count": null, "id": "6b966914-502b-499c-a4cf-e390106dd506", "metadata": {}, "outputs": [], "source": ["# Optional\n", "import os\n", "# os.environ['LANGSMITH_TRACING'] = 'true' # enables tracing\n", "# os.environ['LANGSMITH_API_KEY'] = <your-api-key>"]}, {"attachments": {"83c97bfe-b9b2-48ef-95cf-06faeebaa048.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "81897f2b-5936-4fa0-9445-3edb3af22da7", "metadata": {}, "source": ["`How can we use tools to produce structured output?`\n", "\n", "Function call / tool use just generates a payload.\n", "\n", "Payload often a JSON string, which can be pass to an API or, in this case, a parser to produce structured output.\n", "\n", "<PERSON><PERSON><PERSON><PERSON> has `llm.with_structured_output(schema)` to make it very easy to produce structured output that matches `schema`.\n", "\n", "![Screenshot 2024-04-03 at 10.16.57 PM.png](attachment:83c97bfe-b9b2-48ef-95cf-06faeebaa048.png)"]}, {"cell_type": "code", "execution_count": null, "id": "9caa2aaf-1918-4a8a-982d-f8052b92ed44", "metadata": {"scrolled": true}, "outputs": [], "source": ["from langchain_anthropic import ChatAnthropic\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.pydantic_v1 import BaseModel, Field\n", "\n", "\n", "# Data model\n", "class code(BaseModel):\n", "    \"\"\"Code output\"\"\"\n", "\n", "    prefix: str = Field(description=\"Description of the problem and approach\")\n", "    imports: str = Field(description=\"Code block import statements\")\n", "    code: str = Field(description=\"Code block not including import statements\")\n", "\n", "\n", "# LLM\n", "llm = ChatAnthropic(\n", "    model=\"claude-3-opus-20240229\",\n", "    default_headers={\"anthropic-beta\": \"tools-2024-04-04\"},\n", ")\n", "\n", "# Structured output, including raw will capture raw output and parser errors\n", "structured_llm = llm.with_structured_output(code, include_raw=True)\n", "code_output = structured_llm.invoke(\n", "    \"Write a python program that prints the string 'hello world' and tell me how it works in a sentence\"\n", ")"]}, {"cell_type": "code", "execution_count": 2, "id": "9025bfdc-6060-4042-9a61-4e361dda7087", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'text': \"<thinking>\\nThe tool 'code' is relevant for writing a Python program to print a string.\\n\\nTo use the 'code' tool, I need values for these required parameters:\\nprefix: A description of the problem and approach. I can provide this based on the request.\\nimports: The import statements needed for the code. For this simple program, no imports are needed, so I can leave this blank.\\ncode: The actual Python code, not including imports. I can write a simple print statement to output the string.\\n\\nI have all the required parameters, so I can proceed with calling the 'code' tool.\\n</thinking>\",\n", " 'type': 'text'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Initial reasoning stage\n", "code_output[\"raw\"].content[0]"]}, {"cell_type": "code", "execution_count": 3, "id": "2393d9b6-67a2-41ea-ac01-dc038b4800f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'text': None,\n", " 'type': 'tool_use',\n", " 'id': 'toolu_01UwZVQub6vL36wiBww6CU7a',\n", " 'name': 'code',\n", " 'input': {'prefix': \"To print the string 'hello world' in Python:\",\n", "  'imports': '',\n", "  'code': \"print('hello world')\"}}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Tool call\n", "code_output[\"raw\"].content[1]"]}, {"cell_type": "code", "execution_count": 4, "id": "f4f390ac-fbda-4173-892a-ffd12844228c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'prefix': \"To print the string 'hello world' in Python:\",\n", " 'imports': '',\n", " 'code': \"print('hello world')\"}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# JSON str\n", "code_output[\"raw\"].content[1][\"input\"]"]}, {"cell_type": "code", "execution_count": 5, "id": "ba77d0f8-f79b-4656-9023-085ffdaf35f5", "metadata": {}, "outputs": [], "source": ["# Error\n", "error = code_output[\"parsing_error\"]\n", "error"]}, {"cell_type": "code", "execution_count": 6, "id": "cd854451-68d7-43df-bcae-4f3c3565536a", "metadata": {}, "outputs": [], "source": ["# Result\n", "parsed_result = code_output[\"parsed\"]"]}, {"cell_type": "code", "execution_count": 7, "id": "47b3405f-0aea-460e-8603-f6092019fcd4", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"To print the string 'hello world' in Python:\""]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed_result.prefix"]}, {"cell_type": "code", "execution_count": 8, "id": "85b16b62-1b72-4b6e-81fa-b1d707b728fa", "metadata": {}, "outputs": [{"data": {"text/plain": ["''"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed_result.imports"]}, {"cell_type": "code", "execution_count": 9, "id": "23857441-3e67-460c-b6be-b57cf0dd17ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"print('hello world')\""]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed_result.code"]}, {"attachments": {"bb6c7126-7667-433f-ba50-56107b0341bd.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "74b6c1f0-db28-4b43-ac31-92636dea7b56", "metadata": {}, "source": ["## More challenging example\n", "\n", "Motivating example for tool use / structured outputs.\n", "\n", "![code-gen.png](attachment:bb6c7126-7667-433f-ba50-56107b0341bd.png)"]}, {"cell_type": "markdown", "id": "8f387528-6535-4bc0-a2a6-8480ccf35394", "metadata": {}, "source": ["Here are some docs that we want to answer code questions about."]}, {"cell_type": "code", "execution_count": 10, "id": "97dd1b8c-724a-436a-88b1-b38204fc81f5", "metadata": {}, "outputs": [], "source": ["from bs4 import BeautifulSoup as Soup\n", "from langchain_community.document_loaders.recursive_url_loader import RecursiveUrlLoader\n", "\n", "# LCEL docs\n", "url = \"https://python.langchain.com/docs/expression_language/\"\n", "loader = RecursiveUrlLoader(\n", "    url=url, max_depth=20, extractor=lambda x: Soup(x, \"html.parser\").text\n", ")\n", "docs = loader.load()\n", "\n", "# Sort the list based on the URLs and get the text\n", "d_sorted = sorted(docs, key=lambda x: x.metadata[\"source\"])\n", "d_reversed = list(reversed(d_sorted))\n", "concatenated_content = \"\\n\\n\\n --- \\n\\n\\n\".join(\n", "    [doc.page_content for doc in d_reversed]\n", ")"]}, {"cell_type": "markdown", "id": "5205cd42-8673-4699-9bb4-2cf90bfe098c", "metadata": {}, "source": ["Problem:\n", "\n", "`What if we want to enforce tool use?`\n", "\n", "We can use fallbacks.\n", "\n", "Let's select a code gen prompt that -- from some of my testing -- does not correctly invoke the tool.\n", "\n", "We can see if we can correct from this."]}, {"cell_type": "code", "execution_count": 12, "id": "94e77be5-dddb-4386-b523-6f1136150bbd", "metadata": {}, "outputs": [], "source": ["# This code gen prompt invokes tool use\n", "code_gen_prompt_working = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"<instructions> You are a coding assistant with expertise in LCEL, LangChain expression language. \\n \n", "    Here is the LCEL documentation:  \\n ------- \\n  {context} \\n ------- \\n Answer the user  question based on the \\n \n", "    above provided documentation. Ensure any code you provide can be executed with all required imports and variables \\n\n", "    defined. Structure your answer: 1) a prefix describing the code solution, 2) the imports, 3) the functioning code block. \\n\n", "    Invoke the code tool to structure the output correctly. </instructions> \\n Here is the user question:\"\"\",\n", "        ),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "# This code gen prompt does not invoke tool use\n", "code_gen_prompt_bad = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a coding assistant with expertise in LCEL, LangChain expression language. \\n \n", "    Here is a full set of LCEL documentation:  \\n ------- \\n  {context} \\n ------- \\n Answer the user \n", "    question based on the above provided documentation. Ensure any code you provide can be executed \\n \n", "    with all required imports and variables defined. Structure your answer with a description of the code solution. \\n\n", "    Then list the imports. And finally list the functioning code block. Here is the user question:\"\"\",\n", "        ),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "\n", "# Data model\n", "class code(BaseModel):\n", "    \"\"\"Code output\"\"\"\n", "\n", "    prefix: str = Field(description=\"Description of the problem and approach\")\n", "    imports: str = Field(description=\"Code block import statements\")\n", "    code: str = Field(description=\"Code block not including import statements\")\n", "    description = \"Schema for code solutions to questions about LCEL.\"\n", "\n", "\n", "# LLM\n", "llm = ChatAnthropic(\n", "    model=\"claude-3-opus-20240229\",\n", "    default_headers={\"anthropic-beta\": \"tools-2024-04-04\"},\n", ")\n", "\n", "# Structured output\n", "# Include raw will capture raw output and parser errors\n", "structured_llm = llm.with_structured_output(code, include_raw=True)\n", "\n", "\n", "# Check for errors\n", "def check_claude_output(tool_output):\n", "    \"\"\"Check for parse error or failure to call the tool\"\"\"\n", "\n", "    # Error with parsing\n", "    if tool_output[\"parsing_error\"]:\n", "        # Report back output and parsing errors\n", "        print(\"Parsing error!\")\n", "        raw_output = str(code_output[\"raw\"].content)\n", "        error = tool_output[\"parsing_error\"]\n", "        raise ValueError(\n", "            f\"Error parsing your output! Be sure to invoke the tool. Output: {raw_output}. \\n Parse error: {error}\"\n", "        )\n", "\n", "    # Too<PERSON> was not invoked\n", "    elif not tool_output[\"parsed\"]:\n", "        print(\"Failed to invoke tool!\")\n", "        raise ValueError(\n", "            \"You did not use the provided tool! Be sure to invoke the tool to structure the output.\"\n", "        )\n", "    return tool_output\n", "\n", "\n", "# Chain with output check\n", "code_chain = code_gen_prompt_bad | structured_llm | check_claude_output"]}, {"cell_type": "markdown", "id": "1b915baf-8b1d-43e8-b962-3e73b135dade", "metadata": {}, "source": ["Let's add a check and re-try."]}, {"cell_type": "code", "execution_count": 13, "id": "efae1ff7-4413-4c47-a403-1630dd453219", "metadata": {}, "outputs": [], "source": ["def insert_errors(inputs):\n", "    \"\"\"Insert errors in the messages\"\"\"\n", "\n", "    # Get errors\n", "    error = inputs[\"error\"]\n", "    messages = inputs[\"messages\"]\n", "    messages += [\n", "        (\n", "            \"user\",\n", "            f\"Retry. You are required to fix the parsing errors: {error} \\n\\n You must invoke the provided tool.\",\n", "        )\n", "    ]\n", "    return {\n", "        \"messages\": messages,\n", "        \"context\": inputs[\"context\"],\n", "    }\n", "\n", "\n", "# This will be run as a fallback chain\n", "fallback_chain = insert_errors | code_chain\n", "N = 3  # <PERSON> re-tries\n", "code_chain_re_try = code_chain.with_fallbacks(\n", "    fallbacks=[fallback_chain] * N, exception_key=\"error\"\n", ")"]}, {"cell_type": "code", "execution_count": 14, "id": "c7712c49-ee8c-4a61-927e-3c0beb83782b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Failed to invoke tool!\n"]}], "source": ["# Test\n", "messages = [(\"user\", \"How do I build a RAG chain in LCEL?\")]\n", "code_output_lcel = code_chain_re_try.invoke(\n", "    {\"context\": concatenated_content, \"messages\": messages}\n", ")"]}, {"cell_type": "code", "execution_count": 15, "id": "c8027a6f-6992-4bb4-9d6e-9d0778b04e28", "metadata": {}, "outputs": [], "source": ["parsed_result_lcel = code_output_lcel[\"parsed\"]"]}, {"cell_type": "code", "execution_count": 16, "id": "209186ac-3121-43a9-8358-86ace7e07f61", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"To build a RAG chain using LCEL, we'll use a vector store to retrieve relevant documents, a prompt template that incorporates the retrieved context, a chat model (like OpenAI) to generate a response based on the prompt, and an output parser to clean up the model output.\""]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed_result_lcel.prefix"]}, {"cell_type": "code", "execution_count": 17, "id": "b8d6d189-e5df-49b6-ada8-83f6c0b26886", "metadata": {}, "outputs": [{"data": {"text/plain": ["'from langchain_community.vectorstores import DocArrayInMemorySearch\\nfrom langchain_core.output_parsers import StrOutputParser\\nfrom langchain_core.prompts import ChatPromptTemplate\\nfrom langchain_core.runnables import RunnablePassthrough\\nfrom langchain_openai import ChatOpenAI, OpenAIEmbeddings'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed_result_lcel.imports"]}, {"cell_type": "code", "execution_count": 18, "id": "e3822253-d28b-4f7e-9364-79974d04eff1", "metadata": {}, "outputs": [{"data": {"text/plain": ["'vectorstore = DocArrayInMemorySearch.from_texts(\\n    [\"harrison worked at kensho\", \"bears like to eat honey\"], \\n    embedding=OpenAIEmbeddings(),\\n)\\n\\nretriever = vectorstore.as_retriever()\\n\\ntemplate = \"\"\"Answer the question based only on the following context:\\n{context}\\nQuestion: {question}\"\"\"\\nprompt = ChatPromptTemplate.from_template(template)\\n\\noutput_parser = StrOutputParser()\\n\\nrag_chain = (\\n    {\"context\": retriever, \"question\": RunnablePassthrough()} \\n    | prompt \\n    | ChatOpenAI()\\n    | output_parser\\n)\\n\\nprint(rag_chain.invoke(\"where did harri<PERSON> work?\"))'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed_result_lcel.code"]}, {"cell_type": "markdown", "id": "80d63a3d-bad8-4385-bd85-40ca95c260c6", "metadata": {}, "source": ["Example trace catching an error and correcting:\n", "\n", "https://smith.langchain.com/public/f06e62cb-2fac-46ae-80cd-0470b3155eae/r"]}, {"cell_type": "code", "execution_count": null, "id": "5f70e45c-eb68-4679-979c-0c04502affd1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}