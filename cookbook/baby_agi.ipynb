{"cells": [{"cell_type": "markdown", "id": "517a9fd4", "metadata": {}, "source": ["# BabyAGI User Guide\n", "\n", "This notebook demonstrates how to implement [BabyAGI](https://github.com/yoheinakajima/babyagi/tree/main) by [<PERSON><PERSON><PERSON>](https://twitter.com/yoheinaka<PERSON>). BabyAGI is an AI agent that can generate and pretend to execute tasks based on a given objective.\n", "\n", "This guide will help you understand the components to create your own recursive agents.\n", "\n", "Although BabyAGI uses specific vectorstores/model providers (Pinecone, OpenAI), one of the benefits of implementing it with LangChain is that you can easily swap those out for different options. In this implementation we use a FAISS vectorstore (because it runs locally and is free)."]}, {"cell_type": "markdown", "id": "556af556", "metadata": {}, "source": ["## Install and Import Required Modules"]}, {"cell_type": "code", "execution_count": 1, "id": "c8a354b6", "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "\n", "from langchain_experimental.autonomous_agents import BabyAGI\n", "from langchain_openai import OpenAI, OpenAIEmbeddings"]}, {"cell_type": "markdown", "id": "09f70772", "metadata": {}, "source": ["## Connect to the Vector Store\n", "\n", "Depending on what vectorstore you use, this step may look different."]}, {"cell_type": "code", "execution_count": 2, "id": "794045d4", "metadata": {}, "outputs": [], "source": ["from langchain.docstore import InMemoryDocstore\n", "from langchain_community.vectorstores import FAISS"]}, {"cell_type": "code", "execution_count": 3, "id": "6e0305eb", "metadata": {}, "outputs": [], "source": ["# Define your embedding model\n", "embeddings_model = OpenAIEmbeddings()\n", "# Initialize the vectorstore as empty\n", "import faiss\n", "\n", "embedding_size = 1536\n", "index = faiss.IndexFlatL2(embedding_size)\n", "vectorstore = FAISS(embeddings_model.embed_query, index, InMemoryDocstore({}), {})"]}, {"cell_type": "markdown", "id": "05ba762e", "metadata": {}, "source": ["### Run the BabyAGI\n", "\n", "Now it's time to create the BabyAGI controller and watch it try to accomplish your objective."]}, {"cell_type": "code", "execution_count": 4, "id": "3d220b69", "metadata": {}, "outputs": [], "source": ["OBJECTIVE = \"Write a weather report for SF today\""]}, {"cell_type": "code", "execution_count": 5, "id": "8a8e5543", "metadata": {}, "outputs": [], "source": ["llm = OpenAI(temperature=0)"]}, {"cell_type": "code", "execution_count": 6, "id": "3d69899b", "metadata": {}, "outputs": [], "source": ["# Logging of LLMChains\n", "verbose = False\n", "# If None, will keep on going forever\n", "max_iterations: Optional[int] = 3\n", "baby_agi = BabyAGI.from_llm(\n", "    llm=llm, vectorstore=vectorstore, verbose=verbose, max_iterations=max_iterations\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "f7957b51", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[95m\u001b[1m\n", "*****TASK LIST*****\n", "\u001b[0m\u001b[0m\n", "1: Make a todo list\n", "\u001b[92m\u001b[1m\n", "*****NEXT TASK*****\n", "\u001b[0m\u001b[0m\n", "1: Make a todo list\n", "\u001b[93m\u001b[1m\n", "*****TASK RESULT*****\n", "\u001b[0m\u001b[0m\n", "\n", "\n", "1. Check the weather forecast for San Francisco today\n", "2. Make note of the temperature, humidity, wind speed, and other relevant weather conditions\n", "3. Write a weather report summarizing the forecast\n", "4. Check for any weather alerts or warnings\n", "5. Share the report with the relevant stakeholders\n", "\u001b[95m\u001b[1m\n", "*****TASK LIST*****\n", "\u001b[0m\u001b[0m\n", "2: Check the current temperature in San Francisco\n", "3: Check the current humidity in San Francisco\n", "4: Check the current wind speed in San Francisco\n", "5: Check for any weather alerts or warnings in San Francisco\n", "6: Check the forecast for the next 24 hours in San Francisco\n", "7: Check the forecast for the next 48 hours in San Francisco\n", "8: Check the forecast for the next 72 hours in San Francisco\n", "9: Check the forecast for the next week in San Francisco\n", "10: Check the forecast for the next month in San Francisco\n", "11: Check the forecast for the next 3 months in San Francisco\n", "1: Write a weather report for SF today\n", "\u001b[92m\u001b[1m\n", "*****NEXT TASK*****\n", "\u001b[0m\u001b[0m\n", "2: Check the current temperature in San Francisco\n", "\u001b[93m\u001b[1m\n", "*****TASK RESULT*****\n", "\u001b[0m\u001b[0m\n", "\n", "\n", "I will check the current temperature in San Francisco. I will use an online weather service to get the most up-to-date information.\n", "\u001b[95m\u001b[1m\n", "*****TASK LIST*****\n", "\u001b[0m\u001b[0m\n", "3: Check the current UV index in San Francisco.\n", "4: Check the current air quality in San Francisco.\n", "5: Check the current precipitation levels in San Francisco.\n", "6: Check the current cloud cover in San Francisco.\n", "7: Check the current barometric pressure in San Francisco.\n", "8: Check the current dew point in San Francisco.\n", "9: Check the current wind direction in San Francisco.\n", "10: Check the current humidity levels in San Francisco.\n", "1: Check the current temperature in San Francisco to the average temperature for this time of year.\n", "2: Check the current visibility in San Francisco.\n", "11: Write a weather report for SF today.\n", "\u001b[92m\u001b[1m\n", "*****NEXT TASK*****\n", "\u001b[0m\u001b[0m\n", "3: Check the current UV index in San Francisco.\n", "\u001b[93m\u001b[1m\n", "*****TASK RESULT*****\n", "\u001b[0m\u001b[0m\n", "\n", "\n", "The current UV index in San Francisco is moderate. The UV index is expected to remain at moderate levels throughout the day. It is recommended to wear sunscreen and protective clothing when outdoors.\n", "\u001b[91m\u001b[1m\n", "*****TASK ENDING*****\n", "\u001b[0m\u001b[0m\n"]}, {"data": {"text/plain": ["{'objective': 'Write a weather report for SF today'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["baby_agi({\"objective\": OBJECTIVE})"]}, {"cell_type": "code", "execution_count": null, "id": "898a210b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}