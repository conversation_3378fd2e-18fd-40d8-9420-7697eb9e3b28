{"cells": [{"cell_type": "code", "execution_count": null, "id": "3058e9ca-07c3-4eef-b98c-bc2f2dbb9cc6", "metadata": {}, "outputs": [], "source": ["pip install -U langchain umap-learn scikit-learn langchain_community tiktoken langchain-openai langchainhub langchain-chroma langchain-anthropic"]}, {"attachments": {"72039e0c-e8c4-4b17-8780-04ad9fc584f3.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "ea54c848-0df6-474e-b266-218a2acf67d3", "metadata": {}, "source": ["# RAPTOR: Recursive Abstractive Processing for Tree-Organized Retrieval\n", "\n", "The [RAPTOR](https://arxiv.org/pdf/2401.18059.pdf) paper presents an interesting approaching for indexing and retrieval of documents:\n", "\n", "* The `leafs` are a set of starting documents\n", "* Leafs are embedded and clustered\n", "* Clusters are then summarized into higher level (more abstract) consolidations of information across similar documents\n", "\n", "This process is done recursivly, resulting in a \"tree\" going from raw docs (`leafs`) to more abstract summaries.\n", " \n", "We can applying this at varying scales; `leafs` can be:\n", "\n", "* Text chunks from a single doc (as shown in the paper)\n", "* Full docs (as we show below)\n", "\n", "With longer context LLMs, it's possible to perform this over full documents. \n", "\n", "![Screenshot 2024-03-04 at 12.45.25 PM.png](attachment:72039e0c-e8c4-4b17-8780-04ad9fc584f3.png)"]}, {"cell_type": "markdown", "id": "083dd961-b401-4fc6-867c-8f8950059b02", "metadata": {}, "source": ["### Docs\n", "\n", "Let's apply this to <PERSON><PERSON><PERSON><PERSON>'s LCEL documentation.\n", "\n", "In this case, each `doc` is a unique web page of the LCEL docs.\n", "\n", "The context varies from < 2k tokens on up to > 10k tokens."]}, {"cell_type": "code", "execution_count": 1, "id": "b17c1331-373f-491d-8b53-ccf634e68c8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<function matplotlib.pyplot.show(close=None, block=None)>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import tiktoken\n", "from bs4 import BeautifulSoup as Soup\n", "from langchain_community.document_loaders.recursive_url_loader import RecursiveUrlLoader\n", "\n", "\n", "def num_tokens_from_string(string: str, encoding_name: str) -> int:\n", "    \"\"\"Returns the number of tokens in a text string.\"\"\"\n", "    encoding = tiktoken.get_encoding(encoding_name)\n", "    num_tokens = len(encoding.encode(string))\n", "    return num_tokens\n", "\n", "\n", "# LCEL docs\n", "url = \"https://python.langchain.com/docs/expression_language/\"\n", "loader = RecursiveUrlLoader(\n", "    url=url, max_depth=20, extractor=lambda x: Soup(x, \"html.parser\").text\n", ")\n", "docs = loader.load()\n", "\n", "# LCEL w/ PydanticOutputParser (outside the primary LCEL docs)\n", "url = \"https://python.langchain.com/docs/modules/model_io/output_parsers/quick_start\"\n", "loader = RecursiveUrlLoader(\n", "    url=url, max_depth=1, extractor=lambda x: Soup(x, \"html.parser\").text\n", ")\n", "docs_pydantic = loader.load()\n", "\n", "# LCEL w/ Self Query (outside the primary LCEL docs)\n", "url = \"https://python.langchain.com/docs/modules/data_connection/retrievers/self_query/\"\n", "loader = RecursiveUrlLoader(\n", "    url=url, max_depth=1, extractor=lambda x: Soup(x, \"html.parser\").text\n", ")\n", "docs_sq = loader.load()\n", "\n", "# Doc texts\n", "docs.extend([*docs_pydantic, *docs_sq])\n", "docs_texts = [d.page_content for d in docs]\n", "\n", "# Calculate the number of tokens for each document\n", "counts = [num_tokens_from_string(d, \"cl100k_base\") for d in docs_texts]\n", "\n", "# Plotting the histogram of token counts\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(counts, bins=30, color=\"blue\", edgecolor=\"black\", alpha=0.7)\n", "plt.title(\"Histogram of Token Counts\")\n", "plt.xlabel(\"Token Count\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(axis=\"y\", alpha=0.75)\n", "\n", "# Display the histogram\n", "plt.show"]}, {"cell_type": "code", "execution_count": 75, "id": "70750603-ec82-4439-9b32-d22014b5ff2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Num tokens in all context: 68705\n"]}], "source": ["# Doc texts concat\n", "d_sorted = sorted(docs, key=lambda x: x.metadata[\"source\"])\n", "d_reversed = list(reversed(d_sorted))\n", "concatenated_content = \"\\n\\n\\n --- \\n\\n\\n\".join(\n", "    [doc.page_content for doc in d_reversed]\n", ")\n", "print(\n", "    \"Num tokens in all context: %s\"\n", "    % num_tokens_from_string(concatenated_content, \"cl100k_base\")\n", ")"]}, {"cell_type": "code", "execution_count": 155, "id": "25ca3cf2-0f6b-40f9-a2ff-285a8dcb33dc", "metadata": {}, "outputs": [], "source": ["# Doc texts split\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "chunk_size_tok = 2000\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=chunk_size_tok, chunk_overlap=0\n", ")\n", "texts_split = text_splitter.split_text(concatenated_content)"]}, {"cell_type": "markdown", "id": "797a5469-0942-45a5-adb6-f12e05d76798", "metadata": {}, "source": ["## Models\n", "\n", "We can test various models, including the new [Claude3](https://www.anthropic.com/news/claude-3-family) family.\n", "\n", "Be sure to set the relevant API keys:\n", "\n", "* `ANTHROPIC_API_KEY`\n", "* `OPENAI_API_KEY`"]}, {"cell_type": "code", "execution_count": 2, "id": "033e71d3-5dc8-42a3-a0b7-4df116048c14", "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "\n", "embd = OpenAIEmbeddings()\n", "\n", "# from langchain_openai import ChatOpenAI\n", "\n", "# model = ChatOpenAI(temperature=0, model=\"gpt-4-1106-preview\")\n", "\n", "from langchain_anthropic import ChatAnthropic\n", "\n", "model = ChatAnthropic(temperature=0, model=\"claude-3-opus-20240229\")"]}, {"cell_type": "markdown", "id": "5c63db01-cf95-4c17-ae5d-8dc7267ad58a", "metadata": {}, "source": ["### Tree Constrution\n", "\n", "The clustering approach in tree construction includes a few interesting ideas.\n", "\n", "**GMM (Gaussian Mixture Model)** \n", "\n", "- Model the distribution of data points across different clusters\n", "- Optimal number of clusters by evaluating the model's Bayesian Information Criterion (BIC)\n", "\n", "**UMAP (Uniform Manifold Approximation and Projection)** \n", "\n", "- Supports clustering\n", "- Reduces the dimensionality of high-dimensional data\n", "- UMAP helps to highlight the natural grouping of data points based on their similarities\n", "\n", "**Local and Global Clustering** \n", "\n", "- Used to analyze data at different scales\n", "- Both fine-grained and broader patterns within the data are captured effectively\n", "\n", "**Thresholding** \n", "\n", "- Apply in the context of GMM to determine cluster membership\n", "- Based on the probability distribution (assignment of data points to ≥ 1 cluster)\n", "---\n", "\n", "Code for GMM and thresholding is from <PERSON><PERSON><PERSON> et al, as noted in the below two sources:\n", " \n", "* [Origional repo](https://github.com/parthsarthi03/raptor/blob/master/raptor/cluster_tree_builder.py)\n", "* [Minor tweaks](https://github.com/run-llama/llama_index/blob/main/llama-index-packs/llama-index-packs-raptor/llama_index/packs/raptor/clustering.py)\n", "\n", "Full credit to both authors."]}, {"cell_type": "code", "execution_count": 3, "id": "a849980c-27d4-48e0-87a0-c2a5143cb8c0", "metadata": {}, "outputs": [], "source": ["from typing import Dict, List, Optional, Tuple\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import umap\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from sklearn.mixture import GaussianMixture\n", "\n", "RANDOM_SEED = 224  # Fixed seed for reproducibility\n", "\n", "### --- Code from citations referenced above (added comments and docstrings) --- ###\n", "\n", "\n", "def global_cluster_embeddings(\n", "    embeddings: np.n<PERSON><PERSON>,\n", "    dim: int,\n", "    n_neighbors: Optional[int] = None,\n", "    metric: str = \"cosine\",\n", ") -> np.ndarray:\n", "    \"\"\"\n", "    Perform global dimensionality reduction on the embeddings using UMAP.\n", "\n", "    Parameters:\n", "    - embeddings: The input embeddings as a numpy array.\n", "    - dim: The target dimensionality for the reduced space.\n", "    - n_neighbors: Optional; the number of neighbors to consider for each point.\n", "                   If not provided, it defaults to the square root of the number of embeddings.\n", "    - metric: The distance metric to use for UMAP.\n", "\n", "    Returns:\n", "    - A numpy array of the embeddings reduced to the specified dimensionality.\n", "    \"\"\"\n", "    if n_neighbors is None:\n", "        n_neighbors = int((len(embeddings) - 1) ** 0.5)\n", "    return umap.UMAP(\n", "        n_neighbors=n_neighbors, n_components=dim, metric=metric\n", "    ).fit_transform(embeddings)\n", "\n", "\n", "def local_cluster_embeddings(\n", "    embeddings: np.n<PERSON><PERSON>, dim: int, num_neighbors: int = 10, metric: str = \"cosine\"\n", ") -> np.ndarray:\n", "    \"\"\"\n", "    Perform local dimensionality reduction on the embeddings using UMAP, typically after global clustering.\n", "\n", "    Parameters:\n", "    - embeddings: The input embeddings as a numpy array.\n", "    - dim: The target dimensionality for the reduced space.\n", "    - num_neighbors: The number of neighbors to consider for each point.\n", "    - metric: The distance metric to use for UMAP.\n", "\n", "    Returns:\n", "    - A numpy array of the embeddings reduced to the specified dimensionality.\n", "    \"\"\"\n", "    return umap.UMAP(\n", "        n_neighbors=num_neighbors, n_components=dim, metric=metric\n", "    ).fit_transform(embeddings)\n", "\n", "\n", "def get_optimal_clusters(\n", "    embeddings: np.n<PERSON><PERSON>, max_clusters: int = 50, random_state: int = RANDOM_SEED\n", ") -> int:\n", "    \"\"\"\n", "    Determine the optimal number of clusters using the Bayesian Information Criterion (BIC) with a Gaussian Mixture Model.\n", "\n", "    Parameters:\n", "    - embeddings: The input embeddings as a numpy array.\n", "    - max_clusters: The maximum number of clusters to consider.\n", "    - random_state: Seed for reproducibility.\n", "\n", "    Returns:\n", "    - An integer representing the optimal number of clusters found.\n", "    \"\"\"\n", "    max_clusters = min(max_clusters, len(embeddings))\n", "    n_clusters = np.arange(1, max_clusters)\n", "    bics = []\n", "    for n in n_clusters:\n", "        gm = GaussianMixture(n_components=n, random_state=random_state)\n", "        gm.fit(embeddings)\n", "        bics.append(gm.bic(embeddings))\n", "    return n_clusters[np.argmin(bics)]\n", "\n", "\n", "def GMM_cluster(embeddings: np.n<PERSON>ray, threshold: float, random_state: int = 0):\n", "    \"\"\"\n", "    Cluster embeddings using a Gaussian Mixture Model (GMM) based on a probability threshold.\n", "\n", "    Parameters:\n", "    - embeddings: The input embeddings as a numpy array.\n", "    - threshold: The probability threshold for assigning an embedding to a cluster.\n", "    - random_state: Seed for reproducibility.\n", "\n", "    Returns:\n", "    - A tuple containing the cluster labels and the number of clusters determined.\n", "    \"\"\"\n", "    n_clusters = get_optimal_clusters(embeddings)\n", "    gm = GaussianMixture(n_components=n_clusters, random_state=random_state)\n", "    gm.fit(embeddings)\n", "    probs = gm.predict_proba(embeddings)\n", "    labels = [np.where(prob > threshold)[0] for prob in probs]\n", "    return labels, n_clusters\n", "\n", "\n", "def perform_clustering(\n", "    embeddings: np.n<PERSON><PERSON>,\n", "    dim: int,\n", "    threshold: float,\n", ") -> List[np.ndarray]:\n", "    \"\"\"\n", "    Perform clustering on the embeddings by first reducing their dimensionality globally, then clustering\n", "    using a Gaussian Mixture Model, and finally performing local clustering within each global cluster.\n", "\n", "    Parameters:\n", "    - embeddings: The input embeddings as a numpy array.\n", "    - dim: The target dimensionality for UMAP reduction.\n", "    - threshold: The probability threshold for assigning an embedding to a cluster in GMM.\n", "\n", "    Returns:\n", "    - A list of numpy arrays, where each array contains the cluster IDs for each embedding.\n", "    \"\"\"\n", "    if len(embeddings) <= dim + 1:\n", "        # Avoid clustering when there's insufficient data\n", "        return [np.array([0]) for _ in range(len(embeddings))]\n", "\n", "    # Global dimensionality reduction\n", "    reduced_embeddings_global = global_cluster_embeddings(embeddings, dim)\n", "    # Global clustering\n", "    global_clusters, n_global_clusters = GMM_cluster(\n", "        reduced_embeddings_global, threshold\n", "    )\n", "\n", "    all_local_clusters = [np.array([]) for _ in range(len(embeddings))]\n", "    total_clusters = 0\n", "\n", "    # Iterate through each global cluster to perform local clustering\n", "    for i in range(n_global_clusters):\n", "        # Extract embeddings belonging to the current global cluster\n", "        global_cluster_embeddings_ = embeddings[\n", "            np.array([i in gc for gc in global_clusters])\n", "        ]\n", "\n", "        if len(global_cluster_embeddings_) == 0:\n", "            continue\n", "        if len(global_cluster_embeddings_) <= dim + 1:\n", "            # Handle small clusters with direct assignment\n", "            local_clusters = [np.array([0]) for _ in global_cluster_embeddings_]\n", "            n_local_clusters = 1\n", "        else:\n", "            # Local dimensionality reduction and clustering\n", "            reduced_embeddings_local = local_cluster_embeddings(\n", "                global_cluster_embeddings_, dim\n", "            )\n", "            local_clusters, n_local_clusters = GMM_cluster(\n", "                reduced_embeddings_local, threshold\n", "            )\n", "\n", "        # Assign local cluster IDs, adjusting for total clusters already processed\n", "        for j in range(n_local_clusters):\n", "            local_cluster_embeddings_ = global_cluster_embeddings_[\n", "                np.array([j in lc for lc in local_clusters])\n", "            ]\n", "            indices = np.where(\n", "                (embeddings == local_cluster_embeddings_[:, None]).all(-1)\n", "            )[1]\n", "            for idx in indices:\n", "                all_local_clusters[idx] = np.append(\n", "                    all_local_clusters[idx], j + total_clusters\n", "                )\n", "\n", "        total_clusters += n_local_clusters\n", "\n", "    return all_local_clusters\n", "\n", "\n", "### --- Our code below --- ###\n", "\n", "\n", "def embed(texts):\n", "    \"\"\"\n", "    Generate embeddings for a list of text documents.\n", "\n", "    This function assumes the existence of an `embd` object with a method `embed_documents`\n", "    that takes a list of texts and returns their embeddings.\n", "\n", "    Parameters:\n", "    - texts: List[str], a list of text documents to be embedded.\n", "\n", "    Returns:\n", "    - numpy.ndarray: An array of embeddings for the given text documents.\n", "    \"\"\"\n", "    text_embeddings = embd.embed_documents(texts)\n", "    text_embeddings_np = np.array(text_embeddings)\n", "    return text_embeddings_np\n", "\n", "\n", "def embed_cluster_texts(texts):\n", "    \"\"\"\n", "    Embeds a list of texts and clusters them, returning a DataFrame with texts, their embeddings, and cluster labels.\n", "\n", "    This function combines embedding generation and clustering into a single step. It assumes the existence\n", "    of a previously defined `perform_clustering` function that performs clustering on the embeddings.\n", "\n", "    Parameters:\n", "    - texts: List[str], a list of text documents to be processed.\n", "\n", "    Returns:\n", "    - pandas.DataFrame: A DataFrame containing the original texts, their embeddings, and the assigned cluster labels.\n", "    \"\"\"\n", "    text_embeddings_np = embed(texts)  # Generate embeddings\n", "    cluster_labels = perform_clustering(\n", "        text_embeddings_np, 10, 0.1\n", "    )  # Perform clustering on the embeddings\n", "    df = pd.DataFrame()  # Initialize a DataFrame to store the results\n", "    df[\"text\"] = texts  # Store original texts\n", "    df[\"embd\"] = list(text_embeddings_np)  # Store embeddings as a list in the DataFrame\n", "    df[\"cluster\"] = cluster_labels  # Store cluster labels\n", "    return df\n", "\n", "\n", "def fmt_txt(df: pd.DataFrame) -> str:\n", "    \"\"\"\n", "    Formats the text documents in a DataFrame into a single string.\n", "\n", "    Parameters:\n", "    - df: DataFrame containing the 'text' column with text documents to format.\n", "\n", "    Returns:\n", "    - A single string where all text documents are joined by a specific delimiter.\n", "    \"\"\"\n", "    unique_txt = df[\"text\"].tolist()\n", "    return \"--- --- \\n --- --- \".join(unique_txt)\n", "\n", "\n", "def embed_cluster_summarize_texts(\n", "    texts: List[str], level: int\n", ") -> <PERSON><PERSON>[pd.DataFrame, pd.DataFrame]:\n", "    \"\"\"\n", "    Embeds, clusters, and summarizes a list of texts. This function first generates embeddings for the texts,\n", "    clusters them based on similarity, expands the cluster assignments for easier processing, and then summarizes\n", "    the content within each cluster.\n", "\n", "    Parameters:\n", "    - texts: A list of text documents to be processed.\n", "    - level: An integer parameter that could define the depth or detail of processing.\n", "\n", "    Returns:\n", "    - Tuple containing two DataFrames:\n", "      1. The first DataFrame (`df_clusters`) includes the original texts, their embeddings, and cluster assignments.\n", "      2. The second DataFrame (`df_summary`) contains summaries for each cluster, the specified level of detail,\n", "         and the cluster identifiers.\n", "    \"\"\"\n", "\n", "    # Embed and cluster the texts, resulting in a DataFrame with 'text', 'embd', and 'cluster' columns\n", "    df_clusters = embed_cluster_texts(texts)\n", "\n", "    # Prepare to expand the DataFrame for easier manipulation of clusters\n", "    expanded_list = []\n", "\n", "    # Expand DataFrame entries to document-cluster pairings for straightforward processing\n", "    for index, row in df_clusters.iterrows():\n", "        for cluster in row[\"cluster\"]:\n", "            expanded_list.append(\n", "                {\"text\": row[\"text\"], \"embd\": row[\"embd\"], \"cluster\": cluster}\n", "            )\n", "\n", "    # Create a new DataFrame from the expanded list\n", "    expanded_df = pd.DataFrame(expanded_list)\n", "\n", "    # Retrieve unique cluster identifiers for processing\n", "    all_clusters = expanded_df[\"cluster\"].unique()\n", "\n", "    print(f\"--Generated {len(all_clusters)} clusters--\")\n", "\n", "    # Summarization\n", "    template = \"\"\"Here is a sub-set of LangChain Expression Language doc. \n", "    \n", "    LangChain Expression Language provides a way to compose chain in LangChain.\n", "    \n", "    Give a detailed summary of the documentation provided.\n", "    \n", "    Documentation:\n", "    {context}\n", "    \"\"\"\n", "    prompt = ChatPromptTemplate.from_template(template)\n", "    chain = prompt | model | StrOutputParser()\n", "\n", "    # Format text within each cluster for summarization\n", "    summaries = []\n", "    for i in all_clusters:\n", "        df_cluster = expanded_df[expanded_df[\"cluster\"] == i]\n", "        formatted_txt = fmt_txt(df_cluster)\n", "        summaries.append(chain.invoke({\"context\": formatted_txt}))\n", "\n", "    # Create a DataFrame to store summaries with their corresponding cluster and level\n", "    df_summary = pd.DataFrame(\n", "        {\n", "            \"summaries\": summaries,\n", "            \"level\": [level] * len(summaries),\n", "            \"cluster\": list(all_clusters),\n", "        }\n", "    )\n", "\n", "    return df_clusters, df_summary\n", "\n", "\n", "def recursive_embed_cluster_summarize(\n", "    texts: List[str], level: int = 1, n_levels: int = 3\n", ") -> Dict[int, <PERSON><PERSON>[pd.DataFrame, pd.DataFrame]]:\n", "    \"\"\"\n", "    Recursively embeds, clusters, and summarizes texts up to a specified level or until\n", "    the number of unique clusters becomes 1, storing the results at each level.\n", "\n", "    Parameters:\n", "    - texts: List[str], texts to be processed.\n", "    - level: int, current recursion level (starts at 1).\n", "    - n_levels: int, maximum depth of recursion.\n", "\n", "    Returns:\n", "    - Dict[int, <PERSON><PERSON>[pd.DataFrame, pd.DataFrame]], a dictionary where keys are the recursion\n", "      levels and values are tuples containing the clusters DataFrame and summaries DataFrame at that level.\n", "    \"\"\"\n", "    results = {}  # Dictionary to store results at each level\n", "\n", "    # Perform embedding, clustering, and summarization for the current level\n", "    df_clusters, df_summary = embed_cluster_summarize_texts(texts, level)\n", "\n", "    # Store the results of the current level\n", "    results[level] = (df_clusters, df_summary)\n", "\n", "    # Determine if further recursion is possible and meaningful\n", "    unique_clusters = df_summary[\"cluster\"].nunique()\n", "    if level < n_levels and unique_clusters > 1:\n", "        # Use summaries as the input texts for the next level of recursion\n", "        new_texts = df_summary[\"summaries\"].tolist()\n", "        next_level_results = recursive_embed_cluster_summarize(\n", "            new_texts, level + 1, n_levels\n", "        )\n", "\n", "        # Merge the results from the next level into the current results dictionary\n", "        results.update(next_level_results)\n", "\n", "    return results"]}, {"cell_type": "code", "execution_count": 4, "id": "f0d8cd3e-cd49-484d-9617-1b9811cc08b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--Generated 7 clusters--\n", "--Generated 1 clusters--\n"]}], "source": ["# Build tree\n", "leaf_texts = docs_texts\n", "results = recursive_embed_cluster_summarize(leaf_texts, level=1, n_levels=3)"]}, {"cell_type": "markdown", "id": "e80d7098-5d16-4fa6-837c-968e5c9f118d", "metadata": {}, "source": ["The paper reports best performance from `collapsed tree retrieval`. \n", "\n", "This involves flattening the tree structure into a single layer and then applying a k-nearest neighbors (kNN) search across all nodes simultaneously. \n", "\n", "We do simply do this below."]}, {"cell_type": "code", "execution_count": 6, "id": "d28ba9e6-9124-41a8-b4fd-55a6ef4ac062", "metadata": {}, "outputs": [], "source": ["from langchain_chroma import Chroma\n", "\n", "# Initialize all_texts with leaf_texts\n", "all_texts = leaf_texts.copy()\n", "\n", "# Iterate through the results to extract summaries from each level and add them to all_texts\n", "for level in sorted(results.keys()):\n", "    # Extract summaries from the current level's DataFrame\n", "    summaries = results[level][1][\"summaries\"].tolist()\n", "    # Extend all_texts with the summaries from the current level\n", "    all_texts.extend(summaries)\n", "\n", "# Now, use all_texts to build the vectorstore with Chroma\n", "vectorstore = Chroma.from_texts(texts=all_texts, embedding=embd)\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "0d497627-44c6-41f7-bb63-1d858d3f188f", "metadata": {}, "source": ["Now we can using our flattened, indexed tree in a RAG chain."]}, {"cell_type": "code", "execution_count": 7, "id": "9d6c894b-b3a3-4a01-b779-3e98ea382ff5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Here is a code example of how to define a RAG (Retrieval Augmented Generation) chain in LangChain:\\n\\n```python\\nfrom langchain.vectorstores import FAISS\\nfrom langchain.embeddings import OpenAIEmbeddings\\nfrom langchain.prompts import ChatPromptTemplate\\nfrom langchain.chat_models import ChatOpenAI\\nfrom langchain.output_parsers import StrOutputParser\\n\\n# Load documents into vector store\\nvectorstore = FAISS.from_texts(\\n    [\"harrison worked at kensho\"], embedding=OpenAIEmbeddings()\\n)\\nretriever = vectorstore.as_retriever()\\n\\n# Define prompt template\\ntemplate = \"\"\"Answer the question based only on the following context:\\n{context}\\nQuestion: {question}\"\"\"\\nprompt = ChatPromptTemplate.from_template(template)\\n\\n# Define model and output parser\\nmodel = ChatOpenAI()\\noutput_parser = StrOutputParser()\\n\\n# Define RAG chain\\nchain = (\\n    {\"context\": retriever, \"question\": RunnablePassthrough()}\\n    | prompt\\n    | model '"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain import hub\n", "from langchain_core.runnables import RunnablePassthrough\n", "\n", "# Prompt\n", "prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "\n", "# Post-processing\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "\n", "# Chain\n", "rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | model\n", "    | StrOutputParser()\n", ")\n", "\n", "# Question\n", "rag_chain.invoke(\"How to define a RAG chain? Give me a specific code example.\")"]}, {"cell_type": "markdown", "id": "0c585b37-ad83-4069-8f5d-4a6a3e15128d", "metadata": {}, "source": ["Trace: \n", "\n", "https://smith.langchain.com/public/1dabf475-1675-4494-b16c-928fbf079851/r"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}