{"cells": [{"cell_type": "markdown", "id": "517a9fd4", "metadata": {}, "source": ["# BabyAGI with <PERSON><PERSON>\n", "\n", "This notebook builds on top of [baby agi](baby_agi.html), but shows how you can swap out the execution chain. The previous execution chain was just an LLM which made stuff up. By swapping it out with an agent that has access to tools, we can hopefully get real reliable information"]}, {"cell_type": "markdown", "id": "556af556", "metadata": {}, "source": ["## Install and Import Required Modules"]}, {"cell_type": "code", "execution_count": 1, "id": "c8a354b6", "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "\n", "from langchain.chains import LLMChain\n", "from langchain.prompts import PromptTemplate\n", "from langchain_experimental.autonomous_agents import BabyAGI\n", "from langchain_openai import OpenAI, OpenAIEmbeddings"]}, {"cell_type": "markdown", "id": "09f70772", "metadata": {}, "source": ["## Connect to the Vector Store\n", "\n", "Depending on what vectorstore you use, this step may look different."]}, {"cell_type": "code", "execution_count": 2, "id": "794045d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install faiss-cpu > /dev/null\n", "%pip install google-search-results > /dev/null\n", "from langchain.docstore import InMemoryDocstore\n", "from langchain_community.vectorstores import FAISS"]}, {"cell_type": "code", "execution_count": 3, "id": "6e0305eb", "metadata": {}, "outputs": [], "source": ["# Define your embedding model\n", "embeddings_model = OpenAIEmbeddings()\n", "# Initialize the vectorstore as empty\n", "import faiss\n", "\n", "embedding_size = 1536\n", "index = faiss.IndexFlatL2(embedding_size)\n", "vectorstore = FAISS(embeddings_model.embed_query, index, InMemoryDocstore({}), {})"]}, {"cell_type": "markdown", "id": "0f3b72bf", "metadata": {}, "source": ["## Define the Chains\n", "\n", "BabyAGI relies on three LLM chains:\n", "- Task creation chain to select new tasks to add to the list\n", "- Task prioritization chain to re-prioritize tasks\n", "- Execution Chain to execute the tasks\n", "\n", "\n", "NOTE: in this notebook, the Execution chain will now be an agent."]}, {"cell_type": "code", "execution_count": 4, "id": "b43cd580", "metadata": {}, "outputs": [], "source": ["from langchain.agents import AgentExecutor, Tool, ZeroShotAgent\n", "from langchain.chains import LLMChain\n", "from langchain_community.utilities import SerpAPIWrapper\n", "from langchain_openai import OpenAI\n", "\n", "todo_prompt = PromptTemplate.from_template(\n", "    \"You are a planner who is an expert at coming up with a todo list for a given objective. Come up with a todo list for this objective: {objective}\"\n", ")\n", "todo_chain = LLMChain(llm=OpenAI(temperature=0), prompt=todo_prompt)\n", "search = SerpAPIWrapper()\n", "tools = [\n", "    Tool(\n", "        name=\"Search\",\n", "        func=search.run,\n", "        description=\"useful for when you need to answer questions about current events\",\n", "    ),\n", "    Tool(\n", "        name=\"TOD<PERSON>\",\n", "        func=todo_chain.run,\n", "        description=\"useful for when you need to come up with todo lists. Input: an objective to create a todo list for. Output: a todo list for that objective. Please be very clear what the objective is!\",\n", "    ),\n", "]\n", "\n", "\n", "prefix = \"\"\"You are an AI who performs one task based on the following objective: {objective}. Take into account these previously completed tasks: {context}.\"\"\"\n", "suffix = \"\"\"Question: {task}\n", "{agent_scratchpad}\"\"\"\n", "prompt = ZeroShotAgent.create_prompt(\n", "    tools,\n", "    prefix=prefix,\n", "    suffix=suffix,\n", "    input_variables=[\"objective\", \"task\", \"context\", \"agent_scratchpad\"],\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "4b00ae2e", "metadata": {}, "outputs": [], "source": ["llm = OpenAI(temperature=0)\n", "llm_chain = LLMChain(llm=llm, prompt=prompt)\n", "tool_names = [tool.name for tool in tools]\n", "agent = ZeroShotAgent(llm_chain=llm_chain, allowed_tools=tool_names)\n", "agent_executor = AgentExecutor.from_agent_and_tools(\n", "    agent=agent, tools=tools, verbose=True\n", ")"]}, {"cell_type": "markdown", "id": "05ba762e", "metadata": {}, "source": ["### Run the BabyAGI\n", "\n", "Now it's time to create the BabyAGI controller and watch it try to accomplish your objective."]}, {"cell_type": "code", "execution_count": 6, "id": "3d220b69", "metadata": {}, "outputs": [], "source": ["OBJECTIVE = \"Write a weather report for SF today\""]}, {"cell_type": "code", "execution_count": 7, "id": "3d69899b", "metadata": {}, "outputs": [], "source": ["# Logging of LLMChains\n", "verbose = False\n", "# If None, will keep on going forever\n", "max_iterations: Optional[int] = 3\n", "baby_agi = BabyAGI.from_llm(\n", "    llm=llm,\n", "    vectorstore=vectorstore,\n", "    task_execution_chain=agent_executor,\n", "    verbose=verbose,\n", "    max_iterations=max_iterations,\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "f7957b51", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[95m\u001b[1m\n", "*****TASK LIST*****\n", "\u001b[0m\u001b[0m\n", "1: Make a todo list\n", "\u001b[92m\u001b[1m\n", "*****NEXT TASK*****\n", "\u001b[0m\u001b[0m\n", "1: Make a todo list\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: I need to come up with a todo list\n", "Action: TODO\n", "Action Input: Write a weather report for SF today\u001b[0m\u001b[33;1m\u001b[1;3m\n", "\n", "1. Research current weather conditions in San Francisco\n", "2. Gather data on temperature, humidity, wind speed, and other relevant weather conditions\n", "3. Analyze data to determine current weather trends\n", "4. Write a brief introduction to the weather report\n", "5. Describe current weather conditions in San Francisco\n", "6. Discuss any upcoming weather changes\n", "7. Summarize the weather report\n", "8. Proofread and edit the report\n", "9. Submit the report\u001b[0m\u001b[32;1m\u001b[1;3m I now know the final answer\n", "Final Answer: The todo list for writing a weather report for SF today is: 1. Research current weather conditions in San Francisco; 2. Gather data on temperature, humidity, wind speed, and other relevant weather conditions; 3. Analyze data to determine current weather trends; 4. Write a brief introduction to the weather report; 5. Describe current weather conditions in San Francisco; 6. Discuss any upcoming weather changes; 7. Summarize the weather report; 8. Proofread and edit the report; 9. Submit the report.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\u001b[93m\u001b[1m\n", "*****TASK RESULT*****\n", "\u001b[0m\u001b[0m\n", "The todo list for writing a weather report for SF today is: 1. Research current weather conditions in San Francisco; 2. Gather data on temperature, humidity, wind speed, and other relevant weather conditions; 3. Analyze data to determine current weather trends; 4. Write a brief introduction to the weather report; 5. Describe current weather conditions in San Francisco; 6. Discuss any upcoming weather changes; 7. Summarize the weather report; 8. Proofread and edit the report; 9. Submit the report.\n", "\u001b[95m\u001b[1m\n", "*****TASK LIST*****\n", "\u001b[0m\u001b[0m\n", "2: Gather data on precipitation, cloud cover, and other relevant weather conditions;\n", "3: Analyze data to determine any upcoming weather changes;\n", "4: Research current weather forecasts for San Francisco;\n", "5: Create a visual representation of the weather report;\n", "6: Include relevant images and graphics in the report;\n", "7: Format the report for readability;\n", "8: Publish the report online;\n", "9: Monitor the report for accuracy.\n", "\u001b[92m\u001b[1m\n", "*****NEXT TASK*****\n", "\u001b[0m\u001b[0m\n", "2: Gather data on precipitation, cloud cover, and other relevant weather conditions;\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: I need to search for current weather conditions in San Francisco\n", "Action: Search\n", "Action Input: Current weather conditions in San Francisco\u001b[0m\u001b[36;1m\u001b[1;3mCurrent Weather for Popular Cities ; San Francisco, CA 46 · Partly Cloudy ; Manhattan, NY warning 52 · Cloudy ; Schiller Park, IL (60176) 40 · Sunny ; Boston, MA 54 ...\u001b[0m\u001b[32;1m\u001b[1;3m I need to compile the data into a weather report\n", "Action: TODO\n", "Action Input: Compile data into a weather report\u001b[0m\u001b[33;1m\u001b[1;3m\n", "\n", "1. Gather data from reliable sources such as the National Weather Service, local weather stations, and other meteorological organizations.\n", "\n", "2. Analyze the data to identify trends and patterns.\n", "\n", "3. Create a chart or graph to visualize the data.\n", "\n", "4. Write a summary of the data and its implications.\n", "\n", "5. Compile the data into a report format.\n", "\n", "6. Proofread the report for accuracy and clarity.\n", "\n", "7. Publish the report to a website or other platform.\n", "\n", "8. Distribute the report to relevant stakeholders.\u001b[0m\u001b[32;1m\u001b[1;3m I now know the final answer\n", "Final Answer: Today in San Francisco, the temperature is 46 degrees Fahrenheit with partly cloudy skies. The forecast for the rest of the day is expected to remain partly cloudy.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\u001b[93m\u001b[1m\n", "*****TASK RESULT*****\n", "\u001b[0m\u001b[0m\n", "Today in San Francisco, the temperature is 46 degrees Fahrenheit with partly cloudy skies. The forecast for the rest of the day is expected to remain partly cloudy.\n", "\u001b[95m\u001b[1m\n", "*****TASK LIST*****\n", "\u001b[0m\u001b[0m\n", "3: Format the report for readability;\n", "4: Include relevant images and graphics in the report;\n", "5: Compare the current weather conditions in San Francisco to the forecasted conditions;\n", "6: Identify any potential weather-related hazards in the area;\n", "7: Research historical weather patterns in San Francisco;\n", "8: Identify any potential trends in the weather data;\n", "9: Include relevant data sources in the report;\n", "10: Summarize the weather report in a concise manner;\n", "11: Include a summary of the forecasted weather conditions;\n", "12: Include a summary of the current weather conditions;\n", "13: Include a summary of the historical weather patterns;\n", "14: Include a summary of the potential weather-related hazards;\n", "15: Include a summary of the potential trends in the weather data;\n", "16: Include a summary of the data sources used in the report;\n", "17: Analyze data to determine any upcoming weather changes;\n", "18: Research current weather forecasts for San Francisco;\n", "19: Create a visual representation of the weather report;\n", "20: Publish the report online;\n", "21: Monitor the report for accuracy\n", "\u001b[92m\u001b[1m\n", "*****NEXT TASK*****\n", "\u001b[0m\u001b[0m\n", "3: Format the report for readability;\n", "\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3mThought: I need to make sure the report is easy to read;\n", "Action: TODO\n", "Action Input: Make the report easy to read\u001b[0m\u001b[33;1m\u001b[1;3m\n", "\n", "1. Break up the report into sections with clear headings\n", "2. Use bullet points and numbered lists to organize information\n", "3. Use short, concise sentences\n", "4. Use simple language and avoid jargon\n", "5. Include visuals such as charts, graphs, and diagrams to illustrate points\n", "6. Use bold and italicized text to emphasize key points\n", "7. Include a table of contents and page numbers\n", "8. Use a consistent font and font size throughout the report\n", "9. Include a summary at the end of the report\n", "10. Proofread the report for typos and errors\u001b[0m\u001b[32;1m\u001b[1;3m I now know the final answer\n", "Final Answer: The report should be formatted for readability by breaking it up into sections with clear headings, using bullet points and numbered lists to organize information, using short, concise sentences, using simple language and avoiding jargon, including visuals such as charts, graphs, and diagrams to illustrate points, using bold and italicized text to emphasize key points, including a table of contents and page numbers, using a consistent font and font size throughout the report, including a summary at the end of the report, and proofreading the report for typos and errors.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\u001b[93m\u001b[1m\n", "*****TASK RESULT*****\n", "\u001b[0m\u001b[0m\n", "The report should be formatted for readability by breaking it up into sections with clear headings, using bullet points and numbered lists to organize information, using short, concise sentences, using simple language and avoiding jargon, including visuals such as charts, graphs, and diagrams to illustrate points, using bold and italicized text to emphasize key points, including a table of contents and page numbers, using a consistent font and font size throughout the report, including a summary at the end of the report, and proofreading the report for typos and errors.\n", "\u001b[91m\u001b[1m\n", "*****TASK ENDING*****\n", "\u001b[0m\u001b[0m\n"]}, {"data": {"text/plain": ["{'objective': 'Write a weather report for SF today'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["baby_agi({\"objective\": OBJECTIVE})"]}, {"cell_type": "code", "execution_count": null, "id": "898a210b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 5}