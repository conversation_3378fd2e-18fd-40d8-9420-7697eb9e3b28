{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Human input LLM\n", "\n", "Similar to the fake LLM, <PERSON><PERSON><PERSON><PERSON> provides a pseudo LLM class that can be used for testing, debugging, or educational purposes. This allows you to mock out calls to the LLM and simulate how a human would respond if they received the prompts.\n", "\n", "In this notebook, we go over how to use this.\n", "\n", "We start this with using the HumanInputLLM in an agent."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_community.llms.human import HumanInputLLM"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain.agents import AgentType, initialize_agent, load_tools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Since we will use the `WikipediaQueryRun` tool in this notebook, you might need to install the `wikipedia` package if you haven't done so already."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install wikipedia"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["tools = load_tools([\"wikipedia\"])\n", "llm = HumanInputLLM(\n", "    prompt_func=lambda prompt: print(\n", "        f\"\\n===PROMPT====\\n{prompt}\\n=====END OF PROMPT======\"\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["agent = initialize_agent(\n", "    tools, llm, agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION, verbose=True\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\n", "===PROMPT====\n", "Answer the following questions as best you can. You have access to the following tools:\n", "\n", "Wikipedia: A wrapper around Wikipedia. Useful for when you need to answer general questions about people, places, companies, historical events, or other subjects. Input should be a search query.\n", "\n", "Use the following format:\n", "\n", "Question: the input question you must answer\n", "Thought: you should always think about what to do\n", "Action: the action to take, should be one of [Wikipedia]\n", "Action Input: the input to the action\n", "Observation: the result of the action\n", "... (this Thought/Action/Action Input/Observation can repeat N times)\n", "Thought: I now know the final answer\n", "Final Answer: the final answer to the original input question\n", "\n", "Begin!\n", "\n", "Question: What is '<PERSON><PERSON> the Rock!'?\n", "Thought:\n", "=====END OF PROMPT======\n", "\u001b[32;1m\u001b[1;3mI need to use a tool.\n", "Action: Wikipedia\n", "Action Input: Bocchi the Rock!, Japanese four-panel manga and anime series.\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mPage: Bocchi the Rock!\n", "Summary: <PERSON><PERSON> the Rock! (ぼっち・ざ・ろっく!, Bocchi Za Rokku!) is a Japanese four-panel manga series written and illustrated by <PERSON><PERSON>. It has been serialized in Ho<PERSON><PERSON><PERSON>'s seinen manga magazine Manga Time Kirara Max since December 2017. Its chapters have been collected in five tankōbon volumes as of November 2022.\n", "An anime television series adaptation produced by CloverWorks aired from October to December 2022. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\n", "\n", "Page: Manga <PERSON>\n", "Summary: Manga Time Kirara (まんがタイムきらら, Manga Taimu Kirara) is a Japanese seinen manga magazine published by Houbunsha which mainly serializes four-panel manga. The magazine is sold on the ninth of each month and was first published as a special edition of Manga Time, another Houbunsha magazine, on May 17, 2002. Characters from this magazine have appeared in a crossover role-playing game called Kirara Fantasia.\n", "\n", "Page: Manga Time Kirara Max\n", "Summary: Manga Time Kirara Max (まんがタイムきららMAX) is a Japanese four-panel seinen manga magazine published by Houbunsha. It is the third magazine of the \"Kirara\" series, after \"Manga Time Kirara\" and \"Manga Time Kirara Carat\". The first issue was released on September 29, 2004. Currently the magazine is released on the 19th of each month.\u001b[0m\n", "Thought:\n", "===PROMPT====\n", "Answer the following questions as best you can. You have access to the following tools:\n", "\n", "Wikipedia: A wrapper around Wikipedia. Useful for when you need to answer general questions about people, places, companies, historical events, or other subjects. Input should be a search query.\n", "\n", "Use the following format:\n", "\n", "Question: the input question you must answer\n", "Thought: you should always think about what to do\n", "Action: the action to take, should be one of [Wikipedia]\n", "Action Input: the input to the action\n", "Observation: the result of the action\n", "... (this Thought/Action/Action Input/Observation can repeat N times)\n", "Thought: I now know the final answer\n", "Final Answer: the final answer to the original input question\n", "\n", "Begin!\n", "\n", "Question: What is '<PERSON><PERSON> the Rock!'?\n", "Thought:I need to use a tool.\n", "Action: Wikipedia\n", "Action Input: Bocchi the Rock!, Japanese four-panel manga and anime series.\n", "Observation: Page: <PERSON><PERSON> the Rock!\n", "Summary: <PERSON><PERSON> the Rock! (ぼっち・ざ・ろっく!, Bocchi Za Rokku!) is a Japanese four-panel manga series written and illustrated by <PERSON><PERSON>. It has been serialized in Ho<PERSON><PERSON><PERSON>'s seinen manga magazine Manga Time Kirara Max since December 2017. Its chapters have been collected in five tankōbon volumes as of November 2022.\n", "An anime television series adaptation produced by CloverWorks aired from October to December 2022. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\n", "\n", "Page: Manga <PERSON>\n", "Summary: Manga Time Kirara (まんがタイムきらら, Manga Taimu Kirara) is a Japanese seinen manga magazine published by Houbunsha which mainly serializes four-panel manga. The magazine is sold on the ninth of each month and was first published as a special edition of Manga Time, another Houbunsha magazine, on May 17, 2002. Characters from this magazine have appeared in a crossover role-playing game called Kirara Fantasia.\n", "\n", "Page: Manga Time Kirara Max\n", "Summary: Manga Time Kirara Max (まんがタイムきららMAX) is a Japanese four-panel seinen manga magazine published by Houbunsha. It is the third magazine of the \"Kirara\" series, after \"Manga Time Kirara\" and \"Manga Time Kirara Carat\". The first issue was released on September 29, 2004. Currently the magazine is released on the 19th of each month.\n", "Thought:\n", "=====END OF PROMPT======\n", "\u001b[32;1m\u001b[1;3mThese are not relevant articles.\n", "Action: Wikipedia\n", "Action Input: Bocchi the Rock!, Japanese four-panel manga series written and illustrated by <PERSON><PERSON>.\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mPage: Bocchi the Rock!\n", "Summary: <PERSON><PERSON> the Rock! (ぼっち・ざ・ろっく!, Bocchi Za Rokku!) is a Japanese four-panel manga series written and illustrated by <PERSON><PERSON>. It has been serialized in Ho<PERSON><PERSON><PERSON>'s seinen manga magazine Manga Time Kirara Max since December 2017. Its chapters have been collected in five tankōbon volumes as of November 2022.\n", "An anime television series adaptation produced by CloverWorks aired from October to December 2022. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\u001b[0m\n", "Thought:\n", "===PROMPT====\n", "Answer the following questions as best you can. You have access to the following tools:\n", "\n", "Wikipedia: A wrapper around Wikipedia. Useful for when you need to answer general questions about people, places, companies, historical events, or other subjects. Input should be a search query.\n", "\n", "Use the following format:\n", "\n", "Question: the input question you must answer\n", "Thought: you should always think about what to do\n", "Action: the action to take, should be one of [Wikipedia]\n", "Action Input: the input to the action\n", "Observation: the result of the action\n", "... (this Thought/Action/Action Input/Observation can repeat N times)\n", "Thought: I now know the final answer\n", "Final Answer: the final answer to the original input question\n", "\n", "Begin!\n", "\n", "Question: What is '<PERSON><PERSON> the Rock!'?\n", "Thought:I need to use a tool.\n", "Action: Wikipedia\n", "Action Input: Bocchi the Rock!, Japanese four-panel manga and anime series.\n", "Observation: Page: <PERSON><PERSON> the Rock!\n", "Summary: <PERSON><PERSON> the Rock! (ぼっち・ざ・ろっく!, Bocchi Za Rokku!) is a Japanese four-panel manga series written and illustrated by <PERSON><PERSON>. It has been serialized in Ho<PERSON><PERSON><PERSON>'s seinen manga magazine Manga Time Kirara Max since December 2017. Its chapters have been collected in five tankōbon volumes as of November 2022.\n", "An anime television series adaptation produced by CloverWorks aired from October to December 2022. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\n", "\n", "Page: Manga <PERSON>\n", "Summary: Manga Time Kirara (まんがタイムきらら, Manga Taimu Kirara) is a Japanese seinen manga magazine published by Houbunsha which mainly serializes four-panel manga. The magazine is sold on the ninth of each month and was first published as a special edition of Manga Time, another Houbunsha magazine, on May 17, 2002. Characters from this magazine have appeared in a crossover role-playing game called Kirara Fantasia.\n", "\n", "Page: Manga Time Kirara Max\n", "Summary: Manga Time Kirara Max (まんがタイムきららMAX) is a Japanese four-panel seinen manga magazine published by Houbunsha. It is the third magazine of the \"Kirara\" series, after \"Manga Time Kirara\" and \"Manga Time Kirara Carat\". The first issue was released on September 29, 2004. Currently the magazine is released on the 19th of each month.\n", "Thought:These are not relevant articles.\n", "Action: Wikipedia\n", "Action Input: Bocchi the Rock!, Japanese four-panel manga series written and illustrated by <PERSON><PERSON>.\n", "Observation: Page: <PERSON><PERSON> the Rock!\n", "Summary: <PERSON><PERSON> the Rock! (ぼっち・ざ・ろっく!, Bocchi Za Rokku!) is a Japanese four-panel manga series written and illustrated by <PERSON><PERSON>. It has been serialized in Ho<PERSON><PERSON><PERSON>'s seinen manga magazine Manga Time Kirara Max since December 2017. Its chapters have been collected in five tankōbon volumes as of November 2022.\n", "An anime television series adaptation produced by CloverWorks aired from October to December 2022. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\n", "Thought:\n", "=====END OF PROMPT======\n", "\u001b[32;1m\u001b[1;3mIt worked.\n", "Final Answer: Bocchi the Rock! is a four-panel manga series and anime television series. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["\"Bocchi the Rock! is a four-panel manga series and anime television series. The series has been praised for its writing, comedy, characters, and depiction of social anxiety, with the anime's visual creativity receiving acclaim.\""]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["agent.run(\"What is '<PERSON><PERSON> the Rock!'?\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}, "vscode": {"interpreter": {"hash": "ab4db1680e5f8d10489fb83454f4ec01729e3bd5bdb28eaf0a13b95ddb6ae5ea"}}}, "nbformat": 4, "nbformat_minor": 2}