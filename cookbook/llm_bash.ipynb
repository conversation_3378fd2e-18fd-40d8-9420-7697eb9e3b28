{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Bash chain\n", "This notebook showcases using LLMs and a bash process to perform simple filesystem commands."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new LLMBashChain chain...\u001b[0m\n", "Please write a bash script that prints 'Hello World' to the console.\u001b[32;1m\u001b[1;3m\n", "\n", "```bash\n", "echo \"Hello World\"\n", "```\u001b[0m\n", "Code: \u001b[33;1m\u001b[1;3m['echo \"Hello World\"']\u001b[0m\n", "Answer: \u001b[33;1m\u001b[1;3mHello World\n", "\u001b[0m\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'Hello World\\n'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_experimental.llm_bash.base import LLMBashChain\n", "from langchain_openai import OpenAI\n", "\n", "llm = OpenAI(temperature=0)\n", "\n", "text = \"Please write a bash script that prints 'Hello World' to the console.\"\n", "\n", "bash_chain = LLMBashChain.from_llm(llm, verbose=True)\n", "\n", "bash_chain.invoke(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Customize Prompt\n", "You can also customize the prompt that is used. Here is an example prompting to avoid using the 'echo' utility"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain.prompts.prompt import PromptTemplate\n", "from langchain_experimental.llm_bash.prompt import BashOutputParser\n", "\n", "_PROMPT_TEMPLATE = \"\"\"If someone asks you to perform a task, your job is to come up with a series of bash commands that will perform the task. There is no need to put \"#!/bin/bash\" in your answer. Make sure to reason step by step, using this format:\n", "Question: \"copy the files in the directory named 'target' into a new directory at the same level as target called 'myNewDirectory'\"\n", "I need to take the following actions:\n", "- List all files in the directory\n", "- Create a new directory\n", "- Copy the files from the first directory into the second directory\n", "```bash\n", "ls\n", "mkdir myNewDirectory\n", "cp -r target/* myNewDirectory\n", "```\n", "\n", "Do not use 'echo' when writing the script.\n", "\n", "That is the format. Begin!\n", "Question: {question}\"\"\"\n", "\n", "PROMPT = PromptTemplate(\n", "    input_variables=[\"question\"],\n", "    template=_PROMPT_TEMPLATE,\n", "    output_parser=BashOutputParser(),\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new LLMBashChain chain...\u001b[0m\n", "Please write a bash script that prints 'Hello World' to the console.\u001b[32;1m\u001b[1;3m\n", "\n", "```bash\n", "printf \"Hello World\\n\"\n", "```\u001b[0m\n", "Code: \u001b[33;1m\u001b[1;3m['printf \"Hello World\\\\n\"']\u001b[0m\n", "Answer: \u001b[33;1m\u001b[1;3mHello World\n", "\u001b[0m\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'Hello World\\n'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["bash_chain = LLMBashChain.from_llm(llm, prompt=PROMPT, verbose=True)\n", "\n", "text = \"Please write a bash script that prints 'Hello World' to the console.\"\n", "\n", "bash_chain.invoke(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Persistent Terminal\n", "\n", "By default, the chain will run in a separate subprocess each time it is called. This behavior can be changed by instantiating with a persistent bash process."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new LLMBashChain chain...\u001b[0m\n", "List the current directory then move up a level.\u001b[32;1m\u001b[1;3m\n", "\n", "```bash\n", "ls\n", "cd ..\n", "```\u001b[0m\n", "Code: \u001b[33;1m\u001b[1;3m['ls', 'cd ..']\u001b[0m\n", "Answer: \u001b[33;1m\u001b[1;3mcpal.ipynb  llm_bash.ipynb  llm_symbolic_math.ipynb\n", "index.mdx   llm_math.ipynb  pal.ipynb\u001b[0m\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'cpal.ipynb  llm_bash.ipynb  llm_symbolic_math.ipynb\\r\\nindex.mdx   llm_math.ipynb  pal.ipynb'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_experimental.llm_bash.bash import BashProcess\n", "\n", "persistent_process = BashProcess(persistent=True)\n", "bash_chain = LLMBashChain.from_llm(llm, bash_process=persistent_process, verbose=True)\n", "\n", "text = \"List the current directory then move up a level.\"\n", "\n", "bash_chain.invoke(text)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new LLMBashChain chain...\u001b[0m\n", "List the current directory then move up a level.\u001b[32;1m\u001b[1;3m\n", "\n", "```bash\n", "ls\n", "cd ..\n", "```\u001b[0m\n", "Code: \u001b[33;1m\u001b[1;3m['ls', 'cd ..']\u001b[0m\n", "Answer: \u001b[33;1m\u001b[1;3m_category_.yml\tdata_generation.ipynb\t\t   self_check\n", "agents\t\tgraph\n", "code_writing\tlearned_prompt_optimization.ipynb\u001b[0m\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'_category_.yml\\tdata_generation.ipynb\\t\\t   self_check\\r\\nagents\\t\\tgraph\\r\\ncode_writing\\tlearned_prompt_optimization.ipynb'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Run the same command again and see that the state is maintained between calls\n", "bash_chain.invoke(text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 4}