{"cells": [{"cell_type": "markdown", "id": "0f0b9afa", "metadata": {}, "source": ["# Retrieve as you generate with FLARE\n", "\n", "This notebook is an implementation of Forward-Looking Active REtrieval augmented generation (FLARE).\n", "\n", "Please see the original repo [here](https://github.com/jzbjyb/FLARE/tree/main).\n", "\n", "The basic idea is:\n", "\n", "- Start answering a question\n", "- If you start generating tokens the model is uncertain about, look up relevant documents\n", "- Use those documents to continue generating\n", "- Repeat until finished\n", "\n", "There is a lot of cool detail in how the lookup of relevant documents is done.\n", "Basically, the tokens that model is uncertain about are highlighted, and then an LLM is called to generate a question that would lead to that answer. For example, if the generated text is `<PERSON> went to Harvard`, and the tokens the model was uncertain about was `Harvard`, then a good generated question would be `where did <PERSON> go to college`. This generated question is then used in a retrieval step to fetch relevant documents.\n", "\n", "In order to set up this chain, we will need three things:\n", "\n", "- An LLM to generate the answer\n", "- An LLM to generate hypothetical questions to use in retrieval\n", "- A retriever to use to look up answers for\n", "\n", "The LLM that we use to generate the answer needs to return logprobs so we can identify uncertain tokens. For that reason, we HIGHLY recommend that you use the OpenAI wrapper (NB: not the ChatOpenAI wrapper, as that does not return logprobs).\n", "\n", "The LLM we use to generate hypothetical questions to use in retrieval can be anything. In this notebook we will use ChatOpenAI because it is fast and cheap.\n", "\n", "The retriever can be anything. In this notebook we will use [SERPER](https://serper.dev/) search engine, because it is cheap.\n", "\n", "Other important parameters to understand:\n", "\n", "- `max_generation_len`: The maximum number of tokens to generate before stopping to check if any are uncertain\n", "- `min_prob`: Any tokens generated with probability below this will be considered uncertain"]}, {"cell_type": "markdown", "id": "a7e4b63d", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "id": "042bb161", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"SERPER_API_KEY\"] = \"\"\n", "os.environ[\"OPENAI_API_KEY\"] = \"\""]}, {"cell_type": "code", "execution_count": 2, "id": "a7888f4a", "metadata": {}, "outputs": [], "source": ["from typing import Any, List\n", "\n", "from langchain.callbacks.manager import (\n", "    AsyncCallbackManagerForRetrieverRun,\n", "    CallbackManagerForRetrieverRun,\n", ")\n", "from langchain_community.utilities import GoogleSerperAPIWrapper\n", "from langchain_core.documents import Document\n", "from langchain_core.retrievers import BaseRetriever\n", "from langchain_openai import ChatOpenAI, OpenAI"]}, {"cell_type": "markdown", "id": "5f552dce", "metadata": {}, "source": ["## Retriever"]}, {"cell_type": "code", "execution_count": 3, "id": "59c7d875", "metadata": {}, "outputs": [], "source": ["class SerperSearchRetriever(BaseRetriever):\n", "    search: GoogleSerperAPIWrapper = None\n", "\n", "    def _get_relevant_documents(\n", "        self, query: str, *, run_manager: CallbackManagerForRetrieverRun, **kwargs: Any\n", "    ) -> List[Document]:\n", "        return [Document(page_content=self.search.run(query))]\n", "\n", "    async def _aget_relevant_documents(\n", "        self,\n", "        query: str,\n", "        *,\n", "        run_manager: AsyncCallbackManagerForRetrieverRun,\n", "        **kwargs: Any,\n", "    ) -> List[Document]:\n", "        raise NotImplementedError()\n", "\n", "\n", "retriever = SerperSearchRetriever(search=GoogleSerperAPIWrapper())"]}, {"cell_type": "markdown", "id": "92478194", "metadata": {}, "source": ["## FLARE Chain"]}, {"cell_type": "code", "execution_count": 4, "id": "577e7c2c", "metadata": {}, "outputs": [], "source": ["# We set this so we can see what exactly is going on\n", "from langchain.globals import set_verbose\n", "\n", "set_verbose(True)"]}, {"cell_type": "code", "execution_count": 5, "id": "300d783e", "metadata": {}, "outputs": [], "source": ["from langchain.chains import FlareChain\n", "\n", "flare = FlareChain.from_llm(\n", "    ChatOpenAI(temperature=0),\n", "    retriever=retriever,\n", "    max_generation_len=164,\n", "    min_prob=0.3,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "1f3d5e90", "metadata": {}, "outputs": [], "source": ["query = \"explain in great detail the difference between the langchain framework and baby agi\""]}, {"cell_type": "code", "execution_count": 7, "id": "4b1bfa8c", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new FlareChain chain...\u001b[0m\n", "\u001b[36;1m\u001b[1;3mCurrent Response: \u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mRespond to the user message using any relevant context. If context is provided, you should ground your answer in that context. Once you're done responding return FINISHED.\n", "\n", ">>> CONTEXT: \n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> RESPONSE: \u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new QuestionGeneratorChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "The Langchain Framework is a decentralized platform for natural language processing (NLP) applications. It uses a blockchain-based distributed ledger to store and process data, allowing for secure and transparent data sharing. The Langchain Framework also provides a set of tools and services to help developers create and deploy NLP applications.\n", "\n", "Baby AGI, on the other hand, is an artificial general intelligence (AGI) platform. It uses a combination of deep learning and reinforcement learning to create an AI system that can learn and adapt to new tasks. Baby AGI is designed to be a general-purpose AI system that can be used for a variety of applications, including natural language processing.\n", "\n", "In summary, the Langchain Framework is a platform for NLP applications, while Baby AGI is an AI system designed for\n", "\n", "The question to which the answer is the term/entity/phrase \" decentralized platform for natural language processing\" is:\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "The Langchain Framework is a decentralized platform for natural language processing (NLP) applications. It uses a blockchain-based distributed ledger to store and process data, allowing for secure and transparent data sharing. The Langchain Framework also provides a set of tools and services to help developers create and deploy NLP applications.\n", "\n", "Baby AGI, on the other hand, is an artificial general intelligence (AGI) platform. It uses a combination of deep learning and reinforcement learning to create an AI system that can learn and adapt to new tasks. Baby AGI is designed to be a general-purpose AI system that can be used for a variety of applications, including natural language processing.\n", "\n", "In summary, the Langchain Framework is a platform for NLP applications, while Baby AGI is an AI system designed for\n", "\n", "The question to which the answer is the term/entity/phrase \" uses a blockchain\" is:\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "The Langchain Framework is a decentralized platform for natural language processing (NLP) applications. It uses a blockchain-based distributed ledger to store and process data, allowing for secure and transparent data sharing. The Langchain Framework also provides a set of tools and services to help developers create and deploy NLP applications.\n", "\n", "Baby AGI, on the other hand, is an artificial general intelligence (AGI) platform. It uses a combination of deep learning and reinforcement learning to create an AI system that can learn and adapt to new tasks. Baby AGI is designed to be a general-purpose AI system that can be used for a variety of applications, including natural language processing.\n", "\n", "In summary, the Langchain Framework is a platform for NLP applications, while Baby AGI is an AI system designed for\n", "\n", "The question to which the answer is the term/entity/phrase \" distributed ledger to\" is:\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "The Langchain Framework is a decentralized platform for natural language processing (NLP) applications. It uses a blockchain-based distributed ledger to store and process data, allowing for secure and transparent data sharing. The Langchain Framework also provides a set of tools and services to help developers create and deploy NLP applications.\n", "\n", "Baby AGI, on the other hand, is an artificial general intelligence (AGI) platform. It uses a combination of deep learning and reinforcement learning to create an AI system that can learn and adapt to new tasks. Baby AGI is designed to be a general-purpose AI system that can be used for a variety of applications, including natural language processing.\n", "\n", "In summary, the Langchain Framework is a platform for NLP applications, while Baby AGI is an AI system designed for\n", "\n", "The question to which the answer is the term/entity/phrase \" process data, allowing for secure and transparent data sharing.\" is:\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "The Langchain Framework is a decentralized platform for natural language processing (NLP) applications. It uses a blockchain-based distributed ledger to store and process data, allowing for secure and transparent data sharing. The Langchain Framework also provides a set of tools and services to help developers create and deploy NLP applications.\n", "\n", "Baby AGI, on the other hand, is an artificial general intelligence (AGI) platform. It uses a combination of deep learning and reinforcement learning to create an AI system that can learn and adapt to new tasks. Baby AGI is designed to be a general-purpose AI system that can be used for a variety of applications, including natural language processing.\n", "\n", "In summary, the Langchain Framework is a platform for NLP applications, while Baby AGI is an AI system designed for\n", "\n", "The question to which the answer is the term/entity/phrase \" set of tools\" is:\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "The Langchain Framework is a decentralized platform for natural language processing (NLP) applications. It uses a blockchain-based distributed ledger to store and process data, allowing for secure and transparent data sharing. The Langchain Framework also provides a set of tools and services to help developers create and deploy NLP applications.\n", "\n", "Baby AGI, on the other hand, is an artificial general intelligence (AGI) platform. It uses a combination of deep learning and reinforcement learning to create an AI system that can learn and adapt to new tasks. Baby AGI is designed to be a general-purpose AI system that can be used for a variety of applications, including natural language processing.\n", "\n", "In summary, the Langchain Framework is a platform for NLP applications, while Baby AGI is an AI system designed for\n", "\n", "The question to which the answer is the term/entity/phrase \" help developers create\" is:\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "The Langchain Framework is a decentralized platform for natural language processing (NLP) applications. It uses a blockchain-based distributed ledger to store and process data, allowing for secure and transparent data sharing. The Langchain Framework also provides a set of tools and services to help developers create and deploy NLP applications.\n", "\n", "Baby AGI, on the other hand, is an artificial general intelligence (AGI) platform. It uses a combination of deep learning and reinforcement learning to create an AI system that can learn and adapt to new tasks. Baby AGI is designed to be a general-purpose AI system that can be used for a variety of applications, including natural language processing.\n", "\n", "In summary, the Langchain Framework is a platform for NLP applications, while Baby AGI is an AI system designed for\n", "\n", "The question to which the answer is the term/entity/phrase \" create an AI system\" is:\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "The Langchain Framework is a decentralized platform for natural language processing (NLP) applications. It uses a blockchain-based distributed ledger to store and process data, allowing for secure and transparent data sharing. The Langchain Framework also provides a set of tools and services to help developers create and deploy NLP applications.\n", "\n", "Baby AGI, on the other hand, is an artificial general intelligence (AGI) platform. It uses a combination of deep learning and reinforcement learning to create an AI system that can learn and adapt to new tasks. Baby AGI is designed to be a general-purpose AI system that can be used for a variety of applications, including natural language processing.\n", "\n", "In summary, the Langchain Framework is a platform for NLP applications, while Baby AGI is an AI system designed for\n", "\n", "The question to which the answer is the term/entity/phrase \" NLP applications\" is:\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\u001b[33;1m\u001b[1;3mGenerated Questions: ['What is the Langchain Framework?', 'What technology does the Langchain Framework use to store and process data for secure and transparent data sharing?', 'What technology does the Langchain Framework use to store and process data?', 'What does the Langchain Framework use a blockchain-based distributed ledger for?', 'What does the Langchain Framework provide in addition to a decentralized platform for natural language processing applications?', 'What set of tools and services does the Langchain Framework provide?', 'What is the purpose of Baby AGI?', 'What type of applications is the Langchain Framework designed for?']\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new _OpenAIResponseChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mRespond to the user message using any relevant context. If context is provided, you should ground your answer in that context. Once you're done responding return FINISHED.\n", "\n", ">>> CONTEXT: LangChain: Software. LangChain is a software development framework designed to simplify the creation of applications using large language models. LangChain Initial release date: October 2022. LangChain Programming languages: Python and JavaScript. LangChain Developer(s): <PERSON>. LangChain License: MIT License. LangChain is a framework for developing applications powered by language models. We believe that the most powerful and differentiated applications will not only ... Type: Software framework. At its core, LangChain is a framework built around LLMs. We can use it for chatbots, Generative Question-Answering (GQA), summarization, and much more. LangChain is a powerful tool that can be used to work with Large Language Models (LLMs). LLMs are very general in nature, which means that while they can ... LangChain is an intuitive framework created to assist in developing applications driven by a language model, such as OpenAI or Hugging Face. LangChain is a software development framework designed to simplify the creation of applications using large language models (LLMs). Written in: Python and JavaScript. Initial release: October 2022. LangChain - The A.I-native developer toolkit We started LangChain with the intent to build a modular and flexible framework for developing A.I- ... <PERSON><PERSON><PERSON><PERSON> explained in 3 minutes - <PERSON><PERSON><PERSON><PERSON> is a ... Duration: 3:03. Posted: Apr 13, 2023. LangChain is a framework built to help you build LLM-powered applications more easily by providing you with the following:. LangChain is a framework that enables quick and easy development of applications that make use of Large Language Models, for example, GPT-3. LangChain is a powerful open-source framework for developing applications powered by language models. It connects to the AI models you want to ...\n", "\n", "LangChain is a framework for including AI from large language models inside data pipelines and applications. This tutorial provides an overview of what you ... Missing: secure | Must include:secure. Blockchain is the best way to secure the data of the shared community. Utilizing the capabilities of the blockchain nobody can read or interfere ... This modern technology consists of a chain of blocks that allows to securely store all committed transactions using shared and distributed ... A Blockchain network is used in the healthcare system to preserve and exchange patient data through hospitals, diagnostic laboratories, pharmacy firms, and ... In this article, I will walk you through the process of using the LangChain.js library with Google Cloud Functions, helping you leverage the ... LangChain is an intuitive framework created to assist in developing applications driven by a language model, such as OpenAI or Hugging Face. Missing: transparent | Must include:transparent. This technology keeps a distributed ledger on each blockchain node, making it more secure and transparent. The blockchain network can operate smart ... blockchain technology can offer a highly secured health data ledger to ... framework can be employed to store encrypted healthcare data in a ... In a simplified way, Blockchain is a data structure that stores transactions in an ordered way and linked to the previous block, serving as a ... Blockchain technology is a decentralized, distributed ledger that stores the record of ownership of digital assets. Missing: Langchain | Must include:Langchain.\n", "\n", "LangChain is a framework for including AI from large language models inside data pipelines and applications. This tutorial provides an overview of what you ... LangChain is an intuitive framework created to assist in developing applications driven by a language model, such as OpenAI or Hugging Face. This documentation covers the steps to integrate Pinecone, a high-performance vector database, with LangChain, a framework for building applications powered ... The ability to connect to any model, ingest any custom database, and build upon a framework that can take action provides numerous use cases for ... With LangChain, developers can use a framework that abstracts the core building blocks of LLM applications. LangChain empowers developers to ... Build a question-answering tool based on financial data with LangChain & Deep Lake's unified & streamable data store. Browse applications built on LangChain technology. Explore PoC and MVP applications created by our community and discover innovative use cases for LangChain ... LangChain is a great framework that can be used for developing applications powered by LLMs. When you intend to enhance your application ... In this blog, we'll introduce you to Lang<PERSON>hain and <PERSON> and how to use them to build a search engine using LLM embeddings and a vector ... The LinkChain Framework simplifies embedding creation and storage using Pinecone and Chroma, with code that loads files, splits documents, and creates embedding ... Missing: technology | Must include:technology.\n", "\n", "Blockchain is one type of a distributed ledger. Distributed ledgers use independent computers (referred to as nodes) to record, share and ... Missing: Langchain | Must include:Langchain. Blockchain is used in distributed storage software where huge data is broken down into chunks. This is available in encrypted data across a ... People sometimes use the terms 'Blockchain' and 'Distributed Ledger' interchangeably. This post aims to analyze the features of each. A distributed ledger ... Missing: Framework | Must include:Framework. Think of a “distributed ledger” that uses cryptography to allow each participant in the transaction to add to the ledger in a secure way without ... In this paper, we provide an overview of the history of trade settlement and discuss this nascent technology that may now transform traditional ... Missing: Langchain | Must include:Langchain. LangChain is a blockchain-based language education platform that aims to revolutionize the way people learn languages. Missing: Framework | Must include:Framework. It uses the distributed ledger technology framework and Smart contract engine for building scalable Business Blockchain applications. The fabric ... It looks at the assets the use case is handling, the different parties conducting transactions, and the smart contract, distributed ... Are you curious to know how Blockchain and Distributed ... Duration: 44:31. Posted: May 4, 2021. A blockchain is a distributed and immutable ledger to transfer ownership, record transactions, track assets, and ensure transparency, security, trust and value ... Missing: Langchain | Must include:Langchain.\n", "\n", "LangChain is an intuitive framework created to assist in developing applications driven by a language model, such as OpenAI or Hugging Face. Missing: decentralized | Must include:decentralized. LangChain, created by <PERSON>, is a Python library that provides out-of-the-box support to build NLP applications using LLMs. Missing: decentralized | Must include:decentralized. LangChain provides a standard interface for chains, enabling developers to create sequences of calls that go beyond a single LLM call. Chains ... Missing: decentralized platform natural. LangChain is a powerful framework that simplifies the process of building advanced language model applications. Missing: platform | Must include:platform. Are your language models ignoring previous instructions ... Duration: 32:23. Posted: Feb 21, 2023. LangChain is a framework that enables quick and easy development of applications ... Prompting is the new way of programming NLP models. Missing: decentralized platform. It then uses natural language processing and machine learning algorithms to search ... Summarization is handled via cohere, QnA is handled via langchain, ... LangChain is a framework for developing applications powered by language models. ... There are several main modules that LangChain provides support for. Missing: decentralized platform. In the healthcare-chain system, blockchain provides an appreciated secure ... The entire process of adding new and previous block data is performed based on ... ChatGPT is a large language model developed by OpenAI, ... tool for a wide range of applications, including natural language processing, ...\n", "\n", "LangChain is a powerful tool that can be used to work with Large Language ... If an API key has been provided, create an OpenAI language model instance At its core, LangChain is a framework built around LLMs. We can use it for chatbots, Generative Question-Answering (GQA), summarization, and much more. A tutorial of the six core modules of the LangChain Python package covering models, prompts, chains, agents, indexes, and memory with OpenAI ... LangChain's collection of tools refers to a set of tools provided by the LangChain framework for developing applications powered by language models. LangChain is a framework for developing applications powered by language models. We believe that the most powerful and differentiated applications will not only ... LangChain is an open-source library that provides developers with the tools to build applications powered by large language models (LLMs). LangChain is a framework for including AI from large language models inside data pipelines and applications. This tutorial provides an overview of what you ... Plan-and-Execute Agents · Feature Stores and LLMs · Structured Tools · Auto-Evaluator Opportunities · Callbacks Improvements · Unleashing the power ... Tool: A function that performs a specific duty. This can be things like: Google Search, Database lookup, Python REPL, other chains. · LLM: The language model ... LangChain provides a standard interface for chains, lots of integrations with other tools, and end-to-end chains for common applications.\n", "\n", "Baby AGI has the ability to complete tasks, generate new tasks based on previous results, and prioritize tasks in real-time. This system is exploring and demonstrating to us the potential of large language models, such as GPT and how it can autonomously perform tasks. Apr 17, 2023\n", "\n", "At its core, LangChain is a framework built around LLMs. We can use it for chatbots, Generative Question-Answering (GQA), summarization, and much more. The core idea of the library is that we can “chain” together different components to create more advanced use cases around LLMs.\n", ">>> USER INPUT: explain in great detail the difference between the langchain framework and baby agi\n", ">>> RESPONSE: \u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["' LangChain is a framework for developing applications powered by language models. It provides a standard interface for chains, lots of integrations with other tools, and end-to-end chains for common applications. On the other hand, Baby AGI is an AI system that is exploring and demonstrating the potential of large language models, such as GPT, and how it can autonomously perform tasks. Baby AGI has the ability to complete tasks, generate new tasks based on previous results, and prioritize tasks in real-time. '"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["flare.run(query)"]}, {"cell_type": "code", "execution_count": 8, "id": "7bed8944", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\n\\nThe Langchain framework and Baby AGI are both artificial intelligence (AI) frameworks that are used to create intelligent agents. The Langchain framework is a supervised learning system that is based on the concept of “language chains”. It uses a set of rules to map natural language inputs to specific outputs. It is a general-purpose AI framework and can be used to build applications such as natural language processing (NLP), chatbots, and more.\\n\\nBaby AGI, on the other hand, is an unsupervised learning system that uses neural networks and reinforcement learning to learn from its environment. It is used to create intelligent agents that can adapt to changing environments. It is a more advanced AI system and can be used to build more complex applications such as game playing, robotic vision, and more.\\n\\nThe main difference between the two is that the Langchain framework uses supervised learning while Baby AGI uses unsupervised learning. The Langchain framework is a general-purpose AI framework that can be used for various applications, while Baby AGI is a more advanced AI system that can be used to create more complex applications.'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["llm = OpenAI()\n", "llm.invoke(query)"]}, {"cell_type": "code", "execution_count": 9, "id": "8fb76286", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new FlareChain chain...\u001b[0m\n", "\u001b[36;1m\u001b[1;3mCurrent Response: \u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mRespond to the user message using any relevant context. If context is provided, you should ground your answer in that context. Once you're done responding return FINISHED.\n", "\n", ">>> CONTEXT: \n", ">>> USER INPUT: how are the origin stories of langchain and bitcoin similar or different?\n", ">>> RESPONSE: \u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new QuestionGeneratorChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: how are the origin stories of langchain and bitcoin similar or different?\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "\n", "Langchain and Bitcoin have very different origin stories. Bitcoin was created by the mysterious <PERSON><PERSON> in 2008 as a decentralized digital currency. Langchain, on the other hand, was created in 2020 by a team of developers as a platform for creating and managing decentralized language learning applications. \n", "\n", "FINISHED\n", "\n", "The question to which the answer is the term/entity/phrase \" very different origin\" is:\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: how are the origin stories of langchain and bitcoin similar or different?\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "\n", "Langchain and Bitcoin have very different origin stories. Bitcoin was created by the mysterious <PERSON><PERSON> in 2008 as a decentralized digital currency. Langchain, on the other hand, was created in 2020 by a team of developers as a platform for creating and managing decentralized language learning applications. \n", "\n", "FINISHED\n", "\n", "The question to which the answer is the term/entity/phrase \" 2020 by a\" is:\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven a user input and an existing partial response as context, ask a question to which the answer is the given term/entity/phrase:\n", "\n", ">>> USER INPUT: how are the origin stories of langchain and bitcoin similar or different?\n", ">>> EXISTING PARTIAL RESPONSE:  \n", "\n", "Langchain and Bitcoin have very different origin stories. Bitcoin was created by the mysterious <PERSON><PERSON> in 2008 as a decentralized digital currency. Langchain, on the other hand, was created in 2020 by a team of developers as a platform for creating and managing decentralized language learning applications. \n", "\n", "FINISHED\n", "\n", "The question to which the answer is the term/entity/phrase \" developers as a platform for creating and managing decentralized language learning applications.\" is:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\u001b[33;1m\u001b[1;3mGenerated Questions: ['How would you describe the origin stories of Langchain and Bitcoin in terms of their similarities or differences?', 'When was Langchain created and by whom?', 'What was the purpose of creating Langchain?']\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new _OpenAIResponseChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mRespond to the user message using any relevant context. If context is provided, you should ground your answer in that context. Once you're done responding return FINISHED.\n", "\n", ">>> CONTEXT: Bitcoin and Ethereum have many similarities but different long-term visions and limitations. Ethereum changed from proof of work to proof of ... Bitcoin will be around for many years and examining its white paper origins is a great exercise in understanding why. <PERSON><PERSON>'s blueprint describes ... Bitcoin is a new currency that was created in 2009 by an unknown person using the alias <PERSON><PERSON>. Transactions are made with no middle men – meaning, no ... Missing: Langchain | Must include:Langchain. By comparison, Bitcoin transaction speeds are tremendously lower. ... learn about its history and its role in the emergence of the Bitcoin ... LangChain is a powerful framework that simplifies the process of ... tasks like document retrieval, clustering, and similarity comparisons. Key terms: Bitcoin System, Blockchain Technology, ... Furthermore, the research paper will discuss and compare the five payment. Blockchain first appeared in <PERSON><PERSON><PERSON>'s Bitcoin white paper that describes a new decentralized cryptocurrency [1]. Bitcoin takes the blockchain technology ... Missing: stories | Must include:stories. A score of 0 means there were not enough data for this term. Google trends was accessed on 5 November 2018 with searches for bitcoin, euro, gold ... Contracts, transactions, and records of them provide critical structure in our economic system, but they haven't kept up with the world's digital ... Missing: Langchain | Must include:Langchain. Of course, traders try to make a profit on their portfolio in this way.The difference between investing and trading is the regularity with which ...\n", "\n", "After all these giant leaps forward in the LLM space, OpenAI released ChatGPT — thrusting LLMs into the spotlight. <PERSON><PERSON><PERSON><PERSON> appeared around the same time. Its creator, <PERSON>, made the first commit in late October 2022. Leaving a short couple of months of development before getting caught in the LLM wave.\n", "\n", "At its core, LangChain is a framework built around LLMs. We can use it for chatbots, Generative Question-Answering (GQA), summarization, and much more. The core idea of the library is that we can “chain” together different components to create more advanced use cases around LLMs.\n", ">>> USER INPUT: how are the origin stories of langchain and bitcoin similar or different?\n", ">>> RESPONSE: \u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["' The origin stories of <PERSON><PERSON><PERSON><PERSON> and Bitcoin are quite different. Bitcoin was created in 2009 by an unknown person using the alias <PERSON><PERSON>. LangChain was created in late October 2022 by <PERSON>. Bitcoin is a decentralized cryptocurrency, while LangChain is a framework built around LLMs. '"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["flare.run(\"how are the origin stories of langchain and bitcoin similar or different?\")"]}, {"cell_type": "code", "execution_count": null, "id": "fbadd022", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.1"}}, "nbformat": 4, "nbformat_minor": 5}