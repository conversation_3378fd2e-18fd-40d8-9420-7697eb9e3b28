name: '🧪 Unit Testing'
# Runs unit tests with both current and minimum supported dependency versions
# to ensure compatibility across the supported range

on:
  workflow_call:
    inputs:
      working-directory:
        required: true
        type: string
        description: "From which folder this pipeline executes"
      python-version:
        required: true
        type: string
        description: "Python version to use"

permissions:
  contents: read

env:
  UV_FROZEN: "true"
  UV_NO_SYNC: "true"

jobs:
  # Main test job - runs unit tests with current deps, then retests with minimum versions
  build:
    defaults:
      run:
        working-directory: ${{ inputs.working-directory }}
    runs-on: ubuntu-latest
    timeout-minutes: 20
    name: 'Python ${{ inputs.python-version }}'
    steps:
      - name: '📋 Checkout Code'
        uses: actions/checkout@v4

      - name: '🐍 Set up Python ${{ inputs.python-version }} + UV'
        uses: "./.github/actions/uv_setup"
        id: setup-python
        with:
          python-version: ${{ inputs.python-version }}
      - name: '📦 Install Test Dependencies'
        shell: bash
        run: uv sync --group test --dev

      - name: '🧪 Run Core Unit Tests'
        shell: bash
        run: |
          make test

      - name: '🔍 Calculate Minimum Dependency Versions'
        working-directory: ${{ inputs.working-directory }}
        id: min-version
        shell: bash
        run: |
          VIRTUAL_ENV=.venv uv pip install packaging tomli requests
          python_version="$(uv run python --version | awk '{print $2}')"
          min_versions="$(uv run python $GITHUB_WORKSPACE/.github/scripts/get_min_versions.py pyproject.toml pull_request $python_version)"
          echo "min-versions=$min_versions" >> "$GITHUB_OUTPUT"
          echo "min-versions=$min_versions"

      - name: '🧪 Run Tests with Minimum Dependencies'
        if: ${{ steps.min-version.outputs.min-versions != '' }}
        env:
          MIN_VERSIONS: ${{ steps.min-version.outputs.min-versions }}
        run: |
          VIRTUAL_ENV=.venv uv pip install $MIN_VERSIONS
          make tests
        working-directory: ${{ inputs.working-directory }}

      - name: '🧹 Verify Clean Working Directory'
        shell: bash
        run: |
          set -eu

          STATUS="$(git status)"
          echo "$STATUS"

          # grep will exit non-zero if the target message isn't found,
          # and `set -e` above will cause the step to fail.
          echo "$STATUS" | grep 'nothing to commit, working tree clean'

