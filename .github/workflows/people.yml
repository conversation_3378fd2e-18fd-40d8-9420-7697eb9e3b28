name: '👥 LangChain People'
run-name: 'Update People Data'
# This workflow updates the LangChain People data by fetching the latest information from the LangChain Git
on:
  schedule:
    - cron: "0 14 1 * *"
  push:
    branches: [jacob/people]
  workflow_dispatch:

jobs:
  langchain-people:
    if: github.repository_owner == 'langchain-ai' || github.event_name != 'schedule'
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: '📋 Dump GitHub Context'
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"
      - uses: actions/checkout@v4
      # Ref: https://github.com/actions/runner/issues/2033
      - name: '🔧 Fix Git Safe Directory in Container'
        run: mkdir -p /home/<USER>/work/_temp/_github_home && printf "[safe]\n\tdirectory = /github/workspace" > /home/<USER>/work/_temp/_github_home/.gitconfig
      - uses: ./.github/actions/people
        with:
          token: ${{ secrets.LANGCHAIN_PEOPLE_GITHUB_TOKEN }}
