name: '🔗 Check Broken Links'

on:
  workflow_dispatch:
  schedule:
    - cron:  '0 13 * * *'

permissions:
  contents: read

jobs:
  check-links:
    if: github.repository_owner == 'langchain-ai' || github.event_name != 'schedule'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: '🟢 Setup Node.js 18.x'
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
          cache: "yarn"
          cache-dependency-path: ./docs/yarn.lock
      - name: '📦 Install Node Dependencies'
        run: yarn install --immutable --mode=skip-build
        working-directory: ./docs
      - name: '🔍 Scan Documentation for Broken Links'
        run: yarn check-broken-links
        working-directory: ./docs
