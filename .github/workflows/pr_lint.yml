# -----------------------------------------------------------------------------
# PR Title Lint Workflow
#
# Purpose:
#   Enforces Conventional Commits format for pull request titles to maintain a
#   clear, consistent, and machine-readable change history across our repository.
#   This helps with automated changelog generation and semantic versioning.
#
# Enforced Commit Message Format (Conventional Commits 1.0.0):
#   <type>[optional scope]: <description>
#   [optional body]
#   [optional footer(s)]
#
# Allowed Types:
#   • feat       — a new feature (MINOR bump)
#   • fix        — a bug fix (PATCH bump)
#   • docs       — documentation only changes
#   • style      — formatting, missing semi-colons, etc.; no code change
#   • refactor   — code change that neither fixes a bug nor adds a feature
#   • perf       — code change that improves performance
#   • test       — adding missing tests or correcting existing tests
#   • build      — changes that affect the build system or external dependencies
#   • ci         — continuous integration/configuration changes
#   • chore      — other changes that don't modify src or test files
#   • revert     — reverts a previous commit
#   • release    — prepare a new release
#
# Allowed Scopes (optional):
#   core, cli, langchain, standard-tests, docs, anthropic, chroma, deepseek,
#   exa, fireworks, groq, huggingface, mistralai, nomic, ollama, openai,
#   perplexity, prompty, qdrant, xai
#
# Rules & Tips for New Committers:
#   1. Subject (type) must start with a lowercase letter and, if possible, be
#      followed by a scope wrapped in parenthesis `(scope)`
#   2. Breaking changes:
#        – Append "!" after type/scope (e.g., feat!: drop Node 12 support)
#        – Or include a footer "BREAKING CHANGE: <details>"
#   3. Example PR titles:
#        feat(core): add multi‐tenant support
#        fix(cli): resolve flag parsing error
#        docs: update API usage examples
#        docs(openai): update API usage examples
#
# Resources:
#   • Conventional Commits spec: https://www.conventionalcommits.org/en/v1.0.0/
# -----------------------------------------------------------------------------

name: '🏷️ PR Title Lint'

permissions:
  pull-requests: read

on:
  pull_request:
    types: [opened, edited, synchronize]

jobs:
  # Validates that PR title follows Conventional Commits specification
  lint-pr-title:
    name: 'Validate PR Title Format'
    runs-on: ubuntu-latest
    steps:
      - name: '✅ Validate Conventional Commits Format'
        uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            style
            refactor
            perf
            test
            build
            ci
            chore
            revert
            release
          scopes: |
            core
            cli
            langchain
            langchain_v1
            standard-tests
            text-splitters
            docs
            anthropic
            chroma
            deepseek
            exa
            fireworks
            groq
            huggingface
            mistralai
            nomic
            ollama
            openai
            perplexity
            prompty
            qdrant
            xai
            infra
          requireScope: false
          disallowScopes: |
            release
            [A-Z]+
          ignoreLabels: |
            ignore-lint-pr-title
