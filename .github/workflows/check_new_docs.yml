name: '📑 Integration Docs Lint'

on:
  push:
    branches: [master]
  pull_request:

# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
#
# There's no point in testing an outdated version of the code. GitHub only allows
# a limited number of job runners to be active at the same time, so it's better to cancel
# pointless jobs early so that more useful jobs can run sooner.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - id: files
        uses: Ana06/get-changed-files@v2.3.0
        with:
          filter: |
            *.ipynb
            *.md
            *.mdx
      - name: '🔍 Check New Documentation Templates'
        run: |
          python docs/scripts/check_templates.py ${{ steps.files.outputs.added }}
