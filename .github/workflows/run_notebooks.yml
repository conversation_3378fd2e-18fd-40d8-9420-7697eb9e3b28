name: '📓 Validate Documentation Notebooks'
run-name: 'Test notebooks in ${{ inputs.working-directory  }}'
on:
  workflow_dispatch:
    inputs:
      python_version:
        description: 'Python version'
        required: false
        default: '3.11'
      working-directory:
        description: 'Working directory or subset (e.g., docs/docs/tutorials/llm_chain.ipynb or docs/docs/how_to)'
        required: false
        default: 'all'
  schedule:
    - cron: '0 13 * * *'

permissions:
  contents: read

env:
  UV_FROZEN: "true"

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.repository == 'langchain-ai/langchain' || github.event_name != 'schedule'
    name: '📑 Test Documentation Notebooks'
    steps:
      - uses: actions/checkout@v4

      - name: '🐍 Set up Python + UV'
        uses: "./.github/actions/uv_setup"
        with:
          python-version: ${{ github.event.inputs.python_version || '3.11' }}

      - name: '🔐 Authenticate to Google Cloud'
        id: 'auth'
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GOOGLE_CREDENTIALS }}'

      - name: '🔐 Configure AWS Credentials'
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: '📦 Install Dependencies'
        run: |
          uv sync --group dev --group test

      - name: '📦 Pre-download Test Files'
        run: |
          uv run python docs/scripts/cache_data.py
          curl -s https://raw.githubusercontent.com/lerocha/chinook-database/master/ChinookDatabase/DataSources/Chinook_Sqlite.sql | sqlite3 docs/docs/how_to/Chinook.db
          cp docs/docs/how_to/Chinook.db docs/docs/tutorials/Chinook.db

      - name: '🔧 Prepare Notebooks for CI'
        run: |
          uv run python docs/scripts/prepare_notebooks_for_ci.py --comment-install-cells --working-directory ${{ github.event.inputs.working-directory || 'all' }}

      - name: '🚀 Execute Notebooks'
        env:
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          FIREWORKS_API_KEY: ${{ secrets.FIREWORKS_API_KEY }}
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
          GROQ_API_KEY: ${{ secrets.GROQ_API_KEY }}
          MISTRAL_API_KEY: ${{ secrets.MISTRAL_API_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          TAVILY_API_KEY: ${{ secrets.TAVILY_API_KEY }}
          TOGETHER_API_KEY: ${{ secrets.TOGETHER_API_KEY }}
          WORKING_DIRECTORY: ${{ github.event.inputs.working-directory || 'all' }}
        run: |
          ./docs/scripts/execute_notebooks.sh $WORKING_DIRECTORY
