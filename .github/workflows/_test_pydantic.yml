name: '🐍 Pydantic Version Testing'

on:
  workflow_call:
    inputs:
      working-directory:
        required: true
        type: string
        description: "From which folder this pipeline executes"
      python-version:
        required: false
        type: string
        description: "Python version to use"
        default: "3.11"
      pydantic-version:
        required: true
        type: string
        description: "Pydantic version to test."

permissions:
  contents: read

env:
  UV_FROZEN: "true"
  UV_NO_SYNC: "true"

jobs:
  build:
    defaults:
      run:
        working-directory: ${{ inputs.working-directory }}
    runs-on: ubuntu-latest
    timeout-minutes: 20
    name: 'Pydantic ~=${{ inputs.pydantic-version }}'
    steps:
      - name: '📋 Checkout Code'
        uses: actions/checkout@v4

      - name: '🐍 Set up Python ${{ inputs.python-version }} + UV'
        uses: "./.github/actions/uv_setup"
        with:
          python-version: ${{ inputs.python-version }}

      - name: '📦 Install Test Dependencies'
        shell: bash
        run: uv sync --group test

      - name: '🔄 Install Specific Pydantic Version'
        shell: bash
        run: VIRTUAL_ENV=.venv uv pip install pydantic~=${{ inputs.pydantic-version }}

      - name: '🧪 Run Core Tests'
        shell: bash
        run: |
          make test

      - name: '🧹 Verify Clean Working Directory'
        shell: bash
        run: |
          set -eu

          STATUS="$(git status)"
          echo "$STATUS"

          # grep will exit non-zero if the target message isn't found,
          # and `set -e` above will cause the step to fail.
          echo "$STATUS" | grep 'nothing to commit, working tree clean'
