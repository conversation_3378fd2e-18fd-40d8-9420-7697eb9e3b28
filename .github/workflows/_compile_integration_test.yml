name: '🔗 Compile Integration Tests'

on:
  workflow_call:
    inputs:
      working-directory:
        required: true
        type: string
        description: "From which folder this pipeline executes"
      python-version:
        required: true
        type: string
        description: "Python version to use"

permissions:
  contents: read

env:
  UV_FROZEN: "true"

jobs:
  build:
    defaults:
      run:
        working-directory: ${{ inputs.working-directory }}
    runs-on: ubuntu-latest
    timeout-minutes: 20
    name: 'Python ${{ inputs.python-version }}'
    steps:
      - uses: actions/checkout@v4

      - name: '🐍 Set up Python ${{ inputs.python-version }} + UV'
        uses: "./.github/actions/uv_setup"
        with:
          python-version: ${{ inputs.python-version }}

      - name: '📦 Install Integration Dependencies'
        shell: bash
        run: uv sync --group test --group test_integration

      - name: '🔗 Check Integration Tests Compile'
        shell: bash
        run: uv run pytest -m compile tests/integration_tests

      - name: '🧹 Verify Clean Working Directory'
        shell: bash
        run: |
          set -eu

          STATUS="$(git status)"
          echo "$STATUS"

          # grep will exit non-zero if the target message isn't found,
          # and `set -e` above will cause the step to fail.
          echo "$STATUS" | grep 'nothing to commit, working tree clean'
