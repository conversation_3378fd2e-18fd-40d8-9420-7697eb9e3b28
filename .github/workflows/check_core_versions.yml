name: '🔍 Check `core` Version Equality'
# Ensures version numbers in pyproject.toml and version.py stay in sync
# Prevents releases with mismatched version numbers

on:
  pull_request:
    paths:
      - 'libs/core/pyproject.toml'
      - 'libs/core/langchain_core/version.py'

permissions:
  contents: read

jobs:
  check_version_equality:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: '✅ Verify pyproject.toml & version.py Match'
        run: |
          PYPROJECT_VERSION=$(grep -Po '(?<=^version = ")[^"]*' libs/core/pyproject.toml)
          VERSION_PY_VERSION=$(grep -Po '(?<=^VERSION = ")[^"]*' libs/core/langchain_core/version.py)

          # Compare the two versions
          if [ "$PYPROJECT_VERSION" != "$VERSION_PY_VERSION" ]; then
            echo "langchain-core versions in pyproject.toml and version.py do not match!"
            echo "pyproject.toml version: $PYPROJECT_VERSION"
            echo "version.py version: $VERSION_PY_VERSION"
            exit 1
          else
            echo "Versions match: $PYPROJECT_VERSION"
          fi
