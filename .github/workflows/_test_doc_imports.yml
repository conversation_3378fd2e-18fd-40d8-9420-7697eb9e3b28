name: '📑 Documentation Import Testing'

on:
  workflow_call:
    inputs:
      python-version:
        required: true
        type: string
        description: "Python version to use"

permissions:
  contents: read

env:
  UV_FROZEN: "true"

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    name: '🔍 Check Doc Imports (Python ${{ inputs.python-version }})'
    steps:
      - name: '📋 Checkout Code'
        uses: actions/checkout@v4

      - name: '🐍 Set up Python ${{ inputs.python-version }} + UV'
        uses: "./.github/actions/uv_setup"
        with:
          python-version: ${{ inputs.python-version }}

      - name: '📦 Install Test Dependencies'
        shell: bash
        run: uv sync --group test

      - name: '📦 Install LangChain in Editable Mode'
        run: |
          VIRTUAL_ENV=.venv uv pip install langchain-experimental langchain-community -e libs/core libs/langchain

      - name: '🔍 Validate Documentation Import Statements'
        shell: bash
        run: |
          uv run python docs/scripts/check_imports.py

      - name: '🧹 Verify Clean Working Directory'
        shell: bash
        run: |
          set -eu

          STATUS="$(git status)"
          echo "$STATUS"

          # grep will exit non-zero if the target message isn't found,
          # and `set -e` above will cause the step to fail.
          echo "$STATUS" | grep 'nothing to commit, working tree clean'
