name: '🧹 Code Linting'
# Runs code quality checks using ruff, my<PERSON>, and other linting tools
# Checks both package code and test code for consistency

on:
  workflow_call:
    inputs:
      working-directory:
        required: true
        type: string
        description: "From which folder this pipeline executes"
      python-version:
        required: true
        type: string
        description: "Python version to use"

permissions:
  contents: read

env:
  WORKDIR: ${{ inputs.working-directory == '' && '.' || inputs.working-directory }}

  # This env var allows us to get inline annotations when ruff has complaints.
  RUFF_OUTPUT_FORMAT: github

  UV_FROZEN: "true"

jobs:
  # Linting job - runs quality checks on package and test code
  build:
    name: 'Python ${{ inputs.python-version }}'
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - name: '📋 Checkout Code'
        uses: actions/checkout@v4

      - name: '🐍 Set up Python ${{ inputs.python-version }} + UV'
        uses: "./.github/actions/uv_setup"
        with:
          python-version: ${{ inputs.python-version }}

      - name: '📦 Install Lint & Typing Dependencies'
        # Also installs dev/lint/test/typing dependencies, to ensure we have
        # type hints for as many of our libraries as possible.
        # This helps catch errors that require dependencies to be spotted, for example:
        # https://github.com/langchain-ai/langchain/pull/10249/files#diff-935185cd488d015f026dcd9e19616ff62863e8cde8c0bee70318d3ccbca98341
        #
        # If you change this configuration, make sure to change the `cache-key`
        # in the `poetry_setup` action above to stop using the old cache.
        # It doesn't matter how you change it, any change will cause a cache-bust.
        working-directory: ${{ inputs.working-directory }}
        run: |
          uv sync --group lint --group typing

      - name: '🔍 Analyze Package Code with Linters'
        working-directory: ${{ inputs.working-directory }}
        run: |
          make lint_package

      - name: '📦 Install Unit Test Dependencies'
        # Also installs dev/lint/test/typing dependencies, to ensure we have
        # type hints for as many of our libraries as possible.
        # This helps catch errors that require dependencies to be spotted, for example:
        # https://github.com/langchain-ai/langchain/pull/10249/files#diff-935185cd488d015f026dcd9e19616ff62863e8cde8c0bee70318d3ccbca98341
        #
        # If you change this configuration, make sure to change the `cache-key`
        # in the `poetry_setup` action above to stop using the old cache.
        # It doesn't matter how you change it, any change will cause a cache-bust.
        if: ${{ ! startsWith(inputs.working-directory, 'libs/partners/') }}
        working-directory: ${{ inputs.working-directory }}
        run: |
          uv sync --inexact --group test
      - name: '📦 Install Unit + Integration Test Dependencies'
        if: ${{ startsWith(inputs.working-directory, 'libs/partners/') }}
        working-directory: ${{ inputs.working-directory }}
        run: |
          uv sync --inexact --group test --group test_integration

      - name: '🔍 Analyze Test Code with Linters'
        working-directory: ${{ inputs.working-directory }}
        run: |
          make lint_tests
