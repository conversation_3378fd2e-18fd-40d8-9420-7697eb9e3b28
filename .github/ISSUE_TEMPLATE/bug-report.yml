name: "\U0001F41B Bug Report"
description: Report a bug in LangChain. To report a security issue, please instead use the security option below. For questions, please use the LangChain forum.
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to file a bug report.

        Use this to report BUGS in LangChain. For usage questions, feature requests and general design questions, please use the [LangChain Forum](https://forum.langchain.com/).

        Relevant links to check before filing a bug report to see if your issue has already been reported, fixed or
        if there's another way to solve your problem:

        * [LangChain Forum](https://forum.langchain.com/),
        * [LangChain Github Issues](https://github.com/langchain-ai/langchain/issues?q=is%3Aissue),
        * [LangChain documentation with the integrated search](https://python.langchain.com/docs/get_started/introduction),
        * [LangChain how-to guides](https://python.langchain.com/docs/how_to/),
        * [API Reference](https://python.langchain.com/api_reference/),
        * [<PERSON><PERSON><PERSON><PERSON>t<PERSON>ot](https://chat.langchain.com/)
        * [GitHub search](https://github.com/langchain-ai/langchain),
  - type: checkboxes
    id: checks
    attributes:
      label: Checked other resources
      description: Please confirm and check all the following options.
      options:
        - label: This is a bug, not a usage question. For questions, please use the LangChain Forum (https://forum.langchain.com/).
          required: true
        - label: I added a clear and descriptive title that summarizes this issue.
          required: true
        - label: I used the GitHub search to find a similar question and didn't find it.
          required: true
        - label: I am sure that this is a bug in LangChain rather than my code.
          required: true
        - label: The bug is not resolved by updating to the latest stable version of LangChain (or the specific integration package).
          required: true
        - label: I read what a minimal reproducible example is (https://stackoverflow.com/help/minimal-reproducible-example).
          required: true
        - label: I posted a self-contained, minimal, reproducible example. A maintainer can copy it and run it AS IS.
          required: true
  - type: textarea
    id: reproduction
    validations:
      required: true
    attributes:
      label: Example Code
      description: |
        Please add a self-contained, [minimal, reproducible, example](https://stackoverflow.com/help/minimal-reproducible-example) with your use case.

        If a maintainer can copy it, run it, and see it right away, there's a much higher chance that you'll be able to get help.

        **Important!**

        * Avoid screenshots when possible, as they are hard to read and (more importantly) don't allow others to copy-and-paste your code.
        * Reduce your code to the minimum required to reproduce the issue if possible. This makes it much easier for others to help you.
        * Use code tags (e.g., ```python ... ```) to correctly [format your code](https://help.github.com/en/github/writing-on-github/creating-and-highlighting-code-blocks#syntax-highlighting).
        * INCLUDE the language label (e.g. `python`) after the first three backticks to enable syntax highlighting. (e.g., ```python rather than ```).

      placeholder: |
        The following code:

        ```python
        from langchain_core.runnables import RunnableLambda

        def bad_code(inputs) -> int:
          raise NotImplementedError('For demo purpose')

          chain = RunnableLambda(bad_code)
          chain.invoke('Hello!')
        ```
  - type: textarea
    id: error
    validations:
      required: false
    attributes:
      label: Error Message and Stack Trace (if applicable)
      description: |
        If you are reporting an error, please include the full error message and stack trace.
      placeholder: |
        Exception + full stack trace
  - type: textarea
    id: description
    attributes:
      label: Description
      description: |
        What is the problem, question, or error?

        Write a short description telling what you are doing, what you expect to happen, and what is currently happening.
      placeholder: |
        * I'm trying to use the `langchain` library to do X.
        * I expect to see Y.
        * Instead, it does Z.
    validations:
      required: true
  - type: textarea
    id: system-info
    attributes:
      label: System Info
      description: |
        Please share your system info with us. Do NOT skip this step and please don't trim
        the output. Most users don't include enough information here and it makes it harder
        for us to help you.

        Run the following command in your terminal and paste the output here:

        `python -m langchain_core.sys_info`

        or if you have an existing python interpreter running:

        ```python
        from langchain_core import sys_info
        sys_info.print_sys_info()
        ```

        alternatively, put the entire output of `pip freeze` here.
      placeholder: |
        python -m langchain_core.sys_info
    validations:
      required: true
