name: Documentation
description: Report an issue related to the LangChain documentation.
title: "docs: <Please write a comprehensive title after the 'docs: ' prefix>"
labels: [documentation]

body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to report an issue in the documentation.

        Only report issues with documentation here, explain if there are
        any missing topics or if you found a mistake in the documentation.

        Do **NOT** use this to ask usage questions or reporting issues with your code.

        If you have usage questions or need help solving some problem,
        please use the [LangChain Forum](https://forum.langchain.com/).

        If you're in the wrong place, here are some helpful links to find a better
        place to ask your question:

        * [LangChain Forum](https://forum.langchain.com/),
        * [LangChain Github Issues](https://github.com/langchain-ai/langchain/issues?q=is%3Aissue),
        * [LangChain documentation with the integrated search](https://python.langchain.com/docs/get_started/introduction),
        * [LangChain how-to guides](https://python.langchain.com/docs/how_to/),
        * [API Reference](https://python.langchain.com/api_reference/),
        * [<PERSON><PERSON><PERSON><PERSON>](https://chat.langchain.com/)
        * [GitHub search](https://github.com/langchain-ai/langchain),
  - type: input
    id: url
    attributes:
      label: URL
      description: URL to documentation
    validations:
      required: false
  - type: checkboxes
    id: checks
    attributes:
      label: Checklist
      description: Please confirm and check all the following options.
      options:
        - label: I added a very descriptive title to this issue.
          required: true
        - label: I included a link to the documentation page I am referring to (if applicable).
          required: true
  - type: textarea
    attributes:
      label: "Issue with current documentation:"
      description: >
        Please make sure to leave a reference to the document/code you're
        referring to. Feel free to include names of classes, functions, methods
        or concepts you'd like to see documented more.
  - type: textarea
    attributes:
      label: "Idea or request for content:"
      description: >
        Please describe as clearly as possible what topics you think are missing
        from the current documentation.
